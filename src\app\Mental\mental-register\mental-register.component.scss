.mcard {
    border: 1px solid #efefef;
    border-radius: 3px;
    margin-bottom: 10px;
  
    
  
    .mcard-body {
      padding: 10px;
  
      table {
        color: #666;
        margin-bottom: 0;
  
      }
  
    }
    
  }

/* Apply to ng-select dropdown globally */
::ng-deep .ng-dropdown-panel {
  width: auto !important;
  min-width: 400px !important;   /* adjust this based on ICD-10 text */
  max-width: none !important;    /* remove default restriction */
}

::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items {
  white-space: nowrap !important;   /* keep text in one line */
  overflow-x: auto !important;      /* enable horizontal scroll */
}

::ng-deep .ng-dropdown-panel .ng-option {
  white-space: nowrap !important;

}

.symptom-ngselect {
  ::ng-deep .ng-value {
    background-color: #cce5ff !important; /* Light blue (same tone as your comorbidities box) */
    color: #0b196d !important;            /* Darker text for readability */
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
    display: flex;
    align-items: center;
    border: 1px solid #99ccff;            /* Subtle border */
  }

  /* Label text inside chip */
  ::ng-deep .ng-value-label {
    color: #004085 !important;
    font-weight: 500;
  }

  /* Remove icon inside chip */
  ::ng-deep .ng-value-icon {
    color: #004085 !important;
    margin-right: 4px;
    cursor: pointer;
  }
  ::ng-deep .ng-value-icon:hover {
    color: #cc0000 !important; /* Red on hover for remove */
  }
}


