import { ICellEditorAngularComp } from 'ag-grid-angular';
import { Component, ViewChild, ChangeDetectorRef, Renderer2, ElementRef, AfterViewInit } from "@angular/core";
import { Subject } from 'rxjs/Subject';
import { NgSelectComponent } from '@ng-select/ng-select';
import { concatMap } from 'rxjs/operators';

@Component({
    selector: 'ng-select-cell',
    template: `
    <div >
    <ng-select #dropDown [items]="buffer"
        appendTo="body"
        bindLabel="itemLabel"
        bindValue="itemCode"
        placeholder="Select"
        (change)="onChange($event)"
        (clear)="onClear()"
        [(ngModel)]="row"
        [ngStyle]= "{width:getWidth()}"
        >
        <ng-template ng-option-tmp let-item="item" let-index="index">
            {{item.itemLabel}}
        </ng-template>
    </ng-select>




</div>
    `,

    styles: [
        `
            .container {
                border-radius: 15px;
                border: 1px solid grey;
                background: #fff;
                width: 190px;
                height: 45px;
                padding-left: 15px;
            }

            .container:focus {
                outline: none;
            }

        `
    ]
})
export class GridNgSelectDataComponent implements ICellEditorAngularComp, AfterViewInit {
    private params: any;
    private selectedIndex;

    private displayedvalue;
    private setId;
    private rowId;
    row;

    private selectedObject: any;
    public editorWidth: any;


    instituteTypeAhead = new Subject<string>();

    bufferSize = 0;
    loading = false;
    currentPage = 1;

    mast = [];
    buffer = [];
    private setColumnValue;
    column: any;


    @ViewChild('dropDown', { read: NgSelectComponent, static: false }) public dropDown;
    @ViewChild('dropDown', { static: false }) public dropDownElement: ElementRef;
    isClear: boolean = false;

    constructor(private cd: ChangeDetectorRef, private _renderer: Renderer2) {



    }

    agInit(params: any): void {

        this.setColumnValue = params.value;
        this.column = params.column.colId
        this.params = params;
        this.rowId = this.params.rowIndex;
        this.mast = this.params.values;

        
        if (this.params.column.actualWidth) {
            this.editorWidth = this.params.column.actualWidth + "px";
        } else {

            this.editorWidth = "230px";
        }
        /*
         if(this.params.editorWidth){
             //this.editorWidth = "width:"+this.params.editorWidth+'px;';
             this.editorWidth = this.params.editorWidth+"px";
         }else{
            // this.editorWidth = "width:150px;";
            this.editorWidth = "230px";
         }
 */
        // element.style.width
        //this._renderer.setAttribute(this.dropDownElement['element'],"style",this.editorWidth);
    }
    getWidth() {
        return this.editorWidth;
    }

    ngAfterViewInit() {
        this.getList();
        if (this.params.value) {
            this.selectedObject = this.buffer.filter((value) => {
                return value['itemLabel'] === this.params.value;
            })
        }

        if (this.selectedObject !== undefined) {
            if (this.selectedObject.length > 0) {

                // tslint:disable-next-line:prefer-const
                let model = this.params.model;
                model['itemCode'] = this.selectedObject[0]['itemCode'];
                model['itemLabel'] = this.selectedObject[0]['itemLabel'];
                this.row = model['itemCode'];
                this.onChange(this.row, 'agInit');
            }
        }
        this.getDisplayedValue();
    }


    getDisplayedValue() {
        this.displayedvalue = !this.buffer[this.selectedIndex] ? "" : this.buffer[this.selectedIndex]['itemLabel'];
        this.setId = !this.buffer[this.selectedIndex] ? "" : this.buffer[this.selectedIndex]['itemCode'];


    }

    getValue() {
        if (this.params.node.data[this.params.dataColumn] !== this.setId) { 
            if (this.params.columnApi.getColumn(this.params.dataColumn))
                this.params.node.setDataValue(this.params.columnApi.getColumn(this.params.dataColumn).colId, this.setId);
        }
        return this.displayedvalue;

    }

    isPopup(): boolean {
        return true;
    }



    onChange(event, caller?: any) {
        if (event !== undefined) {
            let id = "";
            if (caller === 'agInit') {
                id = event;
            } else {
                id = event['itemCode'];
            }

            this.selectedIndex = this.buffer.findIndex((item) => {
                return item['itemCode'] === id;
            })
            this.getDisplayedValue();
        }

    }




    onClear() {
        this.loading = false;
        this.isClear = true;
        this.setId = null;
        this.displayedvalue = null;
        this.params.node.setDataValue(this.params.columnApi.getColumn(this.params.dataColumn).colId, null);
        // this.onChange(this.row, 'clear');
        setTimeout(() => {
            this.getList();
        }, 2000);
    }

    getList() {
        // tslint:disable-next-line:prefer-const
        let items = [];
        this.mast.forEach(data => {
            items.push({ "itemCode": data[this.params.objectData[0]], "itemLabel": data[this.params.objectData[1]] });
        })
        this.bufferSize = this.mast.length;
        this.buffer = items.slice(0, this.bufferSize);
        this.openSelect(this.dropDown);


    }

    openSelect(select: NgSelectComponent) {
        select.open();
        select.focus();
    }
}
