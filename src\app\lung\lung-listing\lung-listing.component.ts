import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import * as moment from 'moment';
import { Paginator } from 'primeng/components/paginator/paginator';
import { GridOptions } from 'ag-grid-community';
import { formatDate } from '@angular/common';

import { RegionDataModel } from '../../common/objectModels/region-model';
import { ICDLungList } from '../../common/objectModels/icdList-models';
import { MasterService } from '../../_services/master.service';
import { LungService } from "../lung.service";
import { SharedService } from '../../_services/shared.service';
import * as AppCompUtils from '../../common/app.component-utils';
import * as AppUtils from '../../common/app.utils';
import { Lung<PERSON>istR<PERSON>ult, LungTransplantIndication } from 'src/app/_models/lungTransplant.model';
import { LungExcel } from 'src/app/_models/lung-export-excel';

@Component({
  selector: 'app-lung-listing',
  templateUrl: './lung-listing.component.html',
  styleUrls: ['./lung-listing.component.scss'],
  providers: [LungService]
})
export class LungListingComponent implements OnInit {
  @ViewChild('elderlyRegPaginator', { static: false }) paginator: Paginator;
  
  totalRecords: number;
  paginationSize = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  lungSearchForm: FormGroup;
  
  regionData: RegionDataModel[] = [];
  wallayatList: any[] = [];
  wallayatListFilter: any[] = [];
  instituteList: any[] = [];
  instituteListFilter: any[] = [];
  
  yesNoOptions = AppCompUtils.YES_NO;
  genderOptions = AppCompUtils.GENDER;
  bloodGroupOptions = AppCompUtils.BLOOD_GROUP;
  icdLungList: ICDLungList[] = [];
  
  rowData: LungListResult[] = [];
  lungExportData: LungExcel[] = [];
  lungTransplantMasterList: LungTransplantIndication[] = [];
  
  gridOptions: GridOptions;
  columnDefs: any[];

  constructor(
    private _router: Router,
    private _masterService: MasterService,
    private _lungService: LungService,
    private _sharedService: SharedService,
    private formBuilder: FormBuilder
  ) {
    this.initializeForm();
    this.setupGridColumns();
    this.setupGridOptions();
    this.loadMasterData();
  }

  ngOnInit() { }

  private initializeForm(): void {
    this.lungSearchForm = new FormGroup({
      regNo: new FormControl(),
      civilId: new FormControl(),
      dob: new FormControl(),
      ageFrom: new FormControl(),
      ageTo: new FormControl(),
      regCode: new FormControl(),
      walCode: new FormControl(),
      regInst: new FormControl(),
      causeLungDisease: new FormControl(),
      transplantYn: new FormControl(),
      transIndication: new FormControl(),
      transplantUrgent: new FormControl(),
      transplantReadiness: new FormControl(),
      bloodGroup: new FormControl(),
    });
  }

  private setupGridColumns(): void {
    const dateFormatter = (params: any) =>
      params.data[params.colDef.field]
        ? formatDate(params.data[params.colDef.field], AppCompUtils.DATE_FORMATS.STANDARD, 'en')
        : null;

    this.columnDefs = [
      { headerName: 'Registration No', field: 'regNo', minWidth: 125, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 90, sortable: true },
      { headerName: "Date of Birth", field: "dateOfBirth", minWidth: 120, sortable: true, valueFormatter: dateFormatter },
      { headerName: 'Age', field: 'age', minWidth: 60, sortable: true },
      { headerName: 'Full Name', field: 'fullName', minWidth: 290, sortable: true },
      {
        headerName: "Gender", field: "sex", minWidth: 90, sortable: true,
        valueFormatter: (params: any) => this.formatGender(params)
      },
      { 
        headerName: 'Registered Institute', 
        field: 'regInst', 
        minWidth: 280, 
        sortable: true,
        valueFormatter: (params: any) => this.formatInstituteName(params)
      },
      { 
        headerName: 'Wilayat', 
        field: 'walCode', 
        minWidth: 120, 
        sortable: true,
        valueFormatter: (params: any) => this.formatWilayatName(params)
      },
      { 
        headerName: 'Region', 
        field: 'regCode', 
        minWidth: 160, 
        sortable: true,
        valueFormatter: (params: any) => this.formatRegionName(params)
      },
      { headerName: 'Weight (kg)', field: 'weight', minWidth: 110, sortable: true },
      { headerName: 'Height (cm)', field: 'height', minWidth: 110, sortable: true },
      {
        headerName: "Blood Group",
        field: "bloodGroup",
        minWidth: 130,
        sortable: true,
        valueFormatter: (params: any) => this.formatBloodGroup(params)
      },
      {
        headerName: 'Transplant Y/N', 
        field: 'transplantYn', 
        minWidth: 130, 
        sortable: true, 
        valueFormatter: (params: any) => this.formatYesNoValue(params)
      },
      { 
        headerName: 'Transplant Urgent', 
        field: 'transplantUrgent', 
        minWidth: 140, 
        sortable: true,
        valueFormatter: (params: any) => this.formatYesNoValue(params)
      },
      { 
        headerName: 'Transplant Readiness', 
        field: 'transplantReadiness', 
        minWidth: 170, 
        sortable: true,
        valueFormatter: (params: any) => this.formatYesNoValue(params)
      },
      { 
        headerName: 'Cause of Lung Disease', 
        field: 'icd', 
        minWidth: 220, 
        sortable: true,
        valueFormatter: (params: any) => this.formatIcdName(params)
      },
      { headerName: 'Death Case', field: 'deathCase', minWidth: 150, sortable: true }
    ];
  }

  private setupGridOptions(): void {
    this.gridOptions = {
      pagination: false,
      paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
      onGridReady: () => {
        this.gridOptions.api.setRowData(this.rowData);
        this.gridOptions.api.refreshCells({ force: true });
      },
      onGridSizeChanged: () => {
        this.gridOptions.api.sizeColumnsToFit();
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        suppressSizeToFit: true
      },
      suppressPropertyNamesCheck: true,
      enableCellTextSelection: true
    };
  }

  private loadMasterData(regCode: number = 0, walCode: number = 0): void {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.instituteList = response.result;
      this.instituteListFilter = this.instituteList;
    });

    this._masterService.getRegionsMasterFull();
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regionData = value;
      this.refreshGrid();
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
      this.refreshGrid();
    });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.instituteList = res['result'];
      this.instituteListFilter = this.instituteList;
      this.refreshGrid();
    });

    this._masterService.getIcdLungShortList();
    this._masterService.icdLungShortList.subscribe((value) => {
      this.icdLungList = value;
      this.refreshGrid();
    });

    this._masterService.getAllLungTransIndication().subscribe(response => {
      this.lungTransplantMasterList = response.result;
    });
  }

  private isValid(value: any): boolean {
    return value !== undefined && value != null && value !== "null" && value > 0;
  }

  onRegionSelect(event: any): void {
    if (event != undefined) {
      if (!this.isValid(event.regCode)) {
        this.wallayatListFilter = this.wallayatList;
        this.instituteListFilter = this.instituteList;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.instituteListFilter = this.instituteList;
    }
  }

  onBloodGroupSelect(event: any): void {
    const selectedBloodGroups = this.lungSearchForm.get('bloodGroup') as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;
    let currentSelectedValues = selectedBloodGroups.value ? [...selectedBloodGroups.value] : [];
    const bloodGroup = this.bloodGroupOptions.find(group => group.value === selectedValue);
    const selectedId = bloodGroup ? bloodGroup.id : null;

    if (isChecked && selectedId && !currentSelectedValues.includes(selectedId)) {
      currentSelectedValues.push(selectedId);
    } else if (!isChecked && selectedId) {
      currentSelectedValues = currentSelectedValues.filter(id => id !== selectedId);
    }

    selectedBloodGroups.setValue(currentSelectedValues);
  }

  onWilayatSelect(event: any): void {
    if (event != undefined) {
      if (!this.isValid(event.walCode)) {
        if (this.lungSearchForm.value['regCode'] && (this.lungSearchForm.value['regCode'] != null || this.lungSearchForm.value['regCode'] != 0)) {
          this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.lungSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.lungSearchForm.value['regCode']);
        } else {
          this.instituteListFilter = this.instituteList;
        }
      } else {
        this.instituteListFilter = this.instituteList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.lungSearchForm.value['regCode'] && (this.lungSearchForm.value['regCode'] != null || this.lungSearchForm.value['regCode'] != 0)) {
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.lungSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.lungSearchForm.value['regCode']);
      } else {
        this.instituteListFilter = this.instituteList;
      }
    }
  }

  onCellDoubleClicked(event: any): void {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['lung/registry'], { state: { regNo: event.data.regNo } });
  }

  clearForm(): void {
    this.lungExportData = [];
    this.lungSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.instituteListFilter = this.instituteList;
    this.rowData = [];
  }

  isNumberKey(event: KeyboardEvent): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    return !(charCode > 31 && (charCode < 48 || charCode > 57));
  }

  exportToExcel() {

    if (!this.rowData || this.rowData.length === 0) {
      return Swal.fire('Warning!', 'No Records to export', 'warning');
    }

    const searchBody = { ...this.lungSearchForm.value, getAll: true, pageable: { page: 0, size: this.totalRecords } };
    this._lungService.getLungListing(searchBody).subscribe({
      next: (res) => {
        const data = res['result']['content'] || [];
        if (!data.length) {
          return Swal.fire('Warning!', 'No Records to export', 'warning');
        }
        this.prepareExportData(data);
      },
      error: () => Swal.fire('Error!', 'Error occurred during export', 'error')
    });
  }

  private prepareExportData(rowData: LungListResult[]): void {
    this.lungExportData = [];
    rowData.forEach(el => {
      const genderObj = this.genderOptions.find(g => g.id == el.sex);
      const genderName = genderObj ? genderObj.value : el.sex;
      
      this.lungExportData.push({
        regNo: el.regNo,
        civilId: el.civilId,
        fullName: el.fullName,
        age: el.age,
        dateOfBirth: el.dateOfBirth,
        sex: genderName,
        regInst: this.formatInstituteName({ value: el.regInst }),
        walCode: this.formatWilayatName({ value: el.walCode }),
        regCode: this.formatRegionName({ value: el.regCode }),
        weight: el.weight,
        height: el.height,
        bloodGroup: this.formatBloodGroup({ value: el.bloodGroup }),
        transplantYn: this.formatYesNoValue({ value: el.transplantYn }),
        transplantUrgent: this.formatYesNoValue({ value: el.transplantUrgent }),
        transplantReadiness: this.formatYesNoValue({ value: el.transplantReadiness }),
        icd: this.formatIcdName({ value: el.icd }),
        deathCase: el.deathCase
      });
    });
    this._sharedService.exportAsExcelFile(this.lungExportData, "Lung_Listing");
  }

  onSearch(event?: any): void {
    const pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };

    const searchFormValue = { ...this.lungSearchForm.value };
    Object.keys(searchFormValue).forEach(key => {
      if (typeof searchFormValue[key] === 'string' && searchFormValue[key]) {
        searchFormValue[key] = searchFormValue[key].toUpperCase();
      }
    });

    const searchBody = { ...searchFormValue, pageable };

    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }

    this._lungService.getLungListing(searchBody).subscribe(
      res => {
        if (res['code'] == "S0000") {
          this.rowData = res['result']['content'];
          this.totalRecords = res['result']['totalElements'];

          if (this.gridOptions.api) {
            this.gridOptions.api.setRowData(this.rowData);
            setTimeout(() => {
              this.gridOptions.api.refreshCells({ force: true });
              this.gridOptions.api.redrawRows();
            }, 100);
          }
        } else {
          this.rowData = [];
          Swal.fire('Error!', res['message'], 'error');
        }
      }, 
      error => {
        if (error.status == 401) {
          Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
        }
      }
    );
  }

  formatYesNoValue(params: any): string {
    const yesNoValue = this.yesNoOptions.find(s => s.id == params.value);
    return yesNoValue ? yesNoValue.value : params.value || '';
  }

  formatGender(params: any): string {
    if (params.value === 'M') return 'Male';
    if (params.value === 'F') return 'Female';
    return params.value || '';
  }

  formatBloodGroup(params: any): string {
    const bloodGroupValue = params.value !== undefined ? params.value : params;
    const bloodGroupId = typeof bloodGroupValue === 'string' ? parseInt(bloodGroupValue, 10) : bloodGroupValue;
    
    switch (bloodGroupId) {
      case 1: return 'A+';
      case 2: return 'A-';
      case 3: return 'B+';
      case 4: return 'B-';
      case 5: return 'O+';
      case 6: return 'O-';
      case 7: return 'AB+';
      case 8: return 'AB-';
      default: return bloodGroupValue ? bloodGroupValue.toString() : '';
    }
  }

  formatWilayatName(params: any): string {
    if (!params.value || !this.wallayatList || this.wallayatList.length === 0) {
      return params.value || '';
    }
    const walayat = this.wallayatList.find(w => w.walCode == params.value);
    return walayat ? walayat.walName : params.value;
  }

  formatRegionName(params: any): string {
    if (!params.value || !this.regionData || this.regionData.length === 0) {
      return params.value || '';
    }
    const region = this.regionData.find(r => r.regCode == params.value);
    return region ? region.regName : params.value;
  }

  formatInstituteName(params: any): string {
    if (!params.value || !this.instituteList || this.instituteList.length === 0) {
      return params.value || '';
    }
    const institute = this.instituteList.find(i => i.estCode == params.value);
    return institute && institute.estName ? institute.estName : params.value.toString();
  }

  formatIcdName(params: any): string {
    if (!params.value || !this.icdLungList || this.icdLungList.length === 0) {
      return params.value || '';
    }
    const icd = this.icdLungList.find(i => i.code == params.value);
    return icd ? icd.disease : params.value;
  }

  private refreshGrid(): void {
    if (this.rowData && this.rowData.length > 0 && this.gridOptions.api) {
      this.gridOptions.api.setRowData(this.rowData);
      this.gridOptions.api.refreshCells({ force: true });
    }
  }
}


