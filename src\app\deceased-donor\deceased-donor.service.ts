import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import * as AppUtils from "../common/app.utils";
import { ICDList } from "../_models/deceased-donor.model";

import { BehaviorSubject, EMPTY } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: "root",
})


export class DeceasedDonorService {
  constructor(private http: HttpClient) {}

  

   getDeceasedDonorRegistry(crystalNo, civilId) {
      return this.http.get(AppUtils.GET_DECEASED_DONOR_REGISTRY, {
        params: new HttpParams().set("crystalNo", crystalNo).set("civilId", civilId)
      })
    }

    getDeceasedDonorRegistryByCrystalNo(crystalNo) {
      return this.http.get(AppUtils.GET_DECEASED_DONOR_REGISTRY_CRYSTAL_NO, {
        params: new HttpParams().set("crystalNo", crystalNo)
      })
    }

    
    getBrainDeathIcdList(): Observable<ICDList[]> {
      return this.http.get<{ result: ICDList[] }>(AppUtils.DECEASED_DONOR_BRAIN_DEATH_ICD_LIST)
        .pipe(map(response => response.result));
    }

    
    getInitialIcdList(): Observable<ICDList[]> {
      return this.http.get<{ result: ICDList[] }>(AppUtils.DECEASED_DONOR_INITIAL_ICD_LIST)
        .pipe(map(response => response.result));
    }

    getDeceasedDonorBrainDeathDetails(civilId) {
      return this.http.get(AppUtils.GET_DECEASED_DONOR_BRAIN_DEATH_DETAIL, {
        params: new HttpParams().set("civilId", civilId)
      })
    }

    getDeceasedDonorRegistryByCivilId(civilId) {
      return this.http.get(AppUtils.GET_DECEASED_DONOR_REGISTRY_CIVIL_ID, {
        params: new HttpParams().set("civilId", civilId)
      })
    }

  fetchAllDonorLabFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_LAB_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  getDonorLabDtlFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.GET_DECEASED_DONOR_LAB_DTL_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  getVitalInfoFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.GET_DECEASED_DONOR_VITAL_INFO_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  getRespiratoryInfoFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.GET_DECEASED_DONOR_RESPIRATORY_INFO_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  

  fetchAllDonorProcedureFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_PROCEDURE_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  saveDeceasedDonor(payload: any): Observable<any> {
    return this.http.post(AppUtils.SAVE_DECEASED_DONOR_REGISTRY, payload);
  }


  getDeceasedDByDonorID(Data){
        return this.http.get(AppUtils.GET_DECEASED_DONOR_REGISTRY+Data);
      }
    // liver listing
    getDeceasedDonorListing(data): Observable<any> {
      return this.http.post(AppUtils.GET_DECEASED_DONOR_REGISTRY_LISTING, data);
    }
  
    getMedicalProcedures(): Observable<any[]> {
      return this.http.get<any[]>('/api/medical-procedures');
    }
    
    getOtherInvestigations(): Observable<any[]> {
      return this.http.get<any[]>('/api/other-investigations');
    }

    getLabResults(): Observable<any[]> {
      return this.http.get<any[]>('/api/lab-tests');
    }

    getDashboard(): Observable<any> {
        return this.http.get(AppUtils.DECEASED_DONOR_DASHBOARD);
      }

}
