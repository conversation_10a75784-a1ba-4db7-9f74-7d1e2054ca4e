import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { NgSelectComponent } from '@ng-select/ng-select';
import { ElderlyService } from '../elderly.service';
import { ElderlyDashboard } from '../../_models/elderly-dashboard.model';
import { ElderlyDashboardDisplay } from '../../_models/elderly-dashboard-display.model';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from './../../_helpers/common.constants';
import * as _ from 'lodash';

@Component({
  selector: 'app-elderly-dashboard',
  templateUrl: './elderly-dashboard.component.html',
  styleUrls: ['./elderly-dashboard.component.scss']
})
export class ElderlyDashboardComponent implements OnInit {

  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: ElderlyDashboard[];
  DashboardDataFilter: ElderlyDashboard[];
  displayAgeData: ElderlyDashboardDisplay[];
  displayBradenScoresData: ElderlyDashboardDisplay[];
  displayRBSData: ElderlyDashboardDisplay[];
  displayDepressionScoreData: ElderlyDashboardDisplay[];
  displayHypertTensionData: ElderlyDashboardDisplay[];
  filterType: any;
  filterTitle:any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  ageData: any;
  bradenScoresData: any;
  rBSData: any;
  depressionScoreData: any;
  HypertTensionData: any
  createTime: any;
  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _elderlyService: ElderlyService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'ageF': [null],
      'ageT': [null],
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsMaster();
    this._masterService.regionsMaster.subscribe(value => {
      this.regionData = value;
    });


    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });


    this._masterService.getInstitutesMasterByUserRoles().subscribe(res=>{
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
     })
  }

  

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._elderlyService.getDashboard().subscribe(res => {
      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {

        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'ELD', this.institeList);

        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }
      }

      this.callFilter();
    })

  }
  callReset() {
    window.location.reload();
  }
  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {



      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.setChartData();
      this.displayFilterType();
    }
  }
  displayFilterType(){
  
    if (this.filterType === "institute") {
      this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      
    } else if (this.filterType === "wilayat") {
      this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
   
    } else if (this.filterType === "region") {
      this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
  
    }
    else {
      this.filterTitle = 'All Regions';
    }
  
  }

  setChartData() {
    this.displayAgeData = [];
    this.displayBradenScoresData = [];
    this.displayRBSData = [];
    this.displayDepressionScoreData = [];
    this.displayHypertTensionData = [];

    // for (var i = 0; i < this.DashboardDataFilter.length; i++) {
    //  // let kin = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].icd };
    //  // this.displayKidneyPatients.push(kin);

    //   this.displayBradenScoresData.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].bs.toString() });

    //   // let tra = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].transplant };
    //   // this.displayTransplantPatients.push(tra);
    // }

    this.DashboardDataFilter.forEach(element => {
      let agewise = { centralRegNo: element.centralRegNo, value: element.age.toString() };
      this.displayAgeData.push(agewise);

      if (element.bs != null) {
        this.displayBradenScoresData.push({ centralRegNo: element.centralRegNo, value: element.bs.toString() });
      }

      if (element.rbs != null) {
        this.displayRBSData.push({ centralRegNo: element.centralRegNo, value: element.rbs.toString() });
      }
      if (element.ds != null) {
        this.displayDepressionScoreData.push({ centralRegNo: element.centralRegNo, value: element.ds.toString() });
      }
      if (element.bp != null) {
        this.displayHypertTensionData.push({ centralRegNo: element.centralRegNo, value: element.bp.toString() });
      }

    });

    this.callChart();
  }

  callChart() {
    this.callPieChart(this.DashboardDataFilter.filter(s => s.bs != null), "this.bradenScoresData");
    this.callPieChart(this.DashboardDataFilter.filter(s => s.rbs != null), "this.rBSData");
    this.callPieChart(this.DashboardDataFilter.filter(s => s.ds != null), "this.depressionScoreData");
    this.callPieChart(this.DashboardDataFilter.filter(s => s.bp != null), "this.HypertTensionData");

    //this.callChartAgeData();  //this to display count of patient depend age
    this.callChartAgeWiseDistribution(); //this to display count of patient depend age range

  }

  callChartAgeData() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    this.displayAgeData = this.displayAgeData.filter(s => s.value != null);


    for (var n = 0; n < this.displayAgeData.length; n++) {

      if (listGroup.filter(s => s.centralRegNo === this.displayAgeData[n].value).length == 0) {
        const result = this.displayAgeData.filter(s => s.value == this.displayAgeData[n].value).length;
        let a = { centralRegNo: this.displayAgeData[n].value }
        charlabels.push(this.displayAgeData[n].value);
        charData.push(result);
        listGroup.push(a);
      }
    }


    this.charBGColor.sort(() => Math.random() - 0.2);
    this.ageData = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }
  }

  callChartAgeWiseDistribution() {
    let charlabels = [];
    let charData = [];

    let chartArray = [];


    this.displayAgeData.forEach(element => {
      if (chartArray.filter(s => s.centralRegNo == element.centralRegNo).length == 0) {
        chartArray.push(element);
      }

    }

    )


    let min: number;
    let max: number;
    min = Math.min.apply(Math, chartArray.map(function (o) { return o.value; }));
    max = Math.max.apply(Math, chartArray.map(function (o) { return o.value; }));

    let loopRing: number = 10;
    let newMin: number = _.cloneDeep(min);
    let loopMin: number;
    let loopMax: number;

    while (newMin < max + 1) {
      let totalCount: number = 0;
      loopMin = newMin;
      loopMax = newMin + loopRing;
      let result;
      for (var n = loopMin; n <= loopMax; n++) {
        result = chartArray.filter(s => s.value == n).length;
        totalCount = totalCount + result;
      }
      if (totalCount > 0) {
        if (loopMax > max) {
          let d = loopMin + " : " + max;
          charlabels.push(d);
          charData.push(totalCount);
        }
        else {
          let d = loopMin + " : " + loopMax;
          charlabels.push(d);
          charData.push(totalCount);
        }
      }


      newMin = loopMax + 1;
    }

    this.charBGColor.sort(() => Math.random() - 0.2);
    this.ageData = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }



  }


  callPieChart(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any= "";

    if (this.filterType === "institute") {
      // charTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      // charTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      // charTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);

    if (chartData == "this.bradenScoresData") {
      this.bradenScoresData = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
    if (chartData == "this.rBSData") {
      this.rBSData = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

    if (chartData == "this.depressionScoreData") {
      this.depressionScoreData = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

    if (chartData == "this.HypertTensionData") {
      this.HypertTensionData = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }




  }
  /* ------------  call Chart ---------------- */

}
