.form-stages {
    margin: 0 0 10px;

    li {
        padding: 10px;
        margin-bottom: 5px;
        border-radius: 5px;
        cursor: pointer;
        transition: .3s;
        color: #4a4a4a;
        // border: 1px solid #dfdfdf;

        &:hover,
        &.active {
            // border-color: var(--primary-color);
            // color: var(--primary-color);
            background: #fff;
            padding-left: 20px;
            box-shadow: 0 0 5px rgba(0, 0, 0, .05);
        }

        &.active {
            font-family: var(--font-bold);
        }
    }
}

.min-ht-458 {
    min-height: 458px;
}

::ng-deep .table-striped p-calendar table,
::ng-deep .table-striped p-calendar table tbody tr:nth-of-type(odd),
::ng-deep .table-striped p-calendar table thead {
    background-color: transparent !important;
}

.form-group label+small {
    position: relative;
    top: -6px;
    display: block;
    color: #a3a3a3;
}

.table-wrapper {
    max-height: 400px;
    overflow: auto;
    position: relative;

    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0 !important;

        thead th {
            position: sticky;
            top: 0;
            background: white;
            z-index: 2;
            color: #444;
        }
    }
}

.table-wrapper tbody th,
.table-wrapper thead th:first-child {
    position: sticky;
    left: 0;
    background: white;
    z-index: 3;
    color: #444;
}

.table-wrapper thead th:first-child,
.table-wrapper tbody th {
    border-right: 1px solid #ddd;
}

.minw-label {
    display: inline-block;
    min-width: 50px;
}


.btm-divider {
    border-bottom: 1px dashed #e1e1e1;
    padding-bottom: 10px;
}

.small-chart {
    width: 400px;
    height: 300px;
  }

  .truncate-text {
    white-space: nowrap; /* Prevent text from wrapping */
    overflow: hidden; /* Hide overflowed text */
    text-overflow: ellipsis; /* Add ellipsis (...) for overflowed text */
    max-width: 150px; /* Set a maximum width for the cell */
    display: block; /* Ensure the element behaves like a block for truncation */
  }
  
  .truncate-text:hover {
    overflow: visible; /* Show full content on hover */
    white-space: normal; /* Allow text to wrap */
    background-color: #f9f9f9; /* Optional: Add a background for better visibility */
    position: absolute; /* Position the expanded content */
    z-index: 10; /* Ensure it appears above other elements */
    padding: 5px; /* Add padding for better readability */
    border: 1px solid #ccc; /* Optional: Add a border for clarity */
    max-width: 300px; /* Set a larger width for the expanded content */
  }

  .loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .loader-container {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .loader-text {
    margin-top: 10px;
    color: #333;
    font-size: 14px;
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  .pagination-controls {
    margin-top: 1rem;
    
    button {
      margin: 0 0.5rem;
      
      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }
    }
    
    span {
      margin: 0 1rem;
    }
  }