<div id="case-details-form">
  <div class="row">
    <div class="input-group col-sm-10">
      <h4 class="page-title pt-2">Liver - Case Details</h4>
    </div>
    <div class="input-group col-sm-2 mb-2 text-right">
      <div class="input-group">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId"
          class="form-control form-control-sm search-input" (keyup.enter)="search()" />
        <div class="input-group-append">
          <button class="btn btn-default btn-sm search-icon" id="search" (click)="search()">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">

      <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Patient Details</h6>
           
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>

  <!-- <div class="accordion register">
  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
    <ngb-panel id="patientDetails" id="ngb-panel-0">
      <ng-template ngbPanelHeader let-opened="opened">
        <div
          class="d-flex align-items-center justify-content-between card-head"
          [ngClass]="opened ? 'opened' : 'collapsed'"
        >
          <h6>
            <i
              class="fas fa-male"
              *ngIf="alive"
              (click)="updateDeathDetails()"
              class="alive-icon fas fa-user"
              title="patient is alive "
            ></i>
            <i
              class="fas fa-male"
              *ngIf="!alive"
              class="dead-icon fas fa-user-slash"
              title="patient is dead "
            ></i>

            Patient Details
          </h6>
          <div>
            <span
              class="de-activate-btn"
              *ngIf="alive"
              (click)="updateDeathDetails()"
              >De-Activate</span
            >
            <button ngbPanelToggle class="btn btn-link p-0">
              <i
                class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"
              ></i>
            </button>
          </div>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <app-patient-details
          [submitted]="submitted"
          #patientDetails
        ></app-patient-details>
      </ng-template>
    </ngb-panel>
  </ngb-accordion>
</div> -->

  <div>
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
      <ngb-panel id="RegistrationINFO" id="ngb-panel-1">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Registration Information</h6>
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <form [formGroup]="caseDetailsForm" (ngSubmit)="submit()">
            <div class="case-details-panel">

              <row class="row">
                <div class="col-md-12">
                  <div class="content-wrapper mb-2 h-100">
                    <div class="row">

                      <div class="col-lg-4 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
                        <div class="form-group">

                          <div class="card-header"><strong>Transplant Readiness</strong></div>
                          <div class="card-body">
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantReadiness"
                                    [(ngModel)]="readiness" formControlName="transplantReadiness" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantReadiness"
                                    [(ngModel)]="readiness" formControlName="transplantReadiness" value="N">No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>



                      <div class="col-lg-4 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
                        <div class="form-group">

                          <div class="card-header"><strong>Transplant Willingness</strong></div>
                          <div class="card-body">
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantWillingness"
                                    [(ngModel)]="willingness" formControlName="transplantWillingness" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantWillingness"
                                    [(ngModel)]="willingness" formControlName="transplantWillingness" value="N">No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>


                      <div class="col-lg-4 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
                        <div class="form-group">
                          <div class="card-header"><strong>Transplant</strong></div>
                          <div class="card-body">
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantYn"
                                    [(ngModel)]="transplant" formControlName="transplantYn" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="transplantYn"
                                    [(ngModel)]="transplant" formControlName="transplantYn" value="N">No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </row>
            </div>
            <div class="case-details-panel">
              <row class="row">

                <div class="col-lg-12 col-md-12 col-sm-12" *ngIf="transplant == 'Y'">
                  <div class="card mt-4 theme-card">
                    <div class="mcard-header">Transplantation</div>
                    <div class="card-body">
                      <div class="row" formGroupName="rgTbCkdStageTransplant">
                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Pre-emptive transplant</label>
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="preEmptiveYn"
                                    [(ngModel)]="preEmptiveTransplant" formControlName="preEmptiveYn" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="preEmptiveYn"
                                    [(ngModel)]="preEmptiveTransplant" formControlName="preEmptiveYn" value="N">No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Date of Transplant</label>
                            <!-- <p-calendar [(ngModel)]="value" formControlName="transplantDatr"></p-calendar> -->

                            <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon="true"
                              formControlName="transplantDatr" [ngModelOptions]="{standalone: true}"
                              monthNavigator="true" [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                              showButtonBar="true"></p-calendar>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <!-- 
                                    [(ngModel)]="selectedTransplantPlace"
                                        (ngModelChange)="placeofTransplant()" -->
                            <label>Place of Transplantation</label>
                            <select class="form-control form-control-sm" formControlName="transplantInst">
                              <option disabled selected [value]="null">Select transplant Hospitals</option>
                              <option [value]="res.estCode" *ngFor="let res of hospitals">{{res.estName}}
                            </select>
                          </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Transplant Country</label>
                            <select class="form-control form-control-sm" formControlName="transplantCountry">
                              <option selected [value]="null">All Nation</option>
                              <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.natName}}
                              </option>
                            </select>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Follow Up Hospitals</label>
                            <select class="form-control form-control-sm" formControlName="followupInst">
                              <option disabled selected [value]="null">Select followup hospitals </option>
                              <option [value]="res.estCode" *ngFor="let res of hospitals">{{res.estName}}

                                <!-- <option *ngFor="let item of hospitals">{{ item }}</option> -->
                            </select>
                          </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Follow Up Country</label>
                            <select class="form-control form-control-sm" formControlName="followUpCountry">
                              <option selected [value]="null">All Nation</option>
                              <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.natName}}
                              </option>
                            </select>
                          </div>
                        </div>

                      </div>
                    </div>
                  </div>
                  <br>
                </div>

              </row>
            </div>
          </form>


          <div class="case-details-panel">
            <div class="row">
              <!-- Contact Details Column -->
              <div class="col-md-6">
                <div class="content-wrapper mb-2 h-100"> <!-- Added h-100 class -->
                  <div [formGroup]="contactDetailsForm" class="d-flex flex-column h-100"> <!-- Added flex structure -->
                    <!-- <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">
                          Contact Details
                          <button class="btn btn-sm float-right"></button>
                        </h6> -->
                    <div class="mcard-header">Contact Details
                      <!-- Added flex-grow-1 -->

                      <button *ngIf="showDownloadButton" (click)="onAddNewContact()"
                        class="btn btn-sm btn-primary float-right">
                        Add New
                      </button>

                    </div>
                    <div class="content-wrapper mb-2 lab-results flex-grow-1">
                      <p-dataTable [immutable]="false" [value]="contacts" [editable]="true" dataKey="runId"
                        [responsive]="true">
                        <p-column field="name" header="Name">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" class="form-control form-control-sm" formControlName="name" />
                                  <div
                                    *ngIf="getNameControl(rowIndex).touched && getNameControl(rowIndex).errors?.required"
                                    class="text-danger">
                                    <small>Name is required.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="relation" header="Relations">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">
                                  {{ getRelationName(row.relation) }}
                                </div>
                                <div *ngIf="row.isEditable">
                                  <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                    placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                    formControlName="relation" [dropdownPosition]="'auto'">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">
                                      {{ item.relationName }}
                                    </ng-template>
                                  </ng-select>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="phone" header="Phone">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">{{ row.phone }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" maxlength="16" (keypress)="numberOnly($event)"
                                    class="form-control form-control-sm" formControlName="phone" />
                                  <div
                                    *ngIf="getPhoneControl(rowIndex).touched && getPhoneControl(rowIndex).errors?.invalidPhone"
                                    class="text-danger">
                                    <small>Phone number is invalid.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="email" header="Email">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">{{ row.email }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" class="form-control form-control-sm" formControlName="email" />
                                  <div
                                    *ngIf="getEmailControl(rowIndex).touched && getEmailControl(rowIndex).errors?.invalidEmail"
                                    class="text-danger">
                                    <small>Email is invalid.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                          styleClass="foo">
                          <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="onRowEditInitContact(row)" *ngIf="row.isEditable == false"
                              class="btn btn-sm btn-primary">
                              <i class="fa fa-edit"></i>
                            </button>
                            <button (click)="onRowEditSaveContact(row)" *ngIf="row.isEditable == true"
                              class="btn btn-sm btn-primary" [disabled]="!isContactValid(row)">
                              <i class="fa fa-save"></i>
                            </button>

                          </ng-template>
                        </p-column>
                        <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                          styleClass="foo">
                          <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="deleteContact(row)" class="btn btn-sm btn-primary">
                              <i class="fa fa-trash"></i>
                            </button>
                          </ng-template>
                        </p-column>
                      </p-dataTable>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Family History Column -->
              <div class="col-md-6">
                <div class="content-wrapper mb-2 h-100">
                  <div class="d-flex flex-column h-100">

                    <div class="mcard-header">Family History of Liver Diseases
                      &nbsp;&nbsp;
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" [value]="'Y'" [(ngModel)]="liverRegister.familyHistoryYn"
                            class="form-check-input" name="familyHistory" />
                          Yes
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" [value]="'N'" [(ngModel)]="liverRegister.familyHistoryYn"
                            class="form-check-input" name="familyHistory" />
                          No
                        </label>
                      </div>
                      <button *ngIf="liverRegister.familyHistoryYn === 'Y'" (click)="onAddNewFH()"
                        class="btn btn-sm btn-primary float-right">
                        Add New
                      </button>
                    </div>
                    <div>



                      <div *ngIf="liverRegister.familyHistoryYn === 'Y'" [formGroup]="familyHistoryForm"
                        class="content-wrapper mb-2 lab-results">

                        <p-dataTable *ngIf="liverRegister.familyHistoryYn === 'Y'" [immutable]="false"
                          [value]="famHistory" [editable]="true" dataKey="runId" [responsive]="true">
                          <!-- Existing p-column definitions remain the same -->
                          <p-column field="name" header="Name">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                  <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                                  <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm" formControlName="name" />
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="relation" header="Relations">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">
                                    {{ getRelationName(row.relation) }}
                                  </div>
                                  <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                      placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                      formControlName="relation">
                                      <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{ item.relationName }}
                                      </ng-template>
                                    </ng-select>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="patientID" header="PatientID">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">{{ row.patientID }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" (keypress)="numberOnly($event)"
                                      class="form-control form-control-sm" formControlName="patientID" />
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="instID" header="Institute">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">
                                    {{ getInstName(row.instID) }}
                                  </div>
                                  <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                      placeholder="Select" bindLabel="estName" bindValue="estCode"
                                      formControlName="instID">
                                      <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{ item.estName }}
                                      </ng-template>
                                    </ng-select>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="onRowEditInitFH(row)"
                                *ngIf="row.source == 'W' && row.isEditable == false" class="btn btn-sm btn-primary">
                                <i class="fa fa-edit"></i>
                              </button>
                              <button (click)="onRowEditSaveFH(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                                class="btn btn-sm btn-primary">
                                <i class="fa fa-save"></i>
                              </button>
                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="delete(row)" class="btn btn-sm btn-primary">
                                <i class="fa fa-trash"></i>
                              </button>
                            </ng-template>
                          </p-column>
                        </p-dataTable>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="case-details-panel">
            <div class="row">

              <div class="col-sm-2">
                <div class="card h-100">
                  <div class="mcard-header">Indication of Liver Transplant</div>

                  <div class="card-body p-0">
                    <div class="row mx-0">
                      <!-- First Column -->
                      <div class="col-12">
                        <ul class="list-item list-unstyled">
                          <li *ngFor="let option of liverTransIndications; let i = index" class="checklist">
                            <div class="d-flex align-items-start">
                              <input type="checkbox" [(ngModel)]="option.checked" [id]="'checkbox' + option.id"
                                (change)="onIndicationChange(option)" />
                              <div class="flex-grow-1">
                                <span class="lbl">{{ option.value }}</span>
                                <ng-container *ngIf="option.remarksYn === 'Y' && option.checked">
                                  <div class="mt-1">
                                    <input type="text" [(ngModel)]="option.remarks" [id]="'textbox' + option.id"
                                      [disabled]="!option.checked" class="form-control form-control-sm w-70"
                                      placeholder="Specify" (blur)="onRemarksChange(option)" />
                                  </div>
                                </ng-container>
                              </div>
                            </div>
                          </li>
                        </ul>
                      </div>

                      <!-- Second Column -->
                      <!-- <div class="col-4">
                            <ul class="list-item list-unstyled mb-2">
                              <li *ngFor="let option of (liverTransIndications || []).slice(7); let i = index"
                                class="checklist mb-2">
                                <div class="d-flex align-items-start">
                                  <input type="checkbox" [(ngModel)]="option.checked" [id]="'checkbox' + option.id"
                                    (change)="onIndicationChange(option)" class="mt-1 me-2" />
                                  <div class="flex-grow-1">
                                    <span class="lbl d-block">{{ option.value }}</span>
                                    <ng-container *ngIf="option.remarksYn === 'Y' && option.checked ">
                                      <div class="mt-1">
                                        <input type="text" [(ngModel)]="option.remarks" [id]="'textbox' + option.id"
                                          [disabled]="!option.checked" class="form-control form-control-sm w-70"
                                          placeholder="Specify" (blur)="onRemarksChange(option)" />
                                      </div>
                                    </ng-container>
                                  </div>
                                </div>
                              </li>
                            </ul>
                          </div> -->
                    </div>
                  </div>
                </div>
              </div>


              <div class="col-sm-4">
                <div class="card h-100">
                  <div class="mcard-header">Stages of Liver Cirrhosis</div>
                  <div class="card-body p-2" style="overflow: hidden!important">
                    <div class="row">
                      <div class="col-6">
                        <ul class="list-item list-unstyled mb-0">
                          <li *ngFor="let option of liverCirrhosisStages.slice(0, 6); let i = index"
                            style="margin-bottom: 0rem !important; margin-top: 0rem !important;">
                            <div class="d-flex align-items-center justify-content-between"
                              style="margin-bottom: 0rem !important; margin-top: 0rem !important;">
                              <span class="lbl">{{ option.value }}</span>
                              <ng-container [ngSwitch]="option.id">
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="1">

                                  <input style="width: 50%;" type="text" (keypress)="numberOnly($event)"
                                    class="form-control" [(ngModel)]="liverRegister.childPughScore" />

                                </span>
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="2">
                                  <input style="width: 50%;" type="text" (keypress)="numberOnly($event)"
                                    class="form-control form-control-sm" [(ngModel)]="liverRegister.meldScore" />
                                </span>
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="3">
                                  <input style="width: 50%;" type="text" (keypress)="numberOnly($event)"
                                    class="form-control" [(ngModel)]="liverRegister.meldNaScore" />
                                </span>
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="4">
                                  <div class="btn-group btn-group-sm" role="group">
                                    <label class="form-check form-check-inline">
                                      <input type="radio" [value]="'Y'" [(ngModel)]="liverRegister.meldExceptionYn"
                                        class="form-check-input" name="meldException" />
                                      Yes
                                    </label>
                                    <label class="form-check form-check-inline">
                                      <input type="radio" [value]="'N'" [(ngModel)]="liverRegister.meldExceptionYn"
                                        class="form-check-input" name="meldException" />
                                      No
                                    </label>
                                  </div>
                                  <div class="ui-input-group remarkText mt-1"
                                    *ngIf="liverRegister.meldExceptionYn === 'Y' && !showCommentBox">
                                    <input type="text" class="form-control"
                                      [(ngModel)]="liverRegister.meldExceptionDetails" placeholder="Specify"
                                      (dblclick)="showCommentBox = true" />
                                  </div>
                                  <div *ngIf="showCommentBox && liverRegister.meldExceptionYn === 'Y'"
                                    class="comment-box">
                                    <textarea class="form-control" [(ngModel)]="liverRegister.meldExceptionDetails"
                                      placeholder="Specify" style="height: 100px;"></textarea>
                                  </div>
                                </span>
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="5">
                                  <input type="text" (keypress)="numberOnly($event)" style="width: 50%;"
                                    class="form-control" [(ngModel)]="liverRegister.peldScore" />
                                </span>
                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="6">
                                  <div class="btn-group btn-group-sm" role="group">
                                    <label class="form-check form-check-inline">
                                      <input type="radio" [value]="'Y'" [(ngModel)]="liverRegister.peldExceptionYn"
                                        class="form-check-input" name="peldException" />
                                      Yes
                                    </label>
                                    <label class="form-check form-check-inline">
                                      <input type="radio" [value]="'N'" [(ngModel)]="liverRegister.peldExceptionYn"
                                        class="form-check-input" name="peldException" />
                                      No
                                    </label>
                                  </div>
                                  <div class="ui-input-group remarkText mt-1"
                                    *ngIf="liverRegister.peldExceptionYn === 'Y' && !showCommentBox_peld">
                                    <input type="textarea" class="form-control"
                                      [(ngModel)]="liverRegister.peldExceptionDetails" placeholder="Specify"
                                      (dblclick)="showCommentBox_peld = true" />
                                  </div>
                                  <div *ngIf="showCommentBox_peld && liverRegister.peldExceptionYn === 'Y'"
                                    class="comment-box">
                                    <textarea class="form-control" [(ngModel)]="liverRegister.peldExceptionDetails"
                                      placeholder="Specify" style="height: 100px;"></textarea>
                                  </div>
                                </span>
                              </ng-container>
                            </div>
                          </li>
                        </ul>
                      </div>

                      <div class="col-6">
                        <ul class="list-item list-unstyled mb-0">
                          <li *ngFor="let option of liverCirrhosisStages.slice(6, 7); let i = index"
                            style="margin-bottom: 0rem !important; margin-top: 0rem !important;">
                            <div class="d-flex align-items-center justify-content-between"
                              style="margin-bottom: 0rem !important; margin-top: 0rem !important;">
                              <span class="lbl">{{ option.value }}</span>
                              <ng-container [ngSwitch]="option.id">

                                <span class="ui-input-group remarkText flex-grow-1" *ngSwitchCase="7">
                                  <input style="width: 80%;" type="text" pattern="[A-Z]"
                                    class="form-control form-control-sm" [(ngModel)]="liverRegister.childPughClass" />
                                </span>

                              </ng-container>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>



              <div class="col-sm-6">
                <div class="card h-100">
                  <div class="mcard-header">Complications of Liver Cirrhosis</div>
                  <div class="card-body half-height">
                    <div class="row">

                      <ng-container *ngFor="let complication of liverCirrhosisComplications; let i = index">
                        <div class="col-6 mb-3" style="margin-bottom: 0rem !important;">
                          <!-- Changed from col-4 to col-6 -->
                          <div class="complication-item">
                            <div class="d-flex align-items-center justify-content-between">
                              <span class="form-check form-check-inline me-0 flex-grow-1">{{ complication.value
                                }}</span>
                              <!-- Removed margin -->
                              <div class="btn-group btn-group-sm ms-2" role="group">
                                <!-- Added margin to the left for better spacing -->
                                <label class="form-check form-check-inline">
                                  <input type="radio" [value]="'Y'" [(ngModel)]="complication.selected"
                                    class="form-check-input" [name]="'complication_' + complication.id" />
                                  Yes
                                </label>
                                <label class="form-check form-check-inline">
                                  <input type="radio" [value]="'N'" [(ngModel)]="complication.selected"
                                    class="form-check-input" [name]="'complication_' + complication.id" />
                                  No
                                </label>
                              </div>
                            </div>

                            <div class="ui-input-group pt-2 sub-list"
                              *ngIf="complication.selected === 'Y' && complication.id === 24">
                              <ul class="list-unstyled">
                                <li *ngFor="let subItem of complication.subList; let j = index"
                                  class="d-flex align-items-center mb-2">
                                  <div class="form-check form-check-inline d-flex align-items-center"
                                    style="width: 100%;">

                                    <label class="lbl me-2 flex-grow-1">{{ subItem }}</label> <!-- Removed margin -->
                                    <span *ngIf="j < 2" class="ui-input-group remarkText flex-grow-1">
                                      <input type="text" (keypress)="numberOnly($event)" class="form-control"
                                        style="width: 30%;" [(ngModel)]="complication.subItems[j].remarks"
                                        [name]="'subRemarks_' + complication.id + '_' + j" />
                                    </span>
                                    <span *ngIf="j > 1" class="ui-input-group remarkText ms-2">
                                      <input type="checkbox" [(ngModel)]="complication.subItems[j].checked"
                                        [name]="'subCheck_' + complication.id + '_' + j" />
                                    </span>
                                  </div>
                                </li>
                              </ul>
                            </div>

                            <div class="ui-input-group pt-2 sub-list"
                              *ngIf="complication.selected === 'Y' && complication.id === 102">
                              <div class="d-flex align-items-center justify-content-between">
                                <!-- Added flexbox for alignment -->
                                <label class="lbl me-0 flex-grow-1">If yes specify</label> <!-- Removed margin -->
                                <span class="ui-input-group remarkText ms-2 flex-grow-1">
                                  <!-- Added margin for spacing -->
                                  <input type="text" class="form-control" [(ngModel)]="complication.remarks"
                                    [name]="'remarks_' + complication.id" />
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-container>

                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
          <div class="case-details-panel">
            <div class="row">
              <div class="col-sm-6">
                <div class="card mb-2 h-100"> <!-- Reduced bottom margin -->
                  <div class="mcard-header">
                    Medical Procedures
                    <button *ngIf="showDownloadButton && (!procedureDetails || procedureDetails.length === 0)"
                      (click)="downloadMedicalProcedures()" class="btn btn-sm btn-primary float-right">
                      Download
                    </button>
                  </div>
                  <div class="card-body"> <!-- Reduced padding -->
                    <!-- Procedure Details -->
                    <div class="selected-procedures"> <!-- Reduced top margin -->
                      <table class="table table-sm table-striped">
                        <thead>
                          <tr>
                            <th style="width: 30%">Procedure Type</th>
                            <th style="width: 20%">Done Date</th>
                            <th style="width: 50%">Remarks</th> <!-- Adjusted width for better distribution -->
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="let name of uniqueProcedureTypes.slice(0, 10) | slice: (page-1) * pageSize : (page-1) * pageSize + pageSize">
                            <td>
                              <div class="form-check form-check-inline">
                                <input class="form-check-input form-check-input-sm" type="checkbox"
                                  [id]="'proc_' + name" [checked]="isProcedureSelected(name)"
                                  (change)="onProcedureTypeSelect(name)">
                                <label class="form-check-label" [for]="'proc_' + name">
                                  {{ name }}
                                </label>
                              </div>
                            </td>
                            <td>
                              <p-calendar dateFormat="dd-mm-yy" showIcon="true"
                                [ngModel]="getProcedureDetail(name)?.doneDate"
                                (ngModelChange)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'doneDate', $event)"
                                [disabled]="!isProcedureSelected(name)" monthNavigator="true" [maxDate]="today"
                                yearRange="1930:2030" yearNavigator="true" showButtonBar="true" appendTo="body">
                              </p-calendar>
                            </td>

                            <td>
                              <input type="text" class="form-control form-control-sm" style="width: 100%;"
                                [value]="getProcedureDetail(name)?.remarks"
                                (input)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'remarks', $event.target.value)"
                                placeholder="Enter remarks" [disabled]="!isProcedureSelected(name)">
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <ngb-pagination class="d-flex justify-content-center" [(page)]="page" [pageSize]="3"
                        [collectionSize]="10">
                      </ngb-pagination>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="card currentMgmt h-100">
                  <div class="mcard-header">Current Management</div>
                  <div class="card-body">
                    <div class="accordion">
                      <div class="accordion-item" *ngFor="let option of currentManagement; let i = index">
                        <h6 class="accordion-header click-link" [attr.id]="'heading' + i" (click)="toggleAccordion(i)"
                          aria-expanded="true" [attr.aria-controls]="'collapse' + i">
                          {{ option.value }}
                          <i class="arrw fa" [ngClass]="openAccordion[i] ? 'fa-angle-up' : 'fa-angle-down'"></i>
                        </h6>
                        <div [attr.id]="'collapse' + i" class="accordion-collapse collapse"
                          [class.show]="openAccordion[i]" [attr.aria-labelledby]="'heading' + i"
                          data-bs-parent="#accordionExample">
                          <div class="accordion-body">
                            <ul class="sub-items">
                              <li *ngFor="let item of option.subList; let j = index">
                                <div class="row">
                                  <div class="col-sm-4">
                                    <div class="form-check form-check-inline">
                                      <input type="checkbox" [(ngModel)]="item.isSelected"
                                        [name]="'selected_' + option.id + '_' + item.id" />
                                      <label class="form-check-label" style="margin-left: 0.5rem">{{ item.value
                                        }}</label>
                                    </div>
                                  </div>
                                  <div class="col-sm-8">
                                    <form class="form-inline">
                                      <ng-container *ngIf="option.id !== 2">
                                        <div class="d-flex align-items-center">
                                          <div class="form-group ui-input-group me-2">
                                            <input type="text" (keypress)="numberOnly($event)"
                                              class="form-control form-control-sm w-sm" [(ngModel)]="item.dose1"
                                              [name]="'dose1_' + option.id + '_' + item.id"
                                              [disabled]="!item.isSelected" />
                                          </div>
                                          <div class="form-group mx-2 fade-txt">mg</div>
                                          <div class="form-group ui-input-group me-2">
                                            <input type="text" (keypress)="numberOnly($event)"
                                              class="form-control form-control-sm w-sm" [(ngModel)]="item.dose2"
                                              [name]="'dose2_' + option.id + '_' + item.id"
                                              [disabled]="!item.isSelected" />
                                          </div>
                                        </div>
                                      </ng-container>
                                      <ng-container *ngIf="option.id === 2">
                                        <p-calendar [(ngModel)]="item.date" [name]="'date_' + option.id + '_' + item.id"
                                          dateFormat="dd-mm-yy" [monthNavigator]="true" placeholder="Date"
                                          yearRange="1930:2030" [yearNavigator]="true"></p-calendar>
                                      </ng-container>
                                      <div class="d-flex align-items-center mt-2">
                                        <div class="ui-input-group">
                                          <input type="text" class="form-control form-control-sm" placeholder="Comments"
                                            [(ngModel)]="item.comments" [name]="'comments_' + option.id + '_' + item.id"
                                            [disabled]="!item.isSelected" />
                                        </div>
                                      </div>
                                    </form>
                                  </div>
                                </div>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>





        </ng-template>


      </ngb-panel>
    </ngb-accordion>
  </div>


  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <div [formGroup]="labTestForm">
        <!-- <div class="card-header">
      Lab Results
    </div> -->
        <h6 style="font-weight: bold; color: #973633;
    font-family: open_sanssemibold;
    background: #fff;">
          Lab Results
          <button class="btn btn-sm float-right"></button>

        </h6>
        <div class="content-wrapper mb-2 lab-results">
          <div class="text-right pb-2">
            <button *ngIf="showDownloadButton" (click)="addNewlab()" class="btn btn-sm btn-primary">
              Add New
            </button>
            <button *ngIf="showDownloadButton && (!labnewList || labnewList.length === 0)"
              class="btn btn-sm btn-primary" (click)="openModal(downloadLabTest)">Download</button>
          </div>

          <p-dataTable [immutable]="false" [value]="labnewList" [editable]="true" dataKey="runId" [responsive]="true">
            <p-column field="testDate" header="Date">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredDate" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="releasedDate" />

                    <div *ngIf="!row.isEditable">
                      {{ row.testDate | date : "dd-MM-yyyy" }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="testDate"
                        [ngModelOptions]="{ standalone: true }" monthNavigator="true" [maxDate]="today"
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                      </p-calendar>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="profileTestCode" header="Profile Name">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.profileTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="profileList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="testName" bindValue="testId" formControlName="profileTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.testName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="mohTestCode" header="Test Component">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.componentTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="ComponetList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="componentTestName" bindValue="componentTestId" formControlName="mohTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.componentTestName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="value" header="Result">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.value }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="value" (input)="validateNumberInput($event)" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="unit" header="Unit">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.unit }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" class="form-control form-control-sm" formControlName="unit" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="instCode" header="Institute">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.instName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="instCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.estName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-edit"></i>
                </button>
                <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-save"></i>
                </button>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="deletelab(row)" class="btn btn-sm btn-primary">
                  <i class="fa fa-trash"></i>
                </button>
              </ng-template>
            </p-column>
          </p-dataTable>
          <div *ngIf="labnewList && labnewList.length > 0">
            <p-paginator #labTestPaginator [rows]="paginationSize" [totalRecords]="totalLabRecords"
              (onPageChange)="onLabPageChange($event)" showCurrentPageReport="true"
              currentPageReportTemplate="(Total: {{totalLabRecords}} records)" pageLinkSize="10">
            </p-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <app-vaccination [showAddNewButton]="showVaccineButton"
        [showVaccineButton]="showDownloadButton && (!Vaccination.vaccineFg || Vaccination.vaccineFg.length === 0)"
        [submitted]="submitted" [calledFromParent]="true" (downloadVaccination)="onDownloadVaccination()" #Vaccination>

      </app-vaccination>
    </div>
  </div>

  <div class="col-sm-12">
    <div class="btn-container">
      <button type="submit" class="btn btn-primary" (click)="saveDetails()">
        Save
      </button>
      <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
      <button class="btn btn-primary ripple" (click)="navigateToRegister()">
        Back to register page
      </button>
      <button *ngIf="isPrint" (click)="generatePDF()" class="btn btn-sm btn-primary">Download PDF</button>
      <!-- <button  *ngIf="isPrint" class="btn btn-sm btn-secondary" (click)="print()">Print</button> -->
    </div>
  </div>


  <ng-template #downloadLabTest let-modal>

    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">Download Lab Test</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body" style="height: 250px;">

      <form [formGroup]="filterModelForm">
        <div class="row">
          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>From Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="fromDate">
            </p-calendar>

          </div>


          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>To Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="toDate">
            </p-calendar>
          </div>

        </div>
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <br>
            <label>Test Name</label>
            <div>
              <ng-multiselect-dropdown
                class=" multiappend custom-dropdown custom_color custom_box_color check_box_custom-color"
                [placeholder]="'Add Test'" [data]="testListToDownload" [settings]="dropdownSettings"
                formControlName="profileT">
              </ng-multiselect-dropdown>
            </div>

          </div>
        </div>
      </form>





    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-sm btn-primary" (click)="callFetchLabDataFromAlShifa()"
        (click)="modal.dismiss('Cross click')">Download</button>
    </div>

  </ng-template>
</div>