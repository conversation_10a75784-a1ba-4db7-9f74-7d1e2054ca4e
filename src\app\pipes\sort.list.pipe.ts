import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'sortListPipe'
})
 //This method used to Sort an array  - Pass the propery and Sorting order as PArameter
 //Eg:- this.sort(ArrayList,property,'sortingOrder')   -   this.sort(this.list,'EntryTime','DESC'/'ASC')
export class SortListPipe implements PipeTransform {
  transform(list:any,property,order:string){
    return list.sort((a, b) => {
      switch(order){
        case 'DESC':
          if (a[property] > b[property]) {
            return -1;
          }
          else if (a[property] < b[property]) {
            return 1;
          }
          else {
            return 0;
          }
          case 'ASC':
          if (a[property] < b[property]) {
            return -1;
          }
          else if (a[property] > b[property]) {
            return 1;
          }
          else {
            return 0;
          }
      }
    });
  }
}