<div>


  <header class="navbar-default" *ngIf="currentUser">
    <nav class="navbar navbar-expand-md fixed-top">
      <!-- Brand and toggle get grouped for better mobile display -->
      <div class="navbar-header fixed-brand tgl" [ngClass]="{'tgl':toggleMenu}">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" id="menu-toggle"
          (click)="showSidebar()">
          <span class="hamb-menu">
            <i class="hamb-icon"></i>
          </span>
        </button>
        <a class="navbar-brand">EREG</a>
      </div>
      <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
        <ul class="nav navbar-nav">
          <li class="active">
            <button class="navbar-toggle collapse in" data-toggle="collapse" id="menu-toggle-2" (click)="showSidebar()">
              <span class="hamb-menu">
                <i class="hamb-icon"></i>
              </span>
            </button>
          </li>
        </ul>
        <ul class="navbar-nav ml-auto navbar-right  ">
          <li class="nav-item dropdown lastLink" ngbDropdown display="dynamic" placement="bottom-right">
            <a class="nav-link dropdown-toggle" id="id01" ngbDropdownToggle><i class="fas fa-user"></i>
              {{loggedInPersonName}}</a>
            <div class="dropdown-menu" aria-labelledby="id01" ngbDropdownMenu>
              <!-- <a class="dropdown-item" routerLink="/user">User</a>  -->
          <li *ngIf="!_sharedService.readLocalStorageValue('ALSHIFA_TRUSTED_LOGIN')"><span class="dropdown-item"
              (click)="openModal(changePassowrdModal)">Change Password</span></li>
          <a class="dropdown-item" (click)="logout()">Log Out</a>
      </div>

      </li>

      </ul>

</div>
</nav>
</header>

<!-- wrapper -->
<!--id="wrapper" [ngClass]="{'toggled-2':toggleMenu}" -->
<div id="{{currentUser ? 'wrapper' : ''}}" [ngClass]="{'toggled-2':toggleMenu}">

  <!-- Sidebar -->
  <div *ngIf="currentUser">

    <!-- Sidebar -->
    <div id="sidebar-wrapper">
      <ul class="sidebar-nav nav-pills nav-stacked" id="menu"
        *ngIf="currentUser.menues && currentUser.menues.length > 0">

        <li *ngFor="let menu of currentUser.menues | sortBy : 'asc':'displayOrder'"
          [ngClass]="{ 'active': selectedMenu === menu.menuName }">

          <a style="cursor: pointer;" (click)="selectMenu(menu)" class="main-menu">
            <span class="fa-stack fa-lg pull-left">
              <i class="{{menu.iconClass}}"></i>
            </span>
            {{ menu.menuName }}
          </a>

          <!-- Submenu -->
          <ul class="sub-menu" *ngIf="selectedMenu === menu.menuName">
            <li *ngFor="let submenu of menu.subMenus | sortBy : 'asc': 'displayOrder'"
              [ngClass]="{ 'active': selectedSubMenu === submenu.menuId }">

              <a (click)="selectSubMenu(submenu)" id="{{submenu.routeName}}" *ngIf="submenu.templateUrl"
                [routerLink]="submenu.templateUrl">
                <span class="fa-stack fa-lg pull-left">
                  <i class="submenu-icon {{submenu.iconClass}}"></i>
                </span>
                {{ submenu.menuName }}
              </a>
              <a (click)="selectSubMenu(submenu)" id="{{submenu.routeName}}" *ngIf="!submenu.templateUrl"><span
                  class="fa-stack fa-lg pull-left"><i class="submenu-icon {{submenu.iconClass}}"></i></span>
                {{ submenu.menuName }}</a>

            </li>
          </ul>
        </li>
        <!-- <li>
          <a routerLink="donor/deceasedDonorRegister">
            <span class="fa-stack fa-lg pull-left"><i class="menu-icon liver"></i></span> Deceased Donor Reg
          </a>
        </li> -->
      </ul>
      <!-- <ul class="sidebar-nav nav-pills nav-stacked" id="menu"
        *ngIf="this.currentUser.menues && this.currentUser.menues.length > 0">
          <li *ngFor="let subMenu of this.currentUser.menues | sortBy : 'asc':'displayOrder'">
            <a [routerLink]="subMenu.templateUrl" (click) = "refreshService()">
              <span class="fa-stack fa-lg pull-left"><i class="fa {{subMenu.iconClass}}"></i></span>{{subMenu.menuName}}
          </a>
          </li>
        <li *ngFor="let menu of this.currentUser.menues |  sortBy : 'asc':'displayOrder'"
          (click)="select(menu.menuName);" [ngClass]="{active: isActive(menu.menuName)}">
          <a style="cursor: pointer;" [routerLink]="menu.templateUrl" class="main-menu"><span
              class="fa-stack fa-lg pull-left"><i class="{{menu.iconClass}}"></i></span>{{menu.menuName}}</a>
          <ul class="sub-menu">
            <li *ngFor="let submenu of menu.subMenus | sortBy  : 'asc': 'displayOrder'">
              <a (click)="menuclick(submenu.menuId)" id="{{submenu.routeName}}" *ngIf="submenu.templateUrl"
                [routerLink]="submenu.templateUrl"> <span class="fa-stack fa-lg pull-left"><i
                    class="submenu-icon {{submenu.iconClass}}"></i></span>{{submenu.menuName}}</a>
              <a (click)="menuclick(submenu.menuId)" id="{{submenu.routeName}}" *ngIf="!submenu.templateUrl"><span
                  class="fa-stack fa-lg pull-left"><i class="submenu-icon {{submenu.iconClass}}"></i></span>
                {{submenu.menuName}}</a>
            </li>
          </ul>
        </li>
        <li>
          <a class="main-menu" routerLink="donor/deceasedDonorRegister">
            <span class="fa-stack fa-lg pull-left"><i class="menu-icon liver"></i></span> Deceased Donor Reg
          </a>
        </li>

      </ul> -->
    </div>
  </div>

  <!-- Sidebar -->

  <!-- Sidebar -->

  <div *ngIf="loader.isLoading | async" class="loader-overlay">
  <div class="loader"></div>
</div>

  <div id="page-content-wrapper">
    <router-outlet>
      <ngx-spinner bdColor="rgba(51, 51, 51, 0.8)" size="default" color="#fff" type="ball-8bits"></ngx-spinner>
    </router-outlet>
  </div>

</div>

<!-- wrapper -->
</div>


<!-- changePassowrdModal -->
<ng-template #changePassowrdModal>
  <app-change-passowrd (closeModal)="handleCloseEvent()" [submittedPws]="submittedPws"></app-change-passowrd>
</ng-template>