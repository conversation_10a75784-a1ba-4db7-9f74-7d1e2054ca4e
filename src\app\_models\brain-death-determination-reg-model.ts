import { TbBrainDeathExamTran } from "./brain-death-determination-tran-model";

export class TbBrainDeathReg {
public examinationType: number;
public tranId : number ;
public civilId: number ;
public ancillaryTestDate : Date ;
public ancillaryTestReason : String ;
public estCode : number ;
public estName : String ;
public firstExamBy : number ;
public firstExamDate : Date ;
public firstExamVerifiedBy : number ;
public firstPostPaco2 : number ;
public firstPrePaco2 : number ;
public intracranialBloodFlow : String ;
public patientid : number ;
public primaryDiagnosis : String ;
public secondExamBy : number ;
public secondExamDate : Date ;
public secondExamVerifiedBy : number ;
public secondPrePaco2 : number ;
public firstExamName : String ;
public firstExamVerifiedName : String ;
public secondExamName : String ;
public secondExamVerifiedName : String ;
public rgTbBrainDeathExamTrans: Array<TbBrainDeathExamTran>;
public firstExamByName:String;
public secondExamByName:String;
public ancillaryTestYn:String;
}