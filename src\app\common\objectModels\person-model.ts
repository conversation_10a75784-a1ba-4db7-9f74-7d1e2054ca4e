            import {UserRoleModel} from './userrole-model'; 
            export class Person{
                perscode:number;
                persName:string;
                categoryCode:number;
                categoryName:string;
                partFlag:boolean;
                accessMode:boolean;
                userType:string;
                removedYn:string;
                removedBy:number;
                externalInst:boolean;
                roles:UserRoleModel[];
                strikeThrough:boolean;
            //Added below for updates
                runId:number;
                addedBy: number;
                addedTime: Date;
                disposeYn: string;
                lastAccessTime: Date;
                anonymityMode:string;
                personMast:Person;
                estCode:number;
                isLoggedUser:boolean;
                loggedUserPersCode:number;
                firstname: string;
                secondname: string;
                thirdname: string;
                tribe: string;
                firstname1: string;
                secondname1: string;
                thirdname1: string;
                tribe1: string;
            }   