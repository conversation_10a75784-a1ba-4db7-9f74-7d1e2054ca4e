<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Preparing Waiting List for Liver Transplant</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>
<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" [callLiverWaitingLisitng]="callLiverWaitingLisitng" (callMethod)="callListingPage()" #patientDetails></app-patient-details>

            </ng-template>


        </ngb-panel>
    </ngb-accordion>

<!--
     <button class="btn btn-sm btn-primary btn5" (click)="callListingPage()">Waiting List</button>
-->
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
        <ngb-panel id="waitingList" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Liver Waiting List Details</h6>
                    <div ngbPanelToggle class="btn btn-link p-0">
                        <button class="btn btn-primary add-btn pull-right" style="width:55px ;" type="button"
                            (click)="addRec('waitingListGrid')">Add</button>

                    </div>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>
                <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [gridOptions]="waitingListGrid"
                  [rowData]="rowData" [columnDefs]="columnDefs" enableSorting enableFilter rowSelection="single" singleClickEdit="true"
                  [enableColResize]="true" [frameworkComponents]="frameworkComponents" row-height="22"
                  (gridReady)="onReady($event, 'waitingListGrid')" (cellValueChanged)="onCellValueChanged($event)">
                </ag-grid-angular>
            </ng-template>

        </ngb-panel>
    </ngb-accordion>



    <div class="btn-container">
        <button class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
        <button class="btn btn-sm btn-primary" (click)="saveWaitingList('waitingListGrid')"> Save</button>
    </div>
</div>
