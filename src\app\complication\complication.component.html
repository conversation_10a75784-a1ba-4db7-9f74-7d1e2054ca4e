<div class="content-wrapper mb-2 lab-results">
  <form [formGroup]="complicationForm">
   
    <div class="text-right pb-2">
      <button (click)="onAddNewComp()" class="btn btn-sm btn-primary">Add New</button>
      <!-- <button  class="btn btn-sm btn-primary" >Download</button> -->
    </div>

    <p-dataTable [immutable]="false" [value]="compFg" [editable]="true" dataKey="runId" [responsive]="true">

      <p-column field="compId" header="Complication">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbGenComplication">
            <div [formGroupName]="rowIndex">
              <input type="hidden" class="form-control form-control-sm" formControlName="runid">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredDt">
              <div *ngIf="!row.isEditable">
                {{getComplicationName(row.compId)}}
              </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="complicationMastList" [virtualScroll]="true" formControlName="compId"
                  placeholder="Select Complication" bindLabel="description" bindValue="id">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description }}
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="compId" header="Frequency">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbGenComplication">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">
                {{getComplicationsFrequencyName(row.frequency)}}
              </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="complicationsFrequencyList" [virtualScroll]="true"
                  formControlName="frequency" placeholder="Select Frequency" bindLabel="description" bindValue="id">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description }}
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <!-- <p-column field="Onset Date" header="Date" >
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbGenComplication">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.onsetDt | date:'dd-MM-yyyy'}}</div>
              <div *ngIf="row.isEditable">
                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="onsetDt"
                [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column> -->



      <p-column field="remarks" header="Remarks">
        <!-- <ng-template let-row="rowData" pTemplate="body"> -->
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbGenComplication">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.remarks}}</div>
              <div *ngIf="row.isEditable">
                <input type="text" class="form-control form-control-sm" formControlName="remarks">
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>


      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="onRowEditInit(row)" *ngIf=" row.isEditable == false" class="btn btn-sm btn-primary"><i
              class="fa fa-edit"></i></button>

          <button (click)="onRowEditSave(row)" *ngIf="row.isEditable == true" class="btn btn-sm btn-primary"><i
              class="fa fa-save"></i></button>
        </ng-template>
      </p-column>

      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
        </ng-template>
      </p-column>

    </p-dataTable>
  </form>

</div>