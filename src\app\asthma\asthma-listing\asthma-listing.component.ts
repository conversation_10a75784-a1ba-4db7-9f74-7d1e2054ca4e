import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, FormArray } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { GridOptions } from 'ag-grid-community';
import * as AppUtils from '../../common/app.utils';
import * as AppCompUtils from '../../common/app.component-utils';
import { AsthmaService } from '../asthma.service';
import * as moment from 'moment';
import { MedIdList } from 'src/app/common/objectModels/MedIdList';
import { InhalerList } from 'src/app/common/objectModels/InhalerList';
import { TriggeringFactors } from 'src/app/_models/TriggeringFactors';
import Swal from 'sweetalert2';
import { bmiAsthmaList } from 'src/app/common/objectModels/bmiAsthmaList';






@Component({
  selector: 'app-asthma-listing',
  templateUrl: './asthma-listing.component.html',
  styleUrls: ['./asthma-listing.component.scss']
})
export class AsthmaListingComponent implements OnInit {

  asthmaListingForm: FormGroup;
  regions: any[];
  wallayatList: any[];
  wallayatListFilter: any[];
  instituteList: any[];
  instituteListFilter: any[];
  asthmainstitutes: any[];
  formCompleted = AppCompUtils.FILE_STATUS;
  genderTypeOptions = [];
  nationList: any;
  dateFormat = 'dd/mm/yy';
  rowData: any[] = [];
  columnDefs: any[];
  institutes: any;
  public smokingStatusOption = [];
  public fileStatusOption = [];
  MedIdList: Array<MedIdList>;
  InhalerList: Array<InhalerList>;
  bmiList: Array<bmiAsthmaList>;
  dynamicFormArray: any;
  formDataObject: any;
  lowValue: any;
  highValue: any;
  resultData: any;
  flagish: Boolean = false;
  flagishTwo: Boolean = false;
  flagishThree: Boolean = false;
  flagFour: Boolean = false;
  flagFive: Boolean = false;
  flagSx: Boolean = false;
  flagSeven: Boolean = false;
  nationalityList: any[];


  comorbiditieList = [
    { value: 'co_Atopy', label: 'Atopy' },
    { value: 'co_Stnusitis', label: 'Stnusitis' },
    { value: 'co_Nasal_Polyps', label: 'Nasal Polyps' },
    { value: 'co_Allergic_Rhinitis', label: 'Allergic Rhinitis' },
    { value: 'co_Gerd', label: 'GERD' },
    { value: 'co_Sleep_Apnoea', label: 'Sleep Apnoea' },
    { value: 'co_Hypertenstion', label: 'Hypertension' },
    { value: 'co_Food_Allergy', label: 'Food Allergy' },
    { value: 'co_Diabetes', label: 'Diabetes' },
    { value: 'co_Copd', label: 'COPD' },
    { value: 'co_cvd', label: 'CVD' },
    { value: 'co_Others', label: 'Others' },
  ];

  triggeringFactors = [
    { value: 'exercise', label: 'Exercise' },
    { value: 'perfume', label: 'Perfume' },
    { value: 'bakhoor', label: 'Bakhoor' },
    { value: 'animal_Dander', label: 'Animal Dander' },
    { value: 'spring', label: 'Spring' },
    { value: 'autumn', label: 'Autumn' },
    { value: 'emotions', label: 'Emotions' },
    { value: 'urti', label: 'URTI' },
    { value: 'smoke', label: 'Smoke' },
    { value: 'dust', label: 'Dust' },
    { value: 'medications', label: 'Medications' },
    { value: 'occupational', label: 'Occupational' },
    { value: 'summer', label: 'Summer' },
    { value: 'winter', label: 'Winter' },
    { value: 'weather_Changes', label: 'Weather Changes' },
    { value: 'other_Factor', label: 'Other' },
  ];

  history = [
    { value: 'asthmaControlled', label: 'Asthma Controlled?' },
    { value: 'emergency_Visit', label: 'History of ER Vists' },
    { value: 'hospitalized', label: 'History of Hospitalization' },
    { value: 'icu_Admission', label: 'History of ICU Admission' },
    { value: 'spirometry_Done_YN', label: 'Spirometry Test Done?' },
    { value: 'asthma_confirmed_Yn', label: 'Asthma Confirmed by Spirometry' },
    { value: 'recivedInfuVac', label: 'Recived Infuenza Vaccine (In the last 12 Months)' },
  ];

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };



  constructor(private fb: FormBuilder, private sharedService: SharedService, private masterService: MasterService, private asthmaService: AsthmaService) {
    this.masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];
    });

    this.asthmaListingForm = this.fb.group({
      civilId: new FormControl(null),
      central_Reg: new FormControl(null),
      regCode: new FormControl(null),
      wal_code: new FormControl(null),
      estCode: new FormControl(null),
      reg_Inst: new FormControl(null),
      file_Status: new FormControl(null),
      last_Visit_DateFrom: new FormControl(null),
      last_Visit_DateTo: new FormControl(null),
      reg_DateFrom: new FormControl(null),
      reg_DateTo: new FormControl(null),
      activePatientsFrom: new FormControl(null),
      activePatientsTo: new FormControl(null),
      sex: new FormControl(null),
      ageFrom: new FormControl(null),
      ageTo: new FormControl(null),
      nationality: new FormControl(null),
      last_Smoking_status: new FormControl(null),
      bmi: new FormControl(null),
      med_Id: new FormControl(null),
      dev_Id: new FormControl(null),
      dev_Good: new FormControl(null),
      dev_Poor: new FormControl(null),
      dev_Unchecked: new FormControl(null),
      morbidities: new FormControl(null),
      triggeringFactors: new FormControl(null),
      history: new FormControl(null),
    });

    this.columnDefs = [
      { headerName: 'Registration No', field: 'central_Reg', width: 99, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', width: 125, sortable: true },
      { headerName: 'Name', field: 'fullName', width: 200, sortable: true },
      { headerName: 'Age', field: 'age', width: 66, sortable: true },
      { headerName: 'DOB', field: 'dob', width: 125, sortable: true, cellRenderer: this.getDateFormat },
      { headerName: 'Gender', field: 'sex', width: 100, sortable: true },
      { headerName: 'Institute', field: 'reg_Inst', width: 200, sortable: true, cellRenderer: this.getAncInstitute },
      { headerName: 'Nationality', field: 'nationality', width: 100, sortable: true, cellRenderer: this.cellRendererNationality },
      { headerName: 'File Status', field: 'file_Status', width: 100, sortable: true },
      { headerName: 'Registered date', field: 'reg_Date', width: 100, sortable: true, cellRenderer: this.getDateFormat },
      { headerName: 'Last Visit Date', field: 'last_Visit_Date', width: 100, sortable: true, cellRenderer: this.getDateFormat },
    ];

  }

  ngOnInit() {
    this.smokingStatusOption = AppCompUtils.SMOKING_STATUS_OPTION;
    this.fileStatusOption = AppCompUtils.FILE_STATUS_OPTION;
    this.masterService.getMedicationIdList();
    this.masterService.getInhalerIdList();
    this.masterService.getbmiValueList();
    this.masterService.getWallayatMasterFull();
    this.masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
    });
    this.genderTypeOptions = AppCompUtils.GENDER;
    this.loadMasters();
    this.getNationalityList();
  }


  getAncInstitute = (reg_Inst) => {
    if (reg_Inst.value) {
      let estName;
      this.institutes.forEach(ele => {
        if (reg_Inst.value == ele.estCode) {
          estName = ele.estName;
        }
      })
      return estName;
    } else {
      return ' '
    }
  };

  cellRendererNationality = (data) => {
    if (data.value) {
      let NationalityName = ''
      this.nationalityList.forEach((item) => {
        if (item.natCode == data.value) {
          NationalityName = item.nationality
        }
      });
      return NationalityName;
    } else {
      return '';
    }
  };


  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };



  changeAncInstitute(obj) {
    if (obj && !obj.estCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];
    }
  }


  pickLowAndHighValues(event: any) {
    this.lowValue = event.low;
    this.highValue = event.high;
  }


  getNationalityList(natCode: any = 0) {
    this.masterService.getNationalityList(natCode).subscribe(response => {
      this.nationalityList = response.result;
    }, error => {
    });
  }




  loadMasters() {
    this.masterService.getRegionsMasterFull();
    this.masterService.regionsMasterFull.subscribe(value => {
      this.regions = value;
    });

    this.masterService.getWallayatMasterFull();
    this.masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });

    this.masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.asthmainstitutes = this.institutes;


      this.masterService.getMedicationIdList();
      this.masterService.MedIdList.subscribe(value => {
        this.MedIdList = value;
      });

      this.masterService.getInhalerIdList();
      this.masterService.InhalerList.subscribe(value => {
        this.InhalerList = value;
      });

      this.masterService.getbmiValueList();
      this.masterService.bmiList.subscribe(value => {
        this.bmiList = value;
      });

    });

  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.instituteListFilter = this.instituteList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.instituteListFilter = this.instituteList;
    }
  }


  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.asthmaListingForm.value['regCode'] && (this.asthmaListingForm.value['regCode'] != null || this.asthmaListingForm.value['regCode'] != 0)) {
          this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.asthmaListingForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.asthmaListingForm.value['regCode']);
        } else {
          this.instituteListFilter = this.instituteList;
        }
      }
      else {
        this.instituteListFilter = this.instituteList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.asthmaListingForm.value['regCode'] && (this.asthmaListingForm.value['regCode'] != null || this.asthmaListingForm.value['regCode'] != 0)) {
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.asthmaListingForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.asthmaListingForm.value['regCode']);
      } else {
        this.instituteListFilter = this.instituteList;
      }
    }
  }


  exportExcel() { }

  clear() {
    this.asthmaListingForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.rowData = [];
    this.lowValue = null;
    this.highValue = null;
  }

  getList() {
    const searchCriteria = this.asthmaListingForm.value;

    const source = {
      rgVwAsthmaRegistryPatientsDto: {
        civilId: searchCriteria.civilId,
        central_Reg: searchCriteria.central_Reg,
        reg_Code: searchCriteria.regCode,
        wal_code: searchCriteria.wal_code,
        estCode: searchCriteria.estCode,
        reg_Inst: searchCriteria.reg_Inst,
        file_Status: searchCriteria.file_Status,
        last_Visit_DateFrom: searchCriteria.last_Visit_DateFrom,
        last_Visit_DateTo: searchCriteria.last_Visit_DateTo,
        reg_DateFrom: searchCriteria.reg_DateFrom,
        reg_DateTo: searchCriteria.reg_DateTo,
        activePatientsFrom: searchCriteria.activePatientsFrom,
        activePatientsTo: searchCriteria.activePatientsTo,
        sex: searchCriteria.sex,
        ageFrom: searchCriteria.ageFrom,
        ageTo: searchCriteria.ageTo,
        nationality: searchCriteria.nationality,
        last_Smoking_status: searchCriteria.last_Smoking_status,
      },
      rgVwAsthmaComoribidityDto: {},
      rgVwAsthmaFactorsDto: {},
      rgVwAsthmaMedManagementDto: {
        med_Id: searchCriteria.med_Id,
      },
      rgVwAsthmaDeviceMgmtDto: {
        dev_Id: searchCriteria.dev_Id,
        dev_Good: searchCriteria.dev_Good,
        dev_Poor: searchCriteria.dev_Poor,
        dev_Unchecked: searchCriteria.dev_Unchecked,
      },
      rgVwAsthmaSpirometryDto: {},
      rgVwAsthmaAssessmentDto: {},
      rgVwAsthmaEvaluationDto: {},
      rgTbVaccinationInfoDto: {},
      rgVwAsthmaBMIparaDto: {
        low: this.lowValue,
        high: this.highValue,
      },
    };

    // CO-morbidities
    this.comorbiditieList.forEach(el => {
      if (el['selected']) {
        source.rgVwAsthmaComoribidityDto[el.value] = "Y";
      }
    });

    // Triggering Factors
    this.triggeringFactors.forEach(el => {
      if (el['selected']) {
        source.rgVwAsthmaFactorsDto[el.value] = "Y";
      }
    });

    // Asthma Controlled
    if (this.flagish) {
      source['rgVwAsthmaEvaluationDto']["asthmaControlled"] = "Y";
    }

    // rgVwAsthmaAssessmentDto
    if (this.flagishTwo) {
      source['rgVwAsthmaAssessmentDto']["emergency_Visit"] = "Y";
    }

    if (this.flagishThree) {
      source['rgVwAsthmaAssessmentDto']["hospitalized"] = "Y";
    }

    if (this.flagFour) {
      source['rgVwAsthmaSpirometryDto']["spirometry_Done_YN"] = "Y";
    }

    if (this.flagSeven) {
      source['rgVwAsthmaAssessmentDto']["icu_Admission"] = "Y";
    }

    if (this.flagFive) {
      source['rgVwAsthmaSpirometryDto']["asthma_confirmed_Yn"] = "Y";
    }

    if (this.flagSx) {
      source['rgTbVaccinationInfoDto']["recivedInfuVac"] = "Y";
    }

    this.asthmaService.getAsthmaData(source).subscribe((res) => {
      this.rowData = res.result;
      for (var i = 0; i < this.rowData.length; i++) {
        this.rowData[i].sex = this.genderTypeOptions.filter(s => s.id == this.rowData[i].sex).map(s => s.value).toString();
        this.rowData[i].file_Status = this.fileStatusOption.filter(s => s.id == this.rowData[i].file_Status).map(s => s.value).toString();
      }
    },
      (error) => {
        if (error.status === 401) {
          Swal.fire('Error!', 'Error occurred while retrieving details', 'error');
        }
      }
    );
  }



  changeDateFormat() {
    let body = this.asthmaListingForm.value;
    if (this.asthmaListingForm.controls['visitDateFrom'].value) {
      body['visitDateFrom'] = moment(this.asthmaListingForm.controls['visitDateFrom'].value, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
    if (this.asthmaListingForm.controls['visitDateTo'].value) {
      body['visitDateTo'] = moment(this.asthmaListingForm.controls['visitDateTo'].value, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
  }

  selectItem(event, idx, list) {
    const value = event.target.value;

    const flagMapping = {
      "asthmaControlled": 'flagish',
      "emergency_Visit": 'flagishTwo',
      "hospitalized": 'flagishThree',
      "spirometry_Done_YN": 'flagFour',
      "asthma_confirmed_Yn": "flagFive",
      "recivedInfuVac": 'flagSx',
      "icu_Admission": 'flagSeven',
    };

    const flagProperty = flagMapping[value];

    if (flagProperty) {
      this[flagProperty] = event.target.checked;
    }

    list[idx]['selected'] = event.target.checked;
  }

}


