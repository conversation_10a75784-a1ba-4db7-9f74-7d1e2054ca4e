import { Establishment } from './EstablishmentModel';
import { RoleDataModel } from './role-model';
import { RegistriesDataModel } from './registries-model';
import { SystemIdDataModel } from './system-id-model'

export class SaveUserDataModel {
    id: number;
    person: {
        perscode: Number;
        personName: String;
    }
    loginId: String;
    active: boolean;
    institutes: Array<Establishment> = [];
    roles: Array<RoleDataModel> = [];
    registries: Array<RegistriesDataModel> = [];
    userSystem: Array<SystemIdDataModel> = [];

    constructor() {
        this.person = { perscode: null, personName: "" };
    }
}