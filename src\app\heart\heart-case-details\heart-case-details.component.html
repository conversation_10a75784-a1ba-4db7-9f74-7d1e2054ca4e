<div id="case-details-form">

  <div class="row">
    <div class="input-group col-sm-10">
      <h4 class="page-title pt-2">Heart - Case Details</h4>
    </div>
    <div class="input-group col-sm-2 mb-2 text-right">
      <div class="input-group">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId"
          class="form-control form-control-sm search-input" (keyup.enter)="search()" />
        <div class="input-group-append">
          <button class="btn btn-default btn-sm search-icon" id="search" (click)="search()">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">

      <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Patient Details</h6>
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>

  <div>
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
      <ngb-panel id="RegistrationINFO" id="ngb-panel-1">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Registration Information</h6>
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <form [formGroup]="caseDetailsForm" (ngSubmit)="submit()">

            <div class="case-details-panel">
              <div class="row" formGroupName="heartRegister">
                <!-- Recommended for Transplant -->
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group">
                    <label><strong>Recommended for Transplant?</strong></label>
                    <div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="recTransYn" value="Y"
                          id="recTransYes">
                        <label class="form-check-label" for="recTransYes">Yes</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="recTransYn" value="N"
                          id="recTransNo">
                        <label class="form-check-label" for="recTransNo">No</label>
                      </div>
                      <label class="ml-3 mb-0">Reason</label>
                      <textarea class="form-control form-control-sm mt-2" formControlName="recTransReason"></textarea>
                    </div>
                  </div>
                </div>
                <!-- Urgent Transplant Needed -->
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group">
                    <label><strong>Urgent Transplant Needed?</strong></label>
                    <div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="urgentTransYn" value="Y"
                          id="urgentTransYes">
                        <label class="form-check-label" for="urgentTransYes">Yes</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="urgentTransYn" value="N"
                          id="urgentTransNo">
                        <label class="form-check-label" for="urgentTransNo">No</label>
                      </div>
                      <label class="ml-3 mb-0">Reason</label>
                      <textarea class="form-control form-control-sm mt-2"
                        formControlName="urgentTransReason"></textarea>
                    </div>
                  </div>
                </div>
                <!-- Smoking Related Queries -->
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group mb-2">
                    <label><strong>Cigarette Smoking?</strong></label>
                    <div class="d-flex align-items-center">
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="smokingYn" value="Y"
                          id="smokingYes">
                        <label class="form-check-label" for="smokingYes">Yes</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="smokingYn" value="N"
                          id="smokingNo">
                        <label class="form-check-label" for="smokingNo">No</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="smokingYn" value="U"
                          id="smokingPast">
                        <label class="form-check-label" for="smokingPast">Smoked in Past</label>
                      </div>
                      <label class="ml-3 mb-0">Quit Date</label>
                      <div class="ml-2" style="min-width: 110px;">
                        <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon="true"
                          formControlName="cigaretteQuitDate" [ngModelOptions]="{standalone: true}"
                          monthNavigator="true" [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                          showButtonBar="true"></p-calendar>
                      </div>
                    </div>
                  </div>
                  <div class="form-group mb-2">
                    <label><strong>Substance Used?</strong></label>
                    <div class="d-flex align-items-center">
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="substanceUseYn" value="Y"
                          id="substanceYes">
                        <label class="form-check-label" for="substanceYes">Yes</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="substanceUseYn" value="N"
                          id="substanceNo">
                        <label class="form-check-label" for="substanceNo">No</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" formControlName="substanceUseYn" value="U"
                          id="substancePast">
                        <label class="form-check-label" for="substancePast">Used in Past</label>
                      </div>
                      <label class="ml-3 mb-0">Quit Date</label>
                      <div class="ml-2" style="min-width: 110px;">
                        <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon="true"
                          formControlName="substanceQuitDate" [ngModelOptions]="{standalone: true}"
                          monthNavigator="true" [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                          showButtonBar="true"></p-calendar>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="case-details-panel">
              <div class="row" formGroupName="heartRegister">
                <!-- Contact Details Column -->
                <div class="col-md-5">
                  <div class="content-wrapper mb-2 h-100"> <!-- Added h-100 class -->
                    <div [formGroup]="contactDetailsForm" class="d-flex flex-column h-100">
                      <!-- Added flex structure -->
                      <div class="mcard-header">Contact Details
                        <button *ngIf="showDownloadButton" (click)="onAddNewContact()"
                          class="btn btn-sm btn-primary float-right">
                          Add New
                        </button>

                      </div>
                      <div class="content-wrapper mb-2 lab-results flex-grow-1">
                        <p-dataTable [immutable]="false" [value]="contacts" [editable]="true" dataKey="runId"
                          [responsive]="true">
                          <p-column field="name" header="Name">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbContactDetails">
                                <div [formGroupName]="rowIndex">
                                  <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                  <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm" formControlName="name" />
                                    <div
                                      *ngIf="getNameControl(rowIndex).touched && getNameControl(rowIndex).errors?.required"
                                      class="text-danger">
                                      <small>Name is required.</small>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="relation" header="Relations">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbContactDetails">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">
                                    {{ getRelationName(row.relation) }}
                                  </div>
                                  <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                      placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                      formControlName="relation" [dropdownPosition]="'auto'">
                                      <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{ item.relationName }}
                                      </ng-template>
                                    </ng-select>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="phone" header="Phone">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbContactDetails">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">{{ row.phone }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" maxlength="16" (keypress)="numberOnly($event)"
                                      class="form-control form-control-sm" formControlName="phone" />
                                    <div
                                      *ngIf="getPhoneControl(rowIndex).touched && getPhoneControl(rowIndex).errors?.invalidPhone"
                                      class="text-danger">
                                      <small>Phone number is invalid.</small>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="email" header="Email">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbContactDetails">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">{{ row.email }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm" formControlName="email" />
                                    <div
                                      *ngIf="getEmailControl(rowIndex).touched && getEmailControl(rowIndex).errors?.invalidEmail"
                                      class="text-danger">
                                      <small>Email is invalid.</small>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="onRowEditInitContact(row)" *ngIf="row.isEditable == false"
                                class="btn btn-sm btn-primary">
                                <i class="fa fa-edit"></i>
                              </button>
                              <button (click)="onRowEditSaveContact(row)" *ngIf="row.isEditable == true"
                                class="btn btn-sm btn-primary" [disabled]="!isContactValid(row)">
                                <i class="fa fa-save"></i>
                              </button>

                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="deleteContact(row)" class="btn btn-sm btn-primary">
                                <i class="fa fa-trash"></i>
                              </button>
                            </ng-template>
                          </p-column>
                        </p-dataTable>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Care giver text area -->

                <div class="col-md-2">
                  <div class="content-wrapper mb-2 h-100">
                    <div class="d-flex flex-column h-100">
                      <div class="mcard-header">Care Giver Details</div>
                      <div class="content-wrapper mb-2 lab-results flex-grow-1">
                        <div class="form-group">
                          <label>Details</label>
                          <textarea class="form-control form-control-sm" formControlName="caregiverDtls"></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Family History Column -->

                <div class="col-md-5">
                  <div class="content-wrapper mb-2 h-100">
                    <div class="d-flex flex-column h-100">

                      <div class="mcard-header">Family History of Liver Diseases
                        &nbsp;&nbsp;
                        <div class="form-check-inline">
                          <label class="form-check-label">
                            <input type="radio" [value]="'Y'" formControlName="familyHistYn" class="form-check-input" />
                            Yes
                          </label>
                        </div>
                        <div class="form-check-inline">
                          <label class="form-check-label">
                            <input type="radio" [value]="'N'" (click)="clearFamilyHistory()"
                              formControlName="familyHistYn" class="form-check-input" />
                            No
                          </label>
                        </div>
                        <button *ngIf="heartRegister.familyHistYn === 'Y'" (click)="onAddNewFH()"
                          class="btn btn-sm btn-primary float-right">
                          Add New
                        </button>
                      </div>
                      <div>



                        <div *ngIf="caseDetailsForm.get('heartRegister.familyHistYn').value === 'Y'"
                          [formGroup]="familyHistoryForm" class="content-wrapper mb-2 lab-results">
                          <p-dataTable [immutable]="false" [value]="famHistory" [editable]="true" dataKey="runId"
                            [responsive]="true">
                            <!-- Existing p-column definitions remain the same -->
                            <p-column field="name" header="Name">
                              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbFamilyHistory">
                                  <div [formGroupName]="rowIndex">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                    <input type="hidden" class="form-control form-control-sm"
                                      formControlName="source" />
                                    <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                    <div *ngIf="row.isEditable">
                                      <input type="text" class="form-control form-control-sm" formControlName="name" />
                                    </div>
                                  </div>
                                </ng-container>
                              </ng-template>
                            </p-column>
                            <p-column field="relation" header="Relations">
                              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbFamilyHistory">
                                  <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">
                                      {{ getRelationName(row.relation) }}
                                    </div>
                                    <div *ngIf="row.isEditable">
                                      <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                        placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                        formControlName="relation">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">
                                          {{ item.relationName }}
                                        </ng-template>
                                      </ng-select>
                                    </div>
                                  </div>
                                </ng-container>
                              </ng-template>
                            </p-column>
                            <p-column field="patientID" header="PatientID">
                              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbFamilyHistory">
                                  <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">{{ row.patientID }}</div>
                                    <div *ngIf="row.isEditable">
                                      <input type="text" (keypress)="numberOnly($event)"
                                        class="form-control form-control-sm" formControlName="patientID" />
                                    </div>
                                  </div>
                                </ng-container>
                              </ng-template>
                            </p-column>
                            <p-column field="instID" header="Institute">
                              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbFamilyHistory">
                                  <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">
                                      {{ getInstName(row.instID) }}
                                    </div>
                                    <div *ngIf="row.isEditable">
                                      <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                        placeholder="Select" bindLabel="estName" bindValue="estCode"
                                        formControlName="instID">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">
                                          {{ item.estName }}
                                        </ng-template>
                                      </ng-select>
                                    </div>
                                  </div>
                                </ng-container>
                              </ng-template>
                            </p-column>
                            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                              styleClass="foo">
                              <ng-template let-row="rowData" pTemplate="body">
                                <button (click)="onRowEditInitFH(row)"
                                  *ngIf="row.source == 'W' && row.isEditable == false" class="btn btn-sm btn-primary">
                                  <i class="fa fa-edit"></i>
                                </button>
                                <button (click)="onRowEditSaveFH(row)"
                                  *ngIf="row.source == 'W' && row.isEditable == true" class="btn btn-sm btn-primary">
                                  <i class="fa fa-save"></i>
                                </button>
                              </ng-template>
                            </p-column>
                            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                              styleClass="foo">
                              <ng-template let-row="rowData" pTemplate="body">
                                <button (click)="delete(row)" class="btn btn-sm btn-primary">
                                  <i class="fa fa-trash"></i>
                                </button>
                              </ng-template>
                            </p-column>
                          </p-dataTable>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="case-details-panel">
              <div class="row" formGroupName="heartRegister">
                <div class="col-sm-6">
                  <div class="card h-100">
                    <div class="mcard-header">Heart Failure Assessment & Management</div>
                    <div class="card-body p-2" style="overflow: hidden!important;">
                      <!-- Primary MF Etilogy Dropdown -->
                      <div class="row">
                        <div class="col-md-6">
                          <div class="form-group">
                            <label>Primary MF Etiology</label>
                            <ng-select #entryPoint appendTo="body" [items]="primaryMfEtiology" bindLabel="paramName"
                              bindValue="paramId" placeholder="Select" formControlName="primaryDiag">
                              <ng-template ng-option-tmp let-item="item">
                                {{ item.paramName }}
                              </ng-template>
                            </ng-select>
                          </div>
                        </div>

                        <div class="col-md-6">
                          <div class="form-group">
                            <label>If Other (Specify)</label>
                            <input type="text" class="form-control form-control-sm" formControlName="diagOthers" />
                          </div>
                        </div>
                      </div>

                      <div class="row">
                        <div class="col-md-6">
                          <div class="form-group">
                            <label>NYHA Class</label>
                            <ng-select #entryPoint appendTo="body" [items]="nyhaClassList" bindLabel="paramName"
                              bindValue="paramId" placeholder="Select" formControlName="nyhaClass">
                              <ng-template ng-option-tmp let-item="item">
                                {{ item.paramName }}
                              </ng-template>
                            </ng-select>
                          </div>
                        </div>

                        <div class="col-md-6">
                          <div class="form-group sp top-space">
                            <label>Duration</label>
                            <div class="d-flex align-items-center" style="gap: 10px;">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'C'" formControlName="duration" class="form-check-input"
                                    name="duration" />
                                  Chronic
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'N'" formControlName="duration" class="form-check-input"
                                    name="duration" />
                                  New
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">

                        <!-- Inotrope Agent dropdown-->

                        <div class="col-md-2">
                          <div class="form-group">
                            <label>Inotrope Used</label>
                            <div class="d-flex align-items-center" style="gap: 10px;">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'Y'" formControlName="inotropeYn"
                                    class="form-check-input" name="inotropeYn" />
                                  Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'N'" formControlName="inotropeYn"
                                    (click)="clearInotropeAgent()" class="form-check-input" name="inotropeYn" />
                                  No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div *ngIf="caseDetailsForm.get('heartRegister.inotropeYn').value === 'Y'" class="col-md-5">
                          <div class="form-group" formGroupName="heartInotropeDtls">
                            <label>Inotrope Agent</label>
                            <ng-select [items]="inotropeAgent" bindLabel="paramName" [multiple]="true"
                              bindValue="paramId" placeholder="Select" formControlName="inotropeAgentId">
                              <ng-template ng-option-tmp let-item="item">
                                {{ item.paramName }}
                              </ng-template>
                            </ng-select>
                          </div>
                        </div>
                        <!-- Other Specify Inotrope Agent-->
                        <div *ngIf="caseDetailsForm.get('heartRegister.inotropeYn').value === 'Y'" class="col-md-5">
                          <div class="form-group" formGroupName="heartInotropeDtls">
                            <label>If Other (Specify)</label>
                            <input type="text" class="form-control form-control-sm" formControlName="remarks" />
                          </div>
                        </div>

                      </div>

                      <div class="row">

                        <div class="col-md-2">
                          <div class="form-group">
                            <label>MCS Used</label>
                            <div class="d-flex align-items-center" style="gap: 10px;">
                              <div class="form-check-inline mb-0">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'Y'" formControlName="mcsDeviceUsed"
                                    class="form-check-input" name="mcsDeviceUsed" />
                                  Yes
                                </label>
                              </div>
                              <div class="form-check-inline mb-0">
                                <label class="form-check-label">
                                  <input type="radio" [value]="'N'" formControlName="mcsDeviceUsed"
                                    (click)="clearMcsDeviceUsed()" class="form-check-input" name="mcsDeviceUsed" />
                                  No
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- MSC Device Used dropdown multi select -->
                        <div *ngIf="caseDetailsForm.get('heartRegister.mcsDeviceUsed').value === 'Y'" class="col-md-4">
                          <div class="form-group" formGroupName="heartMcsDtls">

                            <label>MCS Device Used</label>
                            <ng-select #entryPoint appendTo="body" [items]="mcsDeviceUsed" bindLabel="paramName"
                              bindValue="paramId" placeholder="Select" formControlName="mcsId" [multiple]="true"
                              [closeOnSelect]="false" [searchable]="true">
                              <ng-template ng-option-tmp let-item="item">
                                {{ item.paramName }}
                              </ng-template>
                            </ng-select>
                          </div>
                        </div>

                        <div *ngIf="caseDetailsForm.get('heartRegister.mcsDeviceUsed').value === 'Y'" class="col-md-3">
                          <div class="form-group">
                            <label>Initiated Date</label>
                            <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon="true"
                              formControlName="mcsInitiatedDate" [ngModelOptions]="{standalone: true}"
                              monthNavigator="true" [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                              showButtonBar="true"></p-calendar>
                          </div>
                        </div>

                        <!-- Other Specify MCS Device Used-->
                        <div *ngIf="caseDetailsForm.get('heartRegister.mcsDeviceUsed').value === 'Y'" class="col-md-3">
                          <div class="form-group" formGroupName="heartMcsDtls">
                            <label>If Other (Specify)</label>
                            <input type="text" class="form-control form-control-sm" formControlName="remarks" />

                          </div>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-sm-6">
                  <div class="row">
                    <div class="col-sm-12">
                      <div class="card h-100">
                        <div class="mcard-header">Pre Operative Evaluation</div>
                        <div class="card-body p-2" style="overflow: hidden!important;">
                          <!-- Psychological Assessment Summary text area -->
                          <div class="row">
                            <div class="col-md-12">
                              <div class="form-group">
                                <label>Psychological Assessment Summary</label>
                                <textarea class="form-control form-control-sm" formControlName="psychosocialAssessment"
                                  placeholder="Enter psychological assessment summary" rows="2"></textarea>
                              </div>
                            </div>
                          </div>
                          <!-- Dietician Review text area -->
                          <div class="row">
                            <div class="col-md-12">
                              <div class="form-group">
                                <label>Dietician Review</label>
                                <textarea class="form-control form-control-sm" formControlName="dieticianReview"
                                  placeholder="Enter dietician review" rows="2"></textarea>
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-4">
                              <div class="form-group">
                                <label>Psychological Team Clearance</label>
                                <ng-select #entryPoint appendTo="body" [items]="psychologicalTeamClearance"
                                  bindLabel="paramName" bindValue="paramId" placeholder="Select"
                                  formControlName="psychosocialClearance">
                                  <ng-template ng-option-tmp let-item="item">
                                    {{ item.paramName }}
                                  </ng-template>
                                </ng-select>
                              </div>
                            </div>

                            <div class="col-md-4">
                              <div class="form-group">
                                <label>Physiotherapy Baseline</label>
                                <ng-select #entryPoint appendTo="body" [items]="physiotherapyBaseline"
                                  bindLabel="paramName" bindValue="paramId" placeholder="Select"
                                  formControlName="physiotherapyBase">
                                  <ng-template ng-option-tmp let-item="item">
                                    {{ item.paramName }}
                                  </ng-template>
                                </ng-select>

                              </div>
                            </div>
                            <!-- Financial/Insurance approval dropdown -->
                            <div class="col-md-4">
                              <div class="form-group">
                                <label>Financial/Insurance approval</label>
                                <ng-select #entryPoint appendTo="body" [items]="financialInsuranceApproval"
                                  bindLabel="name" bindValue="id" placeholder="Select"
                                  formControlName="financialInsuranceYn">
                                  <ng-template ng-option-tmp let-item="item">
                                    {{ item.name }}
                                  </ng-template>
                                </ng-select>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </form>



        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>


  <div class="case-details-panel">
    <div class="row">
      <div class="col-md-6">
        <div class="content-wrapper mb-2 h-100">
          <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">Medication
          </h6>
          <app-medication [submitted]="submitted" [showAddNewButton]="showButton"
            [showMedicineButton]="showDownloadButton && (!Medication.medFg || Medication.medFg.length === 0)"
            #Medication (callGenMedList)="callGenMedList()"
            (downloadMedicationDtsAlshifa)="fetchMedicineDtlsFromAlShifa()"></app-medication>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="card mb-2 h-100"> <!-- Reduced bottom margin -->
          <div class="mcard-header">
            Medical Procedures
            <button *ngIf="showDownloadButton && (!procedureDetails || procedureDetails.length === 0)"
              (click)="downloadMedicalProcedures()" class="btn btn-sm btn-primary float-right">
              Download
            </button>
          </div>
          <div class="card-body"> <!-- Reduced padding -->
            <!-- Procedure Details -->
            <div class="selected-procedures"> <!-- Reduced top margin -->
              <table class="table table-sm table-striped">
                <thead>
                  <tr>
                    <th style="width: 30%">Procedure Type</th>
                    <th style="width: 20%">Done Date</th>
                    <th style="width: 50%">Remarks</th> <!-- Adjusted width for better distribution -->
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="let name of uniqueProcedureTypes.slice(0, 10) | slice: (page-1) * pageSize : (page-1) * pageSize + pageSize">
                    <td>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input form-check-input-sm" type="checkbox" [id]="'proc_' + name"
                          [checked]="isProcedureSelected(name)" (change)="onProcedureTypeSelect(name)">
                        <label class="form-check-label" [for]="'proc_' + name">
                          {{ name }}
                        </label>
                      </div>
                    </td>
                    <td>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModel]="getProcedureDetail(name)?.doneDate"
                        (ngModelChange)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'doneDate', $event)"
                        [disabled]="!isProcedureSelected(name)" monthNavigator="true" [maxDate]="today"
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true" appendTo="body">
                      </p-calendar>
                    </td>

                    <td>
                      <input type="text" class="form-control form-control-sm" style="width: 100%;"
                        [value]="getProcedureDetail(name)?.remarks"
                        (input)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'remarks', $event.target.value)"
                        placeholder="Enter remarks" [disabled]="!isProcedureSelected(name)">
                    </td>
                  </tr>
                </tbody>
              </table>
              <ngb-pagination class="d-flex justify-content-center" [(page)]="page" [pageSize]="3"
                [collectionSize]="10">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


  <div class="case-details-panel">
    <div class="content-wrapper mb-2 ">
      <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">Surgeries </h6>
      <app-surgery [submitted]="submitted" [showAddNewButton]="showButton"
        [showSurgeryButton]="showDownloadButton && (!surgery.surgList || surgery.surgList.length === 0)"
        (downloadSurgery)="fetchSurgeryFromAlShifa()" #surgery></app-surgery>
    </div>
  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <div [formGroup]="labTestForm">
        <!-- <div class="card-header">
      Lab Results
    </div> -->
        <h6 style="font-weight: bold; color: #973633;
    font-family: open_sanssemibold;
    background: #fff;">
          Lab Results
          <button class="btn btn-sm float-right"></button>

        </h6>
        <div class="content-wrapper mb-2 lab-results">
          <div class="text-right pb-2">
            <button *ngIf="showDownloadButton" (click)="addNewlab()" class="btn btn-sm btn-primary">
              Add New
            </button>
            <button *ngIf="showDownloadButton && (!labnewList || labnewList.length === 0)"
              class="btn btn-sm btn-primary" (click)="openModal(downloadLabTest)">Download</button>
          </div>

          <p-dataTable [immutable]="false" [value]="labnewList" [editable]="true" dataKey="runId" [responsive]="true">
            <p-column field="testDate" header="Date">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredDate" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="releasedDate" />

                    <div *ngIf="!row.isEditable">
                      {{ row.testDate | date : "dd-MM-yyyy" }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="testDate"
                        [ngModelOptions]="{ standalone: true }" monthNavigator="true" [maxDate]="today"
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                      </p-calendar>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="profileTestCode" header="Profile Name">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.profileTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="profileList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="testName" bindValue="testId" formControlName="profileTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.testName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="mohTestCode" header="Test Component">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.componentTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="ComponetList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="componentTestName" bindValue="componentTestId" formControlName="mohTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.componentTestName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="value" header="Result">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.value }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="value" (input)="validateNumberInput($event)" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="unit" header="Unit">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.unit }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" class="form-control form-control-sm" formControlName="unit" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="instCode" header="Institute">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.instName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="instCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.estName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-edit"></i>
                </button>
                <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-save"></i>
                </button>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="deletelab(row)" class="btn btn-sm btn-primary">
                  <i class="fa fa-trash"></i>
                </button>
              </ng-template>
            </p-column>
          </p-dataTable>
          <div *ngIf="labnewList && labnewList.length > 0">
            <p-paginator #labTestPaginator [rows]="paginationSize" [totalRecords]="totalLabRecords"
              (onPageChange)="onLabPageChange($event)" showCurrentPageReport="true"
              currentPageReportTemplate="(Total: {{totalLabRecords}} records)" pageLinkSize="10">
            </p-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <app-vaccination [showAddNewButton]="showButton"
        [showVaccineButton]="showButton && (!Vaccination.vaccineFg || Vaccination.vaccineFg.length === 0)"
        [calledFromParent]="true" #Vaccination (downloadVaccination)="fetchVaccineFromAlShifa()"></app-vaccination>
    </div>
  </div>

  <div class="col-sm-12">
    <div class="btn-container">
      <button *ngIf="showButton" type="submit" class="btn btn-primary" (click)="saveDetails()">
        Save
      </button>
      <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
      <button class="btn btn-primary ripple" (click)="navigateToRegister()">
        Back to register page
      </button>
      <button *ngIf="isPrint" (click)="generatePDF()" class="btn btn-sm btn-primary">Download PDF</button>
    </div>
  </div>


  <ng-template #downloadLabTest let-modal>

    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">Download Lab Test</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body" style="height: 250px;">

      <form [formGroup]="filterModelForm">
        <div class="row">
          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>From Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="fromDate">
            </p-calendar>

          </div>


          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>To Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="toDate">
            </p-calendar>
          </div>

        </div>
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <br>
            <label>Test Name</label>
            <div>
              <ng-multiselect-dropdown
                class=" multiappend custom-dropdown custom_color custom_box_color check_box_custom-color"
                [placeholder]="'Add Test'" [data]="testListToDownload" [settings]="dropdownSettings"
                formControlName="profileT">
              </ng-multiselect-dropdown>
            </div>

          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-sm btn-primary" (click)="callFetchLabDataFromAlShifa()"
        (click)="modal.dismiss('Cross click')">Download</button>
      <button type="button" class="btn btn-sm btn-secondary" (click)="resetFormModal()">Reset</button>
    </div>
  </ng-template>
</div>