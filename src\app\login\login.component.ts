﻿import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import * as CommonConstants from '../_helpers/common.constants';
import { AuthenticationService } from '../_services/authentication.service';
import { MasterService } from '../_services/master.service';
import { BehaviorSubject, Observable } from 'rxjs-compat';
import { NoRowsOverlayComponent } from 'ag-grid-community/dist/lib/rendering/overlays/noRowsOverlayComponent';
import { formatDate } from '@angular/common';
import { LoginService } from './login.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { CaptchaComponent } from 'angular-captcha';
import * as AppUtils from '../common/app.utils';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';




//import { AlertService, AuthenticationService } from '@/_services';

@Component({ templateUrl: 'login.component.html', styleUrls: ['login.component.scss'] })
export class LoginComponent implements OnInit {
@ViewChild('updateOtpProfile', { static: true }) updateOtpProfileModal: TemplateRef<any>;
@ViewChild(CaptchaComponent, { static: true }) captchaComponent: CaptchaComponent;


  loginForm: FormGroup;
  updateOtpProfileForm: FormGroup;

  username: string;
  password: string;
  loading = false;
  submitted = false;
  returnUrl: string;
  message;
  currentYear;
  checkIf2FAIsRequired:any;
  oAuthSysConfigParams: any;
  validatedPersonInfo: any;
  perscode: number;
  validateCivilCardFlag: boolean = false;
  mpiResponseCivilIdMsg: any;
  

  resendDisabled = true;
  countdown = 5;
  timer: any;
  validatedMobileNo: any;
  showOtpProfUpdate: boolean = false;
  updateProfOtpBtntxt: string = "Request OTP";
  maskedMobileNumber: string;
  showOTP: boolean = false;
  loginAfterProfUpdate: boolean = false;
  loginAttempt: 0;
  maxClientLoginAttempts: number = CommonConstants.MAX_CLIENT_INVALID_LOGIN_ATTEMPTS;
  maxServerLoginAttempts: number = CommonConstants.MAX_SERVER_INVALID_LOGIN_ATTEMPTS;

  showCaptcha: boolean = false;
  error = '';




  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authenticationService: AuthenticationService,
    private masterService: MasterService,
    private loginService: LoginService,
    private modalService: NgbModal,
    public http: HttpClient

    //   private authenticationService: AuthenticationService,
    //  private alertService: AlertService
  ) {
    // redirect to home if already logged in
    /*    if (this.authenticationService.currentUserValue) {
           this.router.navigate(['/']);
       } */
  }
 ngAfterViewInit(): void {
  if (this.captchaComponent) {
    this.captchaComponent.captchaEndpoint = AppUtils.BTC_END_POINT;
  }
  }

  ngOnInit() {
    this.currentYear = formatDate(new Date(), 'yyyy', 'en');
    this.loginForm = this.formBuilder.group({
      userName: ['', Validators.required],
      password: ['', Validators.required],
      captchaCode: [''], 
      otpCode: ['']   
    });
     this.updateOtpProfileForm = this.formBuilder.group({
      civilId: ['', [Validators.required, Validators.pattern(/^\d{6,15}$/)]], // example pattern
      civilExpiryDate: ['', Validators.required],
      emailId: ['', [Validators.required, Validators.email]],
      mobileNo: ['', [Validators.required, Validators.pattern(/^\d{8,15}$/)]],
      whatsAppNo: [null, [Validators.pattern(/^\d{8,15}$/)]],
      otpCode: [''] // shown conditionally, so not required initially
    });

    // get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
  }

  // convenience getter for easy access to form fields
  get f() { return this.loginForm.controls; }
  get uop() { return this.updateOtpProfileForm.controls };


  login(userName: string, password: string) {
      this.submitted = true;

    // stop here if form is invalid
    if (this.loginForm.invalid) {
      return;
    }
    // this.username = this.f.userName.value;
    // this.password = this.f.password.value;

    this.loading = true;
    event.preventDefault();
    
    if (this.checkIf2FAIsRequired) {
      if (this.checkIf2FAIsRequired === "Y") {
        //Load the oAuth system config params
        this.loginService.getAuthSystemConfigParams().subscribe(res => {
        if (res) {
        this.oAuthSysConfigParams = res.result;
        }
        });
        if (this.validatedPersonInfo && this.validatedPersonInfo.profValidatedYn && this.validatedPersonInfo.profValidatedYn === 'Y') {

           this.authenticateWithOTP();
        }
        else{
          this.authenticateWith2FA(this.f.userName.value, this.f.password.value);
        }
      }

      else {//if 2FA not required
        this.authenticateWithOut2FA(this.f.userName.value, this.f.password.value);
      }
    }
    else {

      this.loginService.checkIf2FAIsRequired(this.f.userName.value).subscribe(res => {

        if (res) {
          this.checkIf2FAIsRequired = res;
          if (res === "Y") {

            this.authenticateWith2FA(this.f.userName.value, this.f.password.value);

          } else {
            // Proceed with normal login
            this.authenticateWithOut2FA(this.f.userName.value, this.f.password.value);

          }
        }
      });
    }
  }

  loadUserDetails() {
    this.masterService.regionsMaster = new BehaviorSubject<any>(null);
    this.masterService.institutesMaster = new BehaviorSubject<any>(null);
    this.masterService.wallayatMaster = new BehaviorSubject<any>(null);
    

    this.authenticationService.getUserDetails().subscribe((res) => {
      this.router.navigate(['/home']);
    });
  }

  
  authenticateWith2FA(username, password) {
    let data = {
      username: username,
      password: password,
      clientId: CommonConstants.CLIENT_ID,
      systemId: CommonConstants.SYSTEM_ID
    };
    this.loginService.validateLoggedUser(data).subscribe(res => {

      if (res && res.code && res.code === CommonConstants.OAUTH_SUCCESS_CODE) {
        //generate pre auth access token 
        if (res.result && res.result.preAuthToken)
          sessionStorage.setItem(CommonConstants.STORAGE_PREAUTH_ACCESS_TOKEN, res.result.preAuthToken);
        const result = res.result;
        this.loading = false;
        

        this.perscode = result.perscode;
        this.validatedMobileNo = result.telGsm;
        this.populateFormValues(result);

        if (!result.profValidatedYn || result.profValidatedYn === 'N') { //if null or N
          //open modal for profle update
          this.openUpdateOtpProfileModal();
        } 
        else if(!this.isValidCivilExpiryDateStatus(result.civilIdExpiry))//check the civil expiry validity
        {
           //open modal for profle update
          this.openUpdateOtpProfileModal();
        }
        else {
          // Proceed with sending OTP {"isdCode":968,"mobile":97940588,"systemId":9} SYSTEM_ID_EREF
          if (this.validatedMobileNo) {

            this.sendOtp().subscribe(success => {   
              if (success) {
                this.showOTP = true;
                return this.loading = false;
              } else {
                this.showOTP = false;
              }
            });
            /*
            this.maskedMobileNumber = this.maskMobile(result.telGsm);
            console.log("Masked Mobile Number: ", this.maskedMobileNumber);
            //this.showOTP = true;
            // return this.loading = false;

            let data = {
              perscode: this.perscode,
              username: this.model.username,
              action: AppConst.AUTHENTICATE_2FA,
              systemId: AppConst.SYSTEM_ID_EREF,
              isPreAuth: "Y",
              isdCode: AppConst.ISD_CODE,
              mobile: result.telGsm,


            };
            this.loginService.getOTP(data).subscribe(otpResponse => {
              console.log("OTP Response: ", otpResponse);
              //  
              if (otpResponse && otpResponse.result && otpResponse.result === true) {

                // OTP sent successfully, redirect to OTP verification page
                this.showOTP = true;
                return this.loading = false;
                this.startResendTimer();

              } else {
                this.loading = false;
                this.showOTP = false;
                Swal.fire('Error', "Failed to send OTP", 'error');
              }
            });*/
            }
          else {

            this.openUpdateOtpProfileModal();

          }
        }

      }
      else {
        this.loading = false;
        Swal.fire('Error', res.message, 'error');
      }
    });




  }
  populateFormValues(res: any) {

    this.validatedPersonInfo = res;
    if (this.validatedPersonInfo && this.validatedPersonInfo.perscode) {
      this.perscode = this.validatedPersonInfo.perscode;

    }
    this.updateOtpProfileForm.patchValue({
      civilId: res.civilId,
      civilExpiryDate: res.civilIdExpiry,
      emailId: res.officialEmail,
      mobileNo: res.telGsm,
      whatsAppNo: res.whatsappNo ? res.whatsappNo : res.telGsm
    });
  }

   openUpdateOtpProfileModal() {
    this.modalService.open(this.updateOtpProfileModal, { size: 'lg', backdrop: 'static', keyboard: false }).result.then((result) => {
      if (!result || result === 'Close click') {
        this.close();

      }
    }, (reason) => {
      console.log('Modal dismissed with reason:', reason);
      this.close();
     
    });
  }

  close() {
    
    this.updateOtpProfileForm.reset();
    this.showOtpProfUpdate = false;
    this.updateProfOtpBtntxt = "Request OTP";
    this.maskedMobileNumber = '';
    //this.perscode = null;
    this.mpiResponseCivilIdMsg = '';
    this.validateCivilCardFlag = false;
    this.mpiResponseCivilIdMsg = '';
    //this.updateOtpProfileForm.markAsPristine();
    //this.updateOtpProfileForm.markAsUntouched();
    this.modalService.dismissAll();
    this.router.navigate(['/login']);
    this.loading = false;
    window.location.reload();
  }

  isValidCivilExpiryDateStatus(input: string | Date): boolean | void {
  const currentDate = new Date();
  const dateToCheck = new Date(input);

  // Proper TypeScript-safe check for invalid date
  if (isNaN(dateToCheck.getTime())) {
    console.warn("Invalid date provided.");
    return false;
  }

  if (dateToCheck >= currentDate) {
    console.log("Date is valid (not expired).");
    return true;
  }

  // Check if within grace period
  const graceEndDate = new Date(dateToCheck);
  graceEndDate.setMonth(graceEndDate.getMonth() + CommonConstants.GRACE_PERIOD_MONTHS); // Assuming grace period is defined in months

  if (graceEndDate >= currentDate) {
    console.warn("Date is expired but within grace period!");
    return true;
  } else {
    console.error("Date is expired and outside grace period.");
    return false;
  }
   
}
  sendOtp(): Observable<boolean> {
    if(this.updateOtpProfileForm.get('mobileNo').value)
    this.validatedMobileNo = this.updateOtpProfileForm.get('mobileNo').value; 
   // Use validatedMobileNo if available, otherwise use the form value
    this.maskedMobileNumber = this.maskMobile(this.validatedMobileNo);
    //this.updateProfOtpBtntxt = "Resend OTP";
    //this.showOtpProfUpdate = true;

    let data = {
      perscode: this.perscode,
      username: this.f.userName.value,
      action: CommonConstants.AUTHENTICATE_2FA,
      systemId: CommonConstants.SYSTEM_ID,
      isPreAuth: "Y",
      isdCode: CommonConstants.ISD_CODE,
      mobile: this.validatedMobileNo, // Use validatedMobileNo if available, otherwise use the form value
    };

    return new Observable<boolean>(observer => {
      this.loginService.getOTP(data).subscribe(otpResponse => {
        if (otpResponse && otpResponse.result === true) {
          this.startResendTimer();

          observer.next(true);
        } else {
          Swal.fire('Error', "Unable to send OTP", 'error');
          observer.next(false);
          this.loading = false;
        }
        observer.complete();
      }, error => {
        Swal.fire('Error', "Unable to send OTP", 'error');
        observer.next(false);
        observer.complete();
        this.loading = false;
      });
    });
  }
 maskMobile(mobile: any): string {
    const mobileStr = String(mobile);  // ensure it's a string
    if (!mobileStr || mobileStr.length < 4) return '****';
    const last4 = mobileStr.slice(-4);
    return '****' + last4;
  }

   startResendTimer() {
    this.resendDisabled = true;
    this.countdown = 120;//2 mins

    // Clear any existing timer
    if (this.timer) {
      clearInterval(this.timer);
    }

    // Start countdown
    this.timer = setInterval(() => {
      this.countdown--;

      if (this.countdown <= 0) {
        this.resendDisabled = false;
        clearInterval(this.timer);
      }
    }, 1000);
  }
   authenticateWithOut2FA(userName, password) {
    // Proceed with normal login   
   this.authenticationService.userAuthentication(this.f.userName.value, this.f.password.value).subscribe((data: any) => {
       this.loading = false;
        localStorage.setItem(CommonConstants.TOKEN, data.access_token);
        localStorage.setItem(CommonConstants.REFRESH_TOKEN, data.refresh_token);
        localStorage.setItem(
          CommonConstants.STORAGE_ACCOUNT_EXPIRES_IN,
          data.expires_in
        );
      this.loadUserDetails();
    },
    error => {
      this.message ="Login Failed"
    });
  
  }

   validateOtpFields() {
    if (
      this.updateOtpProfileForm.get('civilId').valid &&
      this.updateOtpProfileForm.get('civilExpiryDate').valid &&
      this.updateOtpProfileForm.get('emailId').valid &&
      this.updateOtpProfileForm.get('mobileNo').valid
    ) {
      this.validatedMobileNo = this.updateOtpProfileForm.get('mobileNo').value;
      if (!this.validateCivilCardFlag) {
        this.validateCivilCardInfo().subscribe(isValid => {
          if (isValid) {
            this.sendOtp().subscribe(success => {
              if (success) {
                // OTP sent, continue your logic here
                this.showOtpProfUpdate = true;
              } else {
                Swal.fire('Error', "Unable to send OTP", 'error');
              }
            });
          } else {
            Swal.fire('Error', "Please revalidate the information", 'error');
          }
        });
      } else {
        this.sendOtp().subscribe(success => {
          if (success) {
            // OTP sent, continue your logic here
            this.showOtpProfUpdate = true;
          } else {
            Swal.fire('Error', "Unable to send OTP", 'error');
          }
        });
      }
    } else {
      this.updateOtpProfileForm.markAllAsTouched();
      return false;
    }
  }

   validateCivilCardInfo(): Observable<boolean> {
    this.mpiResponseCivilIdMsg = '';
    this.validateCivilCardFlag = false;
    return new Observable<boolean>(observer => {
      if (this.uop.civilId.value && this.uop.civilId.valid &&
        this.uop.civilExpiryDate.value && this.uop.civilExpiryDate.valid) {

        let mpiInput: any = {};
        mpiInput.civilId = this.uop.civilId.value;
        mpiInput.cardExpiryDate = this.uop.civilExpiryDate.value;
        mpiInput.requesterCivilId = this.uop.civilId.value;
        mpiInput.username = this.f.userName.value;
        mpiInput.systemId = CommonConstants.SYSTEM_ID;
        mpiInput.perscode = this.perscode;
        mpiInput.action = CommonConstants.AUTHENTICATE_2FA;
        mpiInput.requesterPersCode = this.uop.civilId.value;

        this.loginService.getMpiDetailsOAuth(mpiInput).subscribe(res => {
          if (res && res.result && res.result.code == CommonConstants.MPI_CIVIL_ID_SUCCESS_CODE) {
            this.mpiResponseCivilIdMsg = "Civil ID information is validated successfully.";
            this.validateCivilCardFlag = true;
            observer.next(true);
          } else {
            this.mpiResponseCivilIdMsg = "Civil ID validation failed. Please try again or contact System Admin";
            this.validateCivilCardFlag = false;
            observer.next(false);
          }
          observer.complete();
        }, error => {
          this.mpiResponseCivilIdMsg = "Civil ID validation failed. Please try again or contact System Admin";
          this.validateCivilCardFlag = false;
          observer.next(false);
          observer.complete();
        });
      } else {
        observer.next(false);
        observer.complete();
      }
    });
  }
   onBlurCivilId() {
    this.validateCivilCardInfo().subscribe(isValid => {
     });
  }

 onSubmitOtpProfileFormForm(): void {
    if (this.updateOtpProfileForm.valid && this.validateCivilCardFlag == true) {
      const formData = this.updateOtpProfileForm.value;
      this.validatedMobileNo = formData.mobileNo;
      //validate the entered OTP

      //validate the entered OTP

      this.validateOTP(formData.otpCode).subscribe(isValid => {
        if (isValid) {
           let data = {
              perscode: this.perscode,
              username: this.f.userName.value,
              password: this.password,
              action: CommonConstants.AUTHENTICATE_2FA,
              systemId: CommonConstants.SYSTEM_ID,
              isPreAuth: "Y",
              civilId: formData.civilId,
              civilIdExpiry: formData.civilExpiryDate,
              officialEmail: formData.emailId,
              telGsm: formData.mobileNo,
              whatsappNo: formData.whatsAppNo ? formData.whatsAppNo : formData.mobileNo,
            };
            this.loginService.saveOtpPersonInfoCmast(data).subscribe(updateRes => {
              if (updateRes && updateRes.result && updateRes.code === 1) {
                Swal.fire('Success', 'Profile updated successfully!', 'success');
                this.validatedPersonInfo.profValidatedYn = 'Y';
                this.loginAfterProfUpdate = true;
                //this.showOtpProfUpdate = false;
                //this.perscode = updateRes.result;
                this.modalService.dismissAll();
                this.router.navigate(['/login']);
              } else {
                Swal.fire('Error', 'Profile update failed!', 'error');
              }
            });
        }
        else{
            Swal.fire('Error', 'Please enter a valid OTP!', 'error');
        }
      });

    }

  }
   validateOTP(otp: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      if (otp && otp.trim() !== '') {
        this.loginService.validateOTP({
          isdCode: CommonConstants.ISD_CODE,
          mobile: this.validatedMobileNo,
          otp: otp,
          systemId: CommonConstants.SYSTEM_ID
        }).subscribe(res => {
          if (res && res.result && res.result === true) {
            observer.next(true);
          } else {
            Swal.fire('Error', 'Please enter a valid otp!', 'error');
            this.loading = false;
            observer.next(false);
          }
          observer.complete();
        }, error => {
          Swal.fire('Error', 'Please enter a valid otp!', 'error');
          this.loading = false;
          observer.next(false);
          observer.complete();
        });
      } else {
        Swal.fire('Error', 'Please enter a valid otp!', 'error');
        observer.next(false);
        observer.complete();
      }
    });
  }

  authenticateWithOTP() {

    if (this.f.otpCode.value) {
      this.validateOTP(this.f.otpCode.value).subscribe(isValid => {
        if (isValid) {
          this.authenticateWithOut2FA(this.f.userName.value, this.f.password.value);
        }
      });
    }
    else {
      if (this.loginAfterProfUpdate) {
       // this.validatedMobileNo = this.uop.mobileNo.value;
       // this.getOTP();
        this.sendOtp().subscribe(success => {
              if (success) {
                this.showOTP = true;
                return this.loading = false;
              } else {
                 this.showOTP = false;
              }
            });


      }
      else if(!this.isValidCivilExpiryDateStatus(this.validatedPersonInfo.civilIdExpiry))//check the civil expiry validity
        {
           //open modal for profle update
          this.populateFormValues(this.validatedPersonInfo);
          this.openUpdateOtpProfileModal();
        }
      else {
        this.validatedMobileNo = this.validatedPersonInfo.telGsm;
        this.sendOtp().subscribe(success => {
              if (success) {
                this.showOTP = true;
                return this.loading = false;
              } else {
                 this.showOTP = false;
              }
            });
      }

    }

  }

   authenticate() {
    const username = this.f.userName.value;
    const password = this.f.password.value;
    // this.loginAttempt = this.sharedService.getInvalidLoginCount();


    if (!environment.otp2FA) {
      this.authenticateWithOut2FA(username, password);
      return;
    }


    sessionStorage.setItem("login-attempted-user", username);
    this.loginService.getUserAttempts(username).subscribe(res => {
      if (res.result) {
        this.loginAttempt = res.result['attempts'];
      } else {
        this.loginAttempt = 0;
      }

      sessionStorage.setItem(CommonConstants.FAILED_ATTEMPTS, this.loginAttempt.toString());
      if (this.loginAttempt >= this.maxClientLoginAttempts && this.loginAttempt <= this.maxServerLoginAttempts) {
        // setTimeout(() => {
        let captchaCode: string;
        let captchaId: string;
        if (this.loginAttempt >= this.maxServerLoginAttempts) {
          this.showCaptcha = false;
        } else {
          this.showCaptcha = true;
        }
        captchaCode = this.captchaComponent.captchaCode;
        captchaId = this.captchaComponent.captchaId;

        if (captchaCode === '' && this.loginAttempt >= this.maxClientLoginAttempts && this.loginAttempt !== this.maxServerLoginAttempts) {
          this.error = CommonConstants.CAPTCHA_EMPTY;
          // Swal.fire('Invalid Captcha', AppUtils.CAPTCHA_EMPTY, 'error');
          return;
        }



        const postData = {
          captchaCode: this.captchaComponent.captchaCode,
          captchaId: this.captchaComponent.captchaId
        }
        if (this.showCaptcha) {
          this.http.post(AppUtils.BTC_VERIFICATION, postData).subscribe(response => {
            console.log(response);


            if (response['result'] === false) {
              this.error = CommonConstants.CAPTCH_INVALID
              this.captchaComponent.reloadImage();
              Swal.fire('Invalid Captcha', CommonConstants.CAPTCH_INVALID, 'error');
              return;
            }
            this.login(username, password);
            if (this.showCaptcha) {
              // this.captchaComponent.reloadImage();
            }
          });
        } else {
          this.login(username, password);
        }

      }
      else {
        this.login(username, password);
      }
    });
  }
  


}
