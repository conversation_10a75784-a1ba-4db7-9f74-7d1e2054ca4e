<h6 class="page-title">Renal Listing </h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="renalSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil Id</label>
                    <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registration No</label>
                    <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="centralRegNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                    bindValue="id" formControlName="sex">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                    </ng-template>
                </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age (from)</label>
                    <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="ageFrom">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>To</label>
                    <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="ageTo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
                    bindValue="regCode" formControlName="regCode"  (change)="regSelect($event,'region')">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                    </ng-template>
                </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="walName"
                    bindValue="walCode" formControlName="walCode" (change)="walSelect($event,'wilayat')">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                    </ng-template>
                </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
                    bindValue="estCode" formControlName="estCode">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                    </ng-template>
                </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause of Kidney Disease</label>
                    <ng-select #entryPoint [items]="icdList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="disease" bindValue="icd" formControlName="causeKidney">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.disease}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Stages</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="stages">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Dialysis</label>
                    <ng-select #entryPoint [items]="yesNo" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="dialysis">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>


                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Trasplant</label>
                    <ng-select #entryPoint [items]="yesNo" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="trasplant">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-4 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>Blood Group</strong></label><br>
                    <div class="chexkBoxList" style="padding-left: 30px;">
                        <input type="checkbox" value="A +" (change)="bloodGroupSelect($event,'group')" > A +
                        <input type="checkbox" value="A -" (change)="bloodGroupSelect($event,'group')" > A -
                        <input type="checkbox" value="B +" (change)="bloodGroupSelect($event,'group')" > B +
                        <input type="checkbox" value="B -" (change)="bloodGroupSelect($event,'group')" > B -
                        <input type="checkbox" value="O +" (change)="bloodGroupSelect($event,'group')" > O +
                        <input type="checkbox" value="O -" (change)="bloodGroupSelect($event,'group')" > O -
                        <input type="checkbox" value="AB +"(change)="bloodGroupSelect($event,'group')" > AB +
                        <input type="checkbox" value="AB -"(change)="bloodGroupSelect($event,'group')" > AB -
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Readiness of Transpant</label>
                    <ng-select #entryPoint [items]="yesNo" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="readiness">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>


                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Status</label>
                    <ng-select #entryPoint [items]="status" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="status">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <label>Dialysis Duration </label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">                           
                            <input (keypress)="numberOnly($event)" type="text" placeholder="From" class="form-control form-control-sm" formControlName="dialysisDurationFrom">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">                            
                            <input (keypress)="numberOnly($event)" type="text" placeholder="To" class="form-control form-control-sm" formControlName="dialysisDurationTo">
                        </div>
                    </div>
                </div>
                
            </div>

           

            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="submit"  (click)="exportExcel()" class="btn btn-primary ripple" > EXCEL</button>
                    <button type="reset" (click)="clear($event)" class="btn btn-sm btn-secondary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
                </div>
            </div>
        </div>
    </form>
</div>

<div style="margin-top:20px">
    <ag-grid-angular
    style="width: 100%; height: 300px;"
    class="ag-theme-balham"
    [rowData]="rowData"
    [columnDefs]="columnDefs"
    (rowDoubleClicked)="onCellDoubleClicked($event)"
    [gridOptions]="gridOptions"
    >
</ag-grid-angular>

</div>