<h6 class="page-title">Vaccine Listing</h6>
<div class="content-wrapper">
    <form [formGroup]="vaccineSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registration No</label>
                    <input type="text" type="number" class="form-control form-control-sm"
                        formControlName="centralRegNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Vaccine:</label>
                    <ng-select #entryPoint [items]="vaccineTbMaster" [virtualScroll]="true" placeholder="Select"
                    bindLabel="vaccineName" bindValue="vaccineId" formControlName="vaccineName">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.vaccineName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>



            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region:</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="id" formControlName="region"
                        (change)="regSelect($event,'region')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Establishment:</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="estCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Year:</label>
                    <ng-select #entryPoint [items]="years" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="value" formControlName="year">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Month:</label>
                    <ng-select #entryPoint [items]="month" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="month">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

        </div>


        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3 ">
                <label>Vaccination Date:</label>
                <p-calendar type="number" dateFormat="dd-mm-yy" formControlName="vaccinationDateFrom"
                    id="vaccinationDateFrom" placeholder="From" monthNavigator="true" yearNavigator="true"
                    yearRange="2000:2030" [maxDate]="vaccinationDateMax" (onFocus)="onFocusCalendar()">
                </p-calendar>

            </div>
            <div class="col-lg-2 col-md-3 col-sm-3 " style="padding-top: 28px;">
                <p-calendar type="number" dateFormat="dd-mm-yy" formControlName="vaccinationDateTo"
                    id="vaccinationDateTo" placeholder="To" monthNavigator="true" yearNavigator="true"
                    yearRange="2000:2030" [minDate]="vaccinationDateMax" [maxDate]="vaccinationDateMin"
                    (onFocus)="onFocusCalendar()"></p-calendar>
            </div>



            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group ">
                    <label>Civil Id:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group ">
                    <label>VaccineBatch No:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="vaccineBatchNo">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group ">
                    <label>SyringeBatch No:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="syringeBatchNo">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group ">
                    <label>Schedule Id:</label>
                    <ng-select #entryPoint [items]="period" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="value" formControlName="periodDesc">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

        </div>

        <div class="row">

            
                <div class="col-lg-2 col-md-3 col-sm-3" >
                   
                        <label>Defaulters:</label>
                        <p-calendar type="number" dateFormat="dd-mm-yy" formControlName="defaultersFrom"
                            id="defaultersFrom" placeholder="From" monthNavigator="true" yearNavigator="true"
                            yearRange="2000:2030" [maxDate]="defaultersDateMax"
                            (onFocus)="onFocusCalendar()"></p-calendar>

                </div>
                <div class="col-lg-2 col-md-3 col-sm-3" style="padding-top: 28px;">
    
                        <p-calendar type="number" dateFormat="dd-mm-yy" formControlName="defaultersTo" id="defaultersTo"
                            placeholder="To" monthNavigator="true" yearNavigator="true" yearRange="2000:2030"
                            [minDate]="defaultersDateMax" [maxDate]="defaultersDateMin" (onFocus)="onFocusCalendar()">
                        </p-calendar>

                
                    
                </div>
          
        </div>



        <div class="text-right col-lg-12 col-md-12 col-sm-12">
            <button type="submit" class="btn btn-primary ripple" (click)="onClear()">Clear</button>
            <button type="submit" class="btn btn-primary ripple" (click)="getVaccineListing()">Search</button>
            <button type="submit" (click)="exportExcel()" class="btn btn-primary ripple">Excel</button>

        </div>


        <div style="margin-top:20px">

            <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="vaccineAdmDetl"
                [columnDefs]="columnDefs" (gridReady)="onGridReady($event)"
                (rowDoubleClicked)="onCellDoubleClicked($event)" [frameworkComponents]="frameworkComponents"
                [gridOptions]="gridOptions">
            </ag-grid-angular>


        </div>



        <div *ngIf="vaccineAdmDetl.length > 0">
            <p-paginator #vaccineRegPaginator rows={{paginationSize}} totalRecords="{{totalRecords}}"
                (onPageChange)="getVaccineListing($event)" showCurrentPageReport="true"
                currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10">
            </p-paginator>
        </div>

    </form>

    <ng-template #viewVaccineStock let-modal>

        <div class="modal-header">
            <h5 class="modal-title" id="modal-basic-title">StockInfo</h5>
            <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">

            <div class="modal-body">
                <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="vaccineStock"
                    [columnDefs]="columnVaccineStock" (gridReady)="onGridReady($event)" [gridOptions]="gridOptions1">
                </ag-grid-angular>
            </div>

        </div>

    </ng-template>


</div>