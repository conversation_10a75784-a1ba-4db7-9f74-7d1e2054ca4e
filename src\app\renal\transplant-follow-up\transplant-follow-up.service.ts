
// import { stringify } from 'querystring';


import { GET_Renal_Transplant_Followup } from '../../common/app.utils';
import { renalTransplantFollowupModel } from '../../common/objectModels/renal-Transplant-Followup-model';
import { renalComplicationModel } from '../../common/objectModels/renalComplication-model';
import { HttpClient, HttpResponse,HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {Observable,of} from 'rxjs';
import * as AppUtils from '../../common/app.utils';




@Injectable({
    providedIn: 'root'
  })
  export class TransplantFollowUpService {
    constructor(private http:HttpClient) {
    }
  
  getRenalTransplantFollowUp(dto:any) :Observable<any>{
      return  this.http.post<renalTransplantFollowupModel[]>(AppUtils.GET_Renal_Transplant_Followup,dto); 
    }




   getRenalTransComplication(dto:any){
    return this.http.post<renalComplicationModel[]>(AppUtils.GET_Renal_Transplant_COMPLICATION,dto);

   } 
  

  
   addRenalTransplantFollowUp(dto:any) :Observable<any>{
    return  this.http.post(AppUtils.SAVE_Renal_Transplant_Followup,dto); 
  }
  

  addFollowUpComplication(dto:any) :Observable<any>{
   return  this.http.post(AppUtils.SAVE_Renal_Transplant_COMPLICATION,dto); 
 }



  }