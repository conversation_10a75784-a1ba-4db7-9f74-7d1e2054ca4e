<h6>Vaccine Target Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="targetSearch">
        <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Vaccine Name</label>
                    <ng-select #entryPoint [items]="vaccineList" [virtualScroll]="true" placeholder="Select"
                    bindLabel="vaccineGroupName" bindValue="vaccineGroupId" formControlName="vaccineGroupId">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.vaccineGroupName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                    bindLabel="regName" bindValue="regCode" formControlName="regCode"
                    (change)="locSelect($event,'region')" [(ngModel)]="regCodeSelected"
                    >
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                    bindLabel="walName" bindValue="walCode" formControlName="walCode"
                    (change)="locSelect($event,'wilayat')" [(ngModel)]="walCodeSelected"
                    >
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                    bindLabel="estName" bindValue="estCode" formControlName="estCode"
                    (change)="locSelect($event,'institute')" [(ngModel)]="estCodeSelected"
                    >
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Month</label>
                    <ng-select appendTo="body" [items]="months"
                        [virtualScroll]="true" formControlName="month"
                        placeholder="Select" bindLabel="name" bindValue="code">
                        <ng-option *ngFor="let c of months" [value]="c.code">{{
                            c.name
                            }}</ng-option>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Year</label>
                    <input type="number" class="form-control form-control-sm" formControlName="year">
                </div>
            </div>
            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="submit"  (click)="exportToExcel()" class="btn btn-primary ripple" > Excel</button>
                   
                     <button type="submit" class="btn btn-sm btn-primary" (click)="getTargetList()">Search</button>
                    <button type="reset" class="btn btn-sm btn-primary" (click)="clear();">Clear</button>
                </div>
            </div>
        </div>
    </form>
</div>
    
<div style="margin-top:20px">
    <div class="grid-container">
        <div class="grid-item">
            <ag-grid-angular style="width: 100%; height: 500px;" class="ag-theme-balham" [rowData]="rowData" 
                [gridOptions]="gridOptions" [columnDefs]="columnDefs" >
            </ag-grid-angular>

        </div>
        <div class="grid-item">
            <p-paginator *ngIf="rowData.length > 0" #targetPaginator rows="10"
                totalRecords="{{totalRecords}}" (onPageChange)="getTargetList($event)" showCurrentPageReport="true"
                currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
                [rowsPerPageOptions]="[10, 20, 30]">
            </p-paginator>
        </div>
    </div>
</div>

<ng-template #viewVaccineStock let-modal>

    <div class="modal-header">
        <h5 class="modal-title" id="modal-basic-title">StockInfo</h5>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">

        <div class="modal-body">
            <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="vaccineStock"
                [columnDefs]="columnVaccineStock" (gridReady)="onGridReady($event)" [gridOptions]="gridOptions1">
            </ag-grid-angular>
        </div>

    </div>

</ng-template>