table {
    th{
        background:#f8f9fa;
        color: rgb(0, 0, 0);
        text-align: center;
        
    }
    td{
        i{
            color: var(--primary-color);
        }
    }
}
.input-6{
    .col-md-6:first-child{
        padding-right:0;
    }
    
}
.remarks{
    margin-top: 5px;
}

.form-control, label{
    font-size: 0.875rem;
}
textarea{
    // resize: none;
    font-size: 0.813rem;
}
.search-outer{
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
}
.search-panel, .grid-panel{
    border:1px solid #e8e8e8;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 0 5px rgb(0 0 0 / 10%);
}
.whitetback{
    background-color: #fff;
   
}
 table td .form-control, table td .ui-inputtext{
    border: none !important;
}
/*
.btn5{
    padding: 0px 25px !important;
    margin: 1px 1500px;
    width: 139px;
    height: 31px;
    min-height: 28px;
    margin-top: -84px;
    }
*/

/* unvisited link */
.link {
    color: black;
    text-decoration: underline;
    font-size: 16px;

  }

  /* mouse over link */
  .link:hover {
    color: blue;
  }
  
  /* selected link */
  .link:active {
    color: blue;
  }

  .nav-tabs .nav-link{
      border: solid 1px rgb(92, 89, 89);
  }