import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ColumnApi, GridApi, GridOptions, ICellRendererParams } from 'ag-grid-community';
import { GridNgSelectDataComponent } from '../../common/agGridComponents/grid-ngSelect-data.component';
import { IcdDataModel } from '../../common/objectModels/icd-data-model';
import { Parameter2 } from '../../common/objectModels/parameter-2-model';
import { MasterService } from '../../_services/master.service';
import Swal from 'sweetalert2';
import { HttpClient, HttpParams } from '@angular/common/http';
import * as AppUtils from '../../common/app.utils';
import { ViewChild } from '@angular/core';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import { RenalRegistryFrom } from '../../common/objectModels/rena-registry-model';
import { DecimalPipe } from '@angular/common';
import { Diagnosis } from '../../common/objectModels/diagnosis-model';
import { ICDList } from '../../common/objectModels/icdList-models';
import { ICDRenalShortList } from '../../common/objectModels/icdRenalShortList-models';
import * as CommonConstants from '../../_helpers/common.constants';
import * as AppParams from '../../_helpers/app-param.constants';
import { TbVitalSigns } from '../../common/objectModels/vital-signs-model';
import { formatDate } from '@angular/common';
import * as GridUtils from '../../common/agGridComponents/app.grid-spec-utils';
import * as moment from 'moment';
import { RenalService } from '../renal.service';
import { ToastrService } from 'ngx-toastr';
import { ButtonRendererComponent } from '../../common/agGridComponents/ButtonRendererComponent';
import { notEqual } from 'assert';
import { SharedService } from '../../_services/shared.service';
import { Router } from '@angular/router';
import * as AppCompUtils from '../../common/app.component-utils';
import { AppComponent } from '../../app.component';
import { invalid } from '@angular/compiler/src/render3/view/util';
import { LoginService } from 'src/app/login/login.service';
import { DeathDetailsComponent } from 'src/app/_comments/death-details/death-details.component';

@Component({
  selector: 'app-renal-register',
  templateUrl: './renal-register.component.html',
  styleUrls: ['./renal-register.component.scss']
})
export class RenalRegisterComponent implements OnInit {
  renalRegistryForm: FormGroup;
  submitted = false;
  today = new Date();
  regId: any;
  alive = true;
  icdData: Array<Diagnosis> = new Array<Diagnosis>();
  dbIcdData: Array<Diagnosis>;
  diagnosis: Array<Diagnosis>;
  icdList: Array<ICDList>;
  icdRenalShortList: Array<ICDRenalShortList>;
  formData: RenalRegistryFrom;
  rgTbVitalSign: Array<TbVitalSigns>;
  comorbidDiseaseListGrid: GridOptions;
  private comorbidDiseaseGridApi: GridApi;
  private comorbidDiseaseGridColApi: ColumnApi;
  columnDefs;
  frameworkComponents;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  grids: any = [];
  loginId: any;
  centralRegNoExit: boolean = false;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  // alive: boolean = true;
  currentCivilId = '';
  estCode;
  patientId;
  dbIcdDataGrid: any;
  civilIdEntryType: any;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('deathDetails', { static: false }) deathDetails: DeathDetailsComponent;
  constructor(private _sharedService: SharedService, private _loginService: LoginService, private _router: Router, private fb: FormBuilder, private _masterService: MasterService, private _http: HttpClient, private _renalService: RenalService, private toastrService: ToastrService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    this.populateMasterData();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);

    }


  }
  get bmiBodyWidth() {
    return this.renalRegistryForm.get('bmiBodyWidth');
  }

  get bmiBodyHeight() {
    return this.renalRegistryForm.get('bmiBodyHeight');
  }
  ngOnInit() {


    this.populateMasterData();
    this.renalRegistryForm = this.fb.group({
      //  'patientId': ["", Validators.required],
      //    'civilId': ["", Validators.required],
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'civilId': [null],
      // 'firstName': ["", Validators.required],
      'secondName': new FormControl(),
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'maritalStatus': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'tel': new FormControl(),
      'bmiBodyWidth': '',
      'bloodGroup': '',
      'bmi': new FormControl(),
      'bmiBodyHeight': new FormControl(),
      //  'regInst': ["", Validators.required],
      'registerType': [1],
      'rgTbEldExamTrans': this.fb.array([]),
      'instRegDate': [this.today],
      'rgTbPatientInfo': this.fb.array([]),
      'causeOfKindeyDisease': new FormControl(),
      'othercomorbidDisease': new FormControl(),
    });
  }

  populateMasterData() {

    // this._masterService.getIcdList().subscribe(response => {
    //   this.icdList = response.result;
    //   this.getGrids();
    // }, error => {
    // });

    this._masterService.getIcdList();
    this._masterService.icdList.subscribe(value => {
      this.icdList = value;
      this.getGrids();
    });

    this._masterService.getIcdRenalShortList();
    this._masterService.icdRenalShortList.subscribe(value => {
      this.icdRenalShortList = value;
    });
  }


  private getGrids() {
    this.columnDefs = [
      { field: 'centralRegNo', hide: true },
      { field: 'entryDate', hide: true },
      { field: 'enteredBy', hide: true },
      { field: 'icd', hide: true },
      {
        headerName: 'ICD', minWidth: 350, field: 'icdValue', cellEditor: 'ngSelectEditor',
        cellEditorParams: {
          values: this.icdRenalShortList,
          dataColumn: 'code',
          model: new ICDList(),
          objectData: ['code', 'disease']
        }, editable: true, width: 130
      },
      { field: 'icdFlag', hide: true },
      { field: 'runId', hide: true },
      { field: 'remarks', editable: true },
      {
        headerName: 'Action',
        cellRenderer: 'buttonRenderer',
        cellRendererParams: {
          onClick: this.confirmdeleteICD.bind(this),
          label: 'Delete'
        }
      },
    ];

  }







  confirmdeleteICD(e) {

    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: 'btn btn-success',
        cancelButton: 'btn btn-danger'
      },
      buttonsStyling: false
    })

    swalWithBootstrapButtons.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      reverseButtons: true
    }).then((result) => {
      if (!result.dismiss) {
        /*swalWithBootstrapButtons.fire(
          'Deleted!',
          'The record has been deleted.',
          'success'
        )*/
        this.deleteICD(e);
      } else if (
        /* Read more about handling dismissals below */
        result.dismiss === Swal.DismissReason.cancel
      ) {
        swalWithBootstrapButtons.fire(
          'Cancelled',
          'Your imaginary record is safe :)',
          'error'
        )
      }
    })
  }

  deleteICD(e) {


    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === 'comorbidDiseaseListGrid');
    let pushData = [];
    if (e.rowData.runId != null) {
      //this._http.get(AppUtils.DELETE_DIAGNOSIS, { params: new HttpParams().set("runId", e.rowData.runId) }).subscribe(
      this._renalService.deleteDiagnosis(e.rowData.runId).subscribe(res => {
        //  console.log(res);
      }
      );
    }

    let focusedNode = this.comorbidDiseaseGridApi.getSelectedRows();
    this[gridObject['gridApi']].updateRowData({ remove: focusedNode });
    this[gridObject['gridApi']].redrawRows();
    if (this[gridObject['gridApi']].getRenderedNodes().length > 0) {
      this[gridObject['gridApi']].getRenderedNodes().forEach(el => {
        let data = el['data'];
        pushData.push(data);
      })
    } else {
      pushData.length = 0;
    }
    this.icdData = pushData;
  }

  //use to check the data by civil id, first check in eReg DB if data not exist , get the personal information from ROP
  callMpiMethod() {
    this.getdata('civilId', ' ', this.patientDetails.patientForm.value.civilId);
  }


  //get patient date (by RegNo or CivilID)
  getdata(searchby: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }



    }


    this._renalService.getRenal(regNo, AppUtils.REG_TYPE_RENAL.toString(), civilId).subscribe(async res => {

      if (res['code'] == 'S0000') {
        // get data
        this.centralRegNoExit = true;
        this.formData = res['result'];
        this.patientDetails.setPatientDetails(res['result']);

        this.currentCivilId = res['result'].rgTbPatientInfo['civilId'];
        this.patientId = this.formData['patientID'];
        this.estCode = this.formData['regInst'];
        this.rgTbVitalSign = this.formData.rgTbVitalSigns;

        if (res['result'].rgTbDeathDetails != null) {
          this.alive = false;
          
          // Store death details data
          const rgTbDeathDetails: any = res['result'].rgTbDeathDetails;
          
          // Use arrow function to maintain 'this' context and add proper delay
          setTimeout(() => {
            if (this.deathDetails && this.deathDetails.setDeathDetailsResult) {
              this.deathDetails.setDeathDetailsResult(rgTbDeathDetails);
            }
          }, 0);
        } else {
          this.alive = true;
        }


        let width = this.rgTbVitalSign.filter(s => s.paramid == AppParams.VITAL_WT).map(s => s.paramValue)[0];
        let hight = this.rgTbVitalSign.filter(s => s.paramid == AppParams.VITAL_HT).map(s => s.paramValue)[0];
        let bloodGroup = this.rgTbVitalSign.filter(s => s.paramid == AppParams.VITAL_BLOOD_GROUP).map(s => s.paramValue)[0];

        if (bloodGroup != undefined) {
          this.renalRegistryForm.patchValue({ 'bloodGroup': bloodGroup.toString() });
        }



        this.bmiDetails(width, hight);

        //res['result']['diagnosis'])
        this.diagnosis = this.formData.rgTbDiagnosis;
        let kindeyDisease = this.formData.rgTbDiagnosis.filter(s => s.icdFlag == 'P').map(s => s.icd)[0];

        if (kindeyDisease != null) {
          this.renalRegistryForm.patchValue({ 'causeOfKindeyDisease': kindeyDisease.toString() });
        }

        this.dbIcdDataGrid = this.formData.rgTbDiagnosis.filter(s => s.icdFlag == 'O').map(x => Object.assign({}, x));
        this.icdData = this.formData.rgTbDiagnosis.filter(s => s.icdFlag == 'O');


        this.dbIcdDataGrid.forEach(s => {
          let theCode = s.icd;
          if (this.icdRenalShortList) {
            let theReq = this.icdRenalShortList.find(r => r.code == theCode);
            if (theReq) {
              s.icdValue = theReq.disease;
            }


            this.icdData.forEach(el => {
              if (el.icd == s.icd) {
                el.icdValue = s.icdValue;
              }
            });
          }
        });

      } else if (res['code'] == "F0000" || res['code'] == "3") {

        //if record not exist in eReg DB, and qeury by Civil ID , then call fetchMpi method.
        // 
        // title: 'Warning',
        // text: msg +'</br></br> dsdd',
        // icon: 'warning',
        await Swal.fire(
          '', msg, 'warning',
        ).then((result) => {
          if (callMPI) {
            this._sharedService.setPatientData(this.patientDetails);
            this._sharedService.fetchMpi(civilId).subscribe(civilIdEntryType => {
              this.civilIdEntryType = civilIdEntryType !== false ? civilIdEntryType : AppUtils.CIVILID_ENTERY_MANUALLY;
            });
          }
        })
      } else {
        Swal.fire('', res['message'], 'error')
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('', 'Error occured while retrieving user details', 'error')
    })

  }



  bmiDetails(width: any, hight: any) {

    this.renalRegistryForm.patchValue({ 'bmiBodyWidth': width, 'bmiBodyHeight': hight });

    let bmi = this._sharedService.calculateBMI(width, hight);
    this.renalRegistryForm.patchValue({ 'bmi': this._decimalPipe.transform(bmi, "1.2-2") });
  }
  getBmi() {
    let w = this.renalRegistryForm.get('bmiBodyWidth').value;
    let h = this.renalRegistryForm.get('bmiBodyHeight').value;
    this.bmiDetails(w, h);
  }

  search() {
    if (this.regId) {
      setTimeout(() => {
        this.getdata('regNo', this.regId, '');
        this.regId = "";
      }, 1000);

    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
    }
  }

  get f() { return this.renalRegistryForm.controls; }
  isObject(val) {
    return (typeof val === 'object');
  }

  addRec(grid: any) {
    console.log(this.icdRenalShortList)

    let colObject = {};
    if (grid === 'comorbidDiseaseListGrid') {
      //colObject = gridObject['colObject'];
      colObject = {
        "centralRegNo": null,
        "entryDate": null,
        "enteredBy": null,
        "icd": null,
        "icdValue": null,
        "icdFlag": null,
        "runId": null,
        "remarks": null
      };


    }

    let gridObject = {}


    gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);


    const parentModel = gridObject['parentModel']
    const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();
    let nullRowCnt = 0;
    let nullMandatoryFieldCnt = 0;
    if (renderedNodes.length > 0) {
      renderedNodes.forEach((element, index) => {
        const data = element['data'];
        let isEmpty = true;

        Object.keys(data).forEach(el => {

          if (data[el] === "") {
            data[el] = null;
          }
          if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach(elChild => {
                if (data[el][elChild]) {
                  isEmpty = false;
                }
              })
            } else {
              isEmpty = false;
            }

          }
          if (gridObject['mandatoryColumns']) {
            gridObject['mandatoryColumns'].forEach(mandatoryField => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach(elChild => {
                      if (!data[el][elChild]) {
                        nullMandatoryFieldCnt++;
                      }
                    })
                  }
                } else {
                  nullMandatoryFieldCnt++;
                }
              }
            })
          }
        })
        if (isEmpty) {
          nullRowCnt++;
        }
      })

    }
    if ((nullRowCnt === 0 && nullMandatoryFieldCnt === 0) || renderedNodes.length === 0) {

      this[gridObject['gridApi']].updateRowData({ add: [colObject] });
      this[gridObject['gridApi']].redrawRows();
      this[gridObject['gridApi']].forEachNode((node) => {
        if (node.lastChild) {
          node.setSelected(true);
          this[gridObject['gridApi']].ensureNodeVisible(node);
        } else {
          node.setSelected(false);
        }
      });
    }

  }

  getGridData(grid) {
    let nullMandatoryFieldCnt = 0;
    let nullRowCnt = 0;
    let gridObject = {}
    let saveList;
    gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);
    const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();
    if (renderedNodes.length > 0) {


      renderedNodes.forEach((element, index) => {

        const data = element['data'];
        let isEmpty = true;


        Object.keys(data).forEach(el => {


          if (data[el] === "") {
            data[el] = null;
          }

          if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach(elChild => {
                if (data[el][elChild]) {
                  isEmpty = false;
                }
              })
            } else {
              isEmpty = false;
            }

          }
          if (gridObject['mandatoryColumns']) {
            gridObject['mandatoryColumns'].forEach(mandatoryField => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach(elChild => {
                      if (!data[el][elChild]) {
                        nullMandatoryFieldCnt++;
                      }
                    })
                  }
                } else {
                  nullMandatoryFieldCnt++;
                }
              }
            })
          }
        })

        if (isEmpty) {
          nullRowCnt++;
        }

        if (data["runId"] == null) {
          this.icdData.push(data);
        }

      })


    }

    for (var i = 0; i < this.icdData.length; i++) {
      this.icdData[i].icdFlag = "O";
      this.icdData[i].icd = this.icdList.filter(s => s.disease === this.icdData[i].icdValue).map(s => s.icd).toString();
      if (this.icdData[i].runId === null) {
        this.icdData[i].enteredBy = this.loginId;
        this.icdData[i].entryDate = this.currentDate;
        //   this.icdData[i].centralRegNo = this.formData.centralRegNo;
      } else {
        this.dbIcdData.filter(x => x.runId == this.icdData[i].runId).map((data) => {
          if (this.icdData[i].icd !== data.icd || this.icdData[i].remarks !== data.remarks) {
            this.icdData[i].enteredBy = this.loginId;
            this.icdData[i].entryDate = this.currentDate;
          }
        })
      }
      saveList = this.icdData.map(x => Object.assign({}, x));

    }

    return saveList;
  }


  save() {
    this.submitted = true;

    // Added by zia on 24/10/2022
    if (!this.patientDetails.validateFields()) {
      Swal.fire('Alert', ' Mandatory fields cannot be empty', 'warning');
      return;
    }

    if (this.renalRegistryForm.invalid) {
      Swal.fire('Alert', ' Please enter valid data in BMI detail', 'warning');
      return;
    }

    // if (this.patientDetails.patientForm.invalid || this.renalRegistryForm.invalid) {
    //   return false;
    // }


    let patientData = this.renalRegistryForm.value;

    // Vital Signs ( Hight & Wight)
    let vitalData: Array<TbVitalSigns> = [];
    let bmiWidth: number = patientData.bmiBodyWidth;
    let bmiHeight: number = patientData.bmiBodyHeight;
    let bloodGroup: any = patientData.bloodGroup;
    let bloodGroupvalue: any = this.bloodGroupList.filter(s => s.id == bloodGroup).map(s => s.value)[0];

    let wa = this.renalRegistryForm.get('bmiBodyWidth').value;
    let ha = this.renalRegistryForm.get('bmiBodyHeight').value;


    let w = { runId: null, entryBy: this.loginId, entryDate: this.currentDate.toString(), paramValue: bmiWidth, paramDesc: null, paramid: AppParams.VITAL_WT };
    let h = { runId: null, entryBy: this.loginId, entryDate: this.currentDate.toString(), paramValue: bmiHeight, paramDesc: null, paramid: AppParams.VITAL_HT };
    let bg = { runId: null, entryBy: this.loginId, entryDate: this.currentDate.toString(), paramValue: bloodGroup, paramDesc: bloodGroupvalue, paramid: AppParams.VITAL_BLOOD_GROUP };


    if (this.rgTbVitalSign != null && this.rgTbVitalSign.length > 0) {
      vitalData = this.rgTbVitalSign.map(x => Object.assign({}, x));
      if (vitalData.find(param => param.paramid === w.paramid) === undefined) {
        vitalData.push(w);
        //Push
      } else {
        //Update     
        w.runId = vitalData.filter(s => s.paramid === w.paramid).map(s => s.runId)[0];
        vitalData = vitalData.filter(param => param.paramid !== w.paramid);
        vitalData.push(w);
      }

      if (vitalData.find(param => param.paramid === h.paramid) === undefined) {
        vitalData.push(h);
        //Push
      } else {
        //Update     
        h.runId = vitalData.filter(s => s.paramid === h.paramid).map(s => s.runId)[0];
        vitalData = vitalData.filter(param => param.paramid !== h.paramid);
        vitalData.push(h);
      }

      if (vitalData.find(param => param.paramid === bg.paramid) === undefined) {
        vitalData.push(bg);
        //Push
      } else {
        //Update     
        bg.runId = vitalData.filter(s => s.paramid === bg.paramid).map(s => s.runId)[0];
        vitalData = vitalData.filter(param => param.paramid !== bg.paramid);
        vitalData.push(bg);
      }
    } else {
      vitalData.push(w);
      vitalData.push(h);
      vitalData.push(bg);

    }

    //Diagnosis
    let diagnosisData = [];
    this.comorbidDiseaseGridApi.stopEditing();
    let renderedNodes = this.comorbidDiseaseGridApi.getRenderedNodes();
    let KindeyDiseaseData = { runId: null, icdFlag: "P", icd: patientData.causeOfKindeyDisease, enteredBy: this.loginId, entryDate: this.currentDate.toString(), remarks: null, centralRegNo: null, icdValue: null };
    if (this.diagnosis != null && this.diagnosis.length > 0) {
      diagnosisData = this.diagnosis.filter(s => s.icdFlag == "P").map(x => Object.assign({}, x));

      if (diagnosisData.find(param => param.icdFlag === KindeyDiseaseData.icdFlag) === undefined) {
        diagnosisData.push(KindeyDiseaseData);
        //Push
      } else {
        //Update     
        KindeyDiseaseData.runId = diagnosisData.filter(s => s.icdFlag === KindeyDiseaseData.icdFlag).map(s => s.runId)[0];
        KindeyDiseaseData.centralRegNo = this.formData.centralRegNo;
        diagnosisData = diagnosisData.filter(param => param.icdFlag !== KindeyDiseaseData.icdFlag);
        diagnosisData.push(KindeyDiseaseData);
      }
    } else {
      diagnosisData.push(KindeyDiseaseData);
    }

    if (renderedNodes.length > 0) {
      renderedNodes.forEach(node => {
        let requiredIcdValue = node.data.icdValue;
        if (!node.data.icd) {
          let foundIcdItem = this.icdRenalShortList.find(el => el.disease === requiredIcdValue);
          if (foundIcdItem) {
            node.data.icd = foundIcdItem.code;
          }
          node.data.enteredBy = this.loginId;
          node.data.entryDate = this.currentDate;
          node.data.icdFlag = 'O';
        }
        diagnosisData.push(node.data);
      });
    }


    //death details data
    let deathDetails = null;
    if (this.alive == false) {
      deathDetails = this.deathDetails.deatDetailsForm.value;
      if (deathDetails.civillID != null) {
        if (deathDetails.createdBy == null) {
          deathDetails.createdBy = this.loginId;
          deathDetails.createdDate = this.currentDate;
        } else {
          deathDetails.lastModifiedBy = this.loginId;
          deathDetails.lastModifiedDate = this.currentDate;
        }
      } else {
        deathDetails = null;
      }
    }



    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;
    let regDate;
    if (this.patientDetails.f.centralRegNo.value === null) {
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      centralRegNo = null;
      regDate = this.currentDate;
    } else {
      createdBy = this.formData.createdBy;
      createdOn = this.formData.createdOn;
      modifiedBy = this.loginId;
      modifiedOn = this.currentDate;
      centralRegNo = this.patientDetails.f.centralRegNo.value
      regDate = this.formData.instRegDate
    }


    if (!this.civilIdEntryType) {
      if (this.formData) {
        if (this.formData.rgTbPatientInfo) {
          this.civilIdEntryType = this.formData.rgTbPatientInfo.civilIdEntryType;
        }
      } else {
        this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
      }
    }

    let saveData = {
      centralRegNo: centralRegNo,
      activeYn: "Y",
      //civilId: this.patientDetails.f.civilId.value,
      patientID: this.patientDetails.f.patientId.value,
      createdBy: createdBy,
      createdOn: createdOn,
      instRegDate: regDate,  // this.formData.instRegDate ? moment(this.formData.instRegDate).format("DD-MM-yyyy") : null, // formatDate(new Date(), 'yyyy-MM-dd', 'en'); 
      modifiedBy: modifiedBy,
      modifiedOn: modifiedOn,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_RENAL,
      //localRegReferance: this.patientDetails.f.localRegReferance.value,
      rgTbPatientInfo: {
        patientId: this.patientDetails.f.patientId.value,
        civilId: this.patientDetails.f.civilId.value,
        createdBy: createdBy,
        //   createdInstid: this.patientDetails.f.createdInstid.value,
        createdOn: createdOn,
        dob: this.patientDetails.f.dob.value ? moment(this.patientDetails.f.dob.value).format("DD-MM-yyyy") : null, //this.patientDetails.f.dob.value,
        firstName: this.patientDetails.f.firstName.value,
        lastUpdatedBy: modifiedBy,
        lastUpdatedDate: modifiedOn,
        civilIdEntryType: this.civilIdEntryType,
        //   lastUpdatedInstId: this.patientDetails.f.lastUpdatedInstId.value,
        maritalStatus: this.patientDetails.f.maritalStatus.value,
        mobileNo: this.patientDetails.f.mobileNo.value,
        kinTelNo: this.patientDetails.f.kinTelNo.value,
        secondName: this.patientDetails.f.secondName.value,
        sex: this.patientDetails.f.sex.value,
        thirdName: this.patientDetails.f.thirdName.value,
        tribe: this.patientDetails.f.tribe.value,
        village: this.patientDetails.f.village.value,


      },
      rgTbVitalSigns: vitalData,
      rgTbDiagnosis: diagnosisData,
      rgTbDeathDetails: deathDetails
    }



    this._renalService.saveRenal(saveData).subscribe(res => {

      if (res['code'] == 0) {
        Swal.fire('Saved!', 'Renal Registry Saved successfully.', 'success');
        this.regId = res["result"];
        this.search();
      } else if (res['code'] == "3") {
        Swal.fire('Saved!', res['message'], 'error');
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Renal Registry ' + err.message, 'error')
    })


  }


  onReady(params, grid) {
    if (this.grids.length > 0) {
      const exist = this.grids.find(item => grid === item);
      if (!exist) {
        this.grids.push(grid)
      }
    } else {
      this.grids.push(grid)
    }

    if (grid === "comorbidDiseaseListGrid") {
      this.comorbidDiseaseGridApi = params.api;
      this.comorbidDiseaseGridColApi = params.columnApi;
    }
  }

  callCase() {
    this._sharedService.setNavigationData(this.formData);
    this._router.navigate(['stages'], { state: { centralRegNo: this.formData.centralRegNo } });
  }
  callTissue() {
    this._sharedService.setNavigationData(this.formData);
    this._router.navigate(['/tissueType'], { state: { centralRegNo: this.formData.centralRegNo } });
  }

  updateDeathDetails() {

    Swal.fire({
      //title: 'Death details',
      text: "Do you want to add death details?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes'
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    })

  }



  clear() {

    this.renalRegistryForm.reset();
    this.formData = null;
    this.patientDetails.clear();
    this.icdData = null;
    this.diagnosis = null;
    this.rgTbVitalSign = null;
    this.centralRegNoExit = false;
    //this.formData.instRegDate= this.today;

    if (this.alive == false) {
      this.deathDetails.clear();
    }
  }
}