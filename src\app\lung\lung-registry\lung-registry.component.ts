import { Component, OnInit, ViewChild } from "@angular/core";
import Swal from "sweetalert2";
import * as AppUtils from "../../common/app.utils";
import { SharedService } from "src/app/_services/shared.service";
import { PatientDetailsComponent } from "src/app/_comments/patient-details/patient-details.component";
import { DeathDetailsComponent } from "src/app/_comments/death-details/death-details.component";
import * as _ from "lodash";
import { LungService } from "../lung.service";
import {
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";
import * as GridUtils from "../../common/agGridComponents/app.grid-spec-utils";
import { ColumnApi, GridApi, GridOptions } from "ag-grid-community";
import { LungRegistryFrom } from "src/app/common/objectModels/lung-registry-model";
import { TbVitalSigns } from "src/app/common/objectModels/vital-signs-model";
import * as AppParams from "../../_helpers/app-param.constants";
import { DecimalPipe, formatDate } from "@angular/common";
import * as AppCompUtils from "../../common/app.component-utils";

import { MasterService } from "src/app/_services/master.service";
import { LungIcd } from "../../common/objectModels/icd-lung-list-models";
import { Diagnosis } from "src/app/common/objectModels/diagnosis-model";
import { IcdRenalShortList } from "src/app/common/objectModels/icdLungShortList-models";
import { GridNgSelectDataComponent } from "src/app/common/agGridComponents/grid-ngSelect-data.component";
import { ButtonRendererComponent } from "src/app/common/agGridComponents/ButtonRendererComponent";
import * as CommonConstants from "../../_helpers/common.constants";
import * as moment from "moment";
import { Router } from "@angular/router";
@Component({
  selector: "flst",
  templateUrl: "./lung-registry.component.html",
  styleUrls: ["./lung-registry.component.scss"],
  providers: [LungService],
})
export class LungRegistryComponent implements OnInit {
  alive = true;
  diagnosis: Array<Diagnosis>;
  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  @ViewChild("deathDetails", { static: false })
  deathDetails: DeathDetailsComponent;
  formData: LungRegistryFrom;
  today: any;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  dataFetched: boolean;
  comorbidDiseaseListGrid: GridOptions;
  currentCivilId = "";
  estCode: any;
  patientId: any;
  regNo: any;
  centralRegNoExit: boolean = false;
  submitted: boolean = false;
  lungForm: FormGroup;
  patientForm: FormGroup;
  grids: any = [];
  private comorbidDiseaseGridApi: GridApi;
  private comorbidDiseaseGridColApi: ColumnApi;
  bmiBodyWidth: any;
  bmiBodyHeight: any;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  icdLungShortList: Array<IcdRenalShortList>;
  currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");
  icdData: any[] = [];
  columnDefs: any[] = [];
  frameworkComponents: any;
  lungRegistryData: any;
  icdList: Array<LungIcd>;
  rgTbVitalSign: Array<TbVitalSigns>;
  dbIcdDataGrid: Diagnosis[];
  loginId: any;
  constructor(
    private _sharedService: SharedService,
    private _lungService: LungService,
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _router: Router
  ) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };
  }

  ngOnInit() {
    this.initLungForm();
    this.populateMasterData();
    this.initializeComponent();
  }

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regNo = this._sharedService.getNavigationData().regNo;
        if (this.regNo) {
          setTimeout(() => {
            this.getList(this.regNo);
            this.regNo = "";
          }, 1000);
        } else {
          this.regNo = "";
          Swal.fire({
            icon: "warning",
            title: "Please enter Registration ID",
          });
        }
        //this.getList(this.regNo);
        this._sharedService.setNavigationData(null);
      }, 0);
    }
  }

  populateMasterData() {
    this._masterService.getLungIcdList();
    this._masterService.icdLungList.subscribe((value) => {
      this.icdList = value;
      this.getGrids();
    });
    this._masterService.getIcdLungShortList();
    this._masterService.icdLungShortList.subscribe((value) => {
      this.icdLungShortList = value;
    });
  }
  private getGrids() {
    this.columnDefs = [
      { field: "centralRegNo", hide: true },
      { field: "entryDate", hide: true },
      { field: "enteredBy", hide: true },
      { field: "icd", hide: true },
      {
        headerName: "ICD",
        minWidth: 350,
        field: "icdValue",
        cellEditor: "ngSelectEditor",
        cellEditorParams: {
          values: this.icdList,
          dataColumn: "code",
          model: new LungIcd(),
          objectData: ["code", "disease"],
        },
        editable: true,
        width: 130,
      },
      { field: "icdFlag", hide: true },
      { field: "runId", hide: true },
      { field: "remarks", editable: true },
      {
        headerName: "Action",
        cellRenderer: "buttonRenderer",
        cellRendererParams: {
          onClick: this.confirmdeleteICD.bind(this),
          label: "Delete",
        },
      },
    ];
  }

  confirmdeleteICD(e) {
    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: "btn btn-success",
        cancelButton: "btn btn-danger",
      },
      buttonsStyling: false,
    });
    swalWithBootstrapButtons
      .fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: true,
      })
      .then((result) => {
        if (!result.dismiss) this.deleteICD(e);
        else if (result.dismiss === Swal.DismissReason.cancel)
          swalWithBootstrapButtons.fire(
            "Cancelled",
            "Your imaginary record is safe :)",
            "error"
          );
      });
  }

  deleteICD(e) {
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(
      (value) => value["gridName"] === "comorbidDiseaseListGrid"
    );
    let focusedNode = this.comorbidDiseaseGridApi.getSelectedRows();
    this[gridObject["gridApi"]].updateRowData({ remove: focusedNode });
    this[gridObject["gridApi"]].redrawRows();
    let pushData = [];
    this[gridObject["gridApi"]]
      .getRenderedNodes()
      .forEach((el) => pushData.push(el["data"]));
    this.icdData = pushData;
  }

  initLungForm() {
    this.lungForm = this.fb.group({
      bmiBodyWidth: new FormControl(),
      bloodGroup: new FormControl(),
      bmi: new FormControl(),
      bmiBodyHeight: new FormControl(),
      registerType: new FormControl(18),
      rgTbEldExamTrans: new FormControl(this.fb.array([])),
      instRegDate: new FormControl(this.today),
      rgTbPatientInfo: new FormControl(this.fb.array([])),
      causeOfKindeyDisease: new FormControl(),
      othercomorbidDisease: new FormControl(),
    });

    this.patientForm = this.fb.group({
      centralRegNo: new FormControl(null),
      patientId: new FormControl(null, Validators.required),
      civilId: new FormControl(null, Validators.required),
      dob: new FormControl(null, Validators.required),
      age: new FormControl(),
      tribe: new FormControl(),
      firstName: new FormControl(null, Validators.required),
      secondName: new FormControl(null, Validators.required),
      sex: new FormControl(),
      maritalStatus: new FormControl(),
      thirdName: new FormControl(),
      village: new FormControl(),
      walayat: new FormControl(),
      region: new FormControl(),
      mobileNo: new FormControl(),
      kinTelNo: new FormControl(),
      careGiverTel: new FormControl(),
      regInst: new FormControl(null, Validators.required),
      exDate: new FormControl(null),
    });
  }

  addRec(grid: any) {
    let colObject = {};
    if (grid === "comorbidDiseaseListGrid") {
      colObject = {
        centralRegNo: null,
        entryDate: null,
        enteredBy: null,
        icd: null,
        icdValue: null,
        icdFlag: null,
        runId: null,
        remarks: null,
      };
    }
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(
      (value) => value["gridName"] === grid
    );
    const renderedNodes = this[gridObject["gridApi"]].getRenderedNodes();
    let nullRowCnt = 0,
      nullMandatoryFieldCnt = 0;
    if (renderedNodes.length > 0) {
      renderedNodes.forEach((element) => {
        const data = element["data"];
        let isEmpty = true;
        Object.keys(data).forEach((el) => {
          if (data[el] === "") data[el] = null;
          if (
            data[el] &&
            (gridObject["hiddenColumns"]
              ? !gridObject["hiddenColumns"].find((field) => el === field)
              : data[el])
          ) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach((elChild) => {
                if (data[el][elChild]) isEmpty = false;
              });
            } else isEmpty = false;
          }
          if (gridObject["mandatoryColumns"]) {
            gridObject["mandatoryColumns"].forEach((mandatoryField) => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach((elChild) => {
                      if (!data[el][elChild]) nullMandatoryFieldCnt++;
                    });
                  }
                } else nullMandatoryFieldCnt++;
              }
            });
          }
        });
        if (isEmpty) nullRowCnt++;
      });
    }
    if (
      (nullRowCnt === 0 && nullMandatoryFieldCnt === 0) ||
      renderedNodes.length === 0
    ) {
      this[gridObject["gridApi"]].updateRowData({ add: [colObject] });
      this[gridObject["gridApi"]].redrawRows();
      this[gridObject["gridApi"]].forEachNode((node) => {
        node.setSelected(!!node.lastChild);
        if (node.lastChild) this[gridObject["gridApi"]].ensureNodeVisible(node);
      });
    }
  }

  search() {
    if (!this.regNo) {
      Swal.fire({ icon: "warning", title: "Please enter Registration ID" });
      return;
    }
    //this.clear();
    this.getList(this.regNo);
    this.regNo = "";
  }

  save() {
    this.submitted = true;
    this.comorbidDiseaseGridApi.stopEditing();
    const patientForm = this.patientDetails.patientForm;

    const patientId = patientForm.get("patientId").value;

    if (!patientId) {
      Swal.fire("Alert!", "Patient ID should not be null or empty.", "warning");
      return;
    }
    if (patientForm.invalid) {
      const kinTelNoErrors = patientForm.get("kinTelNo").errors;
      if (kinTelNoErrors && kinTelNoErrors["pattern"]) {
        Swal.fire(
          "Alert!",
          "Kin Telephone Number must be between 8 and 16 digits.",
          "warning"
        );
      } else {
        Swal.fire(
          "Alert!",
          "Please correct the highlighted errors in the Patient section.",
          "warning"
        );
      }
      return;
    }
    if (!this.patientDetails.validateFields()) {
      Swal.fire("Alert!", " Mandatory fields cannot be empty", "warning");
      return;
    }

    if (this.lungForm.invalid) {
      Swal.fire("Alert!", " Please enter valid data in BMI detail", "warning");
      return;
    }

    let patientData = this.lungForm.value;

    // Vital Signs ( Hight & Wight)
    let vitalData: Array<TbVitalSigns> = [];
    let bmiWidth: number = patientData.bmiBodyWidth;
    let bmiHeight: number = patientData.bmiBodyHeight;
    let bloodGroup: any = patientData.bloodGroup;
    let bloodGroupvalue: any = this.bloodGroupList
      .filter((s) => s.id == bloodGroup)
      .map((s) => s.value)[0];

    let wa = this.lungForm.get("bmiBodyWidth").value;
    let ha = this.lungForm.get("bmiBodyHeight").value;

    let w = {
      runId: null,
      entryBy: this.loginId,
      entryDate: this.currentDate.toString(),
      paramValue: bmiWidth,
      paramDesc: null,
      paramid: AppParams.VITAL_WT,
    };
    let h = {
      runId: null,
      entryBy: this.loginId,
      entryDate: this.currentDate.toString(),
      paramValue: bmiHeight,
      paramDesc: null,
      paramid: AppParams.VITAL_HT,
    };
    let bg = {
      runId: null,
      entryBy: this.loginId,
      entryDate: this.currentDate.toString(),
      paramValue: bloodGroup,
      paramDesc: bloodGroupvalue,
      paramid: AppParams.VITAL_BLOOD_GROUP,
    };

    if (this.rgTbVitalSign != null && this.rgTbVitalSign.length > 0) {
      vitalData = this.rgTbVitalSign.map((x) => Object.assign({}, x));
      if (
        vitalData.find((param) => param.paramid === w.paramid) === undefined
      ) {
        vitalData.push(w);
      } else {
        w.runId = vitalData
          .filter((s) => s.paramid === w.paramid)
          .map((s) => s.runId)[0];
        vitalData = vitalData.filter((param) => param.paramid !== w.paramid);
        vitalData.push(w);
      }

      if (
        vitalData.find((param) => param.paramid === h.paramid) === undefined
      ) {
        vitalData.push(h);
      } else {
        h.runId = vitalData
          .filter((s) => s.paramid === h.paramid)
          .map((s) => s.runId)[0];
        vitalData = vitalData.filter((param) => param.paramid !== h.paramid);
        vitalData.push(h);
      }

      if (
        vitalData.find((param) => param.paramid === bg.paramid) === undefined
      ) {
        vitalData.push(bg);
      } else {
        bg.runId = vitalData
          .filter((s) => s.paramid === bg.paramid)
          .map((s) => s.runId)[0];
        vitalData = vitalData.filter((param) => param.paramid !== bg.paramid);
        vitalData.push(bg);
      }
    } else {
      vitalData.push(w);
      vitalData.push(h);
      vitalData.push(bg);
    }

    //Diagnosis
    let diagnosisData = [];
    this.comorbidDiseaseGridApi.stopEditing();
    let renderedNodes = this.comorbidDiseaseGridApi.getRenderedNodes();
    let KindeyDiseaseData = {
      runId: null,
      icdFlag: "P",
      icd: patientData.causeOfKindeyDisease,
      enteredBy: this.loginId,
      entryDate: this.currentDate.toString(),
      remarks: null,
      centralRegNo: null,
      icdValue: null,
    };
    if (this.diagnosis != null && this.diagnosis.length > 0) {
      diagnosisData = this.diagnosis
        .filter((s) => s.icdFlag == "P")
        .map((x) => Object.assign({}, x));

      if (
        diagnosisData.find(
          (param) => param.icdFlag === KindeyDiseaseData.icdFlag
        ) === undefined
      ) {
        diagnosisData.push(KindeyDiseaseData);
      } else {
        KindeyDiseaseData.runId = diagnosisData
          .filter((s) => s.icdFlag === KindeyDiseaseData.icdFlag)
          .map((s) => s.runId)[0];
        KindeyDiseaseData.centralRegNo = this.formData.centralRegNo;
        diagnosisData = diagnosisData.filter(
          (param) => param.icdFlag !== KindeyDiseaseData.icdFlag
        );
        diagnosisData.push(KindeyDiseaseData);
      }
    } else {
      diagnosisData.push(KindeyDiseaseData);
    }

    if (renderedNodes.length > 0) {
      renderedNodes.forEach((node) => {
        let requiredIcdValue = node.data.icdValue;
        if (!node.data.icd) {
          let foundIcdItem = this.icdLungShortList.find(
            (el) => el.disease === requiredIcdValue
          );
          if (foundIcdItem) {
            node.data.icd = foundIcdItem.code;
          }
          node.data.enteredBy = this.loginId;
          node.data.entryDate = this.currentDate;
          node.data.icdFlag = "O";
        }
        diagnosisData.push(node.data);
      });
    }

    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;
    let regDate;
    let localRegReferance;
    if (this.patientDetails.f.centralRegNo.value === null) {
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      centralRegNo = null;
      regDate = this.currentDate;
    } else {
      createdBy = this.formData.createdBy;
      createdOn = this.formData.createdOn;
      modifiedBy = this.loginId;
      modifiedOn = this.currentDate;
      centralRegNo = this.patientDetails.f.centralRegNo.value;
      regDate = this.formData.instRegDate;
      localRegReferance = this.formData.localRegReferance;
    }

    const patientFormData = this.patientDetails.patientForm.value;
    const deathDetails = this.deathDetails
      ? this.deathDetails.deatDetailsForm.value
      : null;

    let processedDeathDetails = null;
    if (deathDetails) {
      const hasNonNullValue = Object.values(deathDetails).some(
        (value) => value !== null && value !== undefined && value !== ""
      );
      processedDeathDetails = hasNonNullValue ? deathDetails : null;
    }

    let saveData = {
      centralRegNo: centralRegNo,
      activeYn: "Y",
      civilId: patientFormData.civilId,
      patientID: this.patientDetails.f.patientId.value,
      createdBy: createdBy,
      createdOn: createdOn,
      instRegDate: regDate,
      modifiedBy: modifiedBy,
      modifiedOn: modifiedOn,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_LUNG,
      localRegReferance: localRegReferance,
      rgTbPatientInfo: {
        ...patientFormData,
        dob: patientFormData.dob
          ? moment(patientFormData.dob).format("DD-MM-YYYY")
          : null,
      },
      rgTbVitalSigns: vitalData,
      rgTbDiagnosis: diagnosisData,
      rgTbDeathDetails: processedDeathDetails,
    };

    this._lungService.saveLungRegistry(saveData).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Lung Registry Saved Successfully.", "success");
          this.regNo = res["result"];
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire(
          "Error!",
          "Error occured while saving Renal Registry " + err.message,
          "error"
        );
      }
    );
  }
  clear() {
    this.regNo = "";
    this.lungForm.reset();
    this.formData = null;
    this.patientDetails.clear();
    this.icdData = this.diagnosis = this.rgTbVitalSign = null;
    this.centralRegNoExit = false;
    this.alive = false;
  }

  clearData() {
    this.regNo = "";
    this.lungForm.reset();
    this.formData = null;
    this.patientDetails.clear();
    this.icdData = this.diagnosis = this.rgTbVitalSign = null;
    this.centralRegNoExit = false;
    if (this.deathDetails) {
      this.deathDetails.clear();
    }
    this.alive = true;
  }

  callMpiMethod() {
    const civilId = this.patientDetails.patientForm.value.civilId;
    this._lungService.getLungRegistry(undefined, civilId).subscribe(
      (res) => {
        if (res["code"] === AppUtils.RESPONSE_SUCCESS_CODE) {
          this.clear();

          this.dataFetched = true;
          this.lungRegistryData = res["result"];
          this.centralRegNoExit = true;
          this.patientDetails.setPatientDetails(this.lungRegistryData);
          this.getRegisterForm(this.lungRegistryData);
        } else if (
          res["code"] === AppUtils.RESPONSE_NO_ECORD ||
          res["code"] === AppUtils.RESPONSE_ERROR_CODE
        ) {
          this.getData("civilId", "", civilId);
        } else {
          Swal.fire(
            " ",
            "The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.",
            "warning"
          );
        }
      },
      (error) => {
        if (error.status === 401) {
          Swal.fire("", "Error occurred while retrieving details", "error");
        }
      }
    );
  }

  getData(searchBy: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    if (searchBy === AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Registration No.";
    } else if (searchBy === AppUtils.CALLTYPE_BY_CIVILID) {
      if (
        !(
          this.patientDetails.f.civilId.value &&
          (this.patientDetails.f.exDate || this.patientDetails.f.dob)
        )
      ) {
        callMPI = false;
        msg =
          "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP.";
      } else {
        Swal.fire(
          "",
          "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.",
          "warning"
        );
      }
    }
    if (callMPI) {
      this._sharedService.setPatientData(this.patientDetails);
      this._sharedService.fetchMpi().subscribe();
    } else {
      Swal.fire("", msg, "warning");
    }
  }

  getList(centralRegNo?: any, civilId?: any) {
    this._lungService.getLungRegistry(centralRegNo, civilId).subscribe(
      (res) => {
        if (res["code"] === "S0000") {
          this.centralRegNoExit = true;
          this.lungRegistryData = res["result"];
          this.formData = this.lungRegistryData;
          this.patientDetails.setPatientDetails(this.lungRegistryData);
          this.getRegisterForm(this.lungRegistryData);
          this.estCode = this.lungRegistryData.regInst;
          this.patientId = this.lungRegistryData.rgTbPatientInfo.patientId;
        } else {
          const message =
            res["code"] === "F0000"
              ? "No Record Found with Entered Registration No."
              : res["message"];
          this.clearData();
          Swal.fire("Warning", message, "warning");
        }
      },
      (error) => {
        if (error.status === 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      }
    );
  }
  getRegisterForm(lungRegistryData: any) {
    this.currentCivilId = lungRegistryData.rgTbPatientInfo["civilId"];
    this.rgTbVitalSign = lungRegistryData.rgTbVitalSigns;

    if (lungRegistryData.rgTbDeathDetails != null) {
      this.alive = false;
    } else {
      this.alive = true;
    }
    let width = this.rgTbVitalSign
      .filter((s) => s.paramid == AppParams.VITAL_WT)
      .map((s) => s.paramValue)[0];
    let hight = this.rgTbVitalSign
      .filter((s) => s.paramid == AppParams.VITAL_HT)
      .map((s) => s.paramValue)[0];
    let bloodGroup = this.rgTbVitalSign
      .filter((s) => s.paramid == AppParams.VITAL_BLOOD_GROUP)
      .map((s) => s.paramValue)[0];

    if (bloodGroup != undefined) {
      this.lungForm.patchValue({ bloodGroup: bloodGroup.toString() });
    }

    let rgTbDeathDetails: any = lungRegistryData.rgTbDeathDetails;

    if (rgTbDeathDetails != null && rgTbDeathDetails != undefined) {
      this.alive = false;
      setTimeout(
        function () {
          _.cloneDeep(
            this.deathDetails.setDeathDetailsResult(rgTbDeathDetails)
          );
        }.bind(this),
        0
      );
    }

    this.bmiDetails(width, hight);

    this.diagnosis = lungRegistryData.rgTbDiagnosis;
    let kindeyDisease = lungRegistryData.rgTbDiagnosis
      .filter((s) => s.icdFlag == "P")
      .map((s) => s.icd)[0];

    if (kindeyDisease != null) {
      this.lungForm.patchValue({
        causeOfKindeyDisease: kindeyDisease.toString(),
      });
    }

    this.dbIcdDataGrid = lungRegistryData.rgTbDiagnosis
      .filter((s) => s.icdFlag == "O")
      .map((x) => Object.assign({}, x));
    this.icdData = lungRegistryData.rgTbDiagnosis.filter(
      (s) => s.icdFlag == "O"
    );

    this.dbIcdDataGrid.forEach((s) => {
      let theCode = s.icd;
      if (this.icdLungShortList) {
        let theReq = this.icdLungShortList.find((r) => r.code == theCode);
        if (theReq) {
          s.icdValue = theReq.disease;
        }

        this.icdData.forEach((el) => {
          if (el.icd == s.icd) {
            el.icdValue = s.icdValue;
          }
        });
      }
    });
  }

  callCase() {
    this._sharedService.setNavigationData(this.formData);
    this._router.navigate(["lung/case-details"], {
      state: { centralRegNo: this.formData.centralRegNo },
    });
  }
  updateDeathDetails() {
    Swal.fire({
      text: "Do you want to LungIcd death details?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes",
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    });
  }

  // LungIcd missing methods
  getBmi() {
    let w = this.lungForm.get("bmiBodyWidth").value;
    let h = this.lungForm.get("bmiBodyHeight").value;
    this.bmiDetails(w, h);
  }
  bmiDetails(width: any, hight: any) {
    this.lungForm.patchValue({ bmiBodyWidth: width, bmiBodyHeight: hight });
    let bmi = this._sharedService.calculateBMI(width, hight);
    this.lungForm.patchValue({
      bmi: this._decimalPipe.transform(bmi, "1.2-2"),
    });
  }
  onReady(params, grid) {
    if (this.grids.length > 0) {
      const exist = this.grids.find((item) => grid === item);
      if (!exist) {
        this.grids.push(grid);
      }
    } else {
      this.grids.push(grid);
    }

    if (grid === "comorbidDiseaseListGrid") {
      this.comorbidDiseaseGridApi = params.api;
      this.comorbidDiseaseGridColApi = params.columnApi;
    }
  }

  isObject(val) {
    return typeof val === "object";
  }

  callTissue() {
    this._sharedService.setNavigationData(this.formData);
    //console.log('centralRegNo', this.formData.centralRegNo);
    this._router.navigate(["lung/tissuetype"], {
      state: { centralRegNo: this.formData.centralRegNo },
    });
  }
}
