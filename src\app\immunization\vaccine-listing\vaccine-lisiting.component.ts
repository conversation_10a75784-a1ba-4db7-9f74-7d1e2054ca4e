import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import Swal from 'sweetalert2';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { vaccineAdmDetls } from '../../_models/vaccine-adm-detls-model';
import { MasterService } from '../../_services/master.service';

import { VaccineService } from '../vaccine.service';
import { GridOptions, _ } from "ag-grid-community";
import { SharedService } from '../../_services/shared.service';
import { Paginator } from 'primeng/primeng';
import * as AppUtils from '../../common/app.utils';
import * as AppCompUtils from '../../common/app.component-utils';
import * as moment from 'moment';
import * as CommonConstants from '../../_helpers/common.constants';
import { ButtonRendererComponent } from 'src/app/common/agGridComponents/ButtonRendererComponent';
import { vaccineStockMast } from '../../_models/vaccine-target-model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TemplateRef } from '@angular/core';
import { vaccineTbMaster } from '../../_models/vaccine-tb-master-model';
import { analyzeAndValidateNgModules } from '@angular/compiler/public_api';
import { VaccineExportExcel } from '../../_models/vaccine-export-excel.model';

import { switchMap } from 'rxjs-compat/operator/switchMap';
import { Router } from '@angular/router';



@Component({
  selector: 'app-vaccine',
  templateUrl: './vaccine-listing.component.html',
  styleUrls: ['./vaccine-listing.component.scss']
})
export class VaccineListingComponent implements OnInit {
  @ViewChild('viewVaccineStock', { static: false }) public viewVaccineStock: TemplateRef<any>;
  frameworkComponents: any;
  @ViewChild('VaccineRegPaginator', { static: false }) paginator: Paginator;
  vaccineSearchForm: FormGroup;
  vaccineData: Array<VaccineExportExcel> = new Array<VaccineExportExcel>();
  vaccineAdmDetl: Array<vaccineAdmDetls> = new Array<vaccineAdmDetls>();
  vaccineStock: Array<vaccineStockMast> = new Array<vaccineStockMast>();
  vaccineTbMaster: Array<vaccineTbMaster> = new Array<vaccineTbMaster>();
  regionData: RegionDataModel[];
  institeList: any[];
  institeListFilter: any[];
  years = AppCompUtils.VACCINE_YEAR;
  month = AppCompUtils.VACCINE_MONTH;
  period = AppCompUtils.PERIOD_DESC
  vaccineList: any[];
  gloubaleEstCode: any;
  gloubaleVaccineId: any;
  private gridApi: any;
  isDisabled: boolean;
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    columnGroupShow: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  gridOptions1: GridOptions = <GridOptions>{
    enableColResize: true,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions1.api.sizeColumnsToFit();
    }
  }
  columnVaccineStock: any[];
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  vaccinationDateMax: Date;
  vaccinationDateMin: Date;
  defaultersDateMax: Date;
  defaultersDateMin: Date;
  today: Date;
  criteria: any = {};
  successCode: any = AppUtils.RESPONSE_SUCCESS_CODE;


  constructor(private _router: Router, private _vaccineService: VaccineService, private _masterService: MasterService, private formBuilder: FormBuilder, private _sharedService: SharedService,
    private modalService: NgbModal,
  ) {

    this.frameworkComponents = {
      buttonRenderer: ButtonRendererComponent

    }

    this.getMasterData();
    this.vaccineSearchForm = this.formBuilder.group({
      'centralRegNo': [null],
      'vaccineName': [null],
      'region': [null],
      'estCode': [null],
      'estName': [null],
      'firstName': [null],
      'secondName': [null],
      'thirdName': [null],
      ' tribe': [null],
      'year': [null],
      'month': [null],
      'vaccinationDateFrom': [null],
      'vaccinationDateTo': [null],
      'civilId': [null],
      'vaccineBatchNo': [null],
      'syringeBatchNo': [null],
      'periodDesc': [null],
      'defaultersFrom': [null],
      'defaultersTo': [null],
      'dateGiven': [null],
      'dueDate': [null],
      'regType': [null],
      'fullNameCon': [null],

    });


  }

  columnDefs = [
    { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125, sortable: true },
    // { headerName: 'Year', field: 'year', minWidth: 125,sortable: true},
    // { headerName: 'Month', field: 'month', minWidth: 125,sortable: true },
    { headerName: 'Institute', field: 'estName', minWidth: 150, sortable: true },
    { headerName: 'Name', field: '', sortable: true, cellRenderer: this.getPatientFullName, width:400},
    { headerName: 'vaccineName', field: 'vaccineName', minWidth: 150, sortable: true },
    { headerName: 'GivenDate', field: 'dateGiven', minWidth: 150, sortable: true },
    { headerName: 'DueDate', field: 'dueDate', minWidth: 150, sortable: true },
    { headerName: 'CivilId', field: 'civilId', minWidth: 150, sortable: true },
    { headerName: 'syringeBatchNo', field: 'syringeBatchNo', minWidth: 150, sortable: true },
    { headerName: 'periodDesc', field: 'periodDesc', minWidth: 150, sortable: true },
    { headerName: 'BatchNo', field: 'vaccineBatchNo', minWidth: 150, sortable: true },
    {
      headerName: 'StockInfo',
      cellRenderer: 'buttonRenderer',
      sortable: true, minWidth: 150,
      cellRendererParams: {
        onClick: this.openVaccineModal.bind(this),
        label: 'Stock'
      }
    },

  ];

  ngOnInit() {
    this.getTbVaccineMaster();

  }



  VaccineStockMast(vaccineId,estCode) {
    this._vaccineService.getVaccineStockMast(estCode,vaccineId).subscribe(res => {

      this.vaccineStock = res['result'];

      // to get estName && vaccineName
      for (var i = 0; i < this.vaccineStock.length; i++) {
        this.vaccineStock[i].estName = this.institeList.filter(s => s.estCode == this.vaccineStock[i].estCode).map(s => s.estName).toString();
        this.vaccineStock[i].vaccineName = this.vaccineTbMaster.filter(s => s.vaccineId == this.vaccineStock[i].vaccineId).map(s => s.vaccineName).toString();
      }

    })

    this.columnVaccineStock = [
      { headerName: 'Institute', field: 'estName', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'vaccineName', field: 'vaccineName', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'qty', field: 'qty', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'lostQty', field: 'lostQty', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'expiryDate', field: 'expiryDate', minWidth: 150, sortable: true, sort: 'asc' },
      { headerName: 'receivedDate', field: 'receivedDate', minWidth: 150, sortable: true, sort: 'asc' },
    ];
  }


  public onGridReady(params: any) {
    this.gridApi = params.api;
  }


  getMasterData(regCode: any = 0, walCode: any = 0) {

    this._masterService.getRegionsMasterFull();
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;


    })

    this._masterService.getVaccineMaster().subscribe(res => {
      this.vaccineList = res['result'];
    })
  }


  getTbVaccineMaster() {
    this._vaccineService.getTbVaccineMaster().subscribe(res => {
      this.vaccineTbMaster = res['result'];

    })

  }

  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.institeListFilter = this.institeList;
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.institeListFilter = this.institeList;
    }
  }

  getVaccineListing(event?: any) {

    let body = this.vaccineSearchForm.value;

    if (event) {
      body["startIndex"] = event.first;
      body["rowsPerPage"] = event.rows
    } else {
      if (this.paginator) { this.paginator.first = AppUtils.INDEX; }

      body["startIndex"] = AppUtils.INDEX;

      if (body["rowsPerPage"] == null || body["rowsPerPage"] == typeof ('undefined')) {
        body["rowsPerPage"] = AppUtils.E_REGISTRY_PAGINATION_SIZE;
      }
    }

    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    //  if (curUser['roles'].filter(s => s.name == 'EREGISTRY_ADMIN').length > 0) {
    //   } else if (curUser['roles'].filter(s => s.name == 'REG_ADMIN_ELD').length > 0) {
    //   } else 
    if (curUser['roles'].filter(s => s.name == 'REG_REGION_USER_ELD').length > 0) {
      // Note: if user have region roles, will set regCode depend do defults institutes
      if (body.regCode == null) {
        body.regCode = curUser['institutes'][0].regCode;
      }
    } else if (curUser['roles'].filter(s => s.name == 'REG_EST_USER_ELD').length > 0) {
      // Note: if user have establishment roles, will set estCode depend do defults institutes
      if (body.estCode == null) {
        body.estCode = curUser['institutes'][0].estCode;
      }
    }

    if (this.vaccineSearchForm.controls['vaccinationDateFrom'].value != undefined) {
      body['vaccinationDateFrom'] = moment(this.vaccineSearchForm.controls['vaccinationDateFrom'].value, 'YYYY-MM-DD').toDate();
    }

    if (this.vaccineSearchForm.controls['vaccinationDateTo'].value != undefined) {
      body['vaccinationDateTo'] = moment(this.vaccineSearchForm.controls['vaccinationDateTo'].value, 'YYYY-MM-DD').toDate();
    }

    if (this.vaccineSearchForm.controls['defaultersFrom'].value != undefined) {
      body['defaultersFrom'] = moment(this.vaccineSearchForm.controls['defaultersFrom'].value, 'YYYY-MM-DD').toDate();
    }

    if (this.vaccineSearchForm.controls['defaultersTo'].value != undefined) {
      body['defaultersTo'] = moment(this.vaccineSearchForm.controls['defaultersTo'].value, 'YYYY-MM-DD').toDate();
    }

    let searchFilter = {
      regType: AppUtils.REG_TYPE_IMMUNIZATION,
      centralRegNo: body.centralRegNo,
      vaccineId: body.vaccineName,
      region: body.region,
      estCode: body.estCode,
      estName: body.estName,
      regCode: body.regCode,
      year: body.year,
      month: body.month,
      dateGiven: body.dateGiven,
      vaccinationDateFrom: body.vaccinationDateFrom,
      vaccinationDateTo: body.vaccinationDateTo,
      civilId: body.civilId,
      vaccineBatchNo: body.vaccineBatchNo,
      syringeBatchNo: body.syringeBatchNo,
      periodDesc: body.periodDesc,
      dueDate: body.dueDate,
      defaultersFrom: body.defaultersFrom,
      defaultersTo: body.defaultersTo,
      startIndex: body["startIndex"],
      rowsPerPage: body["rowsPerPage"]


    };

    this._vaccineService.getVaccineListing(searchFilter).subscribe(res => {
      this.vaccineAdmDetl = res['result']['paginatedList'];
      this.totalRecords = res['result']['totalRecords'];

    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })

  }

  getPatientFullName(params: any) {
    let eDiv: any = document.createElement('div');
    let fname: any = "";
    let sname: any = "";
    let tname: any = "";
    let tribe: any = "";
    if (params.node && params.node.data) {
      fname = params.node.data.firstName ? params.data.firstName : "";
      sname = params.node.data.secondName ? params.data.secondName : "";
      tname = params.node.data.thirdName ? params.data.thirdName : "";
      tribe = params.node.data.tribe ? params.data.tribe : "";
    }
    eDiv.innerHTML = '<span>' + fname + " " + sname + " " + tname + " " + tribe + '</span>';
    return eDiv;
  }

  onClear() {
    this.vaccineSearchForm.reset();
    this.institeListFilter = this.institeList;
    this.vaccineAdmDetl = null;
    this.exportExcel = null;
    this.criteria = null;
    this.vaccineData = null;

  }


  onFocusCalendar() {

    if (this.vaccineSearchForm.controls['vaccinationDateFrom'].value) {
      this.vaccinationDateMax = this.vaccineSearchForm.controls['vaccinationDateFrom'].value
    }

    if (this.vaccineSearchForm.controls['vaccinationDateTo'].value) {
      this.vaccinationDateMax = this.vaccineSearchForm.controls['vaccinationDateTo'].value
    } else {

      this.vaccinationDateMin = new Date();
    }

    if (this.vaccineSearchForm.controls['defaultersFrom'].value) {
      this.defaultersDateMax = this.vaccineSearchForm.controls['defaultersFrom'].value
    }

    if (this.vaccineSearchForm.controls['defaultersTo'].value) {
      this.vaccinationDateMax = this.vaccineSearchForm.controls['defaultersTo'].value
    } else {

      this.defaultersDateMin = new Date();
    }
  }


  // Open dialgue box 
  openVaccineModal(e) {
    this.modalService.open(this.viewVaccineStock, { size: <any>'lg' });
    this.VaccineStockMast(e.rowData.vaccineId, e.rowData.estCode);
  }

  exportExcel() {
  
    this. vaccineData = new Array<VaccineExportExcel>();
    if (this.vaccineAdmDetl && this.vaccineAdmDetl.length > 0) {
      if (this.totalRecords == this.vaccineAdmDetl.length) {
        this.vaccineAdmDetl.forEach(el => {
          this.vaccineData.push({
            civilId: el.civilId,dateGiven: el.dateGiven, dueDate: el.dueDate,estName: el.estName, vaccineName:el.vaccineName,month:el.month,
            year: el.year, 
          })
        });
      
  
        this._sharedService.exportAsExcelFile(this. vaccineData, "Vaccine_Listing");
      
      } else {
        this.criteria["startIndex"] = AppUtils.INDEX;
        this.criteria['rowsPerPage'] = this.totalRecords;
        this._vaccineService.getVaccineListing(this.criteria).subscribe(res => {
          if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
            let excelData = res['result']['paginatedList'];
           
            if (excelData && excelData.length > 0) {
              excelData.forEach(el => {
                this.vaccineData.push({
                  civilId: el.civilId,dateGiven: el.dateGiven, dueDate: el.dueDate,estName: el.estName, vaccineName:el.vaccineName,month:el.month,
                  year: el.year, 
                })
              })
              this._sharedService.exportAsExcelFile(this.vaccineData, "Vaccine_Listing");
            }
          }
        });
      }//
    } else {
      Swal.fire('Warning!', 'Please search first', 'warning')

  }


  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['vaccine/registry'], { state: { centralRegNo: event.data.centralRegNo } });
  }

}













