import { liverTransCompListModel } from "./liver-trans-comp-model";
import { liverTransDiseaseListModel } from "./liver-trans-disease-model";
import { liverTransMedListModel } from "./liver-trans-med-model";

export class liverTransplantFollowupModel {
  fwUpId: number;
  centralRegNo: number;
  fwUpDate: Date;
  transferredYn: string;
  transferredTo: number;
  deathYn: string;
  causeOfDeath: string;
  remarks: string;
  createdDate?:Date;
  createdBy?:string;
  modifiedDate: string;
  modifiedBy:number;
  returnDialysisDate: Date;
  returnDialysisReason: string;
  registerType: number;
  rgTbOrganTransCompList: liverTransCompListModel[] = [];
  rgTbOrganTransMedList: liverTransMedListModel[] = [];
  rgTbOrganTransDiseaseList: liverTransDiseaseListModel[] = [];
}
