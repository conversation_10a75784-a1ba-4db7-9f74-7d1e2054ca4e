import { CommunicationDetail } from "./communicationDetail.model";
import { <PERSON><PERSON>aDoctor } from "./corneaDoctor.model";
import { CorneaTissue } from "./corneaTissue.model";

export interface CorneaRequest {
  requestId: number;
  reqInst: number;
  reqDate: Date;
  requestTo: number;
  indication: number;
  primaryDiag: string;
  sizeZone: number;
  otherSpec: string;
  intendedArrDate: Date;
  intendedSurDate: Date;
  patientId: number;
  patCivilId: number;
  patRemarks: string;
  reqRemarks: string;
  status: string;
  invoiceNo: string;
  createdDate : Date;
  createdBy: number;
  rgTbCorReqDoctors: CorneaDoctor[];
  rgTbCorReqTissues: CorneaTissue[];
  rgTbCorReqCommDtls: CommunicationDetail[];
}
