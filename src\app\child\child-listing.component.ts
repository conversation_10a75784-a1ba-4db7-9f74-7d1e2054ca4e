import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { GridOptions } from 'ag-grid-community';
import * as AppUtils from '../common/app.utils';
import { SharedService } from '../_services/shared.service';
import { MasterService } from '../_services/master.service';
import { ChildService } from './childService';
import * as AppCompUtils from '../common/app.component-utils';
import Swal from 'sweetalert2';
import * as moment from 'moment';


@Component({
  selector: 'app-child',
  templateUrl: './child-listing.component.html',
  styleUrls: ['./child-listing.component.scss']
})
export class ChildListingComponent implements OnInit {
  childListingForm: FormGroup;
  regions: any[];
  wallayatList: any[];
  wallayatListFilter: any[];
  instituteList: any[];
  instituteListFilter: any[];
  genderTypeOptions = [];
  nationList: any;
  dateFormat = 'dd/mm/yy';
  rowData: any[] = [];
  columnDefs: any[];
  institutes: any;
  childinstitutes: any[];
  nationalityList: any[];

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };




  constructor(private fb: FormBuilder, private sharedService: SharedService, private masterService: MasterService, private childService: ChildService) { 
    this.masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];

    });
    this.childListingForm = this.fb.group({
            central_RegNo: new FormControl(null),
            civilId: new FormControl(null),
            patientId: new FormControl(null),
            child_Id: new FormControl(null),
            fullName: new FormControl(null),
            ageFrom: new FormControl(null),
            ageTo: new FormControl(null),
            sex: new FormControl(null),
            nationality: new FormControl(null),
            reg_Inst: new FormControl(null),
            reg_DateFrom: new FormControl(null),
            reg_DateTo: new FormControl(null),
            regCode: new FormControl(null),
            wal_Code: new FormControl(null),
            apgar_1: new FormControl(null),
            apgar_5: new FormControl(null),
            sickle_Cell_Yn:new FormControl(null),
            thalassmia_Yn: new FormControl(null),
            congenital_Anamoly_Yn:new FormControl(null),
            cord_Tsh_Yn: new FormControl(null),
            g6PD:new FormControl(null),
    });


    this.columnDefs = [
      { headerName: 'Registration No', field: 'central_RegNo', width: 99, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', width: 125, sortable: true },
      { headerName: 'Patient ID', field: 'patientId', width: 125, sortable: true },
      { headerName: 'Child ID', field: 'child_Id', width: 66, sortable: true },
      { headerName: 'Full Name', field: 'fullName', width: 200, sortable: true },
      { headerName: 'Gender', field: 'sex', width: 100, sortable: true },
      { headerName: 'Age', field: 'age', width: 88, sortable: true },
      { headerName: 'Institute', field: 'reg_Inst', width: 200, sortable: true, cellRenderer: this.getAncInstitute },
      { headerName: 'Nationality', field: 'nationality', width: 100, sortable: true , cellRenderer: this.cellRendererNationality },
      // { headerName: 'Governorate', field: 'regCode', width: 100, sortable: true },
      // { headerName: 'Wilayat', field: 'walName', width: 100, sortable: true },
      { headerName: 'Registration Date', field: 'reg_Date', width: 100, sortable: true , cellRenderer: this.getDateFormat  },
      { headerName: 'APGAR_1', field: 'apgar_1', width: 100, sortable: true },
      { headerName: 'APGAR_5', field: 'apgar_5', width: 100, sortable: true},
    ];



  }

  ngOnInit() {

    this.masterService.getWallayatMasterFull();
    this.masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
    });
    this.genderTypeOptions = AppCompUtils.GENDER;
    this.loadMasters();
    this.getNationalityList();
  }

  getAncInstitute = (reg_Inst) => {
    if (reg_Inst.value) {
      let estName;
      this.institutes.forEach(ele => {
        if (reg_Inst.value == ele.estCode) {
          estName = ele.estName;
        }
      })
      return estName;
    } else {
      return ' '
    }
  };

  changeAncInstitute(obj) {
    if (obj && !obj.estCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];
    }
  }

  cellRendererNationality = (data) => {
    if (data.value) {
      let NationalityName = ''
      this.nationalityList.forEach((item) => {
        if (item.natCode == data.value) {
          NationalityName = item.nationality
        }
      });
      return NationalityName;
    } else {
      return '';
    }
  };


  loadMasters() {
    this.masterService.getRegionsMasterFull();
    this.masterService.regionsMasterFull.subscribe(value => {
      this.regions = value;
    });

    this.masterService.getWallayatMasterFull();
    this.masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });

    this.masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.childinstitutes = this.institutes;

    });

  }

  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };

  getNationalityList(natCode: any = 0) {
    this.masterService.getNationalityList(natCode).subscribe(response => {
      this.nationalityList = response.result;
    }, error => {
    });
  }

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.instituteListFilter = this.instituteList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.instituteListFilter = this.instituteList;
    }
  }


  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.childListingForm.value['regCode'] && (this.childListingForm.value['regCode'] != null || this.childListingForm.value['regCode'] != 0)) {
          this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.childListingForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.childListingForm.value['regCode']);
        } else {
          this.instituteListFilter = this.instituteList;
        }
      }
      else {
        this.instituteListFilter = this.instituteList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.childListingForm.value['regCode'] && (this.childListingForm.value['regCode'] != null || this.childListingForm.value['regCode'] != 0)) {
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.childListingForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.childListingForm.value['regCode']);
      } else {
        this.instituteListFilter = this.instituteList;
      }
    }
  }

  

  exportExcel() { }

  clear() {
    this.childListingForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.rowData = [];
  }
  getList() {
    const searchCriteria = this.childListingForm.value;
  
    // Loop through each property in searchCriteria
    for (const key in searchCriteria) {
      if (searchCriteria.hasOwnProperty(key) && typeof searchCriteria[key] === 'boolean') {
        // Convert boolean properties to "Y" if true
        searchCriteria[key] = searchCriteria[key] ? 'Y' : 'N';
      }
    }
  
    const source = {
      central_RegNo: searchCriteria.central_RegNo,
      civilId: searchCriteria.civilId,
      patientId: searchCriteria.patientId,
      child_Id: searchCriteria.child_Id,
      fullName: searchCriteria.fullName,
      ageFrom: searchCriteria.ageFrom,
      ageTo: searchCriteria.ageTo,
      sex: searchCriteria.sex,
      nationality: searchCriteria.nationality,
      reg_Inst: searchCriteria.reg_Inst,
      reg_DateFrom: searchCriteria.reg_DateFrom,
      reg_DateTo: searchCriteria.reg_DateTo,
      reg_Code: searchCriteria.regCode,
      wal_Code: searchCriteria.wal_Code,
      apgar_1: searchCriteria.apgar_1,
      apgar_5: searchCriteria.apgar_5,
      sickle_Cell_Yn: searchCriteria.sickle_Cell_Yn,
      thalassmia_Yn: searchCriteria.thalassmia_Yn,
      congenital_Anamoly_Yn: searchCriteria.congenital_Anamoly_Yn,
      cord_Tsh_Yn: searchCriteria.cord_Tsh_Yn,
      g6PD: searchCriteria.g6PD,
      pageable: {
        page: 0,
        size: 10
      }
    };
  
    this.childService.getchildList(source).subscribe(
      (res) => {
        this.rowData = res["result"]["content"];
        for (let i = 0; i < this.rowData.length; i++) {
          this.rowData[i].sex = this.genderTypeOptions.filter(s => s.id == this.rowData[i].sex).map(s => s.value).toString();

        }
      },
      (error) => {
        if (error.status === 401) {
          Swal.fire('Error!', 'Error occurred while retrieving details', 'error');
        }
      }
    );
  }
  

  changeDateFormat() {
    let body = this.childListingForm.value;
    if (this.childListingForm.controls['reg_DateFrom'].value) {
      body['reg_DateFrom'] = moment(this.childListingForm.controls['reg_DateFrom'].value, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
    if (this.childListingForm.controls['reg_DateTo'].value) {
      body['reg_DateTo'] = moment(this.childListingForm.controls['reg_DateTo'].value, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
  }
  

}
