import { Component, OnInit, ViewChild } from '@angular/core';
import { SelectItem } from 'primeng/api'
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { GridOptions, _ } from "ag-grid-community";
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MasterService } from 'src/app/_services/master.service';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import * as AppCompUtils from 'src/app/common/app.component-utils';
import * as AppUtils from 'src/app/common/app.utils';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';
import { BrainDeathListResult } from 'src/app/_models/BrainDeathListResult';
import { RenalService } from '../../renal.service';
import { data } from 'jquery';
import * as CommonConstants from 'src/app/_helpers/common.constants';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { SharedService } from 'src/app/_services/shared.service';
import { Paginator } from 'primeng/primeng';
import { autoCol } from '@syncfusion/ej2-angular-grids';


@Component({
  selector: 'app-brain-death-listing',
  templateUrl: './brain-death-listing.component.html',
  styleUrls: ['./brain-death-listing.component.scss'],
})

export class BrainDeathListingComponent implements OnInit {
   @ViewChild('ListingPaginator', { static: false }) paginator: Paginator;
  braindeathTypeMaster: any[];
  regionData: RegionDataModel[];
  braindeathSubTypesList: any[];
  wallayatList: any[];
  wallayatListFilter: any[];
  villageListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  vaccinationDateMax: Date;
  vaccinationDateMin: Date;
  totalRecords: any;
  paginationSize:any;
  gender = AppCompUtils.GENDER;
  setCheck = AppCompUtils.YES_NO_NULL;
  criteria: any = {};
  brainDeathTypeMaster: any[];
  rowData: Array<BrainDeathListResult> = new Array<BrainDeathListResult>();
  brainDeathSearchForm: FormGroup;
  frameworkComponents;
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
      pagination: false,
      resizable: true,
     paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
     onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    } 
    };
 

  constructor(private formBuilder: FormBuilder, private _router: Router, private http: HttpClient, private _sharedService: SharedService,
    private _masterService: MasterService, private _renalService: RenalService, public datepipe: DatePipe) {
    this.getMasterData();
   
    this.brainDeathSearchForm = this.formBuilder.group({
      'civilId': [null],
      'patientId': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'estCode': [null],
      'sex': [null],

      'setCount': [null],
      'set1Ex1': [null],
      'set1Ex2': [null],
      'set2Ex1': [null],
      'set2Ex2': [null],

      'set1Ex1DateFrom': [null],
      'set1Ex1DateTo': [null],

      'set1Ex2DateFrom': [null],
      'set1Ex2DateTo': [null],

      'set2Ex1DateFrom': [null],
      'set2Ex1DateTo': [null],

      'set2Ex2DateFrom': [null],
      'set2Ex2DateTo': [null],



    });



  }
  columnDefs = [
    { headerName: 'Civil ID', field: 'civilId', width: 100, sortable: true },
    { headerName: 'Patient ID', field: 'patientId', width: 100, sortable: true },
    { headerName: 'Date of Birth', field: 'newdate', width: 100, sortable: true },
    // { headerName: 'First Name', field: 'firstName', minWidth: 10, sortable: true },
    // { headerName: 'Second Name', field: 'secondName', minWidth: 10, sortable: true },
    // { headerName: 'Third Name', field: 'thirdName', minWidth: 10, sortable: true },
    // { headerName: 'Tribe', field: 'tribe', minWidth: 10, sortable: true },
    { headerName: 'Name', field: 'name', minWidth: 10, sortable: true },
    { headerName: 'Gender', field: 'sex', width: 85, sortable: true },
    { headerName: 'Institute', field: 'estName', minWidth: 10, sortable: true },
    { headerName: 'setCount', field: 'setCount', width: 110, sortable: true },
   
 
    {
      headerName: 'S1 E1',
      field: 'set1Ex1',
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        }else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        }else{
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85,
      sortable: true
    },

    {
      headerName: 'S1 E2',
      field: 'set1Ex2',
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        }else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        }else{
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85,
      sortable: true
    },

    {
      headerName: 'S2 E1',
      field: 'set2Ex1',
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        }else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        }else{
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85,
      sortable: true
    },


    {
      headerName: 'S2 E2',
      field: 'set2Ex2',
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        }else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        }else{
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85,
      sortable: true
    },


  ];

  ngOnInit() {
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsMaster();
    this._masterService.regionsMaster.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
    })
    this._masterService.getDiabetesTypes().subscribe(value => {
      this.braindeathTypeMaster = value["result"];
    });

    this._masterService.getAllDiabeticSubtypes().subscribe(res => {
      this.braindeathSubTypesList = res.result;
    })

  }


  clearData() {
    this.rowData = [];
    this.brainDeathSearchForm.reset();
    this.institeListFilter = this.institeList;
  }

  listBrainDeath(event?:any) {

    let scores = this.brainDeathSearchForm.value;


   
    // let searchData = {
    //   civilId: scores.civilId,
    //   patientId: scores.patientId,
    //   estName: scores.estName,
    //   sex: scores.sex,
    //   dob: scores.dob,
    //   firstName: scores.firstName,
    //   secondName: scores.secondName,
    //   thirdName: scores.thirdName,
    //   tribe: scores.tribe,
    //   ageFrom: scores.ageFrom,
    //   ageTo: scores.ageTo,
    //   estCode: scores.estCode,
    //   firstExamDateFrom: scores.firstExamDateFrom,
    //   firstExamDateTo: scores.firstExamDateTo,
    //   secondExamDateFrom: scores.secondExamDateFrom,
    //   secondExamDateTo: scores.secondExamDateTo,
    // }
      
  

    let pageable = {
      page: event?event.page:0,
      size: event?event.rows:AppUtils.E_REGISTRY_PAGINATION_SIZE
    }
    
    scores = {pageable, ...scores}
    this._renalService.getBrainDeathData(scores).subscribe(async res => {
      this.rowData = res['result']['content'];
      let data=[] ;
      this.rowData.forEach(e=> {
      //  e.dobs = this._sharedService.setDateFormat2(e.dob);
        data.push({'name': e.firstName+' '+e.secondName+' '+e.thirdName+' '+e.tribe , 'newdate':this._sharedService.setDateFormat2(e.dob) , ...e});

        
      })

      this.rowData = data;

      this.paginationSize = res['result']['size'];
      this.totalRecords = res['result']['totalElements'];
    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while retrieving Brain Death details', 'error')
    })

  }


  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['braindeath/registration'], { state: { civilId: event.data.civilId } });
  }


}

