import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PatientDetailsComponent } from 'src/app/_comments/patient-details/patient-details.component';
import { AncLabInvestModel } from 'src/app/_models/ancLabInvest.model';
import { AncRegisterService } from './anc-register.service';
import * as AppUtils from 'src/app/common/app.utils';
import { AncMidicalHistoryModel } from 'src/app/_models/ancMidicalHistory.model';
import { ancBirthSpacingModel } from 'src/app/_models/ancBirthSpacing.model';
import Swal from 'sweetalert2';
import { AncImmunizationModel } from 'src/app/_models/ancImmunization.model';
import { AncVisitsModel } from 'src/app/_models/ancVisits.model';
import { DatePipe } from '@angular/common';
import { MasterService } from 'src/app/_services/master.service';
import * as moment from 'moment';
import * as _ from 'lodash';
import { BsDatepickerConfig } from 'ngx-bootstrap/datepicker';
import { AncBabyDetailsModel } from 'src/app/_models/ancBabyDetails.model';
import { PncVisitsModel } from 'src/app/_models/pncVisits.model';
import * as CommonConstants from '../../_helpers/common.constants';
import { AncRequestService } from 'src/app/ancRequest/ancRequest.service';
import { TbPatientInfo } from 'src/app/_models/patient-info.model';
import { AncRegisterModel } from 'src/app/_models/ancRegister.model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedService } from 'src/app/_services/shared.service';
import { AncService } from '../anc.service';

@Component({
  selector: 'app-anc-register',
  templateUrl: './anc-register.component.html',
  styleUrls: ['./anc-register.component.scss'],
  providers: [DatePipe],
})
export class AncRegisterComponent implements OnInit {
  opened;

  readOnly: boolean;
  searchForm: FormGroup;
  addNewImmunForm: FormGroup;
  bspMethodForm: FormGroup;
  patientForm: FormGroup;
  bookingForm: FormGroup;
  followUpForm: FormGroup;
  deliveryDetailForm: FormGroup;
  babyDetailForm: FormGroup;
  pncVisitForm: FormGroup;
  abortionType: any[];
  ancSearch: any;
  ancNoSearch: any;
  allAncData: any;
  ancData: any;
  institutes: any;

  DeliveryMode: any;
  deliveryMode: any;
  perinealTears: any;
  deliveryType: any;
  pregnancyOutcome: any;
  vaccinMastList: any;
  loginId: any;
  ancRegList: any;
  civilIdField: any;

  immunAddNewRow: boolean;
  disSelecorBol: boolean = true;
  selectedEarlyUsFindings: boolean = false;
  PncVisitClickedAdd: boolean = true;
  showPopup: boolean = false;
  submitted = false;
  newRegId: boolean = true;

  selectedFUDate: number;
  selectedPNCDate: number;
  ancNo: any;
  newOrReg: string;
  errorMsg: string;

  today = new Date();

  selected = [];
  ancInstitutesFilter: any[];
  ancImmList: any = [];
  ancLabInvestP: any[];
  ancLabInvestB: any[];
  ancLabInvestF: any[];
  ancMidicalHistoB: any[];
  ancMidicalHistoF: any[];
  ancVisitsList: AncVisitsModel[] = [];
  babydetalisList: AncBabyDetailsModel[] = [];
  pncVisitsList: PncVisitsModel[] = [];

  selectedBaby: AncBabyDetailsModel;
  selectedFollowUp: AncVisitsModel;
  selectedPncVisit: PncVisitsModel;

  bsConfig: Partial<BsDatepickerConfig>;

  ancLabInvest: Array<AncLabInvestModel> = new Array<AncLabInvestModel>();
  ancMidicalHisto: Array<AncMidicalHistoryModel> = new Array<AncMidicalHistoryModel>();
  ancBirthSpacing: Array<ancBirthSpacingModel> = new Array<ancBirthSpacingModel>();
  immunizationData: Array<AncImmunizationModel> = new Array<AncImmunizationModel>();

  public ancVisitDateslist: {
    visitId: number;
    visitDate: Date;
  }[] = [];

  @ViewChild('viewSmallANCWindow', { static: false }) public viewSmallANCWindow: TemplateRef<any>;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('addNew', { static: true }) addNewButton: ElementRef;
  @ViewChild('popupModal', { static: false }) public popupModal: TemplateRef<any>;

  cellRenderer = (data) => {
    if (data && data.value) {
      return '';
    }
    if (data && data.value != null) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    }
    if (data) {
      const formattedDate = moment(data).format('DD-MM-yyyy');
      return formattedDate;
    }
    else {
      return '';
    }
  };

  cellRendererBack = (data) => {
    if (data) {
      if (data > 0) {
        const formattedDate = data
        return formattedDate;
      } else {
        const [day, month, year] = data.split('-').map(Number);
        const dateObject = new Date(year, month - 1, day);
        const formattedDate = dateObject.getTime();
        return formattedDate;
      }
    } else {
      return '';
    }
  };

  cellRendererInstName = (data) => {
    if (data.value) {
      let instName = '';


      this.institutes.filter(el => {
        if (el.estCode == data.value) {
          instName = el.estName
        }
      })
      return instName;
    } else {
      return '';
    }
  };

  cellRendererAncDtlsStatus = (data) => {
    if (data.value) {
      let statusName = '';
      this.ancVwDtlsStatus.filter(el => {
        if (el.code == data.value) {
          statusName = el.name
        }
      })
      return statusName;
    } else {
      return '';
    }
  };

  // Constants 
  public page = 1;
  public page1 = 1;
  public page2 = 1;
  public page3 = 1;
  public page4 = 1;
  public pageSize = 2;

  active = 'top';

  ancVwDtlsStatus = [{ code: 'P', name: 'Pending' },
  { code: 'A', name: 'Approved' },
  { code: 'R', name: 'Rejected' },
  { code: 'C', name: 'Cancelled' }];

  findings = [{ code: 'N', name: 'Normal' },
  { code: 'A', name: 'Abnormal' }];

  typeOfPregnancy = [{ code: 'S', name: 'Single' },
  { code: 'M', name: 'Multiple' }];

  riskGrading = [{ code: 'L', name: 'Low' },
  { code: 'H', name: 'High' }];

  places = [{ code: 'C', name: 'Hospital/ Clinic' },
  { code: 'B', name: "Born before arrival(BBA)" },
  { code: 'H', name: "Home delivery" }];

  bStatus = [{ code: 'B', name: 'Booked' },
  { code: 'U', name: 'UnBooked' }];

  babyScHypoth = [{ code: 'N', name: 'Normal' },
  { code: 'H', name: "Hypothyroidism" },
  { code: 'T', name: "Not Done" }];

  echoTestLst = [{ code: 'P', name: 'Passed' },
  { code: 'F', name: "Failed" },
  { code: 'T', name: "Not Done" }];
  followUpClickedAdd: boolean = true;
  columnAncRegList: { headerName: string; field: string; minWidth: number; sortable: boolean; sort: string; cellRenderer?: any; }[];
  centralRegNoExit: boolean = false;
  abortionYn: any;


  constructor(private fb: FormBuilder, private _ancReqService: AncRequestService, private masterService: MasterService, private modalService: NgbModal, private _sharedService: SharedService, private _ancRegisterService: AncRegisterService, private _ancService: AncService) {
    this.initialsForms();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        let ancNo = this._sharedService.getNavigationData().ancNo;
        this.search(ancNo);
        this._sharedService.setNavigationData(null);
      }, 1000);

    }
  }

  ngOnInit() {
    this.followUpForm.controls['typeTest'].disable();
    this.deliveryDetailForm.controls['typeOfBortion'].disable();
    this.initialsMainList();
    this.loadMast();
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode;
    this.searchForm = this.fb.group({
      ancNo: new FormControl(''),
      civilId: new FormControl(null)
    });
  }

  initialsMainList() {
    this.allAncData = this.allAncData ? this.allAncData : {};
    this.allAncData.rgTbAncRegister = this.allAncData.rgTbAncRegister ? this.allAncData.rgTbAncRegister : [];
    if (this.allAncData.rgTbAncRegister.length === 0) {
      this.allAncData.rgTbAncRegister.push({});
    }

    if (this.allAncData.rgTbAncRegister.length > 0) {
      this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails = this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails ? this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails : [];
      this.allAncData.rgTbAncRegister[0].rgTbAncVisits = this.allAncData.rgTbAncRegister[0].rgTbAncVisits ? this.allAncData.rgTbAncRegister[0].rgTbAncVisits : [];
      this.allAncData.rgTbAncRegister[0].rgTbPncVisits = this.allAncData.rgTbAncRegister[0].rgTbPncVisits ? this.allAncData.rgTbAncRegister[0].rgTbPncVisits : [];
      this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod = this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod ? this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod : [];
    }

  }

  private initialsForms() {
    this.bspMethodForm = this.fb.group({
      checkboxes: this.fb.array([]),
    });
    this.initialsPatientForm();
    this.initialsBookingForm();
    this.initialsFollowUpForm();
    this.initialsDeliveryDetailForm();
    this.initialsBabyDetailForm();
    this.initialsPncVisitForm();

    this.selectedBaby = { ...this.babyDetailForm.value };
    this.selectedFollowUp = { ...this.followUpForm.value };
    this.selectedPncVisit = { ...this.pncVisitForm.value }
  }

  private initialsPatientForm() {
    this.patientForm = this.fb.group({
      'centralRegNo': [null],
      'patientId': [null, Validators.required],
      'civilId': [null, Validators.required],
      'dob': new FormControl(null, Validators.required),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'firstName': [null, Validators.required],
      'secondName': new FormControl(null, Validators.required),
      'sex': new FormControl(),
      'maritalStatus': new FormControl(),
      'thirdName': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'kinTelNo': new FormControl(),
      'careGiverTel': new FormControl(),
      'regInst': [new FormControl(), Validators.required],
      'exDate': [null],

    });
  }

  private initialsBookingForm() {
    this.bookingForm = this.fb.group({
      ancNo: new FormControl(null, Validators.required),
      gravida: new FormControl(null, Validators.required),
      parity: new FormControl(null, Validators.required),
      abortion: new FormControl(null),
      ectopicPreg: new FormControl(null),
      mensturalCycle: new FormControl(null),
      mensturalLength: new FormControl(null),
      congAnamoliesYn: new FormControl(null),
      ancInstitute: new FormControl(null, Validators.required),
      lmp: new FormControl(null, Validators.required),
      eddCalc: new FormControl(null, Validators.required),
      eddScan: new FormControl(null),
      eddCorrected: new FormControl(null),
      earlyUsFindings: new FormControl(null),
      earlyUsRemarks: new FormControl(null),
      birthInterval: new FormControl(null),
      pregnancyType: new FormControl(null),
      contraMethodUsedYn: new FormControl(null),
      plannedPregnancy: new FormControl(null),
      riskFactorsYn: new FormControl(false),
      riskFactorsRemarks: new FormControl(null),
      height: new FormControl(null),
      weight: new FormControl(null),
      bmi: new FormControl(null),
      vteRiskScore: new FormControl(null),
      anaemic: new FormControl(null),
      riskGrading: new FormControl(null, Validators.required),
      statusInLastPregnancy: new FormControl(null),
      lastDeliveryDate: new FormControl(null),
      lastAbortionDate: new FormControl(null),
      abortionYn: new FormControl(false),
      pregnancyId: new FormControl(null)

    });
  }

  private initialsFollowUpForm() {
    this.followUpForm = this.fb.group({
      visitId: new FormControl(null),
      visitDate: new FormControl(null),
      scanFindings: new FormControl(null, Validators.required),
      scanRemarks: new FormControl(null),
      riskGrade: new FormControl(null, Validators.required),
      headCircum: new FormControl(null),
      abdomenCircum: new FormControl(null),
      estFetalWeight: new FormControl(null),
      abortionType: new FormControl(null),
      typeTest: new FormControl(null),

      abortionYn: new FormControl(false),
      appDate: new FormControl(null),
      docComments: new FormControl(null),
      planManagement: new FormControl(null),
      refToNutrition: new FormControl(false),
      refToHealthEdu: new FormControl(false),
      refToObstetrician: new FormControl(false),
      refToPsychatrist: new FormControl(false),
      refToOtherDept: new FormControl(false),
      otherDept: new FormControl(null),
      refReason: new FormControl(null),
      index: new FormControl(null),
    });
  }

  private initialsDeliveryDetailForm() {
    this.deliveryDetailForm = this.fb.group({
      abortionYn: new FormControl(false),
      gestAge: new FormControl(null),
      abortionType: new FormControl(null),
      deliveryDate: new FormControl(null),
      deliveryPlaceType: new FormControl(null),
      placeOfdelivery: new FormControl(null),
      bookingStatus: new FormControl(null),
      totalAncVisits: new FormControl(null),
      riskGrade: new FormControl(null),
      riskGradeRemarks: new FormControl(null),
      deliveryMode: new FormControl(null),
      perineum: new FormControl(null),
      deliveryType: new FormControl(null),
      pregOutcome: new FormControl(null),
      condAtDelivery: new FormControl(null),
      vteScore: new FormControl(null),
      deliveryId: new FormControl(null),
      typeOfBortion: new FormControl(null)

    });
  }

  private initialsBabyDetailForm() {
    this.babyDetailForm = this.fb.group({
      babyId: new FormControl(null),
      babyWeight: new FormControl(null),
      conAnamolyYn: new FormControl(false),
      conAnamolyRemarks: new FormControl(null),
      hypothyroidismYn: new FormControl(null),
      echoTest: new FormControl(null),
      babyFileNo: new FormControl(null),
      index: new FormControl(null)

    });


  }




  private initialsPncVisitForm() {
    this.pncVisitForm = this.fb.group({
      visitDate: new FormControl(null),
      breastFeeding: new FormControl(false),
      bpSys: new FormControl(null),
      bpDia: new FormControl(null),
      breastExam: new FormControl(null),
      breastExamRemarks: new FormControl(null),
      id: new FormControl(null),
      parmDesc: new FormControl(null),
      result3: new FormControl(null),
      remarks3: new FormControl(null),
      index: new FormControl(null),
      advice: new FormControl(null),
      pncId: new FormControl(null)
    });
  }

  getLabInvest() {
    this.ancLabInvestB = [];
    this.ancLabInvestF = [];
    this.ancLabInvestP = [];
    this._ancService.getAncLabInvest().subscribe(res => {
      this.ancLabInvest = res["result"];
      this.ancLabInvestB = _.cloneDeep(this.ancLabInvest);
      this.ancLabInvestF = _.cloneDeep(this.ancLabInvest);
      this.ancLabInvestP = _.cloneDeep(this.ancLabInvest);

    })
  }

  getMedHistory() {
    this.ancMidicalHistoB = [];
    this.ancMidicalHistoF = [];
    this._ancService.getAncMidicalHistory().subscribe(res => {
      this.ancMidicalHisto = res["result"];
      this.ancMidicalHistoB = _.cloneDeep(this.ancMidicalHisto);
      this.ancMidicalHistoF = _.cloneDeep(this.ancMidicalHisto);
    })
  }

  // Open dialgue box 
  openSmallWindow(patientDtlsCivilId?) {
    let civilId = this.searchForm.get('civilId').value;
    if (patientDtlsCivilId) {
      this.modalService.open(this.viewSmallANCWindow, { size: <any>'lg' });
      this.getAncList(patientDtlsCivilId);
    }
    else if (civilId) {
      this.modalService.open(this.viewSmallANCWindow, { size: <any>'lg' });
      this.getAncList(civilId);
      civilId = null;
    }
    else {
      Swal.fire('Alert!', 'Please enter CIVIL ID.', 'warning');
    }
  }

  getAncList(civilId: any) {
    this._ancRegisterService.getAncRegisterByCivilId(civilId).subscribe(async res => {
      this.ancRegList = res['result'];
    })

    this.columnAncRegList = [
      { headerName: 'ANC NO.', field: 'ancNo', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'ANC Institute', field: 'ancInstitute', minWidth: 125, sortable: true, sort: 'asc', cellRenderer: this.cellRendererInstName },
      { headerName: 'Status', field: 'status', minWidth: 125, sortable: true, sort: 'asc', cellRenderer: this.cellRendererAncDtlsStatus },
      { headerName: 'Status Date', field: 'statusDate', minWidth: 125, sortable: true, sort: 'asc', cellRenderer: this.cellRenderer },
      { headerName: 'Action', field: 'action', minWidth: 125, sortable: true, sort: 'asc' },
    ];
  }

  onGridReady(params) {
    params.api.sizeColumnsToFit();
  }

  onRowClicked(event) {
    if (event.data.ancNo == "" || event.data.ancNo == null) {
      Swal.fire({
        title: 'Confirmation',
        text: 'ANC Request Available. Want to download ANC details to register ?',
        icon: 'question',
        showCancelButton: true, // Display the Cancel button
      }).then((result) => {
        if (result.isConfirmed) {
          // The user clicked the OK button
          // get req by requestId
          this.clear();
          this.getAncReqByRequestId(event.data.requestId)
          this.modalService.dismissAll(this.viewSmallANCWindow);
        }
        this.searchForm.get('ancNo').patchValue('');
      });
    } else {
      this.searchForm.get('ancNo').patchValue(event.data.ancNo);
      this.search()
      this.modalService.dismissAll(this.viewSmallANCWindow);
    }
    this.searchForm.get('civilId').patchValue(null);
    this.ancRegList = [];
  }

  loadMast() {
    this.masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institutes = res['result'];
      this.ancInstitutesFilter = res['result'];
    });
    this.masterService.getVaccinationMastList().subscribe(response => {
      this.vaccinMastList = response.result;
    }, error => {
    });

    this.getLabInvest();
    this.getMedHistory();
    this.getBirtSpacing();
    this.getAbortionType();
    this.getDeliveryMode();
    this.getPerinealTears();
    this.getDeliveryType();
    this.getPregnancyOutcome();
  }

  changeAncInstitute(obj) {
    if (obj && !obj.regCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];
    }
  }

  getBirtSpacing() {
    this._ancService.getAncBirtSpacing().subscribe(res => {
      this.ancBirthSpacing = res["result"];
    })
  }

  getDeliveryMode() {
    this._ancService.getDeliveryMode().subscribe(res => {
      this.deliveryMode = res["result"];
    })
  }

  getPerinealTears() {
    this._ancService.getPerinealTears().subscribe(res => {
      this.perinealTears = res["result"];
    })
  }

  getDeliveryType() {
    this._ancService.getDeliveryType().subscribe(res => {
      this.deliveryType = res["result"];
    })
  }

  getPregnancyOutcome() {
    this._ancService.getPregnancyOutcome().subscribe(res => {
      this.pregnancyOutcome = res["result"];
    })
  }

  getAbortionType() {
    this._ancService.getAbortionType().subscribe(res => {
      this.abortionType = res["result"];
    })
  }

  addRemoveBspItem(data: any, event: any, list: any) {
    if (event.target.checked) {
      this[list].find(rec => data.id === rec.id).checked = true;
      this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod = this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod ? this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod : [];
      this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod.push({ "methodUsed": data.id })
    } else {
      this[list].find(rec => data.id === rec.id).checked = false;

      for (let i = 0; i < this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod.length; i++) {
        if (this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod[i].methodUsed === data.id) {
          this.allAncData.rgTbAncRegister[0].rgTbAncBspMethod.splice(i, 1);
        }
      }
    }
  }

  addRemoveBookingLabItem(data: any, event: any, list: any) {
    if (event.target.checked) {
      this[list].find(rec => data.id === rec.id).checked = true;
      this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation.push({ "testId": data.id })
    } else {
      this[list].find(rec => data.id === rec.id).checked = false;

      for (let i = 0; i < this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation.length; i++) {
        if (this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation[i].tsetId === data.id) {
          this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation.splice(i, 1);
        }
      }
    }
  }

  search(ancNoSerch?) {
    let ancNo = this.searchForm.get('ancNo').value;
    this.clear();
    if (ancNo) {
      this.getVwAncDtls(ancNo);
    }
    else if (ancNoSerch) {
      this.getVwAncDtls(ancNoSerch);
    }
    else {
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Anc Number'
      });
    }
  }

  getDataByAncNo(ancNo) {
    if (this.newOrReg == "New") {
      Swal.fire({
        title: 'Confirmation',
        text: 'ANC Request Available. Want to download ANC details to register ?',
        icon: 'question',
        showCancelButton: true, // Display the Cancel button
      }).then((result) => {
        if (result.isConfirmed) {
          // The user clicked the OK button
          // get req by anc No 
          this.getAncReqByAncNo(ancNo);
        }
        this.searchForm.get('ancNo').patchValue('');
      });
    }
    if (this.newOrReg == "Registered") {
      //get reg by anc No 
      this.getAncRegisterByAncNo(ancNo);
      this.searchForm.get('ancNo').patchValue('');
    }
    this.newOrReg = "";
  }

  getVwAncDtls(ancSearchNum: any) {
    this._ancRegisterService.getVwAncDtlsByAncNo(ancSearchNum).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.newOrReg = res['result'].action;
      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Entered anc No", 'warning');
      } else {
        Swal.fire(' ', 'The entered Anc Number does not match any existing data. Please double-check and re-enter the correct AncNo.', 'warning')
      }
      this.getDataByAncNo(ancSearchNum);
    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });

  }

  filterObjectByModel(obj: any, model: any) {
    let m = new model()
    for (const key in obj) {
      Object.keys(m).forEach(key => {
        m[key] = obj[key] ? obj[key] : null;
      })
    }
    return m;
  }

  getAncReqByAncNo(ancSearchNum: any) {
    this._ancRegisterService.getRequestAncDtlsByAncNo(ancSearchNum).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        let data = res['result'];
        data.regInst = null;
        this.allAncData = { ...data.regInst, ...data.patientId };
        this.allAncData.rgTbPatientInfo = this.filterObjectByModel(data, TbPatientInfo);
        this.allAncData.rgTbAncRegister = this.allAncData.rgTbAncRegister ? this.allAncData.rgTbAncRegister : [];
        this.allAncData.rgTbAncRegister[0] = this.filterObjectByModel(data, AncRegisterModel);
        this.patientDetails.setPatientDetails(this.allAncData);
        // this.disableUnchangedFeild()
        this.getBookingForm(this.allAncData.rgTbAncRegister[0]);
        this.initialsMainList();
      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Entered anc No", 'warning');
      } else {
        Swal.fire(' ', 'The entered Anc Number does not match any existing data. Please double-check and re-enter the correct AncNo.', 'warning')
      }
    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  getAncReqByRequestId(reqId: any) {
    this._ancReqService.getAncDataByRequestId(reqId).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        let data = res['result'];
        data.regInst = null;
        this.allAncData = { ...data.regInst, ...data.patientId };
        this.allAncData.rgTbPatientInfo = this.filterObjectByModel(data.rgTbPatientInfo, TbPatientInfo);
        this.allAncData.rgTbAncRegister = this.allAncData.rgTbAncRegister ? this.allAncData.rgTbAncRegister : [];
        this.allAncData.rgTbAncRegister[0] = this.filterObjectByModel(data.rgTbAncRequestData, AncRegisterModel);
        this.patientDetails.setPatientDetails(this.allAncData);
        // this.disableUnchangedFeild();
        this.getBookingForm(this.allAncData.rgTbAncRegister[0]);
        this.initialsMainList();
      }
    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  getAncRegisterByAncNo(ancSearchNum: any) {
    this._ancRegisterService.getAncRegisterByAncNo(ancSearchNum).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.allAncData = res['result'];
        this.allAncData.rgTbAncRegister = this.allAncData.rgTbAncRegister.filter(el => el.ancNo === ancSearchNum);
        this.patientDetails.setPatientDetails(this.allAncData);
        // this.disableUnchangedFeild();
        this.getBookingForm(this.allAncData.rgTbAncRegister[0]);
        this.allAncData.rgTbAncRegister[0].rgTbAncVisits.forEach((el, index) => {
          el.index = index;
          this.ancVisitsList.push(el);
        })

        this.allAncData.rgTbAncRegister[0].rgTbPncVisits.forEach((el, index) => {
          el.index = index;
          this.pncVisitsList.push(el);
        })

        this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails.forEach((el, index) => {
          el.index = index;
          this.babydetalisList.push(el);
        })

        this.allAncData.rgTbAncRegister[0].rgTbAncImmunization.forEach((el, index) => {
          el.index = index;
          el.vaccinationDate = this.cellRenderer(el.vaccinationDate);
          this.ancImmList.push(el);
        })
        this.getDeliveryDtls(this.allAncData.rgTbAncRegister[0].rgTbAncDeliveryDtls[0]);

        this.initialsMainList();
      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Entered anc No", 'warning');

      } else {
        Swal.fire(' ', 'The entered Anc Number does not match any existing data. Please double-check and re-enter the correct AncNo.', 'warning')
      }

    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  getBookingForm(data) {
    // format dates
    data.lmp = this.cellRenderer(data.lmp);
    data.eddCalc = this.cellRenderer(data.eddCalc);
    data.eddScan = this.cellRenderer(data.eddScan);
    data.eddCorrected = this.cellRenderer(data.eddCorrected);
    data.lastDeliveryDate = this.cellRenderer(data.lastDeliveryDate);
    data.lastAbortionDate = this.cellRenderer(data.lastAbortionDate);

    // checkboxes
    data.congAnamoliesYn = data.congAnamoliesYn == "Y" ? true : false;
    data.contraMethodUsedYn = data.contraMethodUsedYn == "Y" ? true : false;
    data.riskFactorsYn = data.riskFactorsYn == "Y" ? true : false;
    data.abortionYn = data.abortionYn == "Y" ? true : false;

    // Bsp check box
    if (data.rgTbAncBspMethod && data.rgTbAncBspMethod.length > 0) {
      data.rgTbAncBspMethod.forEach(item1 => {
        this.ancBirthSpacing.forEach(item2 => {
          if (item1.methodUsed === item2.id) {
            item2.checked = true;
          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }

    // BOOKING LAB CHECK
    if (data.rgTbAncLabInvestigation && data.rgTbAncLabInvestigation.length > 0) {
      data.rgTbAncLabInvestigation.forEach(item1 => {
        this.ancLabInvestB.forEach(item2 => {
          if (item1.testId === item2.id) {
            item2.checked = true;
            item2.testResult = item1.testResult
            item2.remarks = item1.remarks
            item2.investId = item1.investId
          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }

    // BOOKING Medical CHECK
    if (data.rgTbAncMedicalHistory && data.rgTbAncMedicalHistory.length > 0) {
      data.rgTbAncMedicalHistory.forEach(item1 => {
        this.ancMidicalHistoB.forEach(item2 => {
          if (item1.diseaseId === item2.id) {
            item2.checked = true;
            item2.remarks = item1.remarks
            item2.medId = item1.medId
          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }

    // populate data to form
    this.bookingForm.patchValue(data);
  }

  getFollowUpVisit(data, indx) {
    this.clearFollowUp();
    data.index = indx;

    // format dates
    data.appDate = this.cellRenderer(data.appDate);

    // checkboxes
    data.refToNutrition = data.refToNutrition == "Y" || data.refToNutrition == true ? true : false;
    data.refToHealthEdu = data.refToHealthEdu == "Y" || data.refToHealthEdu == true ? true : false;
    data.refToObstetrician = data.refToObstetrician == "Y" || data.refToObstetrician == true ? true : false;
    data.refToPsychatrist = data.refToPsychatrist == "Y" || data.refToPsychatrist == true ? true : false;
    data.refToOtherDept = data.refToOtherDept == "Y" || data.refToOtherDept == true ? true : false;
    data.abortionYn = data.abortionYn == "Y" || data.abortionYn == true ? true : false;

    // FollowUp LAB CHECK
    if (data.rgTbAncLab && data.rgTbAncLab.length > 0) {
      data.rgTbAncLab.forEach(item1 => {
        this.ancLabInvestF.forEach(item2 => {
          if (item1.testId === item2.id) {
            item2.checked = true;
            item2.testResult = item1.testResult
            item2.remarks = item1.remarks
            item2.investId = item1.investId

          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }

    // FollowUp Medical CHECK
    if (data.rgTbMidical && data.rgTbMidical.length > 0) {
      data.rgTbMidical.forEach(item1 => {
        this.ancMidicalHistoF.forEach(item2 => {
          if (item1.diseaseId === item2.id) {
            item2.checked = true;
            item2.remarks = item1.remarks
            item2.medId = item1.medId
          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }
    this.followUpForm.patchValue(data)
    this.selectedFUDate = data.visitDate;
    this.selectedFollowUp = this.followUpForm.value;
  }

  getDeliveryDtls(data) {
    if (data) {
      // format dates
      data.deliveryDate = this.cellRenderer(data.deliveryDate);

      // checkboxs
      data.abortionYn = data.abortionYn == "Y" ? true : false;
      this.deliveryDetailForm.patchValue(data)

    }
  }

  getBabyDetailForm(data, indx) {

    this.clearBaby();
    // checkboxs
    data.conAnamolyYn = data.conAnamolyYn === "Y" || data.conAnamolyYn === true ? true : false;

    data.index = indx;
    this.babyDetailForm.patchValue(data)
    this.selectedBaby = this.babyDetailForm.value;
  }

  pncVisitDateSelect(value) {
    this.selectedPNCDate = value;
  }

  immunizationAddNewRow() {
    this.immunAddNewRow = true
  }

  AddNewImmun() {
    this.ancImmList.push({});
  }

  removeImmun(index: number) {
    this.ancImmList.splice(index, 1);
    this.allAncData.rgTbAncRegister[0].rgTbAncImmunization.splice(index, 1);
  }

  addEditImmun(data, indx) {
    this.ancImmList[indx] = data
    this.allAncData.rgTbAncRegister[0].rgTbAncImmunization[indx] = data;
    Swal.fire({
      icon: 'success',
      title: 'SAVED',
      text: 'Saved Successfully',
      showConfirmButton: false,
      timer: 1000,
    });

  }

  submitForm() {
    this.submitted = true;

    if (this.selectedFUDate) {
      this.addEditFollowUp(this.followUpForm.value);
    }
    if (this.selectedBaby && this.selectedBaby.babyFileNo) {
      this.addEditBaby(this.babyDetailForm.value);
    }
    if (this.selectedPNCDate) {
      this.addEditPncVisit(this.pncVisitForm.value);
    }

    if (this.validateFields()) {

      // booking 
      if (this.bookingForm.valid) {
        let booking = this.bookingForm.value;
        booking.lmp = this.cellRendererBack(booking.lmp);
        booking.eddCalc = this.cellRendererBack(booking.eddCalc);
        booking.eddScan = this.cellRendererBack(booking.eddScan);
        booking.eddCorrected = this.cellRendererBack(booking.eddCorrected);
        booking.lastDeliveryDate = this.cellRendererBack(booking.lastDeliveryDate);
        booking.lastAbortionDate = this.cellRendererBack(booking.lastAbortionDate);
        let details = _.cloneDeep(this.allAncData.rgTbAncRegister[0]);
        booking['congAnamoliesYn'] = this.checkboxYn(booking['congAnamoliesYn']);
        booking['contraMethodUsedYn'] = this.checkboxYn(booking['contraMethodUsedYn']);
        booking['abortionYn'] = this.checkboxYn(booking['abortionYn']);
        booking['riskFactorsYn'] = this.checkboxYn(booking['riskFactorsYn']);
        booking['rgTbAncBspMethod'] = details.rgTbAncBspMethod ? details.rgTbAncBspMethod : null;

        const isAtLestOnelabChecked = this.ancLabInvestB.some(item => item.checked === true);
        if (isAtLestOnelabChecked) {
          booking['rgTbAncLabInvestigation'] = new Array<AncLabInvestModel>();
          this.ancLabInvestB.forEach(e => {
            if (e.checked) {
              let lab = new AncLabInvestModel();
              lab.investId = e.investId ? e.investId : null;
              lab.testId = e.id;
              lab.testResult = e.testResult;
              lab.remarks = e.remarks;
              lab.visitType = 'b';
              booking['rgTbAncLabInvestigation'].push(lab);
            }
          });
        }
        const isAtLestOneMedChecked = this.ancMidicalHistoB.some(item => item.checked === true);
        if (isAtLestOneMedChecked) {
          booking['rgTbAncMedicalHistory'] = new Array<AncMidicalHistoryModel>();
          this.ancMidicalHistoB.forEach(e => {
            if (e.checked) {
              let med = new AncMidicalHistoryModel();
              med.medId = e.medId ? e.medId : null;
              med.diseaseId = e.id;
              med.remarks = e.remarks;
              med.visitType = 'b';
              booking['rgTbAncMedicalHistory'].push(med);
            }
          });
        }

        // followup
        if (details.rgTbAncVisits && details.rgTbAncVisits.length) {

          details.rgTbAncVisits.forEach(el => {
            el.refToNutrition = el.refToNutrition ? this.checkboxYn(el.refToNutrition) : null;
            el.refToHealthEdu = el.refToHealthEdu ? this.checkboxYn(el.refToHealthEdu) : null;
            el.refToObstetrician = el.refToObstetrician ? this.checkboxYn(el.refToObstetrician) : null;
            el.refToPsychatrist = el.refToPsychatrist ? this.checkboxYn(el.refToPsychatrist) : null;
            el.refToOtherDept = el.refToOtherDept ? this.checkboxYn(el.refToOtherDept) : null;
            el.abortionYn = el.abortionYn ? this.checkboxYn(el.abortionYn) : null;
            el.visitDate = this.cellRenderer(el.visitDate);
            el.visitDate = this.cellRendererBack(el.visitDate);
            el.appDate = this.cellRendererBack(el.appDate);
          })
          booking['rgTbAncVisits'] = details.rgTbAncVisits;
        }

        // delivery details
        if (this.deliveryDetailForm.valid) {
          details.rgTbAncDeliveryDtls = [];
          details.rgTbAncDeliveryDtls[0] = this.deliveryDetailForm.value;
          details.rgTbAncDeliveryDtls[0].abortionYn = details.rgTbAncDeliveryDtls[0].abortionYn ? this.checkboxYn(details.rgTbAncDeliveryDtls[0].abortionYn) : null;
          details.rgTbAncDeliveryDtls[0].deliveryDate = this.cellRendererBack(details.rgTbAncDeliveryDtls[0].deliveryDate);
          booking['rgTbAncDeliveryDtls'] = details.rgTbAncDeliveryDtls;
        }

        // immunization
        if (details.rgTbAncImmunization && details.rgTbAncImmunization.length) {
          details.rgTbAncImmunization.forEach(el => {
            el.vaccinationDate = this.cellRendererBack(el.vaccinationDate);
          })
          booking['rgTbAncImmunization'] = details.rgTbAncImmunization;
        }

        //baby details
        if (details.rgTbAncBabyDetails && details.rgTbAncBabyDetails.length) {
          details.rgTbAncBabyDetails.forEach(el => {
            el.conAnamolyYn = el.conAnamolyYn ? this.checkboxYn(el.conAnamolyYn) : null;
          })
          booking['rgTbAncBabyDetails'] = details.rgTbAncBabyDetails
        }

        // pnc visits
        if (details.rgTbPncVisits && details.rgTbPncVisits.length) {
          details.rgTbPncVisits.forEach(el => {
            el.breastFeeding = el.breastFeeding ? this.checkboxYn(el.breastFeeding) : null;
          })
          booking['rgTbPncVisits'] = details.rgTbPncVisits
        }

        this.allAncData.rgTbAncRegister[0] = booking;
      }

      // patient info
      if (this.patientForm.valid) {
        this.allAncData.rgTbPatientInfo = this.patientForm.value;
        this.allAncData.rgTbPatientInfo.createdBy = this.loginId;
        this.allAncData.rgTbPatientInfo.centralRegNo = this.allAncData.rgTbPatientInfo.centralRegNo ? this.allAncData.rgTbPatientInfo.centralRegNo : null;
        this.allAncData.rgTbPatientInfo.createdOn = this.allAncData.rgTbPatientInfo.createdOn ? this.allAncData.rgTbPatientInfo.createdOn : new Date();
      }

      // registry patient
      this.allAncData.createdBy = this.allAncData.createdBy ? this.allAncData.createdBy : this.loginId;
      this.allAncData.activeYn = "Y";
      this.allAncData.instRegDate = this.allAncData.instRegDate ? this.allAncData.instRegDate : new Date();
      this.allAncData.modifiedBy = this.loginId;
      this.allAncData.regInst = this.allAncData.rgTbPatientInfo.regInst;
      this.allAncData.patientID = this.allAncData.rgTbPatientInfo ? this.allAncData.rgTbPatientInfo.patientId : null;

      this._ancRegisterService.saveAncRegister(this.allAncData).subscribe(res => {
        if (res.code == AppUtils.RESPONSE_SUCCESS_CODE_SAVE) {
          Swal.fire({
            icon: 'success',
            title: 'SAVED',
            text: 'Registered Successfully',
            showConfirmButton: false,
            timer: 2000,
          });
          // this.newRegId = true;
        }
      }, error => {
        if (error.status == 401)
          Swal.fire('', 'Error occured while retrieving details', 'error')
      });

    } else {
      Swal.fire('Alert', 'Mandatory fields in' + this.errorMsg + 'cannot be empty', 'warning');
    }
  }

  onChicked(item, event) {
    if (event.target.checked === true) {
      item.checked = true;
    }
    else {
      item.checked = false;
      item.testResult = null;
      item.remarks = null;
    }
  }

  checkboxYn(x) {
    if (x == true || x == 'Y') {
      return "Y";
    }
    else {
      return 'N';
    }
  }

  clear() {
    this.allAncData = {};
    this.allAncData.rgTbAncRegister = [];
    this.ancImmList = []
    this.babydetalisList = []
    this.pncVisitsList = []
    this.ancVisitsList = []
    this.initialsForms();
    this.ancLabInvestB = _.cloneDeep(this.ancLabInvest);
    this.ancLabInvestF = _.cloneDeep(this.ancLabInvest);
    this.ancLabInvestP = _.cloneDeep(this.ancLabInvest);
    this.ancMidicalHistoB = _.cloneDeep(this.ancMidicalHisto);
    this.ancMidicalHistoF = _.cloneDeep(this.ancMidicalHisto);
  }

  clearAll() {
    this.clear();
    this.searchForm.reset();
    this.modalService.dismissAll(this.viewSmallANCWindow);
    // this.disableUnchangedFeild();
  }

  validateFields() {
    this.errorMsg = '';
    if (!this.bookingForm.valid || !this.deliveryDetailForm.valid || !this.patientForm.valid) {
      if (!this.bookingForm.valid) {
        this.errorMsg += ' (Booking) '

      }
      if (!this.deliveryDetailForm.valid) {
        this.errorMsg += ' (Delivery Details) '

      }
      if (!this.patientForm.valid) {
        this.errorMsg += ' (Patient Details) '
      }
      return false
    } else {
      return true
    }

  }

  autoGrowTextZone(e) {
    e.target.style.height = (e.target.scrollHeight) + "px";
  }

  addNewFollowUp(e) {
    this.ancVisitsList.push({ visitDate: e, visitId: null, ...e });
    this.allAncData.rgTbAncRegister[0].rgTbAncVisits.push({ visitDate: e, visitId: null, ...e })

    if (this.selectedFUDate) {
      this.addEditFollowUp(this.followUpForm.value);
    }
  }

  removeFollowUp(index: number) {
    if (this.followUpClickedAdd) {
      this.ancVisitsList.splice(index, 1);
      this.allAncData.rgTbAncRegister[0].rgTbAncVisits.splice(index, 1);
      this.followUpForm.reset();
      // this.selectedFUDate = null;
      if (this.ancVisitsList && this.ancVisitsList.length) {
        this.getFollowUpVisit(this.ancVisitsList[this.ancVisitsList.length - 1], this.ancVisitsList.length - 1);
      }
      else {
        this.selectedFUDate = null;
      }
    } else {
      Swal.fire('unsaved changes', 'You Must Click Add/Edit button to save current FollowUp!', 'warning');
    }

  }

  addEditFollowUp(data) {
    if (this.followUpForm.valid) {
      this.ancVisitsList[data.index] = data
      const isAtLestOnelabChecked = this.ancLabInvestF.some(item => item.checked === true);
      if (isAtLestOnelabChecked) {
        this.ancVisitsList[data.index].rgTbAncLab = new Array<AncLabInvestModel>();
        this.ancLabInvestF.forEach(e => {
          if (e.checked) {
            let lab = new AncLabInvestModel();
            lab.investId = e.investId ? e.investId : null;
            lab.testId = e.id;
            lab.testResult = e.testResult;
            lab.remarks = e.remarks;
            lab.visitType = 'a';
            this.ancVisitsList[data.index].rgTbAncLab.push(lab);
          }
        });
      }
      const isAtLestOneMedChecked = this.ancMidicalHistoF.some(item => item.checked === true);
      if (isAtLestOneMedChecked) {
        this.ancVisitsList[data.index].rgTbMidical = new Array<AncMidicalHistoryModel>();
        this.ancMidicalHistoF.forEach(e => {
          if (e.checked) {
            let med = new AncMidicalHistoryModel();
            med.medId = e.medId ? e.medId : null;
            med.diseaseId = e.id;
            med.remarks = e.remarks;
            med.visitType = 'a';
            this.ancVisitsList[data.index].rgTbMidical.push(med);
          }
        });
      }
      this.allAncData.rgTbAncRegister[0].rgTbAncVisits[data.index] = this.ancVisitsList[data.index];
      this.selectedFollowUp = this.ancVisitsList[data.index];
    } else {

      if (!this.unsavedFollowupDtls) {
        Swal.fire('Alert', 'Mandatory fields in Follow Up cannot be empty', 'warning');
        this.removeFollowUp(this.ancVisitsList.length - 1)
      }
    }
  }

  clearFollowUp() {
    // this.initialsFollowUpForm(); 

    this.followUpForm.controls['typeTest'].disable();
    this.ancLabInvestF = _.cloneDeep(this.ancLabInvest);
    this.ancMidicalHistoF = _.cloneDeep(this.ancMidicalHisto);
  }

  showInputFeild() {
    this.modalService.open(this.popupModal, { size: 'sm', windowClass: 'mt-10' });
  }

  addNewBaby(e) {
    this.babydetalisList.push({ babyFileNo: e.target.value, babyId: null, ...e });
    this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails.push({ babyFileNo: e.target.value, babyId: null, ...e })
    this.modalService.dismissAll(this.popupModal);

    if (this.selectedBaby && this.selectedBaby.babyFileNo) {
      this.addEditBaby(this.babyDetailForm.value);
    }
  }

  addEditBaby(data) {
    if (this.babyDetailForm.valid) {
      let index = this.babydetalisList.findIndex((item) => item.babyFileNo === data.babyFileNo);
      this.babydetalisList[index] = data
      this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails[data.index] = this.babydetalisList[index];
      this.selectedBaby = this.babydetalisList[index];

    } else {
      Swal.fire('Alert', 'Mandatory fields in Baby Details cannot be empty', 'warning');
      this.removeBaby(this.babydetalisList.length - 1);
    }
  }


  unsavedBabyDtls() {

    if (this.areObjectsEqual(this.selectedBaby, this.babyDetailForm.value)) {
      return true;
    } else {
      return false;
    }
  }


  unsavedFollowupDtls() {
    if (this.areObjectsEqual(this.selectedFollowUp, this.followUpForm.value)) {
      return true;
    } else {
      return false;
    }
  }

  unsavedPncVisitDtls() {

    if (this.areObjectsEqual(this.selectedPncVisit, this.pncVisitForm.value)) {
      return true;
    } else {
      return false;
    }
  }

  removeBaby(index: number) {
    this.babydetalisList.splice(index, 1);
    this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails.splice(index, 1);

    if (this.babydetalisList && this.babydetalisList.length) {
      this.getBabyDetailForm(this.babydetalisList[this.babydetalisList.length - 1], this.babydetalisList.length - 1);
    }
    else {
      this.selectedBaby.babyFileNo = null;
    }
  }

  clearBaby() {
    this.initialsBabyDetailForm();
  }

  onInput(item, event) {
    const inputElement = event.target as HTMLInputElement;

    if (item.checked === true) {
      item[inputElement.id] = inputElement.value;
    }
  }

  addNewPncVisit(e) {
    this.pncVisitsList.push({ visitDate: e, pncId: null, ...e });
    this.allAncData.rgTbAncRegister[0].rgTbPncVisits.push({ visitDate: e, pncId: null, ...e })

    if (this.selectedPNCDate) {
      this.addEditPncVisit(this.pncVisitForm.value);
    }
  }

  getPncVisit(data, indx) {
    this.clearPnc();
    data.index = indx;

    // checkboxes
    data.breastFeeding = data.breastFeeding == "Y" || data.breastFeeding == true ? true : false;

    // PNC LAB CHECK
    if (data.rgTbPncLab && data.rgTbPncLab.length > 0) {
      data.rgTbPncLab.forEach(item1 => {
        this.ancLabInvestP.forEach(item2 => {
          if (item1.testId === item2.id) {
            item2.checked = true;
            item2.testResult = item1.testResult
            item2.remarks = item1.remarks
            item2.investId = item1.investId
          } else {
            if (item2.checked == null) {
              item2.checked = false;
            }
          }
        })
      })
    }
    this.pncVisitForm.patchValue(data)
    this.selectedPNCDate = data.visitDate;
    this.selectedPncVisit = this.pncVisitForm.value;
  }

  clearPnc() {
    this.pncVisitForm.reset();
    this.ancLabInvestP = _.cloneDeep(this.ancLabInvest);
  }

  removePnc(index: number) {
    this.pncVisitsList.splice(index, 1);
    this.allAncData.rgTbAncRegister[0].rgTbPncVisits.splice(index, 1);
  }

  addEditPncVisit(data) {
    this.PncVisitClickedAdd = true;
    const isPncIdInList = this.pncVisitsList.some(item => item.pncId === data.pncId);
    if (this.pncVisitForm.valid) {
      this.pncVisitsList[data.index] = data

      this.ancLabInvestP.forEach(e => {
        if (e.checked) {
          this.pncVisitsList[data.index].rgTbPncLab = new Array<AncLabInvestModel>();
          let lab = new AncLabInvestModel();
          lab.investId = e.investId ? e.investId : null;
          lab.testId = e.id;
          lab.testResult = e.testResult;
          lab.remarks = e.remarks;
          lab.visitType = 'p';
          this.pncVisitsList[data.index].rgTbPncLab.push(lab);
        }

      });
      this.pncVisitsList[data.index].breastFeeding = this.checkboxYn(this.pncVisitsList[data.index].breastFeeding);
      this.allAncData.rgTbAncRegister[0].rgTbPncVisits[data.index] = this.pncVisitsList[data.index];
      this.selectedPncVisit = this.pncVisitsList[data.index];
    } else {
      Swal.fire('Alert', 'Mandatory fields in PNC Visit cannot be empty', 'warning');
      this.removePnc(this.pncVisitsList.length - 1);

    }
  }

  clearInputFelid(event, validtion, targetValidation) {
    if (validtion.value === "N" || validtion.value === false || validtion.value === 'B' || validtion.value === 'H') {
      targetValidation.reset()
    }
  } clearInputFelid1(event, validtiont, targetValidation) {
    if (validtiont.value === true) {
      this.followUpForm.controls['typeTest'].enable();
    } else { this.followUpForm.controls['typeTest'].disable(); }
  }

  clearInputFelid2(event, validtiont, targetValidation) {
    if (validtiont.value === true) {
      this.deliveryDetailForm.controls['typeOfBortion'].enable();
    } else { this.deliveryDetailForm.controls['typeOfBortion'].disable(); }
  }

  areObjectsEqual(obj1, obj2) {
    // Check if both inputs are null or undefined
    if (obj1 === null && obj2 === null) {
      return true;
    }

    // Check if one of the objects is null or undefined, but not both
    if (obj1 === null || obj2 === null || obj1 === undefined || obj2 === undefined) {
      return false;
    }

    // Check if both inputs are objects
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return obj1 === obj2;
    }

    // Check if both objects have the same set of keys
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    // Check if the values of each key are equal
    for (const key of keys1) {
      if (!this.areObjectsEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  }

  callMpiMethod() {
    this.getdata('civilId', '', this.patientDetails.patientForm.value.civilId);
  }


  //get patient date (by RegNo or CivilID)
  getdata(searchby: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }



    }

    this._ancRegisterService.getAncRegisterByCivilId(civilId).subscribe(async res => {
      this.ancRegList = res['result'];
      if (res['code'] == 'S0000') {
        this.openSmallWindow(civilId);
      } else {
        await Swal.fire(
          '', msg, 'warning',
        ).then((result) => {
          if (callMPI == true) {
            this._sharedService.setPatientData(this.patientDetails);
            // this.disableUnchangedFeild();
            this._sharedService.fetchMpi();
          }
        })
      }

    })






  }

  async newReg() {
    this._ancRegisterService.getRegister(this.ancRegList[0].centralRegNo, AppUtils.REG_TYPE_ANC.toString(), this.ancRegList[0].civilId).subscribe(async res => {

      if (res['code'] == 'S0000') {
        // get data
        this.centralRegNoExit = true;
        if (res['result']) {
          this.ancData = res['result'];
          this.newRegId = false;
          this.allAncData = this.ancData ? _.cloneDeep(this.ancData) : this.allAncData;
          this.allAncData.rgTbAncRegister = [];
          this.allAncData.rgTbAncRegister.push({});
          this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails = [];
          this.allAncData.rgTbAncRegister[0].rgTbAncDeliveryDtls = [];
          this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation = [];
          this.allAncData.rgTbAncRegister[0].rgTbAncMedicalHistory = [];
          this.allAncData.rgTbAncRegister[0].rgTbAncVisits = [];
          this.allAncData.rgTbAncRegister[0].rgTbPncVisits = [];
          this.patientDetails.setPatientDetails(this.allAncData);
          // this.disableUnchangedFeild();
          this.modalService.dismissAll(this.viewSmallANCWindow);
          this.ancData = [];
        }
      }
      if (res['code'] == '3') {
        this._ancReqService.getAncDataByAncNo(this.ancRegList[0].ancNo).subscribe(async res => {
          if (res['result']) {
            this.ancData = res['result'];
            this.newRegId = false;
            this.allAncData = this.ancData ? _.cloneDeep(this.ancData) : this.allAncData;
            this.allAncData.rgTbAncRegister = [];
            this.allAncData.rgTbAncRegister.push({});
            this.allAncData.rgTbAncRegister[0].rgTbAncBabyDetails = [];
            this.allAncData.rgTbAncRegister[0].rgTbAncDeliveryDtls = [];
            this.allAncData.rgTbAncRegister[0].rgTbAncLabInvestigation = [];
            this.allAncData.rgTbAncRegister[0].rgTbAncMedicalHistory = [];
            this.allAncData.rgTbAncRegister[0].rgTbAncVisits = [];
            this.allAncData.rgTbAncRegister[0].rgTbPncVisits = [];
            this.patientDetails.setPatientDetails(this.allAncData);
            // this.disableUnchangedFeild();
            this.modalService.dismissAll(this.viewSmallANCWindow);
            this.ancData = [];
          }

        });
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('', 'Error occured while retrieving user details', 'error')
    })
  }

  // disableUnchangedFeild(){
  //   this.patientForm.get('civilId')? this.patientForm.get('civilId').disable(): this.patientForm.get('civilId').enable();
  //   this.patientForm.get('dob')? this.patientForm.get('dob').disable(): this.patientForm.get('dob').enable();

  // }
}


