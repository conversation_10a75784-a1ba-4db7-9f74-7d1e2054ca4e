import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';
import { GeneticDisorderRegistry } from '../_models/genetic-disorder-registry.model';
import { SharedService } from '../_services/shared.service';

@Injectable({
  providedIn: 'root'
})

export class GeneticService {
  constructor(private _http: HttpClient , private _sharedService: SharedService) { }

  getGeneticListing(data): Observable<any> {
    return this._http.post(AppUtils.SEARCH_GENETIC, data);
  }
  ///// (data : GeneticDisorderRegistry)
  saveGenetic(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_GENETIC_REGISTRY, data);
  }

  // GET_GENETIC_REGISTRY
  getGeneticRegister(centralRegNo): Observable<any> {
    return this._http.get(AppUtils.GET_GENETIC_REGISTRY, {
      params: new HttpParams().set("centralRegNo", centralRegNo).set("regType", AppUtils.REG_TYPE_GENETIC_BLOOD.toString())
    })
  }

  // CHECK_GENETIC_REGISTRY  
  checkGeneticRegister(estCode: any, patientId: any, regType: any): Observable<any> {
    return this._http.get(AppUtils.CHECK_GENETIC_REGISTRY + "?patientID=" + patientId + "&regType=" + regType + "&estCode=" + estCode);

  }

  //fetch-from-alshifa?estCode=20068&patientId=112603
  fetchGeneticRegisterFromShifa(estCode: any, patientId: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_GENETIC_REGISTRY_FROM_ALSHIFA + "?estCode=" + estCode + "&patientId=" + patientId);

  }

    //fetch-all-hplc-alshifa?estCode=20068&patientId=112603
    fetchAllHplcFromShifa(estCode: any, patientId: any): Observable<any> {
      return this._http.get(AppUtils.FETCH_ALL_HPLC_FROM_ALSHIFA + "?estCode=" + estCode + "&patientId=" + patientId);
  
    }


  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.GENETIC_DASHBOARD);
  }




  //Genetic Master Data
  getGeneticComplicationsFrequency(): Observable<any> {
    return this._http.get(AppUtils.GET_GEN_COMPLICTION_FREQ);
  }

  getGeneticVaccineList():Observable<any>{
    return this._http.get(AppUtils.GET_GEN_VACCINE_LIST);
  }

  getGeneticICDList():Observable<any>{
    return this._http.get(AppUtils.GET_GEN_ICD_LIST);
  }

  getGeneticMedicineList():Observable<any>{
    return this._http.get(AppUtils.GET_GEN_MEDICINE_LIST);
  }


  //Genetic Blood Excel

  getExportToGeneticBloodExcel(compParam: any): Observable<any> {
    let httpOptions: any = {
      headers: new HttpHeaders({}),
    };
    httpOptions.responseType = 'blob';
    return this._http.post(AppUtils.GENETIC_GENETICBLOODEXCEL, JSON.stringify(compParam), httpOptions);
    // return this.http.post(AppUtils.ELDERLY_EXPORTEXCEL, JSON.stringify(compParam), { responseType: 'blob' });

  }



}