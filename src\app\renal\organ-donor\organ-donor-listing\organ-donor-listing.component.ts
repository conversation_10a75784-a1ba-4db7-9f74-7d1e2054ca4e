import { OrganDonorModel } from '../../../common/objectModels/organDonor-model';
import { DATE_FORMAT_CUSTOM_UPDATE, GET_USER_BY_PERSCODE } from '../../../_helpers/common.constants';
import { empty } from 'rxjs';
import { MasterService } from '../../../_services/master.service';
import { DashboardAgeModel } from '../../../dashboard/dashboard-age-model';
// import { Response } from '@angular/http';
import { formatDate } from '@angular/common';
import { OrganDonorService } from '../organ-donors.service';
import { Component, OnInit, ViewChild, Pipe, PipeTransform} from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { GET_ORGAN_DONORS } from '../../../common/app.utils';
import * as AppUtils from '../../../common/app.utils';
import { GridOptions, _ } from "ag-grid-community";
import * as AppCompUtils from '../../../common/app.component-utils';
import { DatePipe } from '@angular/common'
import Swal from 'sweetalert2';
import { ButtonOrganDonorRendererComponent } from 'src/app/common/agGridComponents/ButtonOrganDonorRendererComponent';
import { OrganDonorExcel } from 'src/app/_models/organ-donor-excel.model';
import { SharedService } from 'src/app/_services/shared.service';
import { Paginator } from 'primeng/paginator';

import { Data } from '@syncfusion/ej2-angular-grids';
import * as moment from 'moment';

@Component({
  selector: 'app-organ-donor-listing',
  templateUrl: './organ-donor-listing.component.html',
  styleUrls: ['./organ-donor-listing.component.scss'],
  
})


export class OrganDonorListingComponent implements OnInit{

  @ViewChild('organRegPaginator', { static: false }) paginator: Paginator;
  organDonorSearch: FormGroup;
  thisYear = (new Date()).getFullYear();
  startDate = new Date("1/1/" + this.thisYear);
  defaultFormattedDate = this.datepipe.transform(this.startDate, 'dd-MM-yyyy');
  totalRecords:any;
  paginationSize:any;
  organDonorsList: OrganDonorModel[] = [];
  donorList: Array<OrganDonorExcel> = new Array<OrganDonorExcel>();
  public rowData: any[]=[];
  private gridApi: any;
  nationalityList: any[];
  datePipeString: string;
  frameworkComponents;
  organStatus = AppCompUtils.ORGAN_STATUS;
  formCompleted = AppCompUtils.FILE_STATUS;
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
      pagination: false,
      resizable: true,
     paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
     onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
    frameworkComponents : {
      buttonRenderer: ButtonOrganDonorRendererComponent,

    }
    };


    cellRenderer = (data) => {
      if (data.value) {
        const formattedDate = moment(data.value).format('DD-MM-yyyy');
        return formattedDate;
      } else {
        return '';
      }
    };

    cellRendererNationality = (data) => {
      if (data.value) {
        let NationalityName = ''
        this.nationalityList.forEach((item) => {
          if (item.natCode == data.value){
            NationalityName = item.nationality
            
          }
          
        });
        return NationalityName;
      } else {
        return '';
      }
    };

    cellRendererSattus = (data) => {
      if (data.value) {
        let statusName = ''
        this.formCompleted.forEach((item) => {
          if (item.id == data.value){
            statusName = item.value
            
          }
          
        });
        return statusName;
      } else {
        return '';
      }
    };

  

    cellOrganSattus = (data) => {
      if (data.value) {
          const foundItem = this.organStatus.find(item => item.id === data.value);
          return foundItem ? foundItem.value : '';
      } else {
          return '';
      }
  };

    

    cellRendererSend = (data) => {
      if (data.value) {
        const formattedDate = moment(data.value).format('M/D/YYYY h:mm:ss A');
        return formattedDate;
      } else {
        return '';
      }
    };

  excelCriteria: any={};
  columnDefs = [
    { headerName: 'Registered Date', field: 'createdTime', cellRenderer: this.cellRenderer,},
    { headerName: 'Civil ID', field: 'civilId' },
    {
      headerName: 'Name', field: 'fullName', resizable: true, valueGetter: function (params: any) {
        if (params.data.thirdName == null) {
          params.data.thirdName = " ";
        }
        if (params.data.tribe == null) {
          params.data.tribe = " ";
        }
        return params.data.firstName + ' ' + params.data.secondName + ' ' + params.data.thirdName + ' ' + params.data.tribe;
      }
    },
    { headerName: 'Age', field: 'age' },
    { headerName: 'Nationality', field: 'nationality', resizable: true, cellRenderer: this.cellRendererNationality},
    { headerName: 'Mobile No', field: 'mobileNo' },
    {
      headerName: 'Organ', field: 'organ', resizable: true, valueGetter: function (params: any) {
        if (params.data.lungsYn == 'Y') {
          var donatedOrgan = 'Lungs'
        }
        if (params.data.liverYn == 'Y') {
          if (donatedOrgan != null) {
            var donatedOrgan = donatedOrgan + ', Liver';
          }
        }
        if (params.data.heartYn == 'Y') {
          if (donatedOrgan != null) {
            var donatedOrgan = donatedOrgan + ', Heart';
          }
        }
        if (params.data.kidneysYn == 'Y') {
          if (donatedOrgan != null) {
            var donatedOrgan = donatedOrgan + ', Kidneys';
          }
        }
        if (params.data.corneasYn == 'Y') {
          if (donatedOrgan != null) {
            var donatedOrgan = donatedOrgan + ', Corneas';
          }
        }
        if (params.data.pancreasYn == 'Y') {
          if (donatedOrgan != null) {
            var donatedOrgan = donatedOrgan + ', Pancreas';
          }
        }
        if (donatedOrgan == null) {
          donatedOrgan = " - ";
        }
        return donatedOrgan;
      }
    },
    { headerName: 'Email', field: 'email' },
    { headerName: 'Family Contact No', field: 'relationContactNo' },
    { headerName: 'Status', field: 'fileStatus', cellRenderer: this.cellRendererSattus},
    { headerName: 'Organ Status', field: 'afterDeathYn', cellRenderer: this.cellOrganSattus},
    {
      headerName: 'PDF',
      cellRenderer: 'buttonRenderer',
      cellRendererParams: {
        onClick: this.generatePDFCivilId.bind(this),
        // label: 'PDF'
      }
    },

  ];
  data: any;
  currentPageData: any;
  runOnlyOnceInEvent: boolean = false;

  constructor(fb: FormBuilder, private _organDonorService: OrganDonorService,private _sharedService: SharedService, private _masterService: MasterService, public datepipe: DatePipe) {
    this.organDonorSearch = fb.group({
      //organDonorsListt: fb.array([]),
      civilId: new FormControl(null, Validators.required),
      fullName: new FormControl(null, Validators.required),
      age: new FormControl(null),
      ageFrom: new FormControl(null),
      ageTo: new FormControl(null),
      secondName: new FormControl(null),
      firstName: new FormControl(null),
      thirdName: new FormControl(null),
      tribe: new FormControl(null),
      sex: new FormControl(null, Validators.required),
      dobFrom: new FormControl(null, Validators.required),
      dobTo: new FormControl(null, Validators.required),
      nationality: new FormControl(null, Validators.required),
      mobileNo: new FormControl(null),
      email: new FormControl(null, Validators.required),
      relationContactNo: new FormControl(null, Validators.required),
      createdTimeTo: new FormControl(null, Validators.required),
      createdTimeFrom: new FormControl(null, Validators.required),
      organ: new FormControl(null, Validators.required),
      fileStatus:new FormControl(null, Validators.required),
      afterDeathYn:new FormControl(null, Validators.required),
    })

    // this.frameworkComponents = {
    //   buttonRenderer: ButtonOrganDonorRendererComponent,

    // };
  };






  ngOnInit(): void {
    this.getNationalityList();
  }


 
  
  


  public onGridReady(params: any) {
    this.gridApi = params.api;
  }

  setOneDayAfterSelectedDate(lst, date) {
    const selectedDateValue = lst[date];
    const nextDay = new Date(selectedDateValue);
    nextDay.setDate(nextDay.getDate() + 1);
    lst[date] = nextDay;
  }

  getOrganDonorsList(event?:any) {

    let source = this.organDonorSearch.value;
    let pageable = {
        page: event?event.page:0,
        size: event?event.rows:AppUtils.E_REGISTRY_PAGINATION_SIZE
      }

    if (!this.runOnlyOnceInEvent){
      this.setOneDayAfterSelectedDate(source,'createdTimeTo');
      this.runOnlyOnceInEvent = true;
    }

    
    source = {pageable, ...source}




    this._organDonorService.getOrganListing(source).subscribe(data => {
      this.data = data
      this.rowData = data.result['content'];
      this.totalRecords = data.result['totalElements'];
      //this.paginationSize = data.result['size'];
      

       },error => {
        if (error.status == 401)
          Swal.fire('Error!', 'Error occured while retrieving Organ Donor details', 'error')
      })
       ; 
  }



        
  generatePDFCivilId(e) {

    this._organDonorService.generatePDFCivilId(e.rowData.civilId).subscribe(res => {
      if (res['code'] == "S0000") {
        if (res['result'].organFile) {
          this.downloadPdf(res['result'].organFile.sourceString, res['result'].organFile.fileName);
        }
      } else {
        Swal.fire('Error!', 'An error occurred while generating PDF file!', 'error');
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'An error occurred while generating PDF file!', 'error')
    })

  }

  generatePDF(e) {
   
    let le = e.rowData;
    let name = le.firstName + " " + le.secondName + " " + le.thirdName + " " + le.tribe;
    let data = {
      "donorName": name,
      "civilId": le.civilId,
      "firstWitnessName": le.witness1Name,
      "firstWitnessCivilId": le.witness1CivilID,
      "secondWitnessName": le.witness2Name,
      "secondWitnessCivilId": le.witness2CivilID,
      "familyMemberName": le.familyMemberName,
      "familyMemberCivilId": le.reltiveCivilId,
      "relation": le.relationName,
      "phone": le.mobileNo,
      "kidneysYn": le.kidneysYn,
      "liverYn": le.liverYn,
      "heartYn": le.heartYn,
      "lungsYn": le.lungsYn,
      "pancreasYn": le.pancreasYn,
      "corneasYn": le.corneasYn,
      "language": "ar"
    }

    let tmpData = {
      "donorName": "marwan ahmad ali swaleh alshariyni",
      "civilId": 4178278,
      "firstWitnessName": "alazhar obaid alhabsi",
      "firstWitnessCivilId": 14668999,
      "secondWitnessName": "xxxx xxxx xxx xxx",
      "secondWitnessCivilId": 32977215,
      "familyMemberName": "ahmad ali swaleh alshariyni",
      "familyMemberCivilId": 12345678,
      "relation": "father",
      "phone": 98765423,
      "kidneysYn": "Y",
      "liverYn": "Y",
      "heartYn": "Y",
      "lungsYn": "Y",
      "pancreasYn": "Y",
      "corneasYn": "Y",
      "language": "ar"
    }

    this._organDonorService.generatePDF(data).subscribe(res => {
      if (res['code'] == "S0000") {
        this.downloadPdf(res.result.imageString, "OrganFile" + le.civilId);

      } else {
        Swal.fire('Error!', 'An error occurred while generating PDF file!', 'error');
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'An error occurred while generating PDF file!', 'error')
    })

  }

  downloadPdf(base64String, fileName) {
    const source = `data:application/pdf;base64,${base64String}`;
    const link = document.createElement("a");
    link.href = source;
    link.download = `${fileName}.pdf`
    link.click();
  }
  onClickDownloadPdf() {
    let base64String = "your-base64-string";
    this.downloadPdf(base64String, "sample");
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationalityList = response.result;
      //this.nationListFilter = this.nationList;

    }, error => {

    });
  }

  getDefaultDate() {
    let thisYear = (new Date()).getFullYear();
    let startDate = new Date("1/1/" + thisYear);
    let defaultFormattedDate = this.datepipe.transform(startDate, 'dd-MM-yyyy');
    this.organDonorSearch.controls['createdTimeFrom'].patchValue(defaultFormattedDate);

  }

  onSubmit() {
    this.getOrganDonorsList();

  }


  clear() {
    this.rowData = [];
    // this.exportToExcel =null;
    this.excelCriteria = null;
    this.donorList = null;
    this.organDonorSearch.reset();

  }

  formatData(rowData){
    this.donorList=[];
    rowData.forEach(el => {
      let Name =  el.firstName +' ' +el.secondName+' ' +el.thirdName +' '+ el.tribe;

      if (el.lungsYn == 'Y') {
        var donatedOrgan = 'Lungs'
      }
      if (el.liverYn == 'Y') {
        if (donatedOrgan != null) {
          var donatedOrgan = donatedOrgan + ', Liver';
        }
      }
      if (el.heartYn == 'Y') {
        if (donatedOrgan != null) {
          var donatedOrgan = donatedOrgan + ', Heart';
        }
      }
      if (el.kidneysYn == 'Y') {
        if (donatedOrgan != null) {
          var donatedOrgan = donatedOrgan + ', Kidneys';
        }
      }
      if (el.corneasYn == 'Y') {
        if (donatedOrgan != null) {
          var donatedOrgan = donatedOrgan + ', Corneas';
        }
      }
      if (el.pancreasYn == 'Y') {
        if (donatedOrgan != null) {
          var donatedOrgan = donatedOrgan + ', Pancreas';
        }
      }
      if (donatedOrgan == null) {
        donatedOrgan = " - ";
      }

  
  
      this.donorList.push({createdTime:el.createdTime,
        civilId: el.civilId,fullName:Name,
       age: el.age , nationality:el.nationality,mobileNo:el.mobileNo,organ:donatedOrgan,Email:el.email,relationContactNo:el.relationContactNo,afterDeathYn:el.afterDeathYn})
    })

    this._sharedService.exportAsExcelFile(this.donorList, "OrganDonor_Listing");
  }

 
  exportToExcel(event?:any) {
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
          this.formatData(this.rowData);
      } else {
 
        let source = this.organDonorSearch.value;
    
      let pageable = {
          page: event?event.page:0,
          size: event?event.rows: this.totalRecords,
        }
 
      source = {pageable, ...source}
        
        this._organDonorService.getOrganListing(source).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['content'];
            if (excelData && excelData.length > 0) {
              this.formatData(excelData);
            }
          }
        });
      }
    } else {
        Swal.fire('Warning!', 'Please search first', 'warning')

    }

    
  }

}







