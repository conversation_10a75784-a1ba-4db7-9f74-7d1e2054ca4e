import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as AppUtils from '../../common/app.utils';

@Injectable({
    providedIn: 'root'
})
export class CaseDetailService {
    // private apiUrl = 'http://localhost:3000/';

    constructor(private http: HttpClient) { }

    getLiverTransIndications(): Observable<any> {
        return this.http.get(AppUtils.GET_LIVER_TRANS_IND_MAST);
        //return this.http.get(AppUtils.GET_CASE_DETAILS_lIVER + 'liverTransIndications');
    }

    getLiverCirrhosisStages(): Observable<any> {
        //return this.http.get(AppUtils.GET_LIVER_TRANS_IND_MAST);
        return this.http.get(AppUtils.GET_CASE_DETAILS_lIVER + 'liverCirrhosisStages');
    }

    getLiverCirrhosisComplications(): Observable<any> {
       return this.http.get(AppUtils.GET_LIVER_COMPLICATION_MAST);
        //return this.http.get(AppUtils.GET_CASE_DETAILS_lIVER + 'liverCirrhosisComplications');
    }

    getCurrentManagement(): Observable<any> {
       // return this.http.get(AppUtils.GET_LIVER_CURR_MGMT_MAST);
        return this.http.get(AppUtils.GET_CASE_DETAILS_lIVER + 'currentManagement');
    }


    fetchAllDonorProcedureFromShifa(estCode: any, civilId: any): Observable<any> {
        return this.http.get(
          AppUtils.FETCH_ALL_LIVER_DONOR_PROCEDURE_FROM_ALSHIFA +
            "?estCode=" +
            estCode +
            "&civilId=" +
            civilId
        );
      }






}
