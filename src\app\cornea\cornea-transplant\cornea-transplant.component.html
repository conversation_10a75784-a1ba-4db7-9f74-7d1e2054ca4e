<div class="row">
  <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="page-title">Cornea Transplant Details</h6>
  </div>
  <div class="col-lg-3 col-md-3 col-sm-3"></div>
  <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px;">
    <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
      (keyup.enter)="search()" />
    <span class="input-group-btn">
      <button class="btn btn-default btn-sm" id="search" (click)="search()">
        <i class="fa fa-search"></i>
      </button>
    </span>
  </div>
</div>

<div class="accordion register">
  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
    <ngb-panel id="patientDetails" id="ngb-panel-0">
      <ng-template ngbPanelHeader let-opened="opened">
        <div class="d-flex align-items-center justify-content-between card-head"
          [ngClass]="opened ? 'opened' : 'collapsed'">
          <h6> Patient Details</h6>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <app-patient-details #patientDetails [submitted]="submitted" (callMethod)="callMpiMethod()">
        </app-patient-details>
      </ng-template>
    </ngb-panel>
  </ngb-accordion>
</div>

<ngb-accordion #acc="ngbAccordion" class="head-tab" activeIds="ngb-panel-0">
  <ngb-panel id="ngb-panel-0">
    <ng-template ngbPanelHeader let-opened="opened">
      <div class="d-none"> </div>
    </ng-template>
    <ng-template ngbPanelContent>
      <div class="tab-view">
        <form [formGroup]="transplantForm">
          <div class="d-flex justify-content-end position-relative">
            <button type="button" class="position-absolute btn btn-primary add-btn pull-right"
              (click)="addTransplant()">Add</button>
          </div>
          <tabset>

            <tab heading="Cornea Transplant Details">
              <div class="mcard">
                <div>
                  <div class="row transplant-section">
                    <fieldset class="col-md-6 left-transplant">
                      <table>
                        <tr>
                          <td class="py-3">Transplant Institute:<span class="mdtr"> * </span></td>
                          <td class="pl-3">
                            <ng-select [items]="instituteListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="estName" bindValue="estCode" formControlName="tranInst">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName
                                }}</ng-template>
                            </ng-select>
                          </td>
                          <td class="py-3 pl-4">Transplant Date:<span class="mdtr"> * </span></td>
                          <td class="pl-3">
                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="tranDate"
                              [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                              yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Date of Tissue Arrival:</td>
                          <td class="pl-3">
                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                              formControlName="tissueArrDate" [ngModelOptions]="{standalone: true}"
                              monthNavigator="true" [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                              showButtonBar="true"></p-calendar>
                          </td>
                          <td class="py-3 pl-4">Tissue No: </td>
                          <td class="pl-3">
                            <input type="text" class="form-control form-control-sm" placeholder="Enter a text"
                              formControlName="tissueNo" />
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Surgery Type:</td>
                          <td class="pl-3">
                            <ng-select [items]="surgeryTypeListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" bindValue="paramId" formControlName="surgeryType">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                                }}</ng-template>
                            </ng-select>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Surgeon Name:</td>
                          <td class="pl-3">
                            <ng-multiselect-dropdown #surgeonDropdown [placeholder]="'Select'"
                              [data]="surgeonNamesListFilter"
                              [formControl]="transplantForm?.controls['rgTbCorSurgeonDtls']"
                              [settings]="surDropdownSettings" [(ngModel)]="selectedSurgeons">
                            </ng-multiselect-dropdown>

                          </td>
                        </tr>
                        <tr>
                          <td class="pb-5">Medical History:</td>
                          <td class="pl-3" colspan="3">
                            <textarea rows="3" cols="5" class="form-control" formControlName="patMedHistory"></textarea>
                          </td>
                        </tr>
                      </table>
                    </fieldset>

                    <fieldset class="col-md-6">
                      <table>

                        <tr>
                          <td class="py-3">Grafted Eye:<span class="mdtr"> * </span></td>
                          <td class="pl-3">
                            <label><input type="radio" name="eyeOperated" value="L" class="ml-2 mr-1"
                                formControlName="eyeOperated" /> Left
                            </label>
                            <label><input type="radio" name="eyeOperated" value="R" class="ml-2 mr-1"
                                formControlName="eyeOperated">
                              Right</label>
                            <label><input type="radio" name="eyeOperated" value="B" class="ml-2 mr-1"
                                formControlName="eyeOperated">
                              Both</label>

                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Previous Graft in Left Eye:</td>
                          <td class="pl-3">
                            <label><input type="radio" name="prevLeftGraft" value="Y" class="ml-2 mr-1"
                                formControlName="prevLeftGraft"> Yes</label>
                            <label><input type="radio" name="prevLeftGraft" value="N" class="ml-2 mr-1"
                                formControlName="prevLeftGraft"> No</label>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Previous Graft in Right Eye:</td>
                          <td class="pl-3">

                            <label>
                              <input type="radio" name="prevRightGraft" value="Y" class="ml-2 mr-1"
                                formControlName="prevRightGraft"> Yes
                            </label>
                            <label><input type="radio" name="prevRightGraft" value="N" class="ml-2 mr-1"
                                formControlName="prevRightGraft"> No</label>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Transplant Outcome:</td>
                          <td class="pl-3">
                            <ng-select [items]="transplantOutcomeListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="description" bindValue="id" formControlName="transplantOutcome">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                                }}</ng-template>
                            </ng-select>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Visual Acuity Before Surgery:</td>
                          <td class="pl-3">
                            <ng-select [items]="visualAcuityListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="description" bindValue="id" formControlName="visAcuityBefore">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                                }}</ng-template>
                            </ng-select>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3">Visual Acuity After Surgery:</td>
                          <td class="pl-3">
                            <ng-select [items]="visualAcuityListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="description" bindValue="id" formControlName="visAcuityAfter">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                                }}</ng-template>
                            </ng-select>
                          </td>
                        </tr>

                      </table>
                    </fieldset>
                  </div>
                </div>
              </div>
            </tab>

            <tab heading="Disease Requiring Transplantation">
              <div class="mcard">
                <div>
                  <div class="row disease-section">
                    <fieldset class="col-md-6">
                      <table class="disease-left">

                        <tr *ngFor="let item of checklistItems; let i = index;">
                          <td class="py-3 pr-2">{{ item.paramName }}</td>
                          <td>
                            <ng-container *ngIf="item.outputType === 'CHAR'">
                              <label>
                                <input type="radio" [name]="item.paramId" value="Y" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'Y')" [checked]="item.answerYn === 'Y'" />
                                Yes
                              </label>
                              <label>
                                <input type="radio" [name]="item.paramId" value="N" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'N')" [checked]="item.answerYn === 'N'" />
                                No
                              </label>
                            </ng-container>

                            <ng-container *ngIf="item.outputType === 'NUMBER'">
                              <ng-select [items]="numberOptions" [virtualScroll]="true" placeholder="Select"
                                [clearable]="false" [(ngModel)]="item.answerYn" [ngModelOptions]="{standalone: true}">
                                <ng-template ng-option-tmp let-num="item" let-index="index">{{ num }}</ng-template>
                              </ng-select>
                            </ng-container>
                          </td>
                        </tr>
                      </table>
                    </fieldset>
                    <fieldset class="col-md-6">
                      <table class="disease-right">
                        <tr>
                          <td class="py-3 pr-2">Disease requiring transplantation:</td>
                          <td>
                            <ng-select [items]="transDiseaseListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" (change)="onDiseaseChange($event)" bindValue="paramId"
                              formControlName="disReqTransplant">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                                }}</ng-template>
                            </ng-select>
                        </tr>
                        <tr *ngIf="transplantForm.get('disReqTransplant')?.value && transDiseaseProcFilter?.length">
                          <td class="py-3 pr-1 text-right">Disease Transplantation Proc: </td>
                          <td>
                            <ng-select [items]="transDiseaseProcFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" bindValue="paramId" formControlName="disTransProc">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                                }}</ng-template>
                            </ng-select>
                          </td>
                          <td class="py-3 px-1 text-right">Comments: </td>
                          <td>
                            <input type="text" class="form-control form-control-sm ml-2"
                              formControlName="disTransComments" />
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3 pr-2">Principal indication for graft:</td>
                          <td>
                            <ng-multiselect-dropdown [formControl]="transplantForm?.controls['rgTbCorGraftIndication']"
                              [placeholder]="'Select'" [data]="indicationTypeListFilter" [settings]="dropdownSettings">
                            </ng-multiselect-dropdown>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3 pr-2">Preoperative, the patient is:</td>
                          <td>
                            <ng-select [items]="preOpCondListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" bindValue="paramId" formControlName="preopCond">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                                }}</ng-template>
                            </ng-select>
                          </td>
                        </tr>
                      </table>
                    </fieldset>
                  </div>
                </div>
              </div>
            </tab>
            <tab heading="Operative Details">
              <div class="mcard">
                <div>
                  <div class="row operative-section">
                    <fieldset class="col-md-6">
                      <table>

                        <tr *ngFor="let item of leftItems; let i = index;">
                          <td class="py-3">{{ item.paramName }} <span class="mdtr" *ngIf="item.mandatoryYn == 'Y'"> *
                            </span></td>
                          <td class="margin-custom">
                            <ng-container *ngIf="item.outputType === 'CHAR'">
                              <label>
                                <input type="radio" [name]="item.paramId" value="Y" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'Y')" [checked]="item.answerYn === 'Y'" />
                                Yes
                              </label>
                              <label>
                                <input type="radio" [name]="item.paramId" value="N" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'N')" [checked]="item.answerYn === 'N'" />
                                No
                              </label>
                            </ng-container>

                            <ng-container *ngIf="item.outputType === 'NUMBER'">
                              <input type="number" [name]="item.paramId" min="0" step="1"
                                class="form-control form-control-sm" placeholder="Enter a number"
                                (input)="onNumberInputChange($event, item)" [(ngModel)]="item.answerYn"
                                [ngModelOptions]="{standalone: true}" />
                            </ng-container>

                            <ng-container *ngIf="item.outputType === 'LIST'">

                              <ng-select [items]="filteredList.get(item.paramId) || []" bindLabel="paramName"
                                bindValue="paramId" [(ngModel)]="item.answerYn" placeholder="Select"
                                [virtualScroll]="true" [ngModelOptions]="{ standalone: true }">
                              </ng-select>

                            </ng-container>
                            <ng-container *ngIf="item.outputType === 'MULTISELECT'">

                              <ng-multiselect-dropdown [name]="item.paramId" [placeholder]="'Select'"
                                [data]="filteredList.get(item.paramId) || []" [settings]="dropdownSettings"
                                [(ngModel)]="item.answerYn" [ngModelOptions]="{ standalone: true }">
                              </ng-multiselect-dropdown>

                            </ng-container>
                          </td>
                        </tr>
                      </table>
                    </fieldset>
                    <fieldset class="col-md-6">
                      <table>

                        <tr *ngFor="let item of rightItems; let i = index;">
                          <td class="py-3">{{ item.paramName }}<span class="mdtr" *ngIf="item.mandatoryYn == 'Y'"> *
                            </span></td>
                          <td>
                            <ng-container *ngIf="item.outputType === 'CHAR'">
                              <label>
                                <input type="radio" [name]="item.paramId" value="Y" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'Y')" [checked]="item.answerYn === 'Y'" />
                                Yes
                              </label>
                              <label>
                                <input type="radio" [name]="item.paramId" value="N" class="ml-2 mr-1"
                                  (click)="onAnswer(item, 'N')" [checked]="item.answerYn === 'N'" />
                                No
                              </label>
                            </ng-container>

                            <ng-container *ngIf="item.outputType === 'NUMBER'">
                              <input type="number" [name]="item.paramId" min="0" step="1"
                                class="form-control form-control-sm ml-2" placeholder="Enter a number"
                                (input)="onNumberInputChange($event, item)" [(ngModel)]="item.answerYn"
                                [ngModelOptions]="{standalone: true}" />

                            </ng-container>

                            <ng-container *ngIf="item.outputType === 'LIST'">
                              <ng-select [items]="filteredList.get(item.paramId) || []" bindLabel="paramName"
                                bindValue="paramId" [(ngModel)]="item.answerYn" placeholder="Select"
                                [virtualScroll]="true" [ngModelOptions]="{ standalone: true }">
                              </ng-select>
                            </ng-container>
                            <ng-container *ngIf="item.outputType === 'MULTISELECT'">

                              <ng-multiselect-dropdown [name]="item.paramId" [placeholder]="'Select'"
                                [data]="filteredList.get(item.paramId) || []" [settings]="dropdownSettings"
                                [(ngModel)]="item.answerYn" [ngModelOptions]="{ standalone: true }">
                              </ng-multiselect-dropdown>

                            </ng-container>
                          </td>
                        </tr>
                        <tr>
                          <td class="py-3 pr-2">Comment on donor tissue:</td>
                          <td>
                            <input type="text" class="form-control form-control-sm ml-2" placeholder="Enter a text"
                              formControlName="commentDonTissue" />
                          </td>
                        </tr>
                      </table>
                    </fieldset>
                  </div>
                </div>
              </div>
            </tab>

            <div class="d-flex justify-content-end position-relative">
              <button type="button" class="btn btn-secondary d-flex align-items-center gap-2 me-2"
                (click)="previousForm()" [disabled]="currentFormIndex <= 0 || transplantInstituteForms.length === 0">
                <i class="bi bi-arrow-left fs-5"></i> Back
              </button>

              <button type="button" class="btn btn-primary d-flex align-items-center gap-2" (click)="nextForm()"
                [disabled]="currentFormIndex >= transplantInstituteForms.length - 1 || transplantInstituteForms.length === 0">
                Next <i class="bi bi-arrow-right fs-5"></i>
              </button>
            </div>
          </tabset>
        </form>
      </div>
    </ng-template>
  </ngb-panel>
</ngb-accordion>

<div class="btn-container mr-2">
  <button class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
  <button class="btn btn-sm btn-primary" (click)="save()"> Save</button>
</div>