import { DatePipe, DecimalPipe, formatDate } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';
import { PatientDetailsComponent } from 'src/app/_comments/patient-details/patient-details.component';
import { MasterService } from 'src/app/_services/master.service';
import Swal from 'sweetalert2';
import * as AppUtils from '../../common/app.utils';
import * as CommonConstants from '../../_helpers/common.constants';
import { GridNgSelectDataComponent } from 'src/app/common/agGridComponents/grid-ngSelect-data.component';
import { ButtonRendererComponent } from 'src/app/common/agGridComponents/ButtonRendererComponent';
import { SharedService } from 'src/app/_services/shared.service';
import { TbDiabeticRegisterDtls } from 'src/app/_models/diabetic-register-dtls-model';
import { TbDiabMngmnt } from 'src/app/_models/diabetic-managment-model';
import { Router } from '@angular/router';
import * as AppCompUtils from '../../common/app.component-utils';
import { diabeticRegistryFrom } from 'src/app/_models/diabeticRegistryForm.model';
import { DiabeticService } from '../diabeticService';
import { RgTbDiabInvst } from 'src/app/_models/RgTbDiabInvst';
import * as _ from 'lodash';
import { ICDList } from 'src/app/_models/icdList-models';
// import { RegTbDiabMngmntDto } from 'src/app/_models/RegTbDiabMngmntDto';


@Component({
  selector: 'app-diabetic-registry',
  templateUrl: './diabetic-registry.component.html',
  styleUrls: ['./diabetic-registry.component.scss'],

})
export class DiabeticRegistryComponent implements OnInit {
  diabeticRegistryForm: FormGroup;
  diabeticRegistry: FormGroup;
  followUpForm: FormGroup;
  selectedFollowUp: any;
  regTbDiabMngmntForm: FormGroup;
  rgTbDiabeticRegisterDtls: TbDiabeticRegisterDtls;
  diabetesTypesList: any[];
  diabetesSubTypesList: any[];
  icdList: Array<ICDList>;
  institutes: any[];
  loginId: any;
  regId: any;
  civilId: any;
  submitted = false;
  today = new Date();
  frameworkComponents;
  centralRegNoExit: boolean = false;
  sensorTypeList: any;
  pumpType: any;
  eyeExamData: any = [];
  footExamData: any = [];
  eyeExamForm: any = [];
  footExamForm: any = [];
  examForm: FormGroup;
  examParamsAll: any[];
  eyeExamparams: any[];
  eyeExamValues: any[];
  footExamparams: any[];
  footExamValues: any[];
  diabLabInvestList: any[];
  diabLabInvest: any[];
  visitsHist: any[];
  diabLabInvestVisitList: any[];
  visitDateList: any[];
  public familyHistoryOptions = [];
  showFollowUpFields: boolean = false;
  modeOfPrestList: [];
  testResultOptions: any[];
  displayHBA1cList: boolean = false;
  disableInput: boolean = true;
  nagma: any;
  dataFetched: boolean;
  enableFirstExam: boolean = true;
  nationalityList: any[];
  nationalityForDiabetic: boolean = true;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  submittedOne: boolean = false;
  submittedThree: boolean = false;
  submittedFive: boolean = false;
  submittedZero: boolean = false;
  submittedfour: boolean = false;
  submittedSix: boolean = false;
  visitSubmittedZero: boolean = false;
  visitSubmittedOne: boolean = false;
  visitSubmittedTwo: boolean = false;
  retrivedData: any;
  diabticRegDetails: any;
  diabMngmntDetails: any = [];
  diabExamDetails: any = [];
  diabMngmentF: any[];
  diabFamilyHistoryDetails: any[] = [];
  diabInvstDetails: any = [];
  diabVisitDetails: any[] = [];
  diabVisitExamDetails: any = [];
  diabVisitMangDetails: any = [];
  diabVisitInvestDetails: any = [];
  lifestyleModifyChecked: any = [];
  selectedDates: Date[] = [];
  labitoo: any;
  tasnim: boolean = false;
  checked: boolean;
  runId: any;
  civilIdEntryType: any;
  getDetails: any;
  gettData: any;
  existingLab: any;
  searchCivilId: any;
  saveId: any;
  birthDate: any;
  flwupOtherDisease: number;
  followupList: Date[] = [];
  selectedId: number;
  levelValue: number;
  selectedDate: Date;
  maxDate: string;
  visitDate: string;
  activeAccordionId: string = 'ngb-panel-0';
  selectedFUDate: any;
  isError: boolean = false;
  errorMsg: string = '';

  malnutritionData = [
    {
      name: "Weight for Height",
      level: [{ item: 'Obese', value: 0 }, { item: 'Overweight', value: 1 }, { item: 'Moderate Wasting', value: 2 }, { item: 'Severe Wasting', value: 3 }, { item: 'Normal', value: 4 }],
      color: [{ name: 'pink-color', value: 0 }, { name: 'ylw-color', value: 1 }, { name: 'orng-color', value: 2 }, { name: 'red-color', value: 3 }, { name: 'grn-color', value: 4 }]
    },
    {
      name: "Height for Age",
      level: [{ item: 'Moderate Stunting', value: 11 }, { item: 'Severe Stunting', value: 12 }, { item: 'Normal', value: 13 }],
      color: [{ name: 'orng-color', value: 11 }, { name: 'red-color', value: 12 }, { name: 'grn-color', value: 13 }]
    },
    {
      name: "Weight for Age",
      level: [{ item: 'Moderate Underweight', value: 21 }, { item: 'Severe Underweight', value: 22 }, { item: 'Normal', value: 23 }],
      color: [{ name: 'orng-color', value: 21 }, { name: 'red-color', value: 22 }, { name: 'grn-color', value: 23 }]
    }
  ]
  rd: TbDiabeticRegisterDtls = {
    regDate: new Date(),
    diabetesId: 0,
    civilID: 0,
    diabetesType: 0,
    duration: '',
    durationType: 0,
    instPatientId: 0,
    regNo: '',
    regType: '',
    startYear: undefined,
    diabetesSubType: 0,
    modifiedDate: undefined,
    modifiedBy: 0,
    createdDate: undefined,
    createdBy: 0,
    diagnosedDate: undefined,
    fileStatus: 0,
    rgTbDiabFamilyHistory: []
  }
  dmg: TbDiabMngmnt = {
    createdDate: new Date(),
    runId: null,
    estCode: 0,
    createdBy: 0,
    modifiedOn: undefined,
    modifiedBy: 0,
    lifestyleModify: '',
    oralHypoDrugs: '',
    metabolicProcedure: '',
    ingGlptAgonist: '',
    insulin: '',
    insulinType: ''
  }


  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;


  public page1 = 1;
  public page2 = 1;
  public pageSize = 2;

  constructor(private formBuilder: FormBuilder, private _sharedService: SharedService, private _masterService: MasterService, private _diabeticService: DiabeticService, private fb: FormBuilder, private _router: Router) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.checked = false;
    this.loginId = curUser['person'].perscode
    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };


    this.populateMasterData();
    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
    this.initialsFollowUpForm();
    this.initialsRegistryForm();

  }

  private initialsRegistryForm() {
    this.diabeticRegistry = this.fb.group({
      regNo: new FormControl(),
      diabetesId: new FormControl(),
      runId: new FormControl(),
      visitId: new FormControl(),
      civilId: new FormControl(),
      regInst: new FormControl(),
      createdInstid: new FormControl(),
      civilIdEntryType: new FormControl(),
      registerType: [4],
      fileStatus: new FormControl(),
      maritalStatus: new FormControl(),
      regDate: new FormControl(null, Validators.required),
      diagnosedDate: new FormControl(null, Validators.required),
      diabetesTypes: new FormControl(null, Validators.required),
      duration: new FormControl(),
      weight: new FormControl(null, Validators.required),
      hight: new FormControl(null, Validators.required),
      bmi: new FormControl(),
      visitType: new FormControl('R'),
      mode_Presentation: new FormControl(),
      family_Id: new FormControl(),
      bp_Dia: new FormControl(),
      bp_Sys: new FormControl(),
      regType: new FormControl(null, Validators.required),
      diabetesType: new FormControl(),
      lifestyleModify: new FormControl(false),
      ingGlptAgonist: new FormControl(false),
      oralHypoDrugs: new FormControl(false),
      insulin: new FormControl(false),
      insulinType: new FormControl(),
      lab_Result: new FormControl(),
      remarks: new FormControl(),
      sensorYn: new FormControl(),
      sensorType: new FormControl(),
      sensorOther: new FormControl(),
      pumpYn: new FormControl(),
      pumpType: new FormControl(),
      pumpOther: new FormControl(),
      createdBy: new FormControl(null),
      createdDate: new FormControl(null),
    });
  }

  private initialsFollowUpForm() {
    this.followUpForm = this.fb.group({
      visitId: new FormControl(null),
      visitDate: new FormControl(null, Validators.required),
      advice: new FormControl(null, Validators.required),
      visitRemarks: new FormControl(null, Validators.required),
      finalDiagnosis: new FormControl(null, Validators.required),
      weight: new FormControl(null),
      hight: new FormControl(null),
      bmi: new FormControl(null),
      bp_Dia: new FormControl(null),
      bp_Sys: new FormControl(null),
      visitType: new FormControl('F'),
      index: new FormControl(null),
      mode_Presentation: new FormControl(null),
      diabetesType: new FormControl(null),
      LabInvest: new FormControl(null),
      lab_Result: new FormControl(null),
      remarks: new FormControl(null),
      lifestyleModify: new FormControl(null),
      ingGlptAgonist: new FormControl(null),
      oralHypoDrugs: new FormControl(null),
      insulin: new FormControl(null),
      insulinType: new FormControl(null),
      sensorYn: new FormControl(null),
      sensorType: new FormControl(null),
      sensorOther: new FormControl(),
      pumpYn: new FormControl(null),
      pumpType: new FormControl(null),
      pumpOther: new FormControl(),
      createdBy: new FormControl(null),
      createdDate: new FormControl(null),
    });
  }



  ngOnInit() {
    this._masterService.getIcdList();
    this.maxDate = new Date().toISOString().split('T')[0];
    this.populateMasterData();
    this.familyHistoryOptions = AppCompUtils.FAMILY_HISTORY_DIABETES_REG;
    this._masterService.getDiabLabInvst();
    this._masterService.getAllDiabetesPresModReg();
    this._masterService.getIcdList();
    this._masterService.icdList.subscribe(value => {
      this.icdList = value;
    });



    this.initialsMainList();
    if (this.diabVisitDetails && this.diabVisitDetails.length >= 0) {

      const firstItem = this.diabVisitDetails[0];
      this.addEditFollowUp(this.followUpForm.value, 'add', firstItem, 0);
    }

  }



  onVisitDateChange(date: string): void {
    if (!this.followUpForm) {
      return;
    }


    const visitDateControl = this.followUpForm.get('visitDate');
    if (!visitDateControl) {
      return;
    }

    visitDateControl.setValue(date);
  }

  initialsMainList() {
    this.retrivedData = this.retrivedData ? this.retrivedData : {};
    this.retrivedData.rgTbDiabeticRegisterDtls = this.retrivedData.rgTbDiabeticRegisterDtls ? this.retrivedData.rgTbDiabeticRegisterDtls : {};
    this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit = this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit ? this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit : [];
  }



  addNewFollowUp(e, action?) {
    if (this.diabeticRegistry.get('regDate').value) {
      this.initialsMainList(); // Ensure list initialization


      let dateExist = false;
      this.diabVisitDetails.forEach(el => {
        if (this.cellRenderer(el.visitDate) == this.cellRenderer(e)) {
          dateExist = true;
          Swal.fire("", "Visit Date already exist!", "error");
          return;
        }

      })



      if (!dateExist) {
        // let index = this.diabVisitDetails.length;
        this.diabVisitDetails.push({ visitDate: e, visitId: null, ...e });
        this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit.push({ visitDate: e, visitId: null, ...e })


        if (this.selectedFUDate) {
          this.addEditFollowUp(this.followUpForm.value, '', this.diabVisitDetails[this.followUpForm.get('index').value], this.followUpForm.get('index').value);
        }

        if (action === 'get') {
          this.getFollowUpVisit(this.diabVisitDetails[this.diabVisitDetails.length - 1], this.diabVisitDetails.length - 1);
        }
      }
    } else {
      Swal.fire("Missing Data", "Register Form (Registration Date) Must Not Be Empty", "warning");
    }
  }




  removeFollowUp(index: number) {
    if (index > -1 && index < this.selectedDates.length) {
      this.selectedDates.splice(index, 1);
      this.showInsulinDetails = false;
      this.showFolloUpInsulinDetails = false;
      this.diabLabInvestVisitList = _.cloneDeep(this.diabLabInvestVisitList);
      this.diabLabInvestVisitList.forEach(obj => {
        if (obj.lab_Result !== undefined && obj.lab_Result !== null &&
          obj.remarks !== undefined && obj.remarks !== null) {
          obj.lab_Result = null;
          obj.remarks = null;
        }
      });
      this.followUpForm.reset();

    }
  }


  addFollowup() {
    this.initialsFollowUpForm();
    this.followupList.push(new Date());

  }
  getICD(icd) {
    if (icd) {
      return this.icdList.filter(s => s.icd == icd).map(s => s.disease)[0];
    }
  }

  getfollowUpDetails(i: number) {
    this.selectedId = i;
  }
  getcolorcode(val) {
    this.levelValue = val;

  }
  populateMasterData() {
    this._masterService.getDiabetesTypes().subscribe(res => {
      this.diabetesTypesList = res.result;
    });

    this._masterService.getAllDiabeticSubtypes().subscribe(res => {
      this.diabetesSubTypesList = res.result;
    });

    this._masterService.institiutes.subscribe(value => {
      this.institutes = value['result'];
    });
    this._masterService.getAllDiabetesExamParams().subscribe(value => {
      this.examParamsAll = value['result'];
      this.eyeExamparams = value['result'].filter(el => (el.prevId == AppUtils.DIAB_EYE_EXAM_PARAM_PREVID))
      this.footExamparams = value['result'].filter(el => (el.prevId == AppUtils.DIAB_FOOT_EXAM_PARAM_PREVID))
    });


    this._masterService.getDiabLabInvst();

    this._masterService.diabLabList.subscribe(value => {
      this.diabLabInvest = value;
      this.diabLabInvestList = _.cloneDeep(this.diabLabInvest);
      this.diabLabInvestVisitList = _.cloneDeep(this.diabLabInvest);
    });

    this._masterService.getAllDiabetesPresModReg();
    this._masterService.modeOfPrestList.subscribe(value => {
      this.modeOfPrestList = value;
    });

    this._masterService.getDiabSensorsMasterData().subscribe(value => {
      this.sensorTypeList = value.result.sort((a, b) => a.id - b.id);
    });

    this._masterService.getDiabPumpsMasterData().subscribe(value => {
      this.pumpType = value.result.sort((a, b) => a.id - b.id);
    });

  }


  //use to check the data by civil id, first check in eReg DB if data not exist , get the personal information from ROP
  callFetchDataByCivilID() {
    this.getdata(AppUtils.CALLTYPE_BY_CIVILID, '', this.patientDetails.f.civilId.value);
  }

  onInput(item, event) {
    const inputElement = event.target as HTMLInputElement;

    if (item.checked === true) {
      item[inputElement.id] = inputElement.value;
    }
  }


  onInputt(item, event) {
    const inputElement = event.target as HTMLInputElement;

    if (item.checked === true) {
      item[inputElement.id] = inputElement.value;
    }
  }


  onChicked(item, event) {
    this.tasnim
    if (event.target.checked === true) {
      item.checked = true;
    } else {
      item.checked = false;
      // item.testResult = null;
      item.lab_Result = null;
      item.remarks = null;
    }
  }


  showInsulinDetails: boolean = false;
  showFolloUpInsulinDetails: boolean = false;
  toggleInsulinDetails() {
    const insulinChecked = this.diabeticRegistry.get('insulin').value;
    this.showInsulinDetails = insulinChecked === true || insulinChecked === 'Y';

    // Ensure insulin type remains set when insulin is checked
    if (!this.showInsulinDetails) {
      this.diabeticRegistry.patchValue({ insulinType: null });
    }
  }


  toggleInsulinVisitDetails() {
    this.showFolloUpInsulinDetails = !this.showFolloUpInsulinDetails;
  }


  collectCheckedInputs() {
    const checkedInputs = Object.keys(this.regTbDiabMngmntForm)
      .filter(key => this.regTbDiabMngmntForm[key])
      .map(key => ({ name: key, value: this.regTbDiabMngmntForm[key] }));
  }

  private setCheckboxValue(controlName: string, value: string): void {
    let control = this.diabeticRegistry.get(controlName);
    if (control) {
      control.setValue(value === 'Y', { emitEvent: false });
    }

  }
  private setFollowUpCheckboxValue(controlName: string, value: string): void {
    let control = this.followUpForm.get(controlName);

    if (control) {
      control.setValue(value === 'Y', { emitEvent: false });
    }
  }




  findAge() {
    if (this.patientDetails.patientForm.value.dob) {
      let calculatedAge;
      try {
        // Convert the date format
        const formattedDate = this.convertDateFormat(this.patientDetails.patientForm.value.dob);

        // Calculate age from formatted date
        calculatedAge = this.calculateAgeFromFormattedDate(formattedDate);
      } catch (error) {
        calculatedAge = null; // Handle error gracefully
      }

      // Update the age in the form
      this.patientDetails.patientForm.patchValue({ age: calculatedAge });
    }
  }

  convertDateFormat(inputDate: string): string {
    const [day, month, year] = inputDate.split('-');

    let numericYear = parseInt(year, 10);

    // Check if year is in the format "yyyy", if not, assume it's "yy" and adjust accordingly
    if (year.length !== 4) {
      if (year.length === 2) {
        numericYear += 2000; // Assuming any year less than 100 is in the 21st century
      } else {
        throw new Error(`Invalid year format: ${year}`);
      }
    }

    return `${month}-${day}-${numericYear}`;
  }

  calculateAgeFromFormattedDate(formattedDate: string): number {
    // Convert formatted date to a Date object
    const dateOfBirth = new Date(formattedDate);

    // Calculate age
    const today = new Date();
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();

    // If birth month has not occurred yet, subtract 1 from age
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
      age--;
    }

    return age;
  }


  loadFollowup() {
    if (this.diabVisitDetails && this.diabVisitDetails.length >= 0) {
      const firstItem = this.diabVisitDetails[0];
      this.diabVisitMangDetails = firstItem.regTbDiabMngmnt;
      this.diabVisitExamDetails = firstItem.rgTbDiabExam;
      this.addEditFollowUp(this.followUpForm.value, 'add', firstItem, 0);
    }
  }

  getRegisterform(data) {
    if (data && data.rgTbDiabeticRegisterDtls) {
      let diabRegInfo = data.rgTbDiabeticRegisterDtls;

      if (diabRegInfo) {
        diabRegInfo.regDate = diabRegInfo.regDate ? this.cellRenderer(diabRegInfo.regDate) : null;

        if (diabRegInfo.rgTbDiabFamilyHistory && diabRegInfo.rgTbDiabFamilyHistory.length) {
          this.diabFamilyHistoryDetails = diabRegInfo.rgTbDiabFamilyHistory[0];
          if (diabRegInfo.rgTbDiabFamilyHistory[0].familyId) {
            let familyId = diabRegInfo.rgTbDiabFamilyHistory[0].familyId;

            let familyHisDiabetes = this.familyHistoryOptions.find(e => e.id == familyId);
            this.diabeticRegistry.patchValue({ family_Id: familyHisDiabetes.id });
          }

        }

        let rVisit = diabRegInfo.rgTbDiabVisit.filter(el => el.visitType == "R")[0];

        this.diabticRegDetails = rVisit;
        //RegisterExam
        if (rVisit && rVisit.rgTbDiabExam && rVisit.rgTbDiabExam.length) {
          this.diabExamDetails = rVisit.rgTbDiabExam[0];
          this.diabeticRegistry.patchValue(rVisit.rgTbDiabExam[0]);
        }

        // RegisterMang
        if (rVisit && rVisit.regTbDiabMngmnt && rVisit.regTbDiabMngmnt.length) {
          this.diabMngmntDetails = rVisit.regTbDiabMngmnt[0]; // Ensure it's assigned


          if (this.diabMngmntDetails) {

            setTimeout(() => {
              this.setCheckboxValue('lifestyleModify', this.diabMngmntDetails.lifestyleModify);
              this.setCheckboxValue('oralHypoDrugs', this.diabMngmntDetails.oralHypoDrugs);
              this.setCheckboxValue('ingGlptAgonist', this.diabMngmntDetails.ingGlptAgonist);
              this.setCheckboxValue('insulin', this.diabMngmntDetails.insulin);
            }, 500); // Delay ensures no overwrite

          }

          setTimeout(() => {
            if (this.diabMngmntDetails.insulin === 'Y') {
              this.showInsulinDetails = true;

              // Ensure Insulin Checkbox is checked when retrieving data
              this.diabeticRegistry.patchValue({ insulin: true });

              // Ensure correct insulinType radio button is selected
              const insulinTypeValue = this.diabMngmntDetails.insulinType;
              if (['I', 'S', 'M', 'P'].includes(insulinTypeValue)) {
                this.diabeticRegistry.get('insulinType').setValue(insulinTypeValue);
              } else {
                this.diabeticRegistry.get('insulinType').setValue(null); // Clear if invalid
              }

            } else {
              this.showInsulinDetails = false;
              this.diabeticRegistry.patchValue({ insulin: false, insulinType: null });
            }
          }, 500);



          let pumpYnValue = this.diabMngmntDetails.pumpYn;
          this.diabeticRegistry.get('pumpYn').setValue(pumpYnValue);

          if (pumpYnValue == 'Y') {
            let pumpTypeValue = this.diabMngmntDetails.pumpType;
            this.diabeticRegistry.get('pumpType').setValue(pumpTypeValue);
          } else {
            this.diabeticRegistry.get('pumpType').setValue(" ");
          }
          if (this.diabMngmntDetails.pumpType === 1561) {
            let pumpOtherValue = this.diabMngmntDetails.pumpOther;
            this.diabeticRegistry.get('pumpOther').setValue(pumpOtherValue);
          }


          let sensorYnValue = this.diabMngmntDetails.sensorYn;
          this.diabeticRegistry.get('sensorYn').setValue(sensorYnValue);

          if (sensorYnValue === 'Y') {
            let sensorTypeValueID = this.diabMngmntDetails.sensorType;
            this.diabeticRegistry.get('sensorType').setValue(sensorTypeValueID);
          } else {
            this.diabeticRegistry.get('sensorType').setValue(null); // Clear the field if sensorYn is 'N'
          }

          if (this.diabMngmntDetails.sensorType === 1560) {
            let sensorOtherValue = this.diabMngmntDetails.sensorOther;
            this.diabeticRegistry.get('sensorOther').setValue(sensorOtherValue);
          }
        }

        this.visitsHist = _.cloneDeep(diabRegInfo.rgTbDiabVisit);
        if (rVisit && rVisit.rgTbDiabInvst && rVisit.rgTbDiabInvst.length > 0) {
          this.diabInvstDetails = rVisit.rgTbDiabInvst[0]; // Store first item details if needed
          let allData = this.diabLabInvestList;

          rVisit.rgTbDiabInvst.forEach(retrievedData => {
            const existingLab = allData.find(e => e.id === retrievedData.test_Id);

            if (existingLab) {
              // If the lab test already exists, update its properties
              existingLab.lab_Result = retrievedData.lab_Result;
              existingLab.remarks = retrievedData.remarks;
              existingLab.test_Id = retrievedData.test_Id;
              existingLab.runId = retrievedData.runId;
              existingLab.createdBy = retrievedData.createdBy;
              existingLab.createdDate = retrievedData.createdDate;
              existingLab.checked = true; // Mark as checked
            } else {
              // If it doesn't exist, create a new entry
              let newLab = new RgTbDiabInvst();
              newLab.lab_Result = retrievedData.lab_Result;
              newLab.remarks = retrievedData.remarks;
              newLab.test_Id = retrievedData.test_Id;
              newLab.runId = retrievedData.runId;
              this.diabLabInvestList.push(newLab);
            }
          });

          // Ensure all matching test_Id entries are checked
          this.diabLabInvestList.forEach(item => {
            if (rVisit.rgTbDiabInvst.some(retrievedData => item.test_Id === retrievedData.test_Id)) {
              item.checked = true;
            }
          });
        }

        const momentDate = moment(diabRegInfo.regDate, "DD-MM-YYYY").toDate();
        diabRegInfo.regDate = momentDate;

        const dmomentDate = moment(diabRegInfo.diagnosedDate, "DD-MM-YYYY").toDate();
        diabRegInfo.diagnosedDate = dmomentDate;

        // pass data to form
        this.diabeticRegistry.patchValue(diabRegInfo)


      }
    }

  }

  getdata(searchby: any, regNo: any, civilId: any) {

    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        Swal.fire('', 'No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.', 'warning');
      }
    }

    if (callMPI == true) {
      this._sharedService.setPatientData(this.patientDetails);
      this._sharedService.fetchMpi().subscribe(res => {
      });

    } else (
      Swal.fire('', msg, 'warning')
    )

  }

  getDiabRegistryDetails(id: string, type: 'regId' | 'civilId') {
    const fetchData = type === 'regId'
      ? this._diabeticService.getDiabetic(id)
      : this._diabeticService.getDiabeticRegistryByCivilId(id); // <-- Ensure this method exists in your service

    fetchData.subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.centralRegNoExit = true;
        this.retrivedData = res['result'];

        if (this.retrivedData) {
          this.patientDetails.setPatientDetails(this.retrivedData);
          this.getRegisterform(this.retrivedData);

          this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.diabVisitDetails.push(el);
            }
          });

          this.diabVisitDetails.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());
          this.loadFollowup();
        } else {
          Swal.fire({
            title: 'Warning',
            text: 'Patient Not Found',
            icon: 'warning',
            confirmButtonText: 'Ok'
          });
        }

      } else if (res['code'] == '400' || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', 'No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.', 'warning');
      } else {
        Swal.fire(' ', 'The entered identifier does not match any existing data. Please double-check and re-enter.', 'warning');
      }
    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occurred while retrieving user details', 'error');
    });
  }

  search() {
    if (this.regId || this.civilId) {
      setTimeout(() => {
        if (this.regId) {
          this.getDiabRegistryDetails(this.regId, 'regId');
          this.regId = '';
        } else if (this.civilId) {
          this.getDiabRegistryDetails(this.civilId, 'civilId');
          this.civilId = '';
        }
      }, 1000);
    } else {
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID or Civil ID'
      });
    }
  }


  callMpiMethod() {
    this._diabeticService.getDiabeticRegistryByCivilId(this.patientDetails.patientForm.value.civilId).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.clear();
        this.dataFetched = true;
        this.retrivedData = res['result'];

        this.patientDetails.setPatientDetails(this.retrivedData);
        this.getRegisterform(this.retrivedData);

        if (this.retrivedData && this.retrivedData.rgTbDiabeticRegisterDtls && this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit && this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit.length) {
          this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.diabVisitDetails.push(el);
            }
            this.diabVisitDetails.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());

          });
          this.loadFollowup();

        }

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        this.getdata('civilId', '', this.patientDetails.patientForm.value.civilId);

      } else {
        Swal.fire(' ', 'The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.', 'warning')
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });

  }

  getvisitBmi() {
    let w = this.followUpForm.get('weight').value;
    let h = this.followUpForm.get('hight').value;
    this.bmivisitDetails(w, h);
  }

  bmivisitDetails(width: any, hight: any) {
    this.followUpForm.patchValue({ 'weight': width, 'hight': hight });
    let bmi = this._sharedService.calculateBMI(width, hight);
    this.followUpForm.patchValue({ bmi: this._decimalPipe.transform(bmi, "1.2-2") });
  }


  getBmi() {
    let w = this.diabeticRegistry.get('weight').value;
    let h = this.diabeticRegistry.get('hight').value;
    this.bmiDetails(w, h);
  }

  bmiDetails(width: any, hight: any) {
    this.diabeticRegistry.patchValue({ 'weight': width, 'hight': hight });
    let bmi = this._sharedService.calculateBMI(width, hight);
    this.diabeticRegistry.patchValue({ bmi: this._decimalPipe.transform(bmi, "1.2-2") });

  }



  heightValidation(theHeight, TheAge) {
    if (TheAge >= 5 && !theHeight) {
      Swal.fire('Alert', 'Height is mandatory for calculating BMI when age is greater than or equal to 5.', 'warning');
      return false;
    }
    return true;
  }



  clear() {
    this.patientDetails.clear();
    this.visitsHist = [];
    this.submitted = false;
    this.initialsMainList();
    this.diabeticRegistry.reset();
    this.followUpForm.reset();
    this.diabVisitDetails = [];
    this.diabMngmntDetails = [];
    this.diabExamDetails = [];
    this.diabFamilyHistoryDetails = [];
    this.diabInvstDetails = [];
    this.diabVisitExamDetails = [];
    this.diabVisitMangDetails = [];
    this.diabVisitInvestDetails = [];
    this.diabLabInvestVisitList = _.cloneDeep(this.diabLabInvest);
    this.diabLabInvestList = _.cloneDeep(this.diabLabInvest);

  }

  get f() { return this.diabeticRegistryForm.controls; }

  isObject(val) {
    return (typeof val === 'object');
  }


  insulinTypeValidation() {
    if (this.diabeticRegistry.controls.insulin.value && !this.diabeticRegistry.controls.insulinType.value) {
      Swal.fire('Alert', 'To proceed, please select the demonstrated insulin type.', 'warning');
      return false;
    }
    return true;
  }


  validatedVisitData() {
    let weight = this.followUpForm.controls.weight.value;
    let height = this.followUpForm.controls.hight.value;

    if (!weight || !height) {  // If either weight or height is missing
      Swal.fire('Warning', 'Please fill in both Weight and Height fields.', 'warning');
      return false;
    }
    return true;
  }

  validatedData() {
    let regDate = this.diabeticRegistry.controls.regDate.value;
    let diabetesType = this.diabeticRegistry.controls.diabetesType.value;
    let diagnosedDate = this.diabeticRegistry.controls.diagnosedDate.value;
    let regType = this.diabeticRegistry.controls.regType.value;
    let weight = this.diabeticRegistry.controls.weight.value;


    if (!regDate || !diabetesType || !diagnosedDate || !regType || !weight) {
      Swal.fire('Warning', 'Please fill in all mandatory fields.', 'warning');
      return false;

    }
    return true;
  }


  cellRenderer = (data) => {
    if (data && data.value) {
      return '';
    }
    if (data && data.value != null) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    }
    if (data) {
      const formattedDate = moment(data).format('DD-MM-yyyy');
      return formattedDate;
    }
    else {
      return '';
    }
  };


  cellRendererBack = (data) => {
    if (data) {
      if (data > 0) {
        const formattedDate = data
        return formattedDate;
      } else {
        const [day, month, year] = data.split('-').map(Number);
        const dateObject = new Date(year, month - 1, day);
        const formattedDate = dateObject.getTime();
        return formattedDate;
      }
    } else {
      return '';
    }
  };


  addEditFollowUp(data, action?, item?, i?) {
    data.visitType = 'F';
    this.diabVisitDetails[data.index] = data;
    this.diabVisitDetails[data.index].visitType = 'F';


    // lab Processing
    const isAtLestOnelabChecked = this.diabLabInvestVisitList.some(item => item.checked === true);
    if (isAtLestOnelabChecked) {
      this.diabVisitDetails[data.index].rgTbDiabInvst = [];
      this.diabLabInvestVisitList.forEach(e => {
        if (e.checked) {
          let lab = {};
          lab['runId'] = e.runId ? e.runId : null;
          lab['modifiedDate'] = e.modifiedDate ? e.modifiedDate : null;
          lab['modifiedBy'] = e.modifiedBy ? e.modifiedBy : null;
          lab['createdDate'] = e.createdDate ? e.createdDate : null;
          lab['createdBy'] = e.createdBy ? e.createdBy : null;
          lab['test_Id'] = e.id;
          lab['lab_Result'] = e.lab_Result;
          lab['remarks'] = e.remarks;
          this.diabVisitDetails[data.index].rgTbDiabInvst.push(lab);
        }
      });
    }

    // Management Processing
    if (this.diabVisitMangDetails && Object.keys(this.diabVisitMangDetails).length > 0) {
      Object.assign(this.diabVisitMangDetails, this.getFollowUpManagementDetails());
    } else {
      this.diabVisitMangDetails = this.getFollowUpManagementDetails();
    }
    this.diabVisitDetails[data.index].regTbDiabMngmnt = [this.diabVisitMangDetails];

    // Exam Processing
    if (this.diabVisitExamDetails && Object.keys(this.diabVisitExamDetails).length > 0) {
      Object.assign(this.diabVisitExamDetails, this.getFollowUpExamDetails());
    } else {
      this.diabVisitExamDetails = this.getFollowUpExamDetails();
    }
    this.diabVisitDetails[data.index].rgTbDiabExam = [this.diabVisitExamDetails];



    // Update main data
    this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit[data.index] = this.diabVisitDetails[data.index];
    this.selectedFollowUp = this.diabVisitDetails[data.index];

    // Handle 'add' action
    if (action === 'add') {
      this.getFollowUpVisit(item, i);
    }
  }


  getFollowUpManagementDetails() {
    const checkboxControls = ['lifestyleModify', 'oralHypoDrugs', 'ingGlptAgonist', 'insulin'];
    let updatedValues: any = {};

    // Loop through checkboxes to save 'Y' or 'N' correctly
    for (const controlName of checkboxControls) {
      let control = this.followUpForm.get(controlName);
      updatedValues[controlName] = control && control.value ? 'Y' : 'N';
    }

    // Process insulin type: Only save insulinType if insulin is checked
    updatedValues['insulinType'] = (updatedValues['insulin'] === 'Y')
      ? this.followUpForm.controls.insulinType.value
      : ''; // Clear insulinType if insulin is not checked

    // Process sensor details
    updatedValues['sensorYn'] = this.followUpForm.controls.sensorYn.value;
    updatedValues['sensorType'] = (updatedValues['sensorYn'] === 'N') ? '' : this.followUpForm.controls.sensorType.value;
    updatedValues['sensorOther'] = updatedValues['sensorType'] === 1560 ? this.followUpForm.controls.sensorOther.value || '' : '';


    // Process pump details
    updatedValues['pumpYn'] = this.followUpForm.controls.pumpYn.value;
    updatedValues['pumpType'] = (updatedValues['pumpYn'] === 'N')
      ? ''
      : this.followUpForm.controls.pumpType.value;
    updatedValues['pumpOther'] = updatedValues['pumpType'] === 1561 ? this.followUpForm.controls.pumpOther.value || '' : '';


    return updatedValues;
  }



  getFollowUpExamDetails() {
    return {
      hight: this.followUpForm.controls.hight.value,
      weight: this.followUpForm.get('weight').value,
      bmi: this.followUpForm.get('bmi').value,
      bp_Sys: this.followUpForm.get('bp_Sys').value,
      bp_Dia: this.followUpForm.get('bp_Dia').value,
      mode_Presentation: this.followUpForm.get('mode_Presentation').value,
      diabetesType: this.followUpForm.get('diabetesType').value
    };
  }

  unsavedFollowupDtls() {
    if (this.areObjectsEqual(this.selectedFollowUp, this.followUpForm.value)) {
      return true;
    } else {
      return false;
    }
  }
  areObjectsEqual(obj1, obj2) {
    // Check if both inputs are null or undefined
    if (obj1 === null && obj2 === null) {
      return true;
    }

    // Check if one of the objects is null or undefined, but not both
    if (obj1 === null || obj2 === null || obj1 === undefined || obj2 === undefined) {
      return false;
    }

    // Check if both inputs are objects
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return obj1 === obj2;
    }

    // Check if both objects have the same set of keys
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    // Check if the values of each key are equal
    for (const key of keys1) {
      if (!this.areObjectsEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  }

  clearFollowUp() {
    this.showFolloUpInsulinDetails = false;
    this.diabLabInvestVisitList = _.cloneDeep(this.diabLabInvest);
    this.followUpForm.reset();
  }

  getFollowUpVisit(data, indx) {
    this.clearFollowUp(); // Clear any previous follow-up data
this.getPrivMangDetails();

    data.index = indx;

    // FollowUp LAB CHECK
    if (data.rgTbDiabInvst && data.rgTbDiabInvst.length > 0) {
      const allData = this.diabLabInvestVisitList;

      data.rgTbDiabInvst.forEach(retrievedData => {
        const existingLab = allData.find(e => e.id === retrievedData.test_Id);

        if (existingLab) {
          // Update existing lab test details
          existingLab.lab_Result = retrievedData.lab_Result;
          existingLab.remarks = retrievedData.remarks;
          existingLab.test_Id = retrievedData.test_Id;
          existingLab.runId = retrievedData.runId;
          existingLab.createdBy = retrievedData.createdBy;
          existingLab.createdDate = retrievedData.createdDate;
          existingLab.checked = true; // ✅ Mark as checked
        } else {
          // Add new lab test entry
          const newLab = new RgTbDiabInvst();
          newLab.lab_Result = retrievedData.lab_Result;
          newLab.remarks = retrievedData.remarks;
          newLab.test_Id = retrievedData.test_Id;
          newLab.runId = retrievedData.runId;
          this.diabLabInvestVisitList.push(newLab);
        }
      });

      // ✅ Ensure all relevant checkboxes are checked
      this.diabLabInvestVisitList.forEach(item => {
        if (data.rgTbDiabInvst.some(retrievedData => item.test_Id === retrievedData.test_Id)) {
          item.checked = true;
        }
      });
    }



    if (data.regTbDiabMngmnt && data.regTbDiabMngmnt.length) {
      this.diabVisitMangDetails = data.regTbDiabMngmnt[0];

      if (this.diabVisitMangDetails) {

        setTimeout(() => {
          this.setFollowUpCheckboxValue('lifestyleModify', this.diabVisitMangDetails.lifestyleModify);
          this.setFollowUpCheckboxValue('oralHypoDrugs', this.diabVisitMangDetails.oralHypoDrugs);
          this.setFollowUpCheckboxValue('ingGlptAgonist', this.diabVisitMangDetails.ingGlptAgonist);
          this.setFollowUpCheckboxValue('insulin', this.diabVisitMangDetails.insulin);
        }, 500); // Delay ensures no overwrite

      }

      setTimeout(() => {
        if (this.diabVisitMangDetails.insulin === 'Y') {
          this.showFolloUpInsulinDetails = true;

          // Ensure Insulin Checkbox is checked when retrieving data
          this.followUpForm.patchValue({ insulin: true });

          // Ensure correct insulinType radio button is selected
          const insulinTypeValue = this.diabVisitMangDetails.insulinType;
          if (['I', 'S', 'M', 'P'].includes(insulinTypeValue)) {
            this.followUpForm.get('insulinType').setValue(insulinTypeValue);
          } else {
            this.followUpForm.get('insulinType').setValue(null); // Clear if invalid
          }

        } else {
          this.showFolloUpInsulinDetails = false;
          this.followUpForm.patchValue({ insulin: false, insulinType: null });
        }
      }, 500);


      //Pump Detalis

      let pumpYnValue = this.diabVisitMangDetails.pumpYn;
      this.followUpForm.get('pumpYn').setValue(pumpYnValue);

      if (pumpYnValue == 'Y') {
        let pumpTypeValue = this.diabVisitMangDetails.pumpType;
        this.followUpForm.get('pumpType').setValue(pumpTypeValue);
      } else {
        this.followUpForm.get('pumpType').setValue(" ");
      }
      if (this.diabVisitMangDetails.pumpType === 1561) {
        let pumpOtherValue = this.diabVisitMangDetails.pumpOther;
        this.followUpForm.get('pumpOther').setValue(pumpOtherValue);
      }


      //Sensor Detalis

      let sensorYnValue = this.diabVisitMangDetails.sensorYn;
      this.followUpForm.get('sensorYn').setValue(sensorYnValue);

      if (sensorYnValue === 'Y') {
        let sensorTypeValue = this.diabVisitMangDetails.sensorType;
        this.followUpForm.get('sensorType').setValue(sensorTypeValue);
      } else {
        this.followUpForm.get('sensorType').setValue(null); // Clear the field if sensorYn is 'N'
      }
      if (this.diabVisitMangDetails.sensorType === 1560) {
        let sensorOtherValue = this.diabVisitMangDetails.sensorOther;
        this.followUpForm.get('sensorOther').setValue(sensorOtherValue);
      }


    }


    if (data.rgTbDiabExam && data.rgTbDiabExam.length > 0) {
      this.diabVisitExamDetails = data.rgTbDiabExam[0];
      this.followUpForm.patchValue(this.diabVisitExamDetails)
    }

    this.followUpForm.patchValue(data)
    this.selectedFUDate = data.visitDate;
    this.selectedFollowUp = this.followUpForm.value;


  }
  getPrivMangDetails() {
    let visitIndex = this.retrivedData.rgTbDiabeticRegisterDtls.rgTbDiabVisit;

    // Sort visits by date
    visitIndex.sort((a, b) => new Date(a.visitDate).getTime() - new Date(b.visitDate).getTime());
    // Treat the newly added visit as current
    let currentIndex = visitIndex.length - 1;
    // Get the previous visit (if exists)
    let previousVisit = currentIndex > 0 ? visitIndex[currentIndex - 1] : null;
    if (previousVisit && previousVisit.regTbDiabMngmnt && previousVisit.regTbDiabMngmnt.length) {
      let mngDetails = previousVisit.regTbDiabMngmnt[0];


      let pumpYnValue = mngDetails.pumpYn;
      this.followUpForm.get('pumpYn').setValue(pumpYnValue);
      if (pumpYnValue === 'Y') {
        this.followUpForm.get('pumpType').setValue(mngDetails.pumpType);
      } else {
        this.followUpForm.get('pumpType').setValue(" ");
      }
      if (mngDetails.pumpType === 1561) {
        let pumpOtherValue = mngDetails.pumpOther;
        this.followUpForm.get('pumpOther').setValue(pumpOtherValue);
      }



      let sensorYnValue = mngDetails.sensorYn;
      this.followUpForm.get('sensorYn').setValue(sensorYnValue);

      if (sensorYnValue === 'Y') {
        let sensorTypeValueID = mngDetails.sensorType;
        this.followUpForm.get('sensorType').setValue(sensorTypeValueID);
      } else {
        this.followUpForm.get('sensorType').setValue(null);
      }
      if (mngDetails.sensorType === 1560) {
        let sensorOtherValue = mngDetails.sensorOther;
        this.followUpForm.get('sensorOther').setValue(sensorOtherValue);
      }



    } else {
      console.log("Step 10: No previous management details found");
    }
  }

  validateFields() {
    if (!this.patientDetails.validateNationality()) return false;
    // if (!this.patientDetails.validateFields()) return;
    if (!this.validatedData()) return;
    if (!this.insulinTypeValidation()) return;
    if (!this.heightValidation(this.diabeticRegistry.controls.hight.value, this.patientDetails.patientForm.controls.age.value)) return;

  }

  save() {
    this.submitted = true;

    // If it's a follow-up form submission
    if (this.selectedFUDate) {
      if (!this.validatedVisitData()) return;

      let indexControl = this.followUpForm.get('index');
      let index = indexControl ? indexControl.value : null;

      // Find the correct index based on the selectedFUDate
      if (index === null || index === undefined) {
        index = this.diabVisitDetails.findIndex(item =>
          this.cellRenderer(item.visitDate) === this.cellRenderer(this.selectedFUDate)
        );

        // If the date is not found in the list, it means it's a new entry
        if (index === -1) {
          index = this.diabVisitDetails ? this.diabVisitDetails.length : 0;
        }
      }

      this.addEditFollowUp(
        this.followUpForm.value,
        '',
        this.diabVisitDetails[index], // Correctly referencing the list
        index
      );
    }

    if (!this.patientDetails.validateNationality()) return false;
    if (!this.patientDetails.validateFields()) return;
    if (!this.validatedData()) return;
    if (!this.insulinTypeValidation()) return;
    if (!this.heightValidation(this.diabeticRegistry.controls.hight.value, this.patientDetails.patientForm.controls.age.value)) return;

    // Registration logic
    const register: TbDiabeticRegisterDtls = { ...this.diabeticRegistry.value };
    register.regDate = this.cellRendererBack(register.regDate);
    this.initialsFollowUpForm();
    this.followUpForm.patchValue(this.diabeticRegistry.value);
    let rVisit = _.cloneDeep(this.followUpForm.value);
    this.initialsFollowUpForm();
    rVisit.visitDate = this.cellRendererBack(this.diabeticRegistry.get('regDate').value);
    this.initialsMainList();

    // Check if Visit with Type 'F' and same Visit ID already exists
    if (this.diabticRegDetails) {
      rVisit.visitId = this.diabticRegDetails.visitId;
      rVisit.visitType = "R";  // Assign existing visitId to rVisit
    } else {
      rVisit.visitId = null;  // New visit
      rVisit.visitType = "R";
    }

    // Process Family History details
    let familyIdValue = this.diabeticRegistry.controls.family_Id.value;
    if (this.diabFamilyHistoryDetails && Object.keys(this.diabFamilyHistoryDetails).length > 0) {
      Object.assign(this.diabFamilyHistoryDetails, this.getFamilyDetails());
    } else {
      this.diabFamilyHistoryDetails = this.familyHistoryOptions.find(e => e.id == familyIdValue);

      if (this.diabFamilyHistoryDetails) {
        this.diabFamilyHistoryDetails = this.getFamilyDetails();
      }
    }
    let dFamilyHis = [];
    dFamilyHis.push(this.diabFamilyHistoryDetails);
    register.rgTbDiabFamilyHistory = dFamilyHis;


    // Process examination details
    if (this.diabExamDetails && Object.keys(this.diabExamDetails).length > 0) {
      Object.assign(this.diabExamDetails, this.getExamDetails());
    } else {
      this.diabExamDetails = this.getExamDetails();
    }
    rVisit.rgTbDiabExam = [this.diabExamDetails];

    // Process management details
    if (this.diabMngmntDetails && Object.keys(this.diabMngmntDetails).length > 0) {
      Object.assign(this.diabMngmntDetails, this.getManagementDetails());
    } else {
      this.diabMngmntDetails = this.getManagementDetails();
    }
    rVisit.regTbDiabMngmnt = [this.diabMngmntDetails];

    // Process Invest details
    const isAtLeastOneLabChecked = this.diabLabInvestList.some(
      item => item.checked === true
    );
    if (isAtLeastOneLabChecked) {
      rVisit.rgTbDiabInvst = [];
      this.diabLabInvestList.forEach(e => {
        if (e.checked) {
          let lab = {
            runId: e.runId ? e.runId : null,
            modifiedDate: e.modifiedDate ? e.modifiedDate : null,
            modifiedBy: e.modifiedBy ? e.modifiedBy : null,
            createdDate: e.createdDate ? e.createdDate : null,
            createdBy: e.createdBy ? e.createdBy : null,
            test_Id: e.id,
            lab_Result: e.lab_Result,
            remarks: e.remarks,
            visitId: e.visitId,
          };
          rVisit.rgTbDiabInvst.push(lab);
        }
      });
    }

    // Collect visit data
    let allVisit = [];
    if (this.diabVisitDetails && this.diabVisitDetails.length) {
      this.diabVisitDetails.forEach(el => {
        el.visitDate = this.cellRendererBack(this.cellRenderer(el.visitDate));
        if (el.diabLabInvestList && el.diabLabInvestList.length < 1) {
          el.diabLabInvestList = null;
        }
        allVisit.push(el);
      });
      allVisit.push({ ...rVisit });
    } else {
      allVisit.push(rVisit);
    }

    register['rgTbDiabVisit'] = allVisit;
    this.retrivedData.rgTbDiabeticRegisterDtls.civilId = this.patientDetails.patientForm.controls.civilId.value;
    this.retrivedData.rgTbDiabeticRegisterDtls = register;

    if (this.patientDetails.patientForm.valid) {
      this.retrivedData.rgTbPatientInfo = this.patientDetails.patientForm.value;
      this.retrivedData.rgTbPatientInfo.createdInstid = this.patientDetails.patientForm.controls.regInst.value;
      this.retrivedData.rgTbPatientInfo.dob = this.cellRendererBack(this.cellRenderer(this.retrivedData.rgTbPatientInfo.dob));
    }

    let data = _.cloneDeep(this.retrivedData);
    data.rgTbDiabeticRegisterDtls.civilID = this.patientDetails.patientForm.controls.civilId.value;


    this._diabeticService.saveDiabetic(data).subscribe(
      res => {
        if (res && res["code"] == AppUtils.RESPONSE_SUCCESS_CODE_SAVE && res["result"] !== null) {
          Swal.fire({
            icon: 'success',
            title: 'SAVED',
            text: 'Registered Successfully',
            showConfirmButton: false,
            timer: 2000,
          });
          let centralNo = res["result"];
          this.clear();
          this.getDiabRegistryDetails(centralNo, 'regId');
        } else {
          Swal.fire("Error", " Error occurred while saveing Diabetic details", "error");
        }
      },
      err => {

        Swal.fire('', 'Error occurred while retrieving details', 'error');

      }
    );
  }

  getFamilyDetails(): any {
    return {
      familyId: this.diabeticRegistry.controls.family_Id.value

    };
  }

  getExamDetails(): any {
    return {
      hight: this.diabeticRegistry.controls.hight.value,
      weight: this.diabeticRegistry.get('weight').value,
      bmi: this.diabeticRegistry.get('bmi').value,
      bp_Sys: this.diabeticRegistry.get('bp_Sys').value,
      bp_Dia: this.diabeticRegistry.get('bp_Dia').value,
      mode_Presentation: this.diabeticRegistry.get('mode_Presentation').value,
      diabetesType: this.diabeticRegistry.get('diabetesType').value
    };
  }

  onCheckboxChange(controlName: string, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (this.diabeticRegistry.get(controlName)) {
      this.diabeticRegistry.get(controlName).setValue(isChecked ? 'Y' : 'N');
    }
  }






  onFollowUpCheckboxChange(controlName: string, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (this.followUpForm && this.followUpForm.get(controlName)) {
      this.followUpForm.get(controlName).setValue(isChecked ? 'Y' : 'N');
    }
  }

  getManagementDetails() {
    const checkboxControls = ['lifestyleModify', 'ingGlptAgonist', 'oralHypoDrugs', 'insulin'];
    let updatedValues: any = {};

    checkboxControls.forEach(controlName => {
      const control = this.diabeticRegistry.get(controlName);
      updatedValues[controlName] = control && control.value ? 'Y' : 'N';
    });

    // Insulin Type Logic
    updatedValues['insulinType'] = updatedValues['insulin'] === 'Y' ? this.diabeticRegistry.controls.insulinType.value || '' : '';

    // Sensor Logic
    updatedValues['sensorYn'] = this.diabeticRegistry.controls.sensorYn.value || 'N';
    updatedValues['sensorType'] = updatedValues['sensorYn'] === 'Y' ? this.diabeticRegistry.controls.sensorType.value || '' : '';
    updatedValues['sensorOther'] = updatedValues['sensorType'] === 1560 ? this.diabeticRegistry.controls.sensorOther.value || '' : '';

    // Pump Logic
    updatedValues['pumpYn'] = this.diabeticRegistry.controls.pumpYn.value || 'N';
    updatedValues['pumpType'] = updatedValues['pumpYn'] === 'Y' ? this.diabeticRegistry.controls.pumpType.value || '' : '';
    updatedValues['pumpOther'] = updatedValues['pumpType'] === 1561 ? this.diabeticRegistry.controls.pumpOther.value || '' : '';


    return updatedValues;
  }

  updateCheckboxValue(controlName: string, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.diabeticRegistry.patchValue({ [controlName]: isChecked });

    if (controlName === 'insulin') {
      this.showInsulinDetails = isChecked;

      if (!isChecked) {
        // If insulin is unchecked, clear insulinType
        this.diabeticRegistry.patchValue({ insulinType: null });
      }
    }
  }


  updateFollowUpCheckboxValue(controlName: string, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.followUpForm.patchValue({ [controlName]: isChecked });

    if (controlName === 'insulin') {
      this.showFolloUpInsulinDetails = isChecked;

      if (!isChecked) {
        // If insulin is unchecked, clear insulinType
        this.followUpForm.patchValue({ insulinType: null });
      }
    }
  }



  callCase() {
    this._sharedService.setNavigationData(this.rgTbDiabeticRegisterDtls);
    this._router.navigate(['stages'], { state: { centralRegNo: this.rgTbDiabeticRegisterDtls.regNo } });
  }

  callTissue() {
    this._sharedService.setNavigationData(this.rgTbDiabeticRegisterDtls);
    this._router.navigate(['/tissueType'], { state: { centralRegNo: this.rgTbDiabeticRegisterDtls.regNo } });
  }


  addExam(type, examItem?) {
    if (type == AppUtils.DIABATIC_EYE_EXAM) {
      this.eyeExamValues = this.examParamsAll
      if (this.eyeExamForm.value && this.eyeExamForm.value.length > 0 && !examItem) {
        let emptyIndex = this.eyeExamForm.value.findIndex(el => !el.paramId);
        if (emptyIndex > -1) return
      }
      this.eyeExamForm = this.examForm.get('eyeExamForm') as FormArray;
      let eyeExamItem: any;
      if (examItem) {
        eyeExamItem = examItem;
      } else {
        eyeExamItem = { runId: null, lastUpdatedOn: null, paramId: null, rValue: null, lValue: null, remarks: null };
      }
      this.eyeExamData = Object.assign([], this.eyeExamForm.value);
      this.eyeExamData.push(eyeExamItem);
      this.eyeExamForm.push(this.fb.group(eyeExamItem));
    }

    if (type == AppUtils.DIABATIC_FOOT_EXAM) {
      this.footExamValues = this.examParamsAll
      if (this.footExamForm.value && this.footExamForm.value.length > 0 && !examItem) {
        let emptyIndex = this.footExamForm.value.findIndex(el => !el.paramId);
        if (emptyIndex > -1) return
      }
      this.footExamForm = this.examForm.get('footExamForm') as FormArray;
      let footExamItem: any;
      if (examItem) {
        footExamItem = examItem;
      } else {
        footExamItem = { runId: null, lastUpdatedOn: null, paramId: null, rValue: null, lValue: null, remarks: null };
      }
      this.footExamData = Object.assign([], this.footExamForm.value);
      this.footExamData.push(footExamItem);
      this.footExamForm.push(this.fb.group(footExamItem));
    }
  }

  onChange(type, event, row) {
    let prevId = event.id ? event.id : event[row].paramId ? event[row].paramId : null;
    if (type == AppUtils.DIABATIC_EYE_EXAM && prevId) {
      this.eyeExamValues = this.examParamsAll.filter(el => el.prevId == prevId)
    } else {
      this.eyeExamValues = [];
    }
    if (type == AppUtils.DIABATIC_FOOT_EXAM && prevId) {
      this.footExamValues = this.examParamsAll.filter(el => el.prevId == prevId)
    } else {
      this.footExamValues = [];
    }
  }

  deleteEmptyRows(data: any, type: any) {

    let indices: any[] = [];
    data.forEach((el, index) => {
      let isEmpty = true;
      Object.keys(el).forEach(key => {
        if (el[key] != null && el[key] != "") {
          isEmpty = false;
        }
      });

      if (isEmpty) {
        indices.push(index);
      }
    });

    if (indices.length > 0) {
      for (let i = indices.length; i > 0; i--) {
        data.splice(indices[i - 1]);
      }
    }

    if (type == AppUtils.DIABATIC_EYE_EXAM) { this.eyeExamData = data }
    if (type == AppUtils.DIABATIC_FOOT_EXAM) { this.footExamData = data }
  }

  delete(row, type) {
    Swal.fire({
      title: 'Are you sure',
      text: "You want to delete this record ?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {
        if (type == AppUtils.DIABATIC_EYE_EXAM) {
          this.eyeExamForm = this.examForm.get('eyeExamForm') as FormArray;
          let delRow = this.eyeExamData.indexOf(row);
          this.eyeExamData.splice(delRow, 1);
          this.eyeExamForm.removeAt(delRow);
        }
        if (type == AppUtils.DIABATIC_FOOT_EXAM) {
          this.footExamForm = this.examForm.get('footExamForm') as FormArray;
          let delRow = this.footExamData.indexOf(row);
          this.footExamData.splice(delRow, 1);
          this.footExamForm.removeAt(delRow);
        }
      }
    })
  }

}


