<h6>Renal Waiting List Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="renalWaitingListSearchForm" >
     <div class="row">
       <div class="col-lg-2 col-md-3 col-sm-3">
           <div class="form-group">
               <label>Civil Id</label>
               <input type="text" class="form-control form-control-sm" formControlName="civilId">
           </div>
       </div>
       <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
            <label>Registration No</label>
            <input type="text" class="form-control form-control-sm" formControlName="centralRegNo">
        </div>
    </div>
       <div class="col-lg-2 col-md-3 col-sm-3">
           <div class="form-group">
               <label>Gender</label>
               <select class="form-control form-control-sm" formControlName="sex"> 
                <option value="" disabled selected hidden>Select Gender</option>
                <option value="M"> Male</option>
                   <option value="F"> Female </option>
              </select>
           </div>
       </div>
 
       <div class="col-lg-2 col-md-3 col-sm-3">
           <div class="form-group">
               <label>Age (from)</label>
               <input type="text" class="form-control form-control-sm" formControlName="ageFrom">
           </div>
       </div>
       <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
            <label>To</label>
            <input type="text" class="form-control form-control-sm" formControlName="ageTo">
        </div>
    </div>
       <div class="col-lg-2 col-md-3 col-sm-3">
           <div class="form-group">
               <label>Region</label>
               <select formControlName="regCode" class="form-control form-control-sm" id="regionData"  
               name="regionData" (change)="regSelect($event,'region')" >
               <option selected [value]="null">All Region</option>
               <option [value]="res.regCode" *ngFor="let res of regionData" 
                  >{{res.regName}}</option>
           </select>
           </div>
       </div>
       <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
            <label>Wilayat</label>
            <select formControlName="walCode" 
            class="form-control form-control-sm"  (change)="walSelect($event,'wilayat')" >
                <option selected [value]="null">All Wilayat</option>
                <option [value]="res.walCode"  *ngFor="let res of wallayatListFilter"
                >{{res.walName}}</option>
            </select>
        </div>
    </div>
    <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
            <label>Institute</label>
            <select formControlName="estCode"  class="form-control form-control-sm"  >
                <option selected [value]="null">All Institute</option>
                <option [value]="res.estCode" *ngFor="let res of institeListFilter"
                >{{res.estName}}</option>
            </select>
        </div>
    </div>

    <div class="col-lg-4 col-md-3 col-sm-3">
        <div class="form-group">
          <label><strong>Blood Group</strong></label><br />
          <div class="chexkBoxList" style="padding-left: 30px;">
            <ng-container *ngFor="let bloodGroup of bloodGroupList">
              <input type="checkbox" [value]="bloodGroup.value" (change)="bloodGroupSelect($event, 'group')" /> {{
              bloodGroup.value }}
            </ng-container>
          </div>
        </div>
      </div>

       <div class="text-right col-lg-12 col-md-12 col-sm-12">
        
           <div class="btn-box">
               <button type="reset" (click)="clear($event)"class="btn btn-sm btn-secondary">Clear</button>
               <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
           </div>
       </div>
    </div>
    </form>
   </div>
 
   <div style="margin-top:20px">
    <ag-grid-angular
    style="width: 100%; height: 421px;"
    class="ag-theme-balham"
    [rowData]="rowData"
    [columnDefs]="columnDefs"
    (rowDoubleClicked)="onCellDoubleClicked($event)"
    [gridOptions]="gridOptions"
    >
</ag-grid-angular>

</div>