import { Component, OnInit } from '@angular/core';
import { HeartService } from '../heart.service';
import { FormControl, FormGroup } from '@angular/forms';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { WallayatDataModel } from 'src/app/common/objectModels/wallayat-model';
import { HeartDashboard, HeartDashboardDisplay } from 'src/app/_models/heart-dashboard.model';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import * as AppComponent from '../../common/app.component-utils';
import { ageRangeValidator } from 'src/app/_helpers/common.constants';
@Component({
  selector: 'flst',
  templateUrl: './heart-dashboard.component.html',
  styleUrls: ['./heart-dashboard.component.scss'],
  providers: [HeartService]
})
export class HeartDashboardComponent implements OnInit {
  boardForm: FormGroup;
  regionData: RegionDataModel[] = [];
  wallayatList: WallayatDataModel[] = [];
  wallayatListFilter: WallayatDataModel[] = [];
  institeList: any[] = [];
  institeListFilter: any[] = [];

  dashboardDataDB: HeartDashboard[] = [];
  dashboardDataFilter: HeartDashboard[] = [];
  durationList = AppComponent.duration;
  filterType: 'institute' | 'wilayat' | 'region' | 'all' = 'all';
  filterTitle: string = 'All Regions';
  pieOption: any;
  options: any;
  charBGColor: string[];
  createTime: string | null = null;

  icdPieChart: any;
  recTransplantPieChart: any;
  urgentTransplantPieChart: any;
  icdHeartShortList: any;
  nyhaClassList: any;
  primaryDiagList: any;
  PhyBaselineList: any;
  psyClearanceList: any;
  heartTransIndications: any;
  mcsDeviceUsedPieChart: any;
  inotropeYnPieChart: any;
  durationChart: any;
  nyhaClassChart: any;
  primaryDiagChart: any;
  phyBaselineChart: any;
  psyClearanceChart: any;
  constructor(private _heartService: HeartService, private _masterService: MasterService, private _sharedService: SharedService) {
    this.initForm();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit(): void { }

  private initForm(): void {
    this.boardForm = new FormGroup({
      regCode: new FormControl(null),
      walCode: new FormControl(null),
      estCode: new FormControl(null),
      ageF: new FormControl(null),
      ageT: new FormControl(null),
    }, { validators: ageRangeValidator });
  }

  private getMasterData(regCode: any = 0, walCode: any = 0): void {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    });

    this._masterService.getIcdHeartShortList();
    this._masterService.icdHeartShortList.subscribe(value => {
      this.icdHeartShortList = value;
    });

    this._masterService.getAllHeartNyhaClass().subscribe((value) => {
      this.nyhaClassList = value['result'];
    });

    this._masterService.getAllPrimaryEtiology().subscribe((value) => {
      this.primaryDiagList = value['result'];
    });

    this._masterService.getHeartPhysioBaseline().subscribe((value) => {
      this.PhyBaselineList = value['result'];
    });
    this._heartService.getAllHeartPsClearance().subscribe((res) => {
        this.psyClearanceList = res["result"];
    });
    
  }

  private isEmptyNullZero(val: any): boolean {
    return !(val === undefined || val === null || val === 'null' || val <= 0);
  }

  private getCodeValue(val: any, key: string): any {
    if (val && typeof val === 'object') {
      return val[key];
    }
    return val;
  }

  locSelect(event: any, field?: 'region' | 'wilayat'): void {
    const body = this.boardForm.value;

    if (field === 'region') {
      if (!event) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.boardForm.patchValue({ walCode: null, estCode: null });
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode === event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode === event.regCode);
        this.boardForm.patchValue({ walCode: null, estCode: null });
      }
    } else if (field === 'wilayat') {
      if (!event) {
        if (body['regCode'] != null) {
          const regCodeVal = this.getCodeValue(body['regCode'], 'regCode');
          this.institeListFilter = this.institeList.filter(s => s.regCode === regCodeVal);
        } else {
          this.institeListFilter = this.institeList;
        }
        this.boardForm.patchValue({ estCode: null });
      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode === event.walCode);
        this.boardForm.patchValue({ estCode: null });
      }
    }
  }

  getDashboardData(): void {
    this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    this._heartService.getDashboard().subscribe(res => {
      if (res['code'] == "S0000" || res['code'] == "F0000") {
        this.dashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'HEART', this.institeList);

        for (let i = 0; i < this.dashboardDataDB.length; i++) {
          this.dashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.dashboardDataDB[i].dob);
        }
      }
      this.applyFilters();
    });
  }

  callReset(): void {
    window.location.reload();
  }

  callFilter(): void {
    if (!this.dashboardDataDB || this.dashboardDataDB.length === 0) {
      this.getDashboardData();
    } else {
      this.applyFilters();
    }
  }

  private applyFilters(): void {
    const body = this.boardForm.value;
    this.dashboardDataFilter = [...this.dashboardDataDB];

    this.applyLocationFilters(body);

    this.displayFilterType();
    this.callChart();
  }

  private applyLocationFilters(body: any): void {
    const {
      ageF = null,
      ageT = null,
      estCode = null,
      walCode = null,
      regCode = null
    } = body || {};
    if (this.isEmptyNullZero(ageF) && this.isEmptyNullZero(ageT) && ageF <= ageT) {
      this.dashboardDataFilter = this.dashboardDataDB.filter(s => s.age >= ageF && s.age <= ageT);
    } else {
      this.dashboardDataFilter = this.dashboardDataDB;
    }

    const estCodeVal = this.getCodeValue(estCode, 'estCode');
    const walCodeVal = this.getCodeValue(walCode, 'walCode');
    const regCodeVal = this.getCodeValue(regCode, 'regCode');

    if (estCodeVal != null && estCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.estCode) === String(estCodeVal));
      this.filterType = 'institute';
    } else if (walCodeVal != null && walCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.walCode) === String(walCodeVal));
      this.filterType = 'wilayat';
    } else if (regCodeVal != null && regCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.regCode) === String(regCodeVal));
      this.filterType = 'region';
    } else {
      this.filterType = 'all';
    }
  }

  displayFilterType(): void {
    const formValue = this.boardForm.value;

    switch (this.filterType) {
      case 'institute': {
        const est =
          formValue.estCode && typeof formValue.estCode === 'object'
            ? formValue.estCode
            : (this.institeList ? this.institeList.find(s => String(s.estCode) === String(formValue.estCode)) : null);
        this.filterTitle = est ? est.estName : 'Selected Institute';
        break;
      }
      case 'wilayat': {
        const wal =
          formValue.walCode && typeof formValue.walCode === 'object'
            ? formValue.walCode
            : (this.wallayatList ? this.wallayatList.find(s => String(s.walCode) === String(formValue.walCode)) : null);
        this.filterTitle = wal ? `All Institutes under ${wal.walName}` : 'Selected Wilayat';
        break;
      }
      case 'region': {
        const reg =
          formValue.regCode && typeof formValue.regCode === 'object'
            ? formValue.regCode
            : (this.regionData ? this.regionData.find(s => String(s.regCode) === String(formValue.regCode)) : null);
        this.filterTitle = reg ? `All Wilayat under ${reg.regName}` : 'Selected Region';
        break;
      }
      default:
        this.filterTitle = 'All Regions';
    }
  }

  callChart(): void {
    this.icdPieChart = null;
    this.recTransplantPieChart = null;
    this.urgentTransplantPieChart = null;
    this.inotropeYnPieChart = null;
    this.mcsDeviceUsedPieChart = null;
    this.durationChart = null;
    this.nyhaClassChart = null;
    this.primaryDiagChart = null;
    this.phyBaselineChart = null;
    this.psyClearanceChart = null;
    const transplantData = this.dashboardDataFilter
      .filter(item => item != null && item.recTransYn != null)
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.recTransYn
      }));

    const urgentTransplantData = this.dashboardDataFilter
      .filter(item => item != null && item.urgentTransYn != null)
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.urgentTransYn
      }));

    const icdData = this.dashboardDataFilter
      .filter(item => item != null && item.icd != null && item.icd !== '')
      .map(item => {
        let icdName = item.icd;
        if (this.icdHeartShortList && item.icd) {
          const icdItem = this.icdHeartShortList.find(icdData => icdData.code == item.icd);
          icdName = icdItem ? icdItem.disease : item.icd;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(icdName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const inotropeYnData = this.dashboardDataFilter
      .filter(item => item != null && item.inotropeYn != null)
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.inotropeYn
      }));

    const mcsDeviceUsedData = this.dashboardDataFilter
      .filter(item => item != null && item.mcsDeviceUsed != null && item.mcsDeviceUsed !== '')
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.mcsDeviceUsed
      }));

    const durationData  = this.dashboardDataFilter
      .filter(item => item != null && item.duration != null && item.duration !== '')
      .map(item => {
        let durationName = item.duration;
        if (this.durationList && item.duration) {
          const durationItem = this.durationList.find(durationData => durationData.id == item.duration);
          durationName = durationItem ? durationItem.value : item.duration;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(durationName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const nyhaClassData  = this.dashboardDataFilter
      .filter(item => item != null && item.nyhaClass != null && item.nyhaClass !== 0)
      .map(item => {
        let nyhaClassName = item.nyhaClass;
        if (this.nyhaClassList && item.nyhaClass) {
          const nyhaClassItem = this.nyhaClassList.find(nyhaClassData => nyhaClassData.paramId == item.nyhaClass);
          nyhaClassName = nyhaClassItem ? nyhaClassItem.paramName : item.nyhaClass;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(nyhaClassName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const primaryDiagData  = this.dashboardDataFilter
      .filter(item => item != null && item.primaryDiag != null && item.primaryDiag !== 0)
      .map(item => {
        let primaryDiagName = item.primaryDiag;
        if (this.primaryDiagList && item.primaryDiag) {
          const primaryDiagItem = this.primaryDiagList.find(primaryDiagData => primaryDiagData.paramId == item.primaryDiag);
          primaryDiagName = primaryDiagItem ? primaryDiagItem.paramName : item.primaryDiag;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(primaryDiagName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const phyBaselineData  = this.dashboardDataFilter
      .filter(item => item != null && item.physiotherapyBase != null && item.physiotherapyBase !== 0)
      .map(item => {
        let phyBaselineName = item.physiotherapyBase;
        if (this.PhyBaselineList && item.physiotherapyBase) {
          const phyBaselineItem = this.PhyBaselineList.find(phyBaselineData => phyBaselineData.paramId == item.physiotherapyBase);
          phyBaselineName = phyBaselineItem ? phyBaselineItem.paramName : item.physiotherapyBase;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(phyBaselineName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const psyClearanceData  = this.dashboardDataFilter
      .filter(item => item != null && item.psychosocialClearance != null && item.psychosocialClearance !== 0)
      .map(item => {
        let psyClearanceName = item.psychosocialClearance;
        if (this.psyClearanceList && item.psychosocialClearance) {
          const psyClearanceItem = this.psyClearanceList.find(psyClearanceData => psyClearanceData.paramId == item.psychosocialClearance);
          psyClearanceName = psyClearanceItem ? psyClearanceItem.paramName : item.psychosocialClearance;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(psyClearanceName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    if (transplantData && transplantData.length > 0) {
      this.callPieChart(transplantData, 'recTransplantPieChart');
    }
    if (urgentTransplantData && urgentTransplantData.length > 0) {
      this.callPieChart(urgentTransplantData, 'urgentTransplantPieChart');
    }
    if (icdData && icdData.length > 0) {
      this.callBarChart(icdData, 'icdPieChart', 'Y');
    }
    if (inotropeYnData && inotropeYnData.length > 0) {
      this.callPieChart(inotropeYnData, 'inotropeYnPieChart');
    }
    if (mcsDeviceUsedData && mcsDeviceUsedData.length > 0) {
      this.callPieChart(mcsDeviceUsedData, 'mcsDeviceUsedPieChart');
    }
    if (durationData && durationData.length > 0) {
      this.callBarChart(durationData, 'durationChart', 'N');
    }
    if (nyhaClassData && nyhaClassData.length > 0) {
      this.callBarChart(nyhaClassData, 'nyhaClassChart', 'N');
    }
    if (primaryDiagData && primaryDiagData.length > 0) {
      this.callBarChart(primaryDiagData, 'primaryDiagChart', 'N');
    }
    if (phyBaselineData && phyBaselineData.length > 0) {
      this.callBarChart(phyBaselineData, 'phyBaselineChart', 'N');
    }
    if (psyClearanceData && psyClearanceData.length > 0) {
      this.callBarChart(psyClearanceData, 'psyClearanceChart', 'N');
    }
  }

  callBarChart(listData: HeartDashboardDisplay[], chartData: any, withNull: 'Y' | 'N'): void {
    if (!listData || listData.length === 0) {
      return;
    }
    const includeNull = withNull === 'Y';
    const { labels: charlabels, data: charData } = this.buildCounts<HeartDashboardDisplay>(
      listData,
      (x: any) => (x ? (x as any).value : null),
      includeNull,
      (val: any) => String(val)
    );

    if (charlabels.length === 0 || charData.length === 0) {
      return;
    }

    if (chartData === 'icdPieChart') {
      this.icdPieChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      }
    } else if (chartData === 'durationChart') {
      this.durationChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      }
    } else if (chartData === 'nyhaClassChart') {
      this.nyhaClassChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      } 
    } else if (chartData === 'primaryDiagChart') {
      this.primaryDiagChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      } 
    } else if (chartData === 'phyBaselineChart') {
      this.phyBaselineChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      };
    }
    else if (chartData === 'psyClearanceChart') {
      this.psyClearanceChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      };
    }

  }

  callPieChart(listData: HeartDashboardDisplay[], chartData: any): void {
    if (!listData || listData.length === 0) {
      return;
    }

    const { labels: charlabels, data: charData } = this.buildCounts<HeartDashboardDisplay>(
      listData,
      (x: any) => (x ? (x as any).value : null),
      false,
      (val: any) => (val === 'Y' ? 'Yes' : 'No')
    );
    if (charlabels.length === 0 || charData.length === 0) return;


    const chartConfig = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }
      ]
    };

    if (chartData === 'recTransplantPieChart') {
      this.recTransplantPieChart = chartConfig;
    } else if (chartData === 'urgentTransplantPieChart') {
      this.urgentTransplantPieChart = chartConfig;
    } else if (chartData === 'inotropeYnPieChart') {
      this.inotropeYnPieChart = chartConfig;
    } else if (chartData === 'mcsDeviceUsedPieChart') {
      this.mcsDeviceUsedPieChart = chartConfig;
    } 
  }

  private buildCounts<T>(
    items: T[],
    getValue: (item: T) => any,
    includeNull: boolean,
    labelFormatter?: (value: any) => string
  ): { labels: string[]; data: number[] } {
    const counts = new Map<any, number>();
    for (const it of items) {
      const raw = getValue(it);
      if (!includeNull && (raw === null || raw === undefined)) continue;
      const key = raw;
      counts.set(key, (counts.get(key) || 0) + 1);
    }
    const labels: string[] = [];
    const data: number[] = [];
    for (const [key, count] of counts.entries()) {
      labels.push(labelFormatter ? labelFormatter(key) : String(key));
      data.push(count);
    }
    return { labels, data };
  }
}
