import { Injectable } from '@angular/core';
import { InstituteDataModel } from '../common/objectModels/institute-model';
import { HttpResponse, HttpClient, HttpErrorResponse } from '@angular/common/http';
import * as AppUtils from '../common/app.utils';
import { RoleDataModel } from '../common/objectModels/role-model';
import { StatusModel } from '../common/objectModels/status-model';
import { NationalityDataModel } from '../common/objectModels/nationality-model';
import { RegionDataModel } from '../common/objectModels/region-model';
import { WallayatDataModel } from '../common/objectModels/wallayat-model';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/forkJoin';
import 'rxjs/add/operator/map'
import 'rxjs/add/operator/catch'
import 'rxjs/add/operator/publishReplay'
import { environment } from 'src/environments/environment';
import * as CommonConstants from '../_helpers/common.constants';


@Injectable({
  providedIn: 'root'
})
export class MasterDataService {
  private instituteData: Observable<InstituteDataModel[]>;
  private roleData: Observable<RoleDataModel[]>;
  private statuses: Observable<StatusModel[]>;
  private nationalityData: Observable<NationalityDataModel[]>;
  private regionData: Observable<RegionDataModel[]>;
  private wallayatData: Observable<WallayatDataModel[]>;
  private notificationType: Observable<any[]>;

  constructor(private _http: HttpClient) { }
  getApplicationSpecification() {
    const allStatus = this.getAllStatus();
    const institutes = this.getInstitute();
    const regions = this.getRegion();
    const nationalities = this.getNationality();
    const wallayat = this.getWallayat();


    return Observable.forkJoin([allStatus, institutes, regions, nationalities, wallayat]);
  
  }

  
  getNationality(): Observable<NationalityDataModel[]> {
    return this._http.get(AppUtils.NATIONALITY_MASTER)
      .map(this.extractData).catch(this.handleError);
  }

 
  getRegion(): Observable<RegionDataModel[]> {
    this.regionData = this._http.get(AppUtils.REGION_MASTER)
      .map(this.extractData).catch(this.handleError);
    return this.regionData;
  }
  getWallayat(): Observable<WallayatDataModel[]> {
    if (this.wallayatData !== undefined) {
      return this.wallayatData;
    } else {
      this.wallayatData = this._http.get(AppUtils.WALLAYAT_MASTER)
        .map(this.extractData).catch(this.handleError);
      return this.wallayatData;
    }

  }

  getAllStatus(): Observable<StatusModel[]> {
    if (!this.statuses) {
      this.statuses = this._http.get(AppUtils.GET_ALL_STATUS)
        .map(this.extractData)
        .publishReplay()
        .refCount();
    }

    return this.statuses;



  }
  
  getInstitute(): Observable<InstituteDataModel[]> {
    if (!this.instituteData) {
      this.instituteData = this._http.get(environment.centralAuthenticationHost + CommonConstants.GET_INSTITUTE)
        .map(this.extractData)
    }
    return this.instituteData;
  }

  // For User management
  getRole(): Observable<RoleDataModel[]> {
    const headers = new Headers();
    headers.append("Content-Type", "application/json");
    headers.append(CommonConstants.HEADER_AUTHENTICATION, CommonConstants.TOKEN_BEARER + ' ' + localStorage.getItem(CommonConstants.STORAGE_ACCOUNT_ACCESS_TOKEN));

    if (this.roleData !== undefined) {

      return this.roleData;
    } else {
      this.roleData = <any>this._http.get(environment.centralAuthenticationHost + CommonConstants.GET_ROLE + CommonConstants.SYSTEM_ID).catch(this.handleError);

      return this.roleData;

    }
  }

  getNotificationTypes(): Observable<any[]> {
    if (!this.notificationType) {
        this.notificationType = this._http.get(AppUtils.GET_NOTIFICATION_TYPES)
            .map(this.extractData)
            .publishReplay()
            .refCount();
    }
    return this.notificationType;
}

getNotificationT(): Observable<any> {
  
    return  this._http.get(AppUtils.GET_NOTIFICATION_TYPES);
  

}



  private handleError(error: HttpErrorResponse) {
    return Observable.throw(error);
  }

  private extractData(res: HttpResponse<any>) {
    const body = res;
    return body['result'] || {};
  }


 
}
