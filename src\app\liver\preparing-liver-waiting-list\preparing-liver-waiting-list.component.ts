import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { SharedService } from '../../_services/shared.service';
import { LiverService } from '../liver.service';
import * as AppUtils from '../../common/app.utils';
import {  FormBuilder, FormGroup } from '@angular/forms';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { GridNgSelectDataComponent } from '../../common/agGridComponents/grid-ngSelect-data.component';
import * as GridUtils from '../../common/agGridComponents/app.grid-spec-utils';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import * as AppCompUtils from '../../common/app.component-utils';
import { ButtonRendererComponent } from '../../common/agGridComponents/ButtonRendererComponent';
import Swal from 'sweetalert2';
import * as CommonConstants from '../../_helpers/common.constants';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LiverWaitingList } from 'src/app/_models/liver-waiting-list.model';
import { LiverWaitingListFrom } from 'src/app/_models/liver-waiting-list-form.models';

@Component({
  selector: 'app-preparing-liver-waiting-list',
  templateUrl: './preparing-liver-waiting-list.component.html',
  styleUrls: ['./preparing-liver-waiting-list.component.scss'],
  providers: [LiverService],
})
export class PreparingLiverWaitingListComponent implements OnInit {
  patientForm: FormGroup;
  @Input() scoringForm: FormGroup;
  regId: any;
  liverWaitingList: Array<LiverWaitingList> = [];
  saveList: Array<LiverWaitingList> = new Array<LiverWaitingList>();
  formData: LiverWaitingListFrom;
  rowData: Array<LiverWaitingList> = new Array<LiverWaitingList>();
  dbData: Array<LiverWaitingList>;
  isDisabled: true;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  age: any;
  private waitingListGridApi: GridApi;
  private waitingListGridColApi: ColumnApi;
  indices: any[] = [];
  frameworkComponents;
  columnDefs;
  readyTransplantYnValue: any;
  waitingListGrid: GridOptions;
  submitted = false;
  grids: any = [];
  loginId: any;
  scoresStatus: any = null;
  yesNo = AppCompUtils.YES_NO;
  today = new Date();
  callLiverWaitingLisitng: boolean = true;
  isRecommendTransplantNo: boolean = false;
  isUrgentTransplantYes: boolean = false;

  constructor(private fb: FormBuilder, private _router: Router, private _sharedService: SharedService, private _liverService: LiverService, private _formBuilder: FormBuilder, private _modalService: NgbModal) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode;
    this.getGrids();

    if (this._sharedService.getNavigationData()) {
      this.getList(this._sharedService.getNavigationData().centralRegNo);
      this._sharedService.setNavigationData(null);
    }

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };
  }

  private getGrids() {
    this.columnDefs = [
      { field: 'runId', hide: true },
      {
        headerName: 'No Prev Transplant',
        field: 'noPrevTransplant',
        minWidth: 125,
        editable: true,
        cellEditor: 'agTextCellEditor'
      },
      {
        headerName: 'Recommend for TransplantYn', field: 'recommendTransplantYn', cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
      },
      {
        headerName: 'Poor Performance Status', field: 'poorPerformanceYn',
        cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
        hide: !this.isRecommendTransplantNo
      },
      {
        headerName: 'Comorbidities', field: 'comorbidityYn', cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
        hide: !this.isRecommendTransplantNo
      },
      {
        headerName: 'Beyond Transplant Criteria for HCC', field: 'beyondHccCriteriaYn', cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
        hide: !this.isRecommendTransplantNo
      },
      {
        headerName: 'Other Reason', field: 'noRecOtherReason', editable: true, width: 200
      },
      {
        headerName: 'Urgent Transplant Needed', field: 'transplantUrgentYn', cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
      },
      {
        headerName: 'Reason', field: 'urgentTransplantReason', editable: true, width: 230, hide: !this.isUrgentTransplantYes
      },
      {
        headerName: 'Availability of Donor', field: 'donorAvailableYn', cellEditor: 'agSelectCellEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo.map(option => option.id)
        },
        width: 200,
        valueFormatter: params => {
          const option = this.yesNo.find(option => option.id === params.value);
          return option ? option.value : params.value;
        },
        hide: !this.isUrgentTransplantYes
      },
      { field: 'enteredBy', hide: true },
      { field: 'entryDate', hide: true },
      { field: 'modifiedDate', hide: true },
      { field: 'modifiedBy', hide: true },
      { field: 'runId', hide: true },
      {
        headerName: 'Action',
        cellRenderer: 'buttonRenderer',
        cellRendererParams: {
          onClick: this.ConfirmdeleteWaitingList.bind(this),
          label: 'Delete'
        }
      },
    ];

    this.waitingListGrid = <GridOptions>{
      enableColResize: true,
      pagination: true,
      paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
      onGridSizeChanged: () => {
        this.waitingListGrid.api.sizeColumnsToFit();
      }
    };
  }

  onCellValueChanged(params: any): void {
    const toggleColumns = {
      recommendTransplantYn: ['poorPerformanceYn', 'comorbidityYn', 'beyondHccCriteriaYn'],
      transplantUrgentYn: ['donorAvailableYn', 'urgentTransplantReason']
    };

    if (params.colDef.field === 'recommendTransplantYn') {
      this.isRecommendTransplantNo = params.newValue === 'N';
      this.toggleColumnVisibility(toggleColumns.recommendTransplantYn, this.isRecommendTransplantNo);
    }

    if (params.colDef.field === 'transplantUrgentYn') {
      this.isUrgentTransplantYes = params.newValue === 'Y';
      this.toggleColumnVisibility(toggleColumns.transplantUrgentYn, this.isUrgentTransplantYes);
    }
  }

  updateGridColumnsVisibility(): void {
    const isRecommendTransplantNo = this.liverWaitingList.some(item => item.recommendTransplantYn === 'N');
    const isUrgentTransplantYes = this.liverWaitingList.some(item => item.transplantUrgentYn === 'Y');

    this.toggleColumnVisibility(['poorPerformanceYn', 'comorbidityYn', 'beyondHccCriteriaYn'], isRecommendTransplantNo);
    this.toggleColumnVisibility(['donorAvailableYn', 'urgentTransplantReason'], isUrgentTransplantYes);
  }

  toggleColumnVisibility(columns: string[], show: boolean): void {
    this.waitingListGrid.columnApi.setColumnsVisible(columns, show);
    this.waitingListGrid.api.refreshHeader();
  }

  ConfirmdeleteWaitingList(e) {
    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: 'btn btn-success',
        cancelButton: 'btn btn-danger'
      },
      buttonsStyling: false
    });

    swalWithBootstrapButtons.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      reverseButtons: true
    }).then((result) => {
      if (!result.dismiss) {
        this.deleteWaitingList(e);
      } else {
        swalWithBootstrapButtons.fire(
          'Cancelled',
          'Your record is safe :)',
          'error'
        );
      }
    });
  }

  deleteWaitingList(e) {
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === 'waitingListGrid');
    let pushData = [];

    if (e.rowData.runId != null) {
      this._liverService.deleteLiverWaitingList(e.rowData.runId).subscribe(res => {
        if (res['code'] !== 0) {
          Swal.fire('Success', res['message'], 'success');
        } else {
          Swal.fire('Error', res['message'], 'error');
        }
      });
    }

    let focusedNode = this.waitingListGridApi.getSelectedRows();
    this[gridObject['gridApi']].updateRowData({ remove: focusedNode });
    this[gridObject['gridApi']].redrawRows();

    let updatedRowData = [];
    let renderedNodes = this[gridObject['gridApi']].getRenderedNodes();

    if (renderedNodes.length > 0) {
      renderedNodes.forEach(el => {
        let data = el['data'];
        updatedRowData.push(data);
      });
    }

    this.rowData = updatedRowData;
  }

  ngOnInit() {
  }

  ngAfterViewInit(): void {
    this.patientDetails.patientForm.disable();
    if (this.waitingListGrid && this.waitingListGrid.api) {
      console.log('Grid initialized:', this.waitingListGrid.api);
    }
  }

  addRec(grid: any) {
    if (this.formData) {
      let colObject = {};

      if (grid === 'waitingListGrid') {
        colObject = {
          "runId": null,
          "noPrevTransplant": null,
          "recommendTransplantYn": null,
          "poorPerformanceYn": null,
          "comorbidityYn": null,
          "beyondHccCriteriaYn": null,
          "noRecOtherReason": null,
          "transplantUrgentYn": null,
          "urgentTransplantReason": null,
          "donorAvailableYn": null,
          "enteredBy": null,
          "entryDate": null,
          "modifiedDate": null,
          "modifiedBy": null,
          "rgTbRegistryPatient": { "centralRegNo": null },
        }
      }

      let gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);

      const parentModel = gridObject['parentModel']
      const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();
      let nullRowCnt = 0;
      let nullMandatoryFieldCnt = 0;
      if (renderedNodes.length > 0) {
        renderedNodes.forEach((element, index) => {
          const data = element['data'];
          let isEmpty = true;

          Object.keys(data).forEach(el => {
            if (data[el] === "") {
              data[el] = null;
            }
            if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
              if (this.isObject(data[el])) {
                Object.keys(data[el]).forEach(elChild => {
                  if (data[el][elChild]) {
                    isEmpty = false;
                  }
                })
              } else {
                isEmpty = false;
              }
            }
            if (gridObject['mandatoryColumns']) {
              gridObject['mandatoryColumns'].forEach(mandatoryField => {
                if (el === mandatoryField) {
                  if (data[el] && data[el] !== "") {
                    if (this.isObject(data[el])) {
                      Object.keys(data[el]).forEach(elChild => {
                        if (!data[el][elChild]) {
                          nullMandatoryFieldCnt++;
                        }
                      })
                    }
                  } else {
                    nullMandatoryFieldCnt++;
                  }
                }
              })
            }
          })
          if (isEmpty) {
            nullRowCnt++;
          }
        })
      }

      if ((nullRowCnt === 0 && nullMandatoryFieldCnt === 0) || renderedNodes.length === 0) {
        this[gridObject['gridApi']].updateRowData({ add: [colObject] });
        this[gridObject['gridApi']].redrawRows();
        this[gridObject['gridApi']].forEachNode((node) => {
          if (node.lastChild) {
            node.setSelected(true);
            this[gridObject['gridApi']].ensureNodeVisible(node);
          } else {
            node.setSelected(false);
          }
        });
      }
    } else {
      Swal.fire(
        'Warning',
        'Add Registration No. to able to add! ',
        'warning'
      )
    }
  }

  isObject(val) {
    return (typeof val === 'object');
  }

  saveWaitingList(grid) {
    this.waitingListGrid.api.stopEditing();

    if (this.formData) {
      let nullMandatoryFieldCnt = 0;
      let nullRowCnt = 0;
      let validRowFound = false;
      let gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);
      const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();

      if (!this.rowData) {
        this.rowData = [];
      }

      if (renderedNodes.length === 0) {
        Swal.fire('Warning', 'No data found in the grid to save!', 'warning');
        return;
      }

      let invalidRows = [];

      renderedNodes.forEach((element, index) => {
        const data = element['data'];
        if (!data) {
          console.warn(`No data found for row at index ${index}`);
          return;
        }

        let isEmpty = true;

        if (data.noPrevTransplant === null || data.noPrevTransplant === "") {
          if (gridObject['mandatoryColumns'] && gridObject['mandatoryColumns'].includes("noPrevTransplant")) {
            nullMandatoryFieldCnt++;
            invalidRows.push({ index, message: `"No Prev Transplant" is mandatory and cannot be empty.` });
            return;
          }
        } else if (isNaN(data.noPrevTransplant) || Number(data.noPrevTransplant) <= 0) {
          invalidRows.push({ index, message: `"No Prev Transplant" must be a valid positive number.` });
          return;
        }

        Object.keys(data).forEach(el => {
          if (data[el] === "") {
            data[el] = null;
          }

          if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
            isEmpty = false;
          }

          if (gridObject['mandatoryColumns']) {
            gridObject['mandatoryColumns'].forEach(mandatoryField => {
              if (el === mandatoryField && (!data[el] || data[el] === "")) {
                nullMandatoryFieldCnt++;
              }
            });
          }
        });

        if (isEmpty) {
          nullRowCnt++;
          console.warn('Skipping empty row at index', index);
          return;
        }

        if (data["runId"] == null || data["runId"] !== null) { // Check for both new and existing rows
          validRowFound = true;
        }

        if (data["runId"] == null) {
          this.rowData.push(data);
        }
      });

      if (invalidRows.length > 0) {
        let errorMessage = 'Validation Errors:\n';
        invalidRows.forEach(row => {
          errorMessage += `Row ${row.index + 1}: ${row.message}\n`;
        });
        Swal.fire('Validation Errors', errorMessage, 'error');
        return;
      }

      if (!validRowFound) {
        Swal.fire('Warning', 'No valid data found to save!', 'warning');
        return;
      }

      for (let i = 0; i < this.rowData.length; i++) {
        if (this.rowData[i].runId === null) {
          this.rowData[i].rgTbRegistryPatient.centralRegNo = this.formData.centralRegNo;
        } else {
          this.dbData.filter(x => x.runId == this.rowData[i].runId).map((data) => {
            if (this.rowData[i].noPrevTransplant !== data.noPrevTransplant ||
              this.rowData[i].beyondHccCriteriaYn !== data.beyondHccCriteriaYn ||
              this.rowData[i].comorbidityYn !== data.comorbidityYn ||
              this.rowData[i].donorAvailableYn !== data.donorAvailableYn ||
              this.rowData[i].noRecOtherReason !== data.noRecOtherReason ||
              this.rowData[i].poorPerformanceYn !== data.poorPerformanceYn ||
              this.rowData[i].recommendTransplantYn !== data.recommendTransplantYn ||
              this.rowData[i].transplantUrgentYn !== data.transplantUrgentYn ||
              this.rowData[i].urgentTransplantReason !== data.urgentTransplantReason) {

              if (this.rowData[i].recommendTransplantYn === 'Y') {
                this.rowData[i].poorPerformanceYn = null;
                this.rowData[i].comorbidityYn = null;
                this.rowData[i].beyondHccCriteriaYn = null;
              }
              if (this.rowData[i].transplantUrgentYn === 'N') {
                this.rowData[i].donorAvailableYn = null;
                this.rowData[i].urgentTransplantReason = null;
              }
            }
          })
        }

        this.saveList = this.rowData.map(x => Object.assign({}, x));
      }

      for (let i = 0; i < this.saveList.length; i++) {
        this.saveList[i].rgTbRegistryPatient = { centralRegNo: this.formData.centralRegNo };
      }

      this._liverService.saveLiverWaitingList(this.saveList).subscribe(res => {
        if (res['code'] == 0) {
          Swal.fire('Saved!', 'Liver Waiting List has been Saved.', 'success');
          this.regId = res["result"];
          this.search();
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }, err => {
        Swal.fire('Error!', 'Error occurred while saving Liver Waiting List: ' + err.message, 'error');
      });

    } else {
      Swal.fire('Warning', 'No Record Found to Save!', 'warning');
    }
  }


  onReady(params, grid) {
    if (this.grids.length > 0) {
      const exist = this.grids.find(item => grid === item);
      if (!exist) {
        this.grids.push(grid)
      }
    } else {
      this.grids.push(grid)
    }

    if (grid === "waitingListGrid") {
      this.waitingListGridApi = params.api;
      this.waitingListGridColApi = params.columnApi;
    }
  }

  getList(regNo: any) {
    this._liverService.getLiverWaitingList(regNo).subscribe(res => {
      if (res['code'] == "S0000") {
        this.formData = res['result'];
        this.patientDetails.setPatientDetails(res['result']);
        this.liverWaitingList = this.formData.rgTbOrganWaitingList;
        this.updateGridColumnsVisibility();
        this.displayWaitingList();

      } else if (res['code'] == "F0000") {
        Swal.fire(
          'Warning',
          'No Record Found with Entered Regstration No. ',
          'warning'
        )
      } else {
        Swal.fire('warning!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })
  }

  displayWaitingList() {
    if (this.waitingListGrid.api) { this.waitingListGrid.api.showLoadingOverlay(); }

    this.rowData = this.liverWaitingList;
    this.dbData = this.rowData.map(x => Object.assign({}, x));
    for (var i = 0; i < this.liverWaitingList.length; i++) {
      this.liverWaitingList[i].recommendTransplantYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].recommendTransplantYn).map(s => s.id).toString();

      this.liverWaitingList[i].beyondHccCriteriaYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].beyondHccCriteriaYn).map(s => s.id).toString();

      this.liverWaitingList[i].comorbidityYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].comorbidityYn).map(s => s.id).toString();

      this.liverWaitingList[i].donorAvailableYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].donorAvailableYn).map(s => s.id).toString();

      this.liverWaitingList[i].poorPerformanceYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].poorPerformanceYn).map(s => s.id).toString();

      this.liverWaitingList[i].transplantUrgentYn = this.yesNo.filter(s => s.id == this.liverWaitingList[i].transplantUrgentYn).map(s => s.id).toString();

    }

  }

  getAge(event) {
    this.age = this._sharedService.ageFromDateOfBirthday(event);
  }

  search() {
    if (this.regId) {
      this.clear();
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
    }
  }

  clear() {
    this.formData = null;
    this.patientDetails.clear();
    this.rowData = null;
  }

  callListingPage() {
    this._router.navigate(['/liver-waiting/listing']);
  }

  validatePositiveNumber(value, oldValue) {
    const parsedValue = parseInt(value, 10);
    if (isNaN(parsedValue) || parsedValue < 0) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid Input',
        text: 'Please enter a valid positive number.',
        confirmButtonText: 'Okay'
      });
      return oldValue;
    }
    return parsedValue;
  }
}
