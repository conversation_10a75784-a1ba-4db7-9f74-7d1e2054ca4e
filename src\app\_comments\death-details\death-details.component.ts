import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import { RegistryService } from 'src/app/_services/registry.service';
import Swal from 'sweetalert2';
import * as AppUtils from '../../common/app.utils';
@Component({
  selector: 'app-death-details',
  templateUrl: './death-details.component.html'
})
export class DeathDetailsComponent implements OnInit {
  @Input() deatDetailsForm: FormGroup;
  @Input() currentCivilId = '';
  @Input() estCode = '';
  @Input() patientId = '';

  placeOfDeath: any;
  today = new Date();
  constructor(private formBuilder: FormBuilder, private _masterService: MasterService, private _registryService: RegistryService) { }

  ngOnInit() {
    this._masterService.getPlaceOfDeath().subscribe(res => {
      this.placeOfDeath = res['result'];
    });


    this.deatDetailsForm = this.formBuilder.group({
      'placeOfDeath': null,
      'timeOfDeath': null,
      'causedeathDirect': null,
      'causedeathUnderlying1': null,
      'causedeathUnderlying2': null,
      'causedeathUnderlying3': null,
      'civillID': null,
      'instituteOfDeath': null,
      'createdBy': null,
      'createdDate': null,
      'lastModifiedBy': null,
      'lastModifiedDate': null,
    });
    this.deatDetailsForm.disable();

  }
  fetchFromShifa() {
    console.log(this.estCode, this.patientId);
    if (this.estCode != undefined && this.patientId != undefined) {


      this._registryService.getDeathDetails(this.estCode, this.patientId).subscribe(response => {
        if (response['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
          this.setDeathDetailsResult(response['result']);
        } else if (response['code'] == "F0000") {
          Swal.fire({
            icon: 'warning',
            title: response['message'],
          });
        } else {
          Swal.fire({
            icon: 'info',
            title: 'No Death Details Found',
            text: 'No death details found for this patient. You can add them manually if needed.',
          });
        }
      });

    }else{
      Swal.fire({
        icon: 'warning',
        title: 'Please ensure patient details available before fetching death details.',
      });
    }
  }
  addManualy() {
    if (this.currentCivilId != "") {
      this.deatDetailsForm.enable();
      this.deatDetailsForm.patchValue({ civillID: this.currentCivilId });
    }
    else {
      Swal.fire({
        icon: 'warning',
        title: 'Please ensure patient details available before adding death details.',
      });
    }
  }

  setDeathDetailsResult(data) {
    this.deatDetailsForm.patchValue({
      timeOfDeath: new Date(data.timeOfDeath),
      placeOfDeath: data.placeOfDeath,
      causedeathDirect: data.causedeathDirect,
      causedeathUnderlying1: data.causedeathUnderlying1,
      causedeathUnderlying2: data.causedeathUnderlying2,
      causedeathUnderlying3: data.causedeathUnderlying3,
      civillID: this.currentCivilId,
      instituteOfDeath: data.instituteOfDeath,
      createdBy: data.createdBy,
      createdDate: data.createdDate,
      lastModifiedBy: data.lastModifiedBy,
      lastModifiedDate: data.lastModifiedDate,
    });
    this.deatDetailsForm.enable();
  }

  clear() {
    this.deatDetailsForm.reset();
  }
}
