import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from '../../_helpers/common.constants';
import * as _ from 'lodash';
import { NutDashboard } from 'src/app/_models/nut-dashboard.model';
import { NutDashboardDisplay } from 'src/app/_models/nut-dashboard-display.model';
import { ChildNutritionService } from '../child-nutrition.service';

@Component({
  selector: 'app-nut-dashboard',
  templateUrl: './nut-dashboard.component.html',
  styleUrls: ['./nut-dashboard.component.scss']

})
export class NutDashboardComponent implements OnInit {


  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: NutDashboard[];
  DashboardDataFilter: NutDashboard[];
  displayregInst: NutDashboardDisplay[];
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  regInstChart: any;
  createTime: any;
  today: any = new Date();
  visitItems = AppComponent.VISIT_TYPES;
  haLevel = AppComponent.HA_LEVEL[0].level;
  haColor = AppComponent.HA_LEVEL[0].color;
  whLevel = AppComponent.WH_LEVEL[0].level;
  whColor = AppComponent.WH_LEVEL[0].color;
  waLevel = AppComponent.WH_LEVEL[0].level;
  waColor = AppComponent.WH_LEVEL[0].color;
  displayWhLevel: any[];
  displayHaLevel: any[];
  displayWaLevel: any[];
  haChart: any;
  whChart: any;
  waChart: any;
  oedemaPieChart: any;
  displayOedemaYn: any[];
  malStatus: any;
  assOutcome: any;
  displayStatus: any[];
  statusChart: any;
  outcomeCauseChart: any;
  displayOutcomeCause: any[];

  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _nutService: ChildNutritionService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'dateGiven': [null],
      'visitRange': [null],
      'visitType': [null]
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });

    this._nutService.getChildNutritionMalStatusMast().subscribe(value => {
      this.malStatus = value.result;
    });

    this._nutService.getChildNutritionAssOutcomeMast().subscribe(value => {
      this.assOutcome = value.result;
    });
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._nutService.getNutDashboard().subscribe(res => {

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {


        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'NUT', this.institeList);

      }

      this.callFilter();
    })

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {

      this.DashboardDataFilter = this.DashboardDataDB;

      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['visitRange']) === true) {
        let visitFrom = body['visitRange'][0];
        let visitTo = body['visitRange'][1];
        let tmpAgelist = [];
        tmpAgelist = this.DashboardDataDB.filter(item => {
          const itemDate = new Date(item.visitDate);
          return itemDate >= new Date(visitFrom) && itemDate <= new Date(visitTo);
        });

        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (this.isEmptyNullZero(body['visitType']) === true) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.visitType == body['visitType']);
      }


      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regInst == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

  displayFilterType() {

    if (this.filterType === "institute") {
      this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['regInst']).map(s => s.estName);

    } else if (this.filterType === "wilayat") {
      this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);

    } else if (this.filterType === "region") {
      this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

    }
    else {
      this.filterTitle = 'All Regions';
    }

  }
  setChartData() {
    this.displayregInst = [];
    this.displayHaLevel = [];
    this.displayWhLevel = [];
    this.displayWaLevel = [];
    this.displayOedemaYn = [];
    this.displayStatus = [];
    this.displayOutcomeCause = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {
      let regInst = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: (this.institeList.find(s => s.estCode == this.DashboardDataFilter[i].regInst)) ? (this.institeList.find(s => s.estCode == this.DashboardDataFilter[i].regInst)).estName : null };
      this.displayregInst.push(regInst);

      let stunting = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].stunting };
      this.displayHaLevel.push(stunting);

      let wasting = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].wasting };
      this.displayWhLevel.push(wasting);

      let underweight = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].underweight };
      this.displayWaLevel.push(underweight);

      let oedemaYn = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].oedemaYn };
      this.displayOedemaYn.push(oedemaYn);

      let status = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].status };
      this.displayStatus.push(status);

      let outcomeCause = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].outcomeCause };
      this.displayOutcomeCause.push(outcomeCause);

    }

    this.callChart();
  }
  /* ------------  call Charts ---------------- */

  callChart() {
    this.callregInstPieChart(this.DashboardDataFilter);
    this.CallBarChart(this.displayHaLevel, "haChart", "N");
    this.CallBarChart(this.displayWhLevel, "whChart", "N");
    this.CallBarChart(this.displayWaLevel, "waChart", "N");
    this.callPieChartYn(this.displayOedemaYn, 'oedemaPieChart');
    this.CallBarChart(this.displayStatus, "statusChart", "N");
    this.CallBarChart(this.displayOutcomeCause, "outcomeCauseChart", "N");

  }

  CallBarChart(listData: NutDashboardDisplay[], chartData: any, withNull: any) {

    let charlabels = [];
    let charData = [];
    let listGroup = [];
    let haColors = [];
    let whColors = [];
    let waColors = [];

    // if need data with null , select "Y" 
    if (withNull == "N") {
      listData = listData.filter(s => s.value != null);
    }



    for (var n = 0; n < listData.length; n++) {

      if (listGroup.filter(s => s.id === listData[n].value).length == 0) {
        const result = listData.filter(s => s.value == listData[n].value).length;
        let a = { id: listData[n].value }
        if (chartData == "haChart") {
          let haLevelName = ''
          let hacolor = '';
          this.haLevel.forEach((item) => {

            if (item.id == listData[n].value) {
              haLevelName = item.value
              hacolor = this.haColor.find(el => el.id == item.id).value;


            }

          });
          charlabels.push(haLevelName);
          haColors.push(hacolor)
        } else if (chartData == "whChart") {
          let whLevelName = ''
          let whcolor = '';
          this.whLevel.forEach((item) => {

            if (item.id == listData[n].value) {
              whLevelName = item.value
              whcolor = this.whColor.find(el => el.id == item.id).value;


            }

          });
          charlabels.push(whLevelName);
          whColors.push(whcolor)
        } else if (chartData == "waChart") {
          let waLevelName = ''
          let wacolor = '';
          this.waLevel.forEach((item) => {

            if (item.id == listData[n].value) {
              waLevelName = item.value
              wacolor = this.waColor.find(el => el.id == item.id).value;


            }

          });
          charlabels.push(waLevelName);
          waColors.push(wacolor)
        } else if (chartData == "statusChart") {
          let statusName = ''
          this.malStatus.forEach((item) => {

            if (item.paramId == listData[n].value) {
              statusName = item.paramDesc


            }

          });
          charlabels.push(statusName);
        }
        else if (chartData == "outcomeCauseChart") {
          let outcomeCauseName = ''
          this.assOutcome.forEach((item) => {

            if (item.paramId == listData[n].value) {
              outcomeCauseName = item.paramDesc


            }

          });
          charlabels.push(outcomeCauseName);
        } else {
          charlabels.push(listData[n].value);
        }
        charData.push(result);
        listGroup.push(a);
      }
    }


    if (chartData == "haChart") {
      this.haChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: haColors,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "whChart") {
      this.whChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: whColors,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "waChart") {
      this.waChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: waColors,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "statusChart") {
      this.statusChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "outcomeCauseChart") {
      this.outcomeCauseChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

  }

  callregInstPieChart(listData: any[]) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";



    if (this.filterType === "institute") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.regInst).map(s => s.estCode), "label": est.filter(s => s.estCode == a.regInst).map(s => s.estName), "count": data.filter(s => s.regInst == a.regInst).length };
        if (groupByName.filter(s => s.label == a.regInst).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.regInst).map(s => s.estCode), "label": est.filter(s => s.estCode == a.regInst).map(s => s.estName), "count": data.filter(s => s.regInst == a.regInst).length };
        if (groupByName.filter(s => s.id == a.regInst).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    groupByName = groupByName.filter(s => s.id.length);

    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);
    this.regInstChart = {
      labels: groupByName.map(s => s.label),
      datasets: [
        {
          data: groupByName.map(s => s.count),
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
        }]
    };



  }

  callPieChartYn(listData: NutDashboardDisplay[], chartData: any) {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    if (chartData == 'oedemaPieChart') {

      listData = listData.filter(s => s.value != null);

      for (var n = 0; n < listData.length; n++) {
        if (listGroup.filter(s => s.congAnamoliesYn === listData[n].value).length == 0) {
          const result = listData.filter(s => s.value == listData[n].value).length;
          let a = { congAnamoliesYn: listData[n].value }
          charlabels.push(listData[n].value == 'Y' ? 'Yes' : 'No');
          charData.push(result);
          listGroup.push(a);
        }
      }

      this.charBGColor.sort(() => Math.random() - 0.2);
      this.oedemaPieChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
            data: charData,
          }

        ]
      }
    }
  }

}
