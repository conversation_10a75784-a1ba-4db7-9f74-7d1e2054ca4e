export interface CorneaRequestListSearchDto {
    pageable?: PageableParamsDto;

    reqDateFrom?: Date;
    reqDateTo?: Date;
    reqInst?: number;
    requestTo?: string;
    indication?: number;
    sizeClearZone?: string;
    tissueType?: string;
    intendedArrDateFrom?: Date;
    intendedArrDateTo?: Date;
    intendedSurDateFrom?: Date;
    intendedSurDateTo?: Date;
    patientId?: number;
    patCivilId?: number;
}

export interface PageableParamsDto {
    page?: number;
    size?: number;
    sort?: string[]; // e.g., ["fieldName,asc"]
}

