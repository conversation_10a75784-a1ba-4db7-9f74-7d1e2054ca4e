import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import * as AppCompUtils from '../../common/app.component-utils';
import Swal from 'sweetalert2';
import { CorneaService } from '../cornea.service';
import { MasterService } from 'src/app/_services/master.service';
import { GridOptions } from 'ag-grid-community';
import * as AppUtils from '../../common/app.utils';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { SharedService } from 'src/app/_services/shared.service';
import * as _ from 'lodash';
import * as moment from "moment";

interface OperDtl {
  paramId: number;
  paramName: string;
  prevId: number;
}

@Component({
  selector: 'app-cornea-transplant',
  templateUrl: './cornea-transplant.component.html',
  styleUrls: ['./cornea-transplant.component.scss'],
  providers: [CorneaService]
})
export class CorneaTransplantComponent implements OnInit {
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => this.gridOptions.api.sizeColumnsToFit()
  };

  patientForm: FormGroup;
  transplantForm!: FormGroup;
  instituteListFilter: any[] = [];
  surgeryTypeListFilter: any[] = [];
  surgeonNamesListFilter: any[] = [];
  visualAcuityListFilter: any[] = [];
  transplantOutcomeListFilter: any[] = [];
  transDiseaseListFilter: any[] = [];
  indicationTypeListFilter: any[] = [];
  preOpCondListFilter: any[] = [];
  transDiseaseProcFilter: any[] = [];
  checklistItems: any[] = [];
  filteredOperDtlsList: any[] = [];
  operativeDtlsItems: any[] = [];
  rawTransplantData: any[] = [];
  transplantInstituteForms: any[] = [];
  corTransDetailsData: any;
  regId: any;
  today = new Date();
  submitted = false;
  dataFetched = false;
  currentFormIndex = 0;

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('surgeonDropdown', { static: false }) surgeonDropdown: any;

  yesNo = AppCompUtils.YES_NO;
  dropdownSettings: IDropdownSettings;
  surDropdownSettings: IDropdownSettings;
  abcdropdownSettings: IDropdownSettings;
  institutes: any[] = [];

  leftItems = [];
  rightItems = [];
  graftIndicationList: any[][] = [];
  preOpConditionsList: any[][] = [];
  surgeonDtlsList: any[][] = [];
  numberOptions = [0, 1, 2, 3, 4];

  leftItemsList: any[][] = [];
  rightItemsList: any[][] = [];
  checklistItemsList: any[][] = [];

  filteredList: Map<number, OperDtl[]> = new Map();
  operDtlsListFilter: OperDtl[] = [];
  filteredListLoaded = false;

  selectedItems: Array<any> = [];
  selectedSurgeons: Array<any> = [];

  constructor(
    private _corneaService: CorneaService,
    private _masterService: MasterService,
    private _sharedService: SharedService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.getMasterData();
    this.initializeTransplantForm();
    this.initializeDropdownSettings();
    this.cdr.detectChanges();
  }

  initializeDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'paramId',
      textField: 'paramName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };
    this.surDropdownSettings = {
      singleSelection: false,
      idField: 'persCode',
      textField: 'staffName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };
  }

  initializeTransplantForm() {
    this.transplantForm = this.fb.group({
      tranId: new FormControl(),
      tranInst: new FormControl(null, Validators.required),
      tranDate: new FormControl(null, Validators.required),
      tissueArrDate: new FormControl(null),
      surgeryType: new FormControl(),
      doctorId: new FormControl(),
      eyeOperated: new FormControl(null, Validators.required),
      prevLeftGraft: new FormControl(),
      prevRightGraft: new FormControl(),
      transplantOutcome: new FormControl(),
      visAcuityBefore: new FormControl(),
      visAcuityAfter: new FormControl(),
      patMedHistory: new FormControl(),
      regNo: new FormControl(),
      graftType: new FormControl(),
      graftSource: new FormControl(),
      graftCondition: new FormControl(),
      immunoTreatment: new FormControl(),
      immunoRemarks: new FormControl(),
      disReqTransplant: new FormControl(null),
      disTransComments: new FormControl(null),
      disTransProc: new FormControl(),
      rgTbCorParamDtl: new FormControl(),
      rgTbCorGraftIndication: new FormControl([]),
      rgTbCorPreopCondition: new FormControl([]),
      rgTbCorSurgeonDtls: new FormControl(this.selectedItems),
      createdBy: new FormControl(''),
      createdDate: new FormControl(''),
      commentDonTissue: new FormControl(null),
      tissueNo: new FormControl(null),
      preopCond: new FormControl(null),
    });

    if (this._sharedService.getNavigationData()) {
      this.getList(this._sharedService.getNavigationData().regNo, undefined);
      this._sharedService.setNavigationData(null);
    }
  }

  addTransplant() {
    if (!this.corTransDetailsData) {
      Swal.fire('Warning', 'Add Registration No. to be able to add!', 'warning');
      return;
    }
    if (this.transplantInstituteForms.length > 0) {
      const lastForm = this.transplantInstituteForms[this.transplantInstituteForms.length - 1];
      if (lastForm.invalid) {
        Swal.fire('Warning', 'Please fill in all required fields in the previous form before adding a new one.', 'warning');
        return;
      }
    }
    this.newTransplantForm();
  }

  newTransplantForm() {
    const newTransplantForm = this.fb.group({
      tranId: [null],
      tranInst: [null, Validators.required],
      tranDate: [null, Validators.required],
      tissueArrDate: [null],
      surgeryType: [null],
      doctorId: [null],
      eyeOperated: [null, Validators.required],
      prevLeftGraft: [null],
      prevRightGraft: [null],
      transplantOutcome: [null],
      visAcuityBefore: [null],
      visAcuityAfter: [null],
      patMedHistory: [null],
      regNo: [null],
      graftType: [null],
      graftSource: [null],
      graftCondition: [null],
      immunoTreatment: [null],
      immunoRemarks: [null],
      disReqTransplant: [null],
      disTransComments: [null],
      disTransProc: [null],
      rgTbCorParamDtl: [[]],
      rgTbCorGraftIndication: [[]],
      rgTbCorPreopCondition: [[]],
      rgTbCorSurgeonDtls: [[]],
      createdBy: [''],
      createdDate: [''],
      commentDonTissue: [null],
      tissueNo: [null],
      preopCond: [null],
    });
    this.resetChecklistItems();
    this.transplantInstituteForms.push(newTransplantForm);
    this.rawTransplantData.push({});
    this.patchTransplantByIndex(this.transplantInstituteForms.length - 1);
  }

  resetChecklistItems() {
    if (Array.isArray(this.checklistItems)) {
      this.checklistItems.forEach(item => (item.answerYn = null));
    }
    if (Array.isArray(this.leftItems)) {
      this.leftItems.forEach(item => (item.answerYn = null));
    }
    if (Array.isArray(this.rightItems)) {
      this.rightItems.forEach(item => (item.answerYn = null));
    }
  }

  save() {
    this.submitted = true;
    if (!this.patientDetails.validateFields()) {
      Swal.fire('Alert', 'Mandatory fields in (Patient Details) cannot be empty', 'warning');
      return;
    }
    if (this.transplantInstituteForms.some(form => form.invalid)) {
      Swal.fire('Alert', 'Mandatory fields in (Transplant Institute Form) cannot be empty', 'warning');
      return;
    }
    if (!this.transplantForm.valid) {
      Swal.fire('Error!', 'Please fill in all required fields.', 'error');
      return;
    }
    const allParamItems = [...this.leftItems, ...this.rightItems];
    const missingMandatory = allParamItems.filter(item =>
      item.mandatoryYn === 'Y' &&
      (
        item.answerYn == null ||
        item.answerYn === '' ||
        (Array.isArray(item.answerYn) && item.answerYn.length === 0)
      )
    );
    if (missingMandatory.length > 0) {
      Swal.fire('Alert', 'Mandatory fields in (Operative Details) cannot be empty.', 'warning');
      return;
    }
    const submitData = this.prepareSubmitData(true);
    this._corneaService.saveTransplantDetails(submitData).subscribe(
      res => {
        if (res['code'] === '0') {
          Swal.fire('Success!', 'Transplant details saved successfully.', 'success');
          this.getList(res['result'], undefined);
          this.submitted = false;
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      },
      () => Swal.fire('Error!', 'An error occurred while saving data.', 'error')
    );
  }

  cellRenderer(value: any): any { return value; }
  cellRendererBack(value: any): any { return value; }

  prepareSubmitData(isFinal: boolean = false) {
    if (isFinal) this.saveCurrentFormValues();
    const patientFormData = this.patientDetails.patientForm.value;
    const transplantList = [];
    this.transplantInstituteForms.forEach((form, index) => {
      const formValue = form.value;
      formValue.transplantDate = this.cellRendererBack(this.cellRenderer(formValue.transplantDate));
      if (formValue.rgTbCorGraftIndication && formValue.rgTbCorGraftIndication.length < 1) formValue.rgTbCorGraftIndication = null;
      if (formValue.rgTbCorPreopCondition && formValue.rgTbCorPreopCondition.length < 1) formValue.rgTbCorPreopCondition = null;
      if (formValue.rgTbCorSurgeonDtls && formValue.rgTbCorSurgeonDtls.length < 1) formValue.rgTbCorSurgeonDtls = null;
      if (formValue.rgTbCorParamDtl && formValue.rgTbCorParamDtl.length < 1) formValue.rgTbCorParamDtl = null;
      const transplantData = {
        ...formValue,
        createdBy: formValue.createdBy,
        createdDate: formValue.createdDate,
        rgTbCorGraftIndication: this.safeMapList(this.graftIndicationList[index], 'paramId', 'indId'),
        rgTbCorPreopCondition: this.safeMapList(this.preOpConditionsList[index], 'paramId', 'condId'),
        rgTbCorSurgeonDtls: this.safeMapList(this.surgeonDtlsList[index], 'persCode', 'staffId'),
        rgTbCorParamDtl: [
          ...this.mapParamDetails(this.checklistItemsList[index] || [], 'D'),
          ...this.mapParamDetails(this.leftItemsList[index] || [], 'O'),
          ...this.mapParamDetails(this.rightItemsList[index] || [], 'O')
        ]
      };
      transplantList.push(transplantData);
    });
    return {
      ...this.corTransDetailsData,
      regInst: patientFormData.regInst,
      rgTbPatientInfo: { ...patientFormData, dob: patientFormData.dob ? moment(patientFormData.dob).format("DD-MM-YYYY") : null },
      rgTbCorTransplant: transplantList
    };
  }

  safeMapList(list: any[], idKey: string, outputKey: string) {
    return (list || [])
      .filter(item => item && item[idKey] != null)
      .map(item => ({
        runId: null,
        [outputKey]: item[idKey],
        remarks: item.paramName
      }));
  }

  search() {
    if (!this.regId) {
      Swal.fire({ icon: 'warning', title: 'Please enter Registration ID' });
      return;
    }
    this.clear();
    this.getList(this.regId, undefined);
    this.regId = '';
  }

  getList(centralRegNo?: any, civilId?: any) {
    this._corneaService.getCorTransplantDetails(centralRegNo, civilId).subscribe(
      res => {
        if (res['code'] === 'S0000') {
          this.corTransDetailsData = res['result'];
          if (this.corTransDetailsData.rgTbCorTransplant) {
            this.corTransDetailsData.rgTbCorTransplant.sort((a, b) =>
              new Date(b.tranDate).getTime() - new Date(a.tranDate).getTime()
            );
          }
          if (
            this.corTransDetailsData.rgTbCorTransplant &&
            this.corTransDetailsData.rgTbCorTransplant.length > 0 &&
            this.corTransDetailsData.rgTbCorTransplant[0].rgTbCorSurgeonDtls
          ) {
            this.selectedItems = (this.corTransDetailsData.rgTbCorTransplant[0].rgTbCorSurgeonDtls || [])
              .map(item => {
                const id = item.staffId !== undefined && item.staffId !== null ? item.staffId : item.persCode;
                return this.surgeonNamesListFilter.find(f => f.persCode === id) || item;
              });
          } else {
            this.selectedItems = [];
          }
          this.patientDetails.setPatientDetails(this.corTransDetailsData);
          this.getRegisterForm(this.corTransDetailsData);
        } else {
          const message = res['code'] === 'F0000'
            ? 'No Record Found with Entered Registration No.'
            : res['message'];
          Swal.fire('Warning', message, 'warning');
        }
      },
      error => {
        if (error.status === 401) {
          Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
        }
      }
    );
  }

  getRegisterForm(data: any) {
    if (!data.rgTbCorTransplant) return;
    this.transplantInstituteForms = [];
    this.rawTransplantData = [];
    this.graftIndicationList = [];
    this.preOpConditionsList = [];
    this.surgeonDtlsList = [];
    this.leftItemsList = [];
    this.rightItemsList = [];
    this.checklistItemsList = [];
    data.rgTbCorTransplant.forEach((transplant, idx) => {
      if (Array.isArray(transplant.rgTbCorParamDtl)) {
        transplant.rgTbCorParamDtl = transplant.rgTbCorParamDtl.map(dtl => ({
          ...dtl,
          paramValue: dtl.paramValue !== null && dtl.paramValue !== undefined ? dtl.paramValue.toString() : dtl.paramValue
        }));
      }
      const form = this.fb.group({
        tranId: [transplant.tranId],
        tranInst: [transplant.tranInst],
        tranDate: [new Date(transplant.tranDate)],
        tissueArrDate: [transplant.tissueArrDate ? new Date(transplant.tissueArrDate) : null],
        surgeryType: [transplant.surgeryType],
        doctorId: [transplant.doctorId],
        eyeOperated: [transplant.eyeOperated],
        prevLeftGraft: [transplant.prevLeftGraft],
        prevRightGraft: [transplant.prevRightGraft],
        transplantOutcome: [transplant.transplantOutcome],
        visAcuityBefore: [transplant.visAcuityBefore],
        visAcuityAfter: [transplant.visAcuityAfter],
        patMedHistory: [transplant.patMedHistory],
        regNo: [transplant.regNo],
        graftType: [transplant.graftType],
        graftSource: [transplant.graftSource],
        graftCondition: [transplant.graftCondition],
        immunoTreatment: [transplant.immunoTreatment],
        immunoRemarks: [transplant.immunoRemarks],
        disReqTransplant: [transplant.disReqTransplant || null],
        disTransProc: [transplant.disTransProc || null],
        disTransComments: [transplant.disTransComments || ''],
        rgTbCorParamDtl: [transplant.rgTbCorParamDtl || []],
        rgTbCorGraftIndication: [transplant.rgTbCorGraftIndication || []],
        rgTbCorPreopCondition: [transplant.rgTbCorPreopCondition || []],
        rgTbCorSurgeonDtls: [transplant.rgTbCorSurgeonDtls || []],
        createdBy: [transplant.createdBy],
        createdDate: [transplant.createdDate],
        commentDonTissue: [transplant.commentDonTissue],
        tissueNo: [transplant.tissueNo || null],
        preopCond: [transplant.preopCond || null]
      });
      this.transplantInstituteForms.push(form);
      this.rawTransplantData.push({
        ...transplant,
        leftItems: [],
        rightItems: [],
        checklistItems: []
      });
      this.graftIndicationList[idx] = _.cloneDeep(
        (transplant.rgTbCorGraftIndication || []).map(item => {
          var id = (item.indId !== undefined && item.indId !== null) ? item.indId : item.paramId;
          return this.indicationTypeListFilter.find(function (f) { return f.paramId === id; }) || item;
        })
      );
      this.preOpConditionsList[idx] = _.cloneDeep(
        (transplant.rgTbCorPreopCondition || []).map(item => {
          var id = (item.condId !== undefined && item.condId !== null) ? item.condId : item.paramId;
          return this.preOpCondListFilter.find(function (f) { return f.paramId === id; }) || item;
        })
      );
      this.surgeonDtlsList[idx] = _.cloneDeep(
        (transplant.rgTbCorSurgeonDtls || []).map(item => {
          var id = (item.staffId !== undefined && item.staffId !== null) ? item.staffId : item.persCode;
          return this.surgeonNamesListFilter.find(function (f) { return f.persCode === id; }) || item;
        })
      );
      this.checklistItemsList[idx] = _.cloneDeep(this.checklistItems.map(item => {
        const match = (transplant.rgTbCorParamDtl || []).find(p => p.paramId === item.paramId && p.recordType === 'D');
        return { ...item, answerYn: match ? match.paramValue : null };
      }));
      this.leftItemsList[idx] = _.cloneDeep(this.leftItems.map(item => {
        const matches = (transplant.rgTbCorParamDtl || []).filter(p => p.paramId === item.paramId && p.recordType === 'O');
        const list = this.filteredList.get(item.paramId) || [];
        if (item.outputType === 'LIST') {
          const match = matches[0];
          return { ...item, answerYn: match ? Number(match.paramValue) : null };
        } else if (item.outputType === 'MULTISELECT') {
          const selectedIds = matches
            .flatMap(m => {
              if (typeof m.paramValue === 'string') {
                return m.paramValue.split(',').map(val => Number(val.trim()));
              } else if (typeof m.paramValue === 'number') {
                return [m.paramValue];
              } else {
                return [];
              }
            })
            .filter(val => !isNaN(val));
          return {
            ...item,
            answerYn: selectedIds
              .map(id => list.find(opt => opt.paramId === id))
              .filter(opt => opt !== undefined)
          };
        } else {
          const match = matches[0];
          return { ...item, answerYn: match ? match.paramValue : null };
        }
      }));
      this.rightItemsList[idx] = _.cloneDeep(this.rightItems.map(item => {
        const matches = (transplant.rgTbCorParamDtl || []).filter(p => p.paramId === item.paramId && p.recordType === 'O');
        const list = this.filteredList.get(item.paramId) || [];
        if (item.outputType === 'LIST') {
          const match = matches[0];
          return { ...item, answerYn: match ? Number(match.paramValue) : null };
        } else if (item.outputType === 'MULTISELECT') {
          const selectedIds = matches
            .flatMap(m => {
              if (typeof m.paramValue === 'string') {
                return m.paramValue.split(',').map(val => Number(val.trim()));
              } else if (typeof m.paramValue === 'number') {
                return [m.paramValue];
              } else {
                return [];
              }
            })
            .filter(val => !isNaN(val));
          return {
            ...item,
            answerYn: selectedIds
              .map(id => list.find(opt => opt.paramId === id))
              .filter(opt => opt !== undefined)
          };
        } else {
          const match = matches[0];
          return { ...item, answerYn: match ? match.paramValue : null };
        }
      }));
      this.getListDisease(data.rgTbCorTransplant[0].disReqTransplant);
    });
    this.patchTransplantByIndex(0);
  }

  compareByParamId = (a: any, b: any): boolean => {
    return a === b || (a.paramId && b.paramId && a.paramId === b.paramId);
  };

  patchTransplantByIndex(index: number) {
    if (!this.transplantInstituteForms[index] || !this.rawTransplantData[index]) return;
    this.currentFormIndex = index;
    this.transplantForm = this.transplantInstituteForms[index];
    this.transplantForm.markAsPristine();
    this.transplantForm.markAsUntouched();
    this.transplantForm.updateValueAndValidity();
    this.patchDropdownValues(this.transplantForm, this.rawTransplantData[index]);
    const disReqTransplant = this.transplantForm.get('disReqTransplant').value;
    if (disReqTransplant) this.getListDisease(disReqTransplant);
    this.cdr.detectChanges();
  }

  patchDropdownValues(form: FormGroup, transplant: any) {
    this.patchFormArray(form, 'rgTbCorGraftIndication', transplant, this.indicationTypeListFilter, 'paramId', 'indId');
    this.patchFormArray(form, 'rgTbCorPreopCondition', transplant, this.preOpCondListFilter, 'paramId', 'condId');
    this.patchFormArray(form, 'rgTbCorSurgeonDtls', transplant, this.surgeonNamesListFilter, 'persCode', 'staffId');
    setTimeout(() => {
      if (this.surgeonDropdown && this.surgeonDropdown.dropdownList) {
        this.surgeonDropdown.selectedItems = form.get('rgTbCorSurgeonDtls').value || [];
        if (typeof this.surgeonDropdown.onChangeCallback === 'function') {
          this.surgeonDropdown.onChangeCallback(this.surgeonDropdown.selectedItems);
        }
        if (this.surgeonDropdown.cd && typeof this.surgeonDropdown.cd.detectChanges === 'function') {
          this.surgeonDropdown.cd.detectChanges();
        }
      }
      this.cdr.detectChanges();
    }, 50);
    if (form.get('rgTbCorParamDtl')) {
      form.get('rgTbCorParamDtl').setValue(transplant.rgTbCorParamDtl || []);
    }
    if (transplant.rgTbCorParamDtl && this.checklistItems) {
      this.checklistItems.forEach(item => {
        const match = transplant.rgTbCorParamDtl.find(p => p.paramId === item.paramId && p.recordType === 'D');
        item.answerYn = match ? match.paramValue : null;
      });
      this.rightItems.forEach(item => {
        const matches = transplant.rgTbCorParamDtl.filter(
          p => p.paramId === item.paramId && p.recordType === 'O'
        );
        const list = this.filteredList.get(item.paramId) || [];
        if (item.outputType === 'LIST') {
          const match = matches[0];
          item.answerYn = match ? Number(match.paramValue) : null;
        } else if (item.outputType === 'MULTISELECT') {
          const selectedIds = matches
            .flatMap(m => {
              if (typeof m.paramValue === 'string') {
                return m.paramValue.split(',').map(val => Number(val.trim()));
              } else if (typeof m.paramValue === 'number') {
                return [m.paramValue];
              } else {
                return [];
              }
            })
            .filter(val => !isNaN(val));
          item.answerYn = selectedIds
            .map(id => list.find(opt => opt.paramId === id))
            .filter(opt => opt !== undefined);
        } else {
          const match = matches[0];
          item.answerYn = match ? match.paramValue : null;
        }
      });
      this.leftItems.forEach(item => {
        const matches = transplant.rgTbCorParamDtl.filter(
          p => p.paramId === item.paramId && p.recordType === 'O'
        );
        const list = this.filteredList.get(item.paramId) || [];
        if (item.outputType === 'LIST') {
          const match = matches[0];
          item.answerYn = match ? Number(match.paramValue) : null;
        } else if (item.outputType === 'MULTISELECT') {
          const selectedIds = matches
            .flatMap(m => {
              if (typeof m.paramValue === 'string') {
                return m.paramValue.split(',').map(val => Number(val.trim()));
              } else if (typeof m.paramValue === 'number') {
                return [m.paramValue];
              } else {
                return [];
              }
            })
            .filter(val => !isNaN(val));
          item.answerYn = selectedIds
            .map(id => list.find(opt => opt.paramId === id))
            .filter(opt => opt !== undefined);
        } else {
          const match = matches[0];
          item.answerYn = match ? match.paramValue : null;
        }
      });
    }
  }

  patchFormArray(form, key, transplant, filterList, filterKey, itemKey) {
    if (form.get(key)) {
      const sourceArray = transplant[key] || [];
      const patchedValues = sourceArray.map(item =>
        filterList.find(filter => filter[filterKey] === item[itemKey]) || item
      );
      form.get(key).setValue(_.cloneDeep(patchedValues));
    }
  }

  nextForm() {
    this.saveCurrentFormValues();
    if (this.currentFormIndex + 1 < this.transplantInstituteForms.length) {
      this.patchTransplantByIndex(this.currentFormIndex + 1);
    }
  }

  previousForm() {
    this.saveCurrentFormValues();
    if (this.currentFormIndex > 0) {
      this.patchTransplantByIndex(this.currentFormIndex - 1);
    }
  }

  saveCurrentFormValues() {
    if (this.transplantForm && this.rawTransplantData[this.currentFormIndex]) {
      this.transplantForm.updateValueAndValidity();
      this.rawTransplantData[this.currentFormIndex] = {
        ...this.rawTransplantData[this.currentFormIndex],
        ...this.transplantForm.value
      };
      const graftIndications = this.transplantForm.get('rgTbCorGraftIndication').value || [];
      this.graftIndicationList[this.currentFormIndex] = _.cloneDeep(graftIndications);
      this.rawTransplantData[this.currentFormIndex].rgTbCorGraftIndication = graftIndications.map(item => ({
        runId: null,
        indId: item.paramId,
        remarks: item.paramName
      }));
      const preOpConditions = this.transplantForm.get('rgTbCorPreopCondition').value || [];
      this.preOpConditionsList[this.currentFormIndex] = _.cloneDeep(preOpConditions);
      this.rawTransplantData[this.currentFormIndex].rgTbCorPreopCondition = preOpConditions.map(item => ({
        runId: null,
        condId: item.paramId,
        remarks: item.paramName
      }));
      const surgeonDtls = this.transplantForm.get('rgTbCorSurgeonDtls').value || [];
      this.surgeonDtlsList[this.currentFormIndex] = _.cloneDeep(surgeonDtls);
      this.rawTransplantData[this.currentFormIndex].rgTbCorSurgeonDtls
        = surgeonDtls.map(item => ({
          runId: null,
          staffId: item.persCode,
          staffName: item.staffName
        }));
      this.leftItemsList[this.currentFormIndex] = _.cloneDeep(this.leftItems);
      this.rightItemsList[this.currentFormIndex] = _.cloneDeep(this.rightItems);
      this.checklistItemsList[this.currentFormIndex] = _.cloneDeep(this.checklistItems);
      this.rawTransplantData[this.currentFormIndex].rgTbCorParamDtl = [
        ...this.mapParamDetails(this.checklistItemsList[this.currentFormIndex], 'D'),
        ...this.mapParamDetails(this.leftItemsList[this.currentFormIndex], 'O'),
        ...this.mapParamDetails(this.rightItemsList[this.currentFormIndex], 'O')
      ];
    }
  }

  mapParamDetails(items, recordType) {
    const values = [];
    items.forEach(item => {
      let value = item.answerYn;
      if (item.outputType === 'MULTISELECT' && Array.isArray(value)) {
        value
          .filter(v => v && v.paramId != null)
          .forEach(v => {
            values.push({
              paramId: item.paramId,
              paramValue: v.paramId,
              recordType
            });
          });
      } else if (typeof value === 'object' && value !== null) {
        if (value.paramId != null) {
          values.push({
            paramId: item.paramId,
            paramValue: value.paramId,
            recordType
          });
        }
      } else if (value !== null && value !== '') {
        values.push({
          paramId: item.paramId,
          paramValue: value,
          recordType
        });
      }
    });
    return values;
  }

  callMpiMethod() {
    const civilId = this.patientDetails.patientForm.value.civilId;
    this._corneaService.getCorTransplantDetails(undefined, civilId).subscribe(
      res => {
        if (res['code'] === AppUtils.RESPONSE_SUCCESS_CODE) {
          this.clear();
          this.dataFetched = true;
          this.corTransDetailsData = res['result'];
          if (this.corTransDetailsData.rgTbCorTransplant) {
            this.corTransDetailsData.rgTbCorTransplant.sort((a, b) =>
              new Date(b.tranDate).getTime() - new Date(a.tranDate).getTime()
            );
          }
          this.patientDetails.setPatientDetails(this.corTransDetailsData);
          this.getRegisterForm(this.corTransDetailsData);
        } else if (res['code'] === AppUtils.RESPONSE_NO_ECORD || res['code'] === AppUtils.RESPONSE_ERROR_CODE) {
          this.getData('civilId', '', civilId);
          this.newTransplantForm();
        } else {
          Swal.fire(' ', 'The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.', 'warning');
        }
      },
      error => {
        if (error.status === 401) {
          Swal.fire('', 'Error occurred while retrieving details', 'error');
        }
      }
    );
  }

  getData(searchBy: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    if (searchBy === AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = 'No Record Found with Entered Registration No.';
    } else if (searchBy === AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = 'No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP.';
      } else {
        Swal.fire('', 'No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.', 'warning');
      }
    }
    if (callMPI) {
      if (msg) {
        Swal.fire('', msg, 'warning').then(() => {
          this._sharedService.setPatientData(this.patientDetails);
          this._sharedService.fetchMpi().subscribe();
        });
      } else {
        this._sharedService.setPatientData(this.patientDetails);
        this._sharedService.fetchMpi().subscribe();
      }
    }
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getInstiteList(regCode, walCode).subscribe(
      response => (this.instituteListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorSurgeryType().subscribe(
      response => (this.surgeryTypeListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorSurgeonNamesList().subscribe(
      response => (this.surgeonNamesListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorVisualAcuity().subscribe(
      response => (this.visualAcuityListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorTransplantOutcome().subscribe(
      response => (this.transplantOutcomeListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorTransDisease().subscribe(
      response => (this.transDiseaseListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorIndicationType().subscribe(
      response => (this.indicationTypeListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorPreOpCond().subscribe(
      response => (this.preOpCondListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorCheckList().subscribe(
      response => (this.checklistItems = response.result),
      () => { }
    );
    this._masterService.getAllCorOperativeDtls().subscribe(
      response => {
        this.operativeDtlsItems = response.result;
        const half = Math.ceil(this.operativeDtlsItems.length / 2);
        this.leftItems = this.operativeDtlsItems.slice(0, half);
        this.rightItems = this.operativeDtlsItems.slice(half);
      },
      () => { }
    );
    this._masterService.getAllCorOperDtlsList().subscribe(
      response => {
        this.operDtlsListFilter = response.result;
        const filteredItems = Array.isArray(response.result) ? response.result : [];
        this.filteredList = filteredItems.reduce((acc, item) => {
          if (!acc.has(item.prevId)) {
            acc.set(item.prevId, []);
          }
          acc.get(item.prevId).push(item);
          return acc;
        }, new Map());
      },
      error => {
        console.error('Failed to fetch list', error);
      }
    );
  }

  onDiseaseChange(event: any) {
    if (this.transplantForm === this.transplantInstituteForms[this.currentFormIndex]) {
      this.transplantForm.controls['disTransProc'].setValue(null);
      this.transplantForm.controls['disTransComments'].setValue(null);
    }
    const selectedId = event.paramId;
    if (selectedId) {
      this.getListDisease(selectedId);
    } else {
      this.transDiseaseProcFilter = [];
    }
  }

  getListDisease(diseaseId: number) {
    this._masterService.getAllCorTransDiseaseProcByPrevId(diseaseId).subscribe(
      (response: any) => {
        this.transDiseaseProcFilter = _.cloneDeep(response.result);
      },
      error => {
        console.error('Error fetching transplant procedures:', error);
        this.transDiseaseProcFilter = [];
      }
    );
  }

  resetFormData() {
    this.transDiseaseProcFilter = [];
    this.transplantForm.controls['disReqTransplant'].setValue(null);
    this.transplantForm.controls['disTransProc'].setValue(null);
  }

  clear() {
    this.patientDetails.clear();
    this.transplantForm.reset();
    this.transplantInstituteForms = [];
    this.submitted = false;
    this.resetChecklistItems();
    this.corTransDetailsData = null;
    this.dataFetched = false;
    this.rawTransplantData = [];
    this.currentFormIndex = 0;
    this.resetFormData();
  }

  onNumberInputChange(event: any, item: any) {
    item.answerYn = event.target.value;
  }

  onAnswer(item, value) {
    item.answerYn = value;
  }
}