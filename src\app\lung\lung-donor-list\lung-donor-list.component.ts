import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import { LungService } from '../lung.service';
import { SharedService } from 'src/app/_services/shared.service';
import { Router } from '@angular/router';
import { RenalDonor } from 'src/app/_models/renal-donor.model';
import { Nationality } from 'src/app/_models/nationality.model';
import * as AppUtils from "../../common/app.utils";
import { GridOptions } from 'ag-grid-community';
import Swal from 'sweetalert2';
import * as AppCompUtils from '../../common/app.component-utils';
import { formatDate } from '@angular/common';

@Component({
  selector: 'flst',
  templateUrl: './lung-donor-list.component.html',
  styleUrls: ['./lung-donor-list.component.scss'],
  providers: [LungService]
})
export class LungDonorListComponent implements OnInit {
  @Input() searchForm: FormGroup;
  today = new Date();
  advancedToggle = false;
  paginationSize = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  institutes: any[];
  lastSearchBody: any;
  nationList: Nationality[];
  relationList: any[];
  totalRecords = 0;
  rowData: RenalDonor[] = [];
  displayedResults: any[] = [];
  allResults: any[] = [];
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  gender = AppCompUtils.GENDER;
  columnDefs: any[];
  gridOptions: GridOptions = {
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridReady: () => {
      this.gridOptions.api.setRowData(this.rowData);
      this.autoSizeAndFitColumns();
    },
    onGridSizeChanged: () => this.autoSizeAndFitColumns(),
    onFirstDataRendered: () => {
      this.autoSizeAndFitColumns();
    },
    defaultColDef: {
      resizable: true,
      sortable: true,
      minWidth: 50,
      suppressSizeToFit: true
    },
    enableSorting: true,
    multiSortKey: 'ctrl'
  };

  constructor(
    private _masterService: MasterService,
    private _lungService: LungService,
    private _sharedService: SharedService,
    private _router: Router
  ) {
    this.searchForm = new FormGroup({
      civilId: new FormControl(),
      fullname: new FormControl(),
      sex: new FormControl(),
      dob: new FormControl(),
      nationality: new FormControl(),
      bloodGroup: new FormControl(),
      telNo: new FormControl(),
      address: new FormControl(),
      donorType: new FormControl(),
      instCode: new FormControl(),
      instPatientId: new FormControl(),
      relationDesc: new FormControl(),
      relationType: new FormControl(),
    });

    this.loadNationalities();
    this.loadRelationTypes();

    const format = (params: any) =>
      params.data[params.colDef.field]
        ? formatDate(params.data[params.colDef.field], AppCompUtils.DATE_FORMATS.STANDARD, 'en')
        : null;

    this.columnDefs = [
      { headerName: "Donor ID", field: "kidneyDonorId", minWidth: 50 },
      { headerName: "Civil ID", field: "civilId", minWidth: 120 },
      { headerName: "Name", field: "fullname", minWidth: 290 },
      { headerName: "Date of Birth", field: "dob", minWidth: 130, valueFormatter: format },
      {
        headerName: "Gender", field: "sex", minWidth: 100,
        valueFormatter: (params: any) => {
          if (params.value === 'M') return 'Male';
          if (params.value === 'F') return 'Female';
          return params.value || '';
        }
      },
      {
        headerName: "Blood Group",
        field: "bloodGroup",
        minWidth: 120,
        valueFormatter: (params: any) => {
          if (!params.value || !this.bloodGroupList) return '';
          const val = String(params.value).trim();
          const letterMatch = this.bloodGroupList.find(group => group.value === val);
          if (letterMatch) return letterMatch.value;
          if (/^[ABO]{1,2}\s*[-+]?$/i.test(val)) return val;
          const bg = this.bloodGroupList.find(group => String(group.id) === val);
          return bg ? bg.value : val;
        }
      },
      {
        headerName: "Nationality",
        field: "nationality",
        minWidth: 130,
        valueFormatter: (params: any) => {
          if (!params.value || !this.nationList) return '';
          const nat = this.nationList.find(n => n.natCode == params.value);
          return nat ? nat.nationality : params.value;
        }
      },
      { headerName: "Telephone Number", field: "telNo", minWidth: 150 },
      { headerName: "Address", field: "address", minWidth: 200 },
      {
        headerName: "Donating Hospital Name",
        field: "instCode",
        minWidth: 200,
        valueFormatter: (params: any) => {
          if (!params.value || !this.institutes) return '';
          const inst = this.institutes.find(i => i.estCode == params.value);
          return inst ? inst.estName : params.value;
        }
      },
      {
        headerName: "Donor Hospital File No.",
        field: "instPatientId",
        minWidth: 180,
        valueFormatter: (params: any) => params.value ? params.value : ''
      }
    ];
  }

  ngOnInit(): void {
    this._masterService.institiutes.subscribe(res => {
      this.institutes = res["result"];
    });
  }

  clear(_: any) {
    this.searchForm.reset();
    this.rowData = [];
    this.totalRecords = 0;
    if (this.gridOptions.api) {
      this.gridOptions.api.setRowData([]);
      this.gridOptions.api.showNoRowsOverlay();
    }
  }

  onReady() {
    this.autoSizeAndFitColumns();
  }

  autoSizeAndFitColumns() {
    if (this.gridOptions.columnApi && this.gridOptions.api) {
      this.gridOptions.api.sizeColumnsToFit();
    }
  }

  getList(event?: any) {
    const pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };

    const searchFormValue = { ...this.searchForm.value };
    Object.keys(searchFormValue).forEach(key => {
      if (typeof searchFormValue[key] === 'string' && searchFormValue[key]) {
        searchFormValue[key] = searchFormValue[key].toUpperCase();
      }
    });

    const searchBody = { ...searchFormValue, pageable };
    this.lastSearchBody = { ...searchBody };

    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }
    this._lungService.getDonorList(searchBody).subscribe(
      res => {
        if (res['code'] === 'S0000') {
          this.rowData = res['result']['content'] || [];
          this.totalRecords = res['result']['totalElements'] || 0;

          if (this.gridOptions.api) {
            this.gridOptions.api.setRowData(this.rowData);
            setTimeout(() => {
              this.autoSizeAndFitColumns();
            }, 100);
          }
        } else {
          this.handleError(res['message'] || 'Invalid response');
        }
      },
      error => {
        if (error['status'] === 401) {
          this.handleError('Error occurred while retrieving user details');
        } else {
          this.handleError('An unexpected error occurred');
        }
      }
    );
  }

  private handleError(message: string): void {
    this.rowData = [];
    Swal.fire('Error!', message, 'error');
  }

  private loadNationalities(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      response => {
        this.nationList = response.result;
      }
    );
  }

  private loadRelationTypes(relationCode: any = 0) {
    this._masterService.getRelationTypeMast(relationCode).subscribe(
      response => {
        this.relationList = response.result;
      }
    );
  }

  onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(["lung-donor/registry"], {
      state: { civilId: event.data.civilId },
    });
  }

  bloodGroupSelect(event: any) {
    const bloodGroupControl = this.searchForm.get('bloodGroup') as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;
    let selectedValues = bloodGroupControl.value ? [...bloodGroupControl.value] : [];
    const bloodGroup = this.bloodGroupList.find(group => group.value === selectedValue);
    const selectedCode = bloodGroup ? bloodGroup.id : selectedValue;

    if (isChecked && selectedCode && !selectedValues.includes(selectedCode)) {
      selectedValues.push(selectedCode);
    } else if (!isChecked && selectedCode) {
      selectedValues = selectedValues.filter(id => id !== selectedCode);
    }

    bloodGroupControl.setValue(selectedValues);
    this.updateDisplayedResults(selectedValues);
  }

  updateDisplayedResults(selectedValues: string[]) {
    const trimmedSelected = selectedValues.map(v => String(v).trim());
    this.displayedResults = trimmedSelected.length === 0
      ? this.allResults
      : this.allResults.filter(result => {
        const bgId = result.bloodGroupID !== undefined && result.bloodGroupID !== null
          ? String(result.bloodGroupID).trim()
          : '';
        const bgVal = result.bloodGroup !== undefined && result.bloodGroup !== null
          ? String(result.bloodGroup).trim()
          : '';
        return trimmedSelected.includes(bgId) || trimmedSelected.includes(bgVal);
      });
  }
}