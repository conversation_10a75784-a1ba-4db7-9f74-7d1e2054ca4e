export class RgVwDecDonEthicalApprvls {
    id: number;
    value: string;
  }

  export class RgTbPARAM {
    id: number;
    value: string;
  }

  
  export class RgTbLab {
    mohTestCode: number;
    testName: string;
  }

  export class RgTbProcedure {
    procId: number;
    procName: string;
  }

  export class RgTbCancerSite {
    icd: string;
    cancerSite: string;
  }

  export class RgTbOrganPARAM {
    id: number;
    value: string;
    organId: number;
    organ: string;
  }

  export class RgVwOccupationMast {
    occupationCode: number;
    occupationName: string;
    occupationNls: string;
    occupationNlsF: string;
    activeYn: string;
    sortOrder: number;
  }

  export interface RgTbDonorBrainDeathDtlsDto {
    dtlId: number | null;
    donorId: number | null;
    eventDate: Date | null;
    admissionDate: Date | null;
    icuStayDays: number | null;
    brainDeathYn: string | null;
    brainDeathCause: string | null;
    brainDeathTime: string | null;
    brainDeathFormYn: string | null;
    test1Examiner1Name: string | null;
    test1Examiner1Date: Date | null;
    test1Examiner2Name: string | null;
    test1Examiner2Date: Date | null;
    test2Examiner1Name: string | null;
    test2Examiner1Date: Date | null;
    test2Examiner2Name: string | null;
    test2Examiner2Date: Date | null;
    remarks: string | null;
  }

  export interface RgHlaTissueTypeDto {
    runId: number | null;
    centralRegNo: number | null;
    donorId: number | null;
    testType: string;
    a_Test: number | null;
    a_1_Test: number | null;
    b_Test: number | null;
    b_1_Test: number | null;
    cw_Test: number | null;
    cw_1_Test: number | null;
    dr_Test: number | null;
    dr_1_Test: number | null;
    drw_Test: number | null;
    drw_1_Test: number | null;
    dq_Test: number | null;
    dq_1_Test: number | null;
    bw_Test: number | null;
    bw_1_Test: number | null;
    activeYn: string;
    createdOn: Date | null;
    remarks: string | null;
}

export interface RgTbDonorCardiacArstDtlsDto {
  donorId: number | null;
  cardiacArstRevYn: string;
  cprDuration: number | null;
  remarks: string;
}

export interface RgTbDonorScreeningDto {
  dtlId: number | null;
  donorId: number;
  paramId: number;
 paramValue: string;
  remarks: string;
  screenType: string;
}

export interface RgTbDeceasedDonorDto {
  crystalNo: string | null;
  donorId: number | null; // Primary Key, might be null before saving a new record
  civilId: number | null;
  fullName: string | null;
  instCode: number | null;
  instPatientId: number | null;
  sex: string | null; // CHAR(1) mapped to string
  dob: Date  | null; // DATE mapped to Date or string (e.g., ISO format)
  maritalStatus: string | null; // CHAR(1) mapped to string
  nationality: number | null;
  occupation: number | null;
  bloodGroup: string | null;
  initialDiag: string | null;
  donorType: string | null; // CHAR(1) mapped to string
  organCoordinatorName: string | null;
  height: number | null;
  weight: number | null;
  bmi: number | null;
  remarks: string | null;
  telNo: number | null;
  address: string | null;
  createdDate: Date | string | null; // DATE mapped to Date or string
  createdBy: number | null;
  modifiedDate: Date | string | null; // DATE mapped to Date or string
  modifiedBy: number | null;
  skinInspectionYn: string | null; // CHAR(1) mapped to string
  skinInspectionDtls: string | null;
  palpationYn: string | null; // CHAR(1) mapped to string
  palpationDtls: string | null;
  physicalExamComments: string | null;
  ethicalApprovalComments: string | null;
  cancerYn: string | null; // CHAR(1) mapped to string
  cancerSite: string | null;
  histology: string | null;
  cancerMetastasisYn: string | null; // CHAR(1) mapped to string
  cancerDiagDate: Date | string | null; // DATE mapped to Date or string
  cancerTreatmentDtls: string | null;
  medSocHistoryComments: string | null;
  ventilationDt: Date | string | null; // DATE mapped to Date or string
  transfusionYn: string | null; // CHAR(1) mapped to string
  prbc: number | null;
  platelet: number | null;
  ffp: number | null;
  albumin: number | null;
  hemodynamicComments: string | null;
  lungPh: number | null;
  lungPao2: number | null;
  lungPaco2: number | null;
  lungPeep: number | null;
  lungFio2: number | null;
  liverComments: string | null;
  kidneyComments: string | null;
  lungComments: string | null;
  heartComments: string | null;
  admissionDate: Date | string | null; // DATE mapped to Date or string
  brainDeathCause: string | null;
  brainDeathTime: Date | string | null; // DATE mapped to Date or string

}


export interface DeceasedDonorExportExcel {
  CrystalNo : string;
  CivilId : number;
  Name  : string; 
  InstCode  : string;
  Gender : string;
  Dob : string;
  MaritalStatus : string;
  Nationality : string;
  Occupation : string;
  BloodGroup : string;
  Bmi : number;
  CancerYn : string;
  VentilationDt : string;
  AdmissionDate : string;
  BrainDeathCause : string;
  BrainDeathTime  : string; 
  TransfusionYn : string ;
  InitialDiag : string;
}

export interface ICDList {
  icd: string;
  disease: string;

}


export class bmiList {
  id : number;
  low : number;
  high : number;
  value: string;
}

export class DeceasedDonorDashboard{
  crystalNo: string | null;
  instCode: number | null;
  walCode: number | null;
  regCode: number | null;
  dob: Date | string | null;
  initalDiag: string | null;
  age: number | null;
  brainDeathYn: string | null;
  briainDeathCause: string | null;
  brianDeathTime: string | null;
  cancerYn: string | null;
  
}


export class DeceasedDonorDashboardDisplay{

  public  crystalNo: String;
public  value: String;

}