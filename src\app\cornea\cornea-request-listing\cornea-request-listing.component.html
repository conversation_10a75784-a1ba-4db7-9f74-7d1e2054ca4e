<h6 class="page-title">Cornea Request Listing</h6>

<div class="content-wrapper mb-2">
  <form [formGroup]="corneaSearchForm">
    <div class="row">
      <!-- <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Civil Id</label>
          <input type="number" min="0" (keyup.enter)="getList()" class="form-control form-control-sm"
            formControlName="patCivilId" />
        </div>
      </div>

      
      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Patient ID</label>
          <input type="number" min="0" (keyup.enter)="getList()" class="form-control form-control-sm"
            formControlName="patientId" />
        </div>
      </div> -->

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Request No</label>
          <input type="text" class="form-control form-control-sm" formControlName="requestNo" pattern="[A-Z0-9]*"
            style="text-transform:uppercase"
            (input)="corneaSearchForm.get('requestNo').setValue($event.target.value.toUpperCase())" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Request Date (from)</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="reqDateFrom" monthNavigator="true" yearNavigator="true"
            placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
          </p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>To</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="reqDateTo" monthNavigator="true" yearNavigator="true"
            placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
          </p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Request Institute</label>
          <ng-select [items]="institeListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
            bindValue="estCode" formControlName="reqInst">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Requested Doctor</label>
          <ng-select [items]="surgeonNamesListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="staffName"
            bindValue="persCode" formControlName="requestedDoctor">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.staffName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <!-- <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Request To Institute</label>
          <ng-select [items]="reqInstitutesFilter" [virtualScroll]="true" placeholder="Select" bindLabel="instName"
            bindValue="instId" formControlName="requestTo">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.instName
              }}</ng-template>
          </ng-select>
        </div>
      </div> -->
     



      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Indication</label>
          <ng-select [items]="corIndicationTypeFilter" [virtualScroll]="true" placeholder="Select" bindLabel="paramName"
            bindValue="paramId" formControlName="indication">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Clear Zone Size</label>
          <ng-select [items]="corClearZoneFilter" [virtualScroll]="true" placeholder="Select" bindLabel="paramName"
            bindValue="paramId" formControlName="sizeClearZone">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Tissue Type</label>
          <ng-select [items]="corTissueTypeFilter" [virtualScroll]="true" placeholder="Select" bindLabel="paramName"
            bindValue="paramId" formControlName="tissueType">
            <ng-template ng-option-tmp let-item="item" let-index="index">
              {{ item.paramName }}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Intended Arrival Date From</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="intendedArrDateFrom" monthNavigator="true"
            yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true"></p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Intended Arrival Date To</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="intendedArrDateTo" monthNavigator="true"
            yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true"></p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Intended Surgery Date From</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="intendedSurDateFrom" monthNavigator="true"
            yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true"></p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-4">
        <div class="form-group">
          <label>Intended Surgery Date To</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="intendedSurDateTo" monthNavigator="true"
            yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true"></p-calendar>
        </div>
      </div>

      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <div class="btn-box">
          <button type="button" (click)="exportExcel()" class="btn btn-primary ripple"> EXCEL</button>
          <button type="reset" (click)="clear()" class="btn btn-sm btn-secondary">Clear</button>
          <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
        </div>
      </div>
    </div>
  </form>
</div>


<div style="margin-top:20px">
  <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>
  <div *ngIf="rowData && rowData.length > 0">
    <p-paginator #paginator rows={{paginationSize}} totalRecords="{{totalRecords}}" (onPageChange)="getList($event)"
      showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
      [rowsPerPageOptions]="[10, 20, 30]">
    </p-paginator>
  </div>
</div>