import { Parameter2 } from './objectModels/parameter-2-model';


export const ENTER_VALID_DATA = 'Enter valid data'
export const GENDER: Array<Parameter2> = [{ "id": "M", "value": "Male" },
{ "id": "F", "value": "Female" }
];
export const YES_NO: Array<Parameter2> = [{ "id": "Y", "value": "Yes" },
{ "id": "N", "value": "No" }
];

export const YES_NO_NULL: Array<Parameter2> = [{ "id": "Y", "value": "Yes" },
{ "id": "N", "value": "No" },
{ "id": null, "value": "All" }
];

export const ACTIVE_YN: Array<Parameter2> = [{ "id": "Y", "value": "Active" },
{ "id": "N", "value": "Not Active" },
{ "id": null, "value": "All" }
];

export const STATUS_PATIENT: Array<Parameter2> = [{ "id": "A", "value": "Active" },
{ "id": "T", "value": "Transplanted" },
{ "id": "E", "value": "Expired" }
];

export const NEW_OLD: Array<Parameter2> = [{ "id": "N", "value": "New" },
{ "id": "O", "value": "Old" }
];

export const FILE_STATUS: Array<Parameter2> = [{ "id": "Y", "value": "completed" },
{ "id": "D", "value": "not completed" }
];

export const ORGAN_STATUS: Array<Parameter2> = [{ "id": "Y", "value": "Yes" },
{ "id": "N", "value": "No" },
{ "id": "D", "value": "Decide Later" }
];

export const ICD_FLAG: Array<Parameter2> = [{ "id": "P", "value": "Primary" },
{ "id": "S", "value": "Secondary" },
{ "id": "O", "value": "Others" }
];


export const BLOOD_GROUP: Array<Parameter2> = [{ "id": "1", "value": "A +" },
{ "id": "2", "value": "A -" },
{ "id": "3", "value": "B +" },
{ "id": "4", "value": "B -" },
{ "id": "5", "value": "O +" },
{ "id": "6", "value": "O -" },
{ "id": "7", "value": "AB +" },
{ "id": "8", "value": "AB -" }
];
export const MAP_BLOOD_GROUP: Array<Parameter2> = [{ "id": "1", "value": "A POS" },
{ "id": "1", "value": "A POSITIVE " },
{ "id": "1", "value": "A+ " },
{ "id": "2", "value": "A NEG" },
{ "id": "2", "value": "A NEGATIVE " },
{ "id": "2", "value": "A- " },
{ "id": "3", "value": "B POS" },
{ "id": "3", "value": "B POSITIVE " },
{ "id": "3", "value": "B+ " },
{ "id": "4", "value": "B NEG" },
{ "id": "4", "value": "B NEGATIVE " },
{ "id": "4", "value": "B- " },
{ "id": "5", "value": "O POS" },
{ "id": "5", "value": "O POSITIVE " },
{ "id": "5", "value": "O+ " },
{ "id": "6", "value": "O NEG" },
{ "id": "6", "value": "O NEGATIVE " },
{ "id": "6", "value": "O- " },
{ "id": "7", "value": "AB POS" },
{ "id": "7", "value": "AB POSITIVE " },
{ "id": "7", "value": "AB+ " },
{ "id": "8", "value": "AB NEG" },
{ "id": "8", "value": "AB NEGATIVE " },
{ "id": "8", "value": "AB- " }
];

export const Marital_Status_Type: Array<Parameter2> = [{ "id": "M", "value": "Married" },
{ "id": "S", "value": "Single" },
{ "id": "D", "value": "Divorced" },
{ "id": "W", "value": "Widow/Widower" },
{ "id": "O", "value": "Other" }]


export const HLPC_TEST: Array<Parameter2> = [{ "id": "10071", "value": "Haemoglobin A (Adult)" },
{ "id": "10072", "value": "Haemoglobin A2" },
{ "id": "10073", "value": "Haemoglobin F" },
{ "id": "10074", "value": "Haemoglobin S" },
{ "id": "10076", "value": "Haemoglobin D" },
{ "id": "10077", "value": "Other Variant Haemoglobins" },
{ "id": "10075", "value": "Haemoglobin C" },
{ "id": "17287", "value": "HPLC Impression" }]




export const LOGGED_IN_USER_TYPE: string = 'reg-user-type';
export const LOGGED_IN_USER: string = 'reg-user';

export const HTTP_FAILED_STATUS_MSG: string = 'Failed to connect to server, Please check if HTTP server is running';
export const LATITUDE_ALERT: string = 'Enter valid range (+90.000000 to -90.000000)';
export const LONGITUDE_ALERT: string = 'Enter valid range (+180.000000 to -180.000000)';

export const EXPORT_SERVICE_COLUMNS: Array<string> = ['comments', 'walName', 'regName']



////Char Setting
export const CHAR_BG_COLOR = ['#9fdef1', '#2a5d78', '#f6aa54', '#b9e52f', '#e4c7d0',
  '#077e75', '#758aff', '#6a9662', '#c84a09', '#7a1f52',
  '#4d9b9a', '#c7bae3', '#c60f71', '#2343eb', '#7f8e20',
  '#617d8c', '#b65149', '#37718e', '#2accbb', '#385165'];
export const CHAR_GENERAL_OPTION = {
  legend: {
    display: false
  },
  scales: {
    yAxes: [{
      ticks: {
        beginAtZero: true,
        stepSize: 1
      }

    }],

  }
};
export const CHAR_OPTION_2 = {
  legend: {
    display: false
  },
  scales: {
    yAxes: [{
      ticks: {
        beginAtZero: true,
        stepSize: 10
      }

    }],

  }
};
export const CHAR_GENERAL_OPTION_PERCENTAGE = {

  legend: {
    display: true
  },

  scales: {
    yAxes: [{
      ticks: {
        beginAtZero: true,
        stepSize: 10,
        end: 100
      },

    }],

  }
};
export const CHAR_PIE_OPTION = {
  legend: {
    display: true,
    fontSize: 5,
    position: "top"
  }
};



export const CHAR_PIE_OPTION_GENETIC_BLOOD = {
  legend: {
    display: false,
    fontSize: 3,
    position: "bottom"
  }
};

export const CHAR_PIE_OPTION_GENETIC_BLOOD_RIGHT = {
  legend: {
    display: true,
    fontSize: 3,
    position: "right"
  }
};


export const VACCINE_YEAR: Array<Parameter2> =
  [{ "id": "1", "value": "2010" },
  { "id": "2", "value": "2011" },
  { "id": "3", "value": "2012" },
  { "id": "4", "value": "2013" },
  { "id": "5", "value": "2014" },
  { "id": "6", "value": "2015" },
  { "id": "7", "value": "2016" },
  { "id": "8", "value": "2017" },
  { "id": "9", "value": "2018" },
  { "id": "10", "value": "2019" },
  { "id": "11", "value": "2020" },
  { "id": "12", "value": "2021" },
  { "id": "13", "value": "2022" },
  { "id": "14", "value": "2023" },
  { "id": "15", "value": "2024" },
  { "id": "16", "value": "2025" },
  { "id": "17", "value": "2026" },
  { "id": "18", "value": "2027" },
  { "id": "19", "value": "2028" },
  { "id": "20", "value": "2029" },
  { "id": "21", "value": "2030" }
  ];


export const VACCINE_MONTH: Array<Parameter2> =
  [{ "id": "1", "value": "1" },
  { "id": "2", "value": "2" },
  { "id": "3", "value": "3" },
  { "id": "4", "value": "4" },
  { "id": "5", "value": "5" },
  { "id": "6", "value": "6" },
  { "id": "7", "value": "7" },
  { "id": "8", "value": "8" },
  { "id": "5", "value": "9" },
  { "id": "6", "value": "10" },
  { "id": "7", "value": "11" },
  { "id": "8", "value": "12" }

  ];

export const PERIOD_DESC: Array<Parameter2> =
  [{ "id": "1", "value": "At Birth" },
  { "id": "2", "value": "2 Months" },
  { "id": "3", "value": "4 Months" },
  { "id": "4", "value": "6 Months" },
  { "id": "5", "value": "12 Months" },
  { "id": "6", "value": "13 Months" },
  { "id": "7", "value": "18 Months" },
  { "id": "8", "value": "24 Months" },
  ];




export const FIRST_EXAMINATION: number = 1;
export const SECOND_EXAMINATION: number = 2;

export const FIRST_EXAM_SET: number = 1;
export const SECOND_EXAM_SET: number = 2;

export const MSG_NO_RECORD_REG_NO: string = 'No Record Found with Entered Regstration No.';
export const MSG_NO_RECORD_CIVIL_ID: string = 'No Record Found in the Civil ID.';
export const MSG_ERR_FETCH_NRS_DATA: string = 'Fetching Demographic information from ROP.';

export const SEARCH_BY_REGNO: string = 'regNo';
export const SEARCH_BY_CIVILID: string = 'civilID';

export const ANC_STATUS: Array<Parameter2> = [
  { "id": "P", "value": "P -Pending" },
  { "id": "A", "value": "A -Approved" },
  { "id": "R", "value": "R -Rejected" },
  { "id": "C", "value": "C -Cancelled" }
];

export const SMOKING_STATUS_OPTION: Array<Parameter2> = [
  { "id": "S", "value": "Smoker" },
  { "id": "E", "value": "Ex-Smoker" },
  { "id": "P", "value": "Passive Smoker " },
  { "id": "N", "value": "Never Smoke" }
];

export const FILE_STATUS_OPTION: Array<Parameter2> = [
  { "id": "51", "value": "Active" },
  { "id": "52", "value": "Transferred Out" },
  { "id": "53", "value": "Transferred In" },
  { "id": "54", "value": "Expired " }
];

export const BMI_OPTION: Array<Parameter2> = [
  { "id": "51", "value": "Active" },
  { "id": "52", "value": "Transferred Out" },
  { "id": "53", "value": "Transferred In" },
  { "id": "54", "value": "Expired " }
];

export const MONTHS_NAME = [
  { code: 1, name: 'January' },
  { code: 2, name: 'February' },
  { code: 3, name: 'March' },
  { code: 4, name: 'April' },
  { code: 5, name: 'May' },
  { code: 6, name: 'June' },
  { code: 7, name: 'July' },
  { code: 8, name: 'August' },
  { code: 9, name: 'September' },
  { code: 10, name: 'October' },
  { code: 11, name: 'November' },
  { code: 12, name: 'December' }
];
export const HBA1C_OPTION: Array<Parameter2> =
  [{ "id": "0", "value": "<7" },
  { "id": "1", "value": "7-8" },
  { "id": "2", "value": ">8" }
  ];

export const KETONES =
  ["Present", "Absent"];
export const FAMILY_HISTORY_DIABETES_REG: Array<Parameter2> = [
  { "id": "1551", "value": "Parents" },
  { "id": "1552", "value": "Siblings" },
  { "id": "1553", "value": "Relatives" }
];

export const VISIT_TYPE: Array<Parameter2> = [
  { "id": "F", "value": "Registration" },
  { "id": "R", "value": "Follow Up" }
];

export const HB_LEVEL: Array<Parameter2> = [
  { "id": "N", "value": "Normal" },
  { "id": "L", "value": "Mild" },
  { "id": "M", "value": "Moderate" },
  { "id": "S", "value": "Severe" }
];
export const WH_PROGRESS: Array<Parameter2> = [
  { "id": "I", "value": "Improved" },
  { "id": "W", "value": "Worse" },
  { "id": "N", "value": "Not Improved" },
];
export const WA_PROGRESS: Array<Parameter2> = [
  { "id": "I", "value": "Improved" },
  { "id": "W", "value": "Worse" },
  { "id": "N", "value": "Not Improved" },
];
export const HA_PROGRESS: Array<Parameter2> = [
  { "id": "I", "value": "Improved" },
  { "id": "W", "value": "Worse" },
  { "id": "N", "value": "Not Improved" },
];

export const HCA_PROGRESS: Array<Parameter2> = [
  { "id": "I", "value": "Improved" },
  { "id": "W", "value": "Worse" },
  { "id": "N", "value": "Not Improved" },
];

export const HA_RATING: Array<Parameter2> = [
  { "id": "M", "value": "Moderaate Stunting" },
  { "id": "S", "value": "Severe Stunting" },
  { "id": 'N', "value": 'Normal' },
  { "id": 'I', "value": 'Severely Giant' },
  { "id": 'G', "value": 'Moderate Giant' }
];

export const WA_RATING: Array<Parameter2> = [
  { "id": "M", "value": "Moderaate UnderWeight" },
  { "id": "S", "value": "Severe UnderWeight" },
  { "id": 'N', "value": 'Normal' }
];

export const WH_RATING: Array<Parameter2> = [
  { "id": "O", "value": "Obese" },
  { "id": "V", "value": "OverWeight" },
  { "id": "M", "value": "Moderate Wasting" },
  { "id": "S", "value": "Severe Wasting" },
  { "id": 'N', "value": 'Normal' }
];

export const HCA_RATING: Array<Parameter2> = [
  { "id": "I", "value": "Microcephaly" },
  { "id": "A", "value": "Macrocephaly" },
];

export const VISIT_TYPES = [
  { id: 'F', name: 'Follow Up' },
  { id: 'R', name: 'Register' }
];

export const WH_LEVEL = [{
  level : [
  { id: 'O', value: 'Obese' },
  { id: 'V', value: 'Overweight' },
  { id: 'M', value: 'Moderate Wasting' },
  { id: 'S', value: 'Severe Wasting' },
  { id: 'N', value: 'Normal' }],
  color : [
    {id:'O' , value: 'rgb(175, 141, 164)'},
    {id:'V' , value: 'rgb(255, 196, 70)'},
    {id:'M' , value: 'rgb(228, 107, 51)'},
    {id:'S' , value: 'rgb(243, 36, 36)'},
    {id:'N' , value: 'rgb(50, 155, 41)'},

  ]
}];

export const HA_LEVEL = [{
  level : [
  { id: 'M', value: 'Moderate Stunting' },
  { id: 'S', value: 'Severe Stunting' },
  { id: 'N', value: 'Normal' }],
  color : [
    {id:'M' , value: 'rgb(228, 107, 51)'},
    {id:'S' , value: 'rgb(243, 36, 36)'},
    {id:'N' , value: 'rgb(50, 155, 41)'},
  ]
}];

export const WA_LEVEL = [{
  level : [
  { id: 'M', value: 'Moderate Underweight' },
  { id: 'S', value: 'Severe Underweight' },
  { id: 'N', value: 'Normal' }],
  color : [
    {id:'M' , value: 'rgb(228, 107, 51)'},
    {id:'S' , value: 'rgb(243, 36, 36)'},
    {id:'N' , value: 'rgb(50, 155, 41)'},
  ]
}];


export const DATE_FORMATS = {
  STANDARD: 'yyyy-MM-dd',
  SHORT: 'MM/dd/yyyy',
  FULL_DATE: 'EEEE, MMMM d, yyyy',
  SHORT_DATE: 'MM/dd/yyyy',
  EUROPEAN: 'dd/MM/yyyy',
  TIME: 'HH:mm:ss',
  DATE_TIME: 'yyyy-MM-dd HH:mm:ss',
  ISO: "yyyy-MM-dd'T'HH:mm:ss'Z'",
  FRIENDLY: 'MMM d, yyyy',
};

export const EYE_OPERATED: Array<Parameter2> = [
  { "id": "B", "value": "Both" },
  { "id": "L", "value": "Left" },
  { "id": "R", "value": "Right" }
];

export const RELATED_DONOR_DEGREE_LIST: Array<Parameter2> = [
  { id: "1", value: "1st degree" },
  { id: "2", value: "2nd degree" },
  { id: "3", value: "3rd degree" },
  { id: "4", value: "4th degree" },
];

export const HLA_FIELD_KEYS: string[] = [
  'a_Test', 'a_1_Test', 'b_Test', 'b_1_Test', 'cw_Test', 'cw_1_Test',
  'dr_Test', 'dr_1_Test', 'drw_Test', 'drw_1_Test', 'dq_Test', 'dq_1_Test',
  'bw_Test', 'bw_1_Test'
];

export const duration: Array<Parameter2> = [{ "id": "N", "value": "New" },
  { "id": "C", "value": "Chronic" }
]
