<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Heart Registry</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px;">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regNo" class="form-control input-sm"
            (keyup.enter)="search()" />
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>

<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6>
                        <i class="fas fa-male" *ngIf="alive" (click)="updateDeathDetails()"
                            class="alive-icon fas fa-user" title="patient is alive "></i>
                        <i class="fas fa-male" *ngIf="!alive" class="dead-icon fas fa-user-slash"
                            title="patient is dead "></i>
                        Patient Details
                    </h6>

                    <div>
                        <span class="de-activate-btn" *ngIf="alive" (click)="updateDeathDetails()">De-Activate</span>
                        <button ngbPanelToggle class="btn btn-link p-0">
                            <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                        </button>
                    </div>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" #patientDetails
                    (callMethod)="callMpiMethod()"></app-patient-details>
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>

<form [formGroup]="heartForm">
    <div class="accordion register" *ngIf="!alive">
        <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
            <ngb-panel id="patientDetails" id="ngb-panel-0">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>
                            <i class="fas fa-male" *ngIf="alive" (click)="updateDeathDetails()"
                                class="alive-icon fas fa-user" title="patient is alive "></i>
                            <i class="fas fa-male" *ngIf="!alive" class="dead-icon fas fa-user-slash"
                                title="patient is dead "></i>
                            Death Details
                        </h6>
                        <div>
                            <span class="de-activate-btn" *ngIf="alive"
                                (click)="updateDeathDetails()">De-Activate</span>
                            <button ngbPanelToggle class="btn btn-link p-0">
                                <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                            </button>
                        </div>

                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <app-death-details [currentCivilId]="currentCivilId" [estCode]="estCode" [patientId]="patientId"
                        #deathDetails>
                    </app-death-details>
                </ng-template>
            </ngb-panel>
        </ngb-accordion>
    </div>

    <div class="accordion register px-1">
        <div class="d-flex gap-5 card-height">
            <div class="w-100 ">
                <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
                    <ngb-panel id="patientDetails" id="ngb-panel-0">
                        <ng-template ngbPanelHeader let-opened="opened">
                            <div class="d-flex align-items-center justify-content-between card-head card-head-custom"
                                [ngClass]="opened ? 'opened' : 'collapsed'">
                                <h6 class="dark-brown">BMI Details</h6>
                            </div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="row ">
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <div class="form-group">
                                        <label>Body weight <span class="mdtr">( kg )</span></label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b"
                                            formControlName="bmiBodyWidth" (blur)="getBmi()">

                                        <div class="tooltiptext" *ngIf="heartForm.get('bmiBodyWidth')?.errors?.pattern">
                                            {{'Width should be not more than 140'}}
                                        </div>


                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <div class="form-group">
                                        <label>Body height <span class="mdtr">( cm )</span></label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b"
                                            formControlName="bmiBodyHeight" (blur)="getBmi()">

                                        <div class="tooltiptext"
                                            *ngIf="heartForm.get('bmiBodyHeight')?.errors?.pattern">
                                            {{'Height should be not more than 190'}}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <div class="form-group">
                                        <label>BMI</label>
                                        <input type="number" class="form-control form-control-sm" formControlName="bmi"
                                            readonly>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <div class="form-group">
                                        <label>Blood Group </label>
                                        <ng-select #entryPoint appendTo="body" [items]="bloodGroupList"
                                            [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                            formControlName="bloodGroup">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                            </ng-template>
                                        </ng-select>

                                    </div>
                                </div>
                                <div class="col-lg-12 col-md-12 col-sm-12">
                                    <div class="form-group">
                                        <label>Cause of Heart Disease</label>
                                        <div class="form-inputs">
                                            <ng-select #entryPoint appendTo="body" [items]="icdHeartShortList"
                                                [virtualScroll]="true" placeholder="Select" bindLabel="codeDisease"
                                                bindValue="code" formControlName="causeOfKindeyDisease">
                                                <ng-template ng-option-tmp let-item="item"
                                                    let-index="index">{{item.codeDisease}}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
            </div>
            <div class="w-100">
                <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
                    <ngb-panel id="patientDetails" id="ngb-panel-0">
                        <ng-template ngbPanelHeader let-opened="opened">
                            <div class="d-flex align-items-center justify-content-between card-head"
                                [ngClass]="opened ? 'opened' : 'collapsed'">
                                <h6 class="dark-brown">Comorbid Disease</h6>

                                <button class="btn btn-primary add-btn pull-right" style="width:55px ;" type="button"
                                    (click)="addRec('comorbidDiseaseListGrid')">Add</button>
                            </div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div>
                                <div class="form-group">

                                    <ag-grid-angular style="width: 100%; height: 200px;" class="ag-theme-balham"
                                        [gridOptions]="comorbidDiseaseListGrid" [rowData]="icdData"
                                        [columnDefs]="columnDefs" enableSorting enableFilter rowSelection="single"
                                        singleClickEdit=true [enableColResize]="true"
                                        [frameworkComponents]="frameworkComponents" row-height="22"
                                        (gridReady)="onReady($event, 'comorbidDiseaseListGrid')">
                                    </ag-grid-angular>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
            </div>

        </div>
    </div>
</form>

<div class="btn-container mr-2">
    <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callCase()">Case Details</button>
    <button class="btn btn-sm btn-secondary" (click)="clearData()"> Clear</button>
    <button class="btn btn-sm btn-primary" (click)="save()"> Save</button>
</div>