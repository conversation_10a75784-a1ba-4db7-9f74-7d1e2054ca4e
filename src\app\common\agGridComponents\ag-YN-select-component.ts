import { Component } from '@angular/core';
import { AgEditorComponent, ICellEditorAngularComp, INoRowsOverlayAngularComp } from 'ag-grid-angular';
import { Parameter2 } from '../objectModels/parameter-2-model';
import { Parameter } from '../objectModels/parameter-model';
//import { INoRowsOverlayAngularComp } from '@ag-grid-community/angular';

@Component({
    selector: 'app-gender-renderer',
    template: ` <select [(ngModel)]="value" class="form-control form-control-sm" (change)="onChange($event)">
                <option [value]="val.id" *ngFor="let val of data" >
                    {{ val.value }} 
                </option>
            </select> `,
})

export class YNCellRenderer implements ICellEditorAngularComp {
    params: any;
    value: any;
    data: Array<Parameter2>; // [{ "id": "", "value": "" }];
    agInit(params): void {
        //this.value = params.value;
        this.params = params;

        this.value = params.values.filter(s => s.value == params.value).map(s => s.id).toString();
        this.data = params.values;
    }

    isPopup() {
        return true;
    }

    getValue() {
        let d;
        d = this.data.filter(s => s.id == this.value).map(s => s.value).toString();

        return d;
        //return this.value;
    }

    onChange(event) { 

    }
}