<div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="head-title">Transplant Registration Form</h6>
</div>



<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head px-2"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Demographic Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
               
                <app-patient-details [submitted]="submitted" [patientForm]="patientForm" #patientDetails>
                </app-patient-details>

            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>


<div class="col-sm-12 px-0">
    <form *ngIf="transplantRegistryForm" [formGroup]="transplantRegistryForm">  
    <div class="section-panel">
        <div class="card">
            <div class="card-header"><h6>Transplant Registration</h6></div>
            <div class="card-body card-min-ht">
                <div class="row">
                    <div class="col-sm-4">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>Premptive Transplant</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" formControlName="premptiveTransplant"
                                                value="Y">
                                            Yes
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" formControlName="premptiveTransplant"
                                                value="N">
                                            No
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label>Transplant Date <span class="mdtr">*</span></label>
                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                        formControlName="exDate" [ngModelOptions]="{standalone: true}"
                                        monthNavigator="true" [minDate]=today yearRange="1930:2030" yearNavigator="true"
                                        showButtonBar="true"></p-calendar>
                                </div>
                                <!-- <span *ngIf="submitted && f.exDate.errors" class="tooltiptext">{{'Expiry Date is required'}}</span> -->


                            </div>
                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label>Transplant Country <span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="transplantCountry" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-8">
                        <div class="row">

                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Transplant Remarks<span class="mdtr">*</span></label>
                                    <input type="text" class="form-control form-control-sm" formControlName="transplantRemarks">
                                </div>
                            </div>

                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label>Transplant Type <span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="transplantType" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label>Transplant Subtype <span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="transplantSubtype" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Surgery Type <span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="surgeryType" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Surgery Sub<span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="surgerySub" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="col-sm-4">
                        <div class="row">

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>Explanted Liver Path<span class="mdtr">*</span></label>
                                    <input type="text" class="form-control form-control-sm" formControlName="explantedLiverPath">
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>No of days in Ward<span class="mdtr">*</span></label>
                                    <input type="text" class="form-control form-control-sm" formControlName="noOfDaysInWard">
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>No of days in ICU<span class="mdtr">*</span></label>
                                    <input type="text" class="form-control form-control-sm" formControlName="noOfDaysInICU">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-8">
                        <div class="row">
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Re-Admitted?</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled"
                                            formControlName="ReAdmitted" [value]="true" (change)="updateRecordStatus()" />
                                            Yes
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled"
                                             formControlName="ReAdmitted"[value]="false" (change)="updateRecordStatus()" />
                                            No
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Conditionally display these fields -->
                            <div *ngIf="recordEnabled" class="col-sm-2">

                                <div class="form-group">
                                    <label>Re-Admission Reason<span class="mdtr">*</span></label>
                                    <input type="text"  formControlName="ReAdmissionReason"class="form-control form-control-sm">
                                </div>
                            </div>
                            <div *ngIf="recordEnabled" class="col-sm-2">

                                <div class="form-group">
                                    <label>Re-Admitted Days<span class="mdtr">*</span></label>
                                    <input type="text" formControlName="ReAdmittedDays" class="form-control form-control-sm">
                                </div>
                            </div>



                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Surgical Intervention </label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled1"
                                                [value]="true"formControlName="SurgicalIntervention" (change)="updateRecordStatus()" />
                                            Yes
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled1"
                                                [value]="false" formControlName="SurgicalIntervention" (change)="updateRecordStatus()" />
                                            No
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div *ngIf="recordEnabled1" class="col-sm-2">
                                <div class="form-group">
                                    <label>Intervention Type <span class="mdtr">*</span></label>
                                    <input type="text" formControlName="InterventionType" class="form-control form-control-sm" />
                                </div>
                            </div>

                            <div *ngIf="recordEnabled1" class="col-sm-2">
                                <div class="form-group">
                                    <label>Intervention Reason <span class="mdtr">*</span></label>
                                    <input type="text" formControlName="InterventionReason" class="form-control form-control-sm" />
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="row">

                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>Follow Up Hospital<span class="mdtr">*</span></label>

                                    <ng-select #entryPoint appendTo="body" [virtualScroll]="true"
                                        formControlName="regInst" placeholder="Select" bindLabel="estName"
                                        bindValue="estCode">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>Others(Specify)<span class="mdtr">*</span></label>
                                    <input type="text" class="form-control form-control-sm" formControlName="Others">
                                </div>
                            </div>
                        </div></div>
                            <div class="col-sm-8">
                                <div class="row">
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Past Transplant Rejection</label>

                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled2"
                                            [value]="true" formControlName="PastTransplantRejection" (change)="updateRecordStatus()" />
                                        Yes
                                    </div>

                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" [(ngModel)]="recordEnabled2"
                                            [value]="false" formControlName="PastTransplantRejection" (change)="updateRecordStatus()" />
                                        No
                                    </div>
                                </div>
                            </div>


                            <div *ngIf="recordEnabled2" class="col-sm-2">
                                <div class="form-group">
                                    <label>Date of Rejection <span class="mdtr">*</span></label>
                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                        formControlName="dateRejection" [ngModelOptions]="{standalone: true}"
                                        monthNavigator="true" [minDate]=today yearRange="1930:2030" yearNavigator="true"
                                        showButtonBar="true"></p-calendar>
                                </div>

                            </div>

                            <div *ngIf="recordEnabled2" class="col-sm-2">
                                <div class="form-group">
                                    <label>Rejection Grade<span class="mdtr">*</span></label>
                                    <input type="text" formControlName="RejectionGrade"  class="form-control form-control-sm">
                                </div>
                            </div>


                            <div *ngIf="recordEnabled2" class="col-sm-2">
                                <div class="form-group">
                                    <label>Rejection Prescription<span class="mdtr">*</span></label>
                                    <input type="text" formControlName="RejectionPrescription" class="form-control form-control-sm">
                                </div>
                            </div>
                       
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Biopsy</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" formControlName="Biopsy"
                                                value="Y">
                                            Yes
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" formControlName="Biopsy"
                                                value="N">
                                            No
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </form>
</div>

<div class="col-sm-12 px-0">
    <div class="section-panel">
        <div class="card">
            <div class="card-header"><h6>Immunosuppressive & prophylactic Medicines</h6></div>
            <div class="card-body card-min-ht">
                <div class="row">
                    <div class="tabs">
                        <tabset>

                            <tab heading="Induction(up to 7 days)" id="Induction">

                                <div class="content-wrapper content-border">
                                 
                                        <div class="row">
                                            <div class="col-sm-3 pr-0">
                                                <div class="mcard">

                                                    <div class="mcard-body">
                                                        <table class="table table-lg">
                                                            <thead>
                                                                <tr>
                                                                    <th width="10%"></th>
                                                                    <th>Medicine</th>
                                                                    <th>Dose</th>
                                                                    <th>Level</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <ngb-pagination class="d-flex justify-content-center"
                                                            [(page)]="page3" [pageSize]="pageSize">
                                                        </ngb-pagination>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    
                                </div>
                        </tab>
                        <tab heading="Immediate(7 days-1 month)" id="Immediate">

                            <div class="content-wrapper content-border">
                               
                                    <div class="row">
                                        <div class="col-sm-3 pr-0">
                                            <div class="mcard">

                                                <div class="mcard-body">
                                                    <table class="table table-lg">
                                                        <thead>
                                                            <tr>
                                                                <th width="10%"></th>
                                                                <th>Medicine</th>
                                                                <th>Dose</th>
                                                                <th>Level</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <ngb-pagination class="d-flex justify-content-center"
                                                        [(page)]="page3" [pageSize]="pageSize">
                                                    </ngb-pagination>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                             
                            </div>
                    </tab>
                    <tab heading="Early(1-3 months)" id="Early">

                        <div class="content-wrapper content-border">
                          
                                <div class="row">
                                    <div class="col-sm-3 pr-0">
                                        <div class="mcard">

                                            <div class="mcard-body">
                                                <table class="table table-lg">
                                                    <thead>
                                                        <tr>
                                                            <th width="10%"></th>
                                                            <th>Medicine</th>
                                                            <th>Dose</th>
                                                            <th>Level</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <ngb-pagination class="d-flex justify-content-center"
                                                    [(page)]="page3" [pageSize]="pageSize">
                                                </ngb-pagination>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                           
                        </div>
                </tab>
                <tab heading="Late(>3 months)" id="Late">

                    <div class="content-wrapper content-border">
                       
                            <div class="row">
                                <div class="col-sm-3 pr-0">
                                    <div class="mcard">

                                        <div class="mcard-body">
                                            <table class="table table-lg">
                                                <thead>
                                                    <tr>
                                                        <th width="10%"></th>
                                                        <th>Medicine</th>
                                                        <th>Dose</th>
                                                        <th>Level</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td></td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <ngb-pagination class="d-flex justify-content-center"
                                                [(page)]="page3" [pageSize]="pageSize">
                                            </ngb-pagination>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                    </div>
            </tab>
            
                    </tabset>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>