import { environment } from './../../environments/environment';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { RenalDonorPatient} from './../_models/renal_donor_patient.model';
import { TissueTypeModel } from '../common/objectModels/tissueTypeModel';
import { RenalDonor} from './../_models/renal-donor.model';
import { labTestModel } from '../common/objectModels/labTestModel'
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';
import { applyMixins } from 'rxjs/internal-compatibility';
// import ServiceUrlConstants from '../common/serviceUrl.constants';



@Injectable()
export class RegistryService {

	constructor(private http: HttpClient) { }

	searchRegistry(data: any): Observable<any> {
		return this.http.post(environment.eregistryApi +"api/eldr/registries/searchList", data	);			
	}

	getAllPatient():Observable<any> {
		return this.http.get(AppUtils.GET_ALL_PATIENT);
	}

	getDonorDetails(Data,Type){
		if(Type == 'civilId'){
			return this.http.get(AppUtils.REG_DONOR_DTL+Data);
		}else{
			return this.http.get(AppUtils.REG_DONOR_DTL_BY_ID+Data);
		}
	}
	getDonorByCivilId(Data){
		return this.http.get(AppUtils.REG_DONOR_DTL+Data);
	}

	getDonorByDonorID(Data){
		return this.http.get(AppUtils.REG_DONOR_DTL_BY_ID+Data);
	}

	saveRenalDonorPatient(data : RenalDonorPatient):Observable<any> 
	{
		return this.http.post(AppUtils.SAVE_RENAL_DONOR_PATIENT, data);
	}

	saveDonor(data : RenalDonor):Observable<any> 
	{
		return this.http.post(AppUtils.SAVE_RENAL_DONOR, data);
	}

	saveLabTest(data : labTestModel):Observable<any> 
	{
		return this.http.post(AppUtils.SAVE_LAB_TEST, data);	
	}

	saveTissueType(data : TissueTypeModel):Observable<any> 
	{
		return this.http.post(AppUtils.SAVE_TISSUE, data);
	}
	saveTissueTypeList(data : any):Observable<any> 
	{
		return this.http.post(AppUtils.SAVE_TISSUE_LIST, data);
	}

	getScoresByDonorId(Data){
		return this.http.get(AppUtils.GET_RENAL_SCORES_BY_DONOR_ID+"?donorID="+Data);
	}

	getScoresByRegNo(Data) {
		return this.http.get(AppUtils.GET_RENAL_SCORES_BY_REGNO+"?centralRegNo="+Data)
	  }

	// getHlaByDonorId(Data) {
	// 	return this.http.get(AppUtils.DONOR_HLA+Data)
	// }

	getDeathDetails(estCode , patientId){
		return this.http.get(AppUtils.FETCH_PATIENT_DEATH_DETAILS_FROM_ALSHIFA+"/"+estCode+"/"+patientId)
	}

	getCompareHlaScore(regNo,donorId){
		return this.http.get(AppUtils.GET_COMPARE_HLA_SCORE, { params: new HttpParams().set("regNo", regNo).set("donorId",donorId) })

	}

	
	
}