import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import * as AppUtils from '../../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class AncListingService {

  constructor(private http:HttpClient) { 

  }
  getAncRequestListing(date): Observable<any> {
  
    return this.http.post(AppUtils.GET_REQUEST_ANC_DETAILS, date);
   
  }
  
}
