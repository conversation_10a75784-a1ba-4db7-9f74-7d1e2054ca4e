import { AuthTokenService } from './auth-token.service';
import { EmptyObservable } from 'rxjs/observable/EmptyObservable';
import { BehaviorSubject, Observable, Subject, pipe, throwError } from 'rxjs';
import { Injectable, Injector } from '@angular/core';
import {
    HttpInterceptor, HttpRequest, HttpHandler, HttpSentEvent, HttpHeaderResponse, HttpProgressEvent,
    HttpResponse, HttpUserEvent, HttpErrorResponse
} from '@angular/common/http';
import * as CommonConstants from '../_helpers/common.constants';
import {  } from 'rxjs/Observable';
import { Router } from '@angular/router';
import {  } from 'rxjs/observable/ErrorObservable';
import { catchError, filter, take, switchMap, finalize } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {
    isRefreshingToken = true;
    tokenSubject: BehaviorSubject<string> = new BehaviorSubject<string>(null);

    constructor(private inj: Injector, private router: Router) { }

    /**
     * Http Interceptor
     */
    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<any | HttpSentEvent | HttpHeaderResponse | HttpProgressEvent |
        HttpResponse<any> | HttpUserEvent<any>> {
        const auth = this.inj.get(AuthTokenService);
        return next.handle(this.addToken(request))
            .pipe(
            catchError((error, ca) => {
                if (error instanceof HttpErrorResponse) {
                    /**
                     * refresh token expired, then log out.
                     * error description is using to distinguish whether it is access token expired or refresh token expired
                     */
                    if (error && error.status === 401 && error.error && error.error.error === 'invalid_token'
                        && error.error.error_description.indexOf('refresh') !== -1
                        && error.error.error_description.indexOf('expired') !== -1) {
                        this.isRefreshingToken = true;
                        auth.logoutUser(100); // modifed for msa
                    } else {
                        switch ((<HttpErrorResponse>error).status) {
                            case 401:
                                return this.handle401Error(request, next, auth, error); // access token expired
                            case 400:
                                return this.handle400Error(error, auth); // invalid acess/refresh token
                            default:
                                 return throwError(error);
                        }
                    }
                } else {
                    EmptyObservable.create(error);
                   
                }
            })
            );
    }

    /**
     * To clone the http request and append the required headers
     * @param req htt request
     */
    // addToken(req: HttpRequest<any>): HttpRequest<any> {
    //     let customReq: any;
    //     const client_id = CommonConstants.CLIENT_ID;
    //     const client_secret = 'secret';
    //     const basicheader = btoa(client_id + ':' + client_secret);
    //     if (req.url.indexOf('token?grant_type') !== -1) {
    //         customReq = req.clone({
    //             headers: req.headers.set('Authorization', 'Basic ' + basicheader)
    //         });
    //     } else {
    //         customReq = req.clone({
    //             headers: req.headers.set('Content-Type', 'application/json')
    //                 .set('Authorization', 'Bearer ' + localStorage.getItem(CommonConstants.TOKEN))
    //         });
    //     }
    //     return customReq;
    // }
    addToken(req: HttpRequest<any>): HttpRequest<any> {
        const client_id = CommonConstants.CLIENT_ID;
        const client_secret = 'secret';
        const basicheader = btoa(client_id + ':' + client_secret);

        if (req.url.indexOf('token?grant_type') !== -1) {
            // Token request: only add Basic Authorization
            return req.clone({
                headers: req.headers.set('Authorization', 'Basic ' + basicheader)
            });
        } else if (req.url.indexOf('validateLoggedUser') > -1) {
            return req.clone({
                headers: req.headers.set('Authorization', 'Basic ' + basicheader)
            });
        } else if (req.url.indexOf('saveOTPValidatedPerInfo') > -1 || req.url.indexOf('getOtp') > -1 || req.url.indexOf('validateMpiDetails') > -1  /*|| request.url.indexOf('validateOtp') > -1*/) {
            return req.clone({
                headers: req.headers.set(
                    'Authorization',
                    'Bearer ' +
                    sessionStorage.getItem(CommonConstants.STORAGE_PREAUTH_ACCESS_TOKEN)
                )
            });
        }

        if (req.body instanceof FormData) {
            // For FormData (file uploads), do NOT set Content-Type
            return req.clone({
                headers: req.headers.set('Authorization', 'Bearer ' + localStorage.getItem(CommonConstants.TOKEN))
            });
        }

        // For JSON or other requests, set Content-Type to application/json
        return req.clone({
            headers: req.headers
                .set('Content-Type', 'application/json')
                .set('Authorization', 'Bearer ' + localStorage.getItem(CommonConstants.TOKEN))
        });
    }
    

    /**
     * To handle 400 error, for bad credentials throw exception.
     * @param error
     *
     */
    handle400Error(error, auth: any) {
        if (error && error.status === 400 && error.error) {
            if (error.error.error === 'invalid_grant' && error.error.error_description.toLowerCase() === 'bad credentials') { // wrong user credentials
                return throwError('');
            } else if (error.error.error === 'invalid_grant' &&
                        error.error.error_description.indexOf('locked') !== -1) { //account locked
                             return throwError(error.error.error_description);
            } else if (error.error.error === 'invalid_grant' &&
                        error.error.error_description.indexOf('Invalid refresh token') !== -1) {// refresh token failed
                auth.logoutUser(100);
            } else if (error.error.error === 'invalid_grant') {
                auth.logoutUser(0);
            } else if (error.error.error === 'unauthorized') { // auth server failure
                auth.logoutUser(0);
            }
        }
        return EmptyObservable.create();
    }

    /**
     * To handle 401 error, get refresh token and retry all active request with the new token
     */
    handle401Error(req: HttpRequest<any>, next: HttpHandler, auth: any, error) {
        // check invalid login Id error which returns 401.
        if (error.error.error === 'unauthorized' && error.error.error_description.toLowerCase() === 'bad credentials') {
            return throwError('');
        } else if (this.isRefreshingToken) {
            this.isRefreshingToken = false;

            // Reset here so that the following requests wait until the token
            // comes back from the refreshToken call.
            this.tokenSubject.next(null);

            return auth.refreshToken().pipe(switchMap((token) => {
                if (token) {
                    localStorage.setItem(CommonConstants.TOKEN, token['access_token']);
                    localStorage.setItem(CommonConstants.REFRESH_TOKEN, token['refresh_token']);
                    // comment in msa
                    // localStorage.setItem(AppUtils.STORAGE_ACCOUNT_EXPIRES_IN, token["expires_in"]);
                    this.tokenSubject.next(token['access_token']);
                    return next.handle(this.addToken(req));
                }

                // If we don't get a new token, logout.
                auth.logoutUser(100);
                return EmptyObservable.create();

            }), catchError((e: any) => {
                // If there is an exception calling 'refreshToken', logout.
                auth.logoutUser(100);
                return EmptyObservable.create();
            }), finalize(() => {
                this.isRefreshingToken = true;
            }));

        } else {
            return this.tokenSubject.pipe(
                filter(token => token != null),
                take(1),
                switchMap(token => {
                    return next.handle(this.addToken(req));
                })
            );
        }
    }

}
