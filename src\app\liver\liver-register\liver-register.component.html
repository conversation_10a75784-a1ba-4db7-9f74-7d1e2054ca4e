<div class="row">
    <div class="input-group col-sm-10">
      <h4 class="page-title pt-2">Liver Register</h4>
    </div>
    <div class="input-group col-sm-2 mb-2 text-right">
      <div class="input-group">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control form-control-sm search-input" (keyup.enter)="search()"/>
        <div class="input-group-append">
          <button  class="btn btn-default btn-sm search-icon" id="search"  (click)="search()">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
      <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
          <div  class="d-flex align-items-center justify-content-between card-head"  [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> 
              <i class="fas fa-male" *ngIf="alive" (click)="updateDeathDetails()" class="alive-icon fas fa-user" title="patient is alive "></i>
              <i class="fas fa-male" *ngIf="!alive" class="dead-icon fas fa-user-slash" title="patient is dead "></i>
              Patient Details
            </h6>
  
            <div>
              <span class="de-activate-btn" *ngIf="alive" (click)="updateDeathDetails()">De-Activate</span>
              <button ngbPanelToggle class="btn btn-link p-0">
                <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
              </button>
            </div>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <app-patient-details
            [submitted]="submitted"
            #patientDetails
            (callMethod)="callMpiMethod()"
          ></app-patient-details>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>


  <form [formGroup]="liverRegistryForm">  
<div class="accordion register" *ngIf="!alive">
      <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
        <ngb-panel id="patientDetails" id="ngb-panel-0">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
              [ngClass]="opened ? 'opened' : 'collapsed'">
              <h6>
                <i class="fas fa-male" *ngIf="alive" (click)="updateDeathDetails()" class="alive-icon fas fa-user"
                  title="patient is alive "></i>
                <i class="fas fa-male" *ngIf="!alive" class="dead-icon fas fa-user-slash" title="patient is dead "></i>
                Death Details
              </h6>
              <div>
                <span class="de-activate-btn" *ngIf="alive" (click)="updateDeathDetails()">De-Activate</span>
                <button ngbPanelToggle class="btn btn-link p-0">
                  <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                </button>
              </div>

            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <app-death-details [currentCivilId]="currentCivilId" [estCode]="estCode" [patientId]="patientId" #deathDetails>
            </app-death-details>
          </ng-template>
        </ngb-panel>
      </ngb-accordion>
    </div>
    <div class="row">
       <div class="col-sm-6 pr-0">
        <div class="section-panel">
            <div class="card">
              <div class="card-header">BMI Details</div>
              <div class="card-body card-min-ht">
                  <div class="row">
                      <div class="col-sm-6">
                        <div class="form-group">
                          <label>Body weight <span class="mdtr">( kg )</span></label>
                          <input
                            type="number"
                            class="form-control form-control-sm"
                            pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b"
                            formControlName="bmiBodyWidth"
                            (blur)="getBmi()"
                          />
            
                          <div class="tooltiptext" *ngIf="bmiBodyWidth.errors?.pattern">
                            {{ "Width should be not more than 140" }}
                          </div>
                        </div>
                      </div>
            
                      <div class="col-sm-6">
                        <div class="form-group">
                          <label>Body height <span class="mdtr">( cm )</span></label>
                          <input
                            type="number"
                            class="form-control form-control-sm"
                            pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b"
                            formControlName="bmiBodyHeight"
                            (blur)="getBmi()"
                          />
            
                          <div class="tooltiptext" *ngIf="bmiBodyHeight.errors?.pattern">
                            {{ "Height should be not more than 190" }}
                          </div>
                        </div>
                      </div>
            
                      <div class="col-sm-6">
                        <div class="form-group">
                          <label>BMI</label>
                          <input
                            type="number"
                            class="form-control form-control-sm"
                            formControlName="bmi"
                            readonly
                          />
                        </div>
                      </div>
            
                      <div class="col-sm-6">
                        <div class="form-group">
                          <label>Blood Group </label>
                          <ng-select
                            #entryPoint
                            appendTo="body"
                            [items]="bloodGroupList"
                            [virtualScroll]="true"
                            placeholder="Select"
                            bindLabel="value"
                            bindValue="id"
                            formControlName="bloodGroup"
                          >
                            <ng-template ng-option-tmp let-item="item" let-index="index"
                              >{{ item.value }}
                            </ng-template>
                          </ng-select>
                        </div>
                      </div>
                    </div>
            
                    <div class="row">
                      <div class="col-sm-12">
                        <div class="form-group">
                          <label>Cause of Liver Disease</label>
                          <div class="form-inputs">
                            <ng-select
                              #entryPoint
                              appendTo="body"
                              [items]="icdLiverShortList"
                              [virtualScroll]="true"
                              placeholder="Select"
                              bindLabel="codeDisease"
                              bindValue="code"
                              formControlName="causeOfLiverDisease"
                            >
                              <ng-template ng-option-tmp let-item="item" let-index="index"
                                >{{ item.codeDisease }}
                              </ng-template>
                            </ng-select>
                          </div>
                         
                        </div>
                      </div>
                    </div>
            
                    
  
              </div>
            </div>
       </div>
       </div>
       <div class="col-sm-6 pl-0">
        <div class="section-panel">
            <div class="card">
              <div class="card-header">Comorbid Disease 
                <button class="btn btn-link float-right add-btn-link" type="button" (click)="addRec('comorbidDiseaseListGrid')"> + {{ "Add" }}</button>
              </div>
              <div class="card-body">
                    <div class="form-group">
                      <ag-grid-angular
                        style="width: 100%; height: 200px"
                        class="ag-theme-balham"
                        [gridOptions]="comorbidDiseaseListGrid"
                        [rowData]="icdData"
                        [columnDefs]="columnDefs"
                        
                        
                        rowSelection="single"
                        singleClickEdit="true"
                        
                        [frameworkComponents]="frameworkComponents"
                        row-height="22"
                        (gridReady)="onReady($event, 'comorbidDiseaseListGrid')"
                      >
                      </ag-grid-angular>
                    </div>

              </div>
            </div>
        </div> 
       </div>
    </div>

  </form>

  <div class="btn-container">
     <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callCase()"> Case Details </button>
    <!--<button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callTissue()">Tissue Screening</button> -->

    <!-- <button class="btn btn-sm btn-primary" (click)="callCase()" >Case Details </button> -->
    <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
    <button *ngIf="showButton" class="btn btn-sm btn-primary" (click)="save()">Save</button>
  </div>
  