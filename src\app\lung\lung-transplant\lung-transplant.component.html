<!-- <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="head-title">Liver Transplant</h6>
</div> -->

<div class="row">
    <div class="input-group col-sm-10">
      <h4 class="page-title pt-2">Lung Transplant</h4>
    </div>
    <div class="input-group col-sm-2 mb-2 text-right">
      <div class="input-group">
        <input
          type="text"
          placeholder="Search Registration No"
          [(ngModel)]="regId"
          class="form-control form-control-sm search-input"
          (keyup.enter)="search()"
        />
        <div class="input-group-append">
          <button
            class="btn btn-default btn-sm search-icon"
            id="search"
            (click)="search()"
          >
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>



<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head px-2"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Demographic Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details
                  [submitted]="submitted"
                  #patientDetails
                ></app-patient-details>
              </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>


<div class="col-sm-12 px-0">
    <form *ngIf="transplantRegistryForm" [formGroup]="transplantRegistryForm"> 
        <input type="hidden" class="form-control form-control-sm" formControlName="centralRegNo" /> 
        <div class="section-panel">
          <div class="card">
            <div class="card-header"><h6>Transplant Registration</h6></div>
            <div class="card-body card-min-ht">
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Premptive Transplant</label>
                    <div class="col-sm-8">
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" name="preemptiveTransplantYn" formControlName="preemptiveTransplantYn" value="Y">Yes
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" name="preemptiveTransplantYn" formControlName="preemptiveTransplantYn" value="N">No
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Transplant Date <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
                        formControlName="transplantDate" [ngModelOptions]="{standalone: true}"
                        monthNavigator="true" [minDate]="today" yearRange="1930:2030" yearNavigator="true"
                        showButtonBar="true"></p-calendar>
                        
                        <div>
                          <span *ngIf="submitted && f.transplantDate.errors" class="tooltiptext">{{'Transplant Date is required'}}</span> 
                     </div>

                        

                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Transplant Remarks<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control form-control-sm" maxlength="1000" formControlName="transplantRemarks">
                      <div>
                        <span *ngIf="submitted && f.transplantRemarks.errors" class="tooltiptext">{{'Transplant Remarks is required.'}}</span> 
                        
                   </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Transplant Country <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="nationListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="natName"
                        bindValue="natCode" formControlName="transplantCountry">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.natName}}</ng-template>
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.transplantCountry.errors" class="tooltiptext">{{'Transplant Country is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Transplant Type <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="liverTransplanTypeMastList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="id" formControlName="transplantType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}</ng-template>
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.transplantType.errors" class="tooltiptext">{{'Transplant Type is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Transplant Subtype <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="liverTransplanSubTypeMastList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="id" formControlName="transplantSubType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}</ng-template>
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.transplantSubType.errors" class="tooltiptext">{{'Transplant Sub Type is required.'}}</span> 
                   </div>
                    </div>
                   
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Surgery Type <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="liverSurgicalInfoMastList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="id" formControlName="surgeryType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}</ng-template>
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.surgeryType.errors" class="tooltiptext">{{'Surgery Type is required.'}}</span> 
                   </div>
                    </div>
                   
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Surgery Subtype <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="liverSurgicalInfoSubTypeMastList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="id" formControlName="surgerySubType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}</ng-template>
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.surgerySubType.errors" class="tooltiptext">{{'Surgery Sub Type is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Explanted Liver Path<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control form-control-sm" formControlName="explantedLiverPath">
                      <div>
                        <span *ngIf="submitted && f.explantedLiverPath.errors" class="tooltiptext">{{'Explanted Liver Path is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">No of days in Ward<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="noDaysWard">
                      <div>
                        <span *ngIf="submitted && f.noDaysWard.errors" class="tooltiptext">{{'No of Days in Ward is required.'}}</span> 
                   </div>
                    </div>
                   
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">No of days in ICU<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm" formControlName="noDaysIcu">
                      <div>
                        <span *ngIf="submitted && f.noDaysIcu.errors" class="tooltiptext">{{'No of Days in ICU is required.'}}</span> 
                   </div>
                    </div>
                   
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Follow Up Hospital<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <ng-select #entryPoint [items]="hospitalsList" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
                        bindValue="estCode" formControlName="followUpHosp">
                        
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}</ng-template>
                        
                      </ng-select>
                      <div>
                        <span *ngIf="submitted && f.followUpHosp.errors" class="tooltiptext">{{'Follow up Hospital is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div *ngIf="transplantRegistryForm.get('followUpHosp').value === -1" class="form-group row">
                    <label class="col-sm-4 col-form-label">Others(Specify)<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control form-control-sm" formControlName="followUpHospOthers">
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Re-Admitted?</label>
                        <div class="col-sm-8">
                          <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" formControlName="reAdmissionYn" value="Y">Yes
                          </div>
                          <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" formControlName="reAdmissionYn" value="N">No
                          </div>
                        </div>
                      </div>
                      <div *ngIf="transplantRegistryForm.get('reAdmissionYn').value === 'Y'" class="form-group row">
                        <label class="col-sm-4 col-form-label">Re-Admission Reason<span class="mdtr">*</span></label>
                        <div class="col-sm-8">
                          <input type="text" formControlName="reAdmissionReason" class="form-control form-control-sm">
                          <div>
                            <span *ngIf="submitted && f.reAdmissionReason.errors" class="tooltiptext">{{'Re-Admission Reason is required.'}}</span> 
                       </div>
                        </div>
                       
                      </div>
                      <div *ngIf="transplantRegistryForm.get('reAdmissionYn').value === 'Y'" class="form-group row">
                        <label class="col-sm-4 col-form-label">Re-Admitted Days<span class="mdtr">*</span></label>
                        <div class="col-sm-8">
                          <input (keypress)="numberOnly($event)" type="text"  formControlName="reAdmissionDays" class="form-control form-control-sm">
                          <div>
                            <span *ngIf="submitted && f.reAdmissionDays.errors" class="tooltiptext">{{'Re-Admitted Days is required.'}}</span> 
                       </div>
                        </div>
                        
                      </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Surgical Intervention</label>
                    <div class="col-sm-8">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="surgicalInterYn" value="Y">Yes
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="surgicalInterYn" value="N">No
                      </div>
                    </div>
                  </div>
                  <div *ngIf="transplantRegistryForm.get('surgicalInterYn').value === 'Y'" class="form-group row">
                    <label class="col-sm-4 col-form-label">Intervention Type <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input type="text" formControlName="surgicalInterType" class="form-control form-control-sm">
                      <div>
                        <span *ngIf="submitted && f.surgicalInterType.errors" class="tooltiptext">{{'Intervention Type is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  <div *ngIf="transplantRegistryForm.get('surgicalInterYn').value === 'Y'" class="form-group row">
                    <label class="col-sm-4 col-form-label">Intervention Reason <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <input type="text" formControlName="surgicalInterReason" class="form-control form-control-sm">
                      <div>
                        <span *ngIf="submitted && f.surgicalInterReason.errors" class="tooltiptext">{{'Intervention Reason is required.'}}</span> 
                   </div>
                    </div>
                    
                  </div>
                  
                  
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Post Transplant Rejection</label>
                    <div class="col-sm-8">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="postTransRejectionYn" value="Y">Yes
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="postTransRejectionYn" value="N">No
                      </div>
                    </div>
                  </div>
                  <div *ngIf="transplantRegistryForm.get('postTransRejectionYn').value === 'Y'" class="form-group row">
                    <label class="col-sm-4 col-form-label">Date of Rejection <span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
                        formControlName="rejectionDate"
                        monthNavigator="true" [minDate]="today" yearRange="1930:2030" yearNavigator="true"
                        showButtonBar="true"></p-calendar>
                        <div>
                          <span *ngIf="submitted && f.rejectionDate.errors" class="tooltiptext">{{'Rejection Date is required.'}}</span> 
                     </div>
                    </div>
                    
                  </div>
                  <div *ngIf="transplantRegistryForm.get('postTransRejectionYn').value === 'Y'" class="form-group row">
                    <label class="col-sm-4 col-form-label">Rejection Grade<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="rejectionGradeYn" value="Y">Yes
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="rejectionGradeYn" value="N">No
                      </div>
                      <div>
                        <span *ngIf="submitted && f.rejectionGradeYn.errors" class="tooltiptext">{{'Rejection Grade is required.'}}</span> 
                   </div>
                    </div>
                   
                  </div>
                  <div *ngIf="transplantRegistryForm.get('postTransRejectionYn').value === 'Y'" class="form-group row">
                    <label class="col-sm-4 col-form-label">Rejection Prescription<span class="mdtr">*</span></label>
                    <div class="col-sm-8">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" formControlName="rxRejectionYn" value="Y">Yes
                          </div>
                          <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" formControlName="rxRejectionYn" value="N">No
                          </div>
                          <div>
                            <span *ngIf="submitted && f.rxRejectionYn.errors" class="tooltiptext">{{'Rejection Prescription is required.'}}</span> 
                       </div>
                    </div>
                    
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Biopsy</label>
                    <div class="col-sm-8">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="biopsyYn" value="Y">Yes
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="biopsyYn" value="N">No
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
</div>



<!-- <div class="col-sm-12 px-0">
    <div class="section-panel">
        <div class="card">
            <div class="card-header"><h6>Immunosuppressive & prophylactic Medicines</h6></div>
            <div class="card-body card-min-ht">
                <div class="row">
                    <div class="tabs">
                        <tabset>

                            <tab heading="Induction(up to 7 days)" id="Induction">

                                <div class="content-wrapper content-border">
                                 
                                        <div class="row">
                                            <div class="col-sm-3 pr-0">
                                                <div class="mcard">

                                                    <div class="mcard-body">
                                                        <table class="table table-lg">
                                                            <thead>
                                                                <tr>
                                                                    <th width="10%"></th>
                                                                    <th>Medicine</th>
                                                                    <th>Dose</th>
                                                                    <th>Level</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <ngb-pagination class="d-flex justify-content-center"
                                                            [(page)]="page3" [pageSize]="pageSize">
                                                        </ngb-pagination>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    
                                </div>
                        </tab>
                        <tab heading="Immediate(7 days-1 month)" id="Immediate">

                            <div class="content-wrapper content-border">
                               
                                    <div class="row">
                                        <div class="col-sm-3 pr-0">
                                            <div class="mcard">

                                                <div class="mcard-body">
                                                    <table class="table table-lg">
                                                        <thead>
                                                            <tr>
                                                                <th width="10%"></th>
                                                                <th>Medicine</th>
                                                                <th>Dose</th>
                                                                <th>Level</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <ngb-pagination class="d-flex justify-content-center"
                                                        [(page)]="page3" [pageSize]="pageSize">
                                                    </ngb-pagination>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                             
                            </div>
                    </tab>
                    <tab heading="Early(1-3 months)" id="Early">

                        <div class="content-wrapper content-border">
                          
                                <div class="row">
                                    <div class="col-sm-3 pr-0">
                                        <div class="mcard">

                                            <div class="mcard-body">
                                                <table class="table table-lg">
                                                    <thead>
                                                        <tr>
                                                            <th width="10%"></th>
                                                            <th>Medicine</th>
                                                            <th>Dose</th>
                                                            <th>Level</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <ngb-pagination class="d-flex justify-content-center"
                                                    [(page)]="page3" [pageSize]="pageSize">
                                                </ngb-pagination>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                           
                        </div>
                </tab>
                <tab heading="Late(>3 months)" id="Late">

                    <div class="content-wrapper content-border">
                       
                            <div class="row">
                                <div class="col-sm-3 pr-0">
                                    <div class="mcard">

                                        <div class="mcard-body">
                                            <table class="table table-lg">
                                                <thead>
                                                    <tr>
                                                        <th width="10%"></th>
                                                        <th>Medicine</th>
                                                        <th>Dose</th>
                                                        <th>Level</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td></td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <ngb-pagination class="d-flex justify-content-center"
                                                [(page)]="page3" [pageSize]="pageSize">
                                            </ngb-pagination>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                    </div>
            </tab>
            
                    </tabset>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->
<!-- <div class="case-details-panel">
  <div class="row">
    <div class="col-md-12">
      <div class="content-wrapper mb-2 h-100">
        <div [formGroup]="medicineForm" class="d-flex flex-column h-100">
          <div class="card-header">
            <h6>Immunosuppressive & Prophylactic Medicines</h6>
          </div>
          <ngb-tabset>
            <ngb-tab *ngFor="let item of IandPmedicineList; let i = index" [title]="item.value">
              <ng-template ngbTabContent>
                <div class="form-group">
                  <div *ngFor="let subItem of item.SubList">
                    <div class="form-check">
                      <input type="checkbox" class="form-check-input" [formControlName]="'selected_' + subItem.id" />
                      <label class="form-check-label" style="margin-left: 0.5rem">{{ subItem.value }}</label>
                    </div>
                    <div *ngIf="medicineForm.get('selected_' + subItem.id).value" style="margin-left: 20px;">
                      <div *ngFor="let subSubItem of subItem.SubSubList">
                        <div class="form-check">
                          <input type="checkbox" class="form-check-input" [formControlName]="'selectedSubSub_' + subSubItem.id" />
                          <label class="form-check-label" style="margin-left: 0.5rem">{{ subSubItem.value }}</label>
                        </div>
                        <div *ngIf="medicineForm.get('selectedSubSub_' + subSubItem.id).value" style="margin-left: 50px;">
                          <div class="row">
                            <div *ngIf="subSubItem.remarksYn === 'Y'" class="col-sm-2">
                              <div class="form-group row">
                                <div class="col-sm-6">
                                <label for="doseInput_{{subSubItem.id}}">Dose</label>
                                <input id="doseInput_{{subSubItem.id}}" (keypress)="numberOnly($event)" type="text"  class="form-control" [formControlName]="'dose_' + subSubItem.id">
                             </div>
                             <div class="col-sm-6">
                                <label for="levelInput_{{subSubItem.id}}">Level</label>
                                <input id="levelInput_{{subSubItem.id}}" type="text" class="form-control" [formControlName]="'level_' + subSubItem.id">
                              </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
            </ngb-tab>
          </ngb-tabset>
        </div>
      </div>
    </div>
  </div>
</div> -->

<div class="col-sm-12">
    <div class="btn-container">
      <button *ngIf="showButton" type="submit" class="btn btn-primary" (click)="saveDetails()">
        Save
      </button>
      <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
      <!-- <button class="btn btn-primary ripple" (click)="navigateToRegister()">
        Back to register page
      </button>
      <button  *ngIf="isPrint" class="btn btn-sm btn-secondary" (click)="print()">Print</button> -->
    </div>
  </div>