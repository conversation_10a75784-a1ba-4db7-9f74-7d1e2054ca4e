import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { EMPTY } from 'rxjs';
import * as AppCompUtils from '../../common/app.component-utils';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppUtils from '../../common/app.utils';
import { GridOptions } from 'ag-grid-community';
import { RenalWaitingListResult } from '../../_models/renal-waiting-list-result.model';
import { Router } from '@angular/router';
import { SharedService } from '../../_services/shared.service';
import Swal from 'sweetalert2';
@Component({
  selector: 'app-renal-waiting-list-listing',
  templateUrl: './renal-waiting-list-listing.component.html',
  styleUrls: ['./renal-waiting-list-listing.component.scss']
})
export class RenalWaitingListListingComponent implements OnInit {

  renalWaitingListSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  displayedResults: any[] = [];
  allResults: any[] = [];
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  //regCodeSelected: any;
  //walCodeSelected: any;
  //estCodeSelected: any;
  columnDefs: any[];
  totalRecords: any;
  rowData: Array<RenalWaitingListResult> = new Array<RenalWaitingListResult>();
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination:true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  onReady(params) {
    this.gridOptions.api.sizeColumnsToFit();
  }
  constructor(private _router: Router, private _http: HttpClient, private _masterService: MasterService, private _sharedService: SharedService) {

    this.renalWaitingListSearchForm = new FormGroup({
      'civilId': new FormControl(),
      'centralRegNo': new FormControl(),
      'ageFrom': new FormControl(),
      'ageTo': new FormControl(),
      'sex': new FormControl(),
      'bloodGroup':  new FormControl(),
      'regCode': new FormControl(),
      'walCode': new FormControl(),
      'estCode': new FormControl(),
    });

    this.getRegionList();
    this.getWilayatList();
    this.getInstiteList();


    this.columnDefs = [

      { headerName: 'Civil ID', field: 'civilId', minWidth: 125 },
      { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125 },
      { headerName: 'Name', field: 'fullName', minWidth: 150 },
      { headerName: 'Age', field: 'age', minWidth: 150 },
      { headerName: 'Final Score', field: 'renalWaitingListCount', minWidth: 60 },
      { headerName: 'Blood Group', field: 'bloodGroup', minWidth: 150 , sortable: true, cellRenderer: this.renderBloodGroup },

    ];



  }
  ngOnInit() {
  }
  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    //  this.router.navigate([AppUtils.ARI_LINK], { state: { noteId: data.notificationId } });
    this._router.navigate(['renal-waiting/registry'], { state: { centralRegNo: event.data.centralRegNo } });
  }
  getList() {

    if (this.gridOptions.api) { this.gridOptions.api.showLoadingOverlay(); }

    let body = this.renalWaitingListSearchForm.value;

    if (body['regCode'] && body['regCode'].trim() != "") {
      body['regCode'] = body['regCode'].trim();
    } else {
      body['regCode'] = null;
    }

    if (body['walCode'] && body['walCode'].trim() != "") {
      body['walCode'] = body['walCode'].trim();
    } else {
      body['walCode'] = null;
    }

    if (body['estCode'] && body['estCode'].trim() != "") {
      body['estCode'] = body['estCode'].trim();
    } else {
      body['estCode'] = null;
    }
    if ( body['bloodGroup'] && body['bloodGroup'] != "") {
      body['bloodGroup'] = body['bloodGroup'];
    }else{
      body['bloodGroup'] = null;
    }

    this._http.post(AppUtils.FIND_RENAL_WAITING_LIST, body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result'];
      } else {
        this.rowData = null;
        Swal.fire('Warning!', res['message'], 'error')
        this.gridOptions.api.hideOverlay();

      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })

  }

  bloodGroupSelect(event: any, group?: any) {
    const selectedBloodGroups = this.renalWaitingListSearchForm.get('bloodGroup') as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;
  
    // Get the current value of selected blood groups
    let currentSelectedValues = selectedBloodGroups.value ? [...selectedBloodGroups.value] : [];
  
    // Get the ID of the selected blood group
    const bloodGroup = this.bloodGroupList.find(group => group.value === selectedValue);
    const selectedId = bloodGroup ? bloodGroup.id : null;
  
    // Update the currentSelectedValues array based on the checked state
    if (isChecked && selectedId && !currentSelectedValues.includes(selectedId)) {
        currentSelectedValues.push(selectedId);
    } else if (!isChecked && selectedId) {
        currentSelectedValues = currentSelectedValues.filter(id => id !== selectedId);
    }
  
    console.log('Updated Selected Values:', currentSelectedValues); // Debugging log
  
    // Update the selected blood groups with the modified array
    selectedBloodGroups.setValue(currentSelectedValues);
  
    // Filter the results based on the updated selected blood groups and update the displayed results
    this.updateDisplayedResults(currentSelectedValues);
  }

  // Function to filter results based on selected blood groups
updateDisplayedResults(selectedValues: string[]) {
  // Implement the logic to filter the results based on the selected blood group IDs
  if (selectedValues.length === 0) {
      // If no blood group is selected, display all results
      this.displayedResults = this.allResults;
  } else {
      // Filter the results to display only those matching the selected blood group IDs
      this.displayedResults = this.allResults.filter(result => selectedValues.includes(result.bloodGroupID.toString()));
  }
}

renderBloodGroup(params: any): string {
  const bloodGroupID = params.value; // Assuming the blood group ID is provided as a number
  let bloodGroupLabel = '';


  switch (bloodGroupID) {
    case 1:
      bloodGroupLabel = 'A +';
      break;
    case 2:
      bloodGroupLabel = 'A -';
      break;
      case 3:
      bloodGroupLabel = 'B +';
      break;
    case 4:
      bloodGroupLabel = 'B -';
      break;
      case 5:
      bloodGroupLabel = 'O +';
      break;
    case 6:
      bloodGroupLabel = 'O -';
      break;
      case 7:
      bloodGroupLabel = 'AB +';
      break;
    case 8:
      bloodGroupLabel = 'AB -';
      break;
      default:
  bloodGroupLabel = 'Unknown Blood Group';
  break;
  }

  return bloodGroupLabel;
}

  clear(e) {
    
    this.renalWaitingListSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;
    this.gridOptions.api.hideOverlay();
  }


  /* ------------  get DropDownList ---------------- */
  getRegionList() {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {

    });
  }
  getWilayatList(regCode: any = 0) {
    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });
  }
  getInstiteList(regCode: any = 0, walCode: any = 0) {
    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {

    });
  }

  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }
  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (this.isEmptyNullZero(event.target.value) === false) {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
     // this.walCodeSelected = null;
     // this.estCodeSelected = null;
    }
    else {
      this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.target.value);
      this.institeListFilter = this.institeList.filter(s => s.regCode == event.target.value);
    }

  }

  walSelect(event: any, field?: any) {
    if (this.isEmptyNullZero(event.target.value) === false) {
      if (this.renalWaitingListSearchForm.value['regCode'] && (this.renalWaitingListSearchForm.value['regCode'] != null || this.renalWaitingListSearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.renalWaitingListSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.renalWaitingListSearchForm.value['regCode']);
       // this.estCodeSelected = null;
      } else {
        this.institeListFilter = this.institeList;
      }
    }
    else {
      this.institeListFilter = this.institeList.filter(s => s.walCode == event.target.value);
      //this.estCodeSelected = null;
    }

  }
  /* ------------  filter action   ---------------- */



}
