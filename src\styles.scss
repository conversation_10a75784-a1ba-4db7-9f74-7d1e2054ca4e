@import "~@ng-select/ng-select/themes/default.theme.css";
@import "~ag-grid-community/src/styles/ag-grid.scss";
@import "~ag-grid-community/src/styles/ag-theme-balham/sass/ag-theme-balham.scss";
// @import '~bootstrap-icons/font/bootstrap-icons.css';
// custom fonts----------------

@font-face {
    font-family: 'open_sansregular';
    src: url('assets/fonts/opensans.woff2') format('woff2'),
        url('assets/fonts/opensans.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'open_sanssemibold';
    src: url('assets/fonts/opensans-semibold.woff2') format('woff2'),
        url('assets/fonts/opensans-semibold.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}


// variable --------------------------


:root {
    --primary-color: rgba(217, 83, 79, 1);
    --primary-light: rgba(217, 83, 79, 0.2);
    --secondary-color: rgb(119, 119, 119);
    --complementary-color: #444;
    --blue-color: #2f313e;

    --bg-color: #f1f1f1;
    --white-color: #fff;
    --bg-light: #bdbdbd;
    --table-divider: #e6e6e6;

    --font-default: 'open_sansregular', sans-serif;
    --font-bold: 'open_sanssemibold';
    --font-size-base: 0.875rem;
    --font-size-normal: 0.8125rem;
    --font-color-default: #333;
    --font-color-light: #666;
    --header-height: 50px;

}


// mixins --------------------------

@mixin box-shadow($top, $left, $blur, $color, $inset: false) {
    @if $inset {
        -webkit-box-shadow: inset $top $left $blur $color;
        -moz-box-shadow: inset $top $left $blur $color;
        box-shadow: inset $top $left $blur $color;
    }

    @else {
        -webkit-box-shadow: $top $left $blur $color;
        -moz-box-shadow: $top $left $blur $color;
        box-shadow: $top $left $blur $color;
    }
}

@mixin box-shadow($top, $left, $blur, $color, $inset: false) {
    @if $inset {
        -webkit-box-shadow: inset $top $left $blur $color;
        -moz-box-shadow: inset $top $left $blur $color;
        box-shadow: inset $top $left $blur $color;
    }

    @else {
        -webkit-box-shadow: $top $left $blur $color;
        -moz-box-shadow: $top $left $blur $color;
        box-shadow: $top $left $blur $color;
    }

}




.mdtr {
    color: red;
}


body {
    font: 400 var(--font-size-base) var(--font-default);
    background: var(--bg-color);
    overflow-x: hidden;
    // padding: 1.25rem;
}

body,
html {
    height: 100%;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

b,
strong {
    font-family: var(--font-bold);
}

.form-group {
    margin-bottom: 0.625rem;
}

.brand-name {
    background: var(--primary-color);
    font-family: var(--font-bold);
    padding: 0.313rem 3.125rem;
    font-size: 1.25rem;
    display: inline-block;
    color: var(--white-color);
    border-top-left-radius: 5px;
    border-bottom-right-radius: 40px;
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transition: all .3s linear;

    &:active,
    &:focus,
    &:hover {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
    }
}

.dashboard {
    background-image: url(assets/img/dashboard.png);
}

.ng-select-container {
    min-height: 31px !important;
    color: #888 !important;
}

.ng-select .ng-select-container:after {
    content: '';
    width: 100%;
    bottom: 1px;
    position: absolute;
    -webkit-transition: border-color 0.3s cubic-bezier(0.55, 0, 0.55, 0.2);
    transition: border-color 0.3s cubic-bezier(0.55, 0, 0.55, 0.2);
}

.page-title {
    font-size: 14px;
    color: #a7a7a7;
    font-family: var(--font-default) !important;
}

.content-wrapper {
    background: var(--white-color);
    border: 1px solid #eaeaea;
    padding: 15px;
    border-radius: 5px;
}

.accordion {

    .card {
        margin: 0 0 10px;
        border: 0;
        @include box-shadow(0, 0, 5px, rgba(0, 0, 0, 0.06));
        border-radius: 0.25rem !important;
        overflow: visible !important;

        .card-header {
            background: var(--white-color);
            padding: 0 !important;
            // border-color: rgb(245, 220, 219);
            border-color: rgba(0, 0, 0, 0.125);
            border-radius: 5px;

            h6 {
                // color: var(--primary-color);
                // color: rgb(151 54 51);
                color: #444;
                font-family: var(--font-bold);
                margin: 0;
            }

            button {
                width: 30px;
                line-height: 16px;

                i {
                    color: var(--primary-color);
                    font-size: 14px;
                }
            }

            .card-head {
                padding: 8px 15px;

                &.opened {
                    // background: #fff8f7  !important;
                    // border-left:4px solid #d9534f;
                }
            }
        }

        .card-body {
            padding: 0.9375rem;
        }


        .active ul {
            display: block !important;
        }

        p-calendar {
            &.form-control {
                padding: 0;

            }

            &.ui-calendar {
                display: block;

                .ui-inputtext {
                    border: 0;
                    width: 100%;
                    height: 28px;
                }

            }
        }

    }
}


p-calendar {
    .ui-calendar {
        display: block;
        height: 31px;

        .ui-inputtext {
            width: 100%;
            border-color: #e9e9e9;
            padding: 3px;
        }

    }
}



/////////////////////////////
.AllBoxHeight {
    height: 350px !important;
}

.mar-0 {
    margin: 0 !important;
}

.multiselect-dropdown .dropdown-btn {
    padding: 2px 8px !important;
    border-color: #c7c7c7 !important;

}



.inner-wrapper,
.grid-wrapper {
    background: #fff;
    padding: 20px;
    border: 1px solid #eef0f2;
    border-radius: 5px;
}

.grid-wrapper {
    margin-top: 10px;
}

.search-area .ui-multiselect {
    display: block;
}

.search-area .ui-multiselect .ui-multiselect-label {
    padding: 0.4em 2em .39em .25em !important;
}

.custom-dropdown .dropdown-list {
    // width: 230px !important;
}

.custom-dropdown .dropdown-up {
    border-width: 5px !important;
    border-bottom-color: #555 !important;
}

.custom-dropdown .dropdown-down {
    border-width: 5px !important;
    border-top-color: #555 !important;
}


.ui-widget:disabled {
    opacity: 1 !important;
    background-image: none !important;
    cursor: default !important;
}

body .ui-widget-header .ui-button,
body .ui-widget-content .ui-button,
body .ui-widget.ui-button,
.ui-button {
    border-color: #eaeaea !important;
    background: #fff !important;
    padding: 3px 0px 4px;

    .ui-button-icon-left {
        color: #bbb !important;
    }
}

.ui-datepicker.ui-widget .ui-datepicker-calendar td a.ui-state-active {
    background-color: #d9534f !important;
}

input[type='text'][disabled] {
    background-color: #EBEBE4;
}

.modal-body {
    max-height: 490px;

    overflow: auto;
}

.modal-header {
    background: #666666;
    color: #fff;
    padding: 10px 15px !important;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    min-height: 16.43px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-container {
    max-height: 400px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 2px 7px #d8d8d8;
    -moz-box-shadow: 0 2px 7px #d8d8d8;
    -webkit-box-shadow: 0 2px 7px #d8d8d8;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border: 1px solid #d8d8d8;
}

.modal-container .list-group-item {
    padding: 5px;
}

.modal-container .list-group-item.active,
.modal-container .list-group-item.active:focus,
.modal-container .list-group-item.active:hover {
    background-color: #e6e6e6;
    border-color: #ddd;
    color: #333;
}

.modal-container .header-controls {
    width: 30%;
    float: right;
    text-align: right;
}

.modal-container .header-controls span {
    margin-left: 5px;
}

.custom-modal .modal-header {
    background: #d9534f;
    color: #fff;
}

.modal-mainbody,
.modal-header,
.modal-footer {
    padding: 5px;
}

.modal-mainbody {
    margin-top: 0;
    min-height: 90px;
    max-height: 200px;
    overflow: auto;
}

.modal-footer {
    /* height: 40px; */
    background-color: whiteSmoke;
    border-top: 1px solid #DDD;
    border-radius: 5px;
}

.custom-modal .modal-dialog {
    margin: 60px auto;
}

.modal-dialog {
    max-width: 900px;
}

.modal-sm {
    max-width: 300px !important;
}

.modal-lg-height {
    max-height: 560px !important;
}

.modal-wide {
    width: 95%;
}

.modal-container {
    max-height: 400px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 2px 7px #d8d8d8;
    -moz-box-shadow: 0 2px 7px #d8d8d8;
    -webkit-box-shadow: 0 2px 7px #d8d8d8;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border: 1px solid #d8d8d8;
}

.modal-header {
    background: #666666;
    color: #fff;
    padding: 10px 15px !important;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    min-height: 16.43px;
    border-bottom: 1px solid #e5e5e5;
}

.role-modal {
    max-height: 400px !important;
}

.modal-footer {
    padding: 5px !important;
}

.modal-xlg {
    width: 90% !important;
}

.modal-xlg .modal-body {
    max-height: 600px !important;
}

.ui-input-group .form-control {
    color: #888;
    font-size: 13px;
    line-height: 17px;
    border: 1px solid #c7c7c7;
    background: #fff;
    box-shadow: none;
    border-radius: 3px;
    height: 26px;
    padding: 0 6px;
}

.ui-input-group .form-control:focus {
    outline: none;
    box-shadow: none;
}



.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
    background: #f5f8ff;
    border-color: #406ff4;
    color: #1f346f;
}

///////////// BTN

.btn-container {
    padding-top: 10px;
    text-align: right;
}

.btn {
    transition: all 0.3s ease 0s;
    margin: 0 2px;
    font-size: 14px;
}

.btn-primary {
    background: #d9534f !important;
    border-color: #d9534f !important;
    border-radius: 3px;
    font-size: 13px !important;
    padding: 5px 10px !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-outline-primary:hover,
.btn-primary:focus {
    background: #d9534f !important;
    border-color: #d9534f !important;
    color: #fff;
}

.btn-outline-primary {
    border-color: #d9534f !important;
    font-size: 13px !important;
    padding: 5px 10px !important;
    color: #d9534f;
}

.btn-sm {
    font-size: 12px;
    line-height: 1.5;
    padding: 5px 10px;
    border-radius: 3px;
}

.btn-gray {
    background: #666;
    border-color: #666;
    color: #fff;
}

.btn-gray:hover {
    color: #fff;
}

.custom-table .btn-sm {
    padding: 3px 8px !important;
}

p-multiselect,
.ui-multiselect,
.ui-dropdown,
p-dropdown {
    display: block;
}



// .ag-theme-balham {
//     @include ag-theme-balham();
// }


.accordion {
    &.register {
        .card {

            &:nth-child(5),
            &:nth-child(6),
            &:nth-child(7),
            &:nth-child(8),
            &:nth-child(9),
            &:nth-child(10) {
                display: inline-block;
                width: 49%;
                margin: 0 0.5%;
                vertical-align: top;
                margin-bottom: 10px;
            }
        }
    }
}

.tooltiptext {
    color: red;
    display: block;
}


td.foo {
    background-color: #F6F1F1;

}

th.foo {
    color: red !important;
    background-color: #DADEE3 !important;
}

//////////////
.ui-calendar {
    display: flex !important;

    .ui-inputtext {
        width: 100%;
        border-color: #e9e9e9 !important;
    }
}

.custom-dropdown .multiselect-dropdown .dropdown-multiselect__caret:before {
    border-color: rgb(236, 6, 6) transparent !important;
}

.custom_color .multiselect-dropdown .selected-item {
    background: #fb3b4d !important;
    border-color: white !important;
}


.custom_box_color .multiselect-item-checkbox input+div:before {
    border: 2px solid #d81f32 !important;
}

.check_box_custom-color .multiselect-item-checkbox input:checked+div:before {

    background: #d81f32 !important;
}

.ngb-panel-scroll {
    .accordion {
        .card {
            overflow: inherit !important;
        }
    }
}


/// menu icons 
.kidney {
    background-image: url(assets/ico/kidney.png);
}

.blood-disorder {
    background-image: url(assets/ico/blood-disorder.png);
}

.brain {
    background-image: url(assets/ico/brain.png);
}

.diabetic {
    background-image: url(assets/ico/diabetic.png);
}

.elderly {
    background-image: url(assets/ico/elderly.png);
}

.vaccine {
    background-image: url(assets/ico/vaccine.png);
}

.donor {
    background-image: url(assets/ico/donor.png);
}

.anc {
    background-image: url(assets/ico/anc.png);
}

.liver {
    background-image: url(assets/ico/liver.png);
}

.transplant {
    background-image: url(assets/ico/transplant.svg);
}

.PrepareWaitingList {
    background-image: url(assets/ico/PrepareWaitingList.png);
}

.WaitingListListing {
    background-image: url(assets/ico/WaitingListListing.png);
}

.LiverRegisterList {
    background-image: url(assets/ico/LiverRegisterList.png);
}

.asthma {
    background-image: url(assets/ico/asthma.png);
}

.child-health {
    background-image: url(assets/ico/child-health.png);
}

.child-nutrition {
    background-image: url(assets/ico/child-nutrition.png);
}

.cornea {
    background-image: url(assets/ico/cornea.png);
}

.cornea1 {
    background-image: url(assets/ico/cornea1.svg);
}

.corneaTransplant {
    background-image: url(assets/ico/corneaTransplant.png);
}

.DeceasedDonorRegister {
    background-image: url(assets/ico/DeceasedDonorRegister.svg);
}

.mental {
    background-image: url(assets/ico/mental.png);
}

.lung {
    background-image: url(assets/ico/lung.png);
}

.lungRegisterListing {
    background-image: url(assets/ico/lungRegisterListing.png);
}

.lungTransplant {
    background-image: url(assets/ico/lungTransplant.svg);
}

.lungTransplantListing {
    background-image: url(assets/ico/lungTransplantListing.svg);
}

.request {
    background-image: url(assets/ico/request.svg);
}

.request-listing {
    background-image: url(assets/ico/request-listing.svg);
}

.transplant-listing {
    background-image: url(assets/ico/transplant-listing.svg);
}

.deceasedListing {
    background-image: url(assets/ico/deceasedListing.png);
}

.renalRegisterListing {
    background-image: url(assets/ico/renalRegisterListing.svg);
}

.menu-icon {
    width: 25px;
    height: 25px;
    top: 6px !important;
    left: 5px !important;
    background-size: 100% !important;
    opacity: 0.6;
    filter: brightness(0) invert(1);
}

.menu-icon2 {
    width: 28px;
    height: 28px;
    top: 4px !important;
    left: 6px !important;
    background-size: 100% !important;
    opacity: 0.6;

}

.menu-icon3 {
    Width: 36px;
    height: 34px;
    top: 0px !important;

    background-size: 100% !important;
    opacity: 0.8;

}

.menu-icon1 {

    width: 25px;
    height: 25px;
    top: 6px !important;
    left: 5px !important;
    background-size: 100% !important;
    color: #ffb5b3 !important;
}

.menu-icon5 {
    width: 29px;
    height: 29px;
    top: -4px !important;
    left: 4px !important;
    background-size: 100% !important;
    opacity: 0.6;
    -webkit-filter: brightness(0) invert(1);
    filter: brightness(0) invert(1);
}

.hideOverlay {
    .ag-overlay {
        display: none !important;
    }
}

.ng-select.ng-select-single {
    .ng-select-container {
        height: 31px;
    }
}

.search-icon {
    background: #ffffff;
    border: 1px solid #d7d7d7;
    color: #888;
}

strong {
    font-family: var(--font-bold);
}

.de-activate-btn {
    background-color: #e9f9ed;
    padding: 1px 10px;
    border-radius: 10px;
    cursor: pointer;
    color: #155724;
    margin-right: 10px;

    &:hover {
        transition: .3s linear;
        background: #6bab7b;
        color: #fff;

    }
}

%message-shared {
    font-size: 14px;
    padding-right: 5px;
}

.alive-icon {
    color: #69b169;
    @extend %message-shared;

}

.dead-icon {
    color: black;
    @extend %message-shared;
}

.search-input {
    margin-right: -2px;
}

.add-btn-link {
    color: #d9534f;
    text-decoration: none !important;
    padding: 0;
    line-height: 1;

    &:hover {
        color: #cf3c37;
    }
}

.section-panel {
    margin: 0 8px;

    .card {
        margin-bottom: 10px;

        .card-header {
            color: #973633;
            font-family: "open_sanssemibold";
            background: #fff;
            padding: 0.55rem 1.25rem;
        }
    }
}

.btn-pdf {
    font-size: 14px;
    color: #d9534f;
    border: 0;
    background: 0;
    padding: 0;

    &:hover {
        color: #d9534f;
    }
}

.addNewCale {
    margin-bottom: 5px;
    display: block;

    .ui-inputtext {
        display: none;
    }

    button.ui-datepicker-trigger.ui-calendar-button {
        border-radius: 4px !important;
    }

    .pi-calendar:before {
        content: '+ Add New' !important;
        white-space: nowrap;
        color: #d9534f;
        font: 14px var(--font-default);
    }

    .ui-button-icon-left {
        left: 35% !important;
        top: 12px;
    }

}

body .addNewCale .ui-datepicker-trigger.ui-button {

    width: 100% !important;
    background: #fff !important;
    border: 1px dashed #d9534f;

    &:hover {
        background: #d9534f !important;

        .pi-calendar:before {
            color: #fff;
        }
    }

}

.mt-lbl {
    margin-top: 26px;
}

.pl-0 {
    padding-left: 0;
}

.grid-panel {
    margin-top: 20px;
}




.tabs {
    padding: 0 6px;

    .nav-tabs {
        li {
            a {
                color: #666;
                font-family: var(--font-bold);
                margin-bottom: -2px;
                font-size: 1rem;

                &.active {
                    // background: #d9534f;
                    // color: #fff;
                    color: #973633;

                }
            }
        }
    }
}

.ag-theme-balham {
    .ag-row-selected {
        background-color: #efefef;

    }
}

.del-btn {
    color: #d9534f;
    cursor: pointer;
}

.ag-popup-editor {
    box-shadow: none !important;
    border: 0 !important;

    .ng-select-container {
        min-height: 26px !important;
        height: 26px !important
    }

}

table thead th {
    font-family: var(--font-bold);
}

.deactivate {
    color: green;
    font-size: 14px;
    background: #e6f5e6;
    padding: 0 10px;
    border-radius: 10px;
    cursor: pointer;
}

.page-link {
    color: #222;
    padding: 0.3rem 0.75rem;

    &:hover {
        color: #222;
    }
}

.page-item.active {
    .page-link {
        background-color: #999;
        border-color: #999;
        color: #fff !important;
    }
}

.readonly-ng-select {
    pointer-events: none;


    .ng-select-container {
        background-color: #e9ecef;

        .ng-placeholder {
            display: none !important;
        }

        .ng-arrow-wrapper {
            .ng-arrow {
                display: none !important;
            }

        }


    }



}



.fa-area-chart:before {
    content: "\f1fe";
}

.form-control,
.ng-select .ng-select-container {
    border-color: #e9e9e9;
}

.form-group label {
    font-size: 13px;
}

.input-group .form-control {
    border-radius: 30px !important;
    height: 30px;
    border-color: #e3e3e3;
    font-size: 14px;
}

.error {
    color: #e77474
}

.nav-tabs {
    .nav-link {
        background: #f1f1f1;
        color: #444;
        font-family: var(--font-bold);
    }
}

.card {
    .card-header {
        font-family: var(--font-bold);
    }
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #d9534f;
    border-color: #d9534f;
    box-shadow: none !important;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show>.btn-outline-secondary.dropdown-toggle {
    box-shadow: none !important;
}