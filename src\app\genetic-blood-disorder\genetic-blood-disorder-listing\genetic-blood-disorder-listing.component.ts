import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { MasterService } from '../../_services/master.service';
import * as AppCompUtils from '../../common/app.component-utils';
import { GeneticListResult } from '../../_models/genetic-list-result.model';
import { GridOptions } from 'ag-grid-community';
import * as AppUtils from '../../common/app.utils';
import { SharedService } from '../../_services/shared.service';
import { Router } from '@angular/router';
import { GeneticService } from '../genetic.service';
import { medictionModel } from '../../common/objectModels/mediction-model';
import { BloodDisorderExcel } from 'src/app/_models/genetic-blood-disorder-excel.model';
import Swal from 'sweetalert2';
import * as FileSaver from 'file-saver';
declare var swal: any;
@Component({
  selector: 'app-genetic-blood-disorder-listing',
  templateUrl: './genetic-blood-disorder-listing.component.html',
  styleUrls: ['./genetic-blood-disorder-listing.component.scss']
})
export class GeneticBloodDisorderListingComponent implements OnInit {
  regionData: RegionDataModel[];
  // medicine: medictionModel[] = [];
  complicationMastList: any;
  surgeryMaster: any[];
  medicine: any[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  disorderType: any[];
  vaccinMastList: any;
  genotype: any[];
  genotypeBase: any[];
  testDone: any[];
  componentTest: any[];
  bloodData: Array<BloodDisorderExcel> = new Array<BloodDisorderExcel>();
  gender = AppCompUtils.GENDER;
  geneticBloodDisorderSearch: FormGroup;
  columnDefs: any[];
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  rowData: Array<GeneticListResult> = new Array<GeneticListResult>();

  constructor(fb: FormBuilder, private _sharedService: SharedService, private _router: Router, private _masterService: MasterService, private _geneticService: GeneticService) {
    this.getMasterData();
    this.geneticBloodDisorderSearch = fb.group({
      'civilId': [null],
      'centralRegNo': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'sex': [""],
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'regionData': [null],
      'disorderType': [null],
      'genotype': [null],
      'genotypeBase': [null],
      'testDone': [null],
      'componentTest': [null],
      'numericValue': [null],
      'qualitativeValue': [null],
      'medicine': [null],
      'complicationMastList': [null],
      'surgeryMaster': [null],
      /////  'vaccinMastList':[null],
      'regType': [null],

    });

    this.columnDefs = [
      { headerName: 'Reg No', field: 'centralRegNo', minWidth: 70, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 90, sortable: true },
      { headerName: 'Name', field: 'fullname', minWidth: 190, sortable: true, sort: 'asc' },
      { headerName: 'Age', field: 'age', minWidth: 50, sortable: true },
      { headerName: 'DoB', field: 'dob', minWidth: 90, sortable: true },
      { headerName: 'Gender', field: 'sex', minWidth: 80, sortable: true },
    ];
  }

  ngOnInit(): void {

  }
  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {

    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {

    });

    this._masterService.getDisorderType().subscribe(res => {
      this.disorderType = res.result;
    });

    this._masterService.getGenotypeDesc().subscribe(res => {
      this.genotype = res.result;
    })
    this._masterService.getGenotypeBase().subscribe(res => {
      this.genotypeBase = res.result;
    })

    this._masterService.getLabMaster().subscribe(res => {
      this.testDone = res.result;
    })
    this._masterService.getMedicineMaster().subscribe(res => {
      this.medicine = res.result;

    })

    this._masterService.getSurgeryMaster().subscribe(res => {
      this.surgeryMaster = res.result;

    })

    this._masterService.getGenComplicationMast().subscribe(response => {
      this.complicationMastList = response.result;

    });
    this._masterService.getVaccinationMastList().subscribe(response => {
      this.vaccinMastList = response.result;
    }, error => {
    });

  }

  getLabComponents() {

    let body = this.geneticBloodDisorderSearch.value;

    if (body['disorderType'] != null && body['testDone'] != null) {


      this._masterService.getComponentsTest(body['disorderType'], body['testDone']).subscribe(res => {
        this.componentTest = res.result;
      })
    }


  }
  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }
  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }

  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.geneticBloodDisorderSearch.value['regCode'] && (this.geneticBloodDisorderSearch.value['regCode'] != null || this.geneticBloodDisorderSearch.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.geneticBloodDisorderSearch.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.geneticBloodDisorderSearch.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.geneticBloodDisorderSearch.value['regCode'] && (this.geneticBloodDisorderSearch.value['regCode'] != null || this.geneticBloodDisorderSearch.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.geneticBloodDisorderSearch.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.geneticBloodDisorderSearch.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }
  /* ------------  filter action   ---------------- */
  clear(e) {

    this.geneticBloodDisorderSearch.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;

  }

  getList() {
    let body = this.geneticBloodDisorderSearch.value;
    if (body['sex'] == "") {
      body['sex'] = null;
    }
    this._geneticService.getGeneticListing(body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result'];
        this.rowData = this.rowData.sort();
        for (var i = 0; i < this.rowData.length; i++) {

          this.rowData[i].sex = this.gender.filter(s => s.id == this.rowData[i].sex).map(s => s.value).toString();
          this.rowData[i].labtest = this.testDone.filter(s => s.mohTestCode == this.rowData[i].labtest).map(s => s.testName).toString();
          this.rowData[i].geneticDisorderType = this.disorderType.filter(s => s.geneticTypeId == this.rowData[i].geneticDisorderType).map(s => s.geneticTypeDesc).toString();
          this.rowData[i].genotypeBase = this.genotypeBase.filter(s => s.id == this.rowData[i].genotypeBase).map(s => s.description).toString();
          this.rowData[i].genotype = this.genotype.filter(s => s.id == this.rowData[i].genotype).map(s => s.description).toString();
          this.rowData[i].medicineTest = this.medicine.filter(s => s.medicineMasterID == this.rowData[i].medicineTest).map(s => s.medicineMasterValue).toString();
          this.rowData[i].complicationTest = this.complicationMastList.filter(s => s.id == this.rowData[i].complicationTest).map(s => s.description).toString();

          this.rowData[i].surgeryTest = this.surgeryMaster.filter(s => s.procID == this.rowData[i].surgeryTest).map(s => s.procName).toString();


        }

        this.rowData.forEach(el => {
          this.bloodData.push({
            RegNo: el.centralRegNo, civilId: el.civilId,
            Name: el.fullname, Age: el.age, DoB: el.dob, Gender: el.sex
          })

        }

        )

      } else {
        this.rowData = null;
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })

  }


  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['genetic/registry'], { state: { centralRegNo: event.data.centralRegNo } });
  }
  onSubmit() {

  }




  exportExcel() {
    if (this.rowData && this.rowData.length > 0) {
      this.formatData(this.rowData);
    } else {
      Swal.fire('Warning!', 'No Records to explor', 'warning')
    }
  }

  formatData(rowData) {
    this.bloodData = [];
    rowData.forEach(el => {

      this.bloodData.push({
        RegNo: el.centralRegNo,
        civilId: el.civilId, Name: el.fullname, Age: el.age,
        DoB: el.dob, Gender: el.sex
      })
    })
    this._sharedService.exportAsExcelFile(this.bloodData, "Genitic_Blood_disorder_Listing");
  }




}


