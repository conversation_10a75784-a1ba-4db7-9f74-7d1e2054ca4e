import { Component, OnInit, ViewChild } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import { DecimalPipe, formatDate } from "@angular/common";
import { <PERSON>umn<PERSON>pi, Grid<PERSON>pi, GridOptions } from "ag-grid-community";
import Swal from "sweetalert2";
import * as moment from "moment";
import * as _ from "lodash";

import { HeartService } from "../heart.service";
import { SharedService } from "src/app/_services/shared.service";
import { MasterService } from "src/app/_services/master.service";
import { HeartRegistryForm } from "src/app/common/objectModels/heart-registry-model";
import { PatientDetailsComponent } from "src/app/_comments/patient-details/patient-details.component";
import { DeathDetailsComponent } from "src/app/_comments/death-details/death-details.component";
import { Diagnosis } from "src/app/common/objectModels/diagnosis-model";
import { TbVitalSigns } from "src/app/common/objectModels/vital-signs-model";
import { HeartIcd } from "src/app/common/objectModels/icd-heart-list-models";
import { ICDRenalShortList } from "src/app/common/objectModels/icdRenalShortList-models";
import { ButtonRendererComponent } from "src/app/common/agGridComponents/ButtonRendererComponent";
import { GridNgSelectDataComponent } from "src/app/common/agGridComponents/grid-ngSelect-data.component";

import * as AppParams from "../../_helpers/app-param.constants";
import * as AppUtils from "../../common/app.utils";
import * as AppCompUtils from "../../common/app.component-utils";
import * as GridUtils from "../../common/agGridComponents/app.grid-spec-utils";
import * as CommonConstants from "../../_helpers/common.constants";

@Component({
  selector: "app-heart-registry",
  templateUrl: "./heart-registy.component.html",
  styleUrls: ["./heart-registy.component.scss"],
  providers: [HeartService],
})
export class HeartRegistyComponent implements OnInit {
  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  @ViewChild("deathDetails", { static: false })
  deathDetails: DeathDetailsComponent;

  regNo: string;
  today: any;
  heartForm: FormGroup;
  patientForm: FormGroup;
  formData: HeartRegistryForm;
  rgTbVitalSign: TbVitalSigns[];
  diagnosis: Diagnosis[];
  icdData: any[] = [];
  centralRegNoExit = false;
  submitted = false;
  dataFetched = false;
  alive = true;
  heartRegistryData: any;
  estCode: string;
  patientId: string;
  currentCivilId: string;
  dbIcdDataGrid: Diagnosis[] = [];
  icdHeartShortList: ICDRenalShortList[] = [];
  comorbidDiseaseListGrid: GridOptions;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  columnDefs: any[] = [];
  frameworkComponents: any;
  grids: string[] = [];
  icdList: HeartIcd[] = [];
  loginId: string;

  private readonly _decimalPipe = new DecimalPipe("en-US");
  private readonly currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");
  private comorbidDiseaseGridApi: GridApi;
  private comorbidDiseaseGridColApi: ColumnApi;

  constructor(
    private _heartService: HeartService,
    private fb: FormBuilder,
    private _sharedService: SharedService,
    private _router: Router,
    private _masterService: MasterService
  ) {}

  ngOnInit(): void {
    this.initHeartForm();
    this.populateMasterData();
    this.initializeComponent();
  }

  private initHeartForm(): void {
    this.heartForm = this.fb.group({
      bmiBodyWidth: new FormControl(),
      bloodGroup: new FormControl(),
      bmi: new FormControl(),
      bmiBodyHeight: new FormControl(),
      registerType: new FormControl(18),
      rgTbEldExamTrans: new FormControl(this.fb.array([])),
      instRegDate: new FormControl(this.today),
      rgTbPatientInfo: new FormControl(this.fb.array([])),
      causeOfKindeyDisease: new FormControl(),
      othercomorbidDisease: new FormControl(),
    });

    this.patientForm = this.fb.group({
      centralRegNo: new FormControl(null),
      patientId: new FormControl(null, Validators.required),
      civilId: new FormControl(null, Validators.required),
      dob: new FormControl(null, Validators.required),
      age: new FormControl(),
      tribe: new FormControl(),
      firstName: new FormControl(null, Validators.required),
      secondName: new FormControl(null, Validators.required),
      sex: new FormControl(),
      maritalStatus: new FormControl(),
      thirdName: new FormControl(),
      village: new FormControl(),
      walayat: new FormControl(),
      region: new FormControl(),
      mobileNo: new FormControl(),
      kinTelNo: new FormControl(),
      careGiverTel: new FormControl(),
      regInst: new FormControl(null, Validators.required),
      exDate: new FormControl(null),
    });
  }

  private initializeComponent(): void {
    const curUser = JSON.parse(
      localStorage.getItem(CommonConstants.CUR_USER) || "{}"
    );
    this.loginId = curUser && curUser.person ? curUser.person.perscode : null;

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regNo = this._sharedService.getNavigationData().regNo;

        if (this.regNo) {
          setTimeout(() => {
            this.getList(this.regNo);
            this.regNo = "";
          }, 1000);
        } else {
          this.regNo = "";
          Swal.fire({
            icon: "warning",
            title: "Please enter Registration ID",
          });
        }

        //this.getList(this.regNo);
        this._sharedService.setNavigationData(null);
      }, 0);
    }
  }

  search(): void {
    if (!this.regNo) {
      Swal.fire({ icon: "warning", title: "Please enter Registration ID" });
      return;
    }
    this.clear();
    this.getList(this.regNo);
    this.regNo = "";
  }

  clear(): void {
    this.heartForm.reset();
    this.formData = null;
    this.centralRegNoExit = false;
    this.submitted = false;
    this.dataFetched = false;
    this.alive = true;
    this.icdData = [];
    this.diagnosis = null;
    this.rgTbVitalSign = null;
    this.dbIcdDataGrid = [];
    this.heartRegistryData = null;
    this.estCode = null;
    this.patientId = null;
    this.currentCivilId = null;

    if (this.comorbidDiseaseGridApi) {
      this.comorbidDiseaseGridApi.setRowData([]);
    }

    this.centralRegNoExit = false;
    this.alive = false;
  }

  clearData(): void {
    this.regNo = "";
    this.clear();
    if (this.patientDetails) {
      this.patientDetails.clear();
    }

    this.centralRegNoExit = false;
    if (this.deathDetails) {
      this.deathDetails.clear();
    }

    this.alive = true;
  }

  getList(centralRegNo?: string, civilId?: string): void {
    this._heartService.getHeartRegistry(centralRegNo, civilId).subscribe({
      next: (res) => {
        if (res["code"] === "S0000") {
          this.centralRegNoExit = true;
          this.heartRegistryData = res["result"];
          this.formData = this.heartRegistryData;
          this.patientDetails.setPatientDetails(this.heartRegistryData);
          this.getRegisterForm(this.heartRegistryData);
          this.estCode = this.heartRegistryData.regInst;
          this.patientId = this.heartRegistryData.rgTbPatientInfo.patientId;
        } else {
          const message =
            res["code"] === "F0000"
              ? "No Record Found with Entered Registration No."
              : res["message"];
          this.clearData();
          Swal.fire("Warning", message, "warning");
        }
      },
      error: (error) => {
        if (error.status === 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      },
    });
  }
  getRegisterForm(heartRegistryData: any) {
    this.currentCivilId = heartRegistryData.rgTbPatientInfo["civilId"];
    this.rgTbVitalSign = heartRegistryData.rgTbVitalSigns;

    this.alive = heartRegistryData.rgTbDeathDetails == null;

    const width =
      this.rgTbVitalSign &&
      this.rgTbVitalSign.find((s) => s.paramid == AppParams.VITAL_WT)
        ? this.rgTbVitalSign.find((s) => s.paramid == AppParams.VITAL_WT)
            .paramValue
        : undefined;
    const height =
      this.rgTbVitalSign &&
      this.rgTbVitalSign.find((s) => s.paramid == AppParams.VITAL_HT)
        ? this.rgTbVitalSign.find((s) => s.paramid == AppParams.VITAL_HT)
            .paramValue
        : undefined;
    const bloodGroup =
      this.rgTbVitalSign &&
      this.rgTbVitalSign.find((s) => s.paramid == AppParams.VITAL_BLOOD_GROUP)
        ? this.rgTbVitalSign.find(
            (s) => s.paramid == AppParams.VITAL_BLOOD_GROUP
          ).paramValue
        : undefined;

    if (bloodGroup !== undefined) {
      this.heartForm.patchValue({ bloodGroup: bloodGroup.toString() });
    }

    const rgTbDeathDetails = heartRegistryData.rgTbDeathDetails;
    if (rgTbDeathDetails) {
      this.alive = false;
      setTimeout(() => {
        if (this.deathDetails) {
          this.deathDetails.setDeathDetailsResult(
            _.cloneDeep(rgTbDeathDetails)
          );
        }
      }, 0);
    }

    this.bmiDetails(width, height);

    this.diagnosis = heartRegistryData.rgTbDiagnosis;
    const kidneyDisease =
      heartRegistryData.rgTbDiagnosis &&
      heartRegistryData.rgTbDiagnosis.find((s) => s.icdFlag == "P")
        ? heartRegistryData.rgTbDiagnosis.find((s) => s.icdFlag == "P").icd
        : undefined;

    if (kidneyDisease) {
      this.heartForm.patchValue({
        causeOfKindeyDisease: kidneyDisease.toString(),
      });
    }

    this.dbIcdDataGrid =
      heartRegistryData.rgTbDiagnosis &&
      heartRegistryData.rgTbDiagnosis.filter((s) => s.icdFlag == "O")
        ? heartRegistryData.rgTbDiagnosis
            .filter((s) => s.icdFlag == "O")
            .map((x) => ({ ...x }))
        : [];
    this.icdData =
      heartRegistryData.rgTbDiagnosis &&
      heartRegistryData.rgTbDiagnosis.filter((s) => s.icdFlag == "O")
        ? heartRegistryData.rgTbDiagnosis.filter((s) => s.icdFlag == "O")
        : [];

    this.dbIcdDataGrid.forEach((s) => {
      const theCode = s.icd;
      if (this.icdHeartShortList) {
        const theReq = this.icdHeartShortList.find((r) => r.code == theCode);
        if (theReq) {
          s.icdValue = theReq.disease;
          this.icdData.forEach((el) => {
            if (el.icd == s.icd) {
              el.icdValue = s.icdValue;
            }
          });
        }
      }
    });
  }
  bmiDetails(width: any, height: any) {
    this.heartForm.patchValue({ bmiBodyWidth: width, bmiBodyHeight: height });
    const bmi = this._sharedService.calculateBMI(width, height);
    this.heartForm.patchValue({
      bmi: this._decimalPipe.transform(bmi, "1.2-2"),
    });
  }

  updateDeathDetails() {
    Swal.fire({
      text: "Do you want to Update death details?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes",
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    });
  }

  save() {
    this.submitted = true;
    if (this.comorbidDiseaseGridApi) {
      this.comorbidDiseaseGridApi.stopEditing();
    }

    const patientForm =
      this.patientDetails && this.patientDetails.patientForm
        ? this.patientDetails.patientForm
        : null;
    if (!patientForm) {
      Swal.fire("Error!", "Patient form not initialized", "error");
      return;
    }

    const patientId = patientForm.get("patientId")
      ? patientForm.get("patientId").value
      : null;

    if (!patientId) {
      Swal.fire("Alert!", "Patient ID should not be null or empty.", "warning");
      return;
    }
    if (patientForm.invalid) {
      const kinTelNoErrors = patientForm.get("kinTelNo")
        ? patientForm.get("kinTelNo").errors
        : null;
      if (kinTelNoErrors && kinTelNoErrors["pattern"]) {
        Swal.fire(
          "Alert!",
          "Kin Telephone Number must be between 8 and 16 digits.",
          "warning"
        );
      } else {
        Swal.fire(
          "Alert!",
          "Please correct the highlighted errors in the Patient section.",
          "warning"
        );
      }
      return;
    }
    if (!this.patientDetails.validateFields()) {
      Swal.fire("Alert!", " Mandatory fields cannot be empty", "warning");
      return;
    }
    if (this.heartForm.invalid) {
      Swal.fire("Alert!", " Please enter valid data in BMI detail", "warning");
      return;
    }

    const patientData = this.heartForm.value;
    const vitalData = this.buildVitalData(patientData);
    const diagnosisData = this.buildDiagnosisData(patientData);

    const {
      createdBy,
      createdOn,
      modifiedBy,
      modifiedOn,
      centralRegNo,
      regDate,
      localRegReferance,
    } = this.getAuditFields();

    const patientFormData = this.patientDetails.patientForm.value;
    const deathDetails =
      this.deathDetails && this.deathDetails.deatDetailsForm
        ? this.deathDetails.deatDetailsForm.value
        : null;
    const processedDeathDetails = this.processDeathDetails(deathDetails);

    const saveData = {
      centralRegNo: centralRegNo,
      activeYn: "Y",
      civilId: patientFormData.civilId,
      patientID: this.patientDetails.patientForm.get("patientId")
        ? this.patientDetails.patientForm.get("patientId").value
        : null,
      createdBy: createdBy,
      createdOn: createdOn,
      instRegDate: regDate,
      modifiedBy: modifiedBy,
      modifiedOn: modifiedOn,
      regInst: this.patientDetails.patientForm.get("regInst")
        ? this.patientDetails.patientForm.get("regInst").value
        : null,
      registerType: 18,
      localRegReferance: localRegReferance,
      rgTbPatientInfo: {
        ...patientFormData,
        dob: patientFormData.dob
          ? moment(patientFormData.dob).format("DD-MM-YYYY")
          : null,
      },
      rgTbVitalSigns: vitalData,
      rgTbDiagnosis: diagnosisData,
      rgTbDeathDetails: processedDeathDetails,
    };

    this._heartService.saveHeartRegistry(saveData).subscribe({
      next: (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Heart Registry Saved Successfully.", "success");
          this.regNo = res["result"];
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Error!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      error: (err) => {
        Swal.fire(
          "Error!",
          "Error occurred while saving Heart Registry: " + err.message,
          "error"
        );
      },
    });
  }

  private buildVitalData(patientData: any): TbVitalSigns[] {
    const vitalParams = [
      {
        value: patientData.bmiBodyWidth,
        paramid: AppParams.VITAL_WT,
        desc: null,
      },
      {
        value: patientData.bmiBodyHeight,
        paramid: AppParams.VITAL_HT,
        desc: null,
      },
      {
        value: patientData.bloodGroup,
        paramid: AppParams.VITAL_BLOOD_GROUP,
        desc: this.bloodGroupList.find((s) => s.id == patientData.bloodGroup)
          ? this.bloodGroupList.find((s) => s.id == patientData.bloodGroup)
              .value
          : null,
      },
    ];

    let vitalData: TbVitalSigns[] = this.rgTbVitalSign
      ? [...this.rgTbVitalSign]
      : [];

    vitalParams.forEach((param) => {
      const vital: TbVitalSigns = {
        runId: null,
        entryBy: Number(this.loginId),
        entryDate: this.currentDate,
        paramValue: param.value,
        paramDesc: param.desc,
        paramid: param.paramid,
      };

      const existingIndex = vitalData.findIndex(
        (v) => v.paramid === param.paramid
      );
      if (existingIndex === -1) {
        vitalData.push(vital);
      } else {
        vital.runId = vitalData[existingIndex].runId;
        vitalData[existingIndex] = vital;
      }
    });

    return vitalData;
  }

  private buildDiagnosisData(patientData: any): any[] {
    let diagnosisData =
      this.diagnosis && this.diagnosis.filter((s) => s.icdFlag === "P")
        ? this.diagnosis.filter((s) => s.icdFlag === "P")
        : [];

    const kidneyDiseaseData = {
      runId:
        diagnosisData[0] && diagnosisData[0].runId
          ? diagnosisData[0].runId
          : null,
      icdFlag: "P",
      icd: patientData.causeOfKindeyDisease,
      enteredBy: Number(this.loginId),
      entryDate: this.currentDate,
      remarks: null,
      centralRegNo:
        this.formData && this.formData.centralRegNo
          ? this.formData.centralRegNo
          : null,
      icdValue: null,
    };

    diagnosisData = [kidneyDiseaseData];

    const renderedNodes =
      this.comorbidDiseaseGridApi &&
      this.comorbidDiseaseGridApi.getRenderedNodes()
        ? this.comorbidDiseaseGridApi.getRenderedNodes()
        : [];
    renderedNodes.forEach((node) => {
      if (!node.data.icd) {
        const foundIcdItem = this.icdHeartShortList.find(
          (el) => el.disease === node.data.icdValue
        );
        if (foundIcdItem) {
          node.data.icd = foundIcdItem.code;
        }
        Object.assign(node.data, {
          enteredBy: Number(this.loginId),
          entryDate: this.currentDate,
          icdFlag: "O",
        });
      }
      diagnosisData.push(node.data);
    });

    return diagnosisData;
  }

  private getAuditFields() {
    const isNewRecord =
      this.patientDetails &&
      this.patientDetails.patientForm &&
      this.patientDetails.patientForm.get("centralRegNo")
        ? this.patientDetails.patientForm.get("centralRegNo").value === null
        : true;

    return {
      createdBy: isNewRecord
        ? this.loginId
        : this.formData
        ? this.formData.createdBy
        : null,
      createdOn: isNewRecord
        ? this.currentDate
        : this.formData
        ? this.formData.createdOn
        : null,
      modifiedBy: isNewRecord ? null : this.loginId,
      modifiedOn: isNewRecord ? null : this.currentDate,
      centralRegNo: isNewRecord
        ? null
        : this.patientDetails &&
          this.patientDetails.patientForm &&
          this.patientDetails.patientForm.get("centralRegNo")
        ? this.patientDetails.patientForm.get("centralRegNo").value
        : null,
      regDate: isNewRecord
        ? this.currentDate
        : this.formData
        ? this.formData.instRegDate
        : null,
      localRegReferance: isNewRecord
        ? undefined
        : this.formData
        ? this.formData.localRegReferance
        : null,
    };
  }

  private processDeathDetails(deathDetails: any) {
    if (!deathDetails) return null;
    const hasNonNullValue = Object.values(deathDetails).some(
      (value) => value !== null && value !== undefined && value !== ""
    );
    return hasNonNullValue ? deathDetails : null;
  }

  private getGrids() {
    this.columnDefs = [
      { field: "centralRegNo", hide: true },
      { field: "entryDate", hide: true },
      { field: "enteredBy", hide: true },
      { field: "icd", hide: true },
      {
        headerName: "ICD",
        minWidth: 350,
        field: "icdValue",
        cellEditor: "ngSelectEditor",
        cellEditorParams: {
          values: this.icdList,
          dataColumn: "code",
          model: new HeartIcd(),
          objectData: ["code", "disease"],
        },
        editable: true,
        width: 130,
      },
      { field: "icdFlag", hide: true },
      { field: "runId", hide: true },
      { field: "remarks", editable: true },
      {
        headerName: "Action",
        cellRenderer: "buttonRenderer",
        cellRendererParams: {
          onClick: this.confirmDeleteICD.bind(this),
          label: "Delete",
        },
      },
    ];
  }

  callMpiMethod() {
    const civilId =
      this.patientDetails &&
      this.patientDetails.patientForm &&
      this.patientDetails.patientForm.value
        ? this.patientDetails.patientForm.value.civilId
        : null;
    if (!civilId) {
      Swal.fire("Warning", "Please enter Civil ID", "warning");
      return;
    }

    this._heartService.getHeartRegistry(undefined, civilId).subscribe({
      next: (res) => {
        if (res["code"] === AppUtils.RESPONSE_SUCCESS_CODE) {
          this.clear();
          this.dataFetched = true;
          this.heartRegistryData = res["result"];
          this.centralRegNoExit = true;
          this.patientDetails.setPatientDetails(this.heartRegistryData);
          this.getRegisterForm(this.heartRegistryData);
        } else if (
          res["code"] === AppUtils.RESPONSE_NO_ECORD ||
          res["code"] === AppUtils.RESPONSE_ERROR_CODE
        ) {
          this.getData("civilId", "", civilId);
        } else {
          Swal.fire(
            " ",
            "The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.",
            "warning"
          );
        }
      },
      error: (error) => {
        if (error.status === 401) {
          Swal.fire("", "Error occurred while retrieving details", "error");
        }
      },
    });
  }

  getData(searchBy: string, regNo: string, civilId: string) {
    let msg: string;
    let callMPI = true;

    if (searchBy === AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Registration No.";
    } else if (searchBy === AppUtils.CALLTYPE_BY_CIVILID) {
      const hasRequiredData =
        this.patientDetails &&
        this.patientDetails.patientForm &&
        this.patientDetails.patientForm.get("civilId") &&
        this.patientDetails.patientForm.get("civilId").value &&
        ((this.patientDetails.patientForm.get("exDate") &&
          this.patientDetails.patientForm.get("exDate").value) ||
          (this.patientDetails.patientForm.get("dob") &&
            this.patientDetails.patientForm.get("dob").value));
      if (!hasRequiredData) {
        callMPI = false;
        msg =
          "No Record Found in the registry. Please enter civil id and expiry date to fetch Demographic information from ROP.";
      } else {
        Swal.fire(
          "",
          "No Record Found in the registry. Fetching Demographic information from ROP.",
          "warning"
        );
      }
    }

    if (callMPI) {
      this._sharedService.setPatientData(this.patientDetails);
      this._sharedService.fetchMpi().subscribe();
    } else {
      Swal.fire("", msg, "warning");
    }
  }

  callCase() {
    this._sharedService.setNavigationData(this.formData);
    this._router.navigate(["heart/case-details"], {
      state: { centralRegNo: this.formData.centralRegNo },
    });
  }

  getBmi() {
    let w = this.heartForm.get("bmiBodyWidth").value;
    let h = this.heartForm.get("bmiBodyHeight").value;
    this.bmiDetails(w, h);
  }

  addRec(grid: any) {
    let colObject = {};
    if (grid === "comorbidDiseaseListGrid") {
      colObject = {
        centralRegNo: null,
        entryDate: null,
        enteredBy: null,
        icd: null,
        icdValue: null,
        icdFlag: null,
        runId: null,
        remarks: null,
      };
    }
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(
      (value) => value["gridName"] === grid
    );
    const renderedNodes = this[gridObject["gridApi"]].getRenderedNodes();
    let nullRowCnt = 0,
      nullMandatoryFieldCnt = 0;
    if (renderedNodes.length > 0) {
      renderedNodes.forEach((element) => {
        const data = element["data"];
        let isEmpty = true;
        Object.keys(data).forEach((el) => {
          if (data[el] === "") data[el] = null;
          if (
            data[el] &&
            (gridObject["hiddenColumns"]
              ? !gridObject["hiddenColumns"].find((field) => el === field)
              : data[el])
          ) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach((elChild) => {
                if (data[el][elChild]) isEmpty = false;
              });
            } else isEmpty = false;
          }
          if (gridObject["mandatoryColumns"]) {
            gridObject["mandatoryColumns"].forEach((mandatoryField) => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach((elChild) => {
                      if (!data[el][elChild]) nullMandatoryFieldCnt++;
                    });
                  }
                } else nullMandatoryFieldCnt++;
              }
            });
          }
        });
        if (isEmpty) nullRowCnt++;
      });
    }
    if (
      (nullRowCnt === 0 && nullMandatoryFieldCnt === 0) ||
      renderedNodes.length === 0
    ) {
      this[gridObject["gridApi"]].updateRowData({ add: [colObject] });
      this[gridObject["gridApi"]].redrawRows();
      this[gridObject["gridApi"]].forEachNode((node) => {
        node.setSelected(!!node.lastChild);
        if (node.lastChild) this[gridObject["gridApi"]].ensureNodeVisible(node);
      });
    }
  }
  isObject(val: any): boolean {
    return typeof val === "object" && val !== null;
  }

  onReady(params, grid) {
    if (this.grids.length > 0) {
      const exist = this.grids.find((item) => grid === item);
      if (!exist) {
        this.grids.push(grid);
      }
    } else {
      this.grids.push(grid);
    }

    if (grid === "comorbidDiseaseListGrid") {
      this.comorbidDiseaseGridApi = params.api;
      this.comorbidDiseaseGridColApi = params.columnApi;
    }
  }

  populateMasterData() {
    if (this._masterService.getHeartIcdList) {
      this._masterService.getHeartIcdList();
    }
    if (this._masterService.icdHeartList) {
      this._masterService.icdHeartList.subscribe((value) => {
        this.icdList = value || [];
        this.getGrids();
      });
    }

    if (this._masterService.getIcdHeartShortList) {
      this._masterService.getIcdHeartShortList();
    }
    if (this._masterService.icdHeartShortList) {
      this._masterService.icdHeartShortList.subscribe((value) => {
        this.icdHeartShortList = value || [];
      });
    }
  }

  confirmDeleteICD(e) {
    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: "btn btn-success",
        cancelButton: "btn btn-danger",
      },
      buttonsStyling: false,
    });
    swalWithBootstrapButtons
      .fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: true,
      })
      .then((result) => {
        if (!result.dismiss) this.deleteICD(e);
        else if (result.dismiss === Swal.DismissReason.cancel)
          swalWithBootstrapButtons.fire(
            "Cancelled",
            "Your imaginary record is safe :)",
            "error"
          );
      });
  }
  deleteICD(e) {
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(
      (value) => value["gridName"] === "comorbidDiseaseListGrid"
    );
    let focusedNode = this.comorbidDiseaseGridApi.getSelectedRows();
    this[gridObject["gridApi"]].updateRowData({ remove: focusedNode });
    this[gridObject["gridApi"]].redrawRows();
    let pushData = [];
    this[gridObject["gridApi"]]
      .getRenderedNodes()
      .forEach((el) => pushData.push(el["data"]));
    this.icdData = pushData;
  }
}
