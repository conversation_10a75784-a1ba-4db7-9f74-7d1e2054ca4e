import { Component, ElementRef, Input, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ChildNutritionService } from '../child-nutrition.service';
import * as _ from 'lodash';
import Swal from 'sweetalert2';
import * as CommonConstants from '../../_helpers/common.constants';
import { PatientDetailsComponent } from 'src/app/_comments/patient-details/patient-details.component';
import * as AppUtils from 'src/app/common/app.utils';
import * as moment from 'moment';
import { SharedService } from 'src/app/_services/shared.service';
import { NutRegister } from 'src/app/_models/nutRegister.model';
import { CFA_PERIOD, malnutritionDataR } from 'src/app/_helpers/common.constants';
import { AlShifaLoginService } from '../../alshifa/alShifaLogin.service';
import { malnutritionDataF } from 'src/app/_helpers/common.constants';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { DatePipe } from '@angular/common';
import { Chart } from 'chart.js';
import { MasterService } from 'src/app/_services/master.service';
import * as AppCompUtils from '../../common/app.component-utils';

@Component({
  selector: 'app-child-nutriiton-register',
  templateUrl: './child-nutriiton-register.component.html',
  styleUrls: ['./child-nutriiton-register.component.scss'],
  providers: [ChildNutritionService]
})
export class ChildNutriitonRegisterComponent implements OnInit {

  childNutritionRegister: FormGroup;
  flwupOtherDisease: number;
  selectedId: number;
  selColor: number = 1;
  selColorFlwup: number = 0;
  malnutriLevel: number;
  levelValue: number;
  v: number = 0;
  followupList: Date[] = [];
  malnutritionDataR = malnutritionDataR;
  malnutritionDataF = malnutritionDataF;
  nationListFilter: any;
  nationList: any;
  wallayatListFilter: any[];
  dispatientInfoalshifa: any;
  cAssPeriod = CFA_PERIOD;
  malStatus: any;
  assOutcome: any;
  institutes: any[];
  nutLabInvestR: any[];
  nutLabInvest: any[];
  downloadFromShifa: boolean = true;
  public page = 1;
  public page1 = 1;
  public page2 = 1;
  public page3 = 1;
  public page4 = 1;
  public pageSize = 2;
  nutAssesList: any = [];
  assLact: any;
  villageList: any[];
  searchForm: FormGroup;
  allNutData: any;
  childLabInfoalshifa: any;
  today = new Date();
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('viewVisitsHistWindow', { static: false }) public viewVisitsHistWindow: TemplateRef<any>;
  @ViewChild('viewCFAssessHistWindow', { static: false }) public viewCFAssessHistWindow: TemplateRef<any>;
  @ViewChild('viewGrowthChartWindow', { static: false }) public viewGrowthChartWindow: TemplateRef<any>;
  @ViewChild('printContainer', { static: false }) printContainer: ElementRef;
  @ViewChild('chart', { static: false }) chart!: Chart;
  checkList: any = [];
  dataFetched: boolean;
  checkListFilterd: any = [];
  rZscore: any = [];
  rCheckListFilterd: any;
  selectedColor: string;
  colors: string[] = ['red', 'green', 'blue']; // You can add more colors here
  nutVisitsList: any[] = [];
  selectedFUDate: any;
  followUpForm: FormGroup;
  followUpClickedAdd: boolean = true;
  selectedFollowUp: any;
  cFAssess: any[];
  fZscore: any;
  fCheckListFilterd: any;
  cFAssessF: any[];
  cFAssessR: any[];
  institutesFull: any[];
  age: any;
  submitted: boolean;
  errorMsg: string;
  nutNo: any;
  loginId: any;
  loginName: any;
  visitsHist: any[];
  columnVisitHistList: ({ headerName: string; field: string; minWidth: number; sortable: boolean; sort: string; cellRenderer?: undefined; } | { headerName: string; field: string; minWidth: number; sortable: boolean; sort: string; cellRenderer: any; })[];
  columncFAssessHistList: ({ headerName: string; field: string; minWidth: number; sortable: boolean; sort: string; cellRenderer?: undefined; } | { headerName: string; field: string; minWidth: number; sortable: boolean; sort: string; cellRenderer: any })[];
  answeredQes: any;
  nutKeyMsgList: any = [];
  keyMsg: any;
  medHistory: any;
  nutKeyMsgListF: any = [];
  nutAssesListF: any = [];
  nutLabInvestF: any[];
  periodRangeR: { name: string; value: number; }[];
  periodRangeNameR: string;
  periodRangeNameF: string;
  options: any;
  data: any;
  regId: any;
  civilId: any;
  fileStatusChart: { labels: any[]; datasets: { backgroundColor: any; borderColor: string; data: any[]; }[]; };
  charBGColor: any;
  chartOptions: { animationEnabled: boolean; title: { text: string; }; axisX: { title: string; }; axisY: { title: string; }; toolTip: { shared: boolean; }; legend: { cursor: string; itemclick: (e: any) => void; }; data: { type: string; showInLegend: boolean; name: string; dataPoints: { label: string; y: number; }[]; }[]; };
  growthChartName: any;
  selectedTabR: number = 1;
  backgroundColor: string;
  backgroundColorR: string;
  backgroundColorF: string;
  progrssDes: string;
  chartInitialized: boolean = false;
  institutesFilter: any[];
  regionData: any;
  wallayatList: any;
  hbLevelList = AppCompUtils.HB_LEVEL;
  haRatingList = AppCompUtils.HA_RATING;
  waRatingList = AppCompUtils.WA_RATING;
  whRatingList = AppCompUtils.WH_RATING;
  hcaRatingList = AppCompUtils.HCA_RATING;
  gender = AppCompUtils.GENDER;
  minVisitDate: Date | null = null;
  minRegDate: Date | null = null;
  disableRegDate: boolean = false;

  constructor(private fb: FormBuilder, private _alShifaLoginService: AlShifaLoginService, private spinner: NgxSpinnerService, private _ChildNutritionService: ChildNutritionService, private _sharedService:
    SharedService, private modalService: NgbModal, private datePipe: DatePipe, private _masterService: MasterService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode;
    this.loginName = curUser['person'].personName;
    // this.getMasterData();
    this.initialsBookingForm();
    this.initialsFollowUpForm();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        let nutritionNo = this._sharedService.getNavigationData().nutritionNo;
        this.search(nutritionNo);
        this._sharedService.setNavigationData(null);
      }, 1000);

    }

    this.getNationalityList();
    this.getVillageList();

  }
  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationList = response.result;
      this.nationListFilter = this.nationList;

    }, error => {

    });
  }

  getVillageList(walCode: any = 0) {
    this._masterService.getVillageList(walCode).subscribe(response => {
      this.villageList = response.result;
    }, error => {
    });
  }




  ngAfterViewInit() {
    setTimeout(() => {
      // let nutritionNo = this._sharedService.getNavigationData().nutritionNo;
      // this.search(nutritionNo);
      // this._sharedService.setNavigationData(null);

      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaPathentId: any;
        let alShifaEstCode: any;
        let alShifaRegNo: any;
        let alShifaValCode: any;


        this._alShifaLoginService.getAlShifanData().forEach(element => {
          if (element["regNo"]) {
            alShifaRegNo = element["regNo"]
          } else if (element["patientId"]) {
            alShifaPathentId = element["patientId"];
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          }
          else if (element["civilId"]) {
            this.civilId = element["civilId"];
          }
        });

        if (alShifaRegNo) {
          this.regId = alShifaRegNo;
          this.getChildNutritionRegistry(this.regId);
        } else if (alShifaEstCode && alShifaPathentId) {
          if (!alShifaValCode) {
            alShifaValCode = 0;
          }
          this.callFetchDataFromAlShifa(alShifaEstCode, alShifaPathentId, alShifaValCode);
        }

      }
      if (this._sharedService.getNavigationData()) {
        this.regId = this._sharedService.getNavigationData().centralRegNo;

        this.getChildNutritionRegistry(this.regId);
      }

    }, 1000);

  }

  cellRenderer = (data) => {
    if (data && data.value) {
      return '';
    }
    if (data && data.value != null) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    }
    if (data) {
      const formattedDate = moment(data).format('DD-MM-yyyy');
      return formattedDate;
    }
    else {
      return '';
    }
  };


  cellRendererBack = (data) => {
    if (data) {
      if (data > 0) {
        const formattedDate = data
        return formattedDate;
      } else {
        const [day, month, year] = data.split('-').map(Number);
        const dateObject = new Date(year, month - 1, day);
        const formattedDate = dateObject.getTime();
        return formattedDate;
      }
    } else {
      return '';
    }
  };



  cellRendererVisitType = (data) => {
    if (data.value) {
      let visitTypeName = '';
      if (data.value == 'R') {
        visitTypeName = 'Register'
      }
      if (data.value == 'F') {
        visitTypeName = 'Follow Up'
      }
      return visitTypeName;
    } else {
      return '';
    }
  };

  cellRenderVisitDate = (data) => {
    if (data.value) {
      let visitDate = '';
      visitDate = this.datePipe.transform(data.value, 'dd-MM-yyyy')
      return visitDate;
    } else {
      return '';
    }

  }


  cellRendererAnswerYn = (data) => {
    let dataR = data && data.value ? data.value : data;
    if (dataR) {
      let visitTypeName = '';
      if (dataR == 'Y') {
        visitTypeName = 'Yes'
      }
      if (dataR == 'N') {
        visitTypeName = 'No'
      }
      return visitTypeName;
    } else {
      return '';
    }
  };

  getHaRatingCellRender = (haR) => {

    if (haR) {
      let haRating;
      this.haRatingList.forEach(ele => {
        if (haR == ele.id) {
          haRating = ele.value;
        }
      })
      return haRating
    } else {
      return ' '
    }
  };

  getWaRatingCellRender = (waR) => {
    if (waR) {
      let waRating;
      this.waRatingList.forEach(ele => {
        if (waR == ele.id) {
          waRating = ele.value;
        }
      })
      return waRating
    } else {
      return ' '
    }
  };

  getWhRatingCellRender = (whR) => {
    if (whR) {
      let whRating;
      this.whRatingList.forEach(ele => {
        if (whR == ele.id) {
          whRating = ele.value;
        }
      })
      return whRating
    } else {
      return ' '
    }
  };

  getHcaRatingCellRender = (hcaR) => {
    if (hcaR) {
      let hcaRating;
      this.hcaRatingList.forEach(ele => {
        if (hcaR == ele.id) {
          hcaRating = ele.value;
        }
      })
      return hcaRating
    } else {
      return ' '
    }
  };

  getRegionCellRender = (reg) => {
    if (reg) {
      let regName;
      this.regionData.forEach(ele => {
        if (reg == ele.regCode) {
          regName = ele.regName;
        }
      })
      return regName
    } else {
      return ' '
    }
  };

  getWilayatCellRender = (wal) => {
    if (wal) {
      let walName;
      this.wallayatList.forEach(ele => {
        if (wal == ele.walCode) {
          walName = ele.walName;
        }
      })
      return walName
    } else {
      return ' '
    }
  };

  getInstituteCellRender = (inst) => {
    if (inst) {
      let instName;
      this.institutes.forEach(ele => {
        if (inst == ele.estCode) {
          instName = ele.estName;
        }
      })
      return instName
    } else {
      return ' '
    }
  };

  getGenderCellRender = (sex) => {
    if (sex) {
      let sexName;
      this.gender.forEach(ele => {
        if (sex == ele.id) {
          sexName = ele.value;
        }
      })
      return sexName
    } else {
      return ' '
    }
  };

  getLactionNameCellRender = (lac) => {
    if (lac) {
      let lacName;
      this.assLact.forEach(ele => {
        if (lac == ele.paramId) {
          lacName = ele.paramDesc;
        }
      })
      return lacName
    } else {
      return ' '
    }
  };


  getKeyMessageNameCellRender = (key) => {
    if (key) {
      let keyName;
      this.keyMsg.forEach(ele => {
        if (key == ele.paramId) {
          keyName = ele.paramDesc;
        }
      })
      return keyName
    } else {
      return ' '
    }
  };

  getSpecifyStatusNameCellRender = (status) => {
    if (status) {
      let statusName;
      this.malStatus.forEach(ele => {
        if (status == ele.paramId) {
          statusName = ele.paramDesc;
        }
      })
      return statusName
    } else {
      return ' '
    }
  };

  getLabTestNameCellRender = (test) => {
    if (test) {
      let testName;
      this.nutLabInvest.forEach(ele => {
        if (test == ele.paramId) {
          testName = ele.paramDesc;
        }
      })
      return testName
    } else {
      return ' '
    }
  };

  initialsBookingForm() {
    this.childNutritionRegister = this.fb.group({
      visitId: new FormControl(null),
      nutritionNo: new FormControl(null),
      nutritionId: new FormControl(null),
      regDate: ['', Validators.required],
      epiNo: new FormControl(null),
      lengthBirth: new FormControl(null),
      weightBirth: new FormControl(null),
      headCrBirth: new FormControl(null),
      headCircum: new FormControl(null),
      motherName: new FormControl(null),
      ageAtVisit: new FormControl(null),
      weight: new FormControl(null),
      height: new FormControl(null),
      diarrheaYn: new FormControl(null),
      oedemaYn: new FormControl(null),
      cfaYn: new FormControl(null),
      assOutcome: new FormControl(null),
      malStatus: new FormControl(null),
      medHistory: new FormControl(null),
      outcomeCause: new FormControl(null),
      outcomeCauseSpec: new FormControl(null),
      otherDiseases: new FormControl(null),
      otherDiseasesYn: new FormControl(null),
      whZscore: new FormControl(null),
      whLevel: new FormControl({ value: null, disabled: true }),
      haZscore: new FormControl(null),
      haLevel: new FormControl({ value: null, disabled: true }),
      waZscore: new FormControl(null),
      waLevel: new FormControl({ value: null, disabled: true }),
      haColor: new FormControl(null),
      whColor: new FormControl(null),
      waColor: new FormControl(null),
      hcaZscore: new FormControl(null),
      hcaLevel: new FormControl({ value: null, disabled: true }),
      hcaColor: new FormControl(null),
      createdBy: new FormControl(null),
      createdDate: new FormControl(null),
      modifiedBy: new FormControl(null),
      modifiedOn: new FormControl(null),
      keyMessages: new FormControl(null),
      status: new FormControl(null),
      healthAssesment: new FormControl(null),
      visitType: new FormControl('R'),
      haRating: new FormControl(null),
      hcaRating: new FormControl(null),
      waRating: new FormControl(null),
      whRating: new FormControl(null),
      assessmentRemarks: new FormControl(null),
      Rbmi: new FormControl(null)




    });
  }

  private initialsFollowUpForm() {
    this.followUpForm = this.fb.group({
      visitId: new FormControl(null),
      visitDate: new FormControl(null),
      ageAtVisit: new FormControl(null),
      diarrheaResult: new FormControl(null),
      diarrheaYn: new FormControl(null),
      haRating: new FormControl(null),
      haZscore: new FormControl(null),
      headCircum: new FormControl(null),
      healthStatus: new FormControl(null),
      height: new FormControl(null),
      oedemaYn: new FormControl(null),
      otherDiseases: new FormControl(null),
      otherDiseasesYn: new FormControl(null),
      visitType: new FormControl('F'),
      waRating: new FormControl(null),
      waZscore: new FormControl(null),
      weight: new FormControl(null),
      createdBy: new FormControl(null),
      createdDate: new FormControl(null),
      modifiedBy: new FormControl(null),
      modifiedOn: new FormControl(null),
      whRating: new FormControl(null),
      whZscore: new FormControl(null),
      whLevel: new FormControl({ value: null, disabled: true }),
      haLevel: new FormControl({ value: null, disabled: true }),
      waLevel: new FormControl({ value: null, disabled: true }),
      haColor: new FormControl(null),
      whColor: new FormControl(null),
      waColor: new FormControl(null),
      hcaZscore: new FormControl(null),
      hcaLevel: new FormControl({ value: null, disabled: true }),
      hcaColor: new FormControl(null),
      index: new FormControl(null),
      status: new FormControl(null),
      assessmentRemarks: new FormControl(null),
      visitInst: [null, Validators.required],
      hcaRating: new FormControl(null),
      whProgress: new FormControl(null),
      haProgress: new FormControl(null),
      waProgress: new FormControl(null),
      hcaProgress: new FormControl(null),
      result1: new FormControl(null),  // For test result (e.g., Hemoglobin)
      remarks1: new FormControl(null),  // For dynamic remarks (Normal, Mild Anaemia, etc.)
      whProgressDes: new FormControl(null),
      haProgressDes: new FormControl(null),
      hcaProgressDes: new FormControl(null),
      waProgressDes: new FormControl(null),
      Fbmi: new FormControl(null)

    });
  }

  ngOnInit() {


    this.searchForm = this.fb.group({
      nutNo: new FormControl(null)
    });
    this._masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];

    })

    this.initialsMainList();
    this.getMasterData();

    if (this.nutVisitsList && this.nutVisitsList.length >= 0) {
      const firstItem = this.nutVisitsList[0];
      this.addEditFollowUp(this.followUpForm.value, 'add', firstItem, 0);
    }
    
  }

  setMinRegistrationDateFromDOB() {
    const dobControl = this.patientDetails.patientForm.get('dob');
    const dobRaw = dobControl ? dobControl.value : null;

    if (dobRaw) {
      let dob: Date;

      if (typeof dobRaw === 'string') {
        const [day, month, year] = dobRaw.split('-').map(Number);
        dob = new Date(year, month - 1, day);
      } else {
        dob = new Date(dobRaw);
      }

      if (!isNaN(dob.getTime())) {
        const nextDay = new Date(dob);
        nextDay.setDate(nextDay.getDate());
        this.minRegDate = nextDay;
      } else {
        this.minRegDate = null;
      }
    } else {
      this.minRegDate = null;
    }
  }





  disableEnterKey(event: KeyboardEvent) {
    event.preventDefault();
  }


  getMasterData() {
    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institutes = res['result'];
      this.institutesFilter = res['result'];
    });

    this._masterService.getInstitutesMasterFull();
    this._masterService.institutesMasterFull.subscribe(value => {
      this.institutes = value;
      this.institutesFull = value;

    });


    this._ChildNutritionService.getChildNutritionMalStatusMast().subscribe(value => {
      this.malStatus = value.result;
    });

    this._ChildNutritionService.getChildNutritionAssOutcomeMast().subscribe(value => {
      this.assOutcome = value.result;
    });

    this._ChildNutritionService.getChildNutritionAssessLact().subscribe(value => {
      this.assLact = value.result;
    });

    this._ChildNutritionService.getChildNutritionKeyMsgs().subscribe(value => {
      this.keyMsg = value.result;
    });
    this._ChildNutritionService.getChildNutritionMedicalHistory().subscribe(value => {
      this.medHistory = value.result;
    });

    this.nutLabInvestR = [];
    this.nutLabInvestF = [];
    this._ChildNutritionService.getChildNutritionLabInvest().subscribe(value => {
      this.nutLabInvest = value.result;
      this.nutLabInvestR = _.cloneDeep(this.nutLabInvest);
      this.nutLabInvestF = _.cloneDeep(this.nutLabInvest);
    });

    this.cFAssessF = []
    this.cFAssessR = []
    this._ChildNutritionService.getChildNutritionCheckList().subscribe(value => {
      this.checkList = value.result;
    });

    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {

    });

    this._masterService.getWilayatList(0).subscribe(response => {
      this.wallayatList = response.result;
    }, error => {
    });

  }


  changeFollowUpInstitute(obj) {
    if (obj && !obj.regCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];
    }
  }

  addFollowup() {
    this.initialsFollowUpForm();
    this.followupList.push(new Date());

  }

  getfollowUpDetails(i) {

    this.selectedId = i;
  }

  getColor(val) {
    this.malnutriLevel = val;
  }
  getcolorcode(val) {
    this.levelValue = val;

  }

  malnutritionLevelChange(item) {

  }

  onChicked(item, event) {
    if (event.target.checked === true) {
      item.checked = true;
    }
    else {
      item.checked = false;
      item.result = null;
      item.remarks = null;
    }
  }

  clearUnSelect(soueceFormcontrol, TargetFormcontrol) {
    if (soueceFormcontrol.value == 'N' || soueceFormcontrol.value != 16) {
      TargetFormcontrol.setValue(null);
    };
  }

  onChickedCFAssess(item, event) {
    item.answerYn = event;
  }

  validateFollowUpInstitutes(): boolean {
    let isFollowUpInstitutesValid = true;

    const visitInstControl = this.followUpForm.controls['visitInst'];

    if (!visitInstControl || !visitInstControl.value) {
      isFollowUpInstitutesValid = false;
      visitInstControl.markAsTouched();
    }

    return isFollowUpInstitutesValid;
  }



  validateQuestions(): boolean {
    let allAnswered = true;

    // Loop through the list of questions
    for (let item of this.cFAssessF) {
      if (!item.answerYn || (item.answerYn !== 'Y' && item.answerYn !== 'N')) {
        allAnswered = false;
        break;
      }
    }

    return allAnswered;
  }
  validateQuestionsR(): boolean {
    let allAnswered = true;

    // Loop through the list of questions
    for (let item of this.cFAssessR) {
      if (!item.answerYn || (item.answerYn !== 'Y' && item.answerYn !== 'N')) {
        allAnswered = false;
        break;
      }
    }

    return allAnswered;
  }




  changeHemoColor(resNumber, age, type) {
    let backgroundColor;

    if (age && age >= 6 && age <= 23) {
      if (resNumber >= 10.5) {
        backgroundColor = '#00B050';
      } else if (resNumber >= 9.5 && resNumber <= 10.4) {
        backgroundColor = '#FFD966';
      } else if (resNumber >= 7 && resNumber <= 9.4) {
        backgroundColor = '#ED7D31';
      } else if (resNumber > 0 && resNumber < 7) {
        backgroundColor = '#FF0000';
      } else {
        backgroundColor = '';
      }
    } else if (age && age >= 24 && age <= 59) {
      if (resNumber >= 11) {
        backgroundColor = '#00B050';
      } else if (resNumber >= 10 && resNumber <= 10.9) {
        backgroundColor = '#FFD966';
      } else if (resNumber >= 7 && resNumber <= 9.9) {
        backgroundColor = '#ED7D31';
      } else if (resNumber > 0 && resNumber < 7) {
        backgroundColor = '#FF0000';
      } else {
        backgroundColor = '';
      }
    } else {
      backgroundColor = '';
    }

    if (type === 'R') {
      this.backgroundColorR = backgroundColor
    } else {
      this.backgroundColorF = backgroundColor
    }
  }


  onInput(item, event, type?) {
    const inputElement = event.target as HTMLInputElement;

    if (item.checked === true) {
      // Update the result for the checked test
      item[inputElement.id.replace(/[0-9]/g, '')] = inputElement.value;
    }

    // Only handle Hemoglobin-specific logic when the test is Haemoglobin and checked
    if (item.paramDesc === 'Haemoglobin' && item.checked) {
      const inputValue = Number(inputElement.value);
      let age;
      if (type === 'R') {
        age = this.childNutritionRegister.get('ageAtVisit').value;
      } else {
        age = this.followUpForm.get('ageAtVisit').value;
      }

      // Call method to change background color based on Hemoglobin value
      this.changeHemoColor(inputValue, age, type);

      // Update the remarks for Hemoglobin only
      if (inputValue >= 10.5) {
        item.remarks = "Normal";
      } else if (inputValue >= 9.5 && inputValue <= 10.4) {
        item.remarks = "Mild Anaemia";
      } else if (inputValue >= 7 && inputValue <= 9.4) {
        item.remarks = "Moderate Anaemia";
      } else if (inputValue < 7) {
        item.remarks = "Severe Anaemia";
      }
    }
  }


  DownloadLabDetails(estCode: any, patientId: any) {
    if (estCode == 0 && patientId == 0) {
      estCode = this.patientDetails.patientForm.controls["regInst"].value; //20068
      patientId = this.patientDetails.patientForm.controls["patientId"].value;

      if (estCode == "" || patientId == "" || estCode == null || patientId == null) {
        this.clear();
        Swal.fire({
          icon: 'warning',
          title: 'PatientID and Institute should be selected'
        });
        return false;
      }
    }

    this.spinner.show();

    this._ChildNutritionService.fetchChildNutRegisterFromShifa(estCode, patientId).subscribe(res => {

      this.spinner.hide();
      if (res['code'] == "S0000") {
        this.downloadFromShifa = false;

        let labData = res["result"].vwChildNutLabDtlsDto;
        if (labData != null) {
          for (let lab of labData) {
            let mohCode = lab.mohTestCode;

            this._ChildNutritionService.fetchChildNutLabDetails(mohCode).subscribe(res => {
              if (res['code'] == "S0000") {
                let paramIdFromRes = res["result"].paramId;
                this.nutLabInvestR.forEach(item => {

                  if (item.paramId === paramIdFromRes) { // Compare paramId with paramIdFromRes
                    let testName = this.getLabTestNameCellRender(paramIdFromRes); // Fetch the test name 
                    item.paramDesc = testName;  // Update the test name for the matching paramId
                    item.checked = true;        // Mark it as checked
                    item.result = lab.result;   // Fill the result

                    // Format the releasedDt (Date) to a user friendly format
                    if (lab.releasedDt) {
                      let formattedDate = new Date(lab.releasedDt); // Create a Date object

                      // Use Intl.DateTimeFormat to format the date (year/month/day, hour:minute:second)
                      const formattedRemarks = new Intl.DateTimeFormat('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false,  // Use 24-hour format
                      }).format(formattedDate);

                      // Assign the formatted date to remarks
                      item.remarks = formattedRemarks;
                    }
                  }
                });
              } else {
                Swal.fire(' ', 'Failed while Fetching Lab Details', 'warning');
              }
            });
          }
        }

      } else {
        Swal.fire(' ', 'Failed while Fetching At Birth Details From alshifa data', 'warning')
      }

    })

  }

  DownloadBirthDetails(estCode: any, patientId: any) {
    if (estCode == 0 && patientId == 0) {
      estCode = this.patientDetails.patientForm.controls["regInst"].value; //20068
      patientId = this.patientDetails.patientForm.controls["patientId"].value;

      if (estCode == "" || patientId == "" || estCode == null || patientId == null) {
        this.clear();
        Swal.fire({
          icon: 'warning',
          title: 'PatientID and Institute should be selected'
        });
        return false;
      }
    }

    this.spinner.show();

    this._ChildNutritionService.fetchChildNutRegisterFromShifa(estCode, patientId).subscribe(res => {

      this.spinner.hide();
      if (res['code'] == "S0000") {
        this.downloadFromShifa = false;

        this.childNutritionRegister.patchValue({ 'weightBirth': res["result"].vwChildNutClinicalDtlsDto[0].weightBirth })
        this.childNutritionRegister.patchValue({ 'lengthBirth': res["result"].vwChildNutClinicalDtlsDto[0].lengthBirth })
        this.childNutritionRegister.patchValue({ 'headCrBirth': res["result"].vwChildNutClinicalDtlsDto[0].hcBirth })

      } else {
        Swal.fire(' ', 'No Record Found From Al-shifa for AT Birth Details', 'warning')
      }

    })

  }



  AddNewAsses(visitType) {
    if (visitType == 'R') {
      this.nutAssesList.push({});

    }
    if (visitType == 'F') {
      this.nutAssesListF.push({});
    }
  }

  addEditAsses(visitType, item, i) {
    if (visitType == 'R') {
      item.visitType = 'R';
      this.nutAssesList[i] = item;

    }
    if (visitType == 'F') {
      item.visitType = 'R';
      this.nutAssesListF[i] = item;
    }
  }

  removeAsses(visitType, index: number) {
    if (visitType == 'R') {
      this.nutAssesList.splice(index, 1);

    }
    if (visitType == 'F') {
      this.nutAssesListF.splice(index, 1);
    }
  }

  AddNewKeyMsg(visitType) {
    if (visitType == 'R') {
      this.nutKeyMsgList.push({});

    }
    if (visitType == 'F') {
      this.nutKeyMsgListF.push({});
    }
  }

  addEditKeyMsg(visitType, item, i) {
    if (visitType == 'R') {
      item.visitType = 'R';
      this.nutKeyMsgList[i] = item;

    }
    if (visitType == 'F') {
      item.visitType = 'F';
      this.nutKeyMsgListF[i] = item;
    }
  }

  removeKeyMsg(visitType, index: number) {
    if (visitType == 'R') {
      this.nutKeyMsgList.splice(index, 1);

    }
    if (visitType == 'F') {
      this.nutKeyMsgListF.splice(index, 1);
    }
  }

  autoGrowTextZone(e) {
    e.target.style.height = (e.target.scrollHeight) + "px";
  }

  search(nutNoSerch?) {

    let nutNo = this.searchForm.get('nutNo').value;
    this.clearAll();
    if (nutNo) {
      this.getChildNutritionRegistry(nutNo);
    }
    else if (nutNoSerch) {
      this.getChildNutritionRegistry(nutNoSerch);
    }
    else {
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Nut Number'
      });
    }
  }

  clear() {
    this.initialsBookingForm();
    this.initialsFollowUpForm();
    this.nutVisitsList = []
    this.downloadFromShifa = true;

  }

  loadFollowup() {
    if (this.nutVisitsList && this.nutVisitsList.length >= 0) {
      const firstItem = this.nutVisitsList[0];
      this.addEditFollowUp(this.followUpForm.value, 'add', firstItem, 0);
    }
  }

  getChildNutritionRegistry(nutNo) {
    this.downloadFromShifa = false;
    this._ChildNutritionService.getChildNutritionRegistry(nutNo).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.dataFetched = true;
        this.allNutData = res['result'];


        if (this.allNutData.activeYn === 'Y') {
          this.patientDetails.setPatientDetails(this.allNutData);
          this.getRegisterform(this.allNutData);

          this.allNutData.rgTbNutRegister.rgTbNutVisits.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.nutVisitsList.push(el);
            }
            // Sort nutVisitsList by visitDate in ascending order
            this.nutVisitsList.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());

          })

          // for followp initial load 
          this.loadFollowup();

        } else {
          Swal.fire({
            title: 'Child Not Active',
            text: 'Do you want active this child?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, active it!'
          }).then((result) => {
            if (result.isConfirmed) {
              this.submitForm(true);
            }
          });
        }


      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Entered nut No", 'warning');

      } else {
        Swal.fire(' ', 'The entered Nut Number does not match any existing data. Please double-check and re-enter the correct NutNo.', 'warning')
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  callFetchDataFromAlShifa(estCode: any, patientId: any, valCode: any) {
    //call check se  estCode :any,patientId : any,regType:any    this.patientDetails.patientForm.controls["Institute"].value

    if (estCode == 0 && patientId == 0) {
      estCode = this.patientDetails.patientForm.controls["regInst"].value; //20068
      patientId = this.patientDetails.patientForm.controls["patientId"].value;

      if (estCode == "" || patientId == "" || estCode == null || patientId == null) {
        this.clear();
        Swal.fire({
          icon: 'warning',
          title: 'PatientID and Institute should be selected'
        });
        return false;
      }


    }


    this._ChildNutritionService.checkChildRegister(estCode, patientId, AppUtils.REG_TYPE_CHILD_NUT).subscribe(res => {
      this.clear();
      if (res['code'] == "0") {
        this.search(res['result']);
      } else if (res['code'] == "3" || res['code'] == "5") {
        this.FetchDataFromAlShifa(estCode, patientId, valCode);
      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving the  details', 'error')
    });




  }


  // Start Fetch from Al Shifa
  FetchDataFromAlShifa(estCode: any, patientId: any, valCode: any) {
    this.spinner.show();



    this._ChildNutritionService.fetchChildNutRegisterFromShifa(estCode, patientId).subscribe(res => {

      this.spinner.hide();
      if (res['code'] == "S0000") {
        this.downloadFromShifa = false;
        let mpiUser = {};
        mpiUser = res["result"].personInfoDto;
        valCode = res["result"].vwChildNutClinicalDtlsDto[0].valCode;


        if (mpiUser != null) {
          this.patientDetails.patientForm.patchValue({
            civilId: mpiUser["civilId"],
            patientId: Number(patientId),
            regInst: Number(estCode),
            firstName: mpiUser["firstNameEn"],
            secondName: mpiUser["secondNameEn"],
            thirdName: mpiUser["thirdNameEn"],
            tribe: mpiUser["sixthNameEn"],
            dob: new Date(mpiUser["birthDate"]),
            sex: mpiUser["sex"] === "Male" ? "M" : "F",
            age:this.ageFromDateOfBirthday(new Date(mpiUser["birthDate"])),
            nationality: mpiUser["countryID"],
            maritalStatus: mpiUser["maritalStatus"] === "Married" ? "M" : (mpiUser["maritalStatus"] === "Single") ? "S" : (mpiUser["maritalStatus"] === "Divorced") ? "D" : (mpiUser["maritalStatus"] === "Widow/Widower") ? "W" : "O",
            mobileNo: mpiUser["mobileNo"],
            village: valCode,
          })

        }
        
        //set Governorate, Wilayat, Town/Village if valCode is fetching from Al Shifa
        let valObj
        if (valCode != 0) {
          let wal;
          let reg;
          let val: number;
          val = Number(valCode);
          wal = this.villageList.filter(s => s.vilCode == valCode).map(s => s.walCode)[0];
          reg = this.wallayatList.filter(s => s.walCode == wal).map(s => s.regCode)[0];
          valObj = { "valCode": val, "walCode": wal, "regCode": reg };
          this.patientDetails.changeRegion(valObj);
          this.patientDetails.changeWalayat(valObj);
          this.patientDetails.patientForm.patchValue({ 'village': val });
        }

        this.childNutritionRegister.patchValue({ 'weightBirth': res["result"].vwChildNutClinicalDtlsDto[0].weightBirth })
        this.childNutritionRegister.patchValue({ 'lengthBirth': res["result"].vwChildNutClinicalDtlsDto[0].lengthBirth })
        this.childNutritionRegister.patchValue({ 'headCrBirth': res["result"].vwChildNutClinicalDtlsDto[0].hcBirth })

        let labData = res["result"].vwChildNutLabDtlsDto;
        if (labData != null) {
          for (let lab of labData) {
            let mohCode = lab.mohTestCode;

            this._ChildNutritionService.fetchChildNutLabDetails(mohCode).subscribe(res => {
              if (res['code'] == "S0000") {
                let paramIdFromRes = res["result"].paramId;
                this.nutLabInvestR.forEach(item => {

                  if (item.paramId === paramIdFromRes) { // Compare paramId with paramIdFromRes
                    let testName = this.getLabTestNameCellRender(paramIdFromRes); // Fetch the test name 
                    item.paramDesc = testName;  // Update the test name for the matching paramId
                    item.checked = true;        // Mark it as checked
                    item.result = lab.result;   // Fill the result

                    // Format the releasedDt (Date) to a user friendly format
                    if (lab.releasedDt) {
                      let formattedDate = new Date(lab.releasedDt); // Create a Date object

                      // Use Intl.DateTimeFormat to format the date (year/month/day, hour:minute:second)
                      const formattedRemarks = new Intl.DateTimeFormat('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false,  // Use 24-hour format
                      }).format(formattedDate);

                      // Assign the formatted date to remarks
                      item.remarks = formattedRemarks;
                    }
                  }
                });
              } else {
                Swal.fire(' ', 'Failed while Fetching Lab Details', 'warning');
              }
            });
          }
        }

        this.setMinRegistrationDateFromDOB();

      } else {
        Swal.fire(' ', 'Failed while Fetching alshifa data', 'warning')
      }

    })
  }
  // End Fetch from Al Shifa

   ageFromDateOfBirthday(dateOfBirth: any): number {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  getCheckListPeriod(dob, regDate, rVisit?) {
    this._ChildNutritionService.getNutritionPeriod(dob, regDate).subscribe(res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        let period = res.result;
        this.rCheckListFilterd = this.checkList.filter(item => item.periodId == period);
        let periodRangeR = this.cAssPeriod.find(item => item.value == period);
        this.periodRangeNameR = periodRangeR.name;
        this.cFAssessR = _.cloneDeep(this.rCheckListFilterd.sort((a, b) => a.sortOrder - b.sortOrder));
        this.cFAssessR = this.cFAssessR.filter((item, index, self) =>
          index === self.findIndex((t) => t.qstnId === item.qstnId)
        );

        // Register CF
        if (rVisit && rVisit.rgTbNutCfAssessment && rVisit.rgTbNutCfAssessment.length > 0) {
          rVisit.rgTbNutCfAssessment.forEach(item1 => {
            this.cFAssessR.forEach(item2 => {
              if (item1.qstnId === item2.qstnId) {
                item2.answerYn = item1.answerYn;
                item2.runId = item1.runId;
                item2.createdBy = item1.createdBy ? item1.createdBy : null;
                item2.createdDate = item1.createdDate ? item1.createdDate : null;
                item2.modifiedBy = item1.modifiedBy ? item1.modifiedBy : null;
                item2.modifiedDate = item1.modifiedDate ? item1.modifiedDate : null;

              }
            })
          })


        }

      } else {
        Swal.fire(' ', 'Failed while calculate check list period', 'warning')
      }

    })
  }

  initialsMainList() {
    this.allNutData = this.allNutData ? this.allNutData : {};
    this.allNutData.rgTbNutRegister = this.allNutData.rgTbNutRegister ? this.allNutData.rgTbNutRegister : {};
    this.allNutData.rgTbNutRegister.rgTbNutVisits = this.allNutData.rgTbNutRegister.rgTbNutVisits ? this.allNutData.rgTbNutRegister.rgTbNutVisits : [];
  }

  getRegisterform(data) {
    if (data && data.rgTbNutRegister) {
      let nutRegInfo = data.rgTbNutRegister;
      if (nutRegInfo) {
        nutRegInfo.regDate = nutRegInfo.regDate ? this.cellRenderer(nutRegInfo.regDate) : null;
         this.disableRegDate = nutRegInfo.regDate ? true : false;
        let rVisit = nutRegInfo.rgTbNutVisits.filter(el => el.visitType == "R")[0];

       

        // lactitation assessment
        if (rVisit && rVisit.rgTbNutLactation && rVisit.rgTbNutLactation.length) {
          this.nutAssesList = rVisit.rgTbNutLactation;
        }

        // key Messages
        if (rVisit && rVisit.rgTbNutKeyMessages && rVisit.rgTbNutKeyMessages.length) {
          this.nutKeyMsgList = rVisit.rgTbNutKeyMessages;
        }

        this.visitsHist = _.cloneDeep(nutRegInfo.rgTbNutVisits);

        let hemoRes;

        //reg LAB CHECK
        if (rVisit && rVisit.rgTbNutLabInvsts && rVisit.rgTbNutLabInvsts.length > 0) {
          rVisit.rgTbNutLabInvsts.forEach(item1 => {
            this.nutLabInvestR.forEach(item2 => {
              if (item1.testId === item2.paramId) {
                if (item1.testId == 40) {
                  hemoRes = item1.result;
                }
                item2.checked = true;
                item2.result = item1.result;
                item2.remarks = item1.remarks;
                item2.invstId = item1.invstId;
                item2.createdBy = item1.createdBy || null;
                item2.createdDate = item1.createdDate || null;
                item2.modifiedBy = item1.modifiedBy || null;
                item2.modifiedDate = item1.modifiedDate || null;
              } else if (item2.checked == null) {
                item2.checked = false;
              }
            });
          });
        }




        if (rVisit.ageAtVisit == null || rVisit.visitId == null) {
          rVisit.ageAtVisit = this.age;
        }

        if (rVisit.otherDiseases && rVisit.otherDiseases != null) {
          rVisit.otherDiseasesYn = 'Y';
        } else if (rVisit.otherDiseases === null) {
          rVisit.otherDiseasesYn = 'N';
        } else {
          rVisit.otherDiseasesYn = null;
        }

        this.onInputClacRCFAssess(this.allNutData.rgTbPatientInfo.dob, this.allNutData.rgTbNutRegister.regDate, rVisit);

        // pass data to form
        this.childNutritionRegister.patchValue(nutRegInfo)
        this.childNutritionRegister.patchValue(rVisit)


        this.onInputCalc('R');
        this.changeHemoColor(hemoRes, rVisit.ageAtVisit, 'R');
      }

      //this.patientDetails.patientForm.get('regDate').disable;


    }
   this.getAgeFromDob();





  }



  getFollowUpVisit(data, indx) {
    this.clearFollowUp();
    data.index = indx;
    this.getAgeFromDob();

    let dob = this.patientDetails.patientForm.get('dob').value;
    if (dob) {

      this._ChildNutritionService.getNutritionPeriod(dob, data.visitDate).subscribe(res => {
        if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
          let period = res.result;
          this.fCheckListFilterd = this.checkList.filter(item => item.periodId == period);
          let periodRangeF = this.cAssPeriod.find(item => item.value == period);
          this.periodRangeNameF = periodRangeF.name;
          this.cFAssessF = _.cloneDeep(this.fCheckListFilterd.sort((a, b) => a.sortOrder - b.sortOrder));

          this.cFAssessF = this.cFAssessF.filter((item, index, self) =>
            index === self.findIndex((t) => t.qstnId === item.qstnId)
          );


          if (data.ageAtVisit == null || data.visitId == null) {
            data.ageAtVisit = this.age;
            if (this.age >= 60 && (this.childNutritionRegister.get('status').value == 20 || this.childNutritionRegister.get('status').value == null)) {
              data.status = 21;
            }
          }

          if (data.otherDiseases && data.otherDiseases != null) {
            data.otherDiseasesYn = 'Y';
          } else if (data.otherDiseases === null) {
            data.otherDiseasesYn = 'N';
          } else {
            data.otherDiseasesYn = null;
          }

          // FollowUp CF
          if (data.rgTbNutCfAssessment && data.rgTbNutCfAssessment.length > 0) {
            data.rgTbNutCfAssessment.forEach(item1 => {
              this.cFAssessF.forEach(item2 => {
                if (item1.qstnId === item2.qstnId) {
                  item2.answerYn = item1.answerYn;
                  item2.runId = item1.runId;
                  item2.createdBy = item1.createdBy ? item1.createdBy : null;
                  item2.createdDate = item1.createdDate ? item1.createdDate : null;
                  item2.modifiedBy = item1.modifiedBy ? item1.createdBy : null;
                  item2.modifiedOn = item1.modifiedOn ? item1.createdBy : null;

                }
              })
            })


          }

          let hemoRes;

          // FollowUp LAB CHECK
          if (data.rgTbNutLabInvsts && data.rgTbNutLabInvsts.length > 0) {
            data.rgTbNutLabInvsts.forEach(item1 => {
              this.nutLabInvestF.forEach(item2 => {
                if (item1.testId === item2.paramId) {
                  if (item1.testId == 40) {
                    hemoRes = item1.result

                  }
                  item2.checked = true;
                  item2.result = item1.result
                  item2.remarks = item1.remarks
                  item2.invstId = item1.invstId
                  item2.createdBy = item1.createdBy ? item1.createdBy : null;
                  item2.createdDate = item1.createdDate ? item1.createdDate : null;
                  item2.modifiedBy = item1.modifiedBy ? item1.modifiedBy : null;
                  item2.modifiedDate = item1.modifiedDate ? item1.modifiedDate : null;
                } else {
                  if (item2.checked == null) {
                    item2.checked = false;
                  }
                }
              })
            })
          }

          if (data.rgTbNutKeyMessages && data.rgTbNutKeyMessages.length) {
            this.nutKeyMsgListF = data.rgTbNutKeyMessages;
          }

          if (data.rgTbNutLactation && data.rgTbNutLactation.length) {
            this.nutAssesListF = data.rgTbNutLactation;
          }
          this.followUpForm.patchValue(data)
          this.selectedFUDate = data.visitDate;
          this.selectedFollowUp = this.followUpForm.value;
          this.onInputCalc('F');
          this.changeHemoColor(hemoRes, data.ageAtVisit, 'F');

        } else {
          Swal.fire(' ', 'Failed while calculate check list period', 'warning')
        }
      });
    } else {
      Swal.fire('Missing Data', 'Date of Birth Must be Not Empty', 'warning')
    }


  }


  addNewFollowUp(e, action?) {
    if (this.childNutritionRegister.get('regDate').value) {
      this.initialsMainList();
      let dateExist = false;
      this.nutVisitsList.forEach(el => {
        if (this.cellRenderer(el.visitDate) == this.cellRenderer(e)) {
          dateExist = true;
          Swal.fire("", "Visit Date already exist!", "error");
          return;
        }

      })

      if (!dateExist) {
        this.nutVisitsList.push({ visitDate: e, visitId: null, ...e });
        this.allNutData.rgTbNutRegister.rgTbNutVisits.push({ visitDate: e, visitId: null, ...e })


        if (this.selectedFUDate) {
          this.addEditFollowUp(this.followUpForm.value, '', this.nutVisitsList[this.followUpForm.get('index').value], this.followUpForm.get('index').value);
        }

        if (action === 'get') {
          this.getFollowUpVisit(this.nutVisitsList[this.nutVisitsList.length - 1], this.nutVisitsList.length - 1)
        }


      }
    } else {
      Swal.fire('Missing Data', 'Register Form (Registration Date) Must be Not Empty', 'warning')
    }



  }

  removeFollowUp(index: number) {
    if (this.followUpClickedAdd) {
      this.nutVisitsList.splice(index, 1);
      this.allNutData.rgTbNutRegister.rgTbNutVisits.splice(index, 1);
      this.followUpForm.reset();
      if (this.nutVisitsList && this.nutVisitsList.length) {
        this.getFollowUpVisit(this.nutVisitsList[this.nutVisitsList.length - 1], this.nutVisitsList.length - 1);
      }
      else {
        this.selectedFUDate = null;
      }
    } else {
      Swal.fire('unsaved changes', 'You Must Click Add/Edit button to save current FollowUp!', 'warning');
    }

  }

  addEditFollowUp(data, action?, item?, i?) {




    if (this.followUpForm.valid) {
      data.visitType = 'F'
      this.nutVisitsList[data.index] = data

      // lab
      const isAtLestOnelabChecked = this.nutLabInvestF.some(item => item.checked === true);
      if (isAtLestOnelabChecked) {
        data.rgTbNutLabInvsts = [];
        this.nutLabInvestF.forEach(e => {
          if (e.checked) {
            let lab = {};
            lab['invstId'] = e.invstId ? e.invstId : null;
            lab['modifiedDate'] = e.modifiedDate ? e.modifiedDate : null;
            lab['modifiedBy'] = e.modifiedBy ? e.modifiedBy : null;
            lab['createdDate'] = e.createdDate ? e.createdDate : null;
            lab['createdBy'] = e.createdBy ? e.createdBy : null;
            lab['testId'] = e.paramId;
            lab['result'] = e.result;
            lab['remarks'] = e.remarks;
            lab['visitType'] = 'F';
            data.rgTbNutLabInvsts.push(lab);
          }
        });
      }

      // cF assessment
      const isAtLestOnecFAssessChecked = this.cFAssessF && this.cFAssessF.length && this.cFAssessF.some(item => item.answerYn) ? true : false;
      if (isAtLestOnecFAssessChecked) {
        this.nutVisitsList[data.index].rgTbNutCfAssessment = [];

        this.cFAssessF.forEach(e => {
          if (e.answerYn) {
            let cFAssess = {};
            cFAssess['runId'] = e.runId ? e.runId : null;
            cFAssess['modifiedDate'] = e.modifiedDate ? e.modifiedDate : null;
            cFAssess['modifiedBy'] = e.modifiedBy ? e.modifiedBy : null;
            cFAssess['createdBy'] = e.createdBy ? e.createdBy : null;
            cFAssess['createdDate'] = e.createdDate ? e.createdBy : null;
            cFAssess['answerYn'] = e.answerYn;
            cFAssess['qstnId'] = e.qstnId;
            cFAssess['visitType'] = 'F';
            this.nutVisitsList[data.index].rgTbNutCfAssessment.push(cFAssess);
          }
        });
      }

      if (this.nutKeyMsgListF && this.nutKeyMsgListF.length) {
        this.nutVisitsList[data.index].rgTbNutKeyMessages = this.nutKeyMsgListF;
      }

      if (this.nutAssesListF && this.nutAssesListF.length) {
        this.nutVisitsList[data.index].rgTbNutLactation = this.nutAssesListF;
      }

      this.allNutData.rgTbNutRegister.rgTbNutVisits[data.index] = this.nutVisitsList[data.index];
      this.selectedFollowUp = this.nutVisitsList[data.index];
    } else {

      if (!this.unsavedFollowupDtls) {
        Swal.fire('Alert', 'Mandatory fields in Follow Up cannot be empty', 'warning');
        this.removeFollowUp(this.nutVisitsList.length - 1)
      }
    }

    if (action === 'add') {
      this.getFollowUpVisit(item, i)
    }


  }

  clearFollowUp() {
    this.initialsFollowUpForm();
    this.cFAssessF = _.cloneDeep(this.rCheckListFilterd);
    this.nutLabInvestF = _.cloneDeep(this.nutLabInvest);
    this.nutKeyMsgListF = [];
    this.nutAssesListF = [];
  }


  unsavedFollowupDtls() {
    if (this.areObjectsEqual(this.selectedFollowUp, this.followUpForm.value)) {
      return true;
    } else {
      return false;
    }
  }

  areObjectsEqual(obj1, obj2) {
    // Check if both inputs are null or undefined
    if (obj1 === null && obj2 === null) {
      return true;
    }

    // Check if one of the objects is null or undefined, but not both
    if (obj1 === null || obj2 === null || obj1 === undefined || obj2 === undefined) {
      return false;
    }

    // Check if both inputs are objects
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return obj1 === obj2;
    }

    // Check if both objects have the same set of keys
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    // Check if the values of each key are equal
    for (const key of keys1) {
      if (!this.areObjectsEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  }

  getAgeFromDob() {
    let value = this.patientDetails.patientForm.get('dob').value;
    const birthDate = new Date(value);
    const today = new Date();

    const yearsDifference = today.getFullYear() - birthDate.getFullYear();
    const monthsDifference = today.getMonth() - birthDate.getMonth();
    const daysDifference = today.getDate() - birthDate.getDate();

    let ageInMonths = yearsDifference * 12 + monthsDifference;
    if (daysDifference < 0) {
      ageInMonths--;
    }

    let age = ageInMonths;

    if (this.childNutritionRegister.get('nutritionNo').value == null || this.childNutritionRegister.get('ageAtVisit').value == null) {
      this.childNutritionRegister.get('ageAtVisit').setValue(age);
      if (age >= 60 && (this.childNutritionRegister.get('status').value == 20 || this.childNutritionRegister.get('status').value == null)) {
        this.childNutritionRegister.get('status').setValue(21);
      }
    }

    this.age = age;
    this.setMinRegistrationDateFromDOB();

  }

  onInputCalc(item, event?) {
    if (item == 'R') {


      this.intialsZscors(this.childNutritionRegister);
      let rHeight = this.childNutritionRegister.get('height').value ? this.childNutritionRegister.get('height').value : -1;
      let rWeight = this.childNutritionRegister.get('weight').value ? this.childNutritionRegister.get('weight').value : -1;
      let rHeadCircum = this.childNutritionRegister.get('headCircum').value ? this.childNutritionRegister.get('headCircum').value : -1;
      let rOedemaYn = null;

      if (event && event.target && event.target.value == "Y" || event && event.target && event.target.value == "N") {
        rOedemaYn = event.target.value;
      } else {
        rOedemaYn = this.childNutritionRegister.get('oedemaYn').value ? this.childNutritionRegister.get('oedemaYn').value : null;
      }

      let requiFiledMessage = '';

      !this.patientDetails.patientForm.get('sex').value ? requiFiledMessage = requiFiledMessage + ' - Gender Must be Not Empty.<br>' : null;
      !this.patientDetails.patientForm.get('dob').value ? requiFiledMessage = requiFiledMessage + ' - Date of Birth Must be Not Empty.<br>' : null;
      !this.childNutritionRegister.get('regDate').value ? requiFiledMessage = requiFiledMessage + ' - Registration Date Must be Not Empty.<br>' : null;

      if (requiFiledMessage == '') {
        this._ChildNutritionService.getNutritionZscoreCalcR(this.patientDetails.patientForm.get('sex').value, this.patientDetails.patientForm.get('dob').value, this.childNutritionRegister.get('regDate').value, rHeight, rWeight, rHeadCircum, -1, -1, -1, "", rOedemaYn).subscribe(res => {
          if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
            this.rZscore = res.result;

            let wh = this.rZscore.filter(el => el.zscoreType == "WFLH")[0];
            let ha = this.rZscore.filter(el => el.zscoreType == "LHFA")[0];
            let wa = this.rZscore.filter(el => el.zscoreType == "WTFA")[0];
            let bm = this.rZscore.filter(el => el.zscoreType == "BMFA")[0];
            let hca = this.rZscore.filter(el => el.zscoreType == "HCFA")[0];


            if (bm) {
              let bmZscore = bm.zscore;
              this.childNutritionRegister.get('Rbmi').setValue(bmZscore);
            }

            if (wh) {
              let whZscore = wh.zscore;
              let whLevel = wh.zscoreLevel;
              let whColor = wh.zscoreColor;
              this.childNutritionRegister.get('whZscore').setValue(whZscore);
              this.childNutritionRegister.get('whLevel').setValue(whLevel);
              this.childNutritionRegister.get('whColor').setValue(whColor);
              this.childNutritionRegister.get('whRating').setValue(whLevel);
            }

            if (ha) {
              let haZscore = ha.zscore;
              let haLevel = ha.zscoreLevel;
              let haColor = ha.zscoreColor;
              this.childNutritionRegister.get('haZscore').setValue(haZscore);
              this.childNutritionRegister.get('haLevel').setValue(haLevel);
              this.childNutritionRegister.get('haColor').setValue(haColor);
              this.childNutritionRegister.get('haRating').setValue(haLevel);
            }

            if (wa) {
              let waZscore = wa.zscore;
              let waLevel = wa.zscoreLevel;
              let waColor = wa.zscoreColor;
              this.childNutritionRegister.get('waZscore').setValue(waZscore);
              this.childNutritionRegister.get('waLevel').setValue(waLevel);
              this.childNutritionRegister.get('waColor').setValue(waColor);
              this.childNutritionRegister.get('waRating').setValue(waLevel);
            }

            if (hca) {
              let hcaZscore = hca.zscore;
              let hcaLevel = hca.zscoreLevel;
              let hcaColor = hca.zscoreColor;
              this.childNutritionRegister.get('hcaZscore').setValue(hcaZscore);
              this.childNutritionRegister.get('hcaLevel').setValue(hcaLevel);
              this.childNutritionRegister.get('hcaColor').setValue(hcaColor);
              this.childNutritionRegister.get('hcaRating').setValue(hcaLevel);
            }

          } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD) {
            this.intialsZscors(this.childNutritionRegister);
          } else {
            Swal.fire(' ', 'Failed while calculate z-score', 'warning')
          }
        });
      } else {
        Swal.fire('Missing Data', requiFiledMessage, 'warning')
      }
    }


    if (item == "F") {
      this.intialsZscors(this.followUpForm);
      let fHeight = this.followUpForm.get('height').value ? this.followUpForm.get('height').value : -1;
      let fWeight = this.followUpForm.get('weight').value ? this.followUpForm.get('weight').value : -1;
      let fHeadCircum = this.followUpForm.get('headCircum').value ? this.followUpForm.get('headCircum').value : -1;
      let fOedemaYn = null;

      if (event && event.target && event.target.value == "Y" || event && event.target && event.target.value == "N") {
        fOedemaYn = event.target.value;
      } else {
        fOedemaYn = this.childNutritionRegister.get('oedemaYn').value ? this.childNutritionRegister.get('oedemaYn').value : null;
      }

      let requiFiledMessage = '';

      !this.patientDetails.patientForm.get('sex').value ? requiFiledMessage = requiFiledMessage + ' - Gender Must be Not Empty.<br>' : null;
      !this.patientDetails.patientForm.get('dob').value ? requiFiledMessage = requiFiledMessage + ' - Date of Birth Must be Not Empty.<br>' : null;
      !this.followUpForm.get('visitDate').value ? requiFiledMessage = requiFiledMessage + ' - Visit Date Date Must be Not Empty.<br>' : null;


      if (requiFiledMessage == '') {
        this._ChildNutritionService.getNutritionZscoreCalc(this.patientDetails.patientForm.get('centralRegNo').value, this.patientDetails.patientForm.get('sex').value, this.patientDetails.patientForm.get('dob').value, this.followUpForm.get('visitDate').value, fHeight, fWeight, fHeadCircum, -1, -1, -1, "", fOedemaYn).subscribe(res => {
          if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
            this.fZscore = res.result;



            let wh = this.fZscore.filter(el => el.zscoreType == "WFLH")[0];
            let ha = this.fZscore.filter(el => el.zscoreType == "LHFA")[0];
            let wa = this.fZscore.filter(el => el.zscoreType == "WTFA")[0];
            let hca = this.fZscore.filter(el => el.zscoreType == "HCFA")[0];
            let bm = this.fZscore.filter(el => el.zscoreType == "BMFA")[0];

            if (bm) {
              let bmZscore = bm.zscore;
              this.followUpForm.get('Fbmi').setValue(bmZscore);
            }

            if (wh) {
              let whZscore = wh.zscore;
              let whLevel = wh.zscoreLevel;
              let whColor = wh.zscoreColor;
              let whProgress = wh.whProgress;
              this.followUpForm.get('whProgressDes').setValue(wh.whProgressDes);
              if (whProgress == 'I') {
                this.followUpForm.get('whProgress').setValue("Improved")
              } else if (whProgress == 'W') {
                this.followUpForm.get('whProgress').setValue("Worse")
              } else if (whProgress == 'N') {
                this.followUpForm.get('whProgress').setValue("Not Improved")
              } else {
                this.followUpForm.get('whProgress').setValue("Not Improved")
              }

              this.followUpForm.get('whZscore').setValue(whZscore);
              this.followUpForm.get('whLevel').setValue(whLevel);
              this.followUpForm.get('whColor').setValue(whColor);
              this.followUpForm.get('whRating').setValue(whLevel);

            }

            if (ha) {
              let haZscore = ha.zscore;
              let haLevel = ha.zscoreLevel;
              let haColor = ha.zscoreColor;
              let haProgress = ha.haProgress;

              this.followUpForm.get('haProgressDes').setValue(ha.haProgressDes);

              if (haProgress == 'I') {
                this.followUpForm.get('haProgress').setValue("Improved");
              } else if (haProgress == 'W') {
                this.followUpForm.get('haProgress').setValue("Worse");
              } else if (haProgress == 'N') {
                this.followUpForm.get('haProgress').setValue("Not Improved");
              } else {
                this.followUpForm.get('haProgress').setValue("Not Improved");
              }

              this.followUpForm.get('haZscore').setValue(haZscore);
              this.followUpForm.get('haLevel').setValue(haLevel);
              this.followUpForm.get('haColor').setValue(haColor);
              this.followUpForm.get('haRating').setValue(haLevel);
            }

            if (wa) {
              let waZscore = wa.zscore;
              let waLevel = wa.zscoreLevel;
              let waColor = wa.zscoreColor;
              let waProgress = wa.waProgress;
              this.followUpForm.get('waProgressDes').setValue(wa.waProgressDes);
              if (waProgress == 'I') {
                this.followUpForm.get('waProgress').setValue("Improved")
              } else if (waProgress == 'W') {
                this.followUpForm.get('waProgress').setValue("Worse")
              } else if (waProgress == 'N') {
                this.followUpForm.get('waProgress').setValue("Not Improved")
              } else {
                this.followUpForm.get('waProgress').setValue("Not Improved")
              }

              this.followUpForm.get('waZscore').setValue(waZscore);
              this.followUpForm.get('waLevel').setValue(waLevel);
              this.followUpForm.get('waColor').setValue(waColor);
              this.followUpForm.get('waRating').setValue(waLevel);
            }

            if (hca) {
              let hcaZscore = hca.zscore;
              let hcaLevel = hca.zscoreLevel;
              let hcaColor = hca.zscoreColor;
              let hcaProgress = hca.hcaProgress;
              this.followUpForm.get('hcaProgressDes').setValue(hca.hcaProgressDes);

              if (hcaProgress == 'I') {
                this.followUpForm.get('hcaProgress').setValue("Improved")
              } else if (hcaProgress == 'W') {
                this.followUpForm.get('hcaProgress').setValue("Worse")
              } else if (hcaProgress == 'N') {
                this.followUpForm.get('hcaProgress').setValue("Not Improved")
              } else {
                this.followUpForm.get('hcaProgress').setValue("Not Improved")
              }
              this.followUpForm.get('hcaZscore').setValue(hcaZscore);
              this.followUpForm.get('hcaLevel').setValue(hcaLevel);
              this.followUpForm.get('hcaColor').setValue(hcaColor);
              this.followUpForm.get('hcaRating').setValue(hcaLevel);
            }

          } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD) {
            this.intialsZscors(this.followUpForm);
          } else {
            Swal.fire(' ', 'Failed while calculate z-score', 'warning')
          }
        });
      } else {
        Swal.fire('Missing Data', requiFiledMessage, 'warning')
      }
    }

  }






  submitForm(active?) {
    this.submitted = true;

    // If it's a follow-up form submission
    if (this.selectedFUDate && !active) {


      if (!this.validateQuestions()) {
        Swal.fire('Error', 'Please answer all Follow up questions.', 'warning');
        return; // Stop the function if questions are unanswered
      }

      if (!this.validateFollowUpInstitutes() || this.followUpForm.invalid) {
        console.log('Form is invalid');
        Swal.fire('Error', 'FollowUp Institutes is required..', 'warning');
        return;
      }
      this.addEditFollowUp(
        this.followUpForm.value,
        '',
        this.nutVisitsList[this.followUpForm.get('index').value],
        this.followUpForm.get('index').value
      );
    }
    if (!this.childNutritionRegister.get('regDate').value) {
      this.childNutritionRegister.get('regDate').markAsTouched();
      Swal.fire('Error', 'Registration Date is required.', 'warning');
      return;
    }

    if (this.validateFields() || active) {
      if (!this.validateQuestionsR()) {
        Swal.fire('Error', 'Please answer all  Register questions.', 'warning');
        return; // Stop the function if questions are unanswered
      }

      // Registration logic
      if (this.childNutritionRegister.valid && !active) {
        const register: NutRegister = { ...this.childNutritionRegister.value };
        register.regDate = this.cellRendererBack(register.regDate);
        this.initialsFollowUpForm();
        this.followUpForm.patchValue(this.childNutritionRegister.value);
        let rVisit = _.cloneDeep(this.followUpForm.value);

        this.initialsFollowUpForm();
        rVisit.visitDate = this.cellRendererBack(
          this.childNutritionRegister.get('regDate').value
        );

        // Populate messages and assessments if available
        if (this.nutKeyMsgList && this.nutKeyMsgList.length) {
          rVisit.rgTbNutKeyMessages = this.nutKeyMsgList;
        }

        if (this.nutAssesList && this.nutAssesList.length) {
          rVisit.rgTbNutLactation = this.nutAssesList;
        }

        const isAtLeastOneLabChecked = this.nutLabInvestR.some(
          item => item.checked === true
        );
        if (isAtLeastOneLabChecked) {
          rVisit.rgTbNutLabInvsts = [];
          this.nutLabInvestR.forEach(e => {
            if (e.checked) {
              let lab = {
                invstId: e.invstId ? e.invstId : null,
                modifiedDate: e.modifiedDate ? e.modifiedDate : null,
                modifiedBy: e.modifiedBy ? e.modifiedBy : null,
                createdDate: e.createdDate ? e.createdDate : null,
                createdBy: e.createdBy ? e.createdBy : null,
                testId: e.paramId,
                result: e.result,
                remarks: e.remarks,
                visitType: 'R',
              };
              rVisit.rgTbNutLabInvsts.push(lab);
            }
          });
        }

        if (this.cFAssessR && this.cFAssessR.length) {
          rVisit.rgTbNutCfAssessment = [];
          this.cFAssessR.forEach(e => {
            if (e.answerYn) {
              let cFAssess = {
                runId: e.runId ? e.runId : null,
                modifiedDate: e.modifiedDate ? e.modifiedDate : null,
                modifiedBy: e.modifiedBy ? e.modifiedBy : null,
                createdDate: e.createdDate ? e.createdDate : null,
                createdBy: e.createdBy ? e.createdBy : null,
                answerYn: e.answerYn,
                qstnId: e.qstnId,
                visitType: 'R',
              };
              rVisit.rgTbNutCfAssessment.push(cFAssess);
            }
          });
        }

        // Clear empty lab results or assessments
        if (rVisit.rgTbNutLabInvsts && rVisit.rgTbNutLabInvsts.length < 1) {
          rVisit.rgTbNutLabInvsts = null;
        }

        if (rVisit.rgTbNutCfAssessment && rVisit.rgTbNutCfAssessment.length < 1) {
          rVisit.rgTbNutCfAssessment = null;
        }

        let allVisit = [];
        if (this.nutVisitsList && this.nutVisitsList.length) {
          this.nutVisitsList.forEach(el => {
            el.visitDate = this.cellRenderer(el.visitDate);
            el.visitDate = this.cellRendererBack(el.visitDate);
            if (el.rgTbNutLabInvsts && el.rgTbNutLabInvsts.length < 1) {
              el.rgTbNutLabInvsts = null;
            } else if (
              el.rgTbNutCfAssessment &&
              el.rgTbNutCfAssessment.length < 1
            ) {
              el.rgTbNutCfAssessment = null;
            }
            allVisit.push(el);
          });
          allVisit.push({ ...rVisit });
        } else {
          allVisit.push(rVisit);
        }

        register['rgTbNutVisits'] = allVisit;
        this.allNutData.rgTbNutRegister = register;
      }

      // Patient info
      if (this.patientDetails.patientForm.valid && !active) {
        this.allNutData.rgTbPatientInfo = this.patientDetails.patientForm.value;
        this.allNutData.rgTbPatientInfo.dob = this.cellRenderer(
          this.allNutData.rgTbPatientInfo.dob
        );
        this.allNutData.rgTbPatientInfo.dob = this.cellRendererBack(
          this.allNutData.rgTbPatientInfo.dob
        );
      }

      // Registry patient
      if (!active) {
        this.allNutData.regInst = this.allNutData.rgTbPatientInfo.regInst;
      }

      let data = _.cloneDeep(this.allNutData);

      // Save the form
      this._ChildNutritionService.saveNutRegister(data).subscribe(
        res => {
          if (res.code == AppUtils.RESPONSE_SUCCESS_CODE_SAVE) {
            if (active) {
              Swal.fire({
                icon: 'success',
                title: 'Activated!',
                text: 'Child has been activated.',
                showConfirmButton: false,
                timer: 1000,
              });
            } else {
              Swal.fire({
                icon: 'success',
                title: 'SAVED',
                text: 'Registered Successfully',
                showConfirmButton: false,
                timer: 2000,
              });
            }

            this.nutNo = res.result;
            this.clearAll();
            this.getChildNutritionRegistry(this.nutNo);
          }
        },
        error => {
          if (error.status == 401) {
            Swal.fire('', 'Error occurred while retrieving details', 'error');
          }
        }
      );
    } else {
      Swal.fire(
        'Alert',
        'Mandatory fields in ' + this.errorMsg + ' cannot be empty',
        'warning'
      );
    }
  }


  validateFields() {
    this.errorMsg = '';
    if (!this.childNutritionRegister.valid || !this.patientDetails.patientForm.valid) {
      if (!this.childNutritionRegister.valid) {
        this.errorMsg += ' (Register) '
        if (this.childNutritionRegister.get('outcomeCause').value != 16) {
          return true
        }

      }
      if (!this.patientDetails.patientForm.valid) {
        this.errorMsg += ' (Patient Details) '
      }
      return false
    } else {
      if (this.childNutritionRegister.get('outcomeCause').value == 16 && (this.childNutritionRegister.get('outcomeCauseSpec').value == null || this.childNutritionRegister.get('outcomeCauseSpec').value.trim().length === 0)) {
        this.errorMsg += ' (Register) '
        return false
      }
      return true
    }

  }

  callMpiMethod() {
    this._ChildNutritionService.getChildNutritionRegistryByCivilId(this.patientDetails.patientForm.value.civilId).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.clearAll();
        this.dataFetched = true;
        this.allNutData = res['result'];
        this.patientDetails.setPatientDetails(this.allNutData);

        this.getRegisterform(this.allNutData);

        if (this.allNutData && this.allNutData.rgTbNutRegister && this.allNutData.rgTbNutRegister.rgTbNutVisits && this.allNutData.rgTbNutRegister.rgTbNutVisits.length) {
          this.allNutData.rgTbNutRegister.rgTbNutVisits.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.nutVisitsList.push(el);
            }

          });
        }
       this.setMinRegistrationDateFromDOB();
      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        this.getdata('civilId', '', this.patientDetails.patientForm.value.civilId);
        
      } else {
        Swal.fire(' ', 'The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.', 'warning')
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  //get patient date (by RegNo or CivilID)
  getdata(searchby: any, regNo: any, civilId: any) {
    this.clearAll();
    let msg;
    let callMPI = true;

    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }
    }

    if (callMPI == true) {
      Swal.fire('', msg, 'warning').then
      this._sharedService.setPatientData(this.patientDetails);
      this._sharedService.fetchMpi().subscribe(res => {
        this.getAgeFromDob();
      });
      this.setMinRegistrationDateFromDOB();

    } else (
      Swal.fire('', msg, 'warning')
    )
  }



  onInputClacRCFAssess(dob?, regDate?, rVisit?) {
    const dobVal = dob || this.patientDetails.patientForm.get('dob').value;
    const regDateRaw = regDate || this.childNutritionRegister.get('regDate').value;

    if (dobVal && regDateRaw) {
      this.getCheckListPeriod(dobVal, regDateRaw, rVisit);

      // 🔥 Fix date format parsing here
      let regDateObj: Date;

      if (typeof regDateRaw === 'string') {
        // Expecting "dd-mm-yyyy"
        const [day, month, year] = regDateRaw.split('-').map(Number);
        regDateObj = new Date(year, month - 1, day);
      } else {
        regDateObj = new Date(regDateRaw);
      }

      // ✅ Set minVisitDate to next day
      const nextDay = new Date(regDateObj);
      nextDay.setDate(nextDay.getDate() + 1);
      this.minVisitDate = nextDay;
      console.log("minVisitDate set to:", this.minVisitDate);

    } else if (!dobVal) {
      Swal.fire("", "Please Enter Date of Birth", "warning");
    }
  }

  resetHemoColor() {
    // Assuming nutLabInvestR contains the lab tests
    const hemoglobinItem = this.nutLabInvestR.find(item => item.paramDesc === 'Haemoglobin');

    if (hemoglobinItem) {
      // Reset background color for Hemoglobin field
      this.backgroundColorR = '';  // Reset color if you are using this variable for the styling

      // Optionally, reset the result and remarks fields for Hemoglobin if needed
      hemoglobinItem.result = null;
      hemoglobinItem.remarks = null;
    }
  }


  clearAll(PatiClr?) {
    if (PatiClr) {
      this.patientDetails.clear();

    }
    this.resetHemoColor();
    this.page3 = 1;
    this.searchForm.get('nutNo').setValue(null);
    this.submitted = false;
    this.initialsBookingForm();
    this.initialsFollowUpForm();
    this.initialsMainList();
    this.nutLabInvestR = _.cloneDeep(this.nutLabInvest);
    this.nutLabInvestF = _.cloneDeep(this.nutLabInvest);
    this.cFAssessR = _.cloneDeep(this.rCheckListFilterd);
    this.cFAssessF = _.cloneDeep(this.fCheckListFilterd);
    this.nutAssesList = [];
    this.nutVisitsList = [];
    this.selectedFUDate = null;
    this.nutVisitsList = [];
    this.visitsHist = [];
    this.nutKeyMsgList = [];
    this.nutKeyMsgListF = [];
    this.nutAssesListF = [];
  }

  visitsHistList() {


    this.columnVisitHistList = [
      { headerName: 'Visit Date', field: 'visitDate', minWidth: 125, sortable: true, sort: 'asc', cellRenderer: this.cellRenderVisitDate },
      { headerName: 'Weight (kg)', field: 'weight', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'Height/Length (cm)', field: 'height', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'Head Circumference (cm)', field: 'headCircum', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'Visit Type', field: 'visitType', minWidth: 125, sortable: true, sort: 'asc', cellRenderer: this.cellRendererVisitType },
    ];
  }

  onGridReady(params) {
    params.api.sizeColumnsToFit();
  }

  // Open dialgue box 
  openVisitsHistWindow() {
    this.modalService.open(this.viewVisitsHistWindow, { size: <any>'lg' });
    this.visitsHistList();
  }



  cFAssessHistList(type) {

    if (type == 'R') {
      let rCFAssR = this.cFAssessR.filter(el => el.answerYn);
      this.answeredQes = _.cloneDeep(rCFAssR);

    }

    if (type == 'F') {
      let rCFAssF = this.cFAssessF.filter(el => el.answerYn);
      this.answeredQes = _.cloneDeep(rCFAssF);
    }


    this.columncFAssessHistList = [
      { headerName: 'Question', field: 'qstnDesc', minWidth: 500, sortable: true, sort: 'asc' },
      { headerName: 'Answer', field: 'answerYn', minWidth: 50, sortable: true, sort: 'asc', cellRenderer: this.cellRendererAnswerYn }
    ];
  }

  // Open dialgue box 
  opencFAssessHistWindow(visitType) {
    this.answeredQes = [];
    this.modalService.open(this.viewCFAssessHistWindow, { size: <any>'lg' });
    this.cFAssessHistList(visitType);
  }

  intialsZscors(form) {
    form.get('waZscore').setValue(null);
    form.get('waLevel').setValue(null);
    form.get('waColor').setValue(null);


    form.get('haZscore').setValue(null);
    form.get('haLevel').setValue(null);
    form.get('haColor').setValue(null);


    form.get('whZscore').setValue(null);
    form.get('whLevel').setValue(null);
    form.get('whColor').setValue(null);

    form.get('hcaZscore').setValue(null);
    form.get('hcaLevel').setValue(null);
    form.get('hcaColor').setValue(null);
  }


  openGrowthChartWindow(item, data) {

    this.modalService.open(this.viewGrowthChartWindow, { size: <any>'lg' });
    this.callGrothChart(item, data);
  }

  callGrothChart(item?, dataForm?) {
    if (dataForm.ageAtVisit >= 0 && dataForm.ageAtVisit <= 24) {

      let patientDtls = this.patientDetails.patientForm.value;
      let gender = patientDtls.sex;
      let ageMo = dataForm.ageAtVisit
      let ageYr = ageMo / 12
      let zId = item.zId.filter(el => el.gender == gender && el.ageT >= ageYr >= el.ageF)[0].id;
      let xLabel = '';
      let yLabel = '';

      let chartLebel: any = [];
      let sd0: any = [];
      let sd1: any = [];
      let sd2: any = [];
      let sd3: any = [];
      let sd1Neg: any = [];
      let sd2Neg: any = [];
      let sd3Neg: any = [];


      this._ChildNutritionService.getZscoreDatasetByZId(zId).subscribe(res => {
        let data = res.result;

        data.forEach(el => {
          chartLebel.push(el.ageHeight);
          sd0.push(el.sd0);
          sd1.push(el.sd1);
          sd2.push(el.sd2);
          sd3.push(el.sd3);
          sd1Neg.push(el.sd1Neg);
          sd2Neg.push(el.sd2Neg);
          sd3Neg.push(el.sd3Neg);
        })



        chartLebel.push('')

        let maxYA = Math.max(sd3) + 5;


        let leg: any[] = []
        this.growthChartName = item && item.name ? item.name : null;

        let minY = null;
        let maxY = null;
        let minX = null;
        let maxX = null
        let stepSizeY = null;
        let stepSizeX = null;
        let x = null;
        let y = null;
        let purpleArea = false;

        let visitsList = this.nutVisitsList.sort((a, b) => a - b);

        let a = malnutritionDataF[0].level.find(el =>
          el.value === 'N'
        ).item

        let zscoreData = [];



        if ([29, 30, 31, 32].includes(zId)) {
          xLabel = 'Height/Length (cm)';
          yLabel = 'Weight (kg)';
          minY = 5;
          maxY = 24;
          stepSizeY = 2;
          minX = 60
          maxX = 110
          stepSizeX = 5
          purpleArea = true;
          leg = ['Severe Wasting', 'Moderate Wasting', 'Normal', 'Overweight', 'Obese'];

          // Safely retrieve weight and height for the current visit
          y = dataForm && dataForm.weight ? dataForm.weight : null;
          x = dataForm && dataForm.height ? dataForm.height : null;

          // Ensure malnutritionDataF is defined and has the expected structure
          if (malnutritionDataF && malnutritionDataF.length > 0 && malnutritionDataF[0].level) {
            // Clear previous zscoreData to prevent duplicates
            zscoreData = [];

            // Reusable function to find the level name based on a rating
            const findLevelName = (rating) => {
              let level = malnutritionDataF[0].level.find(el1 => el1.value === rating);
              return level ? level.item : null; // Return item if found, otherwise return null
            };

            // Process current visit
            let levelNameR = findLevelName(dataForm.whRating);
            if (levelNameR) {
              zscoreData.push({
                type: 'scatter',
                label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) +
                  ', Zscore: ' + dataForm.whZscore +
                  ', Level: ' + levelNameR +
                  ', Weight: ',
                data: [{ x: x, y: y }],
                fill: true,
                backgroundColor: '#fffff'
              });
            } else {
              console.warn('Level for the current dataForm rating not found');
            }

            // Process previous visits but filter out the current visit from the visitsList
            visitsList
              .filter(el => el.visitDate !== dataForm.visitDate)  // Exclude current visit
              .forEach(el => {
                let levelName = findLevelName(el.whRating);
                if (levelName) {
                  zscoreData.push({
                    type: 'scatter',
                    label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                      ', Zscore: ' + el.whZscore +
                      ', Level: ' + levelName +
                      ', Weight: ',
                    data: [{ x: el.height, y: el.weight }],
                    fill: true,
                    backgroundColor: '#fffff'
                  });
                } else {
                  console.warn('Level for the visit data not found for visit date: ' + el.visitDate);
                }
              });
          } else {
            console.error('Malnutrition data is not available or improperly formatted');
          }
        }





        else if ([25, 26].includes(zId)) {
          xLabel = 'Age (Months)';
          yLabel = 'Height/Length (cm)';
          minY = 40;
          maxY = 125;
          stepSizeY = 5;
          purpleArea = true;
          leg = ['Severe Stunting', 'Moderate Stunting', 'Normal', 'Moderate Giant', 'Severely Giant'];

          // Safely retrieve age and height for the current data
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.height ? dataForm.height : null;

          if (malnutritionDataF && malnutritionDataF.length > 1 && malnutritionDataF[1].level) {
            // Process current visit data
            if (x !== null && y !== null) {
              let levelNameR = malnutritionDataF[1].level.find(el1 => el1.value === dataForm.haRating);
              if (levelNameR) {
                zscoreData.push({
                  type: 'scatter',
                  label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) +
                    ' Zscore: ' + dataForm.haZscore +
                    ' Level: ' + levelNameR.item +
                    ' Height: ',
                  data: [{ x: x, y: y }],
                  fill: true,
                  backgroundColor: '#fffff'
                });
              } else {
                console.warn('Level for the current dataForm HA rating not found');
              }
            } else {
              console.warn('Missing age or height data for the current visit');
            }

            // Safely process each visit in visitsList
            if (visitsList && visitsList.length > 0) {
              visitsList.forEach((el, index) => {
                let ageAtVisit = el.ageAtVisit || null;
                let height = el.height || null;
                let haRating = el.haRating || null;

                // Log each visit being processed
                console.log(`Processing visit ${index + 1} - Age: ${ageAtVisit}, Height: ${height}, HA Rating: ${haRating}`);

                if (ageAtVisit !== null && height !== null) {
                  // Check if haRating is available, and if not, handle it appropriately
                  if (haRating === null) {
                    console.warn(`HA Rating is missing for visit ${index + 1}. Skipping this visit.`);
                  } else {
                    let levelName = malnutritionDataF[1].level.find(el1 => el1.value === haRating);
                    if (levelName) {
                      zscoreData.push({
                        type: 'scatter',
                        label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                          ', Zscore: ' + el.haZscore +
                          ', Level: ' + levelName.item +
                          ', Height: ',
                        data: [{ x: ageAtVisit, y: height }],
                        fill: true,
                        backgroundColor: '#fffff'
                      });
                    } else {
                      console.warn('Level for the HA rating not found for visit date: ' + el.visitDate);
                    }
                  }
                } else {
                  console.warn('Incomplete data for visit: ' + (index + 1) +
                    ' (ageAtVisit: ' + ageAtVisit + ', height: ' + height + ')');
                }
              });
            } else {
              console.warn('visitsList is empty or not defined');
            }
          } else {
            console.error('Malnutrition data is not available or improperly formatted');
          }
        } else if ([27, 28].includes(zId)) {
          xLabel = 'Age (Months)';
          yLabel = 'Weight (kg)';
          minY = 1;
          maxY = 17;
          stepSizeY = 1;
          minX = 0;
          maxX = 24;
          stepSizeX = 3;
          purpleArea = true;
          leg = ['Severe Underweight', 'Moderate Underweight', 'Normal', 'Overweight'];

          // Safely retrieve age and weight for current data
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.weight ? dataForm.weight : null;

          // Check if malnutritionDataF[2] and its level property exist
          if (malnutritionDataF && malnutritionDataF.length > 2 && malnutritionDataF[2].level) {
            // Find level for the current dataForm's waRating
            let levelNameR = malnutritionDataF[2].level.find(el1 => el1.value === dataForm.waRating);

            // Check if levelNameR exists before accessing item property
            if (levelNameR) {
              zscoreData.push({
                type: 'scatter',
                label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) +
                  ', Zscore: ' + dataForm.waZscore +
                  ', Level: ' + levelNameR.item +  // Access item if levelNameR exists
                  ', Weight: ',
                data: [{ x: x, y: y }],
                fill: true,
                backgroundColor: '#fffff'
              });
            } else {
              console.warn('Level for the current dataForm WA rating not found');
            }

            // Safely process each visit in visitsList
            visitsList.forEach(el => {
              // Safeguard against missing or undefined age, weight, or waRating in the visit
              let ageAtVisit = el.ageAtVisit || null;
              let weight = el.weight || null;
              let waRating = el.waRating || null;

              // Log each visit being processed
              console.log(`Processing visit - Age: ${ageAtVisit}, Weight: ${weight}, WA Rating: ${waRating}`);

              // Ensure age and weight are present, and handle waRating if it's null
              if (ageAtVisit !== null && weight !== null) {
                if (waRating !== null) {
                  // Find level for the waRating of each visit
                  let levelName = malnutritionDataF[2].level.find(el1 => el1.value === waRating);

                  if (levelName) {
                    zscoreData.push({
                      type: 'scatter',
                      label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                        ', Zscore: ' + el.waZscore +
                        ', Level: ' + levelName.item +
                        ', Weight: ',
                      data: [{ x: ageAtVisit, y: weight }],
                      fill: true,
                      backgroundColor: '#fffff'
                    });
                  } else {
                    console.warn('Level for the WA rating not found for visit date: ' + el.visitDate);
                  }
                } else {
                  console.warn(`WA Rating is missing for visit date: ${el.visitDate}. Proceeding with age and weight only.`);

                  // You can still add the visit data without the rating if necessary
                  zscoreData.push({
                    type: 'scatter',
                    label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                      ', Weight: ' + weight,
                    data: [{ x: ageAtVisit, y: weight }],
                    fill: true,
                    backgroundColor: '#fffff'
                  });
                }
              } else {
                console.warn('Incomplete data for visit date: ' + el.visitDate +
                  ' (ageAtVisit: ' + ageAtVisit + ', weight: ' + weight + ')');
              }
            });
          } else {
            console.error('Malnutrition data is not available or improperly formatted');
          }
        }




        else if ([35, 36].includes(zId)) {
          xLabel = 'Age (Months)'
          yLabel = 'Head Circumference (cm)'
          minY = 30
          maxY = 54
          stepSizeY = 1
          // minX = 0
          // maxX = 24
          // stepSizeX = 2
          purpleArea = true;
          leg = ['Macrocephaly', 'Microcephaly']
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.headCircum ? dataForm.headCircum : null;
          let levelNameR;
          levelNameR = malnutritionDataF[3].level.find(el1 =>
            el1.value === dataForm.hcaRating
          )
          levelNameR = levelNameR && levelNameR.item ? levelNameR.item : null;
          zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) + ', Zscore: ' + dataForm.hcaZscore + ', Level: ' + levelNameR + ', Head Circumference: ', data: [{ x: x, y: y }], fill: true, backgroundColor: '#fffff' });
          visitsList.forEach(el => {
            let levelName;
            levelName = malnutritionDataF[3].level.find(el1 =>
              el1.value === el.hcaRating
            )
            levelName = levelName && levelName.item ? levelName.item : null;
            zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(el.visitDate) + ', Zscore: ' + el.hcaZscore + ', Level: ' + levelName + ', Head Circumference: ', data: [{ x: el.ageAtVisit, y: el.headCircum }], fill: true, backgroundColor: '#fffff' });
          })
        }

        this.options = {
          responsive: true,
          textBorder: {
            enabled: true, // Enable custom plugin
            borderWidth: 3, // Width of the border
            borderColor: '#000000' // Color of the border
          },

          legend: {
            display: true,
            labels: {
              color: '#000',
              font: {
                size: 14
              },
              filter: (legendItem) => {
                return legendItem && legendItem.text && !legendItem.text.includes('Visit Date:') && legendItem.text !== 'a'; // Remove the 'ad' label from the legend
              }
            }
          },
          stacked: false,
          maintainAspectRatio: false,
          aspectRatio: .8,
          scales: {
            xAxes: [{
              gridLines: {
                display: true,
                color: 'rgba(0,0,0,0.1)', // Color of the grid lines
                z: 1 // Set a higher z-index for grid lines
              },
              scaleLabel: {
                display: true,
                labelString: xLabel
              },
              ticks: {
                beginAtZero: true,
                color: '#000000',
                min: minX,
                max: maxX,
                stepSize: stepSizeX,
                maxTicksLimit: 24  // Limit the number of ticks to prevent crowding
              },
              grid: {
                color: '#000000',
                lineWidth: 3

              },
              border: {
                display: true,
                width: 3
              }

            }],
            yAxes: [
              {
                gridLines: {
                  display: true,
                  color: 'rgba(0,0,0,0.1)', // Color of the grid lines
                  z: 1 // Set a higher z-index for grid lines
                },
                id: 'y-axis-1',
                type: 'linear',
                position: 'left',
                scaleLabel: {
                  display: true,
                  labelString: yLabel
                },
                grid: {
                  color: '#070808',
                  offset: true
                },
                border: {
                  display: true,
                  width: 4
                },
                ticks: {
                  min: minY,
                  max: maxY,
                  stepSize: stepSizeY
                },

              }, {
                title: {
                  display: true,
                  text: 'MM'
                },
                id: 'y-axis-2',
                type: 'linear',
                text: "dd",
                position: 'right',
                grid: {
                  color: '#070808',
                  offset: true
                },
                border: {
                  display: true,
                  width: 4
                },
                ticks: {
                  min: minY,
                  max: maxY,
                  stepSize: stepSizeY
                }
              }],
          }
        };



        this.data = {
          labels: chartLebel,
          datasets: [

            {
              label: leg[0],
              data: sd3Neg,
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(237, 28, 36)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '-3'
            },
            {
              label: leg[1],
              data: sd2Neg,
              fill: true,
              borderColor: '#fc0303',
              backgroundColor: 'rgb(237, 125, 49)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '-2'
            },
            {
              label: leg[2],
              data: sd0,
              fill: true,
              borderColor: '#068000',
              backgroundColor: 'rgb(34, 177, 76)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '0'
            },
            {
              label: 'a',
              data: sd2,
              borderColor: '#fc0303',
              backgroundColor: 'rgb(34, 177, 76)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '2'
            },
            {
              label: leg[3],
              data: sd3,
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(255, 217, 102)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '3'
            }


          ]
        };


        zscoreData.forEach(el => {
          this.data.datasets.push({ font: '16px', color: 'black', ...el })
        })

        if (purpleArea) {
          this.data.datasets.push(
            {
              label: leg[4] != null ? leg[4] : null,
              data: new Array(sd3.length).fill(Math.max(...sd3)),
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(175, 141, 164)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: ''
              //yAxisID: 'y6'
              // tension: .2,
            }
          )
        }

        this.data.datasets.forEach(el => {
          if (el && !el.type) {

            let y = Math.max(...el.data);

            let x = Math.max(...chartLebel);

            let textColor = el.borderColor;

            let text = el.textValue;

            this.addCustomTextPlugin(text, textColor, x, y);

          }

        }
        );





      });
    }

    else if (dataForm.ageAtVisit >= 25 && dataForm.ageAtVisit <= 60) {

      let patientDtls = this.patientDetails.patientForm.value;
      let gender = patientDtls.sex;
      let ageMo = dataForm.ageAtVisit
      let ageYr = ageMo / 12
      let zId = item.zId.filter(el => el.gender == gender && el.ageT >= ageYr >= el.ageF)[0].id;
      let xLabel = '';
      let yLabel = '';

      let chartLebel: any = [];
      let sd0: any = [];
      let sd1: any = [];
      let sd2: any = [];
      let sd3: any = [];
      let sd1Neg: any = [];
      let sd2Neg: any = [];
      let sd3Neg: any = [];


      this._ChildNutritionService.getZscoreDatasetByZId(zId).subscribe(res => {
        let data = res.result;

        data.forEach(el => {
          chartLebel.push(el.ageHeight);
          sd0.push(el.sd0);
          sd1.push(el.sd1);
          sd2.push(el.sd2);
          sd3.push(el.sd3);
          sd1Neg.push(el.sd1Neg);
          sd2Neg.push(el.sd2Neg);
          sd3Neg.push(el.sd3Neg);
        })

        chartLebel.push('')

        let maxYA = Math.max(sd3) + 5;


        let leg: any[] = []
        this.growthChartName = item && item.name ? item.name : null;

        let minY = null;
        let maxY = null;
        let minX = null;
        let maxX = null
        let stepSizeY = null;
        let stepSizeX = null;
        let x = null;
        let y = null;
        let purpleArea = false;

        let visitsList = this.nutVisitsList.sort((a, b) => a - b);

        let a = malnutritionDataF[0].level.find(el =>
          el.value === 'N'
        ).item

        let zscoreData = [];

        if ([29, 30, 31, 32].includes(zId)) {
          xLabel = 'Height/Length (cm)';
          yLabel = 'Weight (kg)';
          minY = 4;
          maxY = 26;
          stepSizeY = 2;
          minX = 65
          maxX = 120
          stepSizeX = 5;
          purpleArea = true
          leg = ['Severe Wasting', 'Moderate Wasting', 'Normal', 'Overweight', 'Obese'];

          // Safely retrieve weight and height for the current visit
          y = dataForm && dataForm.weight ? dataForm.weight : null;
          x = dataForm && dataForm.height ? dataForm.height : null;

          // Ensure malnutritionDataF is defined and has the expected structure
          if (malnutritionDataF && malnutritionDataF.length > 0 && malnutritionDataF[0].level) {
            // Reusable function to find the level name based on a rating
            const findLevelName = (rating) => {
              let level = malnutritionDataF[0].level.find(el1 => el1.value === rating);
              return level ? level.item : null; // Return item if found, otherwise return null
            };

            // Process current visit
            let levelNameR = findLevelName(dataForm.whRating);
            if (levelNameR) {
              zscoreData.push({
                type: 'scatter',
                label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) +
                  ', Zscore: ' + dataForm.whZscore +
                  ', Level: ' + levelNameR +
                  ', Weight: ',
                data: [{ x: x, y: y }],
                fill: true,
                backgroundColor: '#fffff'
              });
            } else {
              console.warn('Level for the current dataForm rating not found');
            }

            // Process previous visits but filter out the current visit from the visitsList
            visitsList
              .filter(el => el.visitDate !== dataForm.visitDate)  // Exclude current visit
              .forEach(el => {
                let levelName = findLevelName(el.whRating);
                if (levelName) {
                  zscoreData.push({
                    type: 'scatter',
                    label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                      ', Zscore: ' + el.whZscore +
                      ', Level: ' + levelName +
                      ', Weight: ',
                    data: [{ x: el.height, y: el.weight }],
                    fill: true,
                    backgroundColor: '#fffff'
                  });
                } else {
                  console.warn('Level for the visit data not found for visit date: ' + el.visitDate);
                }
              });
          } else {
            console.error('Malnutrition data is not available or improperly formatted');
          }
        }

        else if ([25, 26].includes(zId)) {
          xLabel = 'Age (Months)'
          yLabel = 'Height/Length (cm)'
          minY = 40
          maxY = 125
          stepSizeY = 5
          purpleArea = true
          leg = ['Severe Stunting', 'Moderate Stunting', 'Normal', 'Moderate Giant', 'Severely Giant']
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.height ? dataForm.height : null;
          let levelNameR = malnutritionDataF[1].level.find(el1 =>
            el1.value === dataForm.haRating
          ).item;
          zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) + ' Zscore: ' + dataForm.haZscore + ' Level: ' + levelNameR + ' Height: ', data: [{ x: x, y: y }], fill: true, backgroundColor: '#fffff' });
          visitsList.forEach(el => {
            let levelName = malnutritionDataF[1].level.find(el1 =>
              el1.value === el.haRating
            ).item;
            zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(el.visitDate) + ', Zscore: ' + el.haZscore + ', Level: ' + levelName + ', Height: ', data: [{ x: el.ageAtVisit, y: el.height }], fill: true, backgroundColor: '#fffff' });
          })

        }

        else if ([27, 28].includes(zId)) {
          xLabel = 'Age (Months)';
          yLabel = 'Weight (kg)';
          minY = 7;
          maxY = 29;
          stepSizeY = 1;
          minX = 25;
          maxX = 60;
          stepSizeX = 3;
          purpleArea = true;
          leg = ['Severe Underweight', 'Moderate Underweight', 'Normal', 'Overweight'];

          // Safely retrieve age and weight for current data
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.weight ? dataForm.weight : null;

          // Check if malnutritionDataF[2] and its level property exist
          if (malnutritionDataF && malnutritionDataF.length > 2 && malnutritionDataF[2].level) {
            // Find level for the current dataForm's waRating
            let levelNameR = malnutritionDataF[2].level.find(el1 => el1.value === dataForm.waRating);

            // Check if levelNameR exists before accessing item property
            if (levelNameR) {
              zscoreData.push({
                type: 'scatter',
                label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) +
                  ', Zscore: ' + dataForm.waZscore +
                  ', Level: ' + levelNameR.item +  // Access item if levelNameR exists
                  ', Weight: ',
                data: [{ x: x, y: y }],
                fill: true,
                backgroundColor: '#fffff'
              });
            } else {
              console.warn('Level for the current dataForm WA rating not found');
            }

            // Safely process each visit in visitsList
            visitsList.forEach(el => {
              // Safeguard against missing or undefined age, weight, or waRating in the visit
              let ageAtVisit = el.ageAtVisit || null;
              let weight = el.weight || null;
              let waRating = el.waRating || null;

              // Log each visit being processed
              console.log(`Processing visit - Age: ${ageAtVisit}, Weight: ${weight}, WA Rating: ${waRating}`);

              // Ensure age and weight are present, and handle waRating if it's null
              if (ageAtVisit !== null && weight !== null) {
                if (waRating !== null) {
                  // Find level for the waRating of each visit
                  let levelName = malnutritionDataF[2].level.find(el1 => el1.value === waRating);

                  if (levelName) {
                    zscoreData.push({
                      type: 'scatter',
                      label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                        ', Zscore: ' + el.waZscore +
                        ', Level: ' + levelName.item +
                        ', Weight: ',
                      data: [{ x: ageAtVisit, y: weight }],
                      fill: true,
                      backgroundColor: '#fffff'
                    });
                  } else {
                    console.warn('Level for the WA rating not found for visit date: ' + el.visitDate);
                  }
                } else {
                  console.warn(`WA Rating is missing for visit date: ${el.visitDate}. Proceeding with age and weight only.`);

                  // You can still add the visit data without the rating if necessary
                  zscoreData.push({
                    type: 'scatter',
                    label: 'Visit Date: ' + this.cellRenderer(el.visitDate) +
                      ', Weight: ' + weight,
                    data: [{ x: ageAtVisit, y: weight }],
                    fill: true,
                    backgroundColor: '#fffff'
                  });
                }
              } else {
                console.warn('Incomplete data for visit date: ' + el.visitDate +
                  ' (ageAtVisit: ' + ageAtVisit + ', weight: ' + weight + ')');
              }
            });
          } else {
            console.error('Malnutrition data is not available or improperly formatted');
          }
        }
        else if ([35, 36].includes(zId)) {
          xLabel = 'Age (Months)'
          yLabel = 'Head Circumference (cm)'
          minY = 29
          maxY = 54
          stepSizeY = 1
          // minX = 0
          // maxX = 59
          // stepSizeX = 2
          purpleArea = true;
          leg = ['Macrocephaly', 'Microcephaly']
          x = dataForm && dataForm.ageAtVisit ? dataForm.ageAtVisit : null;
          y = dataForm && dataForm.headCircum ? dataForm.headCircum : null;
          let levelNameR;
          levelNameR = malnutritionDataF[3].level.find(el1 =>
            el1.value === dataForm.hcaRating
          )
          levelNameR = levelNameR && levelNameR.item ? levelNameR.item : null;
          zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(dataForm.regDate) + ', Zscore: ' + dataForm.hcaZscore + ', Level: ' + levelNameR + ', Head Circumference: ', data: [{ x: x, y: y }], fill: true, backgroundColor: '#fffff' });
          visitsList.forEach(el => {
            let levelName;
            levelName = malnutritionDataF[3].level.find(el1 =>
              el1.value === el.hcaRating
            )
            levelName = levelName && levelName.item ? levelName.item : null;
            zscoreData.push({ type: 'scatter', label: 'Visit Date: ' + this.cellRenderer(el.visitDate) + ', Zscore: ' + el.hcaZscore + ', Level: ' + levelName + ', Head Circumference: ', data: [{ x: el.ageAtVisit, y: el.headCircum }], fill: true, backgroundColor: '#fffff' });
          })
        }


        this.options = {
          responsive: true,
          textBorder: {
            enabled: true, // Enable custom plugin
            borderWidth: 3, // Width of the border
            borderColor: '#000000' // Color of the border
          },

          legend: {
            display: true,
            labels: {
              color: '#000',
              font: {
                size: 14
              },
              filter: (legendItem) => {
                return legendItem && legendItem.text && !legendItem.text.includes('Visit Date:') && legendItem.text !== 'a'; // Remove the 'ad' label from the legend
              }
            }
          },
          stacked: false,
          maintainAspectRatio: false,
          aspectRatio: .8,
          scales: {
            xAxes: [{
              gridLines: {
                display: true,
                color: 'rgba(0,0,0,0.1)', // Color of the grid lines
                z: 1 // Set a higher z-index for grid lines
              },
              scaleLabel: {
                display: true,
                labelString: xLabel
              },
              ticks: {
                beginAtZero: true,
                color: '#000000',
                min: minX,
                max: maxX,
                stepSize: stepSizeX,
                maxTicksLimit: 24  // Limit the number of ticks to prevent crowding
              },
              grid: {
                color: '#000000',
                lineWidth: 3

              },
              border: {
                display: true,
                width: 3
              }

            }],
            yAxes: [
              {
                gridLines: {
                  display: true,
                  color: 'rgba(0,0,0,0.1)', // Color of the grid lines
                  z: 1 // Set a higher z-index for grid lines
                },
                id: 'y-axis-1',
                type: 'linear',
                position: 'left',
                scaleLabel: {
                  display: true,
                  labelString: yLabel
                },
                grid: {
                  color: '#070808',
                  offset: true
                },
                border: {
                  display: true,
                  width: 4
                },
                ticks: {
                  min: minY,
                  max: maxY,
                  stepSize: stepSizeY
                },

              }, {
                title: {
                  display: true,
                  text: 'MM'
                },
                id: 'y-axis-2',
                type: 'linear',
                text: "dd",
                position: 'right',
                grid: {
                  color: '#070808',
                  offset: true
                },
                border: {
                  display: true,
                  width: 4
                },
                ticks: {
                  min: minY,
                  max: maxY,
                  stepSize: stepSizeY
                }
              }],
          }
        };




        this.data = {
          labels: chartLebel,
          datasets: [

            {
              label: leg[0],
              data: sd3Neg,
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(237, 28, 36)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '-3'
            },
            {
              label: leg[1],
              data: sd2Neg,
              fill: true,
              borderColor: '#fc0303',
              backgroundColor: 'rgb(237, 125, 49)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '-2'
            },
            {
              label: leg[2],
              data: sd0,
              fill: true,
              borderColor: '#068000',
              backgroundColor: 'rgb(34, 177, 76)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '0'
            },
            {
              label: 'a',
              data: sd2,
              borderColor: '#fc0303',
              backgroundColor: 'rgb(34, 177, 76)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '2'
            },
            {
              label: leg[3],
              data: sd3,
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(255, 217, 102)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: '3'
            }


          ]
        };


        zscoreData.forEach(el => {
          this.data.datasets.push({ font: '16px', color: 'black', ...el })
        })

        if (purpleArea) {
          this.data.datasets.push(
            {
              label: leg[4] != null ? leg[4] : null,
              data: new Array(sd3.length).fill(Math.max(...sd3)),
              fill: true,
              borderColor: '#000000',
              backgroundColor: 'rgb(175, 141, 164)',
              borderWidth: 1,
              pointRadius: 0,
              textValue: ''
              //yAxisID: 'y6'
              // tension: .2,
            }
          )
        }

        this.data.datasets.forEach(el => {
          if (el && !el.type) {

            let y = Math.max(...el.data);

            let x = Math.max(...chartLebel);

            let textColor = el.borderColor;

            let text = el.textValue;

            this.addCustomTextPlugin(text, textColor, x, y);

          }

        }
        );





      });
    }
    else {

      Swal.fire({
        title: 'Sorry !!',
        text: 'Child More Than 5 Years',
        icon: 'warning',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
      }).then((result) => {
        if (result.isConfirmed) {
          this.submitForm(true);
        }
      });

    }

  }

  addCustomTextPlugin(text, color, x, y, yAxisId = 'y-axis-1') {
    (window as any).Chart.plugins.register({
      id: 'customTextPlugin',
      afterDraw: function (chart) {
        const ctx = chart.ctx;

        // Get x-axis and specified y-axis scale
        const xScale = chart.scales['x-axis-0']; // Access x-axis scale
        const yScale = chart.scales[yAxisId]; // Use specified y-axis ID

        if (!xScale || !yScale) {
          console.warn('Scales not defined yet. Custom text will not be drawn.');
          return; // Exit if scales are not defined
        }

        // Set font properties for the text
        ctx.font = '16px Arial'; // Set font size and family
        ctx.fillStyle = color; // Set text color
        ctx.textAlign = 'right'; // Align text horizontally
        ctx.textBaseline = 'middle'; // Align text vertically

        const xPos = xScale.getPixelForValue(x); // x position
        const yPos = yScale.getPixelForValue(y); // y position

        // Draw the custom text at the specified (x, y) position
        ctx.fillText(text, xPos + 15, yPos);
      }
    });
  }



  selectTabR(tab: number): void {
    this.selectedTabR = tab;
  }


  getChartDataUrl(chart: any) {
    const canvas = chart.chart.canvas; // Access the underlying canvas element
    if (canvas) {
      const dataURL = canvas.toDataURL(); // Get the data URL

      this.printImage(dataURL);


    } else {
      console.error("Canvas not found!");
    }
  }

  printImage(dataURL: string) {
    const printWindow = window.open('Growth Chart', '_new', 'scrollbars=no,menubar=no,toolbar=no,location=no,status=no');
    if (printWindow) {
      printWindow.document.open();
      printWindow.document.write(`
          <html>
            <head>
              <title>${this.growthChartName}</title>
            </head>
            <body>
              <img id="printImage" src="${dataURL}" />
              <script>
                window.onload = function() {
                  setTimeout(() => {
                    window.print();
                    window.close();
                  }, 500); // Adjust the delay as needed
                }
              </script>
            </body>
          </html>
        `);
      printWindow.document.close(); // Close the document for rendering
    } else {
      console.error("Failed to open print window.");
    }
  }







  printVisitSummary(event: any, item) {

    let NameForm = this.patientDetails.patientForm

    let childName = NameForm.get('firstName').value + ' ' + NameForm.get('secondName').value + ' ' + NameForm.get('thirdName').value + ' ' + NameForm.get('tribe').value;

    let lact = '';
    item && item.rgTbNutLactation ? item.rgTbNutLactation.forEach(el => {
      lact = lact + ` <tr>
        <td>${this.getLactionNameCellRender(el.paramId)}</td>
        <td>${el.remarks}</td>
      </tr>`
    }) : null;


    let rCFAssForPrint = this.cFAssessF.filter(el => el.answerYn);
    let answeredQesForPrint = _.cloneDeep(rCFAssForPrint);
    let cildNutAss = ''
    answeredQesForPrint && answeredQesForPrint.length ? answeredQesForPrint.forEach(el => {
      cildNutAss = cildNutAss + ` <tr>
        <td>${el.qstnDesc}</td>
          <td>${this.cellRendererAnswerYn(el.answerYn)}</td>
        </tr>`
    }) : null;

    let keyMessagesForPrint = '';
    item && item.rgTbNutKeyMessages ? item.rgTbNutKeyMessages.forEach(el => {
      keyMessagesForPrint = keyMessagesForPrint + ` <tr>
        <td>${this.getKeyMessageNameCellRender(el.keyId)}</td>
        <td>${el.remarks}</td>
      </tr>`
    }) : null;

    let labInvestForPrint = '';
    item && item.rgTbNutLabInvsts ? item.rgTbNutLabInvsts.forEach(el => {
      labInvestForPrint = labInvestForPrint + ` <tr>
        <td>${this.getLabTestNameCellRender(el.testId)}</td>
        <td>${el.result}</td>
        <td>${el.remarks}</td>
      </tr>`
    }) : null;

    const printContents = `           
            <div class="header">
    <h3> Child Summary Visits </h3>            
  </div>
  
  <div class="section">  
    <h4>Child Details</h4>        
    <ul class="cell">
      <li><span class="lbl">Registration No: </span> ${this.patientDetails.patientForm.get('centralRegNo').value}</li>
      <li><span class="lbl">Name: </span> ${childName}</li>
      <li><span class="lbl">Gender: </span>${this.getGenderCellRender(this.patientDetails.patientForm.get('sex').value)}</li>
      <li><span class="lbl">DOB: </span> ${this.cellRenderer(this.patientDetails.patientForm.get('dob').value)}</li>
      <li><span class="lbl">Civil Id: </span> ${this.patientDetails.patientForm.get('civilId').value}</li>   
    </ul>
    <ul class="cell">                                  
      <li><span class="lbl">Registration Institute: </span>${this.getInstituteCellRender(this.patientDetails.patientForm.get('regInst').value)}</li>
    </ul>
  </div>
  
  <div class="section">
    <h4>Follow Up Details</h4>
      <ul class="cell">
            <li><span class="lbl">Visit Date:</span> ${item && item.visitDate ? this.cellRenderer(item.visitDate) : null}
            <li><span class="lbl">FollowUp Institutes:</span> ${item && item.visitInst ? this.getInstituteCellRender(item.visitInst) : null}</li>
            <li><span class="lbl">Age (Months):</span> ${item && item.ageAtVisit ? item.ageAtVisit : null}</li>
           
      </ul>     

      <ul class="cell">
      <li><span class="lbl">Weight (kg):</span> ${item && item.weight ? item.weight : null}</li>
      <li><span class="lbl">Height/Length (cm):</span> ${item && item.height ? item.height : null}</li>
      <li><span class="lbl">Head Circumference (cm):</span> ${item && item.headCircum ? item.headCircum : null}</li>
      </ul>  

      <ul class="cell">
          <table>
          <thead>
            <tr>
              <th>Malnutrition (SD)</th>
             <th></th>
              <th>Z score</th>     
              <th></th>
              <th>Malnutrition Level</th>                 
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Weight for Height/Length</td>
            <td></td>
              <td>${item.whZscore}</td>
              <td></td>
              <td>${this.getWhRatingCellRender(item.whRating)}</td>
            </tr>

            <tr>
            <td>Height/Length for Age	</td>
          <td></td>
            <td>${item.haZscore}</td>
            <td></td>
            <td>${this.getHaRatingCellRender(item.haRating)}</td>
          </tr>

          <tr>
          <td>Weight for Age	</td>
        <td></td>
          <td>${item.waZscore}</td>
          <td></td>
          <td>${this.getWaRatingCellRender(item.waRating)}</td>
        </tr>

        <tr>
        <td>Head Circumference for Age	</td>
      <td></td>
        <td>${item.hcaZscore}</td>
        <td></td>
        <td>${this.getHcaRatingCellRender(item.hcaRating)}</td>
      </tr>
          </tbody>
        </table>  
    </ul>

    <ul class="cell">
    <li><span class="lbl">Health Status:</span> ${item && item.healthStatus ? item.healthStatus : null}</li>
    <li><span class="lbl">Diarrhea:</span> ${item && item.diarrheaYn ? this.cellRendererAnswerYn(item.diarrheaYn) : null}</li>
    <li><span class="lbl">Oedema:</span> ${item && item.oedemaYn ? this.cellRendererAnswerYn(item.oedemaYn) : null}</li>
    ${item.otherDiseases ? `<li><span class="lbl">Other Disease:</span> ${item && item.otherDiseases ? item.otherDiseases : null}</li>` : ``}
    </ul>  
    </div>
      <div class="section">
        <h4>Lactation Assessment/Counselling</h4>
        <table>
        <thead>
        <tr>
          <th>Assesssment</th>
          <th>Remarks</th>                 
        </tr>
      </thead>
      <tbody>
      ${lact}   
      </tbody>
        </table>            
      </div>

      <div class="section">
      <h4>Child Nutrition Assessment ${this.periodRangeNameF}</h4>
      <table>
      <thead>
      <tr>
        <th>Question</th>
        <th>Answer</th>                 
      </tr>
    </thead>
    <tbody>
    ${cildNutAss}   
    </tbody>
      </table>      
      ${item.assessmentRemarks ? `<li><span class="lbl">Assessment Remark:</span> ${item && item.otherDiseases ? item.otherDiseases : null}</li>` : ``}      
    </div>

    <ul class="cell">
    <li><span class="lbl">Specify action if any:</span> ${item && item.status ? this.getSpecifyStatusNameCellRender(item.status) : null}</li>
    </ul> 
    
      <div class="section">
      <h4>Counselling/Key Messages
      </h4>
      <table>
      <thead>
      <tr>
        <th>Key Message</th>
        <th>Remarks</th>                 
      </tr>
    </thead>
    <tbody>
    ${keyMessagesForPrint}   
    </tbody>
      </table>            
    </div>

    <div class="section">
    <h4>Lab
    </h4>
    <table>
    <thead>
    <tr>
      <th>Test Name</th>
      <th>Test Result</th>    
      <th>Remark</th             
    </tr>
  </thead>
  <tbody>
  ${labInvestForPrint}   
  </tbody>
    </table>            
  </div>
  
          `;

    // Open a new window
    let height = 700;
    let width = 900;
    var left = (screen.width - width) / 2;
    //
    const printWindow = window.open(
      '',
      '',
      'height=' + height + ',width=' + width + ',left=' + left + ',top=150'
    );

    if (printWindow) {
      // Write the content to the new window
      printWindow.document.write(`
                  <html>
                  <head>
                      <title>Report</title>
                      <style>
                      /* Add your styles here */
                      @media print {
                          @page { margin: 0; }
                          body {-webkit-print-color-adjust: exact;}
                      }
                      .cell{ display:table}
                      .cell li{ display: table-cell; padding-right: 20px;border-left: 1px solid #eaeaea;padding-left: 10px;}
                      .cell li .lbl{display:block; padding-bottom: 5px;}
                      body {font-family: Arial, sans-serif; padding:20px; margin-top:20px}
                      .logo{ height:80px}
                      .txt-right{text-align:right}
                      .header{padding: 10px;margin-bottom:30px;background:#ededed;}
                      h3{ text-align: center; margin-bottom: 10px;}
                      h4{margin: 10px;padding: 0 0 10px 0; border-bottom: 1px solid #e5e5e5;}
                      .split{ width:49%; display: inline-block; vertical-align: top}                    
                      ul{list-style:none;margin:0;padding: 10px; border-radius: 5px;}
                      ul li{ margin-bottom:10px; font-size:14px}
                      .lbl{min-width:180px; display:inline-block; color:#999}
                      table{ width:100%;  border-collapse:collapse; margin:10px }
                      thead th{ background-color: #f9f9f9; print-color-adjust: exact;}
                      td, th{ padding:5px; text-align:left; font-size:14px }
                      td{border-bottom:1px solid #f1f1f1}
                      .section{margin-bottom:20px}
                      </style>
                  </head>
  
                  <body>
                      ${printContents}
                  </body>
  
                  </html>
              `);

      printWindow.document.close();
      printWindow.focus();

      printWindow.onload = function () {
        printWindow.print();
      };
    }
  }




}
