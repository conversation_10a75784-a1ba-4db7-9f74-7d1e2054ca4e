import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormControlName, FormGroup, Validators } from '@angular/forms';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { GridNgSelectDataComponent } from '../../../common/agGridComponents/grid-ngSelect-data.component';
import { IcdDataModel } from '../../../common/objectModels/icd-data-model';
import { Parameter2 } from '../../../common/objectModels/parameter-2-model';
import { MasterService } from '../../../_services/master.service';
import Swal from 'sweetalert2';
import { HttpClient, HttpParams } from '@angular/common/http';
import * as AppUtils from '../../../common/app.utils';
import { ViewChild } from '@angular/core';
import { PatientDetailsComponent } from '../../../_comments/patient-details/patient-details.component';
import { RenalRegistryFrom } from '../../../common/objectModels/rena-registry-model';
import { DecimalPipe } from '@angular/common';
import { Diagnosis } from '../../../common/objectModels/diagnosis-model';
import { ICDList } from '../../../common/objectModels/icdList-models';
import { ICDRenalShortList } from '../../../common/objectModels/icdRenalShortList-models';
import * as CommonConstants from '../../../_helpers/common.constants';
import * as AppParams from '../../../_helpers/app-param.constants';
import { TbVitalSigns } from '../../../common/objectModels/vital-signs-model';
import { formatDate } from '@angular/common';
import * as GridUtils from '../../../common/agGridComponents/app.grid-spec-utils';
import * as moment from 'moment';
import { RenalService } from '../../renal.service';
import { ToastrService } from 'ngx-toastr';
import { ButtonRendererComponent } from '../../../common/agGridComponents/ButtonRendererComponent';
import { notEqual } from 'assert';
import { SharedService } from '../../../_services/shared.service';
import { Router } from '@angular/router';
import * as AppCompUtils from '../../../common/app.component-utils';
import { AppComponent } from '../../../app.component';
import { invalid } from '@angular/compiler/src/render3/view/util';
import { LoginService } from 'src/app/login/login.service';
import { TbBrainDeathReg } from 'src/app/_models/brain-death-determination-reg-model';
import { TbBrainDeathExamTran } from 'src/app/_models/brain-death-determination-tran-model';
import { data } from 'jquery';
import { Observable } from 'rxjs-compat';
import { Person } from 'src/app/_models/person.model';
import { PersonInfo } from 'src/app/_models/person-info-model';
import * as _ from 'lodash';
import { setRowElements } from '@syncfusion/ej2-angular-grids';
import { AlShifaLoginService } from 'src/app/alshifa/alShifaLogin.service';
import { CheckboxModule } from 'primeng/primeng';

@Component({
  selector: 'app-brain-death-registration',
  templateUrl: './brain-death-registration.component.html',
  styleUrls: ['./brain-death-registration.component.scss']
})
export class BrainDeathRegistrationComponent implements OnInit {

  brainDeathForm: FormGroup;
  fDt: any;
  sDt: any;
  nfDt: any;
  // firstExamBy: string = '';
  todos: any;
  selectedExam: any;
  patientview: any = null;
  showExamDetails = false;
  showNow = false;
  examDetails: any = [];
  newVariable: any;
  showfirstExamBy: Boolean = false;
  secondExamBy: string = '';
  personInfo1 = new PersonInfo();
  personInfo2 = new PersonInfo();
  personInfo3 = new PersonInfo();
  personInfo4 = new PersonInfo();
  submitted: Boolean;
  notifyInst: Boolean;
  today = new Date();
  searchCivilId: any;
  brainDeathReg: Array<TbBrainDeathReg>;
  brainDeathExamTran: Array<TbBrainDeathExamTran>;
  personInfo: any = [];
  brianDeathExam: any;
  part1Exam: any;
  part2Exam: any;
  part3Exam: any;
  public parto: any = [];
  patientInfoDtlview: any;
  public part1: any = [];
  public part2: any = [];
  public part3: any = [];
  genDisorderForm: FormGroup;
  part1His: any = [];
  part2His: any = [];
  part3His: any = [];
  bdform: any = [];
  smsData: any = {};
  allData: any;
  rowData: any;
  formData: RenalRegistryFrom;
  comorbidDiseaseListGrid: GridOptions;
  private comorbidDiseaseGridApi: GridApi;
  private comorbidDiseaseGridColApi: ColumnApi;
  columnDefs;
  frameworkComponents;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  grids: any = [];
  loginId: any;
  brainDeathExamination: any = [];
  brainDeathExaminationData: any = [];
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  patientType: any;  // Adult ==> A  / Pediatrics and Neonates  ==> P
  patientAge = 12;
  title1 = "If these prerequisites can not be ocrrected and are judged to be potentially contributing to the loss of brain functions, complete clinical examinations and apnea test, as well ancillary testing, should be performed to confirm brain death. ";
  title2 = "Not assessable (NA) in the physical examination & apnea test, except for absent bilateral oculocphical reflex, necessitates doing the ancillary test.";
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  patientData: any;
  examSetNo: number = 1;
  selectedIndex: number = null;
  disableAfterNotify: Boolean = true;
  loginName: any;
  perscode: any;
  mode: any;
  examNo: any;
  radioIsSelected: Boolean;
  examiner1LoginId: any;
  examiner2LoginId: any;

  enableFirstExam: boolean = true;
  enableSecondExam: boolean = true;
  ExamByNameReadOnly: boolean = true;

  disableAncillaryTest: boolean;
  valueOfChecked: any;


  currentUserNow: any;
  usersOfBDPage: any = [];
  atThisMomentUser: any;

  type: any;
  ancillaryTestYnValue: any;
  part4QuestionSelected: boolean = true;
  civilIdEntryType: any;
  entryTypeMPI: any;
  alshifaDob:any;







  constructor(private _sharedService: SharedService, private _loginService: LoginService, private _alShifaLoginService: AlShifaLoginService, private _router: Router, private fb: FormBuilder, private _masterService: MasterService, private _http: HttpClient, private _renalService: RenalService, private toastrService: ToastrService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    this.populateMasterData();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.searchCivilId = this._sharedService.getNavigationData().civilId;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);

    }


  }

  ngOnInit() {
    this.populateMasterData();
    this.brainDeathForm = this.fb.group({
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'civilId': [null],
      'secondName': new FormControl(),
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'maritalStatus': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'tel': new FormControl(),
      'rgTbPatientInfo': this.fb.array([]),
      'brainDeathReg': this.fb.group({
        'tranId': "",
        'ancillaryTestReason': "",
        'firstExamBy': "",
        'firstExamByName': "",
        'firstExamDate': "",
        'firstPostPaco2': "",
        'firstPrePaco2': "",
        'intracranialBloodFlow': "",
        'primaryDiagnosis': "",
        'secondExamBy': "",
        'secondExamByName': "",
        'secondExamDate': "",
        'secondPrePaco2': "",
        'examinationType': "",
        'secondPostPaco2': "",
        'ancillaryTestYn': "",
      }),
      part1: this.fb.array([]),
      part2: this.fb.array([]),
      part3: this.fb.array([]),

    });
  }


  // null.....for enabling
  // true....for disabling
  checkLoginUser(examiner1LoginId, examiner2LoginId) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginName = curUser['person'].personName;
    this.loginId = curUser['person'].perscode;

    if (this.examSetNo != AppUtils.SECOND_EXAM_SET) {
      // SET 1
      if (!examiner1LoginId && !examiner2LoginId) {
        this.enableFirstExam = null;
        this.enableSecondExam = true;
      } else if (this.loginId != examiner1LoginId && !examiner2LoginId) {
        this.enableFirstExam = true;
        this.enableSecondExam = null;

      } else if (this.loginId != examiner1LoginId && this.loginId != examiner2LoginId) {
        this.enableFirstExam = true;
        this.enableSecondExam = true;

      } else if (this.loginId == examiner1LoginId) {
        this.enableFirstExam = null;
        this.enableSecondExam = true;

      } else if (this.loginId == examiner2LoginId) {
        this.enableFirstExam = true;
        this.enableSecondExam = null;
      }
    } else {
      // SET 2
      // && !this.allData.rgTbBrainDeathReg[1]
      if (this.allData.rgTbBrainDeathReg.find(r => r.examinationType == AppUtils.FIRST_EXAM_SET &&
        (r.firstExamBy === this.loginId || r.secondExamBy === this.loginId) && !this.allData.rgTbBrainDeathReg[1])) {
        this.enableFirstExam = null;
        this.enableSecondExam = true;
      } else {
        if (!examiner1LoginId && !examiner2LoginId) {
          this.enableFirstExam = null;
          this.enableSecondExam = true;

        } else if (this.loginId != examiner1LoginId && !examiner2LoginId) {
          this.enableFirstExam = true;
          this.enableSecondExam = null;

        } else if (!this.allData.rgTbBrainDeathReg[1] && this.loginId != examiner1LoginId && this.loginId != examiner2LoginId) {
          this.enableFirstExam = null;
          this.enableSecondExam = true;

        } else if (this.loginId != examiner1LoginId && this.loginId != examiner2LoginId) {
          this.enableFirstExam = true;
          this.enableSecondExam = true;

        } else if (this.loginId == examiner1LoginId) {
          this.enableFirstExam = null;
          this.enableSecondExam = true;

        } else if (this.loginId == examiner2LoginId) {
          this.enableFirstExam = true;
          this.enableSecondExam = null;
        }
      }

    }

  }


  onChicked(event) {
    if (event.target.checked == true) {
      this.radioIsSelected = true;
      event.target.item.partsYesNoOne = true;
      if (!this.yesNoValidationPart4()) return;
      return false;
    } else {
      this.radioIsSelected = false;

    }
    return true;
  }




  populateMasterData() {
    this._renalService.getBrainDeathExam().subscribe(res => {
      this.brianDeathExam = res['result'];
      this.brianDeathExam.filter(s => s.adultPaed == null);

      this.brianDeathExam.forEach(ele => {
        ele.ex1 = "P" + ele.prevId + "_" + ele.id + "_" + "EX1";
        ele.ex2 = "P" + ele.prevId + "_" + ele.id + "_" + "EX2";
      });


    })

  }



  addPart1Hstory(runId: any = null, examParamId: any = null, examGroupId: any = null, fisrtExamValue: any = null, secondExamValue: any = null): void {
    this.part1 = this.brainDeathForm.get('part1') as FormArray;

    this.part1His = Object.assign([], this.part1.value);
    const partHistItem: any = this.createPartItem(runId, examParamId, examGroupId, fisrtExamValue, secondExamValue);
    this.part1.push(this.createPartGrpItem(partHistItem));

    this.part1His.push(partHistItem);
  }

  addPart2Hstory(runId: any = null, examParamId: any = null, examGroupId: any = null, fisrtExamValue: any = null, secondExamValue: any = null): void {
    this.part2 = this.brainDeathForm.get('part2') as FormArray;

    this.part2His = Object.assign([], this.part2.value);
    const partHistItem: any = this.createPartItem(runId, examParamId, examGroupId, fisrtExamValue, secondExamValue);
    this.part2.push(this.createPartGrpItem(partHistItem));

    this.part2His.push(partHistItem);
  }

  addPart3Hstory(runId: any = null, examParamId: any = null, examGroupId: any = null, fisrtExamValue: any = null, secondExamValue: any = null): void {
    this.part3 = this.brainDeathForm.get('part3') as FormArray;

    this.part3His = Object.assign([], this.part3.value);
    const partHistItem: any = this.createPartItem(runId, examParamId, examGroupId, fisrtExamValue, secondExamValue);
    this.part3.push(this.createPartGrpItem(partHistItem));

    this.part3His.push(partHistItem);
  }

  createPartGrpItem(partHistItem: any): FormGroup {
    return this.fb.group(partHistItem);
  }

  createPartItem(runId: any = null, examParamId: any = null, examGroupId: any = null, fisrtExamValue: any = null, secondExamValue: any = null) {
    return {
      runId: runId,
      examParamId: examParamId,
      examGroupId: examGroupId,
      fisrtExamValue: fisrtExamValue,
      secondExamValue: secondExamValue
    };
  }

  getExamValue(id) {
    if (id) {
      return this.brianDeathExam.filter(s => s.id == id).map(s => s.value)[0];
    }
  }

  getExam1(id) {
    if (id) {
      return this.brianDeathExam.filter(s => s.id == id).map(s => s.ex1)[0];
    }
  }
  getExam2(id) {
    if (id) {
      return this.brianDeathExam.filter(s => s.id == id).map(s => s.ex2)[0];
    }
  }

  clearFormArray = (formArray: FormArray) => {
    while (formArray.length !== 0) {
      formArray.removeAt(0)
    }
  }

  checkAge(alshifaDob?: any) {
    const dobToUse = alshifaDob || this.patientDetails.patientForm.value.dob;
  
    if (dobToUse) {
      this.getAge(dobToUse);
      this.checkLoginUser(this.examiner1LoginId, this.examiner2LoginId);
      this.fillExam();
    }
  }
  

  fillExam() {

    const part1 = <FormArray>this.brainDeathForm.controls.part1;
    this.clearFormArray(part1);
    const part2 = <FormArray>this.brainDeathForm.controls.part2;
    this.clearFormArray(part2);
    const part3 = <FormArray>this.brainDeathForm.controls.part3;
    this.clearFormArray(part3);



    this.part1Exam = this.brianDeathExam.filter(s => s.prevId == 1 && (s.adultPaed == this.patientType || s.adultPaed == null));
    this.part2Exam = this.brianDeathExam.filter(s => s.prevId == 2 && (s.adultPaed == this.patientType || s.adultPaed == null));
    this.part3Exam = this.brianDeathExam.filter(s => s.prevId == 3 && (s.adultPaed == this.patientType || s.adultPaed == null));


    let data = [];

    if (this.brainDeathExamTran) {
      for (let row of this.brainDeathExamTran) {
        for (let n of this.part1Exam) {
          if (n.id == row.examParamId) {
            data.push(row);
          }
        }
      }
      for (let row of data) {
        this.addPart1Hstory(row.runId, row.examParamId, row.examGroupId, row.fisrtExamValue, row.secondExamValue);
      }
    }


    this.part1Exam.forEach(el => {
      if (this.part1His.filter(s => s.examParamId == el.id).length == 0) {
        this.addPart1Hstory(null, el.id, el.prevId, null, null);
      }
    })


    data = [];
    if (this.brainDeathExamTran) {
      for (let row of this.brainDeathExamTran) {
        for (let n of this.part2Exam) {
          if (n.id == row.examParamId) {
            data.push(row);
          }
        }
      }
      for (let row of data) {
        this.addPart2Hstory(row.runId, row.examParamId, row.examGroupId, row.fisrtExamValue, row.secondExamValue);
      }
    }
    this.part2Exam.forEach(el => {
      if (this.part2His.filter(s => s.examParamId == el.id).length == 0) {
        this.addPart2Hstory(null, el.id, el.prevId, null, null);
      }
    })


    data = [];
    if (this.brainDeathExamTran) {
      for (let row of this.brainDeathExamTran) {
        for (let n of this.part3Exam) {
          if (n.id == row.examParamId) {
            data.push(row);
          }
        }
      }
      for (let row of data) {
        this.addPart3Hstory(row.runId, row.examParamId, row.examGroupId, row.fisrtExamValue, row.secondExamValue);
      }
    }
    this.part3Exam.forEach(el => {
      if (this.part3His.filter(s => s.examParamId == el.id).length == 0) {
        this.addPart3Hstory(null, el.id, el.prevId, null, null);
      }
    })

  }


  addSecondExamDetails() {
    this.examSetNo = AppUtils.SECOND_EXAM_SET;
    this.showNow = false;
    this.brainDeathForm.reset();
    this.bdr['examinationType'].setValue(2);
    this.brainDeathExamTran = null;
    this.part1His = [];
    this.part2His = [];
    this.part3His = [];
    this.fillExam();
    if (this.allData.rgTbBrainDeathReg[0].ancillaryTestYn) {
      this.disableAncillaryTest = true;
    } else {
      this.disableAncillaryTest = null;
    }
    this.checkLoginUser(this.examiner1LoginId, this.examiner2LoginId);
  }
  ancillaryTestValidation() {



    if (this.allData.rgTbBrainDeathReg) {
      if (this.allData.rgTbBrainDeathReg.length == 0) {
        this.disableAncillaryTest = null;

      } else if (this.allData.rgTbBrainDeathReg.length == 1) {

        if (this.allData.rgTbBrainDeathReg[0].ancillaryTestYn == "Y") {
          this.disableAncillaryTest = true;
          this.radioIsSelected = true;

        } else if (this.allData.rgTbBrainDeathReg[0].ancillaryTestYn == "N") {
          this.disableAncillaryTest = true;
        } else {
          this.disableAncillaryTest = null;
        }

      }

      if (this.allData.rgTbBrainDeathReg.length == 2) {

        const rgTbBrainDeathRegFirst = this.allData.rgTbBrainDeathReg[0];
        const rgTbBrainDeathRegSecond = this.allData.rgTbBrainDeathReg[1];

        if (!rgTbBrainDeathRegFirst.ancillaryTestYn && !rgTbBrainDeathRegSecond.ancillaryTestYn) {
          this.disableAncillaryTest = null;

        } else if (rgTbBrainDeathRegFirst.ancillaryTestYn == "Y") {
          rgTbBrainDeathRegSecond.ancillaryTestYn = rgTbBrainDeathRegFirst.ancillaryTestYn;
          rgTbBrainDeathRegSecond.ancillaryTestReason = rgTbBrainDeathRegFirst.ancillaryTestReason;
          rgTbBrainDeathRegSecond.intracranialBloodFlow = rgTbBrainDeathRegFirst.intracranialBloodFlow;
          this.disableAncillaryTest = true;
          this.radioIsSelected = true;

        } else if (rgTbBrainDeathRegSecond.ancillaryTestYn == "Y") {
          rgTbBrainDeathRegFirst.ancillaryTestYn = rgTbBrainDeathRegSecond.ancillaryTestYn;
          rgTbBrainDeathRegFirst.ancillaryTestReason = rgTbBrainDeathRegSecond.ancillaryTestReason;
          rgTbBrainDeathRegFirst.intracranialBloodFlow = rgTbBrainDeathRegSecond.intracranialBloodFlow;
          this.disableAncillaryTest = true;
          this.radioIsSelected = true;

        } else if (rgTbBrainDeathRegFirst.ancillaryTestYn == "N") {
          rgTbBrainDeathRegSecond.ancillaryTestYn = rgTbBrainDeathRegFirst.ancillaryTestYn;
          rgTbBrainDeathRegSecond.ancillaryTestReason = rgTbBrainDeathRegFirst.ancillaryTestReason;
          rgTbBrainDeathRegSecond.intracranialBloodFlow = rgTbBrainDeathRegFirst.intracranialBloodFlow;
          this.disableAncillaryTest = true;

        } else if (rgTbBrainDeathRegSecond.ancillaryTestYn == "N") {
          rgTbBrainDeathRegFirst.ancillaryTestYn = rgTbBrainDeathRegSecond.ancillaryTestYn;
          rgTbBrainDeathRegFirst.ancillaryTestReason = rgTbBrainDeathRegSecond.ancillaryTestReason;
          rgTbBrainDeathRegFirst.intracranialBloodFlow = rgTbBrainDeathRegSecond.intracranialBloodFlow;
          this.disableAncillaryTest = true;
        }
      }

    }

  }





  callMpiMethod() {
    this.getdata(this.patientDetails.f.civilId.value);

  }

  //get patient date (by RegNo or CivilID)
  getdata(civilId: any, callType: any = null) {
    let msg;
    let callMPI = true;
    if (callType == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }



    }

    this._renalService.getBrainDeathDetermination(civilId, callType).subscribe(async res => {

      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {

        //check if call from al shifa and there is no brain dearth register, then get information from local MPI.
        if (callType == AppUtils.CALL_TYPE_AL_SHIFA && res['result'].personInfo) {
          this.patientDetails.setMPIDetails(res['result'].personInfo);
          this.patientDetails.patientForm.patchValue({ civilId: res['result'].personInfo.civilId });
          this.alshifaDob = res['result'].personInfo.birthDate;
          this.checkAge(this.alshifaDob);
          Swal.fire('', 'There is no brain death registry for this civil id!', 'warning');
          return true;
        }
        this.formData = res['result'];
        this.patientDetails.setPatientDetails(this.formData);
        this.checkAge();
        this.allData = res['result'];
        this.allData.rgTbBrainDeathReg[0].civilID = this.allData.rgTbPatientInfo.civilId;
        if (this.allData.rgTbBrainDeathReg) {
          if (this.allData.rgTbBrainDeathReg.length > 0) {
            this.patientDetails.updateInstitutesList(this.allData.rgTbBrainDeathReg[0].estCode);
            this.patientDetails.patientForm.get('regInst').disable();
          }
        }

        if (this.allData.rgTbBrainDeathReg.length > 0) {
          // if there is data in brain reg table
          this.examiner1LoginId = this.allData.rgTbBrainDeathReg[0].firstExamBy;
          this.examiner2LoginId = this.allData.rgTbBrainDeathReg[0].secondExamBy;
          this.checkLoginUser(this.examiner1LoginId, this.examiner2LoginId);


          this.ancillaryTestValidation();



          if (this.allData.brainDeathExamination && this.allData.brainDeathExamination.length > 0)
            this.showExamDetails = true;

          if (this.allData.brainDeathExamination.length == 1 && this.allData.brainDeathExamination[0].secondExamDate)
            this.showNow = true;

          if (this.allData.rgTbBrainDeathReg[1]) {
            if (this.allData.rgTbBrainDeathReg[1].examinationType && this.allData.rgTbBrainDeathReg[1].notifyYn == null && this.allData.rgTbBrainDeathReg[1].secondExamDate) {
              //show notify btn
              this.notifyInst = true;
            } else {
              this.disableAfterNotify = true;
            }
          }
          this.allData.rgTbBrainDeathReg[0].civilID = this.allData.rgTbPatientInfo.civilId;
          if (res['result'].rgTbBrainDeathReg && res['result'].rgTbBrainDeathReg.length > 0) {
            this.smsData['tranId'] = res['result'].rgTbBrainDeathReg.tranId;
            let tableData = res['result'].rgTbBrainDeathReg;

            // START HERE
            if (tableData) {
              tableData.forEach(ele => {
                let detail = {
                  tranId: ele.tranId,
                  examinationType: ele.examinationType,
                  firstExamDate: ele.firstExamDate,
                  firstExamName: ele.firstExamName,
                  firstExamByName: ele.firstExamByName,

                  secondExamDate: ele.secondExamDate,
                  secondExamName: ele.secondExamName,
                  secondExamByName: ele.secondExamByName,
                }

                if (detail.examinationType == AppUtils.FIRST_EXAMINATION) {
                  detail.examinationType = "Initial Examination";

                } else if (detail.examinationType == AppUtils.SECOND_EXAMINATION) {
                  detail.examinationType = "Second Examination";
                }

                this.brainDeathExaminationData.push(detail);

              })
              this.allData.rgTbBrainDeathReg[0].civilID = this.allData.rgTbPatientInfo.civilId;
              this.populateSelectedData(this.allData.rgTbBrainDeathReg[0].tranId);
            }
          } else {

            this.fillExam();
          }

          // END HERE
        } else {
          // if there is no data in brain reg table
          this.enableFirstExam = null;
          this.enableSecondExam = true;
        }

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        //if record not exist in eReg DB, and qeury by Civil ID , then call fetchMpi method.
        await Swal.fire(
          '', 'No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.', 'warning',
        ).then(async (result) => {
          if (callMPI) {
            this._sharedService.setPatientData(this.patientDetails);
            this._sharedService.fetchMpi(civilId).subscribe(civilIdEntryType => {
              this.civilIdEntryType = civilIdEntryType !== false ? civilIdEntryType : AppUtils.CIVILID_ENTERY_MANUALLY;
              this.checkAge();
            });

            this.enableFirstExam = null;
            this.enableSecondExam = true;
          } else {
            this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY; // Set to "M" if callMPI is false
          }

        })
      } else {
        Swal.fire('', res['message'], 'error')
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('', 'Error occured while retrieving details', 'error')
    })
  }


  populateSelectedData(tranId: any, index: any = null) {
    let examDetail = this.allData.rgTbBrainDeathReg.filter(el => el.tranId == tranId)[0];
    let brainDeath: FormGroup = <FormGroup>this.brainDeathForm.controls["brainDeathReg"];


    const firstExamData = examDetail.firstExamDate;
    const secondExamData = examDetail.secondExamDate;

    if (firstExamData) {
      examDetail.firstExamDate = new Date(examDetail.firstExamDate);
    } else {
      examDetail.firstExamDate = null;
    }

    if (secondExamData) {
      examDetail.secondExamDate = new Date(examDetail.secondExamDate);
    } else {
      examDetail.secondExamDate = null;
    }


    brainDeath.patchValue(examDetail);

    this.patientDetails.f.regInst.setValue(examDetail.estCode);
    this.brainDeathExamTran = examDetail.rgTbBrainDeathExamTrans;

    if (examDetail.examinationType == AppUtils.FIRST_EXAMINATION) {
      this.examSetNo = AppUtils.FIRST_EXAM_SET;
    } else {
      this.examSetNo = AppUtils.SECOND_EXAM_SET;
    }

    this.fillExam();
    if (index == null) {
      index = 0;
    }
    this.selectedIndex = index;


    this.checkLoginUser(examDetail.firstExamBy, examDetail.secondExamBy);
  }

  ngAfterViewInit() {
    setTimeout(() => {

      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaEstCode: any;
        let alShifaValCode: any;
        let alShifaCivilID: any;
        let alShifaPatientId: any;

        this._alShifaLoginService.getAlShifanData().forEach(element => {
          if (element["civilId"]) {
            alShifaCivilID = element["civilId"]
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          } else if (element["patientId"]) {
            alShifaPatientId = element["patientId"];
          }
        });

        if (alShifaCivilID) {


          this.patientDetails.getEstById(alShifaEstCode);
          this.searchCivilId = alShifaCivilID;
          //  this.patientDetails.updateInstitutesList(alShifaEstCode);

          this.getdata(this.searchCivilId, AppUtils.CALL_TYPE_AL_SHIFA);
          this.patientDetails.patientForm.patchValue({ regInst: alShifaEstCode });
          this.patientDetails.patientForm.patchValue({ patientId: alShifaPatientId });
        }


      }
    }, 1000);
  }



  search() {
    this.clear();

    if (this.searchCivilId) {
      this.clear();
      setTimeout(() => {
        this.getdata(this.searchCivilId);
        this.searchCivilId = "";
      }, 1000);

    } else {
      this.searchCivilId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Civil ID'
      });
    }
  }

  get f() { return this.brainDeathForm.controls; }

  get bdr() { return this.f.brainDeathReg['controls']; }

  get brainDForm() { return this.brainDeathForm.value; }

  get mandPati() { return this.patientDetails.f; }


  isObject(val) {
    return (typeof val === 'object');
  }


  validateFields() {
    if (!this.mandPati.firstName.value || !this.mandPati.secondName.value ||
      !this.mandPati.civilId.value || !this.mandPati.patientId.value) {
      Swal.fire('Alert', 'Mandatory fields in Patient Details cannot be empty', 'warning');
      return false;
    } else {
      return true;
    }
  }

  sendSms() {
    if (this.validateData()) {
      this._renalService.notify(this.allData.rgTbPatientInfo.civilId).subscribe(response => {
        // if (response["result"])
        Swal.fire('', 'Message Sent Successfully.', 'success');
        return true;
      })
    } else {
      Swal.fire('Faild!', 'Message Was Not Sent', 'error');
      return false;
    }
  }



  validateData() {
    let firstExampartOne = this.brainDForm.part1.find(s => s.fisrtExamValue == null);
    let firstExampartTwo = this.brainDForm.part2.find(s => s.fisrtExamValue == null);
    let firstExampartThree = this.brainDForm.part3.find(s => s.fisrtExamValue == null);

    const secondExampartOne = this.brainDForm.part1.find(s => s.secondExamValue == null);
    const secondExampartTwo = this.brainDForm.part2.find(s => s.secondExamValue == null);
    const secondExampartThree = this.brainDForm.part3.find(s => s.secondExamValue == null);

    if (!this.enableSecondExam == true) {
      if (secondExampartOne || secondExampartTwo || secondExampartThree || !this.bdr['secondExamDate'].value
        || !this.bdr['secondPrePaco2'].value || !this.bdr['secondPostPaco2'].value) {
        Swal.fire('Alert', 'All Mandatory Fields of Second Examiner cannot be empty', 'warning');
        return false;
      }
    }

    if (!this.enableSecondExam == true) {
      if (secondExampartOne || secondExampartTwo || secondExampartThree || !this.bdr['secondExamDate'].value
        || !this.bdr['secondPrePaco2'].value || !this.bdr['secondPostPaco2'].value) {
        Swal.fire('Alert', 'All Mandatory Fields of Second Examiner cannot be empty', 'warning');
        return false;
      }
    }

    if (!this.enableFirstExam == true) {
      if (firstExampartOne || firstExampartTwo || firstExampartThree || !this.bdr['firstExamDate'].value
        || !this.bdr['firstPrePaco2'].value || !this.bdr['firstPostPaco2'].value) {
        Swal.fire('Alert', 'All Mandatory Fields of first Examiner cannot be empty', 'warning');
        return false;
      }
    }

    return true;

  }



  part4Validation() {
    if (this.examSetNo == AppUtils.SECOND_EXAM_SET && this.bdr['secondExamDate'].value && !this.bdr['ancillaryTestYn'].value) {
      Swal.fire('Missing Ancillary Testing Info', 'Please indicate whether ancillary testing has been performed (Yes/No).', 'warning');
      return false;
    }
    return true;
  }


  yesNoValidationPart4() {
    if (!this.bdr['ancillaryTestReason'].value || !this.bdr['intracranialBloodFlow'].value) {
      Swal.fire('Alert', 'If the  Ancillary Testing performed , Then you have to enter the "Reason" and the the way the "Absence of intracranial blood flow has been demonstrated"', 'warning');
      return false;
    }
    return true;
  }



  save() {

    let rgTbBrainDeathReg = new TbBrainDeathReg;
    this.submitted = true;


    //|| this.brainDeathForm.invalid
    if (this.patientDetails.patientForm.invalid) {
      return false;
    }

    let patientData = this.brainDForm;



    //set all exam train in trainData
    let trainData = [];

    patientData.part1.forEach(le => {
      trainData.push(le);
    });
    patientData.part2.forEach(le => {
      trainData.push(le);
    });
    patientData.part3.forEach(le => {
      trainData.push(le);
    });




    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;
    // let regDate;



    if (!patientData.brainDeathReg.examinationType || patientData.brainDeathReg.examinationType == "" || patientData.brainDeathReg.examinationType == AppCompUtils.FIRST_EXAM_SET) {
      patientData.brainDeathReg.examinationType = AppCompUtils.FIRST_EXAM_SET;
    } else {
      patientData.brainDeathReg.examinationType = AppCompUtils.SECOND_EXAM_SET;
    }


    if (patientData.brainDeathReg.tranId === null || patientData.brainDeathReg.tranId === "") {
      // new brain death case
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      // patientData.secondExamDate == null
      centralRegNo = null;
      // regDate = this.currentDate;
    } else {
      //Not a new brain death case
      createdBy = this.formData.rgTbPatientInfo.createdBy;
      createdOn = this.formData.rgTbPatientInfo.createdOn;
      modifiedBy = this.loginId;
      modifiedOn = this.currentDate;
      centralRegNo = this.patientDetails.f.centralRegNo.value

      // regDate = this.formData.rgTbPatientInfo.instRegDate;
    }


    // check the active examiner 
    if (!this.enableFirstExam == true) {
      this.examiner1LoginId = this.loginId;
      patientData.brainDeathReg.firstExamBy = this.loginId;
    }

    if (!this.enableSecondExam == true) {
      this.examiner2LoginId = this.loginId;
      patientData.brainDeathReg.secondExamBy = this.loginId;
    }

    // notify when the first exam set  Done
    if (this.examSetNo == AppUtils.FIRST_EXAM_SET && this.bdr['secondExamDate'].value) {
      this.sendSms();
    }

    if (this.examSetNo == AppUtils.SECOND_EXAM_SET && this.bdr['secondExamDate'].value) {
      this.notifyInst = true;
    }

    this.checkLoginUser(this.examiner1LoginId, this.examiner2LoginId);

    patientData.brainDeathReg.civilId = this.patientDetails.f.civilId.value;



    patientData.brainDeathReg.patientid = this.patientDetails.f.patientId.value;
    patientData.brainDeathReg.estCode = this.patientDetails.f.regInst.value;


    patientData.brainDeathReg.ancillaryTestYn = this.ancillaryTestYnValue;

    if (patientData.brainDeathReg.ancillaryTestYn == "Y") {
      if (!this.yesNoValidationPart4()) return;
    }






    patientData.brainDeathReg.firstExamDate = moment(patientData.brainDeathReg.firstExamDate, AppUtils.BD_DATE_FORMAT);
    patientData.brainDeathReg.secondExamDate = moment(patientData.brainDeathReg.secondExamDate, AppUtils.BD_DATE_FORMAT);
    // set tainData and brainDeathReg in rgTbBrainDeathReg before send it to API.


    rgTbBrainDeathReg = patientData.brainDeathReg;


    rgTbBrainDeathReg.rgTbBrainDeathExamTrans = trainData;
    rgTbBrainDeathReg.estName = this.patientDetails.institutes.filter(s => s.estCode == patientData.brainDeathReg.estCode).map(s => s.estName)[0];


    if (!this.civilIdEntryType) {
      if (this.formData) {
        if (this.formData.rgTbPatientInfo) {
          this.civilIdEntryType = this.formData.rgTbPatientInfo.civilIdEntryType;
        }
      } else {
        this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
      }
    }

    let saveData = {

      rgTbPatientInfo: {
        patientId: this.patientDetails.f.patientId.value,
        civilId: this.patientDetails.f.civilId.value,
        createdBy: createdBy,
        //   createdInstid: this.patientDetails.f.createdInstid.value,
        createdOn: createdOn,
        dob: this.patientDetails.f.dob.value ? moment(this.patientDetails.f.dob.value).format("DD-MM-yyyy") : null, //this.patientDetails.f.dob.value
        firstName: this.patientDetails.f.firstName.value,
        lastUpdatedBy: modifiedBy,
        lastUpdatedDate: modifiedOn,
        //   lastUpdatedInstId: this.patientDetails.f.lastUpdatedInstId.value,
        maritalStatus: this.patientDetails.f.maritalStatus.value,
        mobileNo: this.patientDetails.f.mobileNo.value,
        kinTelNo: this.patientDetails.f.kinTelNo.value,
        secondName: this.patientDetails.f.secondName.value,
        civilIdEntryType: this.civilIdEntryType,
        sex: this.patientDetails.f.sex.value,
        thirdName: this.patientDetails.f.thirdName.value,
        tribe: this.patientDetails.f.tribe.value,
        village: this.patientDetails.f.village.value,
      },
      rgTbBrainDeathReg: rgTbBrainDeathReg,

    }



    if (patientData.brainDeathReg.examinationType == 1) {
      if (!patientData.brainDeathReg.ancillaryTestYn && !this.bdr['ancillaryTestYn'].value) {
        this.disableAncillaryTest = null;
      } else {
        patientData.brainDeathReg.ancillaryTestYn = this.bdr['ancillaryTestYn'].value;
        patientData.brainDeathReg.ancillaryTestReason = this.bdr['ancillaryTestReason'].value;
        patientData.brainDeathReg.intracranialBloodFlow = this.bdr['intracranialBloodFlow'].value;
        this.disableAncillaryTest = true;
      }
    }


    if (patientData.brainDeathReg.examinationType == 2) {
      if (!this.bdr['ancillaryTestYn'].value) {
        this.disableAncillaryTest = null;

      } else {
        patientData.brainDeathReg.ancillaryTestYn = this.bdr['ancillaryTestYn'].value;
        patientData.brainDeathReg.ancillaryTestReason = this.bdr['ancillaryTestReason'].value;
        patientData.brainDeathReg.intracranialBloodFlow = this.bdr['intracranialBloodFlow'].value;
        this.disableAncillaryTest = true;
      }

    }


    if (this.allData) {
      if (this.allData.rgTbBrainDeathReg.length > 0) {
        if (this.allData.rgTbBrainDeathReg[0].estCode) {
          this.patientDetails.patientForm.get('regInst').disable()
        }
      }
    }


    if (!this.validateFields()) return;
    if (!this.validateData()) return;
    if (!this.part4Validation()) return;



    this._renalService.saveBrainDeathDetermination(saveData).subscribe(res => {
      if (res['code'] == "S0000") {
        Swal.fire('Saved!', 'Brain Death Determination Saved successfully.', 'success');
        this.searchCivilId = res["result"];
        this.smsData = saveData;
        this.search();
      } else if (res['code'] == "3") {
        Swal.fire('Saved!', res['message'], 'error');
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Brain Death Determination ' + err.message, 'error')
    })


  }


  firstDateValidation(event) {

    if (this.examSetNo == AppUtils.SECOND_EXAM_SET) {

      let firstDateExamOne = _.cloneDeep(new Date(this.allData.brainDeathExamination[0].firstExamDate));
      let secondDateExamOne = _.cloneDeep(new Date(this.allData.brainDeathExamination[0].secondExamDate));

      let firstDateExamTwo = this.bdr['firstExamDate'].value;
      let secondDateExamTwo = this.bdr['secondExamDate'].value;

      if (firstDateExamOne && secondDateExamOne) {

        const maxDate = (dates: Date[]) => new Date(Math.max(...dates.map(Number)));

        let comparedDate = null;
        //comparedDate = new Date(Math.max(firstDateExamOne, secondDateExamOne));
        comparedDate = maxDate([firstDateExamOne, secondDateExamOne])

        comparedDate = new Date(comparedDate.setHours(comparedDate.getHours() + 6));

        if (firstDateExamTwo && !secondDateExamTwo) {
          if ((firstDateExamTwo > comparedDate || firstDateExamTwo == comparedDate)) {
            return true;
          } else {
            Swal.fire("Alert", "Timing of second exam should be at least 6 hours from first exam", "error");
            return false;
          }
        }
      }
    } else {
    }
  }



  clear() {

    this.brainDeathForm.reset();
    this.formData = null;
    this.patientDetails.clear();
    this.patientType = null;
    this.submitted = false;
    this.brainDeathExamination = [];
    this.brainDeathExaminationData = [];
    this.showNow = false;
    this.brainDeathExamTran = null;

    this.part1His = [];
    this.part2His = [];
    this.part3His = [];

  }


  getAge(event) {
    let calculatedAge = this._sharedService.ageFromDateOfBirthday(event)
    this.patientDetails.patientForm.patchValue({ age: calculatedAge });
    if (calculatedAge && calculatedAge <= this.patientAge) {
      this.patientType = AppUtils.PATIENT_TYPE_PEDIATIRICS_AND_NEONATES;
    } else {
      this.patientType = AppUtils.PATIENT_TYPE_ADULT;
    }
  }

  onChange(e) {
    this.type = e.target.value;
    this.ancillaryTestYnValue = this.type;
    if (this.ancillaryTestYnValue == "Y") {
      this.radioIsSelected = true;
    } else if (this.ancillaryTestYnValue == "N") {
      this.radioIsSelected = false;
    }
  }










}
