import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

@Injectable({
    providedIn: 'root'
})
export class NotificationService {

    /**
     * Standardized notification method for all alerts across the application
     * @param type - Type of notification: 'success', 'error', 'warning', 'info'
     * @param title - Title of the notification
     * @param message - Message content
     * @param showConfirmButton - Whether to show confirm button (default: true)
     * @param timer - Auto close timer in milliseconds (default: 3000 when no confirm button)
     */
    showNotification(
        type: NotificationType,
        title: string,
        message: string,
        showConfirmButton: boolean = true,
        timer?: number
    ): void {
        const autoTimer = timer || (showConfirmButton ? undefined : 3000);

        Swal.fire({
            icon: type,
            title: title,
            text: message,
            showConfirmButton: showConfirmButton,
            timer: autoTimer,
            timerProgressBar: !showConfirmButton,
            confirmButtonColor: '#3085d6',
            allowOutsideClick: !showConfirmButton
        });
    }

    /**
     * Standardized confirmation dialog
     * @param title - Title of the confirmation
     * @param text - Text content
     * @param confirmButtonText - Text for confirm button (default: 'Confirm')
     * @param cancelButtonText - Text for cancel button (default: 'Cancel')
     * @returns Promise<boolean> - true if confirmed, false if cancelled
     */
    showConfirmationDialog(
        title: string,
        text: string,
        confirmButtonText: string = 'Confirm',
        cancelButtonText: string = 'Cancel'
    ): Promise<boolean> {
        return Swal.fire({
            title: title,
            text: text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: confirmButtonText,
            cancelButtonText: cancelButtonText
        }).then((result) => {
            return result.isConfirmed;
        });
    }

    /**
     * Success notification shorthand
     * @param title - Title of the notification
     * @param message - Message content
     * @param showConfirmButton - Whether to show confirm button (default: true)
     */
    showSuccess(title: string, message: string, showConfirmButton: boolean = true): void {
        this.showNotification('success', title, message, showConfirmButton);
    }

    /**
     * Error notification shorthand
     * @param title - Title of the notification
     * @param message - Message content
     * @param showConfirmButton - Whether to show confirm button (default: true)
     */
    showError(title: string, message: string, showConfirmButton: boolean = true): void {
        this.showNotification('error', title, message, showConfirmButton);
    }

    /**
     * Warning notification shorthand
     * @param title - Title of the notification
     * @param message - Message content
     * @param showConfirmButton - Whether to show confirm button (default: true)
     */
    showWarning(title: string, message: string, showConfirmButton: boolean = true): void {
        this.showNotification('warning', title, message, showConfirmButton);
    }

    /**
     * Info notification shorthand
     * @param title - Title of the notification
     * @param message - Message content
     * @param showConfirmButton - Whether to show confirm button (default: true)
     */
    showInfo(title: string, message: string, showConfirmButton: boolean = true): void {
        this.showNotification('info', title, message, showConfirmButton);
    }

    /**
     * Loading notification with spinner
     * @param title - Title of the loading message
     * @param message - Loading message
     */
    showLoading(title: string = 'Loading...', message: string = 'Please wait'): void {
        Swal.fire({
            title: title,
            text: message,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            onBeforeOpen: () => {
                Swal.showLoading();
            }
        });
    }

    /**
     * Close any open notification
     */
    close(): void {
        Swal.close();
    }
}
