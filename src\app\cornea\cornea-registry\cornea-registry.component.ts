import { ChangeDetectorRef, Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { CorneaRequest } from 'src/app/_models/corneaRequest.model';
import { CorneaTissue } from 'src/app/_models/corneaTissue.model';
import { MasterService } from 'src/app/_services/master.service';
import { CorneaService } from '../cornea.service';
import Swal from 'sweetalert2';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { SharedService } from 'src/app/_services/shared.service';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-cornea-registry',
  templateUrl: './cornea-registry.component.html',
  styleUrls: ['./cornea-registry.component.scss'],
  providers: [CorneaService]
})
export class CorneaRegistryComponent implements OnInit, OnDestroy {
  transplantForm!: FormGroup;
  instForm!: FormGroup;

  corClearZoneFilter: any[] = [];
  corPrimaryDiagFilter: any[] = [];
  corIndicationTypeFilter: any[] = [];
  corTissueTypeFilter: any[] = [];
  instituteListFilter: any[] = [];
  surgeonNamesListFilter: any[] = [];
  reqInstitutesFilter: any[] = [];
  roleOptions: any[] = [];

  selectedInstitute: any;
  today = new Date();
  page3 = 1;
  pageSize3 = 5;
  pageTeam = 1;
  pageSizeTeam = 5;

  isEdit = false;
  isRequestSaved = false;
  reasonList: string[] = [
    'Not eligible',
    'Incomplete documents',
    'Other medical reasons',
    'No tissue available',
    'Other'
  ];

  viewedFileUrl: string | null = null;
  viewedFileName: string | null = null;
  viewedFileType: string | null = null;
  modalRef: NgbModalRef | null = null;

  requestNo: string = '';

  uploadedFiles: any[][] = [];

  selectedFileIndex: number = -1;

  isEnabled: boolean = false;

  deletedAttachmentIds: number[] = [];

  viewedImageIdx: number = -1;

  dbEstablishment: string = '';
  dbTissueNames: string = '';

  private privilegeSub?: Subscription;

  constructor(
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _corneaService: CorneaService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef,
    private _sharedService: SharedService,
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    // Ensure privileges are available and react to changes
    this._sharedService.refreshPrivilegesFromStorage();
    this.privilegeSub = this._sharedService.privileges$.subscribe(() => {
      if (this.transplantForm) {
        this.applyPrivilegeStates();
      }
    });

    this.loadInstitutes();
    this.getMasterData();
    this.initForm();


    this.roleOptions = [
      { id: 'D', label: 'Doctor' },
      { id: 'S', label: 'Surgeon' }
    ];

    this.subscribeTissueIdChanges();

    this.isEnabled = false;
  }

  ngOnDestroy(): void {
    if (this.privilegeSub) {
      this.privilegeSub.unsubscribe();
    }
  }

  // Centralized enable/disable handling based on privileges
  private applyPrivilegeStates(): void {
    if (!this.transplantForm) return;

    const reqPriv = 'REG_COR_REQUEST';
    const cdntrPriv = 'REG_COR_REQ_CDNTR';
    const apprPriv = 'REG_COR_REQ_APPROVE';

    const setState = (ctrl: any, priv: string) => {
      if (!ctrl) return;
      if (this.hasPrivilege(priv)) ctrl.enable({ emitEvent: false }); else ctrl.disable({ emitEvent: false });
    };

    // Top-level controls
    ['reqInst','reqDate','indication','primaryDiag','otherSpec','intendedArrDate','intendedSurDate','reqRemarks']
      .forEach(name => setState(this.transplantForm.get(name), reqPriv));

    // Requesting Tissues
    this.requestingTissue.controls.forEach((grp: FormGroup) => {
      setState(grp.get('tissueId'), reqPriv);
      setState(grp.get('sizeZone'), reqPriv);
      setState(grp.get('remarks'), reqPriv);
    });

    // Requesting Team
    this.requestingTeam.controls.forEach((grp: FormGroup) => {
      setState(grp.get('reqStaff'), reqPriv);
      setState(grp.get('contactNo'), reqPriv);
      setState(grp.get('remarks'), reqPriv);
      setState(grp.get('staffRole'), reqPriv);
    });

    // Communication Details
    this.communicationDetails.controls.forEach((grp: FormGroup) => {
      setState(grp.get('instId'), cdntrPriv);
      setState(grp.get('sendDate'), cdntrPriv);
      setState(grp.get('replyDate'), cdntrPriv);
      setState(grp.get('remarks'), cdntrPriv);
      setState(grp.get('tissueNo'), cdntrPriv);
      setState(grp.get('receivedDate'), cdntrPriv);

      setState(grp.get('decision'), apprPriv);
      setState(grp.get('reason'), apprPriv);
    });
  }

  subscribeTissueIdChanges() {
    this.requestingTissue.controls.forEach((ctrl) => {
      const tissueIdCtrl = ctrl.get('tissueId');
      if (tissueIdCtrl && !tissueIdCtrl['_subscribedForSizeZone']) {
        tissueIdCtrl.valueChanges.subscribe((val) => {
          if (val !== 8) {
            ctrl.get('sizeZone').setValue(null);
          }
        });
        tissueIdCtrl['_subscribedForSizeZone'] = true;
      }
    });
  }

  initForm(): void {
    this.transplantForm = this.fb.group({
      requestId: new FormControl(null),
      reqInst: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
      reqDate: new FormControl({ value: '', disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
      indication: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      primaryDiag: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      otherSpec: new FormControl({ value: '', disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      intendedArrDate: new FormControl({ value: '', disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      intendedSurDate: new FormControl({ value: '', disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      patientId: new FormControl(null),
      patCivilId: new FormControl(null),
      patRemarks: new FormControl(''),
      reqRemarks: new FormControl({ value: '', disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      status: new FormControl(''),
      invoiceNo: new FormControl(''),
      requestNo: new FormControl({ value: '', disabled: true }),
      requestingTissue: this.fb.array([]),
      requestingTeam: this.fb.array([]),
      communicationDetails: this.fb.array([]),
      createdBy: new FormControl(''),
      createdDate: new FormControl(''),
      requestSendMessage: new FormControl(''),
    });

    this.instForm = this.fb.group({
      instId: [0],
      instName: ['', Validators.required],
      instAddress: ['', Validators.required],
      activeYn: ['Y']
    });

    if (this._sharedService.getNavigationData()) {
      this.getList(this._sharedService.getNavigationData().requestNo);
      this._sharedService.setNavigationData(null);
      this.isEnabled = true;
    }

    // Re-apply states in case privileges arrived just now
    this.applyPrivilegeStates();
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getAllCorClearZone().subscribe(
      response => (this.corClearZoneFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorSurgeonNamesList().subscribe(
      response => (this.surgeonNamesListFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorPrimaryDiag().subscribe(
      response => (this.corPrimaryDiagFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorIndicationType().subscribe(
      response => (this.corIndicationTypeFilter = response.result),
      () => { }
    );
    this._masterService.getAllCorTissueType().subscribe(
      response => (this.corTissueTypeFilter = response.result),
      () => { }
    );
    this._masterService.getInstiteList(regCode, walCode).subscribe(
      response => (this.instituteListFilter = response.result),
      () => { }
    );
  }

  loadInstitutes(): void {
    this._masterService.getAllCorReqInstitutes().subscribe(
      response => (this.reqInstitutesFilter = response.result),
      () => { }
    );
  }

  getList(requestNo: any): void {
    this._corneaService.getCorneaRequestById(requestNo).subscribe(
      (res) => {
        if (res['code'] === 'S0000') {
          this.isEnabled = true;
          const data = res['result'];
          this.patchCorneaData(data);

        } else {
          Swal.fire('Error!', res['message'] || 'Failed to fetch request.', 'error');
        }
      },
      () => Swal.fire('Error!', 'An error occurred while loading request.', 'error')
    );
  }

  patchCorneaData(data: CorneaRequest): void {
    const patchable = {
      ...data,
      reqDate: data.reqDate ? new Date(data.reqDate) : null,
      intendedArrDate: data.intendedArrDate ? new Date(data.intendedArrDate) : null,
      intendedSurDate: data.intendedSurDate ? new Date(data.intendedSurDate) : null,
      primaryDiag: data.primaryDiag ? +data.primaryDiag : null,
    };

    this.transplantForm.patchValue(patchable);

    this.requestingTissue.clear();
    data.rgTbCorReqTissues.forEach((t) => {
      this.requestingTissue.push(this.createTissueGroup(t));
    });
    // Update tissue pagination and subscriptions
    this.page3 = this.requestingTissue.length > 0 ? 1 : 1;
    this.subscribeTissueIdChanges();

    this.requestingTeam.clear();
    data.rgTbCorReqDoctors.forEach((d) => {
      this.requestingTeam.push(this.createRequestingTeamFromData(d));
    });
    // Update team pagination
    this.pageTeam = this.requestingTeam.length > 0 ? 1 : 1;

    // --- NEW: Rebuild communicationDetails as a new FormArray and set it on the form ---
    const commArray = this.fb.array([]);
    const uploadedFiles: any[][] = [];
    if (data.rgTbCorReqCommDtls && Array.isArray(data.rgTbCorReqCommDtls)) {
      const sortedCommDtls = [...data.rgTbCorReqCommDtls].sort((a, b) => (a.commId || 0) - (b.commId || 0));
      sortedCommDtls.forEach((comm: any, commIdx: number) => {
        commArray.push(this.createCommunicationDetailFromData(comm));
        if (!uploadedFiles[commIdx]) uploadedFiles[commIdx] = [];
        if (comm.rgTbCorReqAttachDtls && Array.isArray(comm.rgTbCorReqAttachDtls)) {
          comm.rgTbCorReqAttachDtls.forEach((att: any) => {
            uploadedFiles[commIdx].push({
              name: att.fileName,
              type: att.fileName && att.fileName.toLowerCase().endsWith('.pdf') ? 'pdf' : 'image',
              backend: true,
              filePath: att.filePath,
              attachId: att.attachId
            });
          });
        }
      });
    }
    this.transplantForm.setControl('communicationDetails', commArray);
    this.uploadedFiles = uploadedFiles;

    if (data.reqInst && this.instituteListFilter && this.instituteListFilter.length) {
      const found = this.instituteListFilter.find((inst: any) => inst.estCode === data.reqInst);
      this.dbEstablishment = found ? found.estName : '';
    } else {
      this.dbEstablishment = '';
    }

    if (data.rgTbCorReqTissues && Array.isArray(data.rgTbCorReqTissues)) {
      const tissueNames: string[] = [];
      data.rgTbCorReqTissues.forEach((t: any) => {
        if (t.tissueId && this.corTissueTypeFilter && this.corTissueTypeFilter.length) {
          const tissueObj = this.corTissueTypeFilter.find((tt: any) => tt.paramId === t.tissueId);
          if (tissueObj && tissueObj.paramName) {
            tissueNames.push(tissueObj.paramName);
          }
        }
      });
      this.dbTissueNames = tissueNames.join(', ');
    } else {
      this.dbTissueNames = '';
    }

    // Force change detection to update the UI after patching form and arrays
    this.cdr.detectChanges();
    setTimeout(() => {
      this.cdr.detectChanges();
      window.dispatchEvent(new Event('resize'));
    }, 0);

    // Ensure privilege-based states are up-to-date after data patch
    this.applyPrivilegeStates();

    // Enable form after data is loaded
    this.isEnabled = true;
  }

  private getFileTypeFromName(fileName: string): string {
    if (!fileName) return 'unknown';
    const extRaw = fileName.split('.').pop();
    const ext = extRaw ? extRaw.toLowerCase() : '';
    if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(ext)) return `image/${ext === 'jpg' ? 'jpeg' : ext}`;
    if (["pdf"].includes(ext)) return 'application/pdf';
    if (["doc", "docx"].includes(ext)) return 'application/msword';
    if (["xls", "xlsx"].includes(ext)) return 'application/vnd.ms-excel';
    return 'application/octet-stream';
  }

  createCommunicationDetailFromData(comm: any): FormGroup {
    const attachmentsArray = this.fb.array([]);
    if (comm.rgTbCorReqAttachDtls && Array.isArray(comm.rgTbCorReqAttachDtls)) {
      comm.rgTbCorReqAttachDtls.forEach((att: any) => {
        attachmentsArray.push(this.fb.group({
          attachId: new FormControl(att.attachId != null ? att.attachId : null),
          fileName: new FormControl(att.fileName != null ? att.fileName : null),
          filePath: new FormControl(att.filePath != null ? att.filePath : null),
          createdOn: new FormControl(att.createdOn ? new Date(att.createdOn) : null),
          createdBy: new FormControl(att.createdBy != null ? att.createdBy : null)
        }));
      });
    }
    return this.fb.group({
      commId: new FormControl(comm.commId != null ? comm.commId : null),
      instId: new FormControl({ value: comm.instId != null ? comm.instId : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      sendDate: new FormControl({ value: comm.sendDate ? new Date(comm.sendDate) : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      replyDate: new FormControl({ value: comm.replyDate ? new Date(comm.replyDate) : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      remarks: new FormControl({ value: comm.remarks != null ? comm.remarks : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      decision: new FormControl({ value: comm.decision != null ? comm.decision : null, disabled: !this.hasPrivilege('REG_COR_REQ_APPROVE') }),
      reason: new FormControl({ value: comm.reason != null ? comm.reason : null, disabled: !this.hasPrivilege('REG_COR_REQ_APPROVE') }),
      tissueNo: new FormControl({ value: comm.tissueNo != null ? comm.tissueNo : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      receivedDate: new FormControl({ value: comm.receivedDate ? new Date(comm.receivedDate) : null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      attachments: attachmentsArray
    });
  }

  patchCommunicationDetails(commDtls: any[]): void {
    this.communicationDetails.clear();
    if (commDtls && Array.isArray(commDtls)) {
      commDtls.forEach((comm: any) => {
        const attachmentsArray = this.fb.array([]);
        if (comm.rgTbCorReqAttachDtls && Array.isArray(comm.rgTbCorReqAttachDtls)) {
          comm.rgTbCorReqAttachDtls.forEach((att: any) => {
            attachmentsArray.push(this.fb.group({
              attachId: new FormControl(att.attachId != null ? att.attachId : null),
              fileName: new FormControl(att.fileName != null ? att.fileName : null),
              filePath: new FormControl(att.filePath != null ? att.filePath : null),
              createdOn: new FormControl(att.createdOn ? new Date(att.createdOn) : null),
              createdBy: new FormControl(att.createdBy != null ? att.createdBy : null)
            }));
          });
        }
        this.communicationDetails.push(this.fb.group({
          commId: new FormControl(comm.commId != null ? comm.commId : null),
          instId: new FormControl(comm.instId != null ? comm.instId : null),
          sendDate: new FormControl(comm.sendDate ? new Date(comm.sendDate) : null),
          replyDate: new FormControl(comm.replyDate ? new Date(comm.replyDate) : null),
          remarks: new FormControl(comm.remarks != null ? comm.remarks : null),
          decision: new FormControl(comm.decision != null ? comm.decision : null),
          reason: new FormControl(comm.reason != null ? comm.reason : null),
          tissueNo: new FormControl(comm.tissueNo != null ? comm.tissueNo : null),
          receivedDate: new FormControl(comm.receivedDate ? new Date(comm.receivedDate) : null),
          attachments: attachmentsArray
        }));
      });
    }
  }
  createRequestingTeamFromData(data: any): FormGroup {
    return this.fb.group({
      runId: new FormControl(data.runId || null),
      reqStaff: new FormControl({ value: data.reqStaff || null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
      contactNo: new FormControl({ value: data.contactNo || null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, [Validators.required, Validators.pattern(/^\d{8,16}$/)]),
      remarks: new FormControl({ value: data.remarks || null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      staffRole: new FormControl({ value: data.staffRole || null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
    });
  }
  get requestingTissue(): FormArray {
    return this.transplantForm.get('requestingTissue') as FormArray;
  }

  get requestingTeam(): FormArray {
    return this.transplantForm.get('requestingTeam') as FormArray;
  }

  get paginatedRequestingTeam(): FormGroup[] {
    const start = (this.pageTeam - 1) * this.pageSizeTeam;
    const end = start + this.pageSizeTeam;
    return this.requestingTeam.controls.slice(start, end) as FormGroup[];
  }

  get paginatedTissues() {
    const start = (this.page3 - 1) * this.pageSize3;
    return this.requestingTissue.controls.slice(start, start + this.pageSize3);
  }

  createTissueGroup(tissue: CorneaTissue): FormGroup {
    return this.fb.group({
      runId: new FormControl(tissue.runId ? tissue.runId : null),
      tissueId: new FormControl({ value: tissue.tissueId ? tissue.tissueId : null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
      sizeZone: new FormControl({ value: tissue.sizeZone ? tissue.sizeZone : null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      remarks: new FormControl({ value: tissue.remarks ? tissue.remarks : '', disabled: !this.hasPrivilege('REG_COR_REQUEST') })
    });
  }

  createRequestingTeam(): FormGroup {
    return this.fb.group({
      runId: new FormControl(null),
      reqStaff: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required),
      contactNo: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, [Validators.required, Validators.pattern(/^\d{8,16}$/)]),
      remarks: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }),
      staffRole: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQUEST') }, Validators.required)
    });
  }

  private formatDateToYMD(date: any): string | null {
    if (!date) return null;
    const d = new Date(date);
    const year = d.getFullYear();
    const month = ('0' + (d.getMonth() + 1)).slice(-2);
    const day = ('0' + d.getDate()).slice(-2);
    return `${year}-${month}-${day}`;
  }

  save(): void {
    this.transplantForm.markAllAsTouched();
    this.transplantForm.updateValueAndValidity();

    this.requestingTeam.controls.forEach(ctrl => {
      ctrl.markAllAsTouched();
      ctrl.updateValueAndValidity();
    });

    this.requestingTissue.controls.forEach(ctrl => {
      ctrl.markAllAsTouched();
      ctrl.updateValueAndValidity();
    });

    const invalidContactNo = this.requestingTeam.controls.some(ctrl => {
      const contactNo = ctrl.get('contactNo').value;
      return contactNo && !/^\d{8,16}$/.test(contactNo);
    });
    if (invalidContactNo) {
      Swal.fire('Alert!', 'Contact numbers must be between 8 and 16 digits.', 'warning');
      return;
    }

    const invalidTeamMember = this.requestingTeam.controls.some(ctrl => ctrl.invalid);
    if (invalidTeamMember) {
      Swal.fire('Alert!', 'Please complete all fields in the Requesting Team rows before saving.', 'warning');
      return;
    }

    const invalidTissue = this.requestingTissue.controls.some(ctrl => ctrl.invalid);
    if (invalidTissue) {
      Swal.fire('Alert!', 'Please complete all fields in the Requesting Tissue rows before saving.', 'warning');
      return;
    }

    if (this.transplantForm.invalid) {
      Swal.fire('Alert!', 'Mandatory fields cannot be empty', 'warning');
      return;
    }

    if (this.requestingTissue.length === 0) {
      Swal.fire('Alert!', 'At least one Requesting Tissue is required.', 'warning');
      return;
    }

    // Use getRawValue() to include disabled fields in the payload
    const formValue = { ...this.transplantForm.getRawValue() };

    ['reqDate', 'intendedArrDate', 'intendedSurDate', 'createdDate'].forEach(field => {
      if (formValue[field]) {
        formValue[field] = this.formatDateToYMD(formValue[field]);
      }
    });

    if (formValue.requestingTissue && Array.isArray(formValue.requestingTissue)) {
      formValue.requestingTissue = formValue.requestingTissue.map((tissue: any) => {
        if (tissue.tissueId === 8) {
          return tissue;
        } else {
          return { ...tissue, sizeZone: null };
        }
      });
    }

    if (formValue.communicationDetails && Array.isArray(formValue.communicationDetails)) {
      formValue.communicationDetails = formValue.communicationDetails.map((comm: any) => {
        return {
          ...comm,
          sendDate: this.formatDateToYMD(comm.sendDate),
          replyDate: this.formatDateToYMD(comm.replyDate),
          receivedDate: this.formatDateToYMD(comm.receivedDate)
        };
      });
    }

    const { requestingTissue, requestingTeam, communicationDetails, ...rest } = formValue;

    const savePayload: CorneaRequest = {
      ...rest,
      rgTbCorReqDoctors: requestingTeam,
      rgTbCorReqTissues: requestingTissue,
      rgTbCorReqCommDtls: communicationDetails
    };
    this._corneaService.saveCorneaRequest(savePayload, this.deletedAttachmentIds).subscribe(
      res => {
        if (res['code'] === '0') {
          Swal.fire('Success!', 'Cornea Request saved successfully.', 'success');
          const data = res['result'];
          const commIds: number[] = data.commIds;
          const allFiles: { file: File, commId: number }[] = [];
          for (let i = 0; i < commIds.length; i++) {
            const filesForRow = this.uploadedFiles[i];
            const commId = commIds[i];
            if (filesForRow && filesForRow.length) {
              for (const file of filesForRow) {
                allFiles.push({ file, commId });
              }
              this.uploadedFiles[i] = [];
            }
          }
          if (allFiles.length) {
            this.uploadAttachmentsWithCommId(allFiles, data.requestId, data.requestNo);
          } else {
            this.clearForm();
            this.deletedAttachmentIds = [];
            this.getList(data.requestNo);
          }
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      },
      () => Swal.fire('Error!', 'An error occurred while saving data.', 'error')
    );
  }

  saveInstitute(modalRef: any): void {
    const newName = (this.instForm.value.instName || '').trim().toLowerCase();
    const isDuplicate = this.reqInstitutesFilter && this.reqInstitutesFilter.some(
      (inst: any) => (inst.instName || '').trim().toLowerCase() === newName
    );
    if (isDuplicate) {
      Swal.fire('Alert!', 'Institute Name already exists. Please use another name.', 'warning');
      return;
    }
    const payload = {
      instId: this.instForm.value.instId || 0,
      instName: this.instForm.value.instName,
      instAddress: this.instForm.value.instAddress,
      activeYn: this.instForm.value.activeYn
    };
    this._masterService.saveCorReqInstitutes(payload).subscribe(
      res => {
        if (res['code'] === '0') {
          Swal.fire('Success!', 'Cornea Request Institute saved successfully.', 'success');
          this.loadInstitutes();
          this.instForm.reset();
          modalRef.close();

          this.selectedInstitute = res['result'];

          setTimeout(() => {
            const requestToCtrl = this.transplantForm.get('requestTo');
            if (requestToCtrl) {
              requestToCtrl.setValue(this.selectedInstitute.instId);
            }

            if (this.communicationDetails.length > 0) {
              const lastComm = this.communicationDetails.at(this.communicationDetails.length - 1);
              if (lastComm && lastComm.get('instId')) {
                lastComm.get('instId').setValue(this.selectedInstitute.instId);
              }
            }
          }, 0);

          this.cdr.detectChanges();
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      },
      (error) => {
        Swal.fire('Error!', 'An error occurred while saving data.', error);
      }
    );
  }

  addRequestingTeam(): void {

    if (this.requestingTeam.length > 0) {
      const lastTeamMember = this.requestingTeam.at(this.requestingTeam.length - 1);
      if (!lastTeamMember.valid) {
        Swal.fire('Alert!', 'Please fill out the current Requesting Team member before adding another.', 'warning');
        return;
      }
    }

    this.requestingTeam.push(this.createRequestingTeam());
    this.pageTeam = Math.ceil(this.requestingTeam.length / this.pageSizeTeam);
    this.applyPrivilegeStates();
  }

  removeRequestingTeam(index: number): void {
    this.requestingTeam.removeAt(index);
    if ((this.pageTeam - 1) * this.pageSizeTeam >= this.requestingTeam.length && this.pageTeam > 1) {
      this.pageTeam--;
    }
  }

  addRequestingTissue(): void {
    if (this.requestingTissue.length > 0) {
      const lastTissue = this.requestingTissue.at(this.requestingTissue.length - 1);
      if (!lastTissue.valid) {
        Swal.fire('Alert!', 'Please fill out the current Requesting Tissue before adding another.', 'warning');
        return;
      }
    }

    this.requestingTissue.push(this.createTissueGroup({ runId: null, tissueId: null, sizeZone: null, remarks: '' }));
    const totalItems = this.requestingTissue.length;
    this.page3 = Math.ceil(totalItems / this.pageSize3);
    this.subscribeTissueIdChanges();
    this.applyPrivilegeStates();
  }

  removeRequestingTissue(index: number): void {
    this.requestingTissue.removeAt(index);
    if ((this.page3 - 1) * this.pageSize3 >= this.requestingTissue.length && this.page3 > 1) {
      this.page3--;
    }
  }

  openModalForEdit(content: any, instId?: any): void {
    this.isEdit = true;
    let institute = this.selectedInstitute;
    if (instId !== undefined && this.reqInstitutesFilter) {
      institute = this.reqInstitutesFilter.find((inst: any) => inst.instId === instId);
    }
    const instIdVal = institute && institute.instId ? institute.instId : 0;
    const instNameVal = institute && institute.instName ? institute.instName : '';
    const instAddressVal = institute && institute.instAddress ? institute.instAddress : '';
    const activeYnVal = institute && institute.activeYn ? institute.activeYn : 'Y';
    this.instForm.reset({
      instId: instIdVal,
      instName: instNameVal,
      instAddress: instAddressVal,
      activeYn: activeYnVal
    });
    this.modalService.open(content, { backdrop: 'static' });
  }

  openModal(content: any): void {
    this.isEdit = false;
    this.instForm.reset({
      instId: 0,
      instName: '',
      instAddress: '',
      activeYn: 'Y'
    });
    this.modalService.open(content, { backdrop: 'static' });
  }

  onInstituteSelect(selected: any): void {
    this.selectedInstitute = selected;
  }

  clearForm(): void {
    this.transplantForm.reset();
    this.requestingTissue.clear();
    this.requestingTeam.clear();
    this.communicationDetails.clear();
    //this.selectedInstitute = null;
    this.instForm.reset();
    this.page3 = 1;
    this.pageTeam = 1;
  }

  get communicationDetails(): FormArray {
    return this.transplantForm.get('communicationDetails') as FormArray;
  }

  addCommunicationDetail(): void {
    if (this.communicationDetails.length > 0) {
      const lastComm = this.communicationDetails.at(this.communicationDetails.length - 1);
      const lastInstId = lastComm.get('instId') ? lastComm.get('instId').value : null;
      if (!lastInstId) {
        Swal.fire('Alert!', 'Please select Institute Name in the previous row before adding a new one.', 'warning');
        return;
      }
    }
    const group = this.fb.group({
      commId: new FormControl(),
      instId: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      replyDate: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      sendDate: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      remarks: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      document: new FormControl(),
      decision: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_APPROVE') }),
      reason: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_APPROVE') }),
      tissueNo: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      receivedDate: new FormControl({ value: null, disabled: !this.hasPrivilege('REG_COR_REQ_CDNTR') }),
      attachments: this.fb.array([])
    });

    this.communicationDetails.push(group);
    setTimeout(() => {
      this.cdr.detectChanges();
      window.dispatchEvent(new Event('resize'));
    }, 0);
    this.applyPrivilegeStates();
  }

  viewDocument(i: number, fileViewModal: any) {
    this.selectedFileIndex = i;
    this.modalService.open(fileViewModal, { size: 'lg' });
  }

  attachDocument(i: number, fileAttachModal: any) {
    this.selectedFileIndex = i;
    this.modalService.open(fileAttachModal, { size: 'lg' });
  }

  onFileSelected(event: any, rowIndex: number, fileInput: any): void {
    const files: FileList = event.target.files;
    if (!files || files.length === 0) return;

    if (!this.uploadedFiles) this.uploadedFiles = [];
    if (!this.uploadedFiles[rowIndex]) this.uploadedFiles[rowIndex] = [];

    const MAX_SIZE = 20 * 1024 * 1024;
    //let nameConflict = false;
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const nameExists = this.uploadedFiles[rowIndex].some(
        (f: any) => f.name === file.name
      );
      if (nameExists) {
        // nameConflict = true;
        continue;
      }

      // Only allow image and PDF files
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid file type',
          text: 'Only image and PDF files are allowed.'
        });
        continue;
      }

      if (file.size > MAX_SIZE) {
        Swal.fire({
          icon: 'error',
          title: 'File too large',
          text: `File "${file.name}" exceeds the 20 MB size limit.`
        });
        continue;
      }
      this.uploadedFiles[rowIndex].push(file);
    }
    if (fileInput) fileInput.value = '';

    // if (nameConflict) {
    //   Swal.fire({
    //     icon: 'warning',
    //     title: 'File Name Conflict',
    //     text: 'A file with the same name already exists in this row. Please select another file.'
    //   });
    // }


    if (files.length > 0) {
      Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'success',
        title: 'File(s) selected successfully',
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true
      });
    }
  }

  get showSizeZoneHeader(): boolean {
    return this.paginatedTissues.some(row => row.get('tissueId').value === 8);
  }

  openInNewTab(): void {
    if (this.viewedFileUrl) {
      window.open(this.viewedFileUrl, '_blank');
    }
  }

  search(): void {
    if (!this.requestNo) {
      Swal.fire({ icon: 'warning', title: 'Please enter Request ID' });
      return;
    }
    this.clear();
    this.getList(this.requestNo);
    this.requestNo = '';
    this.isEnabled = true;

  }
  clear() {
  }

  getFileUrl(file: any): SafeUrl {
    if (file instanceof File) {
      return this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(file));
    }
    else if (file.backend && file.filePath) {
      if (file._previewUrl) {
        return this.sanitizer.bypassSecurityTrustUrl(file._previewUrl);
      }
      this._corneaService.previewFile(file.filePath).subscribe((blob: Blob) => {
        file._previewUrl = URL.createObjectURL(blob);
      });
      return '';
    }
    return '';
  }

  onPreviewFile(file: any): void {
    console.log('Preview file:', file);
  }

  deleteUploadedFile(selectedFileIndex: number, fileIdx: number): void {
    if (
      this.uploadedFiles &&
      this.uploadedFiles[selectedFileIndex] &&
      this.uploadedFiles[selectedFileIndex].length > fileIdx
    ) {
      const file = this.uploadedFiles[selectedFileIndex][fileIdx];
      if (file && file.attachId) {
        this.deletedAttachmentIds.push(file.attachId);
      }
      this.uploadedFiles[selectedFileIndex].splice(fileIdx, 1);
    }
  }

  uploadAttachmentsWithCommId(filesWithCommId: { file: File, commId: number }[], requestId: number, requestNo?: string): void {
    const formData = new FormData();
    filesWithCommId.forEach(entry => {
      if (entry.file instanceof File) {
        formData.append('files', entry.file, entry.file.name);
        formData.append('commIds', entry.commId.toString());
      }
    });
    formData.append('requestId', requestId != null ? requestId.toString() : '');

    this._corneaService.uploadCorneaAttachment(formData).subscribe(
      (res: any) => {
        this.clearForm();
        if (requestNo) {
          this.getList(requestNo);
        }
      },
      (err: any) => {
        Swal.fire('Error!', 'Failed to upload attachment(s).', 'error');
        this.deletedAttachmentIds = [];
        this.clearForm();
        if (requestNo) {
          this.getList(requestNo);
        }
      }
    );
  }


  onDecisionRadioChange(rowIndex: number, value: string, checked: boolean): void {
    const commArray = this.communicationDetails;
    const row = commArray.at(rowIndex);
    if (checked) {
      row.get('decision').setValue(value);
      if (value === 'A') {
        let found = false;
        commArray.controls.forEach((ctrl, idx) => {
          if (idx !== rowIndex && ctrl.get('decision').value === 'A') {
            found = true;
          }
        });
        if (found) {
          row.get('decision').setValue('R');
          Swal.fire({
            icon: 'warning',
            title: 'Alert!',
            text: 'Only one row can be marked as Accepted.'
          });
        } else {
          commArray.controls.forEach((ctrl, idx) => {
            if (idx !== rowIndex && ctrl.get('decision').value === 'A') {
              ctrl.get('decision').setValue(null);
            }
          });
        }
      }
    } else {
      if (row.get('decision').value === value) {
        row.get('decision').setValue(null);
      }
    }
  }

  getInstituteAddress(instId: any): string {
    if (!instId || !this.reqInstitutesFilter) return '';
    const found = this.reqInstitutesFilter.find((inst: any) => inst.instId === instId);
    return found ? found.instAddress : '';
  }


  sendSMSMsgDr() {
    const reqInstId = this.transplantForm.get('reqInst').value;
    const requestId = this.transplantForm.get('requestId').value;
    let isRequestSent: boolean = false;

    this._corneaService.getCorReqSendDtlsByRequestId(requestId).subscribe(
      response => {
        isRequestSent = response.result;
        if (isRequestSent) {
          Swal.fire({
            title: 'Already request Sent, Want to send again ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
          }).then((result) => {
            if (result.isConfirmed) {
              this._sendSMSAndEmail(reqInstId);
            }
          });
        } else {
          this._sendSMSAndEmail(reqInstId);
        }
      },
      error => {
        console.error('Error fetching request send details:', error);
      }
    );
  }

  private _sendSMSAndEmail(reqInstId: any): void {
    let establishment = '';

    if (reqInstId && this.instituteListFilter && this.instituteListFilter.length) {
      const found = this.instituteListFilter.find((inst: any) => inst.estCode === reqInstId);
      establishment = found ? found.estName : '';
    }

    let tissueNames: string[] = [];
    if (this.requestingTissue && this.requestingTissue.length > 0) {
      for (let i = 0; i < this.requestingTissue.length; i++) {
        const tId = this.requestingTissue.at(i).get('tissueId').value;
        if (tId && this.corTissueTypeFilter && this.corTissueTypeFilter.length) {
          const tissueObj = this.corTissueTypeFilter.find((t: any) => t.paramId === tId);
          if (tissueObj && tissueObj.paramName) {
            tissueNames.push(tissueObj.paramName);
          }
        }
      }
    }
    let tissueName = tissueNames.join(', ');

    this._corneaService.getSmsNotificationBySmsType().subscribe(
      response => {
        let notificationData = response.result;
        let mobileNumbers: string[] = [];
        let emailList: string[] = [];
        if (Array.isArray(notificationData)) {
          mobileNumbers = notificationData
            .map((item: any) => item.mobileNo)
            .filter((num: any) => !!num);
          emailList = notificationData
            .map((item: any) => item.email)
            .filter((email: any) => !!email);
        } else if (notificationData) {
          if (notificationData.mobileNo) {
            mobileNumbers = [notificationData.mobileNo];
          }
          if (notificationData.email) {
            emailList = [notificationData.email];
          }
        }

        const mobileNumbersStr = mobileNumbers.join(',');

        this._corneaService.getRgTbSmsMsgBySmsType().subscribe(
          response2 => {
            let message = response2 && response2.result && response2.result.emailBodyEn ? response2.result.emailBodyEn : '';
            if (typeof message === 'string' && message.length > 0) {
              let mapObjDr = {
                establishment: establishment,
                tissueName: tissueName
              };

              message = message
                .replace(/{tissueName}/gi, mapObjDr.tissueName)
                .replace(/{establishment}/gi, mapObjDr.establishment)
                .replace(/\btissueName\b/gi, mapObjDr.tissueName)
                .replace(/\bestablishment\b/gi, mapObjDr.establishment);

              // Send SMS to each mobile number
              let smsSendCount = 0;
              let smsError = false;
              let emailError = false;
              let smsDone = false;
              let emailDone = false;
              let errorShown = false;

              const trySaveCorReqSendDtlsAndShowResult = (smsSuccess: boolean, emailSuccess: boolean) => {
                // Save send details before showing result
                const requestId = this.transplantForm.get('requestId').value;
                const mobileNumbersStr = mobileNumbers.join(',');
                let dto = {
                  runId: null,
                  requestId: requestId,
                  sentDate: new Date(),
                  sentToEmail: emailList.length && emailSuccess ? emailList.join(',') : null,
                  sentToGsm: mobileNumbersStr && smsSuccess ? mobileNumbersStr : null
                };
                
                this._corneaService.saveCorReqSendDtls(dto).subscribe(
                  res => {
                    // Show appropriate success/error message based on what succeeded
                    if (smsSuccess && emailSuccess) {
                      Swal.fire('Success!', 'Email and SMS sent successfully.', 'success');
                    } else if (smsSuccess && !emailSuccess) {
                      Swal.fire('Partial Success!', 'SMS sent successfully, but email failed to send.', 'warning');
                    } else if (!smsSuccess && emailSuccess) {
                      Swal.fire('Partial Success!', 'Email sent successfully, but SMS failed to send.', 'warning');
                    } else {
                      Swal.fire('Error!', 'Failed to send both Email and SMS.', 'error');
                    }
                  },
                  err => {
                    // Show result even if saving send details failed
                    if (smsSuccess && emailSuccess) {
                      Swal.fire('Warning!', 'Email and SMS sent successfully, but failed to save send details.', 'warning');
                    } else if (smsSuccess && !emailSuccess) {
                      Swal.fire('Warning!', 'SMS sent successfully, but email failed. Also failed to save send details.', 'warning');
                    } else if (!smsSuccess && emailSuccess) {
                      Swal.fire('Warning!', 'Email sent successfully, but SMS failed. Also failed to save send details.', 'warning');
                    } else {
                      Swal.fire('Error!', 'Failed to send both Email and SMS, and failed to save details.', 'error');
                    }
                  }
                );
              };

              const checkAndShowResult = () => {
                if (smsDone && emailDone && !errorShown) {
                  const smsSuccess = !smsError;
                  const emailSuccess = !emailError;
                  trySaveCorReqSendDtlsAndShowResult(smsSuccess, emailSuccess);
                  errorShown = true;
                }
              };

              // Send SMS to each mobile number
              if (mobileNumbers.length === 0) {
                smsDone = true;
                checkAndShowResult();
              }
              mobileNumbers.forEach(mobileNo => {
                var smsMessageDr: any = {};
                smsMessageDr.msgCatId = "CORNEA";
                smsMessageDr.msgLang = "E";
                smsMessageDr.msgBody = message;
                smsMessageDr.msgInstId = reqInstId;
                smsMessageDr.msgSender;
                smsMessageDr.msgToMobile = mobileNo;

                if (this.isNotEmpty(smsMessageDr)) {
                  this._masterService.getSMSWebService(smsMessageDr).subscribe(
                    () => {
                      smsSendCount++;
                      if (smsSendCount === mobileNumbers.length && !smsError) {
                        smsDone = true;
                        checkAndShowResult();
                      }
                    },
                    error => {
                      smsError = true;
                      smsDone = true;
                      checkAndShowResult();
                    }
                  );
                } else {
                  smsSendCount++;
                  if (smsSendCount === mobileNumbers.length && !smsError) {
                    smsDone = true;
                    checkAndShowResult();
                  }
                }
              });

              // Send Email
              const emailDto = {
                sender: '<EMAIL>',
                recipientList: emailList,
                ccList: [],
                subject: response2.result.note,
                content: message,
              };
              if (emailList.length === 0) {
                emailDone = true;
                checkAndShowResult();
              } else {
                this._corneaService.sendEmails(emailDto).subscribe(
                  () => {
                    emailDone = true;
                    checkAndShowResult();
                  },
                  error => {
                    emailError = true;
                    emailDone = true;
                    checkAndShowResult();
                  }
                );
              }
            } else {
              Swal.fire('Error!', 'SMS template message is empty or not found.', 'error');
            }
          },
          () => {
            Swal.fire('Error!', 'Failed to load SMS template.', 'error');
          }
        );
      },
      () => {
        Swal.fire('Error!', 'Failed to load notification data.', 'error');
      }
    );
  }
  isNotEmpty(obj) {
    for (var key in obj) {
      if (obj[key] === null || obj[key] == '')
        return false;
    }
    return true;
  }

  hasPrivilege(privilege: any) {
    return this._sharedService.hasPrivilege(privilege);
  }
}