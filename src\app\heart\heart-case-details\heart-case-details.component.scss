.case-details-panel {
    margin: 0 8px;

    .card {
        margin-bottom: 10px;

        .card-header {
            color: #973633;
            font-family: "open_sanssemibold";
            background: #fff;
            padding: 0.55rem 1.25rem;
        }
    }
}

.min-height-placeholder {
    min-height: 200px; /* Adjust this value based on your needs */
  }


  .mcard-header {
    font-size: 14px;
    padding: 5px 10px;
    background: #f7f7f7;
    font-weight: bold;
    color: #8f8f8f;
  }
  

.procedure-types {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .form-check-inline {
      margin-right: 1.5rem;
    }
  }
  
  .card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12);
    
    .card-header {
      background-color: #f8f9fa;
      font-weight: 500;
    }
  }

.checklist {
    padding-bottom: 5px;

    input {
        margin-right: 10px;
    }
}

.lbl {
    display: inline-block;
    min-width: 100px; 
    word-break: break-word;
    margin-right: 10px; 
}

.w-40 {
    width: 40% !important;
}

.remarkText {
    display: inline-block;
    margin-left: 10px;
    min-width: 30%;
    margin-bottom: 3px;
}

.card {
    &.h-100 {
        height: 300px; // Set your desired height here
        overflow: hidden; // Prevent overflow from affecting the layout
    }

    .complication-item {
        margin-bottom: 0rem !important;// Adjust this value to reduce height as needed
    }
    
    .ui-input-group {
        padding: 0.25rem; // Adjust this value to reduce height as needed
    }
    .complication-item {
        display: flex;
        flex-direction: column; 
    }
    
    .d-flex {
        display: flex; 
        align-items: center; 
    }

    .card-body {
        height: 100%; // Ensure the card body takes the full height of the card
        overflow-y: auto; // Allow vertical scrolling within the card body

        &.p-0 {
            .row {
                margin: 0;
                
                .col-6 {
                    padding: 0.5rem;
                    
                    .list-item {
                        margin-bottom: 0.5rem;
                        
                        .checklist {
                            margin-bottom: 0.5rem;
                            
                            .flex-grow-1 {
                                min-width: 0; // Prevents flex item from overflowing
                                
                                input.form-control {
                                    width: 100%;
                                    max-width: 150px; // Adjust this value based on your needs
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.half-height {
    height: 50%; /* Set height to 50% */
    overflow-y: auto; /* Allow scrolling if content overflows */
}

.card-min-ht-2 {
    min-height: 228px;
}

.card-min-fh-ht-2 {
    min-height: 98px;
}

.card-min-ht-3 {
    min-height: 370px;
}

.list-item> {
    li {
        padding-bottom: 5px;
    }
}

.list-com> {
    li {
        border-bottom: 1px solid #f5f5f5;
        padding: 5px 0;

        &:last-child {
            border: 0;
        }

    }
}

.list-item-blk> {
    li {
        padding: 10px;
        border: 1px solid #f5f5f5;
    }
}

.sub-list {
    background: #f7f7f7;
    padding: 10px;
    margin: 5px 0;
    border-radius: 3px;
}

.specify-control {
    display: inline-block;
    margin-left: 10px;
}

.min-w-260 {
    min-width: 260px;
}

.sub-items {
    margin: 10px;

    li {
        padding-bottom: 4px;
    }
}

.w-sm {
    width: 50px
}

.fade-txt {
    color: #999;
}

.click-link {
    cursor: pointer;
    transition: .3s linear;

    &:hover {
        background-color: #e9e9e9;
    }
}

.arrw {
    float: right;
    color: #b9b6b6;
}



.number-input-sm {
    width: 70px !important;
    text-align: center;
}

.ui-input-group {
    flex-grow: 1; 
}

::ng-deep {
        .currentMgmt {
                h6 {
                        background: #f7f7f7;
                        padding: 4px 8px;
                        border-radius: 3px;
                        border: 1px solid #efefef;
                        font-size: 14px;
                }

        // p-calendar {
        //     .ui-calendar {
        //         height: auto;

        //         .ui-inputtext {
        //             height: 26px;
        //             padding: 0 6px;
        //             border-color: #c7c7c7 !important;
        //         }
        //     }

        // }

        .accordion {
            column-count: 2;

            .accordion-item {
                break-inside: avoid-column;
            }
        }
    }
}


    .checklist {
  margin-bottom: 1rem;

  .d-flex {
    gap: 1rem;
    align-items: center;
  }

  .ng-select {
    min-width: 50px;
  }

  label {
    margin-bottom: 0;
  }
}

// ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
//   background-color: #e9ecef;
//   padding: 2px 5px;
//   margin-right: 5px;
//   border-radius: 3px;
// }

.list-item {
      li {
    margin-bottom: 0.5rem;
    padding: 8px 0;

    .d-flex {
      gap: 12px;
      align-items: center;
    }

    label {
      margin-bottom: 0;
    }

    .form-control {
      &:disabled {
        background-color: #e9ecef;
        opacity: 0.7;
      }
    }
  }
  .checklist {
    padding: 0.25rem 0;
    
    &:not(:last-child) {
      margin-bottom: 0.5rem;
    }

    .d-flex {
      gap: 0.5rem;
      align-items: center;
    }

    label {
      margin-bottom: 0;
      font-weight: normal;
    }
  }
  .indication-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .checkbox-wrapper {
      display: flex;
      align-items: center;
      padding: 4px 0;

      .form-check-input {
        margin-top: 0;
      }

      .form-check-label {
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .select-wrapper {
      padding-left: 24px; // Aligns with checkbox label
    }
  }
}

textarea.form-control-sm {
  min-height: 31px;
  resize: vertical;
}

.form-control:disabled {
  cursor: not-allowed;
}

.select-container {
  .fixed-width-select {
    width: 300px; // Adjust this value based on your needs
  }
}

// ::ng-deep {
//   .ng-select {
//     &.ng-select-disabled {
//       .ng-select-container {
//         background-color: #e9ecef;
//         cursor: not-allowed;
//       }
//     }

//     &.ng-select-multiple {
//       .ng-select-container {
//         min-height: 32px;
        
//         .ng-value-container {
//           .ng-value {
//             background-color: #e9ecef;
//             padding: 2px 5px;
//             margin: 2px;
//             border-radius: 3px;
//           }
//         }
//       }
//     }
//   }
// }

::ng-deep {
  .common-select {
    .ng-select-container {
      min-height: 32px;
      
    }

    &.ng-select-disabled {
      opacity: 0.7;
    }

    .ng-value-container {
      padding: 2px 8px;
    }

    .ng-value {
      margin-right: 4px;
      background-color: #e9ecef;
      padding: 2px 6px;
      border-radius: 3px;
    }
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  min-width: 200px;  // Adjust this value based on your needs

  .form-check-input {
    margin-right: 10px;
  }

  .form-check-label {
    margin-bottom: 0;
    white-space: nowrap;
  }
}

.indication-item {
  padding: 4px 0;
  
  .d-flex {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .checkbox-column {
    width: 200px; // Adjust based on your needs
    padding-top: 5px;

    .form-check-input {
      margin-right: 8px;
    }

    .form-check-label {
      margin-bottom: 0;
      white-space: nowrap;
    }
  }

  .select-column {
    flex: 1;
    max-width: 250px; // Fixed width for all select boxes
  }
}

.checklist-item {
  padding: 0.5rem 0;
  
  .d-flex {
    gap: 1rem;
    align-items: center;
  }

  .checkbox-wrapper {
    min-width: 200px; // Adjust based on your longest label
    
    .form-check {
      margin: 0;
      
      .form-check-input {
        margin-top: 0;
      }

      .form-check-label {
        margin-bottom: 0;
        white-space: normal;
        line-height: 1.2;
      }
    }
  }

  .select-wrapper {
    .compact-select {
      min-width: 200px;
      
      ::ng-deep .ng-select-container {
        min-height: 32px;
      }
    }
  }
}
