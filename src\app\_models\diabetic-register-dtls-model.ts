
export class TbDiabeticRegisterDtls{
	public  diabetesId: number;
	public  civilID: number;
	public  diabetesType: number;
	public  duration: string;
	public  durationType: number;
	public  instPatientId: number;
	public  regDate: Date;
	public  regNo: string;
	public  regType: string;
	public  startYear: Date;
	public  diabetesSubType: number;
	public  modifiedDate: Date;
    public  modifiedBy: number;
	public  createdDate: Date;
	public  createdBy: number;
	public  diagnosedDate: Date;
	public  fileStatus: number;
  rgTbDiabFamilyHistory: any[];

}