import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { AncDashboard } from '../../_models/anc-dashboard.model';
import { AncDashboardDisplay } from '../../_models/anc-dashboard-display.model';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from '../../_helpers/common.constants';
import * as _ from 'lodash';
import { AncService } from '../anc.service';

@Component({
  selector: 'app-anc-dashboard',
  templateUrl: './anc-dashboard.component.html',
  styleUrls: ['./anc-dashboard.component.scss']
})
export class AncDashboardComponent implements OnInit {

  
  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: AncDashboard[];
  DashboardDataFilter: AncDashboard[];
  displayAncInstitute: AncDashboardDisplay[];
  displayCongAnamolies: AncDashboardDisplay[];
  displayAnaemic : AncDashboardDisplay[];
  displayVteRiskScore : {centralRegNo: number , value: number;}[];
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  ancInstituteChart: any;
  congAnamoliesPieChart: any;
  anaemicPieChart: any;
  vteRiskScoreBarChart: any;
  createTime: any;

  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _ancService: AncService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'dateGiven': [null]
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._ancService.getAncDashboard().subscribe(res => {

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {


        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'ANC', this.institeList);

      }

      this.callFilter();
    })

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {



      let body = this.boardForm.value;

      this.DashboardDataFilter = this.DashboardDataDB;

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regInst == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

displayFilterType(){
  
  if (this.filterType === "institute") {
    this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['regInst']).map(s => s.estName);
    
  } else if (this.filterType === "wilayat") {
    this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
 
  } else if (this.filterType === "region") {
    this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

  }
  else {
    this.filterTitle = 'All Regions';
  }

}
  setChartData() {
    this.displayAncInstitute =  [];
    this.displayCongAnamolies =  [];
    this.displayAnaemic = [];
    this.displayVteRiskScore = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {
      let ancInstitute = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: (this.institeList.find(s => s.estCode == this.DashboardDataFilter[i].ancInstitute))? (this.institeList.find(s => s.estCode == this.DashboardDataFilter[i].ancInstitute)).estName: null};
      this.displayAncInstitute.push(ancInstitute);

      let congAnamolies = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].congAnamoliesYn };
      this.displayCongAnamolies.push(congAnamolies);

      let anaemic = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].anaemic };
      this.displayAnaemic.push(anaemic);

      let vteRiskScore = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: (this.DashboardDataFilter[i].vteRiskScore) };
      this.displayVteRiskScore.push(vteRiskScore);
      
    }
    
    this.callChart();
  }

  callChart() {
    this.callAncInstitutePieChart(this.DashboardDataFilter);
    this.callPieChartYn("this.congAnamoliesPieChart");
    this.callPieChartYn("this.anaemicPieChart");
    this.callVteRiskScoreBarChart();

  }

  groupDaysByRange(days: string[], rangeSize: number): string[][] {
    let groupedDays: string[][] = [];
    for (let i = 0; i < days.length; i += rangeSize) {
        groupedDays.push(days.slice(i, i + rangeSize));
    }
    return groupedDays;
}

  callVteRiskScoreBarChart() {
    let charlabels = ['Less Than 3','Equal 3','Grater Than or Equal 4'];
    let charData = [0,0,0];
   
    let chartArray = [];
    
    this.displayVteRiskScore = this.displayVteRiskScore.filter(s => s.value != null);

    chartArray =  this.displayVteRiskScore

    chartArray.forEach(el=>{;
      if (el.value >0){
        if (el.value < 3){
          charData[0] = charData[0] + 1
        }
        else if (el.value == 3){
          charData[1] = charData[1]+ 1
        }
        else {
          charData[2] = charData[2] + 1
        }
      }
    })
    this.charBGColor.sort(() => Math.random() - 0.2);
    this.vteRiskScoreBarChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }

      ]
    }
  }

  callPieChartYn(chartData: any) {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    if (chartData == 'this.congAnamoliesPieChart'){

      this.displayCongAnamolies = this.displayCongAnamolies.filter(s => s.value != null);

      for (var n = 0; n < this.displayCongAnamolies.length; n++) {
        if (listGroup.filter(s => s.congAnamoliesYn === this.displayCongAnamolies[n].value).length == 0) {
          const result = this.displayCongAnamolies.filter(s => s.value == this.displayCongAnamolies[n].value).length;
          let a = { congAnamoliesYn: this.displayCongAnamolies[n].value }
          charlabels.push(this.displayCongAnamolies[n].value== 'Y'? 'Yes':'No');
          charData.push(result);
          listGroup.push(a);
        }
      }

      this.charBGColor.sort(() => Math.random() - 0.2);
      this.congAnamoliesPieChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }

      ]
    }
    }

    if (chartData == 'this.anaemicPieChart'){

      this.displayAnaemic = this.displayAnaemic.filter(s => s.value != null);

      for (var n = 0; n < this.displayAnaemic.length; n++) {
        if (listGroup.filter(s => s.anaemic === this.displayAnaemic[n].value).length == 0) {
          const result = this.displayAnaemic.filter(s => s.value == this.displayAnaemic[n].value).length;
          let a = { anaemic: this.displayAnaemic[n].value }
          charlabels.push(this.displayAnaemic[n].value== 'Y'? 'Yes':'No');
          charData.push(result);
          listGroup.push(a);
        }
      }

      this.charBGColor.sort(() => Math.random() - 0.2);
      this.anaemicPieChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }

      ]
    }
    }
  }

  callAncInstitutePieChart(listData: any[]) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";

    

    if (this.filterType === "institute") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.ancInstitute).map(s => s.estCode), "label": est.filter(s => s.estCode == a.ancInstitute).map(s => s.estName), "count": data.filter(s => s.ancInstitute == a.ancInstitute).length };
        if (groupByName.filter(s => s.label == a.ancInstitute).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.ancInstitute).map(s => s.estCode), "label": est.filter(s => s.estCode == a.ancInstitute).map(s => s.estName), "count": data.filter(s => s.ancInstitute == a.ancInstitute).length };
        if (groupByName.filter(s => s.id == a.ancInstitute).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    groupByName = groupByName.filter(s => s.id.length);

    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);
      this.ancInstituteChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };



  }
  /* ------------  call Chart ---------------- */

}
