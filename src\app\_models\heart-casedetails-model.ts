
export interface HeartRegister {
  heartId: number;
  centralRegNo: number;
  familyHistYn: string;
  smokingYn: string;
  cigaretteQuitDate: Date;
  substanceUseYn: string;
  substanceQuitDate: Date;
  psychosocialAssessment: string;
  caregiverDtls: string;
  primaryDiag: number;
  diagOthers: string;
  duration: string;
  nyhaClass: number;
  inotropeYn: string;
  inotropeOther: string;
  mcsDeviceUsed: string;
  mcsDeviceOther: string;
  mcsInitiatedDate: Date;
  recTransYn: string;
  recTransReason: string;
  urgentTransYn: string;
  urgentTransReason: string;
  psychosocialClearance: number;
  dieticianReview: string;
  physiotherapyBase: number;
  financialInsuranceYn: string;
  rgTbHeartMcsDtls: HeartMcsDtls[];
  rgTbHeartInotropeDtls: HeartInotropeDtls[];
}

export interface HeartMcsDtls {
  heartId: number;
  transId: number;
  mcsId: number;
  remarks: string;
}

export interface HeartInotropeDtls {
  heartId: number;
  transId: number;
  inotropeAgentId: number;
  remarks: string;
}


export interface FamilyHistory {
  hasHistory: boolean;
  remarks: string;
}


export interface RgProcedureDetailsDto {
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface RgProcedureDetailsSaveDto {
  runId: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface ContactDetails {
  relationType: number;
  phone: number;
  email: string;
}

export interface ProcedureDetails {
  //runId: number;
  //centralRegNo: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface Medication {
  id: number;
  medicine: string;
  startDate: Date;
  dose: string;
  frequency: string;
  medicineType: string;
  isEditable: boolean;
}


export interface RgVwLiverProceduresDto {
  paramId: number;
  paramName: string;
}

export interface RgVwLiverDonorProceduresDto {
  procId: number;
  procedureName: string;
  shortName: string;
  active:string
}

export interface RgProcedureDetailsDto {
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface RgProcedureDetailsSaveDto {
  runId: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface ContactDetails {
  relationType: number;
  phone: number;
  email: string;
}

export interface ProcedureDetails {
  //runId?: number;
  //centralRegNo: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}