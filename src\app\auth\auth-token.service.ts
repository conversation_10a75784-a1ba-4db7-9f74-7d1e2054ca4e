import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { AuthenticationService } from '../_services/authentication.service';
import { Injectable, Component, EventEmitter } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpClient, HttpResponse,
          HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as CommonConstants from '../_helpers/common.constants';
import { Router } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import Swal from 'sweetalert2';

@Injectable()
export class AuthTokenService {

  cachedRequests: Array<HttpRequest<any>> = [];

  constructor(private router: Router, private http: HttpClient, private auth: AuthenticationService, 
  private toastr: ToastrService,  private spinner: NgxSpinnerService) {

  }

  /**
   * To refresh the access token.
   * @returns returns the new refresh token on success.
   */
  refreshToken(): Observable<any> {
    const client_id = CommonConstants.CLIENT_ID;
    const client_secret = 'secret';
    const basicheader = btoa(client_id + ':' + client_secret);
    const headers = new HttpHeaders();
    headers.set('Authorization', 'Basic ' + basicheader);
    return this.http.get(environment.centralAuthenticationHost + CommonConstants.AUTH_TOKEN_API + '?grant_type=refresh_token&refresh_token='
          + localStorage.getItem(CommonConstants.REFRESH_TOKEN), { headers: headers })
          .pipe(catchError((e: any) => throwError(this.errorHandler(e))));
  }

  errorHandler(error: any): void {
    throwError('');
  }


  /**
   * logging out from the application and handle login message using the code.
   * @param code error codes defined for token failure
   */
  logoutUser(code: number) {
    switch (code) {
      case CommonConstants.REFRESH_TOKEN_FAILED:
      this.spinner.hide();
       this.toastr.error("your session has been expired please re login");
        localStorage.clear();
        this.auth.setUser(null);
       this.router.navigate(['/login']);
     /*   swal({
          title: 'Session Expired',
          text: 'your session has been expired please re login',

          timer: 3000,
          onOpen: () => {
            swal.showLoading();
          }
        }).then((result) => {
          if (result.dismiss === 'timer') {
            localStorage.clear();
            this.router.navigate(['/login']);
          }
        });*/
        break;
      default:
        localStorage.clear();
        this.auth.setUser(null);
        this.router.navigate(['/login']);
    }
    return throwError('');
  }
}
