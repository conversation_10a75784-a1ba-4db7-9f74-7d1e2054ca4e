export interface RgVwCorRequestDtlsDto {
    requestId: number;
    reqInst: number;
    estName: string;
    reqDate: Date;
    requestTo: string;
    instName: string;
    instAddress: string;
    indication: number;
    indicationDesc: string;
    primaryDiag: string;
    primaryDiagDesc: string;
    sizeZone: number;
    sizeClearZone: string;
    otherSpec: string;
    intendedArrDate: Date;
    intendedSurDate: Date;
    patientId: number;
    patCivilId: number;
    patRemarks: string;
    reqRemarks: string;
    status: string;
    invoiceNo: string;
    tissueId: number;
    tissueType: string;
}