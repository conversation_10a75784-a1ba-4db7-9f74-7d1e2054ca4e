import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';






@Injectable({
  providedIn: 'root'
})

export class AncRequestService {
  [x: string]: any;


  constructor(private http: HttpClient) { }

  saveAnc(data: any): Observable<any> {
    return this.http.post(AppUtils.SAVE_ANC_REQUEST, data);
  }

  getAnc(civilId: any, callType: any): Observable<any> {
    return this.http.get(AppUtils.GET_ANC_REQUEST, {
      params: new HttpParams().set("civilId", civilId).set("callType", callType)
    })
  }

  getAncDataByAncNo(ancNo: any): Observable<any> {
    return this.http.get(AppUtils.GET_ANC_REQUEST_BY_ANC_NO, {
      params: new HttpParams().set("ancNo", ancNo)
    })
  }
 
  getAncDataByRequestId(requestId: any): Observable<any> {
    return this.http.get(AppUtils.GET_ANC_REQUEST_BY_REQUESTED_ID, {
      params: new HttpParams().set("requestId", requestId)
    })
  }

}