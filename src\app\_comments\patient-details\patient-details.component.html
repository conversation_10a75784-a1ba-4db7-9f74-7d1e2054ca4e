<div class="row">

    <div class="col-lg-3" [formGroup]="patientForm">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label>Registration No.</label>
                    <input type="number" class="form-control form-control-sm" formControlName="centralRegNo" readonly>
                </div>
            </div>

            <div class="col-lg-6" [formGroup]="patientForm">
                <div class="form-group">
                    <label>Patient ID  <span class="mdtr">*</span></label>
                    <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="patientId" required
                        (keyup.enter)="fetchPatientInfo()">
                </div>
                <span *ngIf="patientForm.controls['patientId'].hasError('required') && (submitted || patientForm.controls['patientId'].touched || patientForm.controls['patientId'].dirty)"
                            class="tooltiptext">{{'Patient Id is required'}}</span>

                <!-- <div>
                     <span *ngIf="submitted && f.patientId.errors" class="tooltiptext">{{'Patient Id is required'}}</span> 
                </div> -->
                
            </div>
        </div>
    </div>

    <div class="col-lg-3" [formGroup]="patientForm">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label>Civil ID <span class="mdtr">*</span></label>
                      <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="civilId"
                        (keyup.enter)="fetchPatientInfoByCivilID()">
                </div>
                <span *ngIf="submitted && f.civilId.errors" class="tooltiptext">{{'Civil Id is required'}}</span>
            </div>

            <div class="col-lg-6" [formGroup]="patientForm">
                <div class="form-group" >
                    <label>Expiry Date <span class="mdtr">*</span></label>       
                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="exDate"
                        [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]=today [yearRange]="yearRange"
                        yearNavigator="true" showButtonBar="true"   (onSelect)="fetchPatientInfoByCivilID()"></p-calendar>
                    </div>
                        <!-- <span *ngIf="submitted && f.exDate.errors" class="tooltiptext">{{'Expiry Date is required'}}</span> -->
        
               
            </div>
        </div>
    </div>

    <div class="col-lg-3" [formGroup]="patientForm">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label>Date of Birth  <span class="mdtr">*</span></label>
        
                    <!-- getAge($event); -->
                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="dob"
                        [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                        yearNavigator="true" showButtonBar="true" (onSelect)="fetchPatientInfoByCivilID(); getAge($event);"></p-calendar>
        
                </div>
                <span *ngIf="submitted && f.dob.errors" class="tooltiptext">{{'Birth Date is required'}}</span>
            </div>

            <div class="col-lg-6" [formGroup]="patientForm">
                <div class="form-group">
                    <label>Age (Yrs.)</label>
                    <!-- (blur)="getDob($event)" -->
                    <input type="text" class="form-control form-control-sm" formControlName="age">
                </div>
            </div>
        </div>
    </div>




    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Institutes <span class="mdtr">*</span></label> 
          
            <ng-select #entryPoint appendTo="body" [items]="institutesFilter" [virtualScroll]="true" formControlName="regInst"
                placeholder="Select" bindLabel="estName" bindValue="estCode" (change)="changeInstitute($event)">

                 <ng-option *ngFor="let c of institutes" [value]="c.estCode">{{ c.estName }}</ng-option> 
            </ng-select>
        </div>
        <span *ngIf="patientForm.controls['regInst'].hasError('required') && (submitted || patientForm.controls['regInst'].touched || patientForm.controls['regInst'].dirty)"
                            class="tooltiptext">{{'Institute is required'}}</span>
        <!-- <span *ngIf="submitted && f.regInst.errors" class="tooltiptext">{{'Institute is required'}}</span> -->
    </div>


    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>First Name<span class="mdtr">*</span></label>
            <input type="text" class="form-control form-control-sm" formControlName="firstName">
        </div>
        <span *ngIf="patientForm.controls['firstName'].hasError('required') && (submitted || patientForm.controls['firstName'].touched || patientForm.controls['firstName'].dirty)"
                            class="tooltiptext">{{'First name is required'}}</span>
        <!-- <span *ngIf="submitted && f.firstName.errors" class="tooltiptext">{{'First name is required'}}</span> -->
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Second Name <span class="mdtr">*</span></label>
            <input type="text" class="form-control form-control-sm" formControlName="secondName">
        </div>
        <span *ngIf="patientForm.controls['secondName'].hasError('required') && (submitted || patientForm.controls['secondName'].touched || patientForm.controls['secondName'].dirty)"
                            class="tooltiptext">{{'Second name is required'}}</span>
        <!-- <span *ngIf="submitted && f.secondName.errors" class="tooltiptext">{{'Second name is required'}}</span> -->
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Third Name</label>
            <input type="text" class="form-control form-control-sm" formControlName="thirdName">
        </div>
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Tribe</label>
            <input type="text" class="form-control form-control-sm" formControlName="tribe">
            <!-- <span *ngIf="submitted && f.tribe.errors" class="tooltiptext">{{'Tribe is required'}}</span> -->
        </div>
    </div>




    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Gender</label>

            <ng-select #entryPoint appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true"
                placeholder="Select" bindLabel="value" bindValue="id" formControlName="sex">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                </ng-template>
            </ng-select>
        </div>
    </div>

    <div *ngIf="!isChildNut" class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Marital Status</label>

            <ng-select #entryPoint appendTo="body" [items]="maritalStatusOptions" [virtualScroll]="true"
                placeholder="Select" bindLabel="value" bindValue="id" formControlName="maritalStatus">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                </ng-template>
            </ng-select>
        </div>
    </div>

    <div class="col-lg-6" [formGroup]="patientForm">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label *ngIf="!isChildNut">Next Kin Tel No:</label>
                    <label *ngIf="isChildNut">Telephone No:</label>
                    <input type="number" class="form-control form-control-sm" 
                           formControlName="kinTelNo" 
                           maxlength="16">
                    <div *ngIf="patientForm.get('kinTelNo')?.errors?.pattern && patientForm.get('kinTelNo')?.touched" class="text-danger">
                        Must be between 8 and 16 digits.
                    </div>
                </div>
            </div>
    
            <div class="col-lg-6">
                <div class="form-group">
                    <label>Mobile No:</label>
                    <input type="number" class="form-control form-control-sm"
                           formControlName="mobileNo" 
                           maxlength="16">
                    <div *ngIf="patientForm.get('mobileNo')?.errors?.pattern && patientForm.get('mobileNo')?.touched" class="text-danger">
                        Must be between 8 and 16 digits.
                    </div>
                </div>
            </div>
        </div>
    </div>
    


    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Governorate</label>
            <ng-select #entryPoint appendTo="body" [items]="regions" (change)="changeRegion($event)"
                [virtualScroll]="true" placeholder="Select" bindLabel="regName" bindValue="regCode"
                formControlName="region">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}
                </ng-template>
            </ng-select>
        </div>
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Wilayat</label>
            <ng-select #entryPoint appendTo="body" [items]="wallayatsFilter" [virtualScroll]="true" placeholder="Select"
                bindLabel="walName" bindValue="walCode" formControlName="walayat" (change)="changeWalayat($event)">


                <ng-option *ngFor="let c of wallayatsFilter" [value]="c">{{ c.walName }}</ng-option>
            </ng-select>
        </div>
    </div>


    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
        <div class="form-group">
            <label>Town/Village</label>
            <ng-select #entryPoint appendTo="body" [items]="villagesFilter" [virtualScroll]="true" placeholder="Select"
                bindLabel="vilName" bindValue="vilCode" formControlName="village">

                <ng-option *ngFor="let c of villagesFilter" [value]="c.vilCode">{{ c.vilName }}</ng-option>
            </ng-select>
        </div>
    </div>





  

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm" *ngIf="downloadFromShifa">
        <div class="row">
            <div class="col-lg-6">
                <label> </label>
                <div class="form-group">
                    <button (click)="callFetchDataFromAlShifa(0,0,0)" 
                        class="btn btn-sm btn-primary  " title="Download Patient Data from Selected Institute!">Download
                    </button>
                    <button class="btn btn-sm btn-primary" *ngIf="callRenalWaitingLisitng"
                        (click)="callListingPage()">Waiting List</button>
                </div>
            </div>

        </div>
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm" *ngIf="nationalityForDiabetic">
        <div class="form-group">
            <label>Nationality<span class="mdtr">*</span></label>
            <select class="form-control form-control-sm" formControlName="nationality">
                <option selected [value]="null">Select </option>
                <option [value]="res.natCode" *ngFor="let res of nationalityList">{{res.nationality}}</option>
    
            </select>
            <span *ngIf="submitted && f.nationality.errors" class="tooltiptext">{{'Nationality is required'}}</span>
        </div>
    </div>

</div>