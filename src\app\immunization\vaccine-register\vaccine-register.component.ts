import { formatDate } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { MasterService } from 'src/app/_services/master.service';
import Swal from 'sweetalert2';
import * as CommonConstants from '../../_helpers/common.constants';
import { VaccineService } from '../vaccine.service';
import * as AppUtils from '../../common/app.utils';
import * as AppComUtils from '../../common/app.component-utils';
import { ImmunizationRegistryForm } from 'src/app/common/objectModels/Immunization-registry-model';
import { SharedService } from 'src/app/_services/shared.service';
import { AlShifaLoginService } from 'src/app/alshifa/alShifaLogin.service';
import { rgTbImmunization } from 'src/app/common/objectModels/Immunization-model';
import { __values } from 'tslib';
import { GridOptions } from 'ag-grid-community';
import * as moment from 'moment';
import { GridNgSelectDataComponent } from '../../common/agGridComponents/grid-ngSelect-data.component';
import { InstituteDataModel } from 'src/app/common/objectModels/institute-model';
import { GridDateComponent } from 'src/app/common/agGridComponents/grid-date.component';

import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component'
import { isEmpty } from 'rxjs-compat/operator/isEmpty';
import { AppComponent } from 'src/app/app.component';
import { VaccineRegisterModel } from '../vaccine-register-model';






@Component({
  selector: 'app-vaccine-register',
  templateUrl: './vaccine-register.component.html',
  styleUrls: ['./vaccine-register.component.scss'],

})
export class VaccineRegisterComponent implements OnInit {

  columnDefHistory
  columnDefsData
  frameworkComponents;
  institeList: any[];
  institeNewList: any[];
  rgNo: any;
  data: any;
  regId: any;
  regNo: any
  allData: any;
  alive = true;
  patientForm: FormGroup;
  today = new Date();
  submitted = false;
  view: boolean;
  ImmunizationForm: FormGroup;
  formData: ImmunizationRegistryForm;
  setData: Array<VaccineRegisterModel>;
  currentCivilID = '';
  estCode: any;
  patientId: any;
  immunization: Array<rgTbImmunization>;
  institutes: any[];
  institutesName = [];
  institutesNew = [];
  immunizationList: any[];
  periodList: any[] = [];
  NewList: any[] = [];
  historyImmunizationList: any[]= [];
  private gridApi: any;
  loginId: any;
  civilIdEntryType: any;

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  }

  gridOptions1: GridOptions = <GridOptions>{
    enableColResize: true,
    resizable: true,
    editable: true,
    onGridSizeChanged: () => {
      this.gridOptions1.api.sizeColumnsToFit();
    }
  }
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  runId: null

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;

  render: any;

  constructor(private fb: FormBuilder, private _masterService: MasterService, private _vaccineService: VaccineService, private _sharedService: SharedService, private _alShifaLoginService: AlShifaLoginService) {


    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
     this.loginId = curUser['person'].perscode;

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }


    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      dateEditor: GridDateComponent,
    };

   
  }

  ngOnInit() {
    this.populateMasterData();
    this.patientForm = this.fb.group({
      'centralRegNo': [null],
      'patientId': ["", Validators.required],
      'civilId': ["", Validators.required],
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'firstName': ["", Validators.required],
      'secondName': ["", Validators.required],
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'maritalStatus': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'kinTelNo': new FormControl(),
      'careGiverName': new FormControl(),
      'careGiverMobile': new FormControl(),
      'careGiverTel': new FormControl(),
      'regInst': ['', Validators.required],
      'registerType': [9],
      'exDate':  new FormControl(),
      'instRegDate': [this.today],
      'rgTbPatientInfo': this.fb.array([]),
      'immunization ': this.fb.array([]),
      'rgTbImmunization': this.fb.array([]),

    });



    this.ImmunizationForm = this.fb.group({
      'runId': new FormControl(),
      'civilId': new FormControl(),
      'centralRegNo': new FormControl(),
      'createdBy': new FormControl(),
      'createdDate': new FormControl(),
      'dateGiven': new FormControl(),
      'dose': new FormControl(),
      'dueDate': new FormControl(),
      'estCode': new FormControl(),
      'estName': new FormControl(),
      'modifiedOn': new FormControl(),
      'syringeBatchNo': new FormControl(),
      'vaccine': new FormControl(),
      'vaccineBatchNo': new FormControl(),
      'vaccineName': new FormControl(),
      'vacScheduleId': new FormControl(),
      'periodDesc': new FormControl(),
      'remarks': new FormControl(),
    });

   // this.populateMasterData();
  }

  populateMasterData() {
    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institeList = res['result'];
    }, error => {
    }, () => {
      this.getGrid();
    });


  }

  get f() { return this.patientForm.controls; }


  public onGridReady(params: any) {
    this.gridApi = params.api;
  }

 
  //ngCalender
  getGrid() {
    this.columnDefsData = [

      { headerName: 'Vaccine', field: 'vaccineName', minWidth: 125 },
      { headerName: 'Due Date', field: 'dueDate', minWidth: 125 },
      { headerName: 'Given Date', editable: true, field: 'dateGiven', minWidth: 150, cellEditor: 'dateEditor', cellEditorParams: { date1: 'collectionDate', date2: 'resultDate', allowFutureDate: false } },
      {
        headerName: 'Institute', field: 'estName', minWidth: 200, maxHeight: 10,
        cellEditor: 'ngSelectEditor',
        cellEditorParams: {
          values: this.institeList,
          dataColumn: 'estCode',
          model: new InstituteDataModel(),
          objectData: ['estCode', 'estName']
        }, editable: true, width: 100
      },
      
      { headerName: 'Batch No.', editable: true, field: 'vaccineBatchNo', minWidth: 20 },
      { headerName: 'Syringe Batch No', editable: true, field: 'syringeBatchNo', minWidth: 20 },
      { headerName: 'Remarks', editable: true, field: 'remarks', minWidth: 150 },
      { headerName: 'estCode', hide: true, field: 'estCode' }

    ];


    this.columnDefHistory = [
      { headerName: 'Vaccine', field: 'vaccineName', minWidth: 300 },
      { headerName: 'Due Date', field: 'dueDate', minWidth: 125 },
      { headerName: 'Given Date', field: 'dateGiven', minWidth: 150, },
      { headerName: 'Institute', field: 'estName', minWidth: 400 },
      { headerName: 'Batch No.', field: 'vaccineBatchNo', minWidth: 150 },
      { headerName: 'Syringe Batch No', field: 'syringeBatchNo', minWidth: 180 },
      { headerName: 'Remarks', field: 'remarks', minWidth: 220 },
    ];

  }

  resetForm() {
    this.submitted = false;
    this.patientForm.reset();
  }


  callMpiMethod() {
    this.getdata('civilId', '', this.patientDetails.f.civilId.value);
  }
  


  getdata(searchby: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    let estCode: any = '';
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = AppComUtils.MSG_NO_RECORD_REG_NO;
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = AppComUtils.MSG_NO_RECORD_REG_NO + '</br></br>' + AppComUtils.MSG_ERR_FETCH_NRS_DATA;

      }
    }

    this._vaccineService.getImmunization(civilId, regNo, estCode).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.formData = res['result'];
        if (this.formData.estImmunization != null) {
          this.patientDetails.setPatientDetails(res['result']);
          this.currentCivilID = this.formData.rgTbPatientInfo['civilId'].toString();

          this.estCode = this.formData['regInst'];
          this.patientId = this.formData['patientID'];
          this.immunizationList = res['result'].estImmunization;
          this.immunizationList.forEach(e => {
            e.estName = this.institeList.filter(s => e.estCode == s.estCode).map(s => s.estName).toString();

          });
          this.historyImmunizationList = [];
          this.immunizationList.forEach(row => {
            if (row.dateGiven != null) this.historyImmunizationList.push(row);
          });
          this.gridOptions.api.setRowData(this.historyImmunizationList);
          this.immunizationList.forEach(s => {
            if (this.periodList.filter(p => p.periodDesc == s.periodDesc).length == 0) {
              this.periodList.push({ "periodDesc": s.periodDesc, "vacScheduleId": s.vacScheduleId });
            }
          });


        }
        else {
          await Swal.fire(
            '', msg, 'warning',
          ).then(async (result) => {
            if (callMPI) {
              this._sharedService.setPatientData(this.patientDetails);
              await this._sharedService.fetchMpi().subscribe(res=>{
                 this._sharedService.patientData;
                 this.getImmunizationData();
              });
            }
          })
        }

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire(
          '', msg, 'warning',
        ).then(async (result) => {
          if (callMPI) {
            this._sharedService.setPatientData(this.patientDetails);
            await this._sharedService.fetchMpi().subscribe(res => {
              this._sharedService.patientData;
                 this.getImmunizationData();
            });
          }
        })

      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      Swal.fire('Error!', 'Error occured while retrieving  details', 'error')
    })
  }

  getImmunizationData(){
    let civilId =this.patientDetails.f.civilId.value;
    let dob =this.patientDetails.f.dob.value;
    let dobValue = this.patientForm.controls.dob.value;
    if(dobValue){
      
      this._vaccineService.getImmunizationByCivilId(civilId,dobValue).subscribe(async res => {
      
        this.immunizationList = res['result'].estImmunization;
        this.immunizationList.forEach(e => {
          e.estName = this.institeList.filter(s => e.estCode == s.estCode).map(s => s.estName).toString();
        })
        this.historyImmunizationList = [];
        this.immunizationList.forEach(row => {
          if(row.dateGiven != null) this.historyImmunizationList.push(row);
        });
        this.gridOptions.api.setRowData(this.historyImmunizationList);
  
        this.immunizationList.forEach(s => {
          if (this.periodList.filter(p => p.periodDesc == s.periodDesc).length == 0) {
            this.periodList.push({ "periodDesc": s.periodDesc, "vacScheduleId": s.vacScheduleId });
          }
        })
      })
    }
 
  }


  fetchVisitInfobyPatientID(event) {
    this.getdata('civilId', '', this.patientDetails.f.civilId.value);
  }
  fetchVisitInfo(event) {
    var loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
    if (loggedInUser) {
      var inst = loggedInUser.institutes.filter(item => item.defaultYN === 'Y');
    }
    inst[0].estCode
    // 20068
    this.patientDetails.setPatientDetailsAlShifa(this.immunization);

  }

  search() {
    if (this.regId) {
    
        this.getdata('regNo', this.regId, '');
        this.regId = "";
      

    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter RegNo'
      });
    }
  }

  onFocusOut(): void {
    this.gridOptions1.api.stopEditing();
 }

  saveRegister() {
   
    
    
    this.populateMasterData();
    let dataSet = [];

    this.populateMasterData();
    if (this.gridOptions1.api) {
      this.gridOptions1.api.stopEditing();
      let renderedNodes = this.gridOptions1.api.getRenderedNodes();

      renderedNodes.forEach(el => {
        dataSet.push(el['data']);
      })
    }
    this.setData = dataSet;
    // this.setData.forEach(record=>{
    //   if(record.dateGiven == null) {
    //     record.estCode = null ;
    //     record.estName = null ;
    //   }
    // })

    if (!this.patientDetails.validateFields())
      return false;

    let immu = this.setData;

    if (immu) {
      immu.forEach(s => {
        s.createdBy = this.loginId;
        s.modifiedOn = this.currentDate;
      })
    }


    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;
    if (this.patientDetails.f.centralRegNo.value === null) {
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      centralRegNo = null;
    } else {
      createdBy = this.formData.createdBy;
      createdOn = this.formData.createdOn;
      modifiedBy = this.loginId;
      modifiedOn = this.currentDate;
      centralRegNo = this.patientDetails.f.centralRegNo.value
    }

    let saveData = {
      centralRegNo: centralRegNo,
      activeYn: "Y",
      patientID: this.patientDetails.f.patientId.value,
      createdBy: createdBy,
      createdOn: createdOn,
      regInst: this.f.regInst.value,
      registerType: AppUtils.REG_TYPE_IMMUNIZATION,
      modifiedBy: modifiedBy,
      modifiedOn: modifiedOn,
      instRegDate: this.f.instRegDate.value ? moment(this.f.instRegDate.value).format("DD-MM-YYYY") : null,
      rgTbPatientInfo: {
        createdBy: createdBy,
        createdOn: createdOn,
        careGiverName: this.f.careGiverName.value,
        careGiverTel: this.f.careGiverTel.value,
        cateGiverMob: this.f.careGiverMobile.value,
        civilId: this.f.civilId.value,
        createdInstid: this.f.regInst.value,
        dob: this.f.dob.value ? moment(this.f.dob.value).format("DD-MM-YYYY") : null,  //this.f.dob.value,
        firstName: this.f.firstName.value,
        maritalStatus: this.f.maritalStatus.value,
        mobileNo: this.f.mobileNo.value,
        kinTelNo: this.f.kinTelNo.value,
        secondName: this.f.secondName.value,
        sex: this.f.sex.value,
        thirdName: this.f.thirdName.value,
        tribe: this.f.tribe.value,
        village: this.f.village.value,
        patientId: this.f.patientId.value
      },
      estImmunization: immu,

    }

    this._vaccineService.saveImmunization(saveData).subscribe(res => {
      if (res['code'] == "S0000" || res['code'] == "5" || res['code'] == "3" || res['code'] == "0") {
        Swal.fire('Saved!', 'Immunization  Saved successfully.', 'success');
        this.clear();
        this.regId = res["result"];
       this.search()

      }
      //  else if (res['code'] == "3") {
      //   Swal.fire('Saved!', res['message'], 'error');
      // } 
      else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Immunization info ' + err.message, 'error')
    })

  }

  displayVaccineList(vacID) {
    //immunizationList
    this.setData = new Array<VaccineRegisterModel>();
    for (let entry of this.immunizationList) {
      if (entry.vacScheduleId === vacID) {
        this.setData.push(entry)
        this.view = true;
      }
    }
    this.setData.forEach(record=>{
      if(record.dateGiven == null) {
        record.estCode = this.patientForm.value['regInst'] ;
        record.estName = this.getInstitute(record.estCode)['estName'];
      }
    })

  }

  getInstitute(instId: any): Object{
    return this.institeList.find((institute) => {
      return institute['estCode'] == instId;
    })
  }
 
  clear() {
    this.periodList = [];
    //this.search();
    this.historyImmunizationList = [];
    this.view = false;
    this.patientForm.reset();
    this.ImmunizationForm.reset();
    this.immunizationList = [];
    this.resetForm;
    this.formData = null;
    this.patientDetails.clear();
    this.ngOnInit();
  }

  active: number;
  onClick(index: number) {
    this.active = index;

  }


  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }


}




