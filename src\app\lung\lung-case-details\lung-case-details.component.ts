// Angular imports
import { Component, OnInit, ViewChild, Input } from "@angular/core";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { AbstractControl } from "@angular/forms";
import { LoaderService } from "src/app/_services/loader.service";

import * as pdfMake from 'pdfmake/build/pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts';
import htmlToPdfmake from 'html-to-pdfmake';
(pdfMake as any).vfs = pdfFonts.vfs;
(pdfMake as any).fonts = {
  Roboto: {
    normal: 'Roboto-Regular.ttf',
    bold: 'Roboto-Medium.ttf',
    italics: 'Roboto-Italic.ttf',
    bolditalics: 'Roboto-MediumItalic.ttf'
  },
  Open_sanssemibold: {
    normal: 'Roboto-Regular.ttf',
    bold: 'Roboto-Medium.ttf',
    italics: 'Roboto-Italic.ttf',
    bolditalics: 'Roboto-MediumItalic.ttf'
  }
};

import {
  FormArray,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";

// Third-party imports
import { GridOptions } from "ag-grid-community";
import Swal from "sweetalert2";
import * as _ from "lodash";
import { NgbModal, ModalDismissReasons } from "@ng-bootstrap/ng-bootstrap";
import { IDropdownSettings } from "ng-multiselect-dropdown";

// Application imports
import { MasterService } from "src/app/_services/master.service";
import { SharedService } from "src/app/_services/shared.service";
import { LungService } from "../lung.service";
import { LungCaseDetailService } from "./lung-case-details.service";
import * as AppUtils from "../../common/app.utils";
import * as CommonConstants from "../../_helpers/common.constants";
import { VaccinationComponent } from "../../_comments/vaccination/vaccination.component";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";
import {
  LungIndication,
  ResultDecorator,
  LungTransplantIndication,
  LungDiseaseOption,
  LungTransplantDiseaseSeverity,
  LungCurrentMgmt,
  Medication,
  Pleurodesis,
  LungRegister,
  PleurodesisDto,
  LungTBTransIndicationDto,
  LungTBTransplantDiseaseSeverity,
  LungTBTransplantCurrentManagement,
  LungCurMgmtNonPharmaDto,
  LungPleurodesisIndication,
  LungPleurodesisMethod,
} from "src/app/_models/lungTransplant.model";
import { Paginator } from "primeng/paginator";
import { Observable } from "rxjs";
import { MedicationComponent } from "src/app/_comments/medication/medication.component";
import { surgeryComponent } from "src/app/_comments/surgery/surgery.component";

@Component({
  selector: "app-lung-case-details",

  templateUrl: "./lung-case-details.component.html",
  styleUrls: ["./lung-case-details.component.scss"],
})
export class LungCaseDetailsComponent implements OnInit {
  @ViewChild("labTestPaginator", { static: false }) labTestPaginator: Paginator;
  @ViewChild("printSection", { static: false }) printSection: any;
  totalLabRecords: number = 0;
  paginationSize: number = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  filteredLabnewList: any[] = [];
  showButton: boolean = false;
  showDownloadButton: boolean = false;
  showVaccineButton: boolean = false;
  @ViewChild("Vaccination", { static: false })
  Vaccination: VaccinationComponent;
  showCommentBox: boolean = false;
  showCommentBox_peld: boolean = false;
  contactDetailsForm: FormGroup;
  contactDetails: any[] = []; // Replace with your actual data type

  @Input() liverId: number;

  vaccNewList: any[];
  vaccName: any[];
  vaccinMastList: any;
  liverTransIndications: LungIndication[] = [];
  lungTransIndications: LungTransplantIndication[] = [];
  lungTransDiseaseSeverity: LungTransplantDiseaseSeverity[] = [];
  lungCurrentMgmt: LungCurrentMgmt[] = [];

  familyHistoryYn: number = 0;
  familyHistoryRemarks: string = "";

  page: number = 1;
  pageSize: number = 3;
  disVaccineInfoalshifa: any;
  openAccordion: boolean[] = [];
  hospitals: any;
  public rgTbFamilyHistory: any = [];
  public contactDetailsArray: any = [];
  relation: any;
  pharmacologicalNotes: string = "";

  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  @ViewChild("surgery", { static: false }) surgery: surgeryComponent;
  dialysis = null;
  stage: any;
  transplant = null;
  readiness = null;
  willingness = null;
  preEmptiveTransplant;
  nationListFilter: any;
  nationList: any;
  dialysisType;
  selectedTransplantPlace: any;
  selectedfollowUpHospital: any;
  dropdownSettings: IDropdownSettings;
  selectedProcedures: number[] = [];
  liverRegisterForm: FormGroup;
  familyHistoryForm: FormGroup;
  contactAddressForm: FormGroup;
  @Input() caseDetailsForm: FormGroup;
  patientForm: FormGroup;
  @Input() filterModelForm: FormGroup;
  public labTestForm: FormGroup;
  centralRegNoExit: boolean = false;
  currentCivilId: any;
  patntId = "";
  loginId: any;
  today: any;
  testcomponet: any;
  profileList: any;
  ComponetList: any;
  testComponetList: any;
  rgTbLabTestInfo: any = [];
  labTestFg: any = [];
  labTest: any;
  testDone: any[];
  testListToDownload: any[];
  labnewList: any[];
  labListToFixRowSpan: any[];
  labTestName: any[];
  regId: any;
  institutes: any[];
  @Input() submitted = false;
  famHistory: any = [];
  contacts: any = [];
  isChecked: any;
  alive = true;
  isPrint = false;
  delRow;
  selectedCompYes: any;
  selectedCompNo: any;
  rowData: any[] = [];
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
  };

  // Properties for lab results accordion
  labResults: any[] = [];
  openAccordionLab: boolean[] = [];

  medications: Medication[] = [];
  medicationForm: FormGroup;

  pleurodesisList: PleurodesisDto[] = [];
  pleurodesisMgmtForm: FormGroup;

  lungRegister: LungRegister = {
    lungId: null,
    //centralRegno: null,
    familyHistYn: null,
    smokingYn: null,
    cigarettePerYr: null,
    cigaretteType: null,
    cigaretteQuitDate: null,
    recTransYn: null,
    recTransReason: null,
    urgentTransYn: null,
    urgentTransReason: null,
    curMgmtPharma: null,
    priorityOrder: null,
    rgTbRegistryPatient: null,
    rgTbLungPleurodesisHistory: null,
    rgTbLungDiseaseSeverity: null,
    rgTbLungCurMgmtNonPharma: null,
    rgTbLungTransIndication: null,
  };

  priorityOrder: any[] = [
    { id: 1, value: "1" },
    { id: 2, value: "2" },
    { id: 3, value: "3" },
    { id: 4, value: "4" },
    { id: 5, value: "5" },
    { id: 6, value: "6" },
    { id: 7, value: "7" },
    { id: 8, value: "8" },
    { id: 9, value: "9" },
    { id: 10, value: "10" }
  ];

  columnDefs = [
    { headerName: "Date", field: "date" },
    { headerName: "Profile Name", field: "profilename" },
    { headerName: "Test Component", field: "testComponent" },
    { headerName: "Result", field: "result" },
    { headerName: "Unit", field: "unit" },
    { headerName: "Institute", field: "institute" },
  ];
  estCode: any;
  private _fullLabnewList: any;
  vaccineFg: any;
  @ViewChild("Medication", { static: false }) Medication: MedicationComponent;
  lungPleurodesisIndications: LungPleurodesisIndication[] = [];
  lungPleurodesisMethod: LungPleurodesisMethod[] = [];
  civilId: any;

  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _router: Router,
    private _http: HttpClient,
    private formBuilder: FormBuilder,
    private _sharedService: SharedService,
    private _caseDetaisService: LungCaseDetailService,
    private _LungService: LungService,
    private loaderService: LoaderService
  ) {}

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;
    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }

  ngOnInit() {
    this.initializeForm();
    this.getMasterData();
    this.loadTransplantIndications();
    this.loadTransplantDiseaseSeverity();
    this.loadPleurodesisIndications();
    this.loadPleurodesisMethod();
    this.loadLungCurrentMgmt();
    this.initializeComponent();
    this.getNationalityList();
    this.doDropDown();
    this.initMedicationForm();
    this.initPleurodesisMgmtForm();
  }

  initPleurodesisMgmtForm() {
    this.pleurodesisMgmtForm = this.fb.group({
      rgTbPleurodesis: this.fb.array([]),
    });
  }

  get pleurodesisArray() {
    return this.pleurodesisMgmtForm.get("rgTbPleurodesis") as FormArray;
  }

  savePleurodesis() {
    const pleurodesisData = this.pleurodesisMgmtForm.value.rgTbPleurodesis;

    const validPleurodesis = pleurodesisData.filter((pleurodesis) => {
      return (
        new Date(pleurodesis.doneDate) &&
        pleurodesis.laterality &&
        pleurodesis.methodUsed &&
        pleurodesis.indication &&
        pleurodesis.remarks
      );
    });
    if (validPleurodesis.length === 0) {
      return;
    }
    return validPleurodesis;
  }

  initMedicationForm() {
    this.medicationForm = this.fb.group({
      rgTbMedications: this.fb.array([]),
    });
  }

  callGenMedList() {
    this._LungService.getGeneticMedicineList().subscribe((res) => {
      this.Medication.medicine = res["result"];
    });
  }

  onAddNewMedication() {
    const medicationsArray = this.medicationForm.get(
      "rgTbMedications"
    ) as FormArray;
    medicationsArray.push(this.createMedicationFormGroup());
    this.medications.push({
      id: null,
      medicine: "",
      startDate: new Date(),
      dose: "",
      frequency: "",
      medicineType: "",
      isEditable: true,
    });
  }

  createMedicationFormGroup(): FormGroup {
    return this.fb.group({
      medicine: ["", Validators.required],
      startDate: [new Date(), Validators.required],
      dose: [""],
      frequency: [""],
      medicineType: [""],
    });
  }

  onRowEditInitMedication(row: Medication) {
    row.isEditable = true;
  }

  onRowEditSaveMedication(row: Medication) {
    row.isEditable = false;
    // Add your save logic here
  }

  deleteMedication(row: Medication) {
    const index = this.medications.indexOf(row);
    if (index > -1) {
      this.medications.splice(index, 1);
      const medicationsArray = this.medicationForm.get(
        "rgTbMedications"
      ) as FormArray;
      medicationsArray.removeAt(index);
    }
  }

  doDropDown() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: "mohTestCode",
      textField: "testName",
      selectAllText: "Select All",
      unSelectAllText: "UnSelect All",
      itemsShowLimit: 6,
      allowSearchFilter: true,
    };
  }

  loadContactDetails() {
    const contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;
    this.contactDetails.forEach((contact) => {
      contactDetailsArray.push(
        this.initRgTbContactDetails(
          contact.name,
          contact.relation,
          contact.phone,
          contact.email
        )
      );
    });
  }

  createContactFormGroup(contact: any): FormGroup {
    return this.fb.group({
      runId: [contact.runId],
      name: [contact.name],
      relation: [contact.relation],
      phone: [contact.phone],
      email: [contact.email],
      isEditable: [false], // Default to false
    });
  }

  onAddNewContact() {
    this.addContactDetails("", "", "", "", "", false);
    this.contacts[this.contacts.length - 1].isEditable = true; //
  }

  isEmpty(input) {
    return input.replace(/\s/g, "") === "";
  }

  emailValidator(data) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(data);
  }

  phoneValidator(data) {
    const phoneRegex = /^\d{8,16}$/; // Example: 10-digit number
    return phoneRegex.test(data);
  }

  custom_emailValidator(control: AbstractControl) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(control.value) ? null : { invalidEmail: true }; // Return null if valid
  }

  custom_phoneValidator(control: AbstractControl) {
    const phoneRegex = /^\d{8,16}$/; // Example: 10-digit number
    return phoneRegex.test(control.value) ? null : { invalidPhone: true }; // Return null if valid
  }

  disableSaveButton(row) {
    if (row.name === "") {
      return true;
    }
    return false;
  }

  createContactDetail(): FormGroup {
    return this.fb.group({
      email: ["", [Validators.email]],
      phone: ["", [this.phoneValidator]],
    });
  }

  private initializeForm() {
    this.caseDetailsForm = this.fb.group({
      lungRegister: this.fb.group({
        lungId: [null],
        centralRegno: [null],
        familyHistYn: [null],
        smokingYn: [null],
        cigarettePerYr: [null],
        cigaretteType: [null],
        cigaretteQuitDate: [null],
        recTransYn: [null],
        recTransReason: [null],
        urgentTransYn: [null],
        urgentTransReason: [null],
        curMgmtPharma: [null],
        priorityOrder: [null],
      }),
    });
    this.initializeLungRegister();

    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
    this.familyHistoryForm = this.fb.group({
      rgTbFamilyHistory: this.fb.array([]),
    });

    this.contactDetailsForm = this.fb.group({
      rgTbContactDetails: this.fb.array([]), // Initialize as a FormArray
    });
    this.filterModelForm = this.fb.group({
      fromDate: [null],
      toDate: [null],
      profileT: [null],
    });
  }

  // Get Master Data for dropdown
  private getMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getLabTestList().subscribe((res) => {
      this.labTest = res.result;
    });

    this._masterService.getLabMaster().subscribe((res) => {
      this.testDone = res.result;
    });
    this._masterService.getLabTestToDownload().subscribe((res) => {
      this.testListToDownload = res["result"];
    });

    this._masterService.getLiverComplicationMast();

    this._masterService.getRelationMast().subscribe(async (res) => {
      this.relation = res["result"];
    });

    this._masterService.getDialysisHospital().subscribe((res) => {
      this.hospitals = res["result"];
    });

    this._masterService.getAllMohLabMaster().subscribe((res) => {
      this.testComponetList = res.result;
      this.profileList = [];
      this.ComponetList = [];
      this.testComponetList.forEach((element) => {
        if (
          this.profileList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.profileList.push({
            testId: element.mohTestCode,
            testName: element.testName,
          });
        }

        if (
          this.ComponetList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.ComponetList.push({
            componentTestId: element.mohTestCode,
            componentTestName: element.testName,
          });
        }
      });
    });
  }

  // seach function for getting case details based on registration id
  search() {
    this.clear();
    if (this.regId) {
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Registration ID",
      });
    }
  }

  // GetList function for loading case details
  getList(regNo: any) {
    this._LungService.getCaseDetails(regNo).subscribe({
      next: (res) => {
        if (res["code"] == "S0000") {
          this.isPrint = true;
          this.centralRegNoExit = true;
          this.patientDetails.setPatientDetails(res["result"]);
          this.currentCivilId = res["result"].rgTbPatientInfo["civilId"];
          this.estCode = res["result"].regInst;
          this.patntId = res["result"].rgTbPatientInfo["patientId"];
          this.showDownloadButton = true;
          this.showVaccineButton = true;
          this.showButton = true;
          

          if (res["result"].rgTbLungRegister) {
            this.loadLungRegisterData(res["result"].rgTbLungRegister);
          }

          if (res["result"].rgTbFamilyHistory) {
            let rgTbFamilyHistory: any = res["result"].rgTbFamilyHistory;
            let faFamilyHistory: FormArray = <FormArray>(
              this.familyHistoryForm.controls["rgTbFamilyHistory"]
            );

            for (let famHist of rgTbFamilyHistory) {
              this.addFamilyIstory(
                famHist.runId,
                famHist.name,
                famHist.relation,
                famHist.patientID,
                famHist.instID,
                famHist.source,
                false
              );
            }
          }

          // load Medication
          if (res["result"].rgTbMedicineDtls) {
            const rgTbMedicineDtls: any = res["result"].rgTbMedicineDtls;
            for (let medList of rgTbMedicineDtls) {
              this.Medication.addNewMed(
                medList.runId,
                medList.enteredBy,
                medList.enteredDt,
                medList.medicineID,
                this._sharedService.setDateFormat(medList.startDate),
                medList.dose,
                medList.frequency,
                medList.medicineType,
                medList.source,
                false
              );
            }
          }
          /////////////// Form group surgery

          if (res["result"].rgTbSurgeryDtls) {
            const rgTbSurgeryDtlsDB: any = res["result"].rgTbSurgeryDtls;
            for (let surList of rgTbSurgeryDtlsDB) {
              this.surgery.addNewSurgery(
                surList.runId,
                surList.surgeryID,
                this._sharedService.setDateFormat(surList.surgeryDt),
                surList.remarks,
                surList.enteredBy,
                surList.enteredDt,
                surList.source,
                false
              );
            }
          }

          if (res["result"].rgAddContactDetails) {
            let rgTbContactDetails: any = res["result"].rgAddContactDetails;

            //console.log("rgTbContactDetails--1",rgTbContactDetails);

            let contactDetailsFM: FormArray = <FormArray>(
              this.contactDetailsForm.controls["rgTbContactDetails"]
            );

            for (let conts of rgTbContactDetails) {
              this.addContactDetails(
                conts.runId,
                conts.name,
                conts.relation,
                conts.phone,
                conts.email,
                false
              );
            }

          }

          if (res["result"].rgTbLabTests) {
          for (let labRList of res["result"].rgTbLabTests) {
            let componentTestName = this.testComponetList
              .filter((s) => s.mohTestCode == labRList.mohTestCode)
              .map((s) => s.testName)[0];

            let instName = this.institutes
              .filter((s) => s.estCode == labRList.instCode)
              .map((s) => s.estName)[0];
            let profileTestName = this.testComponetList
              .filter((s) => s.mohTestCode == labRList.profileTestCode)
              .map((s) => s.testName)[0];

            this.addNewLabList(
              labRList.runId,
              this._sharedService.setDateFormat(labRList.testDate),
              this._sharedService.setDateFormat(labRList.releasedDate),
              labRList.profileTestCode,
              profileTestName,
              labRList.mohTestCode,
              componentTestName,
              labRList.value,
              labRList.unit,
              labRList.instCode,
              instName,
              labRList.enteredBy,
              labRList.enteredDate,
              labRList.source,
              false
            );
          }
          
          this.labListToFixRowSpan = res["result"].rgTbLabTest;
          }

          if (res["result"].rgTbVaccinationInfo) {
            this.vaccineFg = res["result"].rgTbVaccinationInfo;
            const rgTbVaccinationInfoDb: any =
              res["result"].rgTbVaccinationInfo;
            for (let vaccinaList of rgTbVaccinationInfoDb) {
              this.Vaccination.addNewVaccine(
                vaccinaList.runId,
                vaccinaList.enteredBy,
                new Date(vaccinaList.vaccinationDate),
                vaccinaList.vaccineCode,
                vaccinaList.vaccinatedInst,
                vaccinaList.remarks,
                vaccinaList.civilId,
                vaccinaList.source,
                false
              );
            }
          }
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      error: (error) => {
        if (error.status == 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      },
    });
  }

  // Transpant Indication Start

  loadTransplantIndications() {
    this._masterService.getLungTransplantMast();
    //console.log("lungTransplantMastList",this._masterService.lungTransplantMastList);
    this._masterService.lungTransplantMastList.subscribe((value) => {
      this.lungTransIndications = _.cloneDeep(value);
    });
  }

  prepareDataForSave(): any {
    return this.lungTransIndications
      .filter((option) => option.checked)
      .map((option) => ({
        paramId: option.paramId,
        paramName: option.paramName,
        selectedDiseases:
          option.selectedDiseases.map((disease) => disease.id) || [],
      }));
  }

  onDiseaseSelect(option: LungTransplantIndication) {
    // console.log(
    //   `Selected diseases for ${option.paramName}:`,
    //   option.selectedDiseases
    // );

    const selectedItems = option.subOptions.filter((item) =>
      option.selectedDiseases.some((disease) => disease.id === item.id)
    );
    //console.log("Selected items:", selectedItems);
  }

  submit() {
    if (this.caseDetailsForm.status === "VALID") {
    }
  }

  getTransplantIndicationControls() {
    return (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).controls;
  }

  addTransplantIndication() {
    const indicationForm = this.fb.group({
      checked: [false],
      value: [""],
      remarks: [""],
    });
    (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).push(indicationForm);
  }

  loadTransplantMaster() {
    this._LungService.getTransplantMaster().subscribe({
      next: (response: ResultDecorator) => {
        if (response && response.result) {
          this.liverTransIndications = response.result.map(
            (item: LungIndication) => ({
              ...item,
              checked: false, // Initialize as unchecked
              remarks: "", // Add remarks field for UI
            })
          );
        }
      },
      error: (error) => {
        console.error("Error loading transplant master:", error);
      },
    });
  }

  formatDate(date: Date): string {
    if (!date) return "";
    if (typeof date === "string") return date;
    return date.toISOString().split("T")[0];
  }

  addRemarks(option: LungIndication, event: any): void {
    option.remarks = event.target.value;
  }

  onIndicationChange(option: LungTransplantIndication, diseaseId: number) {
    //console.log("Indication checked:", option.paramName, option.checked);
    if (option.checked) {
      // Load diseases for this indication
      this.loadTransplantIndicationsDisease(option.paramId).subscribe(
        (diseases: LungDiseaseOption[]) => {
          option.subOptions = diseases;
          option.selectedDiseases = []; // Reset selected diseases
        }
      );
    } else {
      // Clear selections when unchecked
      option.selectedDiseases = [];
      option.subOptions = [];
    }
  }

  loadTransplantIndicationsDisease(
    paramId: number
  ): Observable<LungDiseaseOption[]> {
    return new Observable((observer) => {
      this._masterService.getLungTransplantDiseaseMast(paramId).subscribe({
        next: (response) => {
          if (response && response.result) {
            const diseases = response.result.map((disease) => ({
              id: disease.paramId,
              name: disease.paramName,
            }));
            observer.next(diseases);
          } else {
            observer.next([]);
          }
          observer.complete();
        },
        error: (error) => {
          console.error("Error loading diseases:", error);
          observer.error(error);
        },
      });
    });
  }

  onRemarksChange(option: any) {
    // Validate remarks if required
    if (option.remarksYn === "Y" && option.checked && !option.remarks) {
      // Show validation message
      console.error(`Remarks are required for ${option.value}`);
    }
  }

  validateTransplantIndications(): boolean {
    const validationErrors: string[] = [];

    this.liverTransIndications.forEach((indication) => {
      if (
        indication.checked &&
        indication.remarksYn === "Y" &&
        !indication.remarks
      ) {
        validationErrors.push(`Remarks are required for ${indication.value}`);
      }
    });

    if (validationErrors.length > 0) {
      console.error("Validation errors:", validationErrors);
      // Show validation errors to user
      return false;
    }

    return true;
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        // console.log(this.nationList, "nationlist");
        this.nationListFilter = this.nationList;
      },
      (error) => {}
    );
  }

  saveTransplantIndicationDto(): LungTBTransIndicationDto[] {
    const indications: LungTBTransIndicationDto[] = [];

    this.lungTransIndications.forEach((indication) => {
      if (
        indication.checked &&
        indication.selectedDiseases &&
        indication.selectedDiseases.length > 0
      ) {
        // For each selected disease, create a separate DTO
        indication.selectedDiseases.forEach((disease) => {
          //console.log('Disease ID:', disease);
          const dto: LungTBTransIndicationDto = {
            lungId: this.lungRegister.lungId,
            indicationId: indication.paramId,
            diseaseId: Number(disease), // Use individual disease ID
            remarks: null,
            transId: null,
            isEditable: true,
          };
          indications.push(dto);
        });
      }
    });

    return indications;
  }

  saveDiseaseSeverity(): LungTBTransplantDiseaseSeverity[] {
    const severities: LungTBTransplantDiseaseSeverity[] = [];

    this.lungTransDiseaseSeverity.forEach((severity) => {
      if (severity.checked) {
        const dto: any = {
          lungId: this.lungRegister.lungId,
          classId: severity.paramId,
          score: severity.score,
          remarks: severity.comments,
        };

        severities.push(dto);
      }
    });

    return severities;
  }

  saveLungCurrentManagement(): LungCurMgmtNonPharmaDto[] {
    const management: LungCurMgmtNonPharmaDto[] = [];

    this.lungCurrentMgmt.forEach((item) => {
      if (item.checked) {
        const dto: any = {
          lungId: this.lungRegister.lungId,
          pharmaId: item.paramId,
          remarks: item.comments,
        };

        management.push(dto);
      }
    });

    return management;
  }

  validateLabTests(labTestsData: any[]): boolean {
         const invalidLabTests = (labTestsData || []).filter(
    l => !l.mohTestCode || !l.profileTestCode || !l.instCode || !l.testDate || l.value === null || l.value === ''
  );
  
  if (invalidLabTests.length > 0) {
    Swal.fire("Warning", "Lab entries are mandatory.", "warning");
    return false;
  }
  
  return true;
  }
  
  validateVaccination(vaccinationInfoData: any[]): boolean {
    const invalidVaccines = (vaccinationInfoData || []).filter(
      v => !v.vaccineCode || !v.vaccinationDate
    );
  
    if (invalidVaccines.length > 0) {
      Swal.fire("Warning", "Vaccine Name and Date are mandatory for all entries.", "warning");
      return false;
    }
  
    return true;
    }

    validateContactDetails(contactDetailsArray: any[]): boolean {
      const invalidContacts = (contactDetailsArray || []).filter(
        c => !c.name || !c.relation || !c.phone || !c.email
      );
  
      if (invalidContacts.length > 0) {
        Swal.fire("Warning", "Contact details are mandatory for all entries.", "warning");
        return false;
      }
  
      return true;
      }

      validateFamilyHistory(familyHistoryData: any[]): boolean {
        const invalidFamilyHistory = (familyHistoryData || []).filter(
          fh => !fh.name || !fh.relation || !fh.instID
        );
      
        if (invalidFamilyHistory.length > 0) {
          Swal.fire("Warning", "Family history entries are mandatory.", "warning");
          return false;
        }
      
        return true;
        }

        validateMedications(medicationsData: any[]): boolean {  
          
          const invalidMedications = (medicationsData || []).filter(
            m => !m.medicineID || !m.startDate || !m.dose || !m.frequency || !m.medicineType
          );
      
          if (invalidMedications.length > 0) {
            Swal.fire("Warning", "Medication entries are mandatory.", "warning");
            return false;
          }
      
          return true;
          }

          validatePleurodesis(pleurodesisData: any[]): boolean {  
            const invalidPleurodesis = (pleurodesisData || []).filter(
              p => !p.doneDate || !p.laterality || !p.methodUsed
            );
      
            if (invalidPleurodesis.length > 0) {
              Swal.fire("Warning", "Pleurodesis entries are mandatory.", "warning");
              return false;
            }
      
            return true;
            }

            validateSurgery(surgeryData: any[]): boolean {  
              const invalidSurgery = (surgeryData || []).filter(
                s => !s.surgeryID || !s.surgeryDt
              );
      
              if (invalidSurgery.length > 0) {
                Swal.fire("Warning", "Surgery entries are mandatory.", "warning");
                return false;
              }
      
              return true;
              } 

  saveDetails(): void {
    let patientInfoData = this.patientDetails.patientForm.value;
    let labTestsData = this.labTestForm.value.rgTbLabTestInfo;

    if (!this.validateLabTests(labTestsData)) {
      return;
    }

    let vaccinationInfoData =
      this.Vaccination.vaccinationForm.value.rgTbVaccinationInfo;

    if (!this.validateVaccination(vaccinationInfoData)) {
      return;
    }

    const pleurodesisData = this.pleurodesisMgmtForm.value.rgTbPleurodesis;

    if (!this.validatePleurodesis(pleurodesisData)) {
       return;
    }

    let lungRegisterData = this.caseDetailsForm.value.lungRegister;
    const lungRegister: LungRegister = {
      lungId: this.lungRegister.lungId || null,
      //centralRegno: this.patientDetails.f.centralRegNo.value,
      familyHistYn: this.lungRegister.familyHistYn || null,
      smokingYn: lungRegisterData.smokingYn || null,
      cigarettePerYr: lungRegisterData.cigarettePerYr || null,
      cigaretteType: lungRegisterData.cigaretteType || null,
      cigaretteQuitDate: lungRegisterData.cigaretteQuitDate || null,
      recTransYn: lungRegisterData.recTransYn || null,
      recTransReason: lungRegisterData.recTransReason || null,
      urgentTransYn: lungRegisterData.urgentTransYn || null,
      urgentTransReason: lungRegisterData.urgentTransReason || null,
      curMgmtPharma: this.lungRegister.curMgmtPharma || null,
      priorityOrder: lungRegisterData.priorityOrder || null,
      rgTbRegistryPatient: patientInfoData,
      rgTbLungPleurodesisHistory: this.savePleurodesis(),
      rgTbLungDiseaseSeverity: this.saveDiseaseSeverity(),
      rgTbLungCurMgmtNonPharma: this.saveLungCurrentManagement(),
      rgTbLungTransIndication: this.saveTransplantIndicationDto(),
    };


    if(lungRegister.familyHistYn == 'N'){

      // clear the family history form.

      this.familyHistoryForm.value.rgTbFamilyHistory = [];

      this.familyHistoryForm.setControl(
        "rgTbFamilyHistory",
        this.fb.array([])
      );
      

    }

    //console.log("lungRegister data", lungRegister);

    const contactDetailsArray =
      this.contactDetailsForm.value.rgTbContactDetails;

     if (!this.validateContactDetails(contactDetailsArray)) {
      return;
    }


    const validContactDetails = [];

    contactDetailsArray.forEach((contact) => {
      if (contact.name && contact.phone && contact.email) {
        validContactDetails.push(contact);
      }
    });


    let medicineInfoDate=this.Medication.medicationForm.value.rgTbMedicineDtls;

    if (!this.validateMedications(medicineInfoDate)) {
      return;
    }

    let familyHistoryData = this.familyHistoryForm.value.rgTbFamilyHistory;

    if (!this.validateFamilyHistory(familyHistoryData)) {
      return;
    }

    let surgeryData = this.surgery.surgeryForm.value.rgTbSurgeryDtls;
    if (!this.validateSurgery(surgeryData)) {
      return;
    }

    let saveData = {
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      patientID: this.patientDetails.f.patientId.value,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_LUNG,
      rgTbPatientInfo: patientInfoData,
      rgTbLabTests: labTestsData,
      rgTbMedicineDtls: this.Medication.medicationForm.value.rgTbMedicineDtls,
      rgTbFamilyHistory: this.familyHistoryForm.value.rgTbFamilyHistory,
      rgTbSurgeryDtls: this.surgery.surgeryForm.value.rgTbSurgeryDtls,
      rgTbVaccinationInfo: vaccinationInfoData,
      rgAddContactDetails: validContactDetails,
      rgTbLungRegister: lungRegister,
    };

    //console.log(saveData);

    this._LungService.saveMainCaseDetails(saveData).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Liver Registry Saved successfully.", "success");
          this.regId = res["result"];
          //console.log("regId--", this.regId);
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire(
          "Error!",
          "Error occured while saving Liver Registry " + err.message,
          "error"
        );
      }
    );
  }

  private loadLungRegisterData(registerData: LungRegister) {
    if (registerData) {
      this.caseDetailsForm.patchValue({
        lungRegister: {
          ...registerData,
          lungId: registerData.lungId || null,
          familyHistYn: registerData.familyHistYn || null,
          smokingYn: registerData.smokingYn || null,
          recTransYn: registerData.recTransYn || null,
          urgentTransYn: registerData.urgentTransYn || null,
          curMgmtPharma: registerData.curMgmtPharma || null,
          priorityOrder: registerData.priorityOrder || null,
          cigarettePerYr: registerData.cigarettePerYr || null,
          cigaretteType: registerData.cigaretteType || null,
          cigaretteQuitDate: new Date(registerData.cigaretteQuitDate) || null,
          recTransReason: registerData.recTransReason || null,
          urgentTransReason: registerData.urgentTransReason || null,
        },
      });

      this.lungRegister = {
        ...this.lungRegister,
        ...registerData,
        lungId: registerData.lungId || null,
        cigaretteQuitDate: registerData.cigaretteQuitDate
          ? new Date(registerData.cigaretteQuitDate)
          : null,
        familyHistYn: registerData.familyHistYn || null,
        smokingYn: registerData.smokingYn || null,
        recTransYn: registerData.recTransYn || null,
        urgentTransYn: registerData.urgentTransYn || null,
        curMgmtPharma: registerData.curMgmtPharma || null,
        cigarettePerYr: registerData.cigarettePerYr || null,
        cigaretteType: registerData.cigaretteType || null,
        recTransReason: registerData.recTransReason || null,
        urgentTransReason: registerData.urgentTransReason || null,
      };
    }

    this.loadLungTransIndicationsData(registerData.rgTbLungTransIndication);
    this.loadLungTransDiseaseSeverityData(registerData.rgTbLungDiseaseSeverity);
    this.loadLungCurrentManagementData(registerData.rgTbLungCurMgmtNonPharma);
    this.loadLungPleurodesisData(registerData.rgTbLungPleurodesisHistory);
  }

  loadLungTransDiseaseSeverityData(dtoList: LungTBTransplantDiseaseSeverity[]) {
    //console.log('Severity::',this.lungTransDiseaseSeverity);

    dtoList.forEach((dto) => {
      const severity = this.lungTransDiseaseSeverity.find(
        (s) => s.paramId === dto.classId
      );
      if (severity) {
        severity.checked = true;
        severity.score = dto.score;
        severity.comments = dto.remarks;
      }
    });
  }

  loadLungCurrentManagementData(dtoList: LungCurMgmtNonPharmaDto[]) {
    this.lungCurrentMgmt = this.lungCurrentMgmt.map((item) => {
      const savedItem = dtoList.find((i) => i.pharmaId === item.paramId);
      if (savedItem) {
        return {
          ...item,
          checked: true,
          comments: savedItem.remarks,
        };
      }
      return item;
    });
  }

  loadLungPleurodesisData(dtoList: PleurodesisDto[]) {
    const rgTbLungPleurodesisHistory: any[] = dtoList;
    const faPleurodesis: FormArray = this.pleurodesisMgmtForm.get(
      "rgTbPleurodesis"
    ) as FormArray;

    // Clear existing data if needed
    faPleurodesis.clear();
    this.pleurodesisList = [];

    for (let pleuro of rgTbLungPleurodesisHistory) {
      this.addPleurodesis(
        //pleuro.runId,
        pleuro.doneDate,
        pleuro.laterality,
        pleuro.methodUsed,
        pleuro.indication,
        pleuro.remarks,
        pleuro.source,
        false // isEditable
      );
    }
  }

  loadLungTransIndicationsData(dtoList: LungTBTransIndicationDto[]) {
    // Reset all selections
    this.lungTransIndications.forEach((indication) => {
      indication.checked = false;
      indication.selectedDiseases = [];
    });

    dtoList.forEach((dto) => {
      // Find the matching indication
      // console.log('DTO:', dto);
      const indication = this.lungTransIndications.find(
        (i) => i.paramId === dto.indicationId
      );

      if (indication) {
        indication.checked = true;
        //this.onIndicationChange(indication,dto.diseaseId);

        // Find the full disease object in subOptions
        //  console.log('Indication found:', indication);
        // put 7 and 8 static
        if (!indication.subOptions || indication.subOptions.length === 0) {
          this._masterService
            .getLungTransplantDiseaseMast(indication.paramId)
            .subscribe((res) => {
              if (res && res.result) {
                indication.subOptions = res.result.map((disease) => ({
                  id: disease.paramId,
                  name: disease.paramName,
                }));
              }
              //console.log('Suboptions:', indication.subOptions);
              // assign array of ids
              indication.selectedDiseases = dtoList
                .filter((d) => d.indicationId === indication.paramId)
                .map((d) => d.diseaseId);
            });
        } else {
          indication.selectedDiseases = indication.subOptions.filter((opt) =>
            dtoList.some(
              (d) =>
                d.indicationId === indication.paramId && d.diseaseId === opt.id
            )
          );
        }
      }
    });
  }

  updateDeathDetails() {
    Swal.fire({
      //title: 'Death details',
      text: "Do you want to add death details?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes",
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    });
  }

  private initializeLungRegister(): void {
    // Initialize lungRegister with default values

    this.lungRegister = {
      lungId: null,
      //centralRegno: null,
      familyHistYn: null,
      smokingYn: null,
      cigarettePerYr: null,
      cigaretteType: null,
      cigaretteQuitDate: null,
      recTransYn: null,
      recTransReason: null,
      urgentTransYn: null,
      urgentTransReason: null,
      curMgmtPharma: null,
      priorityOrder: null,
      rgTbRegistryPatient: null,
      rgTbLungPleurodesisHistory: null,
      rgTbLungDiseaseSeverity: null,
      rgTbLungCurMgmtNonPharma: null,
      rgTbLungTransIndication: null,
    };
  }

  clear() {
    this.showDownloadButton = false;
    this.showVaccineButton = false;

    this.patientDetails.clear();

    this.labTestFg = [];

    this.labnewList = [];

    this.contacts = [];
    this.famHistory = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });

    this.familyHistoryForm = this.formBuilder.group({
      rgTbFamilyHistory: this.formBuilder.array([]),
    });

    this.contactDetailsForm = this.formBuilder.group({
      rgTbContactDetails: this.formBuilder.array([]),
    });

   this.pleurodesisMgmtForm = this.formBuilder.group({
      rgTbPleurodesis: this.formBuilder.array([]),
    });

    this.surgery.surgeryForm.reset();
    this.Medication.medicationForm.reset();
    this.pleurodesisMgmtForm.reset();
    this.pleurodesisList = [];

    this.surgery.clear();
    this.Medication.clear();

    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();
    this.Vaccination.clear();
    this._sharedService.setNavigationData(null);
    this.ngOnInit();
  }

  createLabGrpItem(createLabItem: any): FormGroup {
    return this.formBuilder.group(createLabItem);
  }

  createLabItem(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      testDate: testDate,
      releasedDate: releasedDate,
      profileTestCode: profileTestCode,
      profileTestName: profileTestName,
      mohTestCode: mohTestCode,
      componentTestName: componentTestName,
      unit: unit,
      value: value,
      instCode: instCode,
      instName: instName,
      enteredBy: enteredBy,
      enteredDate: enteredDate,
      source: source,
      isEditable: isEditable,
    };
  }
  onRowEditSave(row: any) {
    let rowIndex = this.labnewList.indexOf(row);
    this.labnewList[rowIndex] =
      this.labTestForm.value.rgTbLabTestInfo[rowIndex];
    let data = this.labnewList[rowIndex];

    if (!data.mohTestCode || !data.profileTestCode || !data.instCode || !data.testDate) {
      Swal.fire("Please fill all required fields.");
      this.labnewList[rowIndex].isEditable = true;
      return;
    }

    data.componentTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.mohTestCode)
      .map((s) => s.testName)[0];
    data.instName = this.institutes
      .filter((s) => s.estCode == data.instCode)
      .map((s) => s.estName)[0];
    data.profileTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.profileTestCode)
      .map((s) => s.testName)[0];
    data.isEditable = false;
  }

  toggleAccordion(index: number) {
    // Close all accordions
    this.openAccordion = this.openAccordion.map((_, i) =>
      i === index ? !this.openAccordion[i] : false
    );
  }

  //   toggleAccordion(index: number) {
  //     this.openAccordion[index] = !this.openAccordion[index];
  // }

  getLabFromNehr(data: any = 0) {
    data.forEach((el) => {
      // console.log(el, "el")
      let date = "";

      let componentTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[7])
        .map((s) => s.testName)[0];
      let instName = this.institutes
        .filter((s) => s.estCode == el[19])
        .map((s) => s.estName)[0];
      let profileTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[3])
        .map((s) => s.testName)[0];

      this.addNewLabList(
        null,
        el[1],
        el[5],
        el[3],
        profileTestName,
        el[7],
        componentTestName,
        el[9],
        el[10],
        el[19],
        instName,
        this.loginId,
        date,
        "S",
        false
      );
    });
  }

  // deletelab(row: any) {
  //   Swal.fire({
  //     title: "Are you sure?",
  //     text: "You won't be able to revert this!",
  //     icon: "warning",
  //     showCancelButton: true,
  //     confirmButtonColor: "#3085d6",
  //     cancelButtonColor: "#d33",
  //     confirmButtonText: "Yes, delete it!",
  //   }).then((result) => {
  //     if (!result.dismiss) {
  //       this.rgTbLabTestInfo = this.labTestForm.get(
  //         "rgTbLabTestInfo"
  //       ) as FormArray;
  //       this.delRow = this.labnewList.indexOf(row);
  //       this.labnewList.splice(this.delRow, 1);
  //       this.rgTbLabTestInfo.removeAt(this.delRow);
  //     }
  //   });
  // }

  deletelab(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        // Remove from form array
        this.rgTbLabTestInfo = this.labTestForm.get(
          "rgTbLabTestInfo"
        ) as FormArray;
        this.delRow = this.labnewList.indexOf(row);

        // Remove from both full list and current page list
        const fullListIndex = this._fullLabnewList.findIndex(
          (item) =>
            item.runId === row.runId &&
            item.testDate === row.testDate &&
            item.mohTestCode === row.mohTestCode
        );

        if (fullListIndex > -1) {
          this._fullLabnewList.splice(fullListIndex, 1);
        }
        this.labnewList.splice(this.delRow, 1);
        this.rgTbLabTestInfo.removeAt(this.delRow);

        // Update total records
        this.totalLabRecords = this._fullLabnewList.length;

        // If current page is empty and there are more records, load previous page
        if (this.labnewList.length === 0 && this.totalLabRecords > 0) {
          const currentPage = Math.floor(
            this.labTestPaginator.first / this.paginationSize
          );
          const newPage = Math.max(currentPage - 1, 0);
          const newFirst = newPage * this.paginationSize;

          // Update paginator
          this.labTestPaginator.first = newFirst;
          this.onLabPageChange({
            first: newFirst,
            rows: this.paginationSize,
          });
        }
      }
    });
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
  }

  callFetchLabDataFromAlShifa() {
    let profileList = [];
    if (this.filterModelForm.controls["profileT"].value != null) {
      this.filterModelForm.controls["profileT"].value.forEach((element) => {
        profileList.push(element.mohTestCode);
      });
    }

    let body = {
      civiLId: this.patientDetails.patientForm.controls["civilId"].value,
      fromDate: this.filterModelForm.controls["fromDate"].value,
      toDate: this.filterModelForm.controls["toDate"].value,
      profile_list: profileList,
    };
    //this.patientDetails.patientForm.controls["civilId"].value;
    this._masterService.getNehrLabTest(body).subscribe((response) => {
      if (response.result.length > 0) {
        this.getLabFromNehr(response.result);
      } else {
        Swal.fire("No Records Found");
      }
      //this.getLabFromNehr(response.result);
    });
  }

  addNewlab() {
    this.addNewLabList(
      null,
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      this.loginId,
      "",
      "W",
      true
    );
  }

  addNewLabList(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ): void {
    this.rgTbLabTestInfo = this.labTestForm.get("rgTbLabTestInfo") as FormArray;

    this.labnewList = Object.assign([], this.rgTbLabTestInfo.value);
    const labItem: any = this.createLabItem(
      runId,
      testDate,
      releasedDate,
      profileTestCode,
      profileTestName,
      mohTestCode,
      componentTestName,
      value,
      unit,
      instCode,
      instName,
      enteredBy,
      enteredDate,
      source,
      isEditable
    );
    this.rgTbLabTestInfo.push(this.createLabGrpItem(labItem));

    this.labnewList.push(labItem);
    this.labnewList.slice(0, this.paginationSize);

    this.initializeLabPagination();
  }

  initRgTbFamilyHistory(
    name: any,
    relation: any,
    patientID: any,
    instID: any
  ): FormGroup {
    return this.fb.group({
      name: [name],
      relation: [relation],
      patientID: [patientID],
      instID: [instID],
    });
  }

  initRgTbContactDetails(
    name: any,
    relation: any,
    phone: any,
    email: any
  ): FormGroup {
    return this.fb.group({
      name: [name, [Validators.required]],
      relation: [relation],
      phone: [phone, [this.custom_phoneValidator]], // Default to false, can be changed when editing
      email: [email, [this.custom_emailValidator]],
    });
  }

  addNewContact(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewContactList(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewFamilyList1(
    name: any,
    relation: any,
    patientID: any,
    instID: any,
    formArr: any
  ): void {
    this.rgTbFamilyHistory = formArr.get("rgTbFamilyHistory") as FormArray;
    this.rgTbFamilyHistory.push(
      this.initRgTbFamilyHistory(name, relation, patientID, instID)
    );
  }

  removeRgTbContact(i: number) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbContactRow(control);
    }
  }

  removeAllRgTbContact(i: any) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    control.controls = [];
  }

  // Method to add a new contact row
  addRgTbContactRow(formArr: any) {
    this.addNewContactList("", "", "", "", formArr);
  }

  removeRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbFamilyHistoryRow(control);
    }
  }
  removeAllRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    control.controls = [];
  }

  addRgTbFamilyHistoryRow(formArr: any) {
    this.addNewFamilyList1(null, null, null, null, formArr);
  }

  getRelationName(relation) {
    if (this.relation != null && relation) {
      return this.relation
        .filter((s) => s.relationCode == relation)
        .map((s) => s.relationName)[0];
    }
  }

  getRgTbFamilyHistory(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbFamilyHistory.value;
  }

  getRgTbContactDetails(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbContactDetails.value;
  }

  getInstName(instID) {
    if (instID) {
      return this.institutes
        .filter((s) => s.estCode == instID)
        .map((s) => s.estName)[0];
    }
  }

  onRowEditInitFH(row: any) {
    row.isEditable = true;
    //this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }

  onRowEditSaveFH(row: any) {
    let rowIndex = this.famHistory.indexOf(row);
    this.famHistory[rowIndex] =
      this.familyHistoryForm.value.rgTbFamilyHistory[rowIndex];
    let data = this.famHistory[rowIndex];

    if (!data.name || !data.relation) {
      Swal.fire("Please fill name and relation.");
      data.isEditable = true;
      return;
    }

    data.instName = this.institutes
      .filter((s) => s.estCode == data.instID)
      .map((s) => s.estName);
    data.relationName = this.relation
      .filter((s) => s.relationCode == data.relation)
      .map((s) => s.relationName)[0];
    //data.surgeryDt = moment(data.surgeryDt, "DD-MM-YYYY").format();
    data.isEditable = false;
  }

  onRowEditInitContact(row: any) {
    row.isEditable = true; // Set the row to editable
  }

  onRowEditSaveContact(row: any) {
    let rowIndex = this.contacts.indexOf(row);
    const contactFormGroup = (
      this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray
    ).at(rowIndex);
    this.contacts[rowIndex] =
      this.contactDetailsForm.value.rgTbContactDetails[rowIndex];

    let data = this.contacts[rowIndex];

    let isValid = true;

    // Clear previous errors
    contactFormGroup.get("email").setErrors(null);
    contactFormGroup.get("phone").setErrors(null);

    ///console.log(data.email);
    if (data.email && !this.emailValidator(data.email)) {
      contactFormGroup.get("email").setErrors({ invalidEmail: true }); // Set custom error
      contactFormGroup.get("email").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (data.phone && !this.phoneValidator(data.phone)) {
      contactFormGroup.get("phone").setErrors({ invalidPhone: true });
      contactFormGroup.get("phone").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (isValid) {
      data.relationName = this.relation
        .filter((s) => s.relationCode == data.relation)
        .map((s) => s.relationName)[0];
      data.isEditable = false;
    } else {
      contactFormGroup.markAllAsTouched();
      console.log("Invalid email or phone number");
      return false;
    }
  }

  getNameControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("name");
  }

  isContactValid(row: any): boolean {
    let rowIndex = this.contacts.indexOf(row);
    const emailControl = this.getEmailControl(rowIndex);
    const phoneControl = this.getPhoneControl(rowIndex);
    const nameControl = (
      this.contactDetailsForm.get("rgTbContactDetails") as FormArray
    )
      .at(rowIndex)
      .get("name");

    // Check if all controls are valid
    return emailControl.valid && phoneControl.valid && nameControl.valid;
  }

  getPhoneControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("phone");
  }

  getEmailControl(rowIndex: number) {
    //console.log("called--"+rowIndex);
    return (this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray)
      .at(rowIndex)
      .get("email");
  }

  onAddNewFH() {
    this.addFamilyIstory("", "", "", "", "", "W", false);
    this.famHistory[this.famHistory.length - 1].isEditable = true; //editable last entering row
  }

  addFamilyIstory(
    runId: any,
    name: any,
    relation: any,
    patientId: any,
    instId: any,
    source: any,
    isEditable: any = false
  ): void {
    this.rgTbFamilyHistory = this.familyHistoryForm.get(
      "rgTbFamilyHistory"
    ) as FormArray;

    this.famHistory = Object.assign([], this.rgTbFamilyHistory.value);
    const familyHistItem: any = this.createFHItem(
      runId,
      name,
      relation,
      patientId,
      instId,
      source,
      isEditable
    );
    this.rgTbFamilyHistory.push(this.createFHGrpItem(familyHistItem));

    this.famHistory.push(familyHistItem);
    //this.famHistory[this.famHistory.length - 1].isEditable = true;
  }

  // Method to remove empty or invalid contacts
  removeEmptyContacts() {
    this.contactDetailsArray.controls =
      this.contactDetailsArray.controls.filter((control) => {
        const contact = control.value;
        return (
          contact.name &&
          contact.relation &&
          contact.phone &&
          contact.email &&
          contact.name.trim() !== "" &&
          contact.relation.trim() !== "" &&
          contact.phone.trim() !== "" &&
          contact.email.trim() !== ""
        );
      });
  }

  addContactDetails(
    runId: any,
    name: any,
    relation: any,
    phone: any,
    email: any,
    isEditable: any = false
  ): void {
    this.contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;

    const contactItem: any = this.createContactItem(
      runId,
      name,
      relation,
      phone,
      email,
      isEditable
    );

    this.contactDetailsArray.push(this.createContactGroup(contactItem));

    this.contacts.push(contactItem);
  }

  createContactGroup(contactItem: any): FormGroup {
    return this.fb.group({
      runId: [contactItem.runId || ""], // Use the value from contactItem or default to empty
      name: [contactItem.name || "", [Validators.required]], // Required validator
      relation: [contactItem.relation || ""], // Default value or can be left empty
      phone: [contactItem.phone || "", [this.custom_phoneValidator]], // Custom validator
      email: [contactItem.email || "", [this.custom_emailValidator]], // Custom validator
    });
  }

  // createContactGroup(contactItem: any): FormGroup {
  //   return this.fb.group(contactItem);
  // }

  //   createContactGroup(): FormGroup {
  //     return this.fb.group({
  //         runId: [''],  // Default value or can be left empty
  //         name: ['', [Validators.required]],  // Required validator
  //         relation: [''],  // Default value or can be left empty
  //         phone: ['', [this.custom_phoneValidator]],  // Custom validator
  //         email: ['', [this.custom_emailValidator]]  // Custom validator
  //     });
  // }

  // Helper method to create a contact item
  createContactItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    phone: any = null,
    email: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      phone: phone,
      email: email,
      isEditable: isEditable,
    };
  }

  createFHGrpItem(familyHistItem: any): FormGroup {
    return this.fb.group(familyHistItem);
  }

  createFHItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    patientId: any = null,
    instId: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      patientID: patientId,
      instID: instId,
      source: source,
      isEditable: isEditable,
    };
  }

  addNewFamilyList() {
    if (!this.rgTbFamilyHistory) {
      this.rgTbFamilyHistory = [];
    }
    this.rgTbFamilyHistory.push({
      name: "",
      relation: "",
      patientID: "",
      instID: "",
    });
    this.rgTbFamilyHistory[this.rgTbFamilyHistory.length - 1].isEditable = true;
  }

  get rgTbFamilyHistoryArray() {
    return this.familyHistoryForm.controls["rgTbFamilyHistory"] as FormArray;
  }

  get rgTbContactDetails() {
    return this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray;
  }

  deleteContact(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        this.contactDetailsArray = this.contactDetailsForm.get(
          "rgTbContactDetails"
        ) as FormArray;

        this.delRow = this.contacts.indexOf(row);
        this.contacts.splice(this.delRow, 1);

        this.contactDetailsArray.removeAt(this.delRow);

        //this.contactDetailsForm.value.rgTbContactDetails;

        this.contactDetailsForm
          .get("rgTbContactDetails")
          .setValue(this.contactDetailsArray.value);
        //this.contactDetailsForm.get('rgTbContactDetails') as FormArray).setValue(this.contactDetailsArray.value);
      }
    });
  }

  delete(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.rgTbFamilyHistory = this.familyHistoryForm.get(
          "rgTbFamilyHistory"
        ) as FormArray;
        this.delRow = this.famHistory.indexOf(row);
        this.famHistory.splice(this.delRow, 1);
        this.rgTbFamilyHistory.removeAt(this.delRow);
      }
    });
  }

  clearForm() {
    //this.caseDetailsForm.reset();

    this.showDownloadButton = false;
    this.showVaccineButton = false;

    this.patientDetails.clear();
    this.Vaccination.clear();

    this.famHistory.forEach((element) => {
      this.rgTbFamilyHistoryArray.removeAt(0);
    });
    this.famHistory = [];

    this.contacts.forEach((element) => {
      this.contactDetailsArray.removeAt(0);
    });
    this.contacts = [];
    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();

    this.labTestFg = [];
    this.labTestForm.reset();
    this.labnewList = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
  }

  navigateToRegister() {
    this._sharedService.setNavigationData({
      regNo: this.patientDetails.f.centralRegNo.value,
    });
    this._router.navigate(["lung/registry"], {
      state: { regNo: this.patientDetails.f.centralRegNo.value },
    });
  }

  navigateToDashboard() {
    this._router.navigateByUrl("lung/dashboard");
  }

  resetForm() {
    this.liverRegisterForm.reset();
    this.initializeForm();
  }

  validateNumberInput(event: any): boolean {
    const value = event.target.value;

    // Allow backspace and delete
    if (event.key === "Backspace" || event.key === "Delete") {
      return true;
    }

    // Allow only numbers, decimal point, and negative sign
    if (!/^-?\d*\.?\d*$/.test(value + event.key)) {
      event.preventDefault();
      return false;
    }

    // Check for maximum 10 digits before decimal and 2 after
    const parts = (value + event.key).split(".");
    if (parts[0] && parts[0].replace("-", "").length > 10) {
      event.preventDefault();
      return false;
    }
    if (parts[1] && parts[1].length > 2) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  openModal(downloadLabTest) {
    this.modalService.open(downloadLabTest);
  }

  print() {
    window.print();
  }

  // generatePDF() {
  //   const data = document.getElementById("case-details-form");
  //   const registrationId = this.patientDetails.f.centralRegNo.value;
  //   html2canvas(data).then((canvas) => {
  //     const imgWidth = 208;
  //     const pageHeight = 295;
  //     const imgHeight = (canvas.height * imgWidth) / canvas.width;
  //     const heightLeft = imgHeight;

  //     const contentDataURL = canvas.toDataURL("image/png");
  //     const pdf = new jsPDF("p", "mm", "a4");
  //     const position = 0;
  //     pdf.addImage(contentDataURL, "PNG", 0, position, imgWidth, imgHeight);
  //     pdf.save(`${registrationId}_case_details.pdf`);
  //   });
  // }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }


  // Helper method to update unique procedure types
  onLabPageChange(event: any) {
    const startIndex = event.first;
    const endIndex = startIndex + event.rows;

    // Make sure we have the full list stored
    if (!this._fullLabnewList || this._fullLabnewList.length === 0) {
      this._fullLabnewList = [...this.labnewList];
    }

    // Update the displayed list based on pagination
    this.labnewList = this._fullLabnewList.slice(startIndex, endIndex);
  }

  loadLabData() {
    // Your existing code to fetch lab data

    // After setting this.labnewList, add:
    this._fullLabnewList = [...this.labnewList]; // Store full list
    this.totalLabRecords = this._fullLabnewList.length;

    // Initialize with first page
    if (this.labTestPaginator) {
      this.labTestPaginator.first = 0;
    }
    this.onLabPageChange({ first: 0, rows: this.paginationSize });
  }

  initializeLabPagination() {
    // Store the full list
    this._fullLabnewList = [...this.labnewList];
    this.totalLabRecords = this._fullLabnewList.length;

    // Only show first page (10 records)
    this.labnewList = this._fullLabnewList.slice(0, this.paginationSize);

    // Reset paginator to first page if it exists
    if (this.labTestPaginator) {
      this.labTestPaginator.first = 0;
    }
  }

  loadTransplantDiseaseSeverity() {
    this._masterService.getLungTransplantDiseaseSeverity().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.lungTransDiseaseSeverity = response.result.map((item) => ({
            paramId: item.paramId,
            paramName: item.paramName,
            checked: false,
            score: null,
            comments: "",
          }));
        }
      },
      error: (error) => {
        console.error("Error loading disease severity:", error);
      },
    });
  }

  loadPleurodesisMethod() {
    this._masterService.getLungPleurodesisMethod().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.lungPleurodesisMethod = response.result.map((item) => ({
            paramId: item.paramId,
            paramName: item.paramName,
          }));
        }
      },
      error: (error) => {
        console.error("Error loading pleurodesis method:", error);
      },
    });
  }

  loadPleurodesisIndications() {
    this._masterService.getLungPleurodesisIndications().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.lungPleurodesisIndications = response.result.map((item) => ({
            paramId: item.paramId,
            paramName: item.paramName,
          }));
        }
      },
      error: (error) => {
        console.error("Error loading pleurodesis indications:", error);
      },
    });
  }

  onManagementChange(management: LungCurrentMgmt) {
    if (!management.checked) {
      management.comments = "";
    }
  }

  loadLungCurrentMgmt() {
    this._masterService.getLungCurrentMgmt().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.lungCurrentMgmt = response.result.map((item) => ({
            paramId: item.paramId,
            paramName: item.paramName,
            checked: false,
            comments: "",
          }));
        }
      },
      error: (error) => {
        console.error("Error loading current management:", error);
      },
    });
  }

  onSeverityChange(severity: LungTransplantDiseaseSeverity) {
    if (!severity.checked) {
      severity.score = null;
      severity.comments = "";
    }
  }

  onAddNewPleurodesis() {
    this.addPleurodesis(null, "", "", "", "", "W", true);
    this.pleurodesisList[this.pleurodesisList.length - 1].isEditable = true;
  }

  addPleurodesis(
    //runId: number = null,
    doneDate: Date = null,
    laterality: string = "",
    methodUsed: string = "",
    indication: string = "",
    remarks: string = "",
    source: string = "",
    isEditable: boolean = false
  ): void {
    const pleurodesisArray = this.pleurodesisMgmtForm.get(
      "rgTbPleurodesis"
    ) as FormArray;

    const pleurodesisItem = {
      // runId,
      doneDate,
      laterality,
      methodUsed,
      indication,
      remarks,
      source,
      isEditable,
    };

    pleurodesisArray.push(this.createPleurodesisMgmtForm(pleurodesisItem));
    this.pleurodesisList.push(pleurodesisItem);
  }

  createPleurodesisMgmtForm(item: any = null): FormGroup {
    return this.fb.group({
      runId: [item ? item.runId : null],
      doneDate: [item ? item.doneDate : new Date(), Validators.required],
      laterality: [item ? item.laterality : "", Validators.required],
      methodUsed: [item ? item.methodUsed : "", Validators.required],
      indication: [item ? item.indication : ""],
      remarks: [item ? item.remarks : ""],
      source: [item ? item.source : "W"],
    });
  }

  onRowEditInitPleurodesis(row: PleurodesisDto) {
    row.isEditable = true;
  }

  onRowEditSavePleurodesis(row: PleurodesisDto) {
    const rowIndex = this.pleurodesisList.indexOf(row);
    this.pleurodesisList[rowIndex] =
      this.pleurodesisMgmtForm.value.rgTbPleurodesis[rowIndex];

      if (!row.doneDate || !row.laterality || !row.methodUsed) {
        Swal.fire("Please fill all required fields.");
        this.pleurodesisList[rowIndex].isEditable = true;
        return;
      }

    this.pleurodesisList[rowIndex].isEditable = false;
  }

  deletePleurodesis(row: PleurodesisDto) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        const pleurodesisArray = this.pleurodesisMgmtForm.get(
          "rgTbPleurodesis"
        ) as FormArray;
        const index = this.pleurodesisList.indexOf(row);
        if (index > -1) {
          this.pleurodesisList.splice(index, 1);
          pleurodesisArray.removeAt(index);
        }
      }
    });
  }

  getMethodName(methodCode: any): string {
    const method = this.lungPleurodesisMethod.find(
      (m) => m.paramId === methodCode
    );
    return method ? method.paramName : "";
  }

  getIndicationName(indicationCode: any): string {
    const indication = this.lungPleurodesisIndications.find(
      (i) => i.paramId === indicationCode
    );
    return indication ? indication.paramName : "";
  }


fetchVaccineFromAlShifa() {
    // Check if estCode and civilId are not null
    this.estCode = "20068"; // Example estCode, replace with actual value

    if (this.estCode && this.currentCivilId) {
      this._LungService
        .fetchVaccineFromShifa(this.estCode, this.currentCivilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbVaccinationInfoDb: any =
                  res["result"];
                this.Vaccination.getDataFromAlshifa(rgTbVaccinationInfoDb);
              } else {
                Swal.fire("No vaccination records found.", "info");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching vaccination records: " +
                error.message,
              "error"
            );
          }
        );
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  FetchLabFromAlShifa() {
      // Check if estCode and civilId are not null
      // console.log('estCode: ' + this.estCode + ', civilId: ' + this.civilId);
      if (this.estCode && this.civilId) {
        this._LungService
          .fetchLabFromShifa(this.estCode, this.civilId)
          .subscribe(
            (res) => {
              if (res["code"] == "S0000") {
                if (res["result"] != null && res["result"].length > 0) {
                  const rgTbLabTestsDB: any = res["result"];
                  for (let labRList of rgTbLabTestsDB) {
                    this.addNewLabList(
                      labRList.runId,
                      this._sharedService.setDateFormat(labRList.testDate),
                      labRList.mohTestCode,
                      labRList.resultSummary,
                      labRList.instCode,
                      labRList.enteredBy,
                      labRList.enteredDate,
                      labRList.source,
                      false
                    );
                  }
                } else {
                  Swal.fire(
                    "No Record Found",
                    "No lab results found for the given estCode and civilId.",
                    "info"
                  );
                }
              } else {
                Swal.fire(res["message"]);
              }
            },
            (error) => {
              Swal.fire(
                "Error",
                "Error occurred while fetching lab results: " + error.message,
                "error"
              );
            }
          );
      } else {
        Swal.fire(
          "Error",
          "Please enter civilId before fetching data.",
          "warning"
        );
      }
    }

  fetchSurgeryFromAlShifa() {

      //this.estCode="20068";
      //console.log("estCode--" + this.estCode + "civilId--" + this.civilId);
      //this.civilId=this.donorForm.value.civilId;
      //this.refreshHPLC = false;
      if (this.estCode && this.currentCivilId) {
        this._LungService
          .fetchSurgeryFromShifa(this.estCode, this.currentCivilId)
          .subscribe((res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbSurgeryDtlsDB: any = res["result"];
                this.surgery.getDataFromAlshifa(rgTbSurgeryDtlsDB);
              } else {
                Swal.fire("No records found.", "info");
              }
            } else {
              Swal.fire(res["message"]);
            }
          });
      } else {
        Swal.fire(
          "Error",
          "Please enter civilId before fetching data.",
          "warning"
        );
      }
    }



    fetchMedicineDtlsFromAlShifa() {
      this.estCode="20068";
      this.patntId="10365";
      //this.civilId=this.donorForm.value.civilId;
      //this.refreshHPLC = false;
      if (this.estCode && this.patntId) {
        this._LungService
          .getLungRegistryMedicineInfo(this.estCode, this.patntId)
          .subscribe((res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbMedicineDtlsDB: any = res["result"];

               // console.log("rgTbMedicineDtlsDB: " + JSON.stringify(rgTbMedicineDtlsDB));

                this.Medication.getDataFromAlshifa(rgTbMedicineDtlsDB);
              } else {
                Swal.fire("No records found.", "info");
              }
            } else {
              Swal.fire(res["message"]);
            }
          });
      } else {
        Swal.fire(
          "Error",
          "Please enter civilId before fetching data.",
          "warning"
        );
      }
    }


    resetFormModal() {
      this.filterModelForm.reset();
    } 



    generatePDF() {
  // Step 1: Save current pagination state
  const oldPageSize = this.pageSize;

  // Step 2: Show all records in the DOM
  this.pageSize = this.totalLabRecords;

  setTimeout(async () => {
    const element = document.getElementById('case-details-form');

    if (element) {
      const canvas = await html2canvas(element, { scale: 3, useCORS: true });
      const imgData = canvas.toDataURL('image/png');

      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      const imgProps = pdf.getImageProperties(imgData);
      const imgHeight = (imgProps.height * pageWidth) / imgProps.width;

      let heightLeft = imgHeight;
      let position = 20;

      pdf.text('Lung Case Details', pageWidth / 2, 10, { align: 'center' });
      pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);

      heightLeft -= pageHeight;

      while (heightLeft > 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);

        heightLeft -= pageHeight;
      }

      pdf.save('document.pdf');
    }

    // Step 3: Restore pagination
    this.pageSize = oldPageSize;
  }, 500); // allow Angular to re-render
}


  generateJsPDF() {

    // add loader
    this.loaderService.show();

     const oldPageSize = this.pageSize;

  // Step 2: Show all records in the DOM
    this.pageSize = this.totalLabRecords;
  
    const registrationId = this.patientDetails.f.centralRegNo.value;
    const element = document.getElementById('case-details-form');

    html2canvas(element, { scale: 2 }).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      const imgProps = (pdf as any).getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      let heightLeft = pdfHeight;
      let position = 20;

      pdf.text('Lung Case Details', pdfWidth / 2, 10, { align: 'center' });
      pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
      heightLeft -= pdf.internal.pageSize.getHeight();

      while (heightLeft > 0) {
        position = heightLeft - pdfHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
        heightLeft -= pdf.internal.pageSize.getHeight();
      }

      pdf.save(`${registrationId}_case_details.pdf`);
      this.pageSize = oldPageSize;
      this.loaderService.hide();
    });

    
  }


  async generatePDFMultiPage() {
    const registrationId = this.patientDetails.f.centralRegNo.value;
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageHeight = pdf.internal.pageSize.getHeight();
    const pageWidth = pdf.internal.pageSize.getWidth();

    // ----- 1. First Page -----
    const firstPageElement = document.getElementById('case-details-form');
    if (firstPageElement) {
      const canvas = await html2canvas(firstPageElement, { scale: 3, useCORS: true });
      const imgData = canvas.toDataURL('image/png');
      const imgProps = pdf.getImageProperties(imgData);

    const imgHeight = (imgProps.height * pageWidth) / imgProps.width;
    const yOffset = imgHeight < pageHeight ? (pageHeight - imgHeight) / 2 : 0;
      pdf.text('Lung Case Details', pageWidth / 2, 10, { align: 'center' });
      // Fill exactly one A4 page (fixed height)
      pdf.addImage(imgData, 'PNG', 0, 20, pageWidth, pageHeight);
    }

    // ----- 2. Remaining Pages -----
    const restElement = document.getElementById('firstPageSection');
    if (restElement) {
      const canvas = await html2canvas(restElement, { scale: 1 });
      const imgData = canvas.toDataURL('image/png');
      const imgProps = pdf.getImageProperties(imgData);
      const imgHeight = (imgProps.height * pageWidth) / imgProps.width;

      let heightLeft = imgHeight;
      let position = 0;

      // Start second page
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);

      heightLeft -= pageHeight;

      while (heightLeft > 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);

        heightLeft -= pageHeight;
      }
    }

   pdf.save(`${registrationId}_case_details.pdf`);
  }



}
