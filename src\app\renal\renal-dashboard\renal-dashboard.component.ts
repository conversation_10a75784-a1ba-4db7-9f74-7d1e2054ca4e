import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { NgSelectComponent } from '@ng-select/ng-select';
import { RenalService } from '../renal.service';
import { RenalDashboard } from '../../_models/renal-dashboard.model';
import { RenalDashboardDisplay } from '../../_models/renal-dashboard-display.model';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from './../../_helpers/common.constants';
import { data } from 'jquery';

@Component({
  selector: 'app-renal-dashboard',
  templateUrl: './renal-dashboard.component.html',
  styleUrls: ['./renal-dashboard.component.scss']
})
export class RenalDashboardComponent implements OnInit {


  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: RenalDashboard[];
  DashboardDataFilter: RenalDashboard[];
  displayKidneyPatients: RenalDashboardDisplay[];
  displayDialysisPatients: RenalDashboardDisplay[];
  displayTransplantPatients: RenalDashboardDisplay[];
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  KidneyPatientsChart: any;
  dialysisPatientsChart: any;
  transplantPatientsChart: any;
  createTime: any;

  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _renalService: RenalService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'ageF': [null],
      'ageT': [null],
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._renalService.getDashboard().subscribe(res => {

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {


        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'RENAL', this.institeList);

        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }
      }

      this.callFilter();
    })

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {



      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

displayFilterType(){
  
  if (this.filterType === "institute") {
    this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
    
  } else if (this.filterType === "wilayat") {
    this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
 
  } else if (this.filterType === "region") {
    this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

  }
  else {
    this.filterTitle = 'All Regions';
  }

}
  setChartData() {
    this.displayKidneyPatients = [];
    this.displayDialysisPatients = [];
    this.displayTransplantPatients = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {
      let kin = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].icd };
      this.displayKidneyPatients.push(kin);

      this.displayDialysisPatients.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].dialysis });

      let tra = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].transplant };
      this.displayTransplantPatients.push(tra);
    }
    this.callChart();
  }

  callChart() {
    this.callPieChart(this.DashboardDataFilter.filter(s => s.dialysis == 'Y'), "this.dialysisPatientsChart");
   // console.log("bbbb"+ JSON.stringify(this.DashboardDataFilter.filter(s => s.dialysis == 'Y')))
    this.callPieChart(this.DashboardDataFilter.filter(s => s.transplant == 'Y'), "this.transplantPatientsChart");
    this.callChartKidneyPatients();

  }

  callChartKidneyPatients() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    //let chartArray: any[];

    //chartArray = this.DashboardDataFilter;
    this.displayKidneyPatients = this.displayKidneyPatients.filter(s => s.value != null);


    for (var n = 0; n < this.displayKidneyPatients.length; n++) {

      if (listGroup.filter(s => s.icd === this.displayKidneyPatients[n].value).length == 0) {
        const result = this.displayKidneyPatients.filter(s => s.value == this.displayKidneyPatients[n].value).length;
        let a = { icd: this.displayKidneyPatients[n].value }
        charlabels.push(this.displayKidneyPatients[n].value);
        charData.push(result);
        listGroup.push(a);
      }
    }
    /* for (var n = 0; n < this.DashboardDataFilter.length; n++) {
 
       if (listGroup.filter(s => s.icd === this.DashboardDataFilter[n].icd).length == 0) {
         const result = this.DashboardDataFilter.filter(s => s.icd == this.DashboardDataFilter[n].icd).length;
         let a = { icd: this.DashboardDataFilter[n].icd }
         charlabels.push(this.DashboardDataFilter[n].icd);
         charData.push(result);
         listGroup.push(a);
       }
     }
 */


    this.charBGColor.sort(() => Math.random() - 0.2);
    this.KidneyPatientsChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }



  }


  callPieChart(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";

    if (this.filterType === "institute") {
      // charTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      // charTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      // charTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);

    if (chartData == "this.dialysisPatientsChart") {
      this.dialysisPatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
    if (chartData == "this.transplantPatientsChart") {
      this.transplantPatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }



  }
  /* ------------  call Chart ---------------- */

}
