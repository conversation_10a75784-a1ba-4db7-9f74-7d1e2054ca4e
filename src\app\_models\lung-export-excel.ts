export class LungExportExcel {
    regNo: number;
    civilId: number;
    dateOfBirth: Date;
    age: number;
    fullName: string;
    sex: string;
    regInst: string | number;
    walCode: string | number;
    regCode: string | number;
    weight: number;
    height: number;
    bloodGroup: string;
    transplantYn: string;
    transplantUrgent: string;
    transplantReadiness: string;
    icd: string;
    deathCase: number;
}


export class LungExcel {
    regNo: number;
    civilId: number;
    dateOfBirth: Date;
    age: number;
    fullName: string;
    sex: string;
    regInst: string | number;
    walCode: string | number;
    regCode: string | number;
    weight: number;
    height: number;
    bloodGroup: string;
    transplantYn: string;
    transplantUrgent: string;
    transplantReadiness: string;
    icd: string;
    deathCase: number;
}
