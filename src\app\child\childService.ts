import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import * as AppUtils from '../common/app.utils';

@Injectable({
    providedIn: 'root'
  })
  export class ChildService {
  
    constructor(private _http: HttpClient) { }
  
  
    getchildList(data): Observable<any> {
      return this._http.post(AppUtils.GET_CHILD_LISTING, data);
    }
}