<div class="mental-register">
    <div class="row">
        <div class="col-sm-9">
            <h6 class="page-title">Mental Register</h6>
        </div>
    </div>


    <div class="accordion register">
        <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
            <ngb-panel id="patientDetails" id="ngb-panel-0">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head">
                        <h6> Patient Details</h6>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <app-patient-details #patientDetails [submitted]="submitted" [isDiabtic]="true"
                        (callMethod)="callMpiMethod()">
                    </app-patient-details>
                </ng-template>



            </ngb-panel>
        </ngb-accordion>
    </div>
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
        <ngb-panel id="RegistrationInformation" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6>Registration Information</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>
                <form [formGroup]="registrationInformationForm">
                    <div class="row">

                        <div class="col-sm-12 ">
                            <div class="row">

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Registration Date <span class="mdtr">*</span></label>
                                        <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
                                            formControlName="regDate" [ngModelOptions]="{standalone: true}"
                                            monthNavigator="true" [maxDate]="today" yearRange="1930:2030"
                                            yearNavigator="true" showButtonBar="true">
                                        </p-calendar>

                                    </div>
                                </div>

                                <div class="col-lg-2">
                                    <div class="form-group">
                                        <label>Body Weight(kg)</label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b" formControlName="weight"
                                            (blur)="getBmi()">
                                    </div>
                                </div>

                                <div class="col-lg-2">
                                    <div class="form-group">
                                        <label>Body Height(cm)</label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b" formControlName="height"
                                            (blur)="getBmi()">

                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>BMI</label>
                                        <input type="number" class="form-control form-control-sm" formControlName="bmi"
                                            readonly>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Re-admissions <span class="mdtr">*</span></label>
                                        <ng-select #entryPoint [items]="reAdmissionList" appendTo="body"
                                            [virtualScroll]="true" placeholder="Select" bindLabel="description"
                                            bindValue="id" formControlName="reAdmission">
                                            <ng-template ng-option-tmp let-item="item"
                                                let-index="index">{{item.description}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label title="Visit Purpose">Visit Purpose </label>
                                        <ng-select #entryPoint [items]="visitPurposeList" [virtualScroll]="true"
                                            placeholder="Select" appendTo="body" formControlName="visitPurpose"
                                            bindLabel="description" bindValue="id">
                                            <ng-template ng-option-tmp let-item="item"
                                                let-index="index">{{item.description}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>


                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Duration Of Symptoms<span class="mdtr">*</span></label>
                                        <ng-select #entryPoint [items]="durSymptomsList" [virtualScroll]="true"
                                            appendTo="body" formControlName="durSymptoms" placeholder="Select"
                                            bindLabel="description" bindValue="id">
                                            <ng-template ng-option-tmp let-item="item"
                                                let-index="index">{{item.description}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>


                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>ICD-10 Diagnosis<span class="mdtr">*</span></label>
                                        <ng-select #entryPoint [items]="diagnosisIcdList" appendTo="body"
                                            [virtualScroll]="true" formControlName="diagnosisIcd" placeholder="Select"
                                            bindLabel="disease" bindValue="icd">
                                            <ng-template ng-option-tmp let-item="item"
                                                let-index="index">{{item.disease}}
                                            </ng-template></ng-select>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Main Symptom<span class="mdtr">*</span></label>

                                        <ng-select class="symptom-ngselect" [items]="symptomsList"
                                            bindLabel="description" bindValue="id"
                                            [formControl]="registrationInformationForm?.controls['symptomId']"
                                            placeholder="Select" [multiple]="true" [searchable]="true"
                                            [closeOnSelect]="false" [clearable]="true">

                                            <!-- Header with one toggle checkbox -->
                                            <ng-template ng-header-tmp>
                                                <div class="flex items-center p-2">
                                                    <input type="checkbox" [checked]="allSelected"
                                                        (change)="toggleSelectAll($event)" id="selectAllCheckbox" />
                                                    <label for="selectAllCheckbox" class="ml-2 cursor-pointer">
                                                        {{ allSelected ? 'Unselect All' : 'Select All' }}
                                                    </label>
                                                </div>
                                            </ng-template>

                                            <!-- Option template with checkboxes -->
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div>
                                                    <input id="item-{{ index }}" type="checkbox"
                                                        [checked]="item$.selected" [ngModel]="item$.selected"
                                                        [ngModelOptions]="{ standalone: true }" />
                                                    {{ item.description }}
                                                </div>
                                            </ng-template>
                                        </ng-select>

                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Medical comorbidities<span class="mdtr">*</span></label>


                                        <ng-select class="symptom-ngselect" [items]="comorbiditiesList"
                                            bindLabel="disease" bindValue="icd"
                                            [formControl]="registrationInformationForm?.controls['comId']"
                                            placeholder="Select" [multiple]="true" [searchable]="true"
                                            [closeOnSelect]="false" [clearable]="true">

                                            <!-- Header with one toggle checkbox -->
                                            <ng-template ng-header-tmp>
                                                <div class="flex items-center p-2">
                                                    <input type="checkbox" [checked]="allComSelected"
                                                        (change)="toggleSelectAllComorbidities($event)"
                                                        id="selectAllCheckbox" />
                                                    <label for="selectAllCheckbox" class="ml-2 cursor-pointer">
                                                        {{ allSelected ? 'Unselect All' : 'Select All' }}
                                                    </label>
                                                </div>
                                            </ng-template>

                                            <!-- Option template with checkboxes -->
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div>
                                                    <input id="item-{{ index }}" type="checkbox"
                                                        [checked]="item$.selected" [ngModel]="item$.selected"
                                                        [ngModelOptions]="{ standalone: true }" />
                                                    {{ item.disease }}
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>


                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label>Blood Group</label>
                                        <ng-select #entryPoint [items]="bloodGroupList" appendTo="body"
                                            [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                            formControlName="bloodGroup">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label title="Alcohol consumption">Alcohol Consumption</label>
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="alcoholUsePresent" class="form-check-input"
                                                    formControlName="alcoholUse" value="Y" />
                                                <label for="alcoholUsePresent" class="form-check-label">Yes</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="alcoholUseAbsent" class="form-check-input"
                                                    formControlName="alcoholUse" value="N" />
                                                <label for="alcoholUseAbsent" class="form-check-label">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label title="Substance misuse">Substance Misuse</label>
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="substanceMisusePresent" class="form-check-input"
                                                    formControlName="substanceMisuse" value="Y" />
                                                <label for="substanceMisusePresent" class="form-check-label">Yes</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="substanceMisuseAbsent" class="form-check-input"
                                                    formControlName="substanceMisuse" value="N" />
                                                <label for="substanceMisuseAbsent" class="form-check-label">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label title="Family history">Family History of Mental Health
                                            Condition</label>
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="familyHistoryPresent" class="form-check-input"
                                                    formControlName="familyHistory" value="Y" />
                                                <label for="familyHistoryPresent" class="form-check-label">Yes</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" id="familyHistoryAbsent" class="form-check-input"
                                                    formControlName="familyHistory" value="N" />
                                                <label for="familyHistoryAbsent" class="form-check-label">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <div class="mcard shadow-sm rounded-3 w-100">
                                    <div *ngIf="menLabInvestR && menLabInvestR.length" class="mcard-body">
                                        <div class="table-responsive"><!-- makes it scrollable on small screens -->
                                            <table #bLabtable
                                                class="table table-bordered table-hover align-middle text-center mb-0">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="8%">Select</th>
                                                        <th width="35%">Test Name</th>
                                                        <th width="25%">Test Result</th>
                                                        <th width="32%">Remark</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        *ngFor="let item of menLabInvestR | slice: (page1-1) * pageSize : (page1-1) * pageSize + pageSize">
                                                        <td>
                                                            <input type="checkbox" [value]="item.paramId"
                                                                id="check-{{item.paramId}}"
                                                                (change)="onChicked(item,$event)"
                                                                [checked]="item.checked" />
                                                        </td>
                                                        <td class="text-start fw-semibold">{{item.paramName}}</td>
                                                        <td>
                                                            <input class="form-control form-control-sm text-center"
                                                                type="text" placeholder="Enter result" id="result"
                                                                [value]="item.result != undefined ? item.result : null"
                                                                (input)="onInput(item,$event)"
                                                                [disabled]="!item.checked ? true : null" />
                                                        </td>
                                                        <td>
                                                            <input class="form-control form-control-sm text-center"
                                                                type="text" id="remarks" placeholder="Enter Remarks"
                                                                [value]="item.remarks != undefined ? item.remarks : null"
                                                                (input)="onInput(item,$event)"
                                                                [disabled]="!item.checked ? true : null" />
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Pagination -->
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <small class="text-muted">
                                                Showing {{ startIndex }} - {{ endIndex }} of {{ menLabInvestR.length }}
                                            </small>
                                            <ngb-pagination class="mb-0" [(page)]="page1" [pageSize]="pageSize"
                                                [collectionSize]="menLabInvestR.length" size="sm">
                                            </ngb-pagination>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </ng-template>
        </ngb-panel>

        <ngb-panel id="RiskAssessmentPanel">
            <div class="row"><ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">

                        <h6>
                            Risk assessment

                        </h6>

                        <h6 style="
                            padding-right: 44px;
                        ">
                            Scale used

                        </h6>

                        <button ngbPanelToggle class="btn btn-link p-0">
                            <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                        </button>
                    </div>
                </ng-template>
            </div>

            <ng-template ngbPanelContent>
                <form [formGroup]="riskAssessment">
                    <div class="row">

                        <div class="col-6">


                            <div class="row">
                                <div class="col-3">
                                    <label class="fw-semibold">Suicidal Risk:</label>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="suicidalRisk"
                                            id="A" value="A">
                                        <label class="form-check-label" for="A">Absent</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="suicidalRisk"
                                            id="L" value="L">
                                        <label class="form-check-label" for="L">Low</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="suicidalRisk"
                                            id="H" value="H">
                                        <label class="form-check-label" for="H">High</label>
                                    </div>
                                </div>



                                <!-- Homicidal Risk -->

                                <div class="col-3">
                                    <label class="fw-semibold">Homicidal Risk:</label>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="homicidalRisk"
                                            id="A" value="A">
                                        <label class="form-check-label" for="A">Absent</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="homicidalRisk"
                                            id="L" value="L">
                                        <label class="form-check-label" for="L">Low</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="homicidalRisk"
                                            id="H" value="H">
                                        <label class="form-check-label" for="H">High</label>
                                    </div>
                                </div>

                                <!-- Infanticide Risk -->

                                <div class="col-3">
                                    <label class="fw-semibold">Infanticide Risk:</label>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="infanticidalRisk"
                                            id="A" value="A">
                                        <label class="form-check-label" for="A">Absent</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="infanticidalRisk"
                                            id="L" value="L">
                                        <label class="form-check-label" for="L">Low</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="infanticidalRisk"
                                            id="H" value="H">
                                        <label class="form-check-label" for="H">High</label>
                                    </div>
                                </div>


                                <!-- Aggression Risk -->

                                <div class="col-3">
                                    <label class="fw-semibold">Aggression Risk:</label>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="violenceRisk"
                                            id="A" value="A">
                                        <label class="form-check-label" for="A">Absent</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="violenceRisk"
                                            id="L" value="L">
                                        <label class="form-check-label" for="L">Low</label>
                                    </div>

                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" formControlName="violenceRisk"
                                            id="H" value="H">
                                        <label class="form-check-label" for="H">High</label>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- Scale Used -->

                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Name of Scale Used:</label>

                                <ng-select class="symptom-ngselect" [items]="scaleList" bindLabel="description"
                                    bindValue="id" [formControl]="riskAssessment?.controls['scaleId']"
                                    placeholder="Select" [multiple]="true" [searchable]="true" [closeOnSelect]="false"
                                    [clearable]="true">

                                    <!-- Header with one toggle checkbox -->
                                    <ng-template ng-header-tmp>
                                        <div class="flex items-center p-2">
                                            <input type="checkbox" [checked]="allScaleSelected"
                                                (change)="toggleSelectAllScale($event)" id="selectAllCheckbox" />
                                            <label for="selectAllCheckbox" class="ml-2 cursor-pointer">
                                                {{ allSelected ? 'Unselect All' : 'Select All' }}
                                            </label>
                                        </div>
                                    </ng-template>

                                    <!-- Option template with checkboxes -->
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div>
                                            <input id="item-{{ index }}" type="checkbox" [checked]="item$.selected"
                                                [ngModel]="item$.selected" [ngModelOptions]="{ standalone: true }" />
                                            {{ item.description }}
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </div>

                </form>
            </ng-template>
        </ngb-panel>
        <ngb-panel id="ManagementplanPanel">
            <div class="row">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Management plan</h6>
                        <button ngbPanelToggle class="btn btn-link p-0">
                            <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                        </button>
                    </div>
                </ng-template>
            </div>
            <ng-template ngbPanelContent>
                <form [formGroup]="managementplan" class="form-inline">
                    <div class="col-2 px-0">
                        <div class="form-group">
                            <input type="checkbox" class="form-check-input" formControlName="lifestyleMod"
                                id="LifestyleModificationsCheckbox">
                            <label class="form-check-label" for="LifestyleModificationsCheckbox">Lifestyle
                                modifications</label>
                            <br>
                        </div>

                    </div>
                    <div class="col-3 px-0">
                        <div class="form-group">
                            <input type="checkbox" class="form-check-input" formControlName="psychotherapy"
                                id="psychotherapyCounselingCheckbox">

                            <label class="form-check-label d-block mr-2" for="psychotherapyCounselingCheckbox">
                                psychotherapy/Counseling
                            </label>


                            <!-- show dropdown only if checkbox is checked -->

                            <ng-select *ngIf="managementplan.get('psychotherapy')?.value" class="symptom-ngselect"
                                [items]="psychotherapyList" bindLabel="description" bindValue="id"
                                [formControl]="managementplan?.controls['medId']" placeholder="Select"
                                [multiple]="true" [searchable]="true" [closeOnSelect]="false" [clearable]="true">
                                <ng-template ng-header-tmp>
                                    <div class="flex items-center p-2">
                                        <input type="checkbox" [checked]="allPsychotherapySelected"
                                            (change)="toggleSelectAllPsychotherapy($event)" id="selectAllCheckbox" />
                                        <label for="selectAllCheckbox" class="ml-2 cursor-pointer">
                                            {{ allSelected ? 'Unselect All' : 'Select All' }}
                                        </label>
                                    </div>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div>
                                        <input id="item-{{ index }}" type="checkbox" [checked]="item$.selected"
                                            [ngModel]="item$.selected" [ngModelOptions]="{ standalone: true }" />
                                        {{ item.description }}
                                    </div>
                                </ng-template>
                            </ng-select>



                            
                        </div>
                    </div>
                    <div class="row px-0">
                        <div class="form-group">
                            <input type="checkbox" class="form-check-input" formControlName="pharmacological"
                                id="pharmacologicalCheckbox">
                            <label class="form-check-label d-block mr-2" for="pharmacologicalCheckbox">
                                Pharmacological
                            </label>

                            <!-- show dropdown only if checkbox is checked -->

                            <ng-select *ngIf="managementplan.get('pharmacological')?.value" class="symptom-ngselect"
                                [items]="medicationsList" bindLabel="description" bindValue="id"
                                [formControl]="managementplan?.controls['medicationId']" placeholder="Select"
                                [multiple]="true" [searchable]="true" [closeOnSelect]="false" [clearable]="true">
                                <ng-template ng-header-tmp>
                                    <div class="flex items-center p-2">
                                        <input type="checkbox" [checked]="allPharmacologicalSelected"
                                            (change)="toggleSelectAllPharmacological($event)" id="selectAllCheckbox" />
                                        <label for="selectAllCheckbox" class="ml-2 cursor-pointer">
                                            {{ allSelected ? 'Unselect All' : 'Select All' }}
                                        </label>
                                    </div>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div>
                                        <input id="item-{{ index }}" type="checkbox" [checked]="item$.selected"
                                            [ngModel]="item$.selected" [ngModelOptions]="{ standalone: true }" />
                                        {{ item.description }}
                                    </div>
                                </ng-template>
                            </ng-select>

                        </div>
                    </div>



                    <div class="row px-5">
                        <div class="form-group">
                            <label>Control of mental health condition:</label>
                            <ng-select #entryPoint [items]="conditionsList" appendTo="body" [virtualScroll]="true"
                                formControlName="controlCond" placeholder="Select" bindLabel="description"
                                bindValue="id" style="padding-left: 12px; width: 250px;height: auto;">
                                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.description}}
                                </ng-template></ng-select>

                        </div>
                    </div>

                </form>
            </ng-template>
        </ngb-panel>

    </ngb-accordion>
    <div [formGroup]="followUpForm">

        <div class="accordion register">
            <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
                <ngb-panel id="ngb-panel-0">
                    <ng-template ngbPanelHeader let-opened="opened">
                        <div class="d-flex align-items-center justify-content-between card-head">
                            <h6>Follow Up</h6>
                        </div>
                    </ng-template>

                    <ng-template ngbPanelContent>
                        <div class="row">
                            <div class="col-sm-2">
                                <p-calendar class="addNewCale" appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]="today"
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
                                    (onSelect)="addNewFollowUp($event, 'get');">
                                </p-calendar>

                                <ul class="list-group followup-nav">
                                    <li class="list-group-item added-list"
                                        *ngFor="let item of menVisitDetails; let i = index"
                                        (click)="addEditFollowUp(this.followUpForm.value, 'add', item, i);"
                                        [ngClass]="{'active': selectedFUDate == item.visitDate}">
                                        {{ item.visitDate | date: 'dd-MM-yyyy' }}
                                        <button *ngIf="item.visitId == null" class="fas fa-trash"
                                            (click)="removeFollowUp(i)"></button>
                                        <button *ngIf="selectedFUDate === item.visitDate" class="fa fa-print"></button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>




    <div class="btn-container">
        <button class="btn btn-sm btn-primary" (click)="save()">Save</button>
        <button id="spaceq" class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
    </div>

</div>