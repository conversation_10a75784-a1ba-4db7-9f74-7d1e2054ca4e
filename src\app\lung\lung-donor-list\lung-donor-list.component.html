<h6>Lung Donor Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="searchForm">
        <div class="row">
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Civil ID</label>
                    <input type="number" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="fullname">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select [items]="gender" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Nationality</label>
                    <ng-select [items]="nationList" [virtualScroll]="true" placeholder="Select" bindLabel="nationality" bindValue="natCode" formControlName="nationality">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.nationality
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Date of Birth</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="dob" monthNavigator="true" yearNavigator="true"
                        placeholder="dd-mm-yyyy" yearRange="1930:2030" showButtonBar="true"></p-calendar>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Telephone Number</label>
                    <input type="text" class="form-control form-control-sm" formControlName="telNo">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Address</label>
                    <textarea class="form-control form-control-sm" rows="1" formControlName="address"></textarea>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Donating Hospital Name (In deceased donor)</label>
                    <ng-select [items]="institutes" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
                        bindValue="estCode" formControlName="instCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}</ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Donor Hospital File No.</label>
                    <input type="text" class="form-control form-control-sm" formControlName="instPatientId">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Blood Group</label>
                    <div class="chexkBoxList" style="padding-left: 30px;">
                        <ng-container *ngFor="let bloodGroup of bloodGroupList">
                            <input type="checkbox" [value]="bloodGroup.value" (change)="bloodGroupSelect($event)" /> {{
                            bloodGroup.value }}
                        </ng-container>
                    </div>
                </div>
            </div>
            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="reset" (click)="clear($event)" class="btn btn-sm btn-primary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
                </div>
            </div>
        </div>
    </form>
</div>
<div style="margin-top:20px">
    <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
    </ag-grid-angular>
    <div *ngIf="rowData && rowData.length > 0">
        <p-paginator #paginator rows={{paginationSize}} totalRecords="{{totalRecords}}" (onPageChange)="getList($event)"
            showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
            [rowsPerPageOptions]="[10, 20, 30]">
        </p-paginator>
    </div>
</div>