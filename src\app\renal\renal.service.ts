import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';

import { SharedService } from '../_services/shared.service';

@Injectable({
  providedIn: 'root'
})

export class RenalService {
  [x: string]: any;

  constructor(private _http: HttpClient, private _sharedService:SharedService) { }

  //Renal Dashboard

  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.RENAL_DASHBOARD);
  }


  //renal
  saveRenal(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_RENAL_REGISTRY, data);
  }

  getRenal(regNo, regType, civilId) {
    return this._http.get(AppUtils.GET_RENAL_REGISTRY, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_RENAL.toString()).set("civilId", civilId)
    })
  }
  // renal listing
  getRenalListing(date): Observable<any> {
    return this._http.post(AppUtils.SEARCH_RENAL, date);
  }

  //Renal Waiting List

  public getRenalWaitingList(regNo) {
    return this._http.get(AppUtils.FIND_RENAL_WAITING_LIST_RESULT, { params: new HttpParams().set("centralRegNo", regNo) })
  }
  public saveRenalWaitingList(saveList) {
    return this._http.post(AppUtils.SAVE_RENAL_WAITING_LIST, saveList)
  }

  public deleteRenalWaitingList(runid) {
    return this._http.get(AppUtils.DELETE_RENAL_WAITING_LIST, { params: new HttpParams().set("runId", runid) })

  }

  //DIAGNOSIS

  public deleteDiagnosis(runId) {
    return this._http.get(AppUtils.DELETE_DIAGNOSIS, { params: new HttpParams().set("runId", runId) })
  }


  //Scores
  saveScores(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_RENAL_SCORES, data);
  }

  getScores(regNo) {
    return this._http.get(AppUtils.GET_RENAL_SCORES, { params: new HttpParams().set("centralRegNo", regNo) })
  }


  //Case Details
  SaveCaseDetails(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_RENAL_CASE_DETAILS, data);
  }

  getCaseDetails(regNo) {
    return this._http.get(AppUtils.GET_RENAL_CASE_DETAILS, { params: new HttpParams().set("regNo", regNo).set("regType", AppUtils.REG_TYPE_RENAL.toString()) })
  }


  // Brain Death Determination
  getBrainDeathExam() {
    return this._http.get(AppUtils.RENAL_BRAIN_DEATH_EXAM_PARA);
  }
  saveBrainDeathDetermination(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_RENAL_BRAIN_DEATH_REGISTER, data);
  }
  getBrainDeathDetermination(civilId ,callType) {
    return this._http.get(AppUtils.GET_RENAL_BRAIN_DEATH_REGISTER, { params: new HttpParams().set("civilId", civilId).set("callType", callType) });
  }

  getPatientsDeceasedDonor(donorID) {
    return this._http.get(AppUtils.GET_PATIENTS_DECEASED_DONOR, { params: new HttpParams().set("donorID", donorID) })
  }

  updateRenalDonorPatient(donorID,regNo) {
    return this._http.get(AppUtils.UPDATE_RENAL_DONOR_PATIENT, { params: new HttpParams().set("donorID", donorID).set("regNo", regNo) })
  }
  


  //api renal excel
  getRenalExcel(compParam: any): Observable<any> {
    let httpOptions: any = {
      headers: new HttpHeaders({}),
    };
    httpOptions.responseType = 'blob';
    return this._http.post(AppUtils.RENAL_RENALEXCEL, JSON.stringify(compParam), httpOptions);
    
  }



  notify(civilId) {
    return this._http.get(AppUtils.NOTIFY_RENAL_BRAIN_DEATH_REGISTER, {params: new HttpParams().set("civilId" , civilId)});
  }


  getBrainDeathData(req): Observable<any> {
    return this._http.post(AppUtils.SEARCH_BRAIN_DEATH, req);
  }

  getBrainDeathListing(data): Observable<any> {
    return this._http.post(AppUtils.SEARCH_BRAIN_DEATH_LIST, data);
  }

  
}