<h6 class="page-title">Liver Listing</h6>

<div class="content-wrapper mb-2">
  <form [formGroup]="liverSearchForm">
    <div class="row">

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Civil Id</label>
          <input  type="number"
            class="form-control form-control-sm" formControlName="civilId" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Registration No</label>
          <input type="number"
            class="form-control form-control-sm" formControlName="centralRegNo" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Gender</label>
          <ng-select [items]="gender" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="sex">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Age (from)</label>
          <input type="number"
            class="form-control form-control-sm" formControlName="ageFrom" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>To</label>
          <input type="number"
            class="form-control form-control-sm" formControlName="ageTo" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Region</label>
          <ng-select [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
            bindValue="regCode" formControlName="regCode" (change)="regSelect($event, 'region')">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Wilayat</label>
          <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="walName"
            bindValue="walCode" formControlName="walCode" (change)="walSelect($event, 'wilayat')">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.walName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Institute</label>
          <ng-select [items]="institeListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
            bindValue="estCode" formControlName="estCode">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Cause of Liver Disease</label>
          <ng-select [items]="icdLiverList" [virtualScroll]="true" placeholder="Select" bindLabel="codeDisease"
            bindValue="code" formControlName="causeLiver">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.codeDisease }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Transplant</label>
          <ng-select [items]="yesNo" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="transplant">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>

      <!-- Blood Group -->
      <div class="col-lg-4 col-md-3 col-sm-3">
        <div class="form-group">
          <label><strong>Blood Group</strong></label><br />
          <div class="chexkBoxList" style="padding-left: 30px;">
            <ng-container *ngFor="let bloodGroup of bloodGroupList">
              <input type="checkbox" [value]="bloodGroup.value" (change)="bloodGroupSelect($event, 'group')" /> {{
              bloodGroup.value }}
            </ng-container>
          </div>
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Readiness of Transplant</label>
          <ng-select [items]="yesNo" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="transplantReadiness">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>
      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <div class="btn-box">
          <button type="button" (click)="exportExcel()" class="btn btn-primary ripple">EXCEL</button>
          <button type="reset" (click)="clear($event)" class="btn btn-sm btn-secondary">Clear</button>
          <button type="submit" (click)="onSearch()" class="btn btn-sm btn-primary">Search</button>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- <div style="margin-top: 20px">
  <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>
</div> -->

<!-- <div class="content-wrapper mt-2">
  <div style="margin-top:20px">


    <div style="margin-top:20px">

      <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
      </ag-grid-angular>

    </div>

    <div *ngIf="rowData.length > 0">
      <p-paginator #LiverRegPaginator rows={{paginationSize}} totalRecords="{{totalRecords}}"
        (onPageChange)=" onSearch($event)" showCurrentPageReport="true"
        currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10">
      </p-paginator>
    </div>

  </div>
</div> -->

<div style="margin-top:20px">
  <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>

  <div *ngIf="rowData && rowData.length > 0">
    <p-paginator #paginator rows={{paginationSize}} totalRecords="{{totalRecords}}"
      (onPageChange)="onSearch($event)" showCurrentPageReport="true"
      currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
      [rowsPerPageOptions]="[10, 20, 30]">
    </p-paginator>
  </div>

</div>
