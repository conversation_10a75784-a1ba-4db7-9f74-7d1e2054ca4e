<h6>Organ Donor Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="organDonorSearch">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil ID</label>
                    <input type="text" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age From</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ageFrom">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age To</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ageTo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <select class="form-control form-control-sm" formControlName="sex">
                        <option value=''>--Select--</option>
                        <option value='M'>Male</option>
                        <option value='F'>Female</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Mobile No</label>
                    <input type="text" class="form-control form-control-sm" formControlName="mobileNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Email</label>
                    <input type="text" class="form-control form-control-sm" formControlName="email">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Family Contact No</label>
                    <input type="text" class="form-control form-control-sm" formControlName="relationContactNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>First Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="firstName">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Second Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="secondName">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Third Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="thirdName">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Tribe</label>
                    <input type="text" class="form-control form-control-sm" formControlName="tribe">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality">
                        <option value=''>--Select--</option>
                        <option *ngFor="let natio of nationalityList" [value]="natio.natCode">{{natio.nationality}}
                        </option>
                    </select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registered From</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="createdTimeFrom" monthNavigator="true"
                        yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
                        showButtonBar="true">
                    </p-calendar>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registered To</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="createdTimeTo" monthNavigator="true"
                        yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true"
                        showButtonBar="true">
                    </p-calendar>
                </div>
            </div>
            

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Organ</label>
                    <select class="form-control form-control-sm" formControlName="organ">
                        <option value=''>--Select--</option>
                        <option value='Kidneys'> Kidneys</option>
                        <option valeu='Liver'>Liver</option>
                        <option value='Heart'>Heart</option>
                        <option value='Pancreas'>Pancreas</option>
                        <option value='Corneas'>Corneas</option>
                        <option value='Lungs'>Lungs</option>
                    </select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>File Status</label>
                    <ng-select #entryPoint [items]="formCompleted" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="fileStatus">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Status</label>
                    <ng-select #entryPoint [items]="organStatus" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="afterDeathYn">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="submit"  (click)="exportToExcel()" class="btn btn-primary ripple" > Excel</button>
                   
                     <button type="submit" class="btn btn-sm btn-primary" (click)="getOrganDonorsList()">Search</button>
                    <button type="reset" class="btn btn-sm btn-primary" (click)="clear();">Clear</button>
                </div>
            </div>
        </div>
    </form>
</div>
    
<div style="margin-top:20px">
    <div class="grid-container">
        <div class="grid-item">
            <ag-grid-angular style="width: 100%; height: 500px;" class="ag-theme-balham" [rowData]="rowData" 
                [gridOptions]="gridOptions" [columnDefs]="columnDefs" >
            </ag-grid-angular>

        </div>
        <div class="grid-item">
            <p-paginator *ngIf="rowData.length > 0" #organRegPaginator rows="10"
                totalRecords="{{totalRecords}}" (onPageChange)="getOrganDonorsList($event)" showCurrentPageReport="true"
                currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
                [rowsPerPageOptions]="[10, 20, 30]">
            </p-paginator>
        </div>
    </div>
</div>