import { Response } from '@angular/http';
import { Observable,BehaviorSubject } from 'rxjs/Rx';
import { Injectable } from '@angular/core';
import { LoginService } from '../login/login.service';
import { PrivilegeModel } from '../common/objectModels/privilege-model';
import { UserPrefModel } from '../common/objectModels/user-pref-model';
import * as AppUtils from '../common/app.utils';
import { HttpClient } from '@angular/common/http';
import { UserModel } from '../common/objectModels/user-model';
import * as CommonConstants from '../_helpers/common.constants';

@Injectable()
export class UserService {
    private loginService: LoginService;
    privileges: PrivilegeModel[] = [];
    isPrivilege: boolean;
    userRolesAndMenu : Observable<any>;
    preferences: Observable<UserPrefModel[]>;
    private currentUserSubject = new BehaviorSubject<UserModel>(null as UserModel);//BehaviourSubject sends to the components about any value change in the User where it has been subscribed
    //To subscribe the behaviour subject we will declare an observable 'currentUser' which will return the current value of 'user' object (loggedUserObject). 
    public getCurrentUser = this.currentUserSubject.asObservable();

    constructor(private http: HttpClient) {
    }

    /**
     * To check whether the logged in  user has the given privilege.
     * @param privilege privilege to check
     * @returns Return true or false value after verifying.
     */
    hasPrivilege(privilege: string): Observable<any> {
        let privileges: PrivilegeModel[] = [];
        return this.getUserRolesAndMenus().map((result) => {
            this.isPrivilege = result.roles.filter((element: any) => {
                return element.privileges.filter((item: any) => {
                    return item.name === privilege;
                }).length != 0
            }).length != 0
            return this.isPrivilege;
        })
    }
    /**
     * To get the user roles and menus. 
     * @param privilege given user privilege.
     * @returns Return boolean value, whether the user have the given privilege or not.
     */
    getUserRolesAndMenus(): Observable<any> {
        if(this.userRolesAndMenu == null){
            this.userRolesAndMenu =  this.http.get(AppUtils.GET_USER_ROLES_AND_MENUS + CommonConstants.SYSTEM_ID)
                .map(response =>
                    response['result']
                )
                .catch(this.handleError)
                .publishReplay(1)
                .refCount();
        }
            return this.userRolesAndMenu ;
    }

    /**
     * <AUTHOR>
     * @date 05Nov2017
     * To get the user preferences based on the perscode
     * @param perscode
     * @returns Return the user preferences which is already configured based on perscode
     */
    // getUserPreferences(perscode: number): Observable<UserPrefModel[]> {
    //     if (perscode != undefined) {
    //         this.preferences = this.http.get(AppUtils.GET_USER_PREFERENCES + '/' + perscode)
    //             .map(response => response['result'])
    //             .catch(this.handleError);
    //     }
    //     return this.preferences;
    // }

    /**
     * To clear user data
     */
    clearUserData(){
        this.userRolesAndMenu=null;
    }


    private handleError(error: any) {
        return Observable.throw(error || 'Server error');
    }

    /* To set the currentUser(currently logged in User details) */
    setCurrentUser(user: UserModel) {
        //This will update the curerntUser details
        // Since we are using it as a type of BehaviorSubject, this will modify the currentUser value to everywhere where this has been subscribed.
        this.currentUserSubject.next(user);
    }
}