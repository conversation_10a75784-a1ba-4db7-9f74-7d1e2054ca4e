import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { WallayatDataModel } from 'src/app/common/objectModels/wallayat-model';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { AsthmaService } from '../asthma.service';
import * as AppComponent from '../../common/app.component-utils';
import { AsthmaDashboard } from 'src/app/_models/asthma-dashboard.model';
import { AsthmaDashboardDisplay } from 'src/app/_models/asthma-dashboard-display.model';

@Component({
  selector: 'app-asthma-dashboard',
  templateUrl: './asthma-dashboard.component.html',
  styleUrls: ['./asthma-dashboard.component.scss']
})
export class AsthmaDashboardComponent implements OnInit {

  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: AsthmaDashboard[];
  DashboardDataFilter: AsthmaDashboard[];

  displayBreathingPorblem: AsthmaDashboardDisplay[];
  displayWheezing: AsthmaDashboardDisplay[];
  displayChildrenAsthma: AsthmaDashboardDisplay[];
  displayFileStatus: any;

  

  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];

  breathingPorblemChart: any;
  wheezingChart: any;
  childrenAsthmaChart: any;
  fileStatusChart: any;

  createTime: any;
  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _asthmaService: AsthmaService) { 
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }

  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'ageF': [null],
      'ageT': [null],
    });
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }
  callReset() {
    window.location.reload();
  }

  getDashboardData() {
    this._asthmaService.getDashboard().subscribe(res => {
      if (res['code'] == "S0000" || res['code'] == "F0000") {
        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'RENAL', this.institeList);

        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }
      }

      this.callFilter();
    })

  }
  callFilter(){
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {
      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

  displayFilterType(){
  
    if (this.filterType === "institute") {
      this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      
    } else if (this.filterType === "wilayat") {
      this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
   
    } else if (this.filterType === "region") {
      this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
  
    }
    else {
      this.filterTitle = 'All Regions';
    }
  }


  setChartData() {

    this.displayBreathingPorblem =[];
    this.displayWheezing = [];
    this.displayChildrenAsthma = [];
    this.displayFileStatus = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {


      let fileStatus = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].fileStatus };
      this.displayFileStatus.push(fileStatus);

      let breathingProblem = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].breathingProblem };
      this.displayBreathingPorblem.push(breathingProblem);

      this.displayWheezing.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].wheezing });

      let childrenAsthma = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].childrenAsthma };
      this.displayChildrenAsthma.push(childrenAsthma);
    }
    this.callChart();
  }

  callChart() {
    this.callPieChart(this.DashboardDataFilter.filter(s => s.breathingProblem == 'Y'), "this.displayBreathingPorblem");
    this.callPieChart(this.DashboardDataFilter.filter(s => s.wheezing == 'Y'), "this.displayWheezing");
    this.callPieChart(this.DashboardDataFilter.filter(s => s.childrenAsthma == 'Y'), "this.displayChildrenAsthma");
 
    this.callChartFileStatus();

  }

  callPieChart(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any= "";

    if (this.filterType === "institute") {
      // charTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      // charTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      // charTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }

    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }

    this.charBGColor.sort(() => Math.random() - 0.3);

    if (chartData == "this.displayBreathingPorblem") {
      this.breathingPorblemChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

    if (chartData == "this.displayWheezing") {
      this.wheezingChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

    if (chartData == "this.displayChildrenAsthma") {
      this.childrenAsthmaChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

  }












  callChartFileStatus() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    this.displayFileStatus = this.displayFileStatus.filter(s => s.value != null);


    for (var n = 0; n < this.displayFileStatus.length; n++) {

      if (listGroup.filter(s => s.icd === this.displayFileStatus[n].value).length == 0) {
        const result = this.displayFileStatus.filter(s => s.value == this.displayFileStatus[n].value).length;
        let a = { icd: this.displayFileStatus[n].value }
        charlabels.push(this.displayFileStatus[n].value);
        charData.push(result);
        listGroup.push(a);
      }
    }



    this.charBGColor.sort(() => Math.random() - 0.2);
    this.fileStatusChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }



  }

}
