<p>Diabetic Registry Listing</p>
<div class="content-wrapper mb-2">
    <form [formGroup]="diabeticSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil Id</label>
                    <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Patient Id</label>
                    <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm" formControlName="patientId">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registration No</label>
                    <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm"
                        formControlName="centralRegNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sexCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age From</label>
                    <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm" formControlName="ageFrom">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age To</label>
                    <input (keypress)="numberOnly($event)" type="text"  class="form-control form-control-sm" formControlName="ageTo">
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality">
                        <option selected [value]="null">Select </option>
                        <option [value]="res.nationality" *ngFor="let res of nationalityList">{{res.nationality}}
                        </option>
                    </select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>File Status</label>
                    <select class="form-control form-control-sm" formControlName="fileStatus">
                        <option [value]=null></option>
                        <option [value]="1">Active</option>
                        <option [value]="2">transfered out</option>
                        <option [value]="3">Expired</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="regSelect($event,'region')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode"
                        (change)="walSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="regInst">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <label>Registration</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="regDateFrom" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="From"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="regDateTo" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="To"
                                showButtonBar="true"
                                [maxDate]="currentDate"
                                >
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
       

            <!-- <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>BMI</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="bmi">
                </div>
            </div>
             <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>10 Year Cardio vascular Disease</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="cvd10Year">
                </div>
            </div> 

             <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>LDL</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="ldl">
                </div>
            </div> -->
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>HbA1c</label>
                    <ng-select #entryPoint [items]="hba1cList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="hba1c" (change)=" pickValues($event)"> 
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <!-- <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>EGFR</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="egfr">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Urine Acr</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="urineAcr">
                </div>
            </div> -->

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>DiabetsType</label>
                    <ng-select #entryPoint [items]="diabetsTypeMaster" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="diabetesType"
                        (change)="onChange($event,'diabeticType')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Mode of presentation</label>
                    <ng-select [items]="modeOfPrestList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                    bindValue="id" formControlName="modePresentaion">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                    </ng-template>
                </ng-select>
                </div>
            </div>



            <div class="col-lg-3 col-xl-2">
                <label>Diagnose Date</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <!-- <label>Registered From</label> -->
                            <p-calendar dateFormat="dd-mm-yy" formControlName="diaDateFrom" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="From"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <!-- <label>Registered To</label> -->
                            <p-calendar dateFormat="dd-mm-yy" formControlName="diaDateTo" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="To"
                                showButtonBar="true"
                                [maxDate]="currentDate"
                                >
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>

           
            
     
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Ketones</label>
                    <ng-select #entryPoint [items]="ketonesList" [virtualScroll]="true" placeholder="Select"
                    bindLabel="value" bindValue="id" formControlName="ketones">
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="regInst">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3" *ngIf="showSubType">
                <div class="form-group" >
                    <label>Sub Type</label>
                    <ng-select #entryPoint [items]="diabetesSubTypesList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="diabetesSubType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>            
        </div>
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>Diagnose Type</strong></label>
                    <div class="box r-divider">
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" formControlName="regType" value="N">Newly
                                Diagnosed
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" formControlName="regType" value="O">Old
                                Diagnosed
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>Managed By</strong></label>
                    <div class="box r-divider">
                        <div class="form-check-inline">
                            <label class="form-check-label"><input type="checkbox" class="form-check-input"
                                    formControlName="lifestyleModify">Life Style Modification </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label"><input type="checkbox" class="form-check-input"
                                    formControlName="oralHypoDrugs">Oral Hypoglycemic Drugs </label>
                        </div>
                        <!-- <div class="form-check-inline">
                                <label class="form-check-label"><input type="checkbox" class="form-check-input" formControlName="metabolicProcedure">Metabolic Procedure/Surgery </label>
                            </div> -->
                        <div class="form-check-inline">
                            <label class="form-check-label"><input type="checkbox" class="form-check-input"
                                    formControlName="injGlpiAgonist">GLP-1 Agonist </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label"><input type="checkbox" class="form-check-input"
                                    formControlName="insulin" (click)="onClick($event,'insulin')">Insulin</label>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-lg-4 col-md-3 col-sm-3" *ngIf="showInsulinType">
                <div class="form-group" >
                    <label><strong>Insulin Regimen</strong></label>
                    <div class="box r-divider">
                        <div class="form-check-inline">
                            <label class="chkbox-outer"><input type="radio" name="insulinType"
                                    formControlName="insulinType" value="I">Single</label>
                        </div>
                        <div class="form-check-inline">
                            <label class="chkbox-outer"><input type="radio" name="insulinType"
                                    formControlName="insulinType" value=" S">Split (Mixed)</label>
                        </div>
                        <div class="form-check-inline">
                            <label class="chkbox-outer"><input type="radio" name="insulinType"
                                    formControlName="insulinType" value="M">MDI (Multiple Daily Injections)</label>
                        </div>
                        <div class="form-check-inline">
                            <label class="chkbox-outer"><input type="radio" name="insulinType"
                                    formControlName="insulinType" value="P">Pump</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            
            <!-- <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>BP Status</strong></label>
                    <div class="box r-divider">
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" class="form-check-input" 
                            formControlName="bp" value="1">Controlled
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" class="form-check-input" 
                            formControlName="bp" value="2">Not Controlled
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" class="form-check-input" 
                            formControlName="bp" value="3">Not Documented
                        </label>
                      </div>
                    </div>
                </div>
            </div> -->
            <!-- <div class="col-lg-6 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>Examination</strong></label>
                    <div class="box r-divider">
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="checkbox" class="form-check-input" 
                            formControlName="footUlcer" >Foot Ulcer
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="checkbox" class="form-check-input" 
                            formControlName="sensoryNeuropathy">Sensory Neuropathy
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="checkbox" class="form-check-input" 
                            formControlName="retinopathy">Retinopathy
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="checkbox" class="form-check-input" 
                            formControlName="lowerLimpAmputation">Lower LimpAmputation
                        </label>
                      </div>
                    </div>
                </div>
            </div> -->
        </div>
        <div class="row">
            
        </div> 

        <div class="text-right col-lg-12 col-md-12 col-sm-12">
            <div class="btn-box">
                <button type="submit" [disabled]="!rowData || (rowData && rowData.length == 0)"
                    (click)=" showDashboard()" class="btn btn-primary ripple"> Dashboard</button>
                <button type="submit" (click)=" exportToExcel()" class="btn btn-primary ripple"> Export Excel</button>
                <button type="reset" (click)="clear($event)" class="btn btn-sm btn-secondary">Clear</button>
                <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
            </div>
        </div>
    </form>
</div>

<div style="margin-top:20px">
    <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
    </ag-grid-angular>

    <div *ngIf="rowData && rowData.length > 0">
        <p-paginator #diabeticPaginator rows={{paginationSize}} totalRecords="{{totalRecords}}"
            (onPageChange)="getList($event)" showCurrentPageReport="true"
            currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
            [rowsPerPageOptions]="[10, 20, 30]"
            >
        </p-paginator>
    </div>

</div>
