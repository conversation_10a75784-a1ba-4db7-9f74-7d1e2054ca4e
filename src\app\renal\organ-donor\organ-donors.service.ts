import { GET_ORGAN_DONORS } from '../../common/app.utils';
import { OrganDonorModel } from '../../common/objectModels/organDonor-model';
import { HttpClient, HttpResponse,HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {Observable,of} from 'rxjs';
import * as AppUtils from '../../common/app.utils';
import { SharedService } from 'src/app/_services/shared.service';


@Injectable({
  providedIn: 'root'
})
export class OrganDonorService {
  constructor(private http:HttpClient, private _sharedService:SharedService) {
  }

  getOrganDonors(searchParam:any) :Observable<any>{
    return  this.http.post<OrganDonorModel[]>(AppUtils.GET_ORGAN_DONORS,searchParam);  
  }

  getDashboard(): Observable<any> {
    return this.http.get(AppUtils.GET_ORGAN_DONORS_DASHBOARD);
  }

  generatePDF(data): Observable<any> {
    return  this.http.post<OrganDonorModel[]>(AppUtils.GENERATE_ORGAN_DONORS_PDF,data); 
  }


  generatePDFCivilId(civil_id): Observable<any> {
    return this.http.get(AppUtils.GENERATE_ORGAN_DONORS_PDF_CIVIL_ID+"?civilID="+ civil_id);
  }



  //Organ listing
  getOrganListing(date): Observable<any> {
  
    return this.http.post(AppUtils.SEARCH_ORGAN_REGISTRY, date);
   
  }
 


  
}

 