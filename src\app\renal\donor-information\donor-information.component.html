<h6>Donor Information</h6>
<div class="content-wrapper mb-2">
  <div class="row">

    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="row">
        <div class="col-lg-6" [formGroup]="donorForm">
          <div class="form-group">
            <label>Civil ID<span class="mdtrf">*</span></label>
            <input type="text" class="form-control form-control-sm" [(ngModel)]="renalDonor.civilId"
              formControlName="civilId" (keyup)="onClickKey($event)">

          </div>
          <span *ngIf="submitted && renalDonor.civilId==null" class="tooltiptext">{{'Civil Id is required'}}</span>
        </div>

        
        <div class="col-lg-6" [formGroup]="donorForm">
          <div class="form-group">
            <label>Expiry Date <span class="mdtr">*</span></label>
            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="exDate"
              [ngModelOptions]="{standalone: true}" monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
              showButtonBar="true" (onSelect)="onExpiryDateSelect($event)"></p-calendar>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-4 col-sm-4" [formGroup]="donorForm">
      <div class="form-group">
        <label>Name</label>
        <input type="text" class="form-control form-control-sm" [(ngModel)]="renalDonor.fullname"
          formControlName="fullname" disabled>
      </div>
    </div>

    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Gender</label>
        <!-- <select class="form-control form-control-sm" formControlName="sex" [(ngModel)]="renalDonor.sex" disabled>
          <option value='M'>Male</option>
          <option value='F'>Female</option>
        </select> -->
        <ng-select #entryPoint appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true" placeholder="Select"
          bindLabel="value" bindValue="id" [(ngModel)]="renalDonor.sex" formControlName="sex">
          <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
          </ng-template>
        </ng-select>
      </div>
    </div>

    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Nationality</label>
        <select class="form-control form-control-sm" [(ngModel)]="renalDonor.nationality" formControlName="nationality"
          disabled>
          <option selected [value]="null">All Nationality</option>
          <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>
        </select>

      </div>
    </div>

    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Date of Birth</label>

        <p-calendar dateFormat="dd-mm-yy" formControlName="dob" [(ngModel)]="renalDonor.dob" monthNavigator="true"
          [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
          [disabled]="disabledCondition">
        </p-calendar>
      </div>
    </div>

    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Blood Group</label>
        <select class="form-control form-control-sm" [(ngModel)]="renalDonor.bloodGroup" formControlName="bloodGroup">

          <option value='A +'>A +</option>
          <option value='A -'>A -</option>
          <option value='B -'>B -</option>
          <option value='B +'>B +</option>
          <option value='O +'>O +</option>
          <option value='O -'>O -</option>
          <option value='AB +'>AB +</option>
          <option value='AB -'>AB -</option>
        </select>
      </div>
    </div>


    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Telephone Number</label>
        <input type="text" class="form-control form-control-sm" [(ngModel)]="renalDonor.telNo" formControlName="telNo">
      </div>
    </div>
    <div class="col-lg-3 col-md-4 col-sm-4">
      <div class="form-group" [formGroup]="donorForm">
        <label>Address</label>
        <textarea class="form-control form-control-sm" rows="1" [(ngModel)]="renalDonor.address"
          formControlName="address" disabled></textarea>
      </div>
    </div>

    <div class="col-lg-6 col-md-6 col-sm-6">
      <div>
        <div class="form-group" [formGroup]="donorForm">
          <label>Donor Type</label>
          <div class="box r-divider">
            <div class="form-check-inline">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="donorType" [(ngModel)]="renalDonor.donorType"
                  formControlName="donorType" value="D">Deceased
                Donor
              </label>
            </div>
            <div class="form-check-inline">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="donorType" [(ngModel)]="renalDonor.donorType"
                  formControlName="donorType" value="R">Living
                Related Donor
              </label>
            </div>
            <div class="form-check-inline">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="donorType" [(ngModel)]="renalDonor.donorType"
                  formControlName="donorType" value="U">Living
                Unrelated Donor
              </label>
            </div>


          </div>
        </div>



      </div>

      <div class="border-top" *ngIf="renalDonor.donorType =='R'">
        <div class="mt-2 row">

          <div class="col-lg-4 col-md-4 col-sm-4">
            <div class="form-group" [formGroup]="donorForm">
              <label>Relation Type <span class="mdtrf">*</span></label>
              <select class="form-control form-control-sm" [(ngModel)]="renalDonor.relationType"
                formControlName="relationType">
                <option selected [value]="null">Relation Type</option>
                <option [value]="res.id" *ngFor="let res of bloodRelationList">
                  {{res.value}}</option>
              </select>
              <span *ngIf="renalDonor.relationType==null && renalDonor.donorType == 'R'" class="tooltiptext">{{'Relation
                Type is required'}}</span>
            </div>
          </div>
          <!--  -->

          <div class="col-lg-3 col-md-3 col-sm-3"
            *ngIf="renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null &&  renalDonor.relationType != null ">
            <div class="linkpadding"><a class="link" (click)="openModal(linkWithPatient)"><i class="fas fa-link"></i>
                Link with Patient</a></div>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-3" style="padding-top: 35px"
            *ngIf="renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null &&  renalDonor.relationType != null ">
            <button class="btn btn-sm btn-primary" id="HLAButton" (click)="donorSelectModal(donor)">Add HLA For
              Donor</button>
          </div>


        </div>
      </div>


      <div class="border-top" *ngIf="renalDonor.donorType =='U'">
        <div class="mt-2 row">

          <div class="col-lg-4 col-md-4 col-sm-4">
            <div class="form-group" [formGroup]="donorForm">
              <label>Relation Type <span class="mdtrf">*</span></label>
              <select class="form-control form-control-sm" [(ngModel)]="renalDonor.relationType"
                formControlName="relationType">
                <option selected [value]="null">Relation Type</option>
                <option [value]="res.id" *ngFor="let res of nonBloodRelationList">
                  {{res.value}}</option>
              </select>
              <span *ngIf="renalDonor.relationType==null && renalDonor.donorType == 'U'" class="tooltiptext">{{'Relation
                Type is required'}}</span>
            </div>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-3">
            <div class="form-group" [formGroup]="donorForm">
              <label>Please Specify<span class="mdtrf">*</span></label>
              <input type="text" class="form-control form-control-sm" placeholder="Please Specify"
                [(ngModel)]="renalDonor.relationDesc" formControlName="relationDesc">
            </div>
            <div class="text-right"
              *ngIf="renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null && renalDonor.relationType != null ">
              <a class="link" (click)="openModal(linkWithPatient)"><i class="fas fa-link"></i>
                Link with Patient</a>
            </div>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-3" style="padding-top: 35px"
            *ngIf="renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null &&  renalDonor.relationType != null ">
            <button class="btn btn-sm btn-primary" id="HLAButton" (click)="donorSelectModal(donor)">Add HLA For
              Donor</button>
          </div>
        </div>
      </div>
      <!--submitted == true &&   renalDonor.relationType != null renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null && renalDonor.donorType === 'D'-->
      <div class="border-top"
        *ngIf=" renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null && renalDonor.donorType === 'D'">
        <br>
        <button class="btn btn-sm btn-primary mr-2" id="HLAButton" (click)="donorSelectModal(donor)">Add HLA For
          Donor</button>
        <button class="btn btn-sm btn-primary mr-2" id="selectPatientButton" (click)="selectPatient()">Select
          Patient</button>


      </div>




    </div>


    <div class="col-lg-3 col-md-4 col-sm-4" *ngIf="renalDonor.donorType == 'D'">
      <div class="form-group" [formGroup]="donorForm">
        <label>Donating Hospital Name (In deceased donor)<span class="mdtrf"
            *ngIf="renalDonor.donorType == 'D'">*</span></label>
        <ng-select #entryPoint [items]="hospitalsList" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
          bindValue="estCode" [(ngModel)]="renalDonor.instCode" formControlName="instCode">
          <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
          </ng-template>
        </ng-select>
      </div>
    </div>

    <!-- <div class="col-lg-3 col-md-4 col-sm-4" *ngIf="renalDonor.donorType == 'D'">
      <div class="form-group" [formGroup]="donorForm">
        <label>Donating Hospital Name (In deceased donor)<span class="mdtrf"
            *ngIf="renalDonor.donorType == 'D'">*</span></label>
            <select class="form-control form-control-sm" [(ngModel)]="renalDonor.instCode"
             formControlName="instCode">
            <option disabled selected [ngValue]="undefined">Select</option>
            <option *ngFor="let item of hospitals">{{ item }}</option>
        </select>
      </div>
    </div> -->



    <div class="col-lg-3 col-md-4 col-sm-4" *ngIf="renalDonor.donorType == 'D'">
      <div class="form-group" [formGroup]="donorForm">
        <label>Donor Hospital File No.</label>
        <input type="text" class="form-control form-control-sm" [(ngModel)]="renalDonor.instPatientId"
          formControlName="instPatientId">
      </div>
    </div>






    <div class="col-lg-3 col-md-4 col-sm-4" *ngIf="renalDonor.donorType =='R'">
    </div>

  </div>





  <div *ngIf="showSelectPatient == true && renalDonor.donorType =='D'">
    <hr>
    <button class="btn btn-sm btn-primary" (click)="confirm()" 
            >confirm
                </button>
    <div>
      <h6>Patient</h6>
      <div class="content-wrapper mb-2 lab-results">

        <!-- (click)="showscoreModal(ScoringInfo)" -->
        <p-dataTable [immutable]="false" [value]="PatientsDeceasedDonorList" [editable]="true" selectionMode="single"
          (onRowSelect)="onRowSelect($event)" [(selection)]="selectedPatient" (onRowUnselect)="onRowUnselect($event)"
          [metaKeySelection]="false" dataKey="centralRegNo" >


          <p-column field="" header="Civil ID">
            <ng-template let-row="rowData" pTemplate="body">
              <div *ngIf="!row.isEditable">{{row.civilId }}</div>
            </ng-template>
          </p-column>

          <p-column field="" header="Full Name">
            <ng-template let-row="rowData" pTemplate="body">
              <div *ngIf="!row.isEditable">{{row.fullName}}</div>

            </ng-template>
          </p-column>

          <p-column field="" header="Wilayat">
            <ng-template let-row="rowData" pTemplate="body">
              <div *ngIf="!row.isEditable">{{row.wilayat}}</div>

            </ng-template>
          </p-column>

          <p-column field="" header="Village">
            <ng-template let-row="rowData" pTemplate="body">
              <div *ngIf="!row.isEditable">{{row.village}}</div>

            </ng-template>
          </p-column>

          <p-column field="" header="Score">
            <ng-template let-row="rowData" pTemplate="body">
              <div *ngIf="!row.isEditable">{{row.score}}</div>

            </ng-template>
          </p-column>


          <!-- <p-column field="" header="">
            <ng-template let-row="rowData" pTemplate="body">
              <button class="btn btn-sm btn-primary" (click)="confirm(row)" 
            >confirm
                </button>

            </ng-template>
          </p-column> -->

        </p-dataTable>

      </div>
    </div>
  </div>
</div>

<div class="btn-container">
  <a *ngIf=" renalDonor.kidneyDonorId && renalDonor.kidneyDonorId != null" class="btn btn-sm btn-primary"
    href="http://nehr-app-gw.healthnet.gov.om/nehr/#/login" target="_blank" rel="noreferrer noopener">
    NEHR</a>

  <button class="btn btn-sm btn-primary" (click)="clear()">Clear</button>
  <button class="btn btn-sm btn-primary" (click)="saveDonor()">Save</button>
</div>







<ng-template #linkWithPatient let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Link with Patient</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <div class="form-group">
      <label>Choose Patient</label>
      <ng-select #entryPoint id="ChoosePatient" appendTo="body" [items]="renalPatinetNewList" [virtualScroll]="true"
        placeholder="Select" bindLabel="fullName" bindValue="civilid" (change)="openModalInfo(PatientInfo, $event)">
        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.fullName}}

        </ng-template>
      </ng-select>


    </div>
    <div>
      <!--<p>Patient: <strong>John Doe</strong></p>-->
    </div>
    <form [formGroup]="patientForm">

      <div class="form-group">
        <label>Description</label>
        <textarea class="form-control form-control-sm" rows="2" [(ngModel)]="relationDesc"
          formControlName="relationDesc"></textarea>
      </div>
    </form>

  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-primary" (click)="savePatientInfo()"
      (click)="modal.dismiss('Cross click')">Save</button>
  </div>

</ng-template>


<ng-template #PatientInfo let-modal>
  <div class="modal-header">
    Patient information
  </div>
  <div class="modal-body">
    <div class="col-md-9">
      <div class="row" *ngIf="patientInfoDtlview">

        <label class="col-md-12"><b>Full Name</b> : {{patientInfoDtlview.firstname}} {{patientInfoDtlview.secondname}}
          {{patientInfoDtlview.thirdname}} {{patientInfoDtlview.tribe}}
        </label>
        <label class="col-md-12"><b>Civil Id</b> : {{patientInfoDtlview.civilid}}</label>
        <label class="col-md-12"><b>Patient Id</b> : {{patientInfoDtlview.patientid}}</label>
        <label class="col-md-12"><b>Nationality</b> : {{patientInfoDtlview.nationality}}</label>
        <label class="col-md-12"><b>Gender</b> : {{patientInfoDtlview.sexDesc}}</label>
        <label class="col-md-12"><b>DOB</b> : {{patientInfoDtlview.dob | date: 'dd-MM-yyyy'}}</label>
        <label class="col-md-12"><b>age</b> : {{patientInfoDtlview.age}}</label>

      </div>
    </div>
  </div>


  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-primary" (click)="modal.close(PatientInfo)">Close</button>
  </div>
</ng-template>


<ng-template #donor id="donor" let-c="close" let-d="dismiss" role="dialog">
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Add Donor</h4>
    <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>


  <div class="modal-body">

    <div class="row">

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Civil ID</label>
          <input type="text" class="form-control form-control-sm" formControlName="civilId" disabled>
        </div>
      </div>

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Name</label>
          <input type="text" class="form-control form-control-sm" formControlName="name" disabled>
        </div>
      </div>

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Gender</label>
          <select class="form-control form-control-sm" formControlName="sex" disabled>
            <option value='M'>Male</option>
            <option value='F'>Female</option>
          </select>

        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Nationality</label>
          <select class="form-control form-control-sm" formControlName="nationality" disabled>
            <option selected [value]="null">All Nationality</option>
            <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>
            <!--<option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>-->
          </select>

        </div>
      </div>



      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Date of Birth</label>
          <p-calendar dateFormat="dd-mm-yy" monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true" formControlName="dob">
          </p-calendar>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Blood Group</label>
          <select class="form-control form-control-sm" formControlName="bloodGroup">
            <option value='A +'>A +</option>
            <option value='A -'>A -</option>
            <option value='B -'>B -</option>
            <option value='B +'>B +</option>
            <option value='O +'>O +</option>
            <option value='O -'>O -</option>
            <option value='AB +'>AB +</option>
            <option value='AB -'>AB -</option>
          </select>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Telephone Number</label>
          <input type="text" class="form-control form-control-sm" formControlName="telNo" disabled>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group" [formGroup]="hlaDonor">
          <label>Address</label>
          <textarea class="form-control form-control-sm" rows="1" formControlName="address" disabled></textarea>
        </div>
      </div>
    </div>
    <br>

    <form>
      <div class="row mb-2">


        <div class="col-lg-6 col-md-6 col-sm-6">
          <div class="card mb-2">
            <div class="card-header text-center">Class 1</div>
            <div class="card-body entry-center">
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">A:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="a_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="a_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">B:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="b_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="b_1_Test">
                </div>
              </div>
              <!--
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">BW:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="format16()" [(ngModel)]="BW1" formControlName="bw_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="format17()" [(ngModel)]="BW2" formControlName="bw_1_Test">
                </div>
              </div>-->
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">CW:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="cw_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="cw_1_Test">
                </div>
              </div>
            </div>
          </div>
        </div>




        <div class="col-lg-6 col-md-6 col-sm-6">
          <div class="card mb-2">
            <div class="card-header text-center ">Class 2</div>
            <div class="card-body entry-center custome-height">
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DR:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="dr_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="dr_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DRW:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="drw_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="drw_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DQ:</label>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="dq_Test">
                </div>
                <div class="col-sm-5 col-md-5" [formGroup]="donorTissueForm">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="3" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$" (focusout)="changeFormat($event.target)"
                    formControlName="dq_1_Test">
                </div>
              </div>


            </div>
          </div>



        </div>
      </div>
    </form>



  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-primary" (click)="saveTissueTypeInfo('D')">Save</button>
  </div>
</ng-template>


<ng-template #ScoringInfo let-modal let-c="close" let-d="dismiss" role="dialog" id="ScoringInfo">

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Priority Scoring</h4>
    <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <!-- <form [formGroup]="scoringForm" > -->



    <div>

      <ngb-tabset [destroyOnHide]="false">

        <ngb-tab>
          <ng-template ngbTabTitle>Scoring</ng-template>
          <ng-template ngbTabContent>
            <table class="table table-hover">
              <tbody>
                <tr>
                  <td></td>
                  <th>Donor</th>
                  <th>Patient</th>
                </tr>
                <tr>
                  <td class="talbeHeader">Civil Id:</td>
                  <td>{{renalDonor.civilId}}</td>
                  <td>{{patientview.civilId}}</td>
                </tr>
                <tr>
                  <td class="talbeHeader">Name:</td>
                  <td>{{renalDonor.fullname}}</td>
                  <td> {{patientview.fullName}} </td>
                </tr>
              </tbody>
            </table>
            <table class="table table-hover">
              <tbody>
                <tr>
                  <td class="talbeHeader">PRA :</td>
                  <td>{{scoreData.pra}}</td>

                  <td class="talbeHeader">Age :</td>
                  <td>{{scoreData.ageScore}}</td>
                </tr>
                <tr>
                  <td class="talbeHeader">Period on dialysis :</td>
                  <td>{{scoreData.dialysisPeriod}}</td>

                  <td class="talbeHeader">Previous failed LRD transplant :</td>
                  <td>{{scoreData.prevFailed}}</td>
                </tr>
                <tr>
                  <td class="talbeHeader">HLA match :</td>
                  <td>{{scoreData.hlaMatch}}</td>

                  <td class="talbeHeader">Identical blood group :</td>
                  <td>{{scoreData.bloodGroup}}</td>
                </tr>
                <tr>
                  <td class="talbeHeader">PROXIMITY OF AGE GROUP:</td>
                  <td>{{scoreData.ageProximity}}</td>

                  <td class="talbeHeader">PREVIOUS SOLID ORGAN DONOR :</td>
                  <td>{{scoreData.prevDonor}}</td>
                </tr>
              </tbody>
            </table>
          </ng-template>
        </ngb-tab>

        <ngb-tab>
          <ng-template ngbTabTitle>HLA Tissue
            Screeing</ng-template>
          <ng-template ngbTabContent>

            <table class="table table-hover">
              <tbody>
                <tr>
                  <td></td>
                  <th>Donor</th>
                  <th>Patient</th>

                  <td></td>
                  <th>Donor</th>
                  <th>Patient</th>
                </tr>
                <tr>
                  <td class="talbeHeader">A:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.a_Test ===  HlaByRegNo.a_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.a_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.a_Test ===  HlaByRegNo.a_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.a_Test}}</td>

                  <td class="talbeHeader">A1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.a_1_Test ===  HlaByRegNo.a_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.a_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.a_1_Test ===  HlaByRegNo.a_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.a_1_Test}}</td>
                </tr>

                <tr>
                  <td class="talbeHeader">B:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.b_Test ===  HlaByRegNo.b_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.b_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.b_Test ===  HlaByRegNo.b_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.b_Test}}</td>


                  <td class="talbeHeader">B1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.b_1_Test ===  HlaByRegNo.b_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.b_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.b_1_Test ===  HlaByRegNo.b_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.b_1_Test}}</td>

                </tr>


                <tr>
                  <td class="talbeHeader">BW:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.bw_Test ===  HlaByRegNo.bw_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.bw_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.bw_Test ===  HlaByRegNo.bw_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.bw_Test}}</td>

                  <td class="talbeHeader">BW1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.bw_1_Test ===  HlaByRegNo.bw_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.bw_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.bw_1_Test ===  HlaByRegNo.bw_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.bw_1_Test}}</td>
                </tr>

                <tr>
                  <td class="talbeHeader">CW:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.cw_Test ===  HlaByRegNo.cw_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.cw_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.cw_Test ===  HlaByRegNo.cw_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.cw_Test}}</td>

                  <td class="talbeHeader">CW1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.cw_1_Test ===  HlaByRegNo.cw_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.cw_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.cw_1_Test ===  HlaByRegNo.cw_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.cw_1_Test}}</td>
                </tr>

                <tr>
                  <td class="talbeHeader">DR:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dr_Test ===  HlaByRegNo.dr_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.dr_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dr_Test ===  HlaByRegNo.dr_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.dr_Test}}</td>

                  <td class="talbeHeader">DR1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dr_1_Test ===  HlaByRegNo.dr_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.dr_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dr_1_Test ===  HlaByRegNo.dr_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.dr_1_Test}}</td>
                </tr>


                <tr>
                  <th class="talbeHeader">DRW:</th>
                  <td [ngStyle]="{'color': HlaByDonorId.drw_Test ===  HlaByRegNo.drw_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.drw_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.drw_Test ===  HlaByRegNo.drw_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.drw_Test}}</td>

                  <th class="talbeHeader">DRW1:</th>
                  <td [ngStyle]="{'color': HlaByDonorId.drw_1_Test ===  HlaByRegNo.drw_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.drw_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.drw_1_Test ===  HlaByRegNo.drw_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.drw_1_Test}}</td>
                </tr>

                <tr>
                  <td class="talbeHeader">DQ:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dq_Test ===  HlaByRegNo.dq_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.dq_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dq_Test ===  HlaByRegNo.dq_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.dq_Test}}</td>

                  <td class="talbeHeader">DQ1:</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dq_1_Test ===  HlaByRegNo.dq_1_Test ? 'black' : 'red' }">
                    {{HlaByDonorId.dq_1_Test}}</td>
                  <td [ngStyle]="{'color': HlaByDonorId.dq_1_Test ===  HlaByRegNo.dq_1_Test ? 'black' : 'red' }">
                    {{HlaByRegNo.dq_1_Test}}</td>
                </tr>
              </tbody>
            </table>
          </ng-template>
        </ngb-tab>

      </ngb-tabset>
    </div>


    <!-- </form> -->
  </div>
  <div class="modal-footer">


  </div>

</ng-template>