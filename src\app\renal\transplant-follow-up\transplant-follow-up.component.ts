import { renalTransplantFollowupModel } from '../../common/objectModels/renal-Transplant-Followup-model';
import { Data } from '@angular/router';
import { INDEX } from '../../_helpers/common.constants';
import { renalComplicationModel } from '../../common/objectModels/renalComplication-model';
import { ComlicationMastModel } from '../../common/objectModels/complication-mast-model';
import { InstituteDataModel } from '../../common/objectModels/institute-model';
// import { stringify } from 'querystring';
import { FormArray, Validators, FormControl } from '@angular/forms';
import { FormGroup, FormBuilder } from '@angular/forms';
import { HttpClient, HttpResponse, HttpParams } from '@angular/common/http';
import { Component, OnInit, Input, ViewChild, TemplateRef } from '@angular/core';
import { MasterService } from '../../_services/master.service';
import * as AppUtils from '../../common/app.utils';
import { GridOptions, _ } from "ag-grid-community";
import { TransplantFollowUpService } from './transplant-follow-up.service';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import Swal from 'sweetalert2';
import { analyzeAndValidateNgModules } from '@angular/compiler';
import { DatePipe, formatNumber, formatDate } from '@angular/common';
import * as moment from "moment";
import { IDropdownSettings } from 'ng-multiselect-dropdown';

@Component({
  selector: 'app-transplant-follow-up',
  templateUrl: './transplant-follow-up.component.html',
  styleUrls: ['./transplant-follow-up.component.scss']
})
export class TransplantFollowUpComponent implements OnInit {
  wallayatList: any[];
  instituteName: any[];
  compList: any[];
  compMAstList: ComlicationMastModel[] = [];
  regId: any;
  institutes: InstituteDataModel[] = [];
  followupDto: renalTransplantFollowupModel = new renalTransplantFollowupModel();
  followupData: renalTransplantFollowupModel = new renalTransplantFollowupModel();
  complicationDto: renalComplicationModel = new renalComplicationModel();
  transplantFollowUpForm: FormGroup;
  ComplicationsForm: FormGroup;
  followUpList: renalTransplantFollowupModel[] = [];
  complicationList: renalComplicationModel[] = [];
  complicationListDto: renalComplicationModel[] = [];
  complicationListInput: renalComplicationModel[] = [];
  gridOptions: GridOptions = <GridOptions>{
  }
  private gridApi: any;
  submitted = false;
  rowSelection: renalTransplantFollowupModel;
  selectedrow: any;
  dialysis;


  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  //valueGetter: function(params:any) {return formatDate(params.data[5], 'dd-MM-yyyy', 'en')}},//
  @Input()
  columnDefs = [
    { headerName: 'Follow Up Date', field: '5', resizable: true, valueGetter: function (params: any) { return formatDate(params.data[5], 'dd-MM-yyyy', 'en') } },//cellRenderer: (data) => { return moment(data[5]).format('DD-MM-yyyy') } },
    { headerName: 'Transferred To Institute', field: '6', resizable: true },
    {
      headerName: 'Dialysis Return Date', field: '3', resizable: true, valueGetter: function (params: any) {
        if (params.data[3] == null) {
          return null;
        }
        else {
          return formatDate(params.data[3], 'dd-MM-yyyy', 'en')
        }
      }
    },
    { headerName: 'Dialysis Return Reason', field: '2', resizable: true },
    { headerName: 'Remarks', field: '4', resizable: true },
    {
      headerName: 'Complication', field: '8', resizable: true, valueGetter: function (params: any) {
        if (params.data[7] == "1") {
          var compValue = 'Infection'
        }
        if (params.data[8] == "1") {
          if (compValue != null) {
            var compValue = compValue + ', Malignancy';
          }
          else {
            var compValue = 'Malignancy';
          }
        }
        if (params.data[9] == "1") {
          if (compValue != null) {
            var compValue = compValue + ', Graft Lose';
          }
          else {
            var compValue = 'Graft Lose';
          }
        }
        if (params.data[10] == "1") {
          if (compValue != null) {
            var compValue = compValue + ', Diabetes';
          }
          else {
            var compValue = 'Diabetes';
          }
        }
        if (params.data[11] == "1") {
          if (compValue != null) {
            var compValue = compValue + ', Surgical';
          }
          else {
            var compValue = 'Surgical';
          }
        }
        if (params.data[12] == "1") {
          if (compValue != null) {
            var compValue = compValue + ', Others';
          }
          else {
            var compValue = 'Others';
          }
        }
        if (compValue == null) {
          compValue = '-';
        }
        return compValue;
      }
    }];

  public rowData: any[];
  dropdownSettings: IDropdownSettings;
  patientForm: FormGroup;

  constructor(private fb: FormBuilder, private http: HttpClient, public datepipe: DatePipe, private _transplantFollowUpService: TransplantFollowUpService, private _masterService: MasterService) {

  }
  dateFormatter(params) {
    return formatDate(params.value, 'dd-MM-yyyy', 'en');//moment(params.value).format('dd-MM-yyyy');
  }
  ngOnInit() {
    this.rowData = [];
    this.getInstituteList();
    this.getComplicationList();
    this.patientForm = this.fb.group({
      'patientId': ["", Validators.required],
      'civilId': ["", Validators.required],
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'firstName': ["", Validators.required],
      'secondName': new FormControl(),
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'maritalStatus': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'tel': new FormControl(),
    });
    this.ComplicationsForm = this.fb.group({
      'complicationType': ["", Validators.required],
      'complicationRemarks': new FormControl(),
    });
    this.transplantFollowUpForm = this.fb.group({
      'remarks': ["", Validators.required],
      'transferredTo': new FormControl(),
      'transferredYn': new FormControl(),
      'reason': new FormControl(),
      'returnDialysisYn': new FormControl(),
      'followUpDate': new FormControl(),
      'centralRegNo': new FormControl(),
      'returnToDialysis': new FormControl(),
      'returnDialysisDate': new FormControl(),
      'returnDialysisReason': new FormControl(),
      'complicationType': ["", Validators.required],
      'complicationRemarks': new FormControl(),
    });
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'value',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };

  }

  ngAfterViewInit() {
    this.patientDetails.patientForm.disable();
  }


  get controlValues() { return this.transplantFollowUpForm.controls; }
  get complicationsControlValues() { return this.ComplicationsForm.controls; }
  search() {
    if (this.regId) {
      this.getList(this.regId);
      this.followupDto.centralRegNo = this.regId;
      this.clear();
      this.getRenalTransplantFollowupList();
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
    }
  }

  getList(regNo: any) {
    this.http.get(AppUtils.FIND_RENAL_WAITING_LIST_RESULT, { params: new HttpParams().set("centralRegNo", regNo) }).subscribe(res => {

      if (res['code'] == "S0000") {

        this.patientDetails.setPatientDetails(res['result'][0]);
      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })
  }
  //*************************************************************************************//
  getRenalTransplantFollowupList() {

    this._transplantFollowUpService.getRenalTransplantFollowUp(this.followupDto).subscribe(data => {
      this.followUpList = data;
      if (data.result != null) {
        for (let item of data.result) {
          this.rowData = data.result;
        }
      }
      else {
        this.rowData = null;
        Swal.fire('Error!', data['message'], 'error')
      }
    },
      err => {
        // console.log("An error occurred!!");
      });
  }
  /***************************************Save FollowUp******************************************************** */
  saveFollowUp() {
    this.submitted = true;
    //|| this.transplantFollowUpForm.invalid
    if (this.patientDetails.patientForm.invalid ) {
      return false;
    }
    this.followupDto.transferredTo = this.controlValues.transferredTo.value;
    this.followupDto.fwUpDate = this.controlValues.followUpDate.value;
    this.followupDto.remarks = this.controlValues.remarks.value;
    this.followupDto.returnDialysisDate = this.controlValues.returnDialysisDate.value;
    this.followupDto.returnDialysisReason = this.controlValues.returnDialysisReason.value;
    this.compMAstList.forEach(el => {
      this.followupDto.rgRenalTransptComplication.push({
        complicationType: el.id, complicationRemarks: null, runId: null, fwUpId: null
      });
    },
    );
    if (this.followupDto.transferredTo != null) {
      this.followupDto.transferredYn = "Y";
    }
    this._transplantFollowUpService.addRenalTransplantFollowUp(this.followupDto).subscribe(res => {
      if (res) {
        if (res['code'] === 0) {
          Swal.fire('Saved!', 'TransplantFollowUp Saved successfully.', 'success');
        }  else if (res['code'] === "3") {
          Swal.fire('Saved!', res['message'], 'error');
        }else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }
    }, err => {
      Swal.fire('Error!', 'Error occured while saving TransplantFollowUp ' + err.message, 'error')
    })
  }
  //**********************************************Master Services*************************************************************//
  getInstituteList() {
    this._masterService.institiutes.subscribe(res => {
      this.institutes = res["result"];
    })
  }

  getComplicationList(id: any = 0) {
    this._masterService.getComplicationList(id).subscribe(response => {
      this.compList = response.result;
    }, error => {

    });
  }

  clear() {
    this.rowData = [];
    this.transplantFollowUpForm.reset();
    this.patientDetails.clear();
  }
}



