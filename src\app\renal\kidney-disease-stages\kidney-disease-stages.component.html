
<div class="row">
  <div class="input-group col-sm-10">
    <h4 class="page-title pt-2">Case Details</h4>
  </div>
  <div class="input-group col-sm-2 mb-2 text-right">
    <div class="input-group">
      <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control form-control-sm search-input" (keyup.enter)="search()"/>
      <div class="input-group-append">
        <button  class="btn btn-default btn-sm search-icon" id="search"  (click)="search()">
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
  </div>
</div>



<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>

<form [formGroup]="caseDetailsForm" (ngSubmit)="submit()">
    <div class="row">

        <div class="col-lg-4 col-md-4 col-sm-6" formGroupName="rgTbCkdStageTransplant">

            <div class="form-group">
                <div class="card-header"><strong>Chronic Kidney Disease</strong></div>
                <input type="hidden" class="form-check-input" formControlName="runId">
                <div class="card-body">
                    <div class="box">

                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="ckdFailureStage" value='2'
                                    [(ngModel)]="stage" formControlName="ckdFailureStage">Stage 2
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="ckdFailureStage" value='3'
                                    [(ngModel)]="stage" formControlName="ckdFailureStage">Stage 3
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="ckdFailureStage" value='4'
                                    [(ngModel)]="stage" formControlName="ckdFailureStage">Stage 4
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="ckdFailureStage" value='5'
                                    [(ngModel)]="stage" formControlName="ckdFailureStage">Stage 5
                            </label>
                        </div>


                    </div>
                </div>
            </div>

        </div>


        <div class="col-lg-2 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
            <div class="form-group">

                <div class="card-header"><strong>Transplant Readiness</strong></div>
                <div class="card-body">
                    <div class="mt-1">
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantReadiness" [(ngModel)]="readiness"
                                    formControlName="transplantReadiness" value="Y">Yes
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantReadiness" [(ngModel)]="readiness"
                                    formControlName="transplantReadiness" value="N">No
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
            <div class="form-group">

                <div class="card-header"><strong>Transplant Willingness</strong></div>
                <div class="card-body">
                    <div class="mt-1">
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantWillingness" [(ngModel)]="willingness"
                                    formControlName="transplantWillingness" value="Y">Yes
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantWillingness" [(ngModel)]="willingness"
                                    formControlName="transplantWillingness" value="N">No
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
            <div class="form-group">

                <div class="card-header"><strong>Dialysis</strong></div>
                <div class="card-body">
                    <div class="mt-1">
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="dialysisYn" [(ngModel)]="dialysis"
                                    formControlName="dialysisYn" value="Y">Yes
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="dialysisYn" [(ngModel)]="dialysis"
                                    formControlName="dialysisYn" value="N">No
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        

   

        <div class="col-lg-2 col-md-2 col-sm-4" formGroupName="rgTbCkdStageTransplant">
            <div class="form-group">
                <div class="card-header"><strong>Transplant</strong></div>
                <div class="card-body">
                    <div class="mt-1">
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantYn"
                                    [(ngModel)]="transplant" formControlName="transplantYn" value="Y">Yes
                            </label>
                        </div>
                        <div class="form-check-inline">
                            <label class="form-check-label">
                                <input type="radio" class="form-check-input" name="transplantYn"
                                    [(ngModel)]="transplant" formControlName="transplantYn" value="N">No
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12" *ngIf="dialysis == 'Y'">
            <div class="card mt-4 theme-card" formGroupName="rgTbDialysisInfo">
                <div class="card-header"><strong>Dialysis Details</strong></div>
                <div class="card-body">
                    <input type="hidden" class="form-check-input" formControlName="runId">
                    <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Date of starting dialysis</label>
                                <!-- <p-calendar formControlName="startDate"> </p-calendar> -->

                                <!-- <p-calendar type="number" class="form-control  form-control-sm" dateFormat="dd/mm/yy"
                                    formControlName="startDate" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                                </p-calendar> -->


                                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="startDate"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>

                            </div>
                            <div class="form-group" *ngIf="dialysisType != 'Peritoneal'">
                                <label>Dialysis Centre Name</label>
                                <select class="form-control form-control-sm" formControlName="dialysisInst">
                                    <option disabled selected [value]="null">Select</option>
                                    <option [value]="res.id" *ngFor="let res of dialysisCenters">{{res.value}}
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Dialysis Type</label>
                                <div class="box min-ht">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="radio" class="form-check-input" id="Hemodialysis"
                                                name="dialysisType" [(ngModel)]="dialysisType" value="H"
                                                formControlName="dialysisType">Hemodialysis
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="radio" class="form-check-input" id="Peritoneal"
                                                name="dialysisType" [(ngModel)]="dialysisType" value="P"
                                                formControlName="dialysisType">Peritoneal Dialysis
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Type of Dialysis Access</label>
                                <div class="box">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" class="form-check-input"  name="diyAccessAvf"
                                                formControlName="diyAccessAvf" value="F">Arteriovenous Fistula (AVF)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" class="form-check-input" name="diyAccessAvg"
                                                formControlName="diyAccessAvg" value="G">Arteriovenous Graft (AVG)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" class="form-check-input" name="diyAccessPcath"
                                                formControlName="diyAccessPcath" value="P">P. CATH
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" class="form-check-input" name="diyAccessPdcath"
                                                formControlName="diyAccessPdcath" value="D">PD. CATH
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div>
                </div>
            </div>


        </div>

        <div class="col-lg-12 col-md-12 col-sm-12" *ngIf="transplant == 'Y'">
            <div class="card mt-4 theme-card">
                <div class="card-header"><strong>Transplantation</strong></div>
                <div class="card-body">
                    <div class="row" formGroupName="rgTbCkdStageTransplant">
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Pre-emptive transplant</label>
                                <div class="mt-1">
                                    <div class="form-check-inline">
                                        <label class="form-check-label">
                                            <input type="radio" class="form-check-input" name="preEmptiveYn"
                                                [(ngModel)]="preEmptiveTransplant" formControlName="preEmptiveYn"
                                                value="Y">Yes
                                        </label>
                                    </div>
                                    <div class="form-check-inline">
                                        <label class="form-check-label">
                                            <input type="radio" class="form-check-input" name="preEmptiveYn"
                                                [(ngModel)]="preEmptiveTransplant" formControlName="preEmptiveYn"
                                                value="N">No
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Date of Transplant</label>
                                <!-- <p-calendar [(ngModel)]="value" formControlName="transplantDatr"></p-calendar> -->

                                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="transplantDatr"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
<!-- 
                                [(ngModel)]="selectedTransplantPlace"
                                    (ngModelChange)="placeofTransplant()" -->
                                <label>Place of Transplantation</label>
                                <select class="form-control form-control-sm"  formControlName="transplantInst">
                                    <option disabled selected [value]="null">Select transplant Hospitals</option>
                                    <option [value]="res.estCode" *ngFor="let res of hospitals">{{res.estName}}
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Transplant Country</label>
                                <select class="form-control form-control-sm" formControlName="transplantCountry">
                                    <option selected [value]="null">All Nation</option>
                                    <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.natName}}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Follow Up Hospitals</label>
                                <select class="form-control form-control-sm" formControlName="followupInst">
                                    <option disabled selected [value]="null">Select followup hospitals </option>
                                    <option [value]="res.estCode" *ngFor="let res of hospitals">{{res.estName}}

                                    <!-- <option *ngFor="let item of hospitals">{{ item }}</option> -->
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Follow Up Country</label>
                                <select class="form-control form-control-sm" formControlName="followUpCountry">
                                    <option selected [value]="null">All Nation</option>
                                    <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.natName}}
                                    </option>
                                </select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <br>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12">
        <h6>Transfusion Details</h6>
        <div class="content-wrapper mb-2">
            <div class="row">

                <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="rgTbBldTransDtls">
                    <div class="form-group">
                        <label> Previous Blood Transfusion on </label>
                        <input  type="hidden" class="form-control form-control-sm" formControlName="runId">
                        <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="transfusedDate"
                        [ngModelOptions]="{standalone: true}" monthNavigator="true"
                        [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                        showButtonBar="true"></p-calendar>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="rgTbBldTransDtls">
                    <div class="form-group">
                        <label>Institutes : </label>
                        <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                            bindLabel="estName" bindValue="estCode" formControlName="transfusedInst">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12">
            <div [formGroup]="labTestForm">
                <h6>Lab Results</h6>
                <div class="content-wrapper mb-2 lab-results">
                    <div class="text-right pb-2">
                        <button (click)="addNewlab()" class="btn btn-sm btn-primary">Add New</button>
                        <button class="btn btn-sm btn-primary" (click)="openModal(downloadLabTest)">Download</button>
                    </div>

                    <p-dataTable [immutable]="false" [value]="labnewList" [editable]="true" dataKey="runId"
                        [responsive]="true">


                        <p-column field="testDate" header="Date">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">

                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="runId">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="enteredBy">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="enteredDate">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="source">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="releasedDate">

                                        <div *ngIf="!row.isEditable">{{row.testDate | date:'dd-MM-yyyy'}}</div>
                                        <div *ngIf="row.isEditable">
                                            <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="testDate"
                                                [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                                                showButtonBar="true"></p-calendar>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>

                        <p-column field="profileTestCode" header="Profile Name">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.profileTestName}}</div>
                                        <div *ngIf="row.isEditable">
                                            <ng-select #entryPoint [items]="profileList" [virtualScroll]="true"
                                                placeholder="Select" bindLabel="testName" bindValue="testId"
                                                formControlName="profileTestCode">
                                                <ng-template ng-option-tmp let-item="item" let-index="index">{{
                                                    item.testName }}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>


                        <p-column field="mohTestCode" header="Test Component">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.componentTestName}}</div>
                                        <div *ngIf="row.isEditable">
                                            <ng-select #entryPoint [items]="ComponetList" [virtualScroll]="true"
                                                placeholder="Select" bindLabel="componentTestName"
                                                bindValue="componentTestId" formControlName="mohTestCode">
                                                <ng-template ng-option-tmp let-item="item" let-index="index">{{
                                                    item.componentTestName }}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>


                        <!-- <p-column field="resultSummary" header="Result">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    
                              <ng-container formArrayName="rgTbLabTestInfo">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">{{row.resultSummary}}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm" formControlName="resultSummary">
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column> -->
                        <p-column field="value" header="Result">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.value}}</div>
                                        <div *ngIf="row.isEditable">
                                            <input type="text" class="form-control form-control-sm"
                                                formControlName="value">
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>

                        <p-column field="unit" header="Unit">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.unit}}</div>
                                        <div *ngIf="row.isEditable">
                                            <input type="text" class="form-control form-control-sm"
                                                formControlName="unit">
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>


                        <p-column field="instCode" header="Institute">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                                <ng-container formArrayName="rgTbLabTestInfo">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.instName}} </div>
                                        <div *ngIf="row.isEditable">
                                            <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                                placeholder="Select" bindLabel="estName" bindValue="estCode"
                                                formControlName="instCode">
                                                <ng-template ng-option-tmp let-item="item" let-index="index">{{
                                                    item.estName }}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>

                        <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <button (click)="onRowEditInit(row)"
                                    *ngIf="row.source == 'W' && row.isEditable == false"
                                    class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>

                                <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                                    class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>

                            </ng-template>
                        </p-column>

                        <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <button (click)="deletelab(row)" class="btn btn-sm btn-primary"><i
                                        class="fa fa-trash"></i></button>
                            </ng-template>
                        </p-column>



                        <!--
                             paginator="false" rows="10" paginatorTemplate="{CurrentPageReport} {FirstPageLink}
                 {PreviousPageLink} {PageLinks} {NextPageLink}
                 {LastPageLink} {RowsPerPageDropdown}"
                 [multiSortMeta]="multiSortMeta"
                 groupField="profile_test"
                   sortField="profile_test"
                 rowGroupMode="rowspan"
                 rowGroupMode="subheader"
                -->
                        <!-- <ng-template pTemplate="rowgroupheader" let-rowData>Test :{{rowData.profile_NAME   }} , Date : {{rowData.order_dt | date:'dd-MM-yyyy'}} </ng-template>  -->
                        <!-- <p-column field="order_dt" header="Date">
                            <ng-template let-row="rowData" pTemplate="body">
                                <ng-container formArrayName="rgTbLabTestInfo">

                                    <div *ngIf="!row.isEditable">{{row.order_dt | date:'dd-MM-yyyy' }}</div>
                                    <div *ngIf="row.isEditable">
                                        <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true"
                                            [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                                            showButtonBar="true">
                                        </p-calendar>
                                    </div>

                                </ng-container>
                            </ng-template>
                        </p-column> -->
                        <!--  -->


                        <!-- <p-column field="test_bacteria" header="Test Component">
                            <ng-template let-row="rowData" pTemplate="body">
                                <ng-container formArrayName="rgTbLabTestInfo">

                                <div *ngIf="!row.isEditable">{{row.description}}</div>
                                <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="testcomponet" [virtualScroll]="true"
                                        placeholder="Select" bindLabel="testName" bindValue="componentTestId">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.testName }}
                                        </ng-template>
                                    </ng-select>
                                </div>

                            </ng-container>
                            </ng-template>
                        </p-column> -->




                        <!-- <p-column field="est_code" header="Institute">
                            <ng-template let-row="rowData" pTemplate="body">
                                <ng-container formArrayName="rgTbLabTestInfo">
                                <div *ngIf="!row.isEditable">{{row.estList}}</div>
                                <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                        placeholder="Select" bindLabel="estName" bindValue="estCode">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </ng-container>
                            </ng-template>
                        </p-column> -->

                        <!-- <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <ng-container formArrayName="rgTbLabTestInfo">
                                <button class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>
                            </ng-container>
                            </ng-template>
                        </p-column> -->

                        <!-- <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <ng-container formArrayName="rgTbLabTestInfo">
                                <button (click)="deletelab(row)" class="btn btn-sm btn-primary"><i
                                        class="fa fa-trash"></i></button>
                                    </ng-container>
                            </ng-template>


                        </p-column> -->
                    </p-dataTable>

                </div>
            </div>
            <br>
            <div>
                <!-- <h6>Vaccination</h6> -->

                <div class="content-wrapper mb-2">
                    <app-vaccination [submitted]="submitted" [currentCivilId]="currentCivilId" #Vaccination>
                    </app-vaccination>
                </div>
                <!--
                <div class="content-wrapper mb-2 lab-results">
                    <div class="text-right pb-2">
                        <button (click)="addNewVacc()" class="btn btn-sm btn-primary">Add New</button>
                       <button (click)="callFetchVaccDataFromAlShifa()" class="btn btn-sm btn-primary">Download</button>
                    </div>
                  
                    <p-dataTable [immutable]="false" [value]="vaccNewList" [editable]="true" dataKey="runId" >
                
                
                        <p-column  field="vaccinationDate" header="Vaccination Date"   >
                            <ng-template let-row="rowData" pTemplate="body" >
                                <div *ngIf="!row.isEditable">{{row.givenDate | date:'dd-MM-yyyy'}}</div>
                                <div *ngIf="row.isEditable">
                                    <p-calendar   type="number" dateFormat="dd-mm-yy" monthNavigator="true"
                                        [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                                    </p-calendar>
                
                
                                </div>
                            </ng-template>
                        </p-column>
                        <p-column field="vaccineCode" header="Vaccination Name"  >
                            <ng-template let-row="rowData" pTemplate="body">
                                <div *ngIf="!row.isEditable">{{vaccName}}
                                </div>
                                <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="vaccinMastList"
                                        [virtualScroll]="true" placeholder="Select Vaccination" bindLabel="vaccineName"
                                        bindValue="vaccineId">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.vaccineName }}
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </ng-template>
                        </p-column>
                        <p-column field="vaccinatedInst" header="institute"   >
                            <ng-template let-row="rowData" pTemplate="body">
                                <div *ngIf="!row.isEditable">{{row.estList}} </div>
                                <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="institutes"  [virtualScroll]="true"
                                        placeholder="Select institutes" bindLabel="estName" bindValue="estCode">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </ng-template>
                        </p-column>
                        <p-column field="remarks" header="Remarks"   >
                            <ng-template let-row="rowData" pTemplate="body">
                                <div *ngIf="!row.isEditable">{{row.remarks}}</div>
                                <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm">
                                </div>
                            </ng-template>
                        </p-column>
                    <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}"  styleClass="foo" >
                      <ng-template let-row="rowData" pTemplate="body">
                        <button  class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>
                      </ng-template>
                    </p-column>
           
                     <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}"  styleClass="foo">
                      <ng-template let-row="rowData" pTemplate="body">
                        <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
                      </ng-template>
                         
                
                    </p-column>
                   
                    </p-dataTable>
                </div>
            -->
            </div>
        </div>



    </div>
    <div class="btn-container left">
        <div class="col-lg-12 col-md-12 col-sm-12">
            <hr>
            <button type="submit" class="btn btn-primary" (click)="saveSatages()">Save</button>
            <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
            <button class="btn btn-primary ripple" (click)="navigateToDashboard()">Cancel</button>
            <button class="btn btn-primary ripple" (click)="navigateToRegister()">Back to register page</button>
            <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callTissue()">Tissue Screening</button>

        </div>
    </div>

    <ng-template #downloadLabTest let-modal>

        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Download Lab Test</h4>
            <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" style="height: 250px;">

            <form [formGroup]="filterModelForm">
                <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-3">
                        <label>From Date</label>
                        <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
                            [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                            showButtonBar="true" formControlName="fromDate">
                        </p-calendar>
                    </div>


                    <div class="col-lg-3 col-md-3 col-sm-3">
                        <label>To Date</label>
                        <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
                            [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                            showButtonBar="true" formControlName="toDate">
                        </p-calendar>
                    </div>

                </div>
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <br>
                        <label>Test Name</label>
                        <div>
                            <ng-multiselect-dropdown
                                class=" multiappend custom-dropdown custom_color custom_box_color check_box_custom-color"
                                [placeholder]="'Add Test'" [data]="testListToDownload" [settings]="dropdownSettings"
                                formControlName="profileT">
                            </ng-multiselect-dropdown>
                        </div>

                    </div>
                </div>
            </form>





        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-sm btn-primary" (click)="callFetchLabDataFromAlShifa()"
                (click)="modal.dismiss('Cross click')">Download</button>
        </div>

    </ng-template>



</form>