
 .navbar-nav {
    .nav-link {        
        cursor: pointer; 
        color: #333;      
        i{
            margin-right: 5px;
            color: var(--primary-color);
        }
    }   
}
.dropdown-menu{
    font-size: var(--font-size-base);
    .dropdown-item{
        cursor: pointer;
        &:active, &:focus{
            background: var(--primary-light);
        }
    }
    &.messages {
        min-width: 330px;
    }
}
.main{
    top:50px;
    position: relative; 
}
header{
    .navbar{
      background: var(--white-color);  
      height: var(--header-height);
      padding-left: 0;
      box-shadow: 0 0 5px rgba(0,0,0,0.2);
    }
}
.sidebar{
    height:calc(100vh - var(--header-height));
    background: var(--primary-color);
    a{
        color: var(--white-color);      
    }
}

#sidebar-wrapper {
    z-index: 99;
    position: fixed;
    left: 220px;
    top: 50px;
    width: 220px;
    height: 100%;
    margin-left: -220px;
    overflow-y: auto;
    overflow-x: hidden;
    background: var(--primary-color);
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    box-shadow: 0 0 2px 2px rgba(0,0,0,0.04);
}
#wrapper.toggled-2 #sidebar-wrapper:hover, #wrapper.toggled #sidebar-wrapper  {
    width: 220px;
}

#page-content-wrapper {
    position: absolute;
    width: 100%;
    // overflow-x: hidden;
    padding:10px;
}
#wrapper.toggled #page-content-wrapper {
    position: relative;
    margin-right: 0px;
}
.fixed-brand {
    width: 220px;
}
/* Sidebar Styles */

.sidebar-nav {
    /*position: absolute;
    top: 120px;*/
    width: 220px;
    margin: 0;
    padding: 0;
    list-style: none;
    margin-top: 2px;
    li {
        text-indent: 10px;
        line-height: 40px;

        &.active{
            background:rgba(0,0,0,0.1);
            .sub-link{
                &:before{                   
                    border-bottom: 5px solid #fff;
                    border-top:0 !important;
                }
            }
        }
        
        a {
            display: block;
            text-decoration: none;
            color: #fff;
            font-size: 13px;
            border-radius: 0;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;

            &.sub-link{
                &:before{
                    position: absolute;
                    content:'';
                    right: 17px;
                    top: 15px;
                    width: 0px;
                    height: 0px;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #fff;
                }
            }

            span{
                padding-left: 36px;
            }
            i {
                font-size: 15px;
                position: absolute;
                left: 0;
                top: 10px;
            }
            &:hover {
                text-decoration: none;
                background: rgba(0,0,0,0.1);
            }
            &:active, &:focus{
                text-decoration: none;
            }
        }

        ul{
            // background: rgba(0,0,0,0.2);
            display: none;
            li{
                a {
                    // padding-left: 35px;
                    i{
                        color: #ffb5b3;
                    }
                }
            }
        }
        &.active{
            ul{
                display: block;
            }
        }
    }
}

.sub-icons{
   width: 20px;
   height: 20px;
   background: #ccc;
   top: 5px;
   right: 0;
   left: auto !important;
   text-indent: 0;

    img{
        background-size: 100%;  
    }
}


.sidebar-brand {
    height: 65px;
    font-size: 18px;
    line-height: 60px;
    a {
        color: #999999;
        &:hover {
            color: #fff;
            background: none;
        }
    }
}

.menu-search {
    span {
        color: #fff;
    }
    input {
        background: none;
        border: 0;
        line-height: 12px;
        color: #fff;
        padding: 5px;
        width: 160px;
    }
}

 /* chrome ustom scroll */
#sidebar-wrapper::-webkit-scrollbar {
 width: 5px;
}
#sidebar-wrapper::-webkit-scrollbar-track {
 background: #ddd;
}
#sidebar-wrapper::-webkit-scrollbar-thumb {
 background: #666;
}
/* =============== #header =================*/

.navbar-header {
    transition: all 0.5s ease;
}
/* .navbar-header.tgl {
    width: 50px;
} */
%hamb-lines{
    position: absolute;
    left:0;
    width: 20px;
    height: 1px;
    background: #ccc;
    content: '';
}
.hamb-menu {
    position: relative;
    width: 20px;
    height: 20px;
    display: block;

    .hamb-icon {
        width: 20px;
        height: 1px;
        background: #ccc;
        position: relative;
        top: 8px;
        display: block;        
    
        &::before {   
            @extend %hamb-lines;  
            top: -5px;           
        }
        &::after {       
            @extend %hamb-lines;      
            bottom: -5px;
        }
    }
}


.navbar-default {
    .navbar-toggle {
        border: 0;
        font-size: 20px;
        margin: 0;
        padding: 15px 20px;
        border-radius: 0;
        background: #fff;
        outline: none;
    }
}

.navbar-brand {
    text-align: center;
    width: 100%;
    background: var(--primary-color);
    color: #fff !important;
    white-space: nowrap;
    padding: 15px 7px;
    font-size: 0.875rem;
    transition: all 0.3s ease 0s;

        &:hover, &:focus{
            background: var(--primary-color) !important;
        }
   
}



@media (max-width: 768px){
    .navbar-toggle {
        position: fixed;
        top: 0;
        left:0;
    }
    .navbar-brand{
        display: none;
    }
    #wrapper.toggled-2 #sidebar-wrapper {
        width: 0;
    }  
    #page-content-wrapper{
        top: 50px;
    }
}
@media (min-width: 768px){
    #page-content-wrapper {
        position: relative;
        transition: all 0.5s ease;
    }    
    #wrapper {
        padding-left: 220px;
        padding-top: 50px;
    }
    #wrapper.toggled-2 #sidebar-wrapper {
        width: 50px;
    }  
    .toggled-2{
        padding-left: 50px !important;
    }
    .fixed-brand.tgl {
        width: 50px;
    }
    .navbar-header {
        float: left;
    }
    .navbar-toggle {
        display: none;
    }
    .navbar-nav {
        float: left;
        margin: 0;
    }
    
}
.collapse.in{
    display: block;
}
.main-menu{
    position: relative;
    &::before{
        content: "";
        position: absolute;
        top: 17px;
        right: 3px;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 5px 5px 0 5px;
        border-color: #ff9c9c transparent transparent transparent;
    }
}

// loader styles added 
.loader-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loader {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #007bff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}