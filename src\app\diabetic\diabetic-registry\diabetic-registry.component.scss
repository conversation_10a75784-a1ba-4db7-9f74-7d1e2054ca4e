.label-block label {

  display: block;
}


.top-space {
  margin-top: 30px;
}

.sp label {

  padding-right: 18px;
  display: inline-block;
  position: relative;
}


.chkbox-outer {

  input[type=radio],
  input[type=checkbox] {
    margin-left: 0 !important;
    position: relative;
    margin-right: 10px;


  }

}

.follow-up-list {
  display: flex;
  flex-direction: column-reverse !important;
  border: 1px solid #e3e3e3;
  border-bottom: 0;

  a {
    cursor: pointer;
    padding: 8px;
    border-bottom: 1px solid #e3e3e3;
    border-left: 3px solid transparent;
    text-decoration: none;
    color: #444;

    &:hover {
      background: #f7f7f7;
      color: #d9534f;
      font-family: var(--font-bold);
    }

    &.active {
      background: #d9534f1c !important;
      color: #d9534f !important;
      border-left-color: #d9534f;
      font-family: var(--font-bold);
    }
  }
}

.resize {
  padding-left: 0 !important;

}


#moh {
  padding-right: 27px;
}


#mof {
  padding-right: 35px;
}

#mop {
  padding-right: 37px;
}

// min-width:1145


.mcard {
  border: 1px solid #efefef;
  border-radius: 3px;
  margin: 10px 0;

  .mcard-header {
    font-size: 14px;
    padding: 5px 10px;
    background: #f7f7f7;
    font-weight: bold;
    color: #8f8f8f;
  }

  .mcard-body {
    padding: 10px;

    table {
      color: #666;
      margin-bottom: 0;

    }

  }
}


@media (max-width:1300px) {
  .resize {
    display: block !important;
  }

  .chkbox-outer {
    margin-left: 20px;
    display: block !important;
  }
}

.add-new {
  width: 100%;
  margin-bottom: 10px;
}

.pr-0 {
  padding-right: 0;
}

:host ::ng-deep {
  .enc-register {
    .accordion {

      .card {

        &:nth-child(4),
        &:nth-child(5) {
          display: inline-block;
          width: 49%;
          margin: 0 0.5%;
          vertical-align: top;
          margin-bottom: 10px;
        }

        .card-body {
          min-height: 170px;
        }
      }
    }
  }

}

.btn-ftr-container {
  text-align: right;
  padding: 10px;
}

.inline-list {
  padding: 0 10px;
  display: inline-block;

  li {
    display: inline-block;
    padding: 0 5px;

    label {
      margin: 0;
    }

    input {
      margin-right: 5px;
    }

    &:last-child {
      border: 0;
    }

  }
}

.divider-right {
  border-right: 1px solid #e5e5e5;
}

.mcard {
  border: 1px solid #efefef;
  border-radius: 3px;
  margin-bottom: 10px;

  .mcard-header {
    font-size: 14px;
    padding: 5px 10px;
    background: #f7f7f7;
    font-weight: bold;
    color: #8f8f8f;
  }

  .mcard-body {
    padding: 10px;

    table {
      color: #666;
      margin-bottom: 0;

    }

  }
}

.selected {
  background-color: #d3d3d3;
  /* Change to your desired color */
  color: #000;
  /* Change text color if necessary */
}

.border-box {
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 3px;
  padding: 10px;
}

.followup-nav {
  .list-group-item {
    padding: 5px;
    cursor: pointer;

    &.active {
      background: #fffbfb;
      border-color: #d9534f;
      font-weight: bold;
      color: #d9534f;
    }

    &:hover {
      background: #f7f7f7;
    }
  }
}

.radio-inline {
  display: inline-block;
  padding: 0 10px 0 0;

  input,
  label {
    vertical-align: middle;
    margin: 0;
  }
}

.c-label {
  padding-right: 10px;
  display: inline-block;
  text-align: right;
}

.label-space {
  margin-top: 30px;
}

.sub-title {
  margin-bottom: 15px;
  background: #f3f3f3;
  padding: 3px 6px;
  font-size: 15px;
  border-radius: 3px;
}

.checkbox input {
  margin-right: 5px;
}

.top-search {
  padding-right: 6px;
  margin-bottom: 8px;

  .col-form-label {
    white-space: nowrap;
    padding-right: 0;
    line-height: 10px;
  }

  .form-control {
    height: 25px;
  }
}

.add-btn {
  width: 100%;
  margin-bottom: 10px;
  border: 1px dashed #d9534f;
  color: #d9534f;
  transition: .3s linear;

  &:hover {
    background: #d9534f;
    color: #fff;
    border-color: transparent;

  }
}

.add-btn-link {
  float: right;
  padding: 0;
  color: #d9534f;
  line-height: 1;
  font-weight: bold;
}

.custom-btn {
  padding: 4px 10px !important
}

.hide-txt {
  .ui-inputtext {
    display: none;
  }
}

.added-list {
  position: relative;

  button {
    position: absolute;
    right: 5px;
    border: 0;
    background: none;
    color: #d9534f;
  }
}

.action-btn {
  border: 0;
  background: #d9534f;
  color: white;
  font-size: 14px;
  margin-top: 5px;
  padding: 7px;
  margin-right: 8px;
  border-radius: 3px;
}

.label1 {
  padding-left: 30px;
}

.label2 {
  padding-left: 32px;
}

.label3 {
  padding-left: 7px;
}

.text {
  margin-left: -50px;
}