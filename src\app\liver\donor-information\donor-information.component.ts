import {
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
  FormArray
} from "@angular/forms";
import {
  Component,
  OnInit,
  Input,
  ViewChild,
  TemplateRef,
  ChangeDetectorRef,
} from "@angular/core";
import {
  NgbModal,
  NgbModalOptions,
} from "@ng-bootstrap/ng-bootstrap";
import { GenderType } from "../../_models/gender-type";
import { Nationality } from "../../_models/nationality.model";
import { MasterService } from "../../_services/master.service";
import { LiverService } from "../../liver/liver.service";
import { SharedService } from "../../_services/shared.service";
import { PatientInfo } from "../../_models/patient.model";
import { Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { RenalDonorPatient } from "../../_models/renal_donor_patient.model";
import { RenalDonor } from "../../_models/renal-donor.model";
import { MpiModel } from "../../common/objectModels/mpi-model";
import { LoginService } from "../../login/login.service";
import { DatePipe, formatDate } from "@angular/common";
import { ModalConfig } from "../../config/modal-config";
import { AlShifaLoginService } from "../../alshifa/alShifaLogin.service";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";
import * as $ from "jquery";
import "jqueryui";
import { surgeryComponent } from "../../_comments/surgery/surgery.component";
import { LabResultsComponent } from "../../_comments/lab-result-listing/lab-result-listing.component";
import * as AppUtils from "../../common/app.utils";
import * as CommonConstants from "../../_helpers/common.constants";
import { TissueTypeModel } from "../../common/objectModels/tissueTypeModel";
import * as moment from "moment";
import * as AppCompUtils from "../../common/app.component-utils";
import { RgProcedureDetailsDto, RgProcedureDetailsSaveDto, RgVwLiverDonorProceduresDto } from "src/app/_models/liverTransplant.model";
import { NotificationService } from "../../_services/notification.service";

@Component({
  selector: "app-donor-information",
  templateUrl: "./donor-information.component.html",
  styleUrls: ["./donor-information.component.scss"],
  providers: [LiverService],
})
export class LiverDonorInformationComponent implements OnInit {
  proceduresPerPage: number = 3;
  currentProcedurePage: number = 1;
  paginatedProcedureTypes: string[] = [];

  @ViewChild("ScoringInfo", { static: false })
  public ScoringInfo: TemplateRef<any>;
  @ViewChild("surgery", { static: false }) surgery: surgeryComponent;
  @ViewChild("LabResults", { static: false }) LabResults: LabResultsComponent;

  @Input() patientForm: FormGroup;
  donorForm: FormGroup;
  hlaDonor: FormGroup;
  donorTissueForm: FormGroup;
  scoringForm: FormGroup;
  @Input() complicationForm: FormGroup;

  public genderTypeOptions = AppCompUtils.GENDER;
  public maritalStatusOptions = AppCompUtils.Marital_Status_Type;
  public bloodGroupOptions = AppCompUtils.BLOOD_GROUP;
  public nationOption = [];

  nationList: any;
  nationListFilter: any;
  relationList: any;
  relationListFilter: any;
  institutes: any[];
  patientInfoView: PatientInfo[];
  renalPatinetNewList: any = [];
  patientsDeceasedDonorList: any = [];
  patientInfoDtl: PatientInfo[];
  complicationList: any[];
  complicationMastList: any;
  procedures: RgVwLiverDonorProceduresDto[] = [];
  procedureDetails: RgProcedureDetailsSaveDto[] = [];
  uniqueProcedureTypes: string[] = [];
  selectedProcedures: number[] = [];
  relatedDonorDegreeList: any[] = AppCompUtils.RELATED_DONOR_DEGREE_LIST;
  sexList: Array<GenderType> = [];
  bloodRelationList: any;
  nonBloodRelationList: any;
  hospitalsList: any;

  renalDonorPatient: RenalDonorPatient;
  renalDonor: RenalDonor = new RenalDonor();
  hlaTissueType: TissueTypeModel;
  MpiModelInfo: MpiModel;

  @Input() submitted = false;
  showButton: boolean = false;
  showSelectPatient: boolean = false;
  civilIdInvalid = false;
  disabledCondition: boolean = true;

  civilId: any;
  centralRegNo: number;
  relationDesc: any;
  relationType: any;
  selectedDonateHospital: any;
  selectNationality: any;
  donorType: any;
  value: Date;
  selectPtient: any;
  Ropdata: any;
  code: any;
  loginId: any;
  estCode: any;
  patientId: any;
  runId: any;

  today = new Date();
  thisYear = new Date().getFullYear();
  startDate = new Date("1/1/" + this.thisYear);
  defaultFormattedDate = this.datePipe.transform(this.startDate, "dd-MM-yyyy");
  currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");

  modalOptions: NgbModalOptions = ModalConfig;
  selectedDate: any;
  delRow: any;
  selectedNote: any;
  patientview: any = null;
  selectedPatient: any;

  
  HlaByDonorId: any;
  HlaByRegNo: any;
  savingTissue: any;
  scoreData: any;

  public rgTbGenComplication: any = [];
  compFg: any = [];

  private genderTypes = GenderType;
  private nationalityList = Nationality;
  patientDetails: PatientDetailsComponent;
  newPatientView: any = [];
  patientInfoDtlview: any;
  deceasedDonor: any;
  livinRelatedDonor: any;
  livinUnrelatedDonor: any;
  geneticTypeId = 1;
  testID = 10070;
  newpatientInfoDtlview: any;
  genbloodlist: any;
  complicationsFrequencyList: any;
  donorLabInfoalshifa: any;
  donorSurgerInfoalshifa: any;
  donorVaccineInfoalshifa: any;
  donorProcedureInfoalshifa: any;

  constructor(
    private modalService: NgbModal,
    private liverService: LiverService,
    private _alShifaLoginService: AlShifaLoginService,
    private formBuilder: FormBuilder,
    private _masterService: MasterService,
    private _sharedService: SharedService,
    public shared: SharedService,
    private router: Router,
    private http: HttpClient,
    private LoginService: LoginService,
    private datePipe: DatePipe,
    private changeDetectorRef: ChangeDetectorRef,
    private notificationService: NotificationService
  ) {
    this.getNationalityList();
    this.getRelationTypeMast();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaPathentId: any;
        let alShifaEstCode: any;
        let alShifaRegNo: any;
        let alShifaValCode: any;
        let alShifaCivilId: any;

        this._alShifaLoginService.getAlShifanData().forEach((element) => {
          if (element["regNo"]) {
            alShifaRegNo = element["regNo"];
          } else if (element["patientId"]) {
            alShifaPathentId = element["patientId"];
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          } else if (element["civilId"]) {
            alShifaCivilId = element["civilId"];
          }
        });

        if (alShifaCivilId) {
          this.getDonorDetails(alShifaCivilId, "civilId");
        }
      }

      if (this.shared.getNavigationData()) {
        let civilId = this.shared.getNavigationData().civilId;
        this.getDonorDetails(civilId, "civilId");
        this._sharedService.setNavigationData(null);
      }
    }, 1000);
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.renalDonorPatient = new RenalDonorPatient();
    this.relatedDonorDegreeList = AppCompUtils.RELATED_DONOR_DEGREE_LIST;
    
    this.donorForm.valueChanges.subscribe(val => {
      this.renalDonor = { ...this.renalDonor, ...val };
    });
    
    this.populateMasterData();
  }  populateMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getBloodRelation().subscribe((response) => {
      this.bloodRelationList = response["result"];
    });

    this._masterService.getHospitals().subscribe((response) => {
      this.hospitalsList = response["result"];
    });

    this._masterService.getNonBloodRelation().subscribe((response) => {
      this.nonBloodRelationList = response["result"];
    });

    this.loadProcedures();

    this.complicationForm = this.formBuilder.group({
      rgTbGenComplication: this.formBuilder.array([]),
    });

    this._masterService.getLiverDonorComplicationMast().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.complicationMastList = response.result;
        } else {
          this.complicationMastList = [];
        }
      },
      error: (error) => {
        this.complicationMastList = [];
      },
    });

    this.liverService.getAllLiverDonorPatient().subscribe((response) => {
      this.patientInfoView = response["result"];

      this.patientInfoView.forEach((element) => {
        let nameparts = [];
        if (element["firstName"]) nameparts.push(element["firstName"]);
        if (element["secondName"]) nameparts.push(element["secondName"]);
        if (element["thirdName"]) nameparts.push(element["thirdName"]);

        let fullName =
          "CivilID:  " +
          element.civilid +
          " , " +
          nameparts.join(" ") +
          (element.tribe ? " " + element.tribe : "") +
          ", Age:  " +
          element.age;
        element["fullName"] = fullName;
        this.renalPatinetNewList.push(element);
      });
    });
  }

  loadProcedures() {
    this.liverService.getLiverDonorProcedure().subscribe(
      (response) => {
        this.procedures = response["result"];
        this.uniqueProcedureTypes = [
          ...new Set(this.procedures.map((p) => p.procedureName)),
        ];
        this.updateProcedurePagination();
      }
    );
  }

  onProcedureTypeSelect(name: string) {
    const procedure = this.procedures.find((p) => p.procedureName === name);
    if (procedure) {
      const existingIndex = this.procedureDetails.findIndex(
        (pd) => pd.procId === procedure.procId
      );

      if (existingIndex === -1) {
        this.procedureDetails.push({
          runId: null,
          procId: procedure.procId,
          doneDate: null,
          remarks: "",
        });
      } else {
        this.procedureDetails.splice(existingIndex, 1);
      }
    }

    this.updateProcedurePagination();
  }

  updateProcedureDetails(procId: number, field: string, value: any) {
    const detail = this.procedureDetails.find((pd) => pd.procId === procId);
    if (detail) {
      detail[field] = value;
    }
  }

  isProcedureSelected(type: string): boolean {
    return this.procedureDetails.some(
      (pd) =>
        this.procedures.find((p) => p.procId === pd.procId).procedureName ===
        type
    );
  }

  getProcedureType(procId: number): string {
    const procedure = this.procedures.find((p) => p.procId === procId);
    return procedure ? procedure.procedureName : "";
  }

  getProcedureDetail(procedureType: string): RgProcedureDetailsDto | undefined {
    return this.procedureDetails.find(
      (detail) => this.getProcedureType(detail.procId) === procedureType
    );
  }

  saveProcedureDetails(): RgProcedureDetailsDto[] {
    return this.uniqueProcedureTypes
      .filter((name) => this.isProcedureSelected(name))
      .map((name) => {
        const detail = this.getProcedureDetail(name);
        if (!detail) return null;

        let doneDate = null;
        if (detail.doneDate) {
          try {
            if (detail.doneDate instanceof Date) {
              doneDate = detail.doneDate;
            } else {
              doneDate = new Date(detail.doneDate);
            }
          } catch (e) {
            console.error("Error parsing date:", e);
          }
        }

        return {
          procId: detail.procId,
          doneDate: doneDate,
          remarks: detail.remarks || "",
        };
      })
      .filter((detail) => detail !== null);
  }

  initializeFormGroup(): void {
    this.donorForm = new FormGroup({
      civilId: new FormControl(null),
      fullname: new FormControl({ value: '', disabled: true }, Validators.required),
      sex: new FormControl({ value: '', disabled: true }, Validators.required),
      dob: new FormControl({ value: '', disabled: true }, Validators.required),
      nationality: new FormControl({ value: '', disabled: true }, Validators.required),
      bloodGroup: new FormControl(null),
      telNo: new FormControl(null),
      address: new FormControl({ value: null, disabled: true }, Validators.required),
      donorType: new FormControl(null),
      instCode: new FormControl(null),
      instCodeReadonly: new FormControl({ value: null, disabled: true }),
      instPatientId: new FormControl(null),
      instPatientIdReadonly: new FormControl({ value: null, disabled: true }),
      relationDesc: new FormControl(null),
      relationType: new FormControl(null),
      exDate: new FormControl(null),
      relationDegree: new FormControl(null),
      martialStatus: new FormControl(null),
      occupation: new FormControl(null),
      height: new FormControl(null),
      weight: new FormControl(null),
      kinName: new FormControl(null),
      kinPhone: new FormControl(null),
      regType: new FormControl(null)
    });

    const instPatientIdControl = this.donorForm.get('instPatientId');
    const instPatientIdReadonly = this.donorForm.get('instPatientIdReadonly');

    if (instPatientIdControl && instPatientIdReadonly) {
      instPatientIdControl.valueChanges.subscribe(value => {
        instPatientIdReadonly.setValue(value, { emitEvent: false });
      });
    }

    const instCodeControl = this.donorForm.get('instCode');
    const instCodeReadonly = this.donorForm.get('instCodeReadonly');
    if (instCodeControl && instCodeReadonly) {
      instCodeControl.valueChanges.subscribe(value => {
        instCodeReadonly.setValue(value, { emitEvent: false });
      });
    }

    this.patientForm = new FormGroup({
      relationType: new FormControl(null),
      relationDesc: new FormControl(null),
    });

    this.hlaDonor = new FormGroup({
      donorId: new FormControl((this.renalDonor && this.renalDonor.kidneyDonorId) || null),
      civilId: new FormControl(null),
      name: new FormControl(null),
      sex: new FormControl(null),
      nationality: new FormControl(null),
      dob: new FormControl(null),
      bloodGroup: new FormControl(null),
      telNo: new FormControl(null),
      address: new FormControl(null),
    });

    this.donorTissueForm = new FormGroup({
      runId: new FormControl(null),
      donorId: new FormControl(null),
      a_Test: new FormControl(null),
      a_1_Test: new FormControl(null),
      b_Test: new FormControl(null),
      b_1_Test: new FormControl(null),
      cw_Test: new FormControl(null),
      cw_1_Test: new FormControl(null),
      dr_Test: new FormControl(null),
      dr_1_Test: new FormControl(null),
      drw_Test: new FormControl(null),
      drw_1_Test: new FormControl(null),
      dq_Test: new FormControl(null),
      dq_1_Test: new FormControl(null),
      bw_Test: new FormControl(null),
      bw_1_Test: new FormControl(null),
    });

    this.scoringForm = new FormGroup({
      pra: new FormControl(null),
      ageScore: new FormControl(null),
      dialysisPeriod: new FormControl(null),
      prevFailed: new FormControl(null),
      hlaMatch: new FormControl(null),
      bloodGroup: new FormControl(null),
      ageProximity: new FormControl(null),
      prevDonor: new FormControl(null),
      runId: new FormControl(null),
      activeYn: new FormControl(null),
      donorID: new FormControl(null),
      centralRegNo: new FormControl(null),
    });
  }

  openModal(linkWithPatient) {
    this.modalService.open(linkWithPatient);
  }

  openModalInfo(PatientInfo, event) {
    this.modalService.open(PatientInfo);
    this.getData(event.civilid);
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        this.nationListFilter = this.nationList;
      },
      (error) => { }
    );
  }

  getRelationTypeMast(relationCode: any = 0) {
    this._masterService.getRelationTypeMast(relationCode).subscribe(
      (response) => {
        this.relationList = response.result;
        this.relationListFilter = this.relationList;
      },
      (error) => { }
    );
  }

  getData(civilid) {
    this.patientInfoDtlview = this.renalPatinetNewList.filter(
      (s) => s.civilid == civilid
    )[0];
    this.centralRegNo = this.patientInfoDtlview.centralRegNo;
  }

  savePatientInfo() {
    if (!this.patientForm.get('relationType') || !this.patientForm.get('relationType').value) {
      this.notificationService.showWarning('Warning!', 'Please select a patient first.');
      return;
    }

    const selectedRelationType = this.patientForm.get('relationType').value;

    this.liverService.getPatientsDonorbyRelationType(selectedRelationType, this.renalDonor.kidneyDonorId).subscribe(
      (checkResponse) => {
        if (checkResponse === true || (checkResponse && checkResponse["result"] === true)) {
          this.notificationService.showWarning('Warning!', 'Relation type is already selected for this donor.');
          return;
        }

        this.renalDonorPatient.renalDonorId = this.renalDonor.kidneyDonorId;
        this.renalDonorPatient.relationType = selectedRelationType;
        this.renalDonorPatient.relationDesc = this.patientForm.get('relationDesc').value;

        this.liverService.saveLiverDonorPatient(this.renalDonorPatient).subscribe(
          (res) => {
            if (res["code"] == 0) {
              this.notificationService.showSuccess("Saved!", "Patient Saved successfully.");
              this.patientForm.reset();
              this.relationType = null;
              this.relationDesc = null;
              this.selectedPatient = null;
              this.modalService.dismissAll();
            } else if (res["code"] == "3") {
              this.notificationService.showError("Saved!", res["message"]);
            } else {
              this.notificationService.showError("Error!", res["message"]);
            }
          },
          (err) => {
            this.notificationService.showError(
              "Error!",
              "Error occured while saving Patient " + err.message
            );
          }
        );
      },
      (error) => {
        this.notificationService.showError(
          "Error!",
          "Error occurred while checking existing relation: " + error.message
        );
      }
    );
  }
  patchModelFromForm(): void {
    const formValues = this.donorForm.getRawValue();
    this.renalDonor = { ...this.renalDonor, ...formValues };
    this.liverService.instPatientId = formValues.instPatientId;
  }

  saveDonor() {
    this.patchModelFromForm();

    let rgTbDonorSurgeryDtls = this.surgery.surgeryForm.value.rgTbSurgeryDtls;
    let labTestsData = this.LabResults.labResultForm.value.rgTbLabTests;
    let complicationsaveData = this.complicationForm.value.rgTbGenComplication;

    const invalidComplications = (complicationsaveData || []).filter(c => c && !c.paramId);
    if (invalidComplications.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all complication records. Complication type is required for all complication entries.");
      return;
    }
    complicationsaveData = (complicationsaveData || []).filter(c => c && c.paramId);

    const invalidSurgeries = (rgTbDonorSurgeryDtls || []).filter(s => s && (!s.surgeryID || !s.surgeryDt));
    if (invalidSurgeries.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all surgery records. Surgery Type and Surgery Date are required for all surgery entries.");
      return;
    }
    rgTbDonorSurgeryDtls = (rgTbDonorSurgeryDtls || []).filter(s => s && s.surgeryID && s.surgeryDt);

    const invalidLabTests = (labTestsData || []).filter(l => l && (!l.mohTestCode || !l.instCode));
    if (invalidLabTests.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all lab test records. Test Code and Institute Code are required for all lab test entries.");
      return;
    }
    labTestsData = (labTestsData || []).filter(l => l && l.mohTestCode && l.instCode);

    let saveData = {
      kidneyDonorId: this.renalDonor.kidneyDonorId,
      rgTbRenalDonorDto: this.renalDonor,
      rgTbDonorSurgeryDtls: rgTbDonorSurgeryDtls,
      rgTbDonorProcedureDtls: this.saveProcedureDetails(),
      rgTbDonorLabTests: labTestsData,
      rgTbDonorComplication: complicationsaveData,
    };

    if (this.renalDonor.civilId == null) {
      this.submitted == false;
      this.notificationService.showWarning("Warning", "Civil Id Can not be empty");
    }

    const raw = this.donorForm.getRawValue();
    if (!raw.fullname) {
      this.submitted = false;
      this.notificationService.showWarning(
        'Warning',
        'Name cannot be empty. Please fetch donor details using Civil ID and Expiry Date.'
      );
      return;
    }

    if (this.renalDonor.donorType === "R") {
      const missing: string[] = [];
      if (this.isMissing(this.renalDonor.relationType)) {
        missing.push('Relation Type');
      }
      if (this.isMissing(this.renalDonor.relationDegree)) {
        missing.push('Relation Degree');
      }
      if (missing.length) {
        this.submitted = false;
        this.notificationService.showWarning(
          "Warning",
          `${missing.join(' and ')} ${missing.length > 1 ? 'are' : 'is'} mandatory for Living Related Donor`
        );
        return;
      }
    }

    if (this.renalDonor.donorType === "U") {
      const missingU: string[] = [];
      if (this.isMissing(this.renalDonor.relationType)) {
        missingU.push('Relation Type');
      }
      if (this.isMissing(this.renalDonor.relationDesc)) {
        missingU.push('Please Specify');
      }
      if (missingU.length) {
        this.submitted = false;
        this.notificationService.showWarning(
          "Warning",
          `${missingU.join(' and ')} ${missingU.length > 1 ? 'are' : 'is'} mandatory for Living Unrelated Donor`
        );
        return;
      }
    }

    if (
      this.renalDonor.donorType === "D" &&
      (this.renalDonor.instPatientId === null || this.renalDonor.instPatientId === undefined)
    ) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Institute Patient ID cannot be empty for Deceased Donor");
      return;
    }

    if (!this.renalDonor.instCode) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Institutes can not be empty");
      return;
    }

    if (!this.renalDonor.instPatientId) {
      console.log(this.liverService.instPatientId);
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Patient Id can not be empty");
      return;
    } else {
      this.liverService.saveLiverDonorRegistry(saveData).subscribe(
        (res) => {
          this.submitted = true;
          if (res["code"] == 0) {
            this.notificationService.showSuccess(
              "Saved!",
              "Donor has been saved successfully."
            );
            this.clear();
            this.getDonorDetails(res["result"], "civilId");
          } else if (res["code"] == "3") {
            this.notificationService.showError("Saved!", res["message"]);
          } else {
            this.notificationService.showError("Error!", res["message"]);
          }
        },
        (err) => {
          this.notificationService.showError(
            "Error!",
            "Error occured while saving Donor " + err.message
          );
        }
      );
    }
  }

  private isMissing(val: any): boolean {
    return val === null || val === undefined || (typeof val === 'string' && val.trim() === '');
  }

  clear() {
    this.showButton = false;
    this.donorForm.reset();
    this.renalDonor[Object.keys(this.renalDonor)[0]] = "";
    this.donorForm.controls["civilId"].enable();
    this.donorForm.controls["exDate"].enable();
    this.showSelectPatient = false;
    this.patientsDeceasedDonorList = null;
    this.surgery.clear();
    this.LabResults.clear();
    this.surgery.surgList = [];
    this.LabResults.rgTbLabTests = [];
    this.compFg = [];
    this.rgTbGenComplication = [];
    this.complicationForm.reset();
    this.complicationForm = this.formBuilder.group({
      rgTbGenComplication: this.formBuilder.array([]),
    });

    this.procedureDetails = [];
    this.selectedProcedures = [];
    this.updateProcedurePagination();
  }

  getDonorDetails(id, type) {
    if (id != null) {
      const loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
      if (loggedInUser) {
        const inst = loggedInUser.institutes.filter((item) => item.defaultYN === "Y");
        if (inst && inst.length > 0) {
          this.estCode = inst[0].estCode;
        }
      }

      this.liverService
        .getDonorDetails(id, type)
        .subscribe(async (response) => {
          if (response != null) {
            if (response["code"] == "S0000") {
              this.showButton = true;
              const result = response["result"] || {};
              const donorDto =
                result.rgTbLiverDonorDto ||
                result.rgTbRenalDonorDto ||
                result.rgTbDonorDto ||
                null;

              if (!donorDto) {
                this.notificationService.showWarning(
                  "Warning!",
                  "Donor details are missing in the response."
                );
                return;
              }

              this.renalDonor = donorDto;
              this.hlaTissueType = result.rgHlaTissueType || result.hlaTissueType || null;
              const civilCtrl = this.donorForm.get("civilId");
              this.civilId = donorDto.civilId || (civilCtrl ? civilCtrl.value : null) || id;

              this.hlaDonor.patchValue({
                donorId: this.renalDonor.kidneyDonorId || null
              });

              if (this.hlaTissueType) {
                this.hlaTissueType = this.normalizeHlaModel(this.hlaTissueType);
                this.donorTissueForm.patchValue(this.hlaTissueType);
              }

              this.patchFormFromModel();
              this.donorForm.controls["civilId"].disable();
              this.donorForm.controls["exDate"].disable();

              if (result.rgTbDonorComplication) {
                const rgTbGenComplication: any = result.rgTbDonorComplication;
                this.compFg = [];
                this.complicationForm.setControl(
                  "rgTbGenComplication",
                  this.formBuilder.array([])
                );
                for (let compList of rgTbGenComplication) {
                  this.addNewCompl(
                    compList.runid,
                    compList.paramId,
                    compList.remarks,
                  );
                }
              }

              if (result.rgTbDonorLabTests) {
                const rgTbLabTestsDB: any = result.rgTbDonorLabTests;
                for (let labRList of rgTbLabTestsDB) {
                  this.LabResults.addNewLabResult(
                    labRList.runId,
                    this._sharedService.setDateFormat(labRList.testDate),
                    labRList.mohTestCode,
                    labRList.resultSummary,
                    labRList.instCode,
                    labRList.enteredBy,
                    labRList.enteredDate,
                    labRList.source,
                    false
                  );
                }
              }

              if (result.rgTbDonorSurgeryDtls) {
                const rgTbSurgeryDtlsDB: any = result.rgTbDonorSurgeryDtls;
                for (let surList of rgTbSurgeryDtlsDB) {
                  this.surgery.addNewSurgery(
                    surList.runId,
                    surList.surgeryID,
                    this._sharedService.setDateFormat(surList.surgeryDt),
                    surList.remarks,
                    surList.enteredBy,
                    surList.enteredDt,
                    "W",
                    false
                  );
                }
              }

              if (result.rgTbDonorProcedureDtls) {
                this.procedureDetails = result.rgTbDonorProcedureDtls.map((proc) => ({
                  runId: proc.runId,
                  procId: proc.procId,
                  doneDate: proc.doneDate ? new Date(proc.doneDate) : null,
                  remarks: proc.remarks || "",
                }));
                this.selectedProcedures = this.procedureDetails.map(
                  (proc) => proc.procId
                );
              }
            } else if (response["code"] == "C0001") {
              this.notificationService.showWarning('Warning!', response["message"]);
            } else if (response["code"] == "C0002") {
              const exDate = this.donorForm.value.exDate;
              const civil = this.donorForm.value.civilId;
              if (exDate == "" || exDate == null || civil == "" || civil == null) {
                this.notificationService.showWarning('Warning', 'The entered Civil ID is not in the registry file, please enter Civil ID & Expiry Date to get the data from MPI');
              } else {
                this.getPatientDetails(id);
              }
            } else {
              this.notificationService.showError('Error!', 'Error occurred while fetching donor information details: ' + response["message"]);
            }
          } else {
            this.renalDonor = new RenalDonor();
            this.notificationService.showError('', response["message"]);
          }
        });
    }
  }

  private normalizeHlaModel<T extends { [key: string]: any }>(model: T): T {
    if (!model) return model;
    const copy: any = { ...model };
    AppCompUtils.HLA_FIELD_KEYS.forEach((key) => {
      copy[key] = this.padLeft(copy[key], '0', 2);
    });
    return copy as T;
  }

  private normalizeAndPatchHlaForm(): void {
    if (!this.donorTissueForm) return;
    const normalized = this.normalizeHlaModel(this.donorTissueForm.value || {});
    this.donorTissueForm.patchValue(normalized);
  }

  patchFormFromModel(): void {
    if (!this.renalDonor || !this.donorForm) {
      return;
    }
    const patch: any = {};
    Object.keys(this.donorForm.controls).forEach((key) => {
      if ((this.renalDonor as any).hasOwnProperty(key)) {
        const val = (this.renalDonor as any)[key];
        patch[key] = key === 'dob' && val ? new Date(val) : val;
      }
    });
    this.donorForm.patchValue(patch);
  }

  getPatientDetails(civilId) {
    if (civilId != null) {
      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      const loginId = curUser["person"].perscode;

      if (curUser) {
        var inst = curUser.institutes.filter((item) => item.defaultYN === "Y");
      }
      this.estCode = inst[0].estCode;

      let exDate = this.donorForm.value.exDate;
      if (exDate == "") {
        exDate = null;
      } else if (exDate != null) {
        exDate = formatDate(exDate, "yyyy-MM-dd", "en");
      }
      let req = {
        birthDate: null,
        cardExpiryDate: exDate,
        civilId: this.donorForm.value.civilId,
        requesterPersCode: loginId,
      };
      this._masterService.getMpiV2Details(req).subscribe((response) => {
        if (response != null) {
          if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            let mpires = [];
            mpires = response["result"];
            this.donorForm.patchValue({
              civilId: mpires["civilId"],
              fullname:
                mpires["firstNameEn"] +
                " " +
                mpires["secondNameEn"] +
                " " +
                mpires["thirdNameEn"] +
                " " +
                mpires["sixthNameEn"],
              nationality: mpires["countryID"],
              telNo: mpires["mobileNo"],
              sex: mpires["sex"] === "Male" ? "M" : "F",
              address: mpires["birthTown"],
              dob: new Date(mpires["birthDate"]),
            });
            this.donorForm.controls["civilId"].disable();
            this.donorForm.controls["exDate"].disable();
            this.civilId = mpires["civilId"];
          } else if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            this.notificationService.showError(
              "Error!",
              "Error occurred while fetching civil id details: " + response["result"]["message"]
            );
          } else {
            this.notificationService.showError(
              "Error!",
              "Error occurred while fetching civil id details: " + response["message"]
            );
          }
        }
      });
    }
  }

  onDownloadLabResults() {
    if (this.civilId) {
      this.fetchAllDonorLabFromAlShifa();
    } else {
      this.notificationService.showWarning(
        "Warning!",
        "Please enter civilId before fetching data."
      );
    }
  }

  onDownloadSurgery() {
    if (this.civilId) {
      this.fetchAllDonorSurgeryFromAlShifa();
    } else {
      this.notificationService.showWarning(
        "Warning!",
        "Please enter civilId before fetching data."
      );
    }
  }

  fetchAllDonorLabFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this.liverService
        .fetchAllDonorLabFromShifa(this.civilId, this.donorForm.get('instCode').value)
        .subscribe(
          (res) => {
            if (res["code"] === "S0000") {
              const labResults = res["result"];
              if (labResults && labResults.length > 0) {
                for (let lab of labResults) {
                  this.LabResults.addNewLabResult(
                    lab.runId,
                    this._sharedService.setDateFormat(lab.testDate),
                    lab.mohTestCode,
                    lab.resultSummary,
                    lab.instCode,
                    lab.enteredBy,
                    lab.enteredDate,
                    lab.source,
                    false
                  );
                }
              } else {
                this.notificationService.showInfo(
                  "No Lab Results",
                  "No lab results were found for the given Civil ID and Establishment Code."
                );
              }
            } else if (res["code"] === "F0000") {
              this.notificationService.showInfo(
                "No Records Found",
                res["message"] || "No data available for the provided Civil ID."
              );
            } else {
              this.notificationService.showInfo(
                "No Lab Results",
                "No lab results were found for the given Civil ID and Establishment Code."
              );
            }
          },
          (error) => {
            this.notificationService.showError(
              "Server Error",
              "Unable to retrieve lab data due to a server error. Please try again later."
            );
          }
        );
    } else {
      this.notificationService.showWarning(
        "Missing Information",
        "Please ensure both Civil ID and Establishment Code are entered before fetching lab results."
      );
    }
  }

  fetchAllDonorSurgeryFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this.liverService
        .fetchAllDonorSurgeryFromShifa(this.civilId, this.donorForm.get('instCode').value)
        .subscribe((res) => {
          if (res["code"] === "S0000") {
            const surgeryData = res["result"];

            if (Array.isArray(surgeryData) && surgeryData.length > 0) {
              for (let sur of surgeryData) {
                this.surgery.addNewSurgery(
                  sur.runId,
                  sur.surgeryId,
                  this._sharedService.setDateFormat(sur.surgeryDate),
                  sur.surgeryRemarks,
                  sur.enteredBy,
                  sur.enteredDt,
                  "W",
                  false
                );
              }
            } else {
              this.notificationService.showInfo(
                "No Surgery Records",
                "No donor surgery records found for the provided Civil ID."
              );
            }
          } else if (res["code"] === "F0000") {
            this.notificationService.showInfo(
              "No Records Found",
              res["message"] || "No surgery details are available for this Civil ID."
            );
          } else {
            this.notificationService.showInfo(
              "No Surgery Records",
              "No surgery records were found for the given Civil ID and Establishment Code."
            );
          }
        },
          (error) => {
            this.notificationService.showError(
              "Server Error",
              "An error occurred while fetching surgery data. Please try again later."
            );
          }
        );
    } else {
      this.notificationService.showWarning(
        "Missing Information",
        "Please enter a Civil ID before attempting to fetch donor surgery data."
      );
    }
  }

  fetchAllDonorProcedureFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this.liverService
        .fetchAllDonorProcedureFromShifa(this.donorForm.get('instCode').value, this.civilId)
        .subscribe({
          next: (res) => {
            if (res["code"] === "S0000") {
              const procedureData = res["result"];

              if (Array.isArray(procedureData) && procedureData.length > 0) {
                this.procedureDetails = procedureData.map((proc) => ({
                  runId: null,
                  procId: proc.procedureId,
                  doneDate: proc.reportDate,
                  remarks: proc.report || "",
                }));

                this.selectedProcedures = this.procedureDetails.map(
                  (proc) => proc.procId
                );
              } else {
                this.notificationService.showInfo(
                  "No Procedures Found",
                  "No donor procedure records found for the provided Civil ID and Establishment Code."
                );
              }
            } else if (res["code"] === "F0000") {
              this.notificationService.showInfo(
                "No Records Found",
                res["message"] || "No procedure information is available for this donor."
              );
            } else {
              this.notificationService.showInfo(
                "No Procedures Found",
                "No donor procedure records found for the provided Civil ID and Establishment Code."
              );
            }
          },
          error: (error) => {
            this.notificationService.showError(
              "Server Error",
              "An error occurred while fetching procedure data. Please try again later."
            );
          },
        });
    } else {
      this.notificationService.showWarning(
        "Missing Information",
        "Please enter both Civil ID and Institute Code before fetching donor procedure data."
      );
    }
  }

  getCompareHlaScores(regNo, donorId) {
    this.liverService
      .getCompareHlaScore(regNo, donorId)
      .subscribe((Response) => {
        this.HlaByDonorId = Response["result"].donorHla;
        this.HlaByRegNo = Response["result"].patientHla;
        if (this.HlaByRegNo != null) {
          this.showscoreModal(this.ScoringInfo);
        }
      });
  }

  public onClickKey($event: any) {
    if ($event.keyCode == 13) {
      this.civilId = $event.target.value;
      this.getDonorDetails(this.civilId, "civilId");
    } else {
      this.civilIdInvalid = false;
    }
  }

  public onExpiryDateSelect(event: any) {
    const civilId = this.donorForm.get("civilId").value;
    this.getDonorDetails(civilId, "civilId");
  }

  get f() {
    return this.donorForm.controls;
  }

  getSexCode(sexDesc: any) {
    let sex: GenderType[];
    sex = this.sexList.filter((item) => item.valueOf === sexDesc);
    return sex[0].valueOf;
  }

  donorSelectModal(donor) {
    const currentDonorId = (this.renalDonor && this.renalDonor.kidneyDonorId) || null;
    const formValues = this.donorForm.getRawValue();

    this.hlaDonor.enable();

    const patchObject = {
      donorId: currentDonorId,
      civilId: formValues.civilId || null,
      name: formValues.fullname || null,
      sex: formValues.sex || "",
      nationality: formValues.nationality || null,
      dob: formValues.dob || null,
      bloodGroup: formValues.bloodGroup || null,
      telNo: formValues.telNo || null,
      address: formValues.address || null,
    };

    this.hlaDonor.patchValue(patchObject);

    const nationalityCtrl = this.hlaDonor.get('nationality');
    const bloodGroupCtrl = this.hlaDonor.get('bloodGroup');
    if (nationalityCtrl) {
      nationalityCtrl.disable({ emitEvent: false });
    }
    if (bloodGroupCtrl) {
      bloodGroupCtrl.disable({ emitEvent: false });
    }

    this.normalizeAndPatchHlaForm();

    this.changeDetectorRef.detectChanges();
    this.modalService.open(donor, this.modalOptions);
  }

  onDonorTypeChange(selectedValue: any) {
    this.selectedPatient = null;
    this.showSelectPatient = false;
    this.patientsDeceasedDonorList = [];
    this.patientview = null;

    if (selectedValue === "D") {
      const instCode = this.donorForm.get('instCode') ? this.donorForm.get('instCode').value : null;
      const instPatientId = this.donorForm.get('instPatientId') ? this.donorForm.get('instPatientId').value : null;

      if (!instCode || !instPatientId) {
        this.notificationService.showWarning(
          "Warning",
          "Please fill Institute Name and Institute Patient ID before selecting Deceased Donor type"
        );

        const donorTypeControl = this.donorForm.get('donorType');
        if (donorTypeControl) {
          donorTypeControl.setValue('');
        }
        this.renalDonor.donorType = '';
        return;
      }
    }

    const relationTypeCtrl = this.donorForm.get('relationType');
    const relationDegreeCtrl = this.donorForm.get('relationDegree');
    const relationDescCtrl = this.donorForm.get('relationDesc');
    if (relationTypeCtrl && relationDegreeCtrl && relationDescCtrl) {
      relationTypeCtrl.clearValidators();
      relationDegreeCtrl.clearValidators();
      relationDescCtrl.clearValidators();

      relationTypeCtrl.setValue(null);
      relationDescCtrl.setValue(null);

      if (selectedValue === 'R') {
        relationDegreeCtrl.setValue(null);
        relationTypeCtrl.setValidators([Validators.required]);
        relationDegreeCtrl.setValidators([Validators.required]);
      } else if (selectedValue === 'U') {
        relationDegreeCtrl.setValue(null);
        relationTypeCtrl.setValidators([Validators.required]);
        relationDescCtrl.setValidators([Validators.required]);
      } else {
        relationDegreeCtrl.setValue(null);
      }

      relationTypeCtrl.updateValueAndValidity({ emitEvent: false });
      relationDegreeCtrl.updateValueAndValidity({ emitEvent: false });
      relationDescCtrl.updateValueAndValidity({ emitEvent: false });
    }

    this.renalDonor.relationType = null;
    this.renalDonor.relationDesc = null;
    this.renalDonor.relationDegree = selectedValue === 'R' ? null : null;

    this.renalDonor.donorType = selectedValue;
    this.changeDetectorRef.detectChanges();
  }

  confirm() {
    if (!this.patientview || this.patientview == null) {
      this.notificationService.showWarning("Warning!", "Please select patients");
    } else {
      this.notificationService.showConfirmationDialog(
        "Confirm Mapping",
        "This donor will be mapped to patient " + this.patientview.fullName,
        "confirm"
      ).then((confirmed) => {
        if (confirmed) {
          this.liverService
            .updateLiverDonorPatient(
              this.renalDonor.kidneyDonorId,
              this.patientview.civilID,
              this.patientview.centralRegNo
            )
            .subscribe((res) => {
              if (res["code"] == "S0000") {
                this.notificationService.showSuccess("Success", res["result"]);
              } else {
                this.notificationService.showError("Error!", res["message"]);
              }
            });
        }
      });
    }
  }
  showscoreModal(info) {
    this.modalService.open(info, this.modalOptions);

    $(document).ready(function () {
      let modalContent: any = $(".modal-content");
      let modalHeader = $(".modal-header");
      modalHeader.addClass("cursor-all-scroll");
      modalContent.draggable({
        handle: ".modal-header",
      });
    });
  }
  onRowSelect(event) {
    this.selectedPatient = event.data;
    this.patientview = event.data;

    if (this.patientview.centralRegNo != null) {
      this.getCompareHlaScores(
        this.patientview.centralRegNo,
        this.renalDonor.kidneyDonorId
      );
    }
  }

  onRowUnselect(event) {
    this.selectedPatient = null;
    this.patientview = null;
  }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  changeFormat($event) {
    if ($event.value != null) {
      $event.value = this.padLeft($event.value, "0", 2);
    }
  }

  padLeft(text: any, padChar: string, size: number): string {
    if (!text) {
      return null;
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  selectPatient() {
    this.showSelectPatient = true;

    this.liverService
      .getPatientsLiverDeceasedDonor(this.renalDonor.kidneyDonorId)
      .subscribe((res) => {
        if (res["code"] == "S0000") {
          this.patientsDeceasedDonorList = res["result"];
        } else if (res["code"] == "F0000") {
          this.notificationService.showInfo("Info!", "No donor record found for mapping.");
        } else {
          this.notificationService.showError("Error!", res["message"]);
        }
      });
  }

  saveTissueTypeInfo(type: any) {
    this.donorTissueForm.value["donorId"] = this.renalDonor["kidneyDonorId"];
    if (this.donorTissueForm.value["runId"] == null) {
      this.donorTissueForm.value["runId"] = this.runId != null ? this.runId : null;
    }
    let donadBody = this.donorTissueForm.value;
    this.savingTissue = [donadBody];

    this.liverService.saveTissueTypeList(this.savingTissue).subscribe(
      (response) => {
        this.modalService.dismissAll();
        if (response["code"] == "0") {
          this.notificationService.showSuccess("Saved!", "Patient HLA Saved successfully.");
          this.runId = response["result"];
          this.donorTissueForm.patchValue({ runId: this.runId });
        } else {
          this.notificationService.showError("Error!", response["message"]);
        }
      },
      (err) => {
        this.notificationService.showError(
          "Error!",
          "Error occured while saving Patient HLA " + err.message
        );
      }
    );
  }

  onProcedurePageChange(page: number) {
    this.currentProcedurePage = page;
    this.updateProcedurePagination();
  }

  onAddNewComp() {
    this.addNewCompl("", "", "", this.loginId, this.currentDate, false, "", "");
    this.compFg[this.compFg.length - 1].isEditable = true;
  }

  addNewCompl(
    runid: any = null,
    paramId: any = null,
    remarks: any = null,
    enteredBy: any = null,
    enteredDt: any = null,
    isEditable: any = false,
    frequency: any = null,
    onsetDt: any = null
  ): void {
    this.rgTbGenComplication = this.complicationForm.get(
      "rgTbGenComplication"
    ) as FormArray;

    this.compFg = Object.assign([], this.rgTbGenComplication.value);
    const familyHistItem: any = this.createCOPMtem(
      runid,
      paramId,
      remarks,
      enteredBy,
      enteredDt,
      isEditable,
      frequency,
      onsetDt
    );
    this.rgTbGenComplication.push(this.createCompGrpItem(familyHistItem));

    this.compFg.push(familyHistItem);
  }

  createCompGrpItem(familyHistItem: any): FormGroup {
    return this.formBuilder.group(familyHistItem);
  }

  createCOPMtem(
    runid: any = null,
    paramId: any = null,
    remarks: any = null,
    enteredBy: any = null,
    enteredDt: any = null,
    isEditable: any = false,
    frequency: any = null,
    onsetDt: any = null
  ) {
    return {
      runid: runid,
      paramId: paramId,
      remarks: remarks,
      enteredBy: enteredBy,
      enteredDt: enteredDt,
      isEditable: isEditable,
      frequency: frequency,
      onsetDt: onsetDt,
    };
  }

  trackByFn(index: number, item: any): any {
    return item.civilID || index;
  }

  getComplicationName(paramId) {
    if (
      paramId &&
      this.complicationMastList &&
      this.complicationMastList.length > 0
    ) {
      const complication = this.complicationMastList.filter(
        (s) => s.paramId == paramId
      );
      return complication && complication.length > 0
        ? complication[0].paramValue
        : "";
    }
    return "";
  }

  getComplicationsFrequencyName(frequency) {
    if (frequency) {
      return this.complicationsFrequencyList
        .filter((s) => s.id == frequency)
        .map((s) => s.description)[0];
    }
  }

  addNewComplication() {
    if (!this.complicationList) {
      this.complicationList = [];
    }
    this.complicationList.push({ complication: "", remarks: "" });
    this.complicationList[this.complicationList.length - 1].isEditable = true;
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }
  onRowEditSave(row: any) {
    let rowIndex = this.compFg.indexOf(row);
    this.compFg[rowIndex] =
      this.complicationForm.value.rgTbGenComplication[rowIndex];
    let data = this.compFg[rowIndex];

    if (!data.paramId) {
      this.notificationService.showWarning("Warning", "Please select complication.");
      data.isEditable = true;
      return;
    }

    data.complicationName = this.complicationMastList
      .filter((s) => s.paramId == data.paramId)
      .map((s) => s.paramValue)[0];
    data.entryDate = moment(this.selectedDate, "DD-MM-YYYY").format();
    data.isEditable = false;
  }

  delete(row: any) {
    this.notificationService.showConfirmationDialog(
      "Are you sure?",
      "You won't be able to revert this!",
      "Yes, delete it!"
    ).then((confirmed) => {
      if (confirmed) {
        this.rgTbGenComplication = this.complicationForm.get(
          "rgTbGenComplication"
        ) as FormArray;
        this.delRow = this.compFg.indexOf(row);
        this.compFg.splice(this.delRow, 1);
        this.rgTbGenComplication.removeAt(this.delRow);
      }
    });
  }

  updateProcedurePagination() {
    if (!this.uniqueProcedureTypes || this.uniqueProcedureTypes.length === 0) {
      this.paginatedProcedureTypes = [];
      return;
    }

    const startIndex = (this.currentProcedurePage - 1) * this.proceduresPerPage;
    const endIndex = Math.min(
      startIndex + this.proceduresPerPage,
      this.uniqueProcedureTypes.length
    );
    this.paginatedProcedureTypes = this.uniqueProcedureTypes.slice(
      startIndex,
      endIndex
    );
  }

  updateUniqueProcedureTypes() {
    this.currentProcedurePage = 1;
    this.updateProcedurePagination();
  }
}