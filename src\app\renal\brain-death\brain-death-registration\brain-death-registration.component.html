<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Brain Death Determination Form</h6>

    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Civil ID" [(ngModel)]="searchCivilId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>


<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1" [destroyOnHide]="false">
        <ngb-panel id="patientDetails" id="ngb-panel-1">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" (checkAge)="checkAge()" (callMethod)="callMpiMethod()"
                    #patientDetails>
                </app-patient-details>

            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>

<!-- ******************************************tablee******************************************************* -->

<div class="accordion register" *ngIf="showExamDetails">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-2" [destroyOnHide]="false">
        <ngb-panel id="vitalSigns" id="ngb-panel-2">


            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Brain Death Examination Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>
                <!-- START HERE -->

                <table class="custom-table table-row-active">
                    <thead>
                        <tr>
                            <th>Examination Type</th>
                            <th>Exam Date I</th>
                            <th>Examiner I</th>
                            <!-- <th>Verifier I</th> -->

                            <th>Exam Date II</th>
                            <th>Examiner II</th>
                            <!-- <th>Verifier II</th> -->
                        </tr>
                    </thead>
                    <tbody>
                        <ng-container *ngFor="let item of brainDeathExaminationData ; let i = index">
                            <tr (click)="populateSelectedData(item.tranId, i)" [class.active]="selectedIndex === i">
                                <td>{{item.examinationType}}</td>

                                <td>{{item.firstExamDate | date: 'dd-MM-yyyy'}}</td>
                                <td>{{item.firstExamByName}}</td>
                                <!-- <td>{{item.firstExamVerifiedName}}</td> -->


                                <td id="newo">{{item.secondExamDate | date: 'dd-MM-yyyy'}}</td>
                                <td>{{item.secondExamByName}}</td>
                                <!-- <td>{{item.secondExamVerifiedName}}</td> -->


                            </tr>
                        </ng-container>
                    </tbody>
                </table>

                <!-- END HERE -->
                <div id="spacic" class="text-right pb-2">
                    <button *ngIf="showNow" (click)="addSecondExamDetails()" class="btn btn-sm btn-primary">Add Second
                        Examination</button>
                </div>

            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>

<div *ngIf="patientType == 'A' "></div>
<div *ngIf="patientType == 'P' "></div>


<form [formGroup]="brainDeathForm" *ngIf="patientType == 'A' || patientType == 'P' ">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
        <ngb-panel id="BMIDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Form: National Brain Death Determination Form <span *ngIf="patientType == 'A' ">(Adult)</span>
                        <span *ngIf="patientType == 'P' ">(Pediatrics and
                            Neonates)</span>
                    </h6>

                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>

                <!-- <input class="form-control form-control-sm" type = "hidden" formControlName="examinationType" > -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="table-wrap" formGroupName="brainDeathReg">
                            <table class="table ">
                                <tr>
                                    <td>

                                        <label id="spacei"> Primary Diagnosis:</label>
                                        <input type="text" class="form-control form-control-sm custom-input"
                                            formControlName="primaryDiagnosis">
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>
                </div>

                <div class="row" *ngIf="patientType == 'P' ">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <table class="table table2">
                                <thead class="thead-primary">
                                    <tr>
                                        <th>Age of patient</th>
                                        <th>Timing of first exam</th>
                                        <th>Timing of second exam</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="alert" role="alert">
                                        <td>
                                            Term newborn 37 weeks gestational age and < 30 days of age </td>
                                        <td>
                                            First exam may be performed at least 48 hrs after
                                            birth OR following CPR or other servere brine injury.
                                        </td>
                                        <td>
                                            At least 24 hours from first exam.
                                        </td>


                                    </tr>
                                    <tr class="alert" role="alert">
                                        <td>
                                            30 day and less than 13 years of age
                                        </td>
                                        <td>
                                            First exam may be performed 24 hrs following CPR or
                                            other servere brine injury.
                                        </td>
                                        <td>
                                            At least 6 hours from first exam.
                                        </td>

                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>


                <div class="row">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <table class="table table2">
                                <thead class="thead-primary">
                                    <tr>
                                        <th>Part 1. Prerequisites:</th>
                                        <th>Examiner One</th>
                                        <th>Examiner Two</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="alert" role="alert" formGroupName="brainDeathReg">
                                        <td>
                                        </td>
                                        <td>
                                            <label> Date/TIME: <span class="mdtr">*</span></label>

                                            <p-calendar [disabled]="enableFirstExam" class="form-control" showIcon=true
                                                appendTo="body" showTime="true" dateFormat="dd-mm-yy"
                                                formControlName="firstExamDate" [maxDate]=today showButtonBar="true"
                                                (onClose)="firstDateValidation($event)"
                                                [ngModelOptions]="{standalone: true}"></p-calendar>

                                        </td>

                                        <td>

                                            <label> Date/TIME: <span class="mdtr">*</span></label>
                                            <p-calendar [disabled]="enableSecondExam" class="form-control"
                                                appendTo="body" showTime="true" dateFormat="dd-mm-yy"
                                                formControlName="secondExamDate" showButtonBar="true" [maxDate]=today
                                                (onClose)="firstDateValidation($event)"
                                                [ngModelOptions]="{standalone: true}" showIcon=true></p-calendar>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>


                <div class="row" *ngIf="part1 && part1.length>0">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <p-dataTable [immutable]="false" [value]="part1His" [editable]="true" dataKey="runId"
                                class="p-tables" [responsive]="true">



                                <p-column field="runId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part1">
                                            <div [formGroupName]="rowIndex">
                                                {{getExamValue(row.examParamId)}} <span class="mdtr">*</span>
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examParamId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examGroupId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="runId">

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>
                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part1">
                                            <div [formGroupName]="rowIndex">
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part1">
                                            <div [formGroupName]="rowIndex">

                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                            </p-dataTable>
                        </div>
                    </div>
                </div>











                <div class="row" *ngIf="part2 && part2.length>0">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <table class="table table2">
                                <thead class="thead-primary">
                                    <tr>
                                        <th>Part 2. Physical examination:</th>
                                        <th>Examiner One</th>
                                        <th>Examiner Two</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row" *ngIf="part2 && part2.length>0">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <p-dataTable [immutable]="false" [value]="part2His" [editable]="true" dataKey="runId"
                                class="p-tables" [responsive]="true">



                                <p-column field="runId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part2">
                                            <div [formGroupName]="rowIndex">
                                                {{getExamValue(row.examParamId)}} <span class="mdtr">*</span>
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examParamId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examGroupId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="runId">

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>
                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part2">
                                            <div [formGroupName]="rowIndex">
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part2">
                                            <div [formGroupName]="rowIndex">

                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableSecondExam"
                                                        formControlName="secondExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableSecondExam"
                                                        formControlName="secondExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableSecondExam"
                                                        formControlName="secondExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                            </p-dataTable>
                        </div>
                    </div>
                </div>






                <div class="row" *ngIf="part3 && part3.length>0">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <table class="table table2">
                                <thead class="thead-primary">
                                    <tr>

                                        <th>Part 3. Apnea Test:
                                            If high spinal cord injury or test could not be completed toproceed with the
                                            ancillary test
                                        </th>
                                        <th></th>
                                        <th></th>

                                    </tr>

                                </thead>

                                <tbody formGroupName="brainDeathReg">


                                    <tr class="alert" role="alert">
                                        <td>
                                            Pre-test PaCO<sub>2</sub> <span class="mdtr">*</span>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control form-control-sm"
                                                    [attr.disabled]="enableFirstExam" formControlName="firstPrePaco2">
                                                <span>mm Hg</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control form-control-sm"
                                                    formControlName="secondPrePaco2" [attr.disabled]="enableSecondExam">
                                                <span>mm Hg</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="alert" role="alert">
                                        <td>
                                            Post-test PaCO<sub>2</sub> <span class="mdtr">*</span>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control form-control-sm"
                                                    [attr.disabled]="enableFirstExam" formControlName="firstPostPaco2">
                                                <span>mm Hg</span>
                                            </div>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control form-control-sm"
                                                    formControlName="secondPostPaco2"
                                                    [attr.disabled]="enableSecondExam">
                                                <span>mm Hg</span>
                                            </div>
                                        </td>

                                    </tr>


                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>



                <div class="row" *ngIf="part3 && part3.length>0">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <p-dataTable [immutable]="false" [value]="part3His" [editable]="true" dataKey="runId"
                                class="p-tables" [responsive]="true">



                                <p-column field="runId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part3">
                                            <div [formGroupName]="rowIndex">
                                                {{getExamValue(row.examParamId)}} <span class="mdtr">*</span>
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examParamId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="examGroupId">
                                                <input type="hidden" class="form-control form-control-sm"
                                                    style="width: 60px;" formControlName="runId">

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>
                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part3">
                                            <div [formGroupName]="rowIndex">
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input type="radio" [attr.disabled]="enableFirstExam"
                                                        formControlName="fisrtExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                                <p-column field="examParamId">
                                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                        <ng-container formArrayName="part3">
                                            <div [formGroupName]="rowIndex">

                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="Y">
                                                    Yes
                                                </span>
                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="N">
                                                    NO
                                                </span>
                                                <span class="col">
                                                    <input [attr.disabled]="enableSecondExam" type="radio"
                                                        formControlName="secondExamValue" value="I">
                                                    <span title={{title2}}>NA</span>
                                                </span>

                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </p-column>

                            </p-dataTable>
                        </div>
                    </div>
                </div>







                <div class="row">
                    <div class="col-md-12">
                        <div class="table-wrap">
                            <table class="table">
                                <thead class="thead-primary">
                                    <tr>
                                        <th>
                                            <span class="col-6" formGroupName="brainDeathReg"> Part 4. Ancillary Testing
                                                performed:

                                                <span class="col-3">
                                                    <input type="radio" [attr.disabled]="disableAncillaryTest"
                                                        formControlName="ancillaryTestYn" value="Y"
                                                        (change)="onChange($event)">
                                                    Yes
                                                </span>

                                                <span class="col-3">
                                                    <input type="radio" [attr.disabled]="disableAncillaryTest"
                                                        formControlName="ancillaryTestYn" value="N"
                                                        (change)="onChange($event)">
                                                    NO
                                                </span>
                                                <!-- <input [attr.disabled]="enableSecondExam" type="radio" formControlName="secondExamValue" value="N"> -->
                                            </span>

                                        </th>
                                    </tr>
                                </thead>
                                <tbody *ngIf="radioIsSelected">
                                    <tr class="alert" role="alert" formGroupName="brainDeathReg">
                                        <td>
                                            Reason: <span class="mdtr">*</span>
                                            <input type="text" [attr.disabled]="disableAncillaryTest"
                                                class="form-control form-control-sm custom-input input-md"
                                                formControlName="ancillaryTestReason">
                                        </td>

                                    </tr>

                                    <!-- <tr class="alert" role="alert">
                                        <td>
                                            DATE/TIME:
                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showTime="true"
                                                showIcon=true [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" [minDate]=today yearRange="1930:2030"
                                                yearNavigator="true" showButtonBar="true"></p-calendar>
                                        </td>
                                    </tr> -->
                                    <tr class="alert" role="alert" formGroupName="brainDeathReg">
                                        <td>
                                            <span class="col">Absence of intracranial blood flow has been demonstrated
                                                by: <span class="mdtr">*</span> </span>

                                            <span class="col">
                                                <input type="radio" [attr.disabled]="disableAncillaryTest" value="F"
                                                    formControlName="intracranialBloodFlow">
                                                Four vessels cerebral
                                                angiography
                                            </span>

                                            <span class="col"><input type="radio" value="R"
                                                    [attr.disabled]="disableAncillaryTest"
                                                    formControlName="intracranialBloodFlow"> Radionuclide
                                                imaging</span>
                                            <span class="col"><input type="radio" value="C"
                                                    [attr.disabled]="disableAncillaryTest"
                                                    formControlName="intracranialBloodFlow"> CT
                                                angiography</span>
                                        </td>


                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-wrap">

                            <table class="table table2">
                                <thead class="thead-primary">
                                    <tr>
                                        <th>Part 5. Signatures:</th>
                                        <th>Examiner One</th>
                                        <th>Examiner Two</th>

                                    </tr>
                                </thead>
                                <tbody>

                                    <tr class="alert" role="alert" formGroupName="brainDeathReg">
                                        <td>
                                            I certify that my examination and/or ancillary test report confirms
                                            unchanged and irreversible cessation of function of the brain and brainstem.
                                        </td>


                                        <td class="exam">

                                            Exam By : <input type="text" [attr.disabled]="ExamByNameReadOnly"
                                                class="form-control form-control-sm inline-block custom-input input-sm mr-2"
                                                formControlName="firstExamBy" id="inputIdOne"
                                                [ngModelOptions]="{standalone: true}">


                                            <b> <input
                                                    class="form-control custom-input input-lg form-control-sm inline-block mr-2"
                                                    [attr.disabled]="ExamByNameReadOnly" type="text" id="inputId"
                                                    formControlName="firstExamByName"></b>

                                        </td>

                                        <td class="exam">Exam By :<input [attr.disabled]="ExamByNameReadOnly"
                                                type="text"
                                                class="form-control form-control-sm inline-block mr-2 custom-input input-sm  "
                                                formControlName="secondExamBy" id="inputIdtwo"
                                                [ngModelOptions]="{standalone: true}">


                                            <b> <input
                                                    class="form-control form-control-sm custom-input input-lg  inline-block mr-2"
                                                    [attr.disabled]="ExamByNameReadOnly" type="text" id="inputIdThree"
                                                    formControlName="secondExamByName"></b>
                                        </td>

                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </ng-template>
        </ngb-panel>
    </ngb-accordion>

</form>

<div class="btn-container">
    <button id="spaceq" class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
    <button id="spacer" class="btn btn-sm btn-primary" *ngIf="disableAfterNotify" (click)="save()"> Save</button>
    <button id="spacef" class="btn btn-sm btn-primary" *ngIf="notifyInst" (click)="sendSms()"> Notify</button>
</div>