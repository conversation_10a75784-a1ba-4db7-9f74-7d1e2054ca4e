
import { Observable } from 'rxjs/Observable';
import { HttpClient, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as AppUtils from '../common/app.utils';
import { SharedService } from '../_services/shared.service';


@Injectable({
  providedIn: 'root'
})
export class ElderlyService {

  constructor(private http: HttpClient , private _sharedService: SharedService) { }
  getDashboard(): Observable<any> {
    return this.http.get(AppUtils.ELDERLY_DASHBOARD);
  }

   saveElderly(data) :Observable<any>{
    //return this.http.post(environment.eregistryApi+'api/eldr/registries/save', data);
    return this.http.post(AppUtils.SAVE_ELDERLY_REGISTRY, data);
   }

      
   getElderly(regNo, regType, civilId, patientId , createdInstid) {
    return this.http.get(AppUtils.GET_ELDERLY_REGISTRY, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_ELDERELY.toString()).set("civilId", civilId).set("patientId",patientId).set("createdInstid",createdInstid)
    })
  }
    /**
   * extractData
   */
  public extractData(res: HttpResponse<any>) {
    let body = res;
    return body || {}
  }


   //api elderly export
  getExcelExport2(compParam: any): Observable<any> {
    let httpOptions: any = {
      headers: new HttpHeaders({}),
    };
    httpOptions.responseType = 'blob';
    return this.http.post(AppUtils.ELDERLY_EXPORTEXCEL2, JSON.stringify(compParam), httpOptions);
    // return this.http.post(AppUtils.ELDERLY_EXPORTEXCEL, JSON.stringify(compParam), { responseType: 'blob' });

  }


   // elderly listing
   getEldelyListing(data): Observable<any> {
    return this.http.post(AppUtils.SEARCH_ELDERLY_REGISTRY, data);
    
  }


 

}
