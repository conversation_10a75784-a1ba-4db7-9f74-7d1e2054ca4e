import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';
import * as moment from 'moment';
import { DatePipe } from '@angular/common';

@Injectable({
	providedIn: 'root'
  })
export class ChildNutritionService {
	checkChildRegister(estCode: any, patientId: any, regType: any): Observable<any> {
		return this.http.get(AppUtils.CHECK_CHILD_REGISTRY + "?patientID=" + patientId + "&regType=" + regType + "&estCode=" + estCode);
	
	  }
	fetchChildNutRegisterFromShifa(estCode: any, patientId: any): Observable<any> {
		return this.http.get(AppUtils.FETCH_CHILD_REGISTRY_FROM_ALSHIFA + "?estCode=" + estCode + "&patientId=" + patientId);
	
	  }

	  fetchChildNutLabDetails(mohTestCode: any): Observable<any> {
		return this.http.get(AppUtils.FETCH_CHILD_LAB_DETAILS + "?mohTestCode=" + mohTestCode);
	
	  }

	constructor(private http: HttpClient, private datePipe: DatePipe) { }

	getChildNutritionListing(date): Observable<any> {
  
		return this.http.post(AppUtils.GET_CHILD_NUTRITION_LISTING, date);
	   
	  }

	getChildNutritionAssOutcomeMast(): Observable<any> {

	return this.http.get(AppUtils.GET_ASS_OUTCOME);
	
	}

	getChildNutritionMalStatusMast(): Observable<any> {

	return this.http.get(AppUtils.GET_MAL_STATUS);
	
	}


	getChildNutritionLabInvest(): Observable<any> {

	return this.http.get(AppUtils.GET_NUT_LAB);
	
	}

	getChildNutritionAssessLact(): Observable<any> {

	return this.http.get(AppUtils.GET_NUT_ASSESS_LACT);
	
	}

	getChildNutritionKeyMsgs(): Observable<any> {

		return this.http.get(AppUtils.GET_NUT_KEY_MSG);
		
		}
	getChildNutritionMedicalHistory(): Observable<any> {

		return this.http.get(AppUtils.GET_NUT_MED_HISTORY);
			
		}
	
	getNutritionPeriod(dob, regDate): Observable<any> {
		let dobF = moment(dob).format('DD-MMM-yyyy');
		let regDateF =this.transformDate(regDate);
		return this.http.get(AppUtils.GET_NUTRITION_PERIOD, {params: new HttpParams().set("dob", dobF).set("regDate", regDateF)});
	   
	}

	getChildNutritionRegistry(nutNo): Observable<any> {

	return this.http.get(AppUtils.GET_CHILD_NUTRITION_Registry,{
		params: new HttpParams().set("nutNo", nutNo)
		});
	
	}

	getChildNutritionRegistryByCivilId(civilId): Observable<any> {

	return this.http.get(AppUtils.GET_CHILD_NUTRITION_Registry_BY_CIVIL_ID,{
		params: new HttpParams().set("civilId", civilId)
		});
	
	}

	getChildNutritionCheckList(): Observable<any> {

		return this.http.get(AppUtils.GET_NUT_CHECK_LIST);
		
		}

	getNutritionZscoreCalc(reg, gender, dob, visitDate, height, weight, headCirc, muac, tsfa, ssfa, position, oedemaYn): Observable<any> {
		let dobF = moment(dob).format('DD-MMM-yyyy');
		let visitDateF =this.transformDate(visitDate);
		return this.http.get(AppUtils.GET_CHILD_NUTRITION_Z_SCORE_CALC, {params: new HttpParams().set("centralRegNo", reg).set("gender", gender).set("dob", dobF).set("visitDate", visitDateF).set("height", height).set("weight", weight).set("headCirc", headCirc).set("muac", muac).set("tsfa", tsfa).set("ssfa", ssfa).set("position", position).set("oedemaYn", oedemaYn)});
		
	}
	getNutritionZscoreCalcR(gender, dob, visitDate, height, weight, headCirc, muac, tsfa, ssfa, position, oedemaYn): Observable<any> {
		let dobF = moment(dob).format('DD-MMM-yyyy');
		let visitDateF =this.transformDate(visitDate);
		return this.http.get(AppUtils.GET_CHILD_NUTRITION_Z_SCORE_CALCR, {params: new HttpParams().set("gender", gender).set("dob", dobF).set("visitDate", visitDateF).set("height", height).set("weight", weight).set("headCirc", headCirc).set("muac", muac).set("tsfa", tsfa).set("ssfa", ssfa).set("position", position).set("oedemaYn", oedemaYn)});
		
	}
	

	

	transformDate(dateString: any): string {
		dateString = dateString.toString();
		if (dateString.includes('+')){
			return moment(dateString).format('DD-MMM-yyyy')
		}else{
			const dateParts = dateString.split('-');
			const date = new Date(parseInt(dateParts[2]), parseInt(dateParts[1]) - 1, parseInt(dateParts[0]));
			return this.datePipe.transform(date, 'd-MMM-yyyy');
		}
		
	  }

	  saveNutRegister(data: any): Observable<any> {
		return this.http.post(AppUtils.SAVE_NUT_REGISTER, data);
	  }
	  

	  getNutDashboard(): Observable<any> {
        return this.http.get(AppUtils.CHILD_NUT_DASHBOARD);
      }

	  getZscoreDatasetByZId(zId): Observable<any> {

		return this.http.get(AppUtils.GET_ZSCORE_DATASET,{
			params: new HttpParams().set("zId", zId)
			});
		
		}

}