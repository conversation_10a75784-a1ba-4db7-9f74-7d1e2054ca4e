export interface HeartDashboard {
    centralRegNo: number;
    estCode: number;
    walCode: number;
    regCode: number;
    dob: Date;
    age: number;
    icd: string;
    recTransYn: string;
    urgentTransYn: string;
    inotropeYn: string;
    mcsDeviceUsed: string;
    duration: string;
    nyhaClass: number;
    physiotherapyBase: number;
    primaryDiag: number;
    psychosocialClearance: number;
}

export class HeartDashboardDisplay {

    public centralRegNo: number;
    public value: String;

}