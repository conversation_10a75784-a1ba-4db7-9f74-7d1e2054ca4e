<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Renal Waiting List</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>
<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" [callRenalWaitingLisitng]="callRenalWaitingLisitng" (callMethod)="callListingPage()" #patientDetails></app-patient-details>
               
            </ng-template>


        </ngb-panel>
    </ngb-accordion>

<!--
     <button class="btn btn-sm btn-primary btn5" (click)="callListingPage()">Waiting List</button>
-->
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
        <ngb-panel id="waitingList" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Renal Waiting List Details</h6>
                    <div ngbPanelToggle class="btn btn-link p-0">
                        <button class="btn btn-primary add-btn pull-right" style="width:55px ;" type="button"
                            (click)="addRec('waitingListGrid')">Add</button>
                        <!--  <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>-->
                    </div>
                </div>
            </ng-template>

            <ng-template ngbPanelContent>

                <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham"
                    [gridOptions]="waitingListGrid" [rowData]="rowData" [columnDefs]="columnDefs" enableSorting
                    enableFilter rowSelection="single" singleClickEdit=true [enableColResize]="true"
                    [frameworkComponents]="frameworkComponents" row-height="22"
                    (gridReady)="onReady($event, 'waitingListGrid')">
                </ag-grid-angular>


            </ng-template>

        </ngb-panel>
    </ngb-accordion>

    <ng-template #ScoringInfo let-modal>

        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Priority Scoring</h4>
            <!--<button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                <span aria-hidden="true">&times;</span>
            </button>-->
        </div>
        <div class="modal-body">
            <form [formGroup]="scoringForm">
                <div class="form-group">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <td colspan="2">
                                    <span *ngIf="scoresStatus == 'update'" class="mdtr"> there is an update in the
                                        current scores</span>
                                    <span *ngIf="scoresStatus == 'newDonor'" class="mdtr"> new scores from a new
                                        Donor</span>
                                    <span *ngIf="scoresStatus == 'newScores'" class="mdtr"> new scores to save</span>
                                    <span *ngIf="scoresStatus == 'nothing'"> There is no update </span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <th scope="row">PRA</th>
                                <td><input formControlName="pra" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Age</th>
                                <td><input formControlName="ageScore" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Period on dialysis</th>
                                <td><input formControlName="dialysisPeriod" type="text"
                                        class="form-control form-control-sm" readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Previous failed LRD transplant</th>
                                <td><input formControlName="prevFailed" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">HLA match</th>
                                <td><input formControlName="hlaMatch" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Identical blood group</th>
                                <td><input formControlName="bloodGroup" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Proximity of age group (age group within 10 years)</th>
                                <td><input formControlName="ageProximity" type="text"
                                        class="form-control form-control-sm" readonly></td>
                            </tr>
                            <tr>
                                <th scope="row">Previous solid organ donor</th>
                                <td><input formControlName="prevDonor" type="text" class="form-control form-control-sm"
                                        readonly></td>
                            </tr>
                        </tbody>
                    </table>

                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-sm btn-primary" (click)="cancelScoring()">Cancel</button>

            <button type="button" class="btn btn-sm btn-primary" (click)="saveScoring()"
                (click)="modal.dismiss('Cross click')"
                *ngIf="scoresStatus == 'update' || scoresStatus == 'newDonor' || scoresStatus == 'newScores'">Save</button>


        </div>

    </ng-template>

    <div class="btn-container">
        <button class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
        <button class="btn btn-sm btn-primary" (click)="saveWaitingList('waitingListGrid')"> Save</button>
        <button class="btn btn-sm btn-primary" (click)="openModalInfo(ScoringInfo)"> Score</button>

    </div>
</div>