import { HttpClient, HttpParams } from '@angular/common/http';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { SharedService } from '../../_services/shared.service';
import { LiverService } from "../liver.service";
import * as AppUtils from '../../common/app.utils';
import { RenalWaitingList } from '../../_models/renal-waiting-list.model';
import { RegistryPatient } from '../../_models/registry-patient.model';
import { connectableObservableDescriptor } from 'rxjs/internal/observable/ConnectableObservable';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PatientInfo } from '../../_models/patient.model';
import { TbPatientInfo } from '../../_models/patient-info.model';
import { <PERSON>umn<PERSON><PERSON>, GridA<PERSON>, GridOptions } from 'ag-grid-community';
import { Parameter } from '../../common/objectModels/parameter-model';
import { YNCellRenderer } from '../../common/agGridComponents/ag-YN-select-component';

import { GridNgSelectDataComponent } from '../../common/agGridComponents/grid-ngSelect-data.component';
import * as GridUtils from '../../common/agGridComponents/app.grid-spec-utils';
import * as moment from 'moment';
import { formatDate } from '@angular/common';
import { RenalWaitingListFrom } from '../../_models/renal-waiting-list-form.models';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import * as AppCompUtils from '../../common/app.component-utils';
import { Parameter2 } from '../../common/objectModels/parameter-2-model';
import { ButtonRendererComponent } from '../../common/agGridComponents/ButtonRendererComponent';
import Swal from 'sweetalert2';
import * as CommonConstants from '../../_helpers/common.constants';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RenalScores } from '../../common/objectModels/renal-scores-model';

@Component({
  selector: 'app-liver-waiting-list',
  templateUrl: './liver-waiting-list.component.html',
  styleUrls: ['./liver-waiting-list.component.scss'],
  providers: [LiverService],
})

export class LiverWaitingListComponent implements OnInit {
  patientForm: FormGroup;
  @Input() scoringForm: FormGroup;
  regId: any;
  renalwaitingList: Array<RenalWaitingList> = [];
  saveList: Array<RenalWaitingList> = new Array<RenalWaitingList>();
  formData: RenalWaitingListFrom;
  rowData: Array<RenalWaitingList> = new Array<RenalWaitingList>();
  //  dbData: Array<RenalWaitingList> = new Array<RenalWaitingList>();
  //dbData: Array<RenalWaitingList> ;
  dbData: Array<RenalWaitingList>;
  isDisabled: true;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  age: any;
  private waitingListGridApi: GridApi;
  private waitingListGridColApi: ColumnApi;
  indices: any[] = [];
  frameworkComponents;
  columnDefs;
  readyTransplantYnValue: any;
  waitingListGrid: GridOptions;
  submitted = false;
  grids: any = [];
  scores: RenalScores;
  scoresFrom: RenalScores;
  loginId: any;
  scoresStatus: any = null;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  yesNo = AppCompUtils.YES_NO;
  yesNo1: Array<Parameter2> = [{ "id": "Y", "value": "Yes (No Vascular Access)" },
  { "id": "N", "value": "No" }
  ];
  delayReason: Array<Parameter2> = [{ "id": "U", "value": "Under workup" },
  { "id": "T", "value": "Temporarily" },
  { "id": "P", "value": "Permanent" }
  ];
  today = new Date();
  callRenalWaitingLisitng: boolean = true;


  constructor(private fb: FormBuilder, private _router: Router, private _sharedService: SharedService, private _liverService: LiverService, private _formBuilder: FormBuilder, private _modalService: NgbModal) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode
    this.getGrids();


    if (this._sharedService.getNavigationData()) {
      //this.regNo = this._sharedService.getNavigationData().centralRegNo;
      this.getList(this._sharedService.getNavigationData().centralRegNo);
      this._sharedService.setNavigationData(null);
    }

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,

    };
  }


  private getGrids() {
    this.columnDefs = [

      { field: 'runId', hide: true },
      { headerName: 'No Prev Transplat', field: 'noPrevTransplant', minWidth: 125, editable: true },
      { field: 'readyTransplantYn', minWidth: 150, hide: true },
      {
        headerName: 'Ready for transplant', field: 'readyTransplantYnValue', cellEditor: 'ngSelectEditor',
        cellEditorParams: {
          values: this.yesNo,
          dataColumn: 'readyTransplantYn',
          model: new Parameter2(),
          objectData: ['id', 'value']
        }, editable: true, width: 130
      },
      { field: 'transplantDelayReason', minWidth: 150, hide: true },
      {
        headerName: 'Reason', field: 'transplantDelayReasonValue', cellEditor: 'ngSelectEditor', editable: true,

        cellEditorParams: {
          values: this.delayReason,
          dataColumn: 'transplantDelayReason',
          model: new Parameter2(),
          objectData: ['id', 'value']
        }
        , width: 230
      },
      { headerName: 'Remarks', field: 'transplantReasonReamarks', minWidth: 60, editable: true },
      { field: 'transplantUrgentYn', minWidth: 60, hide: true },
      {
        headerName: 'Urgent Transplant is nedded', field: 'transplantUrgentYnValue', cellEditor: 'ngSelectEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo1,
          dataColumn: 'transplantUrgentYn',
          model: new Parameter2(),
          objectData: ['id', 'value']
        }
        , width: 230
      },
      { field: 'previousOrganDonor', minWidth: 60, hide: true },
      {
        headerName: 'Previous Solid organ Donor', field: 'previousOrganDonorValue', cellEditor: 'ngSelectEditor', editable: true,
        cellEditorParams: {
          values: this.yesNo,
          dataColumn: 'previousOrganDonor',
          model: new Parameter2(),
          objectData: ['id', 'value']
        }
        , width: 230
      },
      { field: 'enteredBy', hide: true },
      { field: 'entryDate', hide: true },
      { field: 'modifiedDate', hide: true },
      { field: 'modifiedBy', hide: true },
      { field: 'runId', hide: true },
      {
        headerName: 'Action',
        cellRenderer: 'buttonRenderer',
        cellRendererParams: {
          onClick: this.ConfirmdeleteWaitingList.bind(this),
          label: 'Delete'
        }
      },
    ];


    this.waitingListGrid = <GridOptions>{
      enableColResize: true,
      pagination: true,
      paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
      onGridSizeChanged: () => {
        this.waitingListGrid.api.sizeColumnsToFit();
      }
    };
  }



  ConfirmdeleteWaitingList(e) {

    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: 'btn btn-success',
        cancelButton: 'btn btn-danger'
      },
      buttonsStyling: false
    })

    swalWithBootstrapButtons.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      reverseButtons: true
    }).then((result) => {
      if (!result.dismiss) {
        /*    swalWithBootstrapButtons.fire(
              'Deleted!',
              'The record has been deleted.',
              'success'
            )*/
        this.deleteWaitingList(e);
      } else if (
        /* Read more about handling dismissals below */
        result.dismiss === Swal.DismissReason.cancel
      ) {
        swalWithBootstrapButtons.fire(
          'Cancelled',
          'Your imaginary record is safe :)',
          'error'
        )
      }
    })


  }
  deleteWaitingList(e) {
    let gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === 'waitingListGrid');
    let pushData = [];

    if (e.rowData.runId != null) {
      this._liverService.deleteRenalWaitingList(e.rowData.runId).subscribe(res => {
        if (res['code'] == 0) {
          Swal.fire('Saved!', res['message'], 'success')
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }
      );
    }


    let focusedNode = this.waitingListGridApi.getSelectedRows();
    this[gridObject['gridApi']].updateRowData({ remove: focusedNode });
    this[gridObject['gridApi']].redrawRows();
    if (this[gridObject['gridApi']].getRenderedNodes().length > 0) {
      this[gridObject['gridApi']].getRenderedNodes().forEach(el => {
        let data = el['data'];
        pushData.push(data);
      })
    } else {
      pushData.length = 0;
    }
    this.rowData = pushData;

  }
  ngOnInit() {
    this.scoringForm = this._formBuilder.group({
      'pra': [null],
      'ageScore': [null],
      'dialysisPeriod': [null],
      'prevFailed': [null],
      'hlaMatch': [null],
      'bloodGroup': [null],
      'ageProximity': [null],
      'prevDonor': [null],
      'runId': [null],
      'activeYn': [null],
      'donorID': [null],
      'centralRegNo': [null],
    });
  }
  ngAfterViewInit() {
    this.patientDetails.patientForm.disable();
   // this.patientDetails.patientForm.controls['patientId'].enable();
  }


  addRec(grid: any) {
    
    if (this.formData){
      let colObject = {};
    
    if (grid === 'waitingListGrid') {
      colObject = {
        "runId": null,
        "noPrevTransplant": null,
        "readyTransplantYn": null,
        "readyTransplantYnValue": null,
        "transplantDelayReason": null,
        "transplantDelayReasonValue": null,
        "transplantReasonReamarks": null,
        "transplantUrgentYn": null,
        "transplantUrgentYnValue": null,
        "previousOrganDonor": null,
        "previousOrganDonorValue": null,
        "enteredBy": null,
        "entryDate": null,
        "modifiedDate": null,
        "modifiedBy": null,
        "rgTbRegistryPatient": { "centralRegNo": null }
      }
    }
  
    let gridObject = {}
    gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);
    // if (grid === 'waitingListGrid') {
    //  colObject = gridObject['colObject'];
    // }
    const parentModel = gridObject['parentModel']
    const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();
    let nullRowCnt = 0;
    let nullMandatoryFieldCnt = 0;
    if (renderedNodes.length > 0) {
      renderedNodes.forEach((element, index) => {
        const data = element['data'];
        let isEmpty = true;

        Object.keys(data).forEach(el => {

          if (data[el] === "") {
            data[el] = null;
          }
          if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach(elChild => {
                if (data[el][elChild]) {
                  isEmpty = false;
                }
              })
            } else {
              isEmpty = false;
            }

          }
          if (gridObject['mandatoryColumns']) {
            gridObject['mandatoryColumns'].forEach(mandatoryField => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach(elChild => {
                      if (!data[el][elChild]) {
                        nullMandatoryFieldCnt++;
                      }
                    })
                  }
                } else {
                  nullMandatoryFieldCnt++;
                }
              }
            })
          }
        })
        if (isEmpty) {
          nullRowCnt++;
        }
      })

    }

    if ((nullRowCnt === 0 && nullMandatoryFieldCnt === 0) || renderedNodes.length === 0) {

      this[gridObject['gridApi']].updateRowData({ add: [colObject] });
      this[gridObject['gridApi']].redrawRows();
      this[gridObject['gridApi']].forEachNode((node) => {
        if (node.lastChild) {
          node.setSelected(true);
          this[gridObject['gridApi']].ensureNodeVisible(node);
        } else {
          node.setSelected(false);
        }
      });
    }
  }
  }


  isObject(val) {
    return (typeof val === 'object');
  }


  saveWaitingList(grid) {

    //if (this.patientDetails.patientForm.invalid) {
      if (this.formData){
      

    let nullMandatoryFieldCnt = 0;
    let nullRowCnt = 0;
    let gridObject = {}
    //this.newData = [];
    gridObject = GridUtils.GRID_SPECIFICATIONS.find(value => value['gridName'] === grid);
    const renderedNodes = this[gridObject['gridApi']].getRenderedNodes();

    if (renderedNodes.length > 0) {


      renderedNodes.forEach((element, index) => {

        const data = element['data'];
        let isEmpty = true;


        Object.keys(data).forEach(el => {


          if (data[el] === "") {
            data[el] = null;
          }

          if (data[el] && (gridObject['hiddenColumns'] ? !gridObject['hiddenColumns'].find(field => el === field) : data[el])) {
            if (this.isObject(data[el])) {
              Object.keys(data[el]).forEach(elChild => {
                if (data[el][elChild]) {
                  isEmpty = false;
                }
              })
            } else {
              isEmpty = false;
            }

          }
          if (gridObject['mandatoryColumns']) {
            gridObject['mandatoryColumns'].forEach(mandatoryField => {
              if (el === mandatoryField) {
                if (data[el] && data[el] !== "") {
                  if (this.isObject(data[el])) {
                    Object.keys(data[el]).forEach(elChild => {
                      if (!data[el][elChild]) {
                        nullMandatoryFieldCnt++;
                      }
                    })
                  }
                } else {
                  nullMandatoryFieldCnt++;
                }
              }
            })
          }
        })

        if (isEmpty) {
          nullRowCnt++;
        }

        if (data["runId"] == null) {
          this.rowData.push(data);
        }

      })


    }
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    const loginId = curUser['person'].perscode;
    let currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');

    for (var i = 0; i < this.rowData.length; i++) {
      if (this.rowData[i].runId === null) {
        this.rowData[i].enteredBy = loginId;
        this.rowData[i].entryDate = currentDate; //new Date(moment(this.currentDate, 'DD-mm-yyyy').format("MM-DD-YYYY")).getDate();    
        this.rowData[i].rgTbRegistryPatient.centralRegNo = this.formData.centralRegNo;
      } else {
        this.dbData.filter(x => x.runId == this.rowData[i].runId).map((data) => {
          if (this.rowData[i].noPrevTransplant !== data.noPrevTransplant || this.rowData[i].readyTransplantYn !== data.readyTransplantYn
            || this.rowData[i].transplantDelayReason !== data.transplantDelayReason || this.rowData[i].transplantReasonReamarks !== data.transplantReasonReamarks
            || this.rowData[i].transplantUrgentYn !== data.transplantUrgentYn || this.rowData[i].previousOrganDonor !== data.previousOrganDonor) {

            this.rowData[i].modifiedBy = loginId;
            this.rowData[i].modifiedDate = currentDate; //new Date(moment(this.currentDate, 'DD-mm-yyyy').format("MM-DD-YYYY")).getDate();
          }
        })
      }
      this.saveList = this.rowData.map(x => Object.assign({}, x));

    }
    for (let i = 0; i < this.saveList.length; i++) {
      this.saveList[i].rgTbRegistryPatient = { centralRegNo: this.formData.centralRegNo }


    }

    this._liverService.saveRenalWaitingList(this.saveList).subscribe(res => {
      if (res['code'] == 0) {
        Swal.fire('Saved!', 'Renal Waiting List has been Saved.', 'success');
        this.regId = res["result"];
        this.search();
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }
    }
      , err => {
        Swal.fire('Error!', 'Error occured while saving Renal Waiting List ' + err.message, 'error')
      });


    }
    


  
  else{
    Swal.fire(
      'Warning',
      'No Record Found to Save! ',
      'warning'
    )
    //return false;
  }
}
  onCellClicked(event, grid?: any) {


  }

  onReady(params, grid) {
    if (this.grids.length > 0) {
      const exist = this.grids.find(item => grid === item);
      if (!exist) {
        this.grids.push(grid)
      }
    } else {
      this.grids.push(grid)
    }

    if (grid === "waitingListGrid") {
      this.waitingListGridApi = params.api;
      this.waitingListGridColApi = params.columnApi;
    }
  }


  getList(regNo: any) {
    this._liverService.getRenalWaitingList(regNo).subscribe(res => {
      if (res['code'] == "S0000") {
        this.formData = res['result'];
        this.patientDetails.setPatientDetails(res['result']);
        this.renalwaitingList = this.formData.rgTbRenalWaitingList;
        this.displayWaitingList();
      } else if (res['code'] == "F0000") {
        Swal.fire(
          'Warning',
          'No Record Found with Entered Regstration No. ',
          'warning'
        )
      } else {
        Swal.fire('warning!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })
  }

  displayWaitingList() {
    if (this.waitingListGrid.api) { this.waitingListGrid.api.showLoadingOverlay(); }

    this.rowData = this.renalwaitingList;
    this.dbData = this.rowData.map(x => Object.assign({}, x));
    for (var i = 0; i < this.renalwaitingList.length; i++) {
      this.renalwaitingList[i].readyTransplantYnValue = this.yesNo.filter(s => s.id == this.renalwaitingList[i].readyTransplantYn).map(s => s.value).toString();
      this.renalwaitingList[i].transplantDelayReasonValue = this.delayReason.filter(s => s.id == this.renalwaitingList[i].transplantDelayReason).map(s => s.value).toString();
      this.renalwaitingList[i].transplantUrgentYnValue = this.yesNo1.filter(s => s.id == this.renalwaitingList[i].transplantUrgentYn).map(s => s.value).toString();
      this.renalwaitingList[i].previousOrganDonorValue = this.yesNo.filter(s => s.id == this.renalwaitingList[i].previousOrganDonor).map(s => s.value).toString();
      if (this.renalwaitingList[i].readyTransplantYn === 'Y') {
        this.columnDefs.field = { editable: false };

      }
    }

    // this.rowData = this.renalwaitingListdisplay;
  }


  getAge(event) {
    this.age = this._sharedService.ageFromDateOfBirthday(event);
    //this.patientForm.patchValue({ age: this._sharedService.ageFromDateOfBirthday(event) });

  }

  search() {

    if (this.regId) {
      this.clear();
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
      /*
      swal({
        text: 'Please enter Registration ID',
        type: 'warning',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });*/
    }
  }

  clear() {
    this.formData = null;
    this.patientDetails.clear();
    this.rowData = null;
  }

  saveScoring() {
    let scores = this.scoringForm.value;


    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let runId: number;

    if (this.scoresStatus === 'update') {
      //update use same runid
      createdBy = this.scoresFrom.createdBy;
      createdOn = this.scoresFrom.createdOn;
      modifiedBy = this.loginId;
      modifiedOn = this.currentDate;
      runId = scores.runId;
    } else if (this.scoresStatus === 'newDonor') {
      //insert new record, new Donor
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      runId = null;
    } else if (this.scoresStatus === 'newScores') {
      //insert freash record
      createdBy = this.loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      runId = null;

    } else {
      // nothing
      return;
    }

    let saveScores = {
      runId: runId,
      centralRegNo: scores.centralRegNo,
      pra: scores.pra,
      ageScore: scores.ageScore,
      dialysisPeriod: scores.dialysisPeriod,
      prevFailed: scores.prevFailed,
      hlaMatch: scores.hlaMatch,
      bloodGroup: scores.bloodGroup,
      ageProximity: scores.ageProximity,
      prevDonor: scores.prevDonor,
      activeYn: "Y",
      donorID: scores.donorID,
      modifiedBy: modifiedBy,
      createdBy: createdBy,
      modifiedOn: modifiedOn,
      createdOn: createdOn,

    }


    //console.log(saveScores);

    this._liverService.saveScores(saveScores).subscribe(res => {
      if (res['code'] == 0) {
        Swal.fire('Saved!', 'Renal Scores has been Saved.', 'success');
        this.regId = res["result"];
        this.search();
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }
    }
      , err => {
        Swal.fire('Error!', 'Error occured while saving scores ' + err.message, 'error')
      });


  }
  cancelScoring() {
    if (this.scoresStatus === 'update' || this.scoresStatus === 'newDonor' || this.scoresStatus === 'newScores') {
      //need to save 
      Swal.fire({
        title: 'Are you sure?',
        text: "There are new scores need to save!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Ok',
        cancelButtonText: 'Cancel',
        reverseButtons: true
      }).then((result) => {
        if (!result.dismiss) {
          /*  Swal.fire(
              'Deleted!',
              'The record has been deleted.',
              'success'
            )
           */
        } else if (
          /* Read more about handling dismissals below */
          result.dismiss === Swal.DismissReason.cancel
        ) {
          Swal.fire(
            'Cancelled',
            'The new scores not saved :)',
            'warning'
          )
          this._modalService.dismissAll()
        }
      })

    } else {
      // nothing
      this._modalService.dismissAll()
      return;
    }

  }
  callListingPage(){
    this._router.navigate(['/renal-waiting/listing']);
  }
  /* popup */
  openModalInfo(info) {
  /*  this.scoresFrom = {
      "runId": 1,
      "centralRegNo": 33,
      "createdOn": "2021-02-10",
      "modifiedOn": null,
      "createdBy": "-1001",
      "modifiedBy": null,
      "pra": 1,
      "ageScore": 2,
      "dialysisPeriod": 1,
      "prevFailed": 2,
      "hlaMatch": 3,
      "bloodGroup": 1,
      "ageProximity": 1,
      "prevDonor": 1,
      "activeYn": "Y",
      "donorID": 123,
      "oldPra": 232,
      "oldAgeScore": null,
      "oldDialysisPeriod": null,
      "oldPrevFailed": null,
      "oldHlaMatch": null,
      "oldBloodGroup": null,
      "oldAgeProximity": null,
      "oldPrevDonor": null,
      "oldDonorID": null,
      "status": "update"
    }
    this.scoringForm.patchValue(this.scoresFrom);
    this._modalService.open(info);*/
if (this.formData){
    this.scoresStatus = null;
    this._liverService.getScores(this.formData.centralRegNo).subscribe(res => {

      if (res['code'] == 'S0000') {
        this.scoresFrom = res['result'];
        this.scoresStatus = res['result']['status'];
        this.scoringForm.patchValue(res['result']);
        this._modalService.open(info);

      } if (res['code'] == "F0000" || res['code'] == "3") {
        Swal.fire(
          'Warning',
          'No Record Found with Entered Regstration No. ',
          'warning'
        )
      } else {
        Swal.fire('Error!', res['message'], 'error')
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })


  }

    // this.getData();
  }
}
