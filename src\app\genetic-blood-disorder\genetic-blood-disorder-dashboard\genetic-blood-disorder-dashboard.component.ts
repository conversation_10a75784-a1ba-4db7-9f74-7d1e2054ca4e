import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { GeneticService } from '../genetic.service';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { GeneticBloodDashboardDisplay } from '../../_models/genetic-dashboard-display.model';
import { GeneticBloodDashboard } from '../../_models/genetic_blood_disorder.model';
import * as AppComponent from '../../common/app.component-utils';
import { MasterService } from '../../_services/master.service';
import { SharedService } from '../../_services/shared.service';
import { HaemoglobinFPatientsDisplay } from 'src/app/_models/HaemoglobinFPatients';
import * as CommonConstants from './../../_helpers/common.constants';


@Component({
  selector: 'app-genetic-blood-disorder-dashboard',
  templateUrl: './genetic-blood-disorder-dashboard.component.html',
  styleUrls: ['./genetic-blood-disorder-dashboard.component.scss']
})
export class GeneticBloodDisorderDashboardComponent implements OnInit {

  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: GeneticBloodDashboard[];
  DashboardDataFilter: GeneticBloodDashboard[];
  geneticBloodPatients: GeneticBloodDashboardDisplay[];
  HaemoglobinFPatients: HaemoglobinFPatientsDisplay[];
  filterType: any;
  filterTitle: any;
  charBGColor: any[];
  options: any;
  pieOption: any;
  pieOptionRight: any;
  GeneticBloodPatientsChart: any;
  HaemoglobinFPatientsChart: any;
  ageValidate: boolean;
  genoTypePatientsChart: any;
  genoCompPatientsChart: any;
  genotypeList: any[];
  genoCompList: any[];
  createTime: any;
  constructor(private formBuilder: FormBuilder, private _sharedService: SharedService, private _masterService: MasterService, private _geneticService: GeneticService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION_GENETIC_BLOOD;
    this.pieOptionRight = AppComponent.CHAR_PIE_OPTION_GENETIC_BLOOD_RIGHT
    this.charBGColor = AppComponent.CHAR_BG_COLOR;
    // this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'ageF': [null],
      'ageT': [null],
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });

    this._masterService.getGenotypeDesc().subscribe(response => {
      this.genotypeList = response.result;
    }, error => {

    });

    // for getting complication list
    this._masterService.getGenComplicationMast().subscribe(response => {
      this.genoCompList = response.result;
    }, error => { });


  }

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }

  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._geneticService.getDashboard().subscribe(res => {
      // this.DashboardDataDB = res.result;


      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {
        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'GENETIC', this.institeList);
  
        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }
      }

      this.callFilter();
    })
  }

  callReset() {
    window.location.reload();
  }
  callFilter() {

    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());

    } else {



      let body = this.boardForm.value;



      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }

      if (body['ageF'] > body['ageT']) {
        this.ageValidate = true;
      } else {
        this.ageValidate = false;
        this.displayFilterType();
        this.setChartData();
      }


    }
  }

  displayFilterType(){
  
    if (this.filterType === "institute") {
      this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      
    } else if (this.filterType === "wilayat") {
      this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
   
    } else if (this.filterType === "region") {
      this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
  
    }
    else {
      this.filterTitle = 'All Regions';
    }
  
  }


  setChartData() {
    this.geneticBloodPatients = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {
      let disorderTypeName;
      if (this.DashboardDataFilter[i].disorderType == 1) {
        disorderTypeName = "Sickle Cell Anemia1";
      } else if (this.DashboardDataFilter[i].disorderType == 2) {
        disorderTypeName = "G6PD";
      } else {
        disorderTypeName = "Thalassemia";
      }

      let kin = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: disorderTypeName, genoType: this.DashboardDataFilter[i].genotype };
      this.geneticBloodPatients.push(kin);
    }
    this.callChart();
  }

  callChart() {
    this.callChartGeneticBloodPatients();
    this.callChartHaemoglobinF();
    this.callPieChartGenoType(this.DashboardDataFilter, "this.genoTypePatientsChart");
    this.callPieChartGenComplication(this.DashboardDataFilter, "this.genoCompPatientsChart");
  }


  callChartGeneticBloodPatients() {
    let charlabels = ["Sickle Cell Anemia1", "G6PD", "Thalassemia"];
    let charData = [0, 0, 0];
    let listGroup = [];

    let patientsList = this.geneticBloodPatients.filter(s => s.value != null);

    this.geneticBloodPatients = Array.from(new Set(patientsList.map(a => a.centralRegNo)))
      .map(id => {
        return patientsList.find(a => a.centralRegNo === id)
      })

    for (var n = 0; n < this.geneticBloodPatients.length; n++) {

      if (listGroup.filter(s => s.icd === this.geneticBloodPatients[n].value).length == 0) {
        const result = this.geneticBloodPatients.filter(s => s.value == this.geneticBloodPatients[n].value).length;
        let a = { icd: this.geneticBloodPatients[n].value }


        if (this.geneticBloodPatients[n].value == "Sickle Cell Anemia1") {
          charData[0] = result;
        } else if (this.geneticBloodPatients[n].value == "G6PD") {
          charData[1] = result;
        } else if (this.geneticBloodPatients[n].value == "Thalassemia") {
          charData[2] = result;
        }
        listGroup.push(a);
      }
    }

    // this.charBGColor.sort(() => Math.random() - 0.2);
    this.GeneticBloodPatientsChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        },
      ]
    }
  }

  callChartHaemoglobinF() {
    let charlabels = ["0-5%", "6-10%", "11-15%", "16-20%", "21-25%", "26-30%",
      "31-35%", "36-40%", "41-45%", "46-50%", "51-55%", "56-60%", "61-65%", "66-70%%",
      "71-75%", "76-80%", "81-85%", "86-90%", "91-95%", "96-100%"];
    let charData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    let listGroup = [];

    let patientsList = this.DashboardDataFilter.filter(s => s.valueNumeric != null);

    this.HaemoglobinFPatients = Array.from(new Set(patientsList.map(a => a.runId)))
      .map(id => {
        return patientsList.find(a => a.runId === id)
      })

    for (var n = 0; n < this.HaemoglobinFPatients.length; n++) {

      if (listGroup.filter(s => s.icd === this.HaemoglobinFPatients[n].valueNumeric).length == 0) {

        let a = { icd: this.HaemoglobinFPatients[n].valueNumeric }
        let start = 0;
        let end = 5;
        for (let i = 0; i < 20; i++) {

          const result = this.HaemoglobinFPatients.filter(s => s.valueNumeric > start && s.valueNumeric <= end).length;

          if (this.HaemoglobinFPatients[n].valueNumeric >= start && this.HaemoglobinFPatients[n].valueNumeric <= end) {
            charData[i] = result;
            break;
          }
          start += 5;
          end += 5;
        }

        listGroup.push(a);
      }
    }

    // this.charBGColor.sort(() => Math.random() - 0.2);
    this.HaemoglobinFPatientsChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        },
      ]
    }
  }

  callPieChartGenoType(listData: any[], chartData: any) {

    let data = Array.from(new Set(listData.map(a => a.centralRegNo)))
      .map(id => {
        return listData.find(a => a.centralRegNo === id)
      })


    let groupByName = [];
    let charTitle: any= "";

    if (this.filterType === "institute") {
      data.filter(s => s.estCode == this.boardForm.value['estCode']);
      this.genotypeList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.genotype == a.id).length })
      })
    } else if (this.filterType === "wilayat") {
      data.filter(s => s.estCode == this.boardForm.value['walCode']);
      this.genotypeList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.genotype == a.id).length })
      })
    } else if (this.filterType === "region") {
      data.filter(s => s.estCode == this.boardForm.value['regCode']);
      this.genotypeList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.genotype == a.id).length })
      })
    }
    else {
      this.genotypeList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.genotype == a.id).length })
      })
    }



    this.pieOption.title = {
      display: false,
      text: charTitle,
      fontSize: 15
    }

    if (chartData == "this.genoTypePatientsChart") {
      this.genoTypePatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
  }

  callPieChartGenComplication(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let charTitle: any= "";

    if (this.filterType === "institute") {
      data.filter(s => s.estCode == this.boardForm.value['estCode']);
      this.genoCompList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.compId == a.id).length })
      })
    } else if (this.filterType === "wilayat") {
      data.filter(s => s.estCode == this.boardForm.value['walCode']);
      this.genoCompList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.compId == a.id).length })
      })
    } else if (this.filterType === "region") {
      data.filter(s => s.estCode == this.boardForm.value['regCode']);
      this.genoCompList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.compId == a.id).length })
      })
    }
    else {
      this.genoCompList.forEach(function (a) {
        groupByName.push({ "label": a.description, "count": data.filter(s => s.compId == a.id).length })
      })
    }



    this.pieOption.title = {
      display: false,
      text: charTitle,
      fontSize: 15
    }

    if (chartData == "this.genoCompPatientsChart") {
      this.genoCompPatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
  }

}
