import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as AppUtils from '../common/app.utils';
import { Observable } from 'rxjs';

@Injectable(
  { providedIn: 'root' }
)
export class CorneaService {

  constructor(private _http: HttpClient,) { }

  public getCorTransplantDetails(centralRegNo, civilId) {
    let params = new HttpParams();
    if (centralRegNo !== undefined) {
      params = params.set('centralRegNo', centralRegNo);
    }

    if (civilId !== undefined) {
      params = params.set('civilId', civilId);
    }

    return this._http.get(AppUtils.FIND_CORNEA_PATIENT_RESULT, { params });
  }

  public saveTransplantDetails(dto) {
    return this._http.post(AppUtils.SAVE_CORNEA_PATIENT_RESULT, dto);
  }

  public saveCorneaRequest(dto, deletedAttachmentIds: number[]) {
    let params = new HttpParams();
    if (deletedAttachmentIds && deletedAttachmentIds.length > 0) {
      deletedAttachmentIds.forEach(id => {
        params = params.append('deletedAttachmentIds', id.toString());
      });
    }

    return this._http.post(AppUtils.SAVE_CORNEA_REQUEST, dto, { params });
  }
  public getCorneaRequestList(dto) {
    return this._http.post(AppUtils.GET_CORNEA_REQUEST_LIST, dto);
  }

  public getCorneaRequestById(requestNo: any) {
    return this._http.get(AppUtils.GET_CORNEA_REQUEST_BY_ID, { params: new HttpParams().set("requestNo", requestNo) });
  }
  
  public getCorneaTransplantList(dto) {
    return this._http.post(AppUtils.GET_CORNEA_TRANSPLANT_LIST, dto);
  }

  public getCorneaAttachment(commId: number) {
    return this._http.get(AppUtils.GET_CORNEA_TRANSPLANT_LIST);
  }

  uploadCorneaAttachment(formData: FormData) {
    return this._http.post(AppUtils.SAVE_CORNEA_UPLOAD_ATTACHMENT, formData);
  }

  previewFile(fileName: string) {
    let params = new HttpParams().set('fileName', fileName);
    return this._http.get(AppUtils.GET_CORNEA_FILE, {
      params,
      responseType: 'blob' 
    });
  }

  public saveCorReqSendDtls(dto) {
    return this._http.post(AppUtils.SAVE_CORNEA_REQ_SEND_DTLS, dto);
  }
  public sendEmails(dto) {
    return this._http.post(AppUtils.SEND_EMAILS, dto);
  }

  getSmsNotificationBySmsType(): Observable<any> {
    return this._http.get(AppUtils.GET_SMS_NOTIFICATION);
  }

  getRgTbSmsMsgBySmsType(): Observable<any> {
    return this._http.get(AppUtils.GET_SMS_MSG);
  }

  getCorReqSendDtlsByRequestId(requestId: any): Observable<any> {
    return this._http.get(AppUtils.GET_CORNEA_REQ_SEND_DTLS_BY_ID, { params: new HttpParams().set("requestId", requestId) });
  }

  getDashboard(): Observable<any> {
      return this._http.get(AppUtils.GET_CORNEA_DASHBOARD);
  }
}
