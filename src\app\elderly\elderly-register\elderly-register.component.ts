import { LoginService } from '../../login/login.service';
import { ToastrService } from 'ngx-toastr';
import { ElderlyService } from '../elderly.service';
import { Examparam } from '../../_models/exam-param.model';
import { MasterService } from '../../_services/master.service';
import { Observable } from 'rxjs';
import { FormArray, Validators, FormControl } from '@angular/forms';
import { FormGroup, FormBuilder } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import * as AppParams from '../../_helpers/app-param.constants';
import * as CommonConstants from '../../_helpers/common.constants';
import * as moment from "moment";
import Swal from 'sweetalert2';
import { ViewChild } from '@angular/core';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import { ElderlyVisitInfo } from '../../common/objectModels/elderly-visit-info-model';
import { SharedService } from '../../_services/shared.service';
import * as AppUtils from '../../common/app.utils';
import { ElderlyRegistryFrom } from '../../common/objectModels/elderly-registry-model';
import { AppComponent } from '../../app.component';
import { formatDate } from '@angular/common';
import { TbVitalSigns } from '../../common/objectModels/vital-signs-model';
import { DeathDetailsComponent } from 'src/app/_comments/death-details/death-details.component';

@Component({
  selector: 'app-elderly-register',
  templateUrl: './elderly-register.component.html',
  styleUrls: ['./elderly-register.component.scss']
})
export class ElderlyRegisterComponent implements OnInit {
  regId: any;
  fieldArray: Array<any> = [];
  newAttribute: any = {};
  patientForm: FormGroup;
  elderlyParams;
  public form: FormGroup;
  formData: ElderlyRegistryFrom;
  socioEconomicParams;
  submitted = false;
  today = new Date();
  visitInfo: Array<ElderlyVisitInfo> = new Array<ElderlyVisitInfo>();
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  alive = true;
  currentCivilId = '';
  estCode;
  patientId: any;
  civilIdEntryType: any;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('deathDetails', { static: false }) deathDetails: DeathDetailsComponent;
  constructor(private _sharedService: SharedService, private _loginService: LoginService, private fb: FormBuilder, private masterService: MasterService, private elderlyService: ElderlyService,
    private toastrService: ToastrService) {

    if (this._sharedService.getNavigationData()) {
      this.regId = this._sharedService.getNavigationData().centralRegNo;
      this.search();
      this._sharedService.setNavigationData(null);
    }
  }

  get rgTbVitalSigns(): FormArray {
    return this.patientForm.get("rgTbVitalSigns") as FormArray
  }

  ngOnInit(): void {
    this.patientForm = this.fb.group({
      'centralRegNo': [null],
      'patientId': ["", Validators.required],
      'civilId': ["", Validators.required],
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'firstName': ["", Validators.required],
      'secondName': new FormControl(),
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'maritalStatus': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'kinTelNo': new FormControl(),
      'careGiverName': new FormControl(),
      'careGiverMobile': new FormControl(),
      'careGiverTel': new FormControl(),
      'regInst': [new FormControl(), Validators.required],
      'registerType': [1],
      'exDate': [""],
      'rgTbEldExamTrans': this.fb.array([]),
      'instRegDate': [this.today],
      'rgTbPatientInfo': this.fb.array([]),
      vitals: this.fb.array([this.addVitalsFormGroup()]),
      rgTbVitalSigns: this.fb.array([]),
      socioEconomics: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      bradenScore: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      randomBloodSugar: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      mentalState: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      depressionScale: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      countingMoney: this.fb.array([
        this.fb.group(new Examparam())
      ]),
      incontinence: this.fb.array([
        this.fb.group(new Examparam())
      ]),
      adl: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      iadl: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      timeUp: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      nutritionAssessement: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ]),
      gaitAndBalance: this.fb.array([
        this.fb.group(new Examparam()),
        this.fb.group(new Examparam())
      ])
    });
    //this.addFieldValue();
    this.masterService.exParams.subscribe(res => {
      this.elderlyParams = res["result"];
      // console.log(" this.elderlyParams$ :" + JSON.stringify(this.elderlyParams));
    });
    this.patchSocioEconomicsValues();
    this.patchBradenScoreValues();
    this.patchRandomBloodSugarValues();
    this.patchDepressionScaleValues();
    this.patchCountingMoneyValues();
    this.patchIncontinenceValues();
    this.patchMentalStateValues();
    this.patchAdlValues();
    this.patchIadlValues();
    this.patchTimeUpValues();
    this.patchNutritionAssessementValues();
    this.patchGaitAndBalanceValues();
  }

  get f() { return this.patientForm.controls; }

  getControls() {
    return (this.patientForm.get('vitals') as FormArray).controls;
  }

  get socioEconomics() {
    return this.patientForm.controls.socioEconomics as FormArray;
  }

  get bradenScore() {
    return this.patientForm.controls.bradenScore as FormArray;
  }

  get randomBloodSugar() {
    return this.patientForm.controls.randomBloodSugar as FormArray;
  }

  get mentalState() {
    return this.patientForm.controls.mentalState as FormArray;
  }

  get depressionScale() {
    return this.patientForm.controls.depressionScale as FormArray;
  }

  get countingMoney() {
    return this.patientForm.controls.countingMoney as FormArray;
  }

  get incontinence() {
    return this.patientForm.controls.incontinence as FormArray;
  }

  get adl() {
    return this.patientForm.controls.adl as FormArray;
  }

  get iadl() {
    return this.patientForm.controls.iadl as FormArray;
  }

  get timeUp() {
    return this.patientForm.controls.timeUp as FormArray;
  }

  get nutritionAssessement() {
    return this.patientForm.controls.nutritionAssessement as FormArray;
  }

  get gaitAndBalance() {
    return this.patientForm.controls.gaitAndBalance as FormArray;
  }


  patchMentalStateValues() {
    this.mentalState.patchValue([
      { examParam: AppParams.MENTAL_STATE, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.MENTAL_STATE, freqParam: AppParams.FREQ_PARAM_SECOND_VISIT },
      { examParam: AppParams.MENTAL_STATE, freqParam: AppParams.FREQ_PARAM_THIRD_VISIT }
    ]);
  }


  patchCountingMoneyValues() {
    this.countingMoney.patchValue([
      { examParam: AppParams.COUNTIN_MONEY, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT }
    ]);
  }

  patchIncontinenceValues() {
    this.incontinence.patchValue([
      { examParam: AppParams.INCONTINENCE, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT }
    ]);
  }

  patchRandomBloodSugarValues() {
    this.randomBloodSugar.patchValue([
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_SECOND_VISIT },
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_THIRD_VISIT },
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_FOURTH_VISIT },
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_FIFTH_VISIT },
      { examParam: AppParams.RANDOM_BLOOD_SUGAR, freqParam: AppParams.FREQ_PARAM_SIXTH_VISIT }
    ]);
  }

  patchSocioEconomicsValues() {
    this.socioEconomics.patchValue([
      { examParam: AppParams.SOCIO_ECONOMICS, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.SOCIO_ECONOMICS, freqParam: AppParams.FREQ_PARAM_THIRD_YEAR }
    ]);
  }

  patchBradenScoreValues() {
    this.bradenScore.patchValue([
      { examParam: AppParams.BRADEN_SCORE, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.BRADEN_SCORE, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR },
      { examParam: AppParams.BRADEN_SCORE, freqParam: AppParams.FREQ_PARAM_THIRD_YEAR }
    ]);
  }

  patchAdlValues() {
    this.adl.patchValue([
      { examParam: AppParams.MOV_ACT_ADL, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.MOV_ACT_ADL, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR }
    ]);
  }

  patchIadlValues() {
    this.iadl.patchValue([
      { examParam: AppParams.MOV_ACT_IADL, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.MOV_ACT_IADL, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR }
    ]);
  }


  patchTimeUpValues() {
    this.timeUp.patchValue([
      { examParam: AppParams.TIME_UP, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.TIME_UP, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR }
    ]);
  }

  patchNutritionAssessementValues() {
    this.nutritionAssessement.patchValue([
      { examParam: AppParams.NUTRITIONAL_ASSESSMENT, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.NUTRITIONAL_ASSESSMENT, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR }
    ]);
  }

  patchGaitAndBalanceValues() {
    this.gaitAndBalance.patchValue([
      { examParam: AppParams.GAIT_AND_BALANCE, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.GAIT_AND_BALANCE, freqParam: AppParams.FREQ_PARAM_FIRST_YEAR }
    ]);
  }

  patchDepressionScaleValues() {
    this.depressionScale.patchValue([
      { examParam: AppParams.DEPRESSION_SCALE, freqParam: AppParams.FREQ_PARAM_FIRST_VISIT },
      { examParam: AppParams.DEPRESSION_SCALE, freqParam: AppParams.FREQ_PARAM_SECOND_VISIT },
      { examParam: AppParams.DEPRESSION_SCALE, freqParam: AppParams.FREQ_PARAM_THIRD_VISIT }
    ]);
  }


  addVitalsFormGroup(): FormGroup {
    return this.fb.group({
      entryDate: [""],
      BPs: [""],
      BPsrunID: [""],
      BPd: [""],
      BPdrunID: [""],
      PULSE: [""],
      PULSErunID: [""],
      Temp: [""],
      TemprunID: [""],
      Wt: [""],
      WtrunID: [""],
      Ht: [""],
      HtrunID: [""]
    });
  }

  addProductButtonClick(): void {
    (<FormArray>this.patientForm.get("vitals")).push(
      this.addVitalsFormGroup()
    );
  }

  public removeCompany(index: number): void {
    const companies = this.patientForm.get("vitals") as FormArray;
    companies.removeAt(index);
  }



  saveRegister() {
    this.submitted = true;
    //this.findInvalidControls();
    if (!this.patientDetails.validateFields()) {
      Swal.fire('Alert', ' Mandatory fields cannot be empty', 'warning');
      return;
    }
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    const loginId = curUser['person'].perscode;


    let patientData = this.patientForm.value;


    let paramsData = [];
    patientData['socioEconomics'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['bradenScore'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['randomBloodSugar'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['mentalState'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['depressionScale'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['countingMoney'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['incontinence'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['adl'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['iadl'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['timeUp'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['nutritionAssessement'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });
    patientData['gaitAndBalance'].forEach(element => {
      if (element.valueNum || element.valueText)
        paramsData.push(element);
    });

    /* delete patientData['socioEconomics'];
    delete patientData['bradenScore'];
    delete patientData['randomBloodSugar'];
    delete patientData['mentalState'];
    delete patientData['depressionScale'];
    delete patientData['countingMoney'];
    delete patientData['incontinence'];
    delete patientData['adl'];
    delete patientData['iadl'];
    delete patientData['timeUp'];
    delete patientData['nutritionAssessement'];
    delete patientData['gaitAndBalance']; */

    let vitalData = [];
    patientData.vitals.forEach(element => {
      let entryDate = element.entryDate;//? moment(element.entryDate).format("DD-MMM-YYYY") : null;
      element.BPs ? vitalData.push({ "runId": element.BPsrunID, "paramValue": element.BPs, "paramid": AppParams.VITAL_BPs, "entryBy": loginId, "entryDate": entryDate }) : null;
      element.BPd ? vitalData.push({ "runId": element.BPdrunID, "paramValue": element.BPd, "paramid": AppParams.VITAL_BPd, "entryBy": loginId, "entryDate": entryDate }) : null;

      element.PULSE ? vitalData.push({ "runId": element.PULSErunID, "paramValue": element.PULSE, "paramid": AppParams.VITAL_PULSE, "entryBy": loginId, "entryDate": entryDate }) : null;
      element.Temp ? vitalData.push({ "runId": element.TemprunID, "paramValue": element.Temp, "paramid": AppParams.VITAL_TEMP, "entryBy": loginId, "entryDate": entryDate }) : null;
      element.Wt ? vitalData.push({ "runId": element.WtrunID, "paramValue": element.Wt, "paramid": AppParams.VITAL_WT, "entryBy": loginId, "entryDate": entryDate }) : null;
      element.Ht ? vitalData.push({ "runId": element.HtrunID, "paramValue": element.Ht, "paramid": AppParams.VITAL_HT, "entryBy": loginId, "entryDate": entryDate }) : null;
    });




    let createdBy;
    let createdOn;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;
    if (this.patientDetails.f.centralRegNo.value === null) {
      createdBy = loginId;
      createdOn = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
      centralRegNo = null;
    } else {
      createdBy = this.formData.createdBy;
      createdOn = this.formData.createdOn;
      modifiedBy = loginId;
      modifiedOn = this.currentDate;
      centralRegNo = this.patientDetails.f.centralRegNo.value
    }


    //death details data
    let deathDetails = null;
    if (this.alive == false) {
      deathDetails = this.deathDetails.deatDetailsForm.value;
      if (deathDetails.civillID != null) {
        if (deathDetails.createdBy == null) {
          deathDetails.createdBy = loginId;
          deathDetails.createdDate = this.currentDate;
        } else {
          deathDetails.lastModifiedBy = loginId;
          deathDetails.lastModifiedDate = this.currentDate;
        }
      } else {
        deathDetails = null;
      }
    }


    if (!this.civilIdEntryType) {
      if (this.formData) {
        if (this.formData.rgTbPatientInfo) {
          this.civilIdEntryType = this.formData.rgTbPatientInfo.civilIdEntryType;
        }
      } else {
        this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
      }
    }

    let saveData = {
      centralRegNo: centralRegNo,
      activeYn: "Y",
      patientID: this.patientDetails.f.patientId.value,
      createdBy: createdBy,
      createdOn: createdOn,
      regInst: this.f.regInst.value,
      registerType: AppUtils.REG_TYPE_ELDERELY,
      modifiedBy: modifiedBy,
      modifiedOn: modifiedOn,
      instRegDate: this.f.instRegDate.value ? moment(this.f.instRegDate.value).format("DD-MM-YYYY") : null,
      rgTbPatientInfo: {
        createdBy: createdBy,
        createdOn: createdOn,
        careGiverName: this.f.careGiverName.value,
        careGiverTel: this.f.careGiverTel.value,
        cateGiverMob: this.f.careGiverMobile.value,
        civilId: this.f.civilId.value,
        createdInstid: this.f.regInst.value,
        dob: this.f.dob.value ? moment(this.f.dob.value).format("DD-MM-YYYY") : null,  //this.f.dob.value,
        firstName: this.f.firstName.value,
        maritalStatus: this.f.maritalStatus.value,
        mobileNo: this.f.mobileNo.value,
        kinTelNo: this.f.kinTelNo.value,
        secondName: this.f.secondName.value,
        civilIdEntryType: this.civilIdEntryType,
        sex: this.f.sex.value,
        thirdName: this.f.thirdName.value,
        tribe: this.f.tribe.value,
        village: this.f.village.value,
        patientId: this.f.patientId.value
      },
      rgTbVitalSigns: vitalData,
      rgTbEldExamTrans: paramsData,
      rgTbDeathDetails: deathDetails
    }
    this.elderlyService.saveElderly(saveData).subscribe(res => {
      if (res['code'] == 0) {


        this.clear();
        Swal.fire('Saved!', 'Elderly Registry Saved successfully.', 'success');
        this.regId = res["result"];
        this.search();
      } else if (res['code'] == "3") {
        Swal.fire('Error!', res['message'], 'error');
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Elderly Registry ' + err.message, 'error')
    })

  }

  resetForm() {
    this.submitted = false;
    this.patientForm.reset();
  }

  callMpiMethod() {
    this.getdata('civilId', '', this.patientDetails.f.civilId.value, '', '');
  }
  getdata(searchby: any, regNo: any, civilId: any, patientId: any, createdInstid: any) {
    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }
    }


    if (!this.patientForm == undefined && this.patientForm.status == 'VALID') {
      this.clear();
    }



    this.elderlyService.getElderly(regNo, AppUtils.REG_TYPE_ELDERELY.toString(), civilId, patientId, createdInstid).subscribe(async res => {
      if (res['code'] == 'S0000') {
        this.formData = res['result'];
        if (this.formData['rgTbDeathDetails'] != null) {
          this.alive = false;
        } else {
          this.alive = true;
        }

        this.patientDetails.setPatientDetails(res['result']);
        this.currentCivilId = this.formData.rgTbPatientInfo['civilId'].toString();
        this.estCode = this.formData['regInst'];
        this.patientId = this.formData['patientID'];

        this.patchVisitInfoFromCentral(this.formData);

        let rgTbDeathDetails: any = this.formData['rgTbDeathDetails'];

        if (rgTbDeathDetails != null && rgTbDeathDetails != undefined) {
          this.alive = false;
          setTimeout(function () {
            this.deathDetails.setDeathDetailsResult(rgTbDeathDetails);
          }.bind(this), 3000);

        }


      }
      else if (res['code'] == "F0000" || res['code'] == "3") {
        //if record not exist in eReg DB, and qeury by Civil ID , then call fetchMpi method.
        if (searchby == 'patientId') {
          this.fetchVisitInfo(null);
        } else {
          await Swal.fire(
            '', msg, 'warning',
          ).then((result) => {
            this._sharedService.setPatientData(this.patientDetails);
            this._sharedService.fetchMpi(civilId).subscribe(civilIdEntryType => {
              this.civilIdEntryType = civilIdEntryType !== false ? civilIdEntryType : AppUtils.CIVILID_ENTERY_MANUALLY;
            });
          })
        }

      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving  details', 'error')
    })
  }


  search() {
    if (this.regId) {
      this.getdata('regNo', this.regId, '', '', '');
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
    }
  }

  fetchVisitInfobyPatientID(event) {
    this.getdata('patientId', '', '', this.patientDetails.f.patientId.value, this.patientDetails.f.regInst.value);

  }
  fetchVisitInfo(event) {
    var loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
    if (loggedInUser) {
      var inst = loggedInUser.institutes.filter(item => item.defaultYN === 'Y');
    }
    //inst[0].estCode
    // 20068

    if (this.patientDetails.f.regInst.value > 0) {
      this.masterService.findElderlyPatientVisitInfo(this.patientDetails.f.regInst.value, this.patientDetails.f.patientId.value).subscribe(response => {
        if (response["code"] === 'S0000') {
          let responsea = response["result"][0];
          this.visitInfo = responsea;
          this.patientDetails.setPatientDetailsAlShifa(this.visitInfo);
          this.patchVisitInfoFromLocal(this.visitInfo);
        } else {
          Swal.fire({
            title: 'No Data',
            text: 'data not found ! ',
            icon: 'info',
            confirmButtonText: 'Ok',
            confirmButtonColor: '#3085d6'
          });
        }
      });

    } else {
      Swal.fire({
        title: 'Warning',
        text: 'Please select the Institute ! ',
        icon: 'warning',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });
    }

  }


  patchVisitInfoFromCentral(response: any) {
    // response.rgTbVitalSigns
    let patientData = this.patientForm.value;
    let freqloop = [];

    // Vital Signs
    //let temVital <TbVitalSigns> = Array<TbVitalSigns>();
    let temVital: Array<TbVitalSigns> = new Array<TbVitalSigns>();

    response.rgTbVitalSigns.forEach(element => {
      if (temVital.filter(s => s.entryDate == element.entryDate).length == 0) {
        temVital.push(element);
      }
    });

    for (let i = 0; i <= temVital.length - 1; i++) {
      if (i > 0) {
        this.addProductButtonClick();
        patientData.vitals.push({ BPsrunID: "", BPdrunID: "", PULSErunID: "", HtrunID: "", TemprunID: "", WtrunID: "", entryDate: "", BPs: "", BPd: "", PULSE: "", Temp: "", Wt: "", Ht: "" });
      }

      patientData.vitals[i].BPsrunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_BPs && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();
      patientData.vitals[i].BPs = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_BPs && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();

      patientData.vitals[i].BPdrunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_BPd && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();
      patientData.vitals[i].BPd = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_BPd && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();

      patientData.vitals[i].PULSE = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_PULSE && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();
      patientData.vitals[i].PULSErunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_PULSE && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();

      patientData.vitals[i].TemprunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_TEMP && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();
      patientData.vitals[i].Temp = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_TEMP && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();

      patientData.vitals[i].WtrunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_WT && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();
      patientData.vitals[i].Wt = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_WT && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();

      patientData.vitals[i].HtrunID = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_HT && s.entryDate == temVital[i].entryDate).map(s => s.runId).toString();
      patientData.vitals[i].Ht = response.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_HT && s.entryDate == temVital[i].entryDate).map(s => s.paramValue).toString();
      patientData.vitals[i].entryDate = temVital[i].entryDate;
    }

    let vitals = patientData.vitals;


    // Random Blood Sugan Entry Below Every Six Months
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_SECOND_VISIT, AppParams.FREQ_PARAM_THIRD_VISIT, AppParams.FREQ_PARAM_FOURTH_VISIT, AppParams.FREQ_PARAM_FIFTH_VISIT, AppParams.FREQ_PARAM_SIXTH_VISIT];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.randomBloodSugar[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.RANDOM_BLOOD_SUGAR && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.randomBloodSugar[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.RANDOM_BLOOD_SUGAR && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.randomBloodSugar[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.RANDOM_BLOOD_SUGAR && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }


    let randomBloodSugar = patientData.randomBloodSugar

    // Mental State
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_SECOND_VISIT, AppParams.FREQ_PARAM_THIRD_VISIT];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.mentalState[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MENTAL_STATE && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.mentalState[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MENTAL_STATE && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.mentalState[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MENTAL_STATE && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let mentalState = patientData.mentalState;

    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_SECOND_VISIT, AppParams.FREQ_PARAM_THIRD_VISIT];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.depressionScale[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.DEPRESSION_SCALE && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.depressionScale[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.DEPRESSION_SCALE && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.depressionScale[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.DEPRESSION_SCALE && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let depressionScale = patientData.depressionScale;



    // Counting & Inconsistence
    patientData.countingMoney[0].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.COUNTIN_MONEY && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.runId).toString();
    patientData.countingMoney[0].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.COUNTIN_MONEY && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.valueNum).toString();
    patientData.countingMoney[0].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.COUNTIN_MONEY && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.valueText).toString();
    let countingMoney = patientData.countingMoney;

    patientData.incontinence[0].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.INCONTINENCE && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.runId).toString();
    patientData.incontinence[0].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.INCONTINENCE && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.valueNum).toString();
    patientData.incontinence[0].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.INCONTINENCE && s.freqParam == AppParams.FREQ_PARAM_FIRST_VISIT).map(s => s.valueText).toString();
    let incontinence = patientData.incontinence;



    //  Socioeconomic Enviormental Assessment
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_THIRD_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.socioEconomics[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.SOCIO_ECONOMICS && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.socioEconomics[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.SOCIO_ECONOMICS && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.socioEconomics[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.SOCIO_ECONOMICS && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let socioEconomics = patientData.socioEconomics;


    // Braden Risk of Bed Sores
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR, AppParams.FREQ_PARAM_THIRD_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.bradenScore[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.BRADEN_SCORE && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.bradenScore[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.BRADEN_SCORE && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.bradenScore[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.BRADEN_SCORE && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let bradenScore = patientData.bradenScore;
    // Movement and Activity
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.adl[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_ADL && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.adl[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_ADL && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.adl[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_ADL && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let adl = patientData.adl;
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.iadl[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_IADL && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.iadl[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_IADL && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.iadl[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.MOV_ACT_IADL && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }
    let iadl = patientData.iadl;

    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.timeUp[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.TIME_UP && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.timeUp[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.TIME_UP && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.timeUp[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.TIME_UP && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let timeUp = patientData.timeUp;

    // Nutritional Assessment MNA
    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.nutritionAssessement[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.NUTRITIONAL_ASSESSMENT && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.nutritionAssessement[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.NUTRITIONAL_ASSESSMENT && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.nutritionAssessement[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.NUTRITIONAL_ASSESSMENT && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }

    let nutritionAssessement = patientData.nutritionAssessement;

    freqloop = [AppParams.FREQ_PARAM_FIRST_VISIT, AppParams.FREQ_PARAM_FIRST_YEAR];
    for (let i = 0; i <= freqloop.length - 1; i++) {
      patientData.gaitAndBalance[i].runId = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.GAIT_AND_BALANCE && s.freqParam == freqloop[i]).map(s => s.runId).toString();
      patientData.gaitAndBalance[i].valueNum = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.GAIT_AND_BALANCE && s.freqParam == freqloop[i]).map(s => s.valueNum).toString();
      patientData.gaitAndBalance[i].valueText = response.rgTbEldExamTrans.filter(s => s.examParam == AppParams.GAIT_AND_BALANCE && s.freqParam == freqloop[i]).map(s => s.valueText).toString();
    }
    let gaitAndBalance = patientData.gaitAndBalance;


    this.patientForm.patchValue({
      vitals: vitals,
      randomBloodSugar: randomBloodSugar,
      mentalState: mentalState,
      depressionScale: depressionScale,
      countingMoney: countingMoney,
      incontinence: incontinence,
      socioEconomics: socioEconomics,
      bradenScore: bradenScore,
      adl: adl,
      iadl: iadl,
      timeUp: timeUp,
      nutritionAssessement: nutritionAssessement,
      gaitAndBalance: gaitAndBalance
    });

  }



  patchVisitInfoFromLocal(response: any) {

    let patientData = this.patientForm.value;

    // Vital Signs

    var splittedBPF6Months = response.bpF6Months.split("/", 2);

    let localF6BPs = splittedBPF6Months[0];
    let localF6BPd = splittedBPF6Months[1];

    patientData.vitals[0].BPs = localF6BPs;
    patientData.vitals[0].BPd = localF6BPd;
    patientData.vitals[0].PULSE = response.pulseF6Months;
    patientData.vitals[0].Temp = response.tempF6Months;
    patientData.vitals[0].Wt = response.wtF6Months;
    patientData.vitals[0].Ht = response.htF6Months;
    this.addProductButtonClick();

    var splittedBPS6Months = response.bpS6Months.split("/", 2);

    let localS6BPs = splittedBPS6Months[0];
    let localS6BPd = splittedBPS6Months[1];

    patientData.vitals.push({ entryDate: "", BPs: localS6BPs, BPd: localS6BPd, PULSE: response.pulseS6Months, Temp: response.tempS6Months, Wt: response.wtS6Months, Ht: response.htS6Months })
    let vitals = patientData.vitals;



    // Random Blood Sugar Entry Below Every Six Months	
    patientData.randomBloodSugar[0].valueNum = response.rbsFirstVisit;
    patientData.randomBloodSugar[1].valueNum = response.rbsSecondVisit;
    patientData.randomBloodSugar[2].valueNum = response.rbsThirdVisit;
    patientData.randomBloodSugar[3].valueNum = response.rbsFourthVisit;
    patientData.randomBloodSugar[4].valueNum = response.rbsFifthVisit;
    patientData.randomBloodSugar[5].valueNum = response.rbsSixthVisit;
    let randomBloodSugar = patientData.randomBloodSugar

    // Mental State
    patientData.mentalState[0].valueNum = response.mmmseFirstYear;
    patientData.mentalState[1].valueNum = response.mmmseSecondYear;
    patientData.mentalState[2].valueNum = response.mmmseThirdYear;
    let mentalState = patientData.mentalState;

    // Counting & Inconsistence
    patientData.incontinence[0].valueNum = response.incScrFirstVisit;
    let incontinence = patientData.incontinence;

    //Balance & Gait




    //Depression
    patientData.depressionScale[0].valueNum = response.gdFirstYear;
    patientData.depressionScale[1].valueNum = response.gdSecondYear;
    patientData.depressionScale[2].valueNum = response.gdThirdYear;
    let depressionScale = patientData.depressionScale;

    //    gaitAndBalance	20	31		  gdFirstYear
    //gaitAndBalance	20	37		  gdSecondYear


    //  Socioeconomic Enviormental Assessment
    patientData.socioEconomics[0].valueNum = response.seeaFirstVisit;
    patientData.socioEconomics[1].valueNum = response.seeaAfter3YrVisit;
    let socioEconomics = patientData.socioEconomics;

    // Braden Risk of Bed Sores
    patientData.bradenScore[0].valueNum = response.brFirstVisit;
    patientData.bradenScore[1].valueNum = response.brAfter1YrVisit;
    patientData.bradenScore[2].valueNum = response.brAfter3YrVisit;
    let bradenScore = patientData.bradenScore;

    // Movement and Activity
    patientData.adl[0].valueNum = response.adlFirstVisit;
    patientData.adl[1].valueNum = response.adlAfter1YrVisit;
    patientData.iadl[0].valueNum = response.iadlFirstVisit;
    patientData.iadl[1].valueNum = response.iadlAfter1YrVisit;
    patientData.timeUp[0].valueNum = response.tugFirstVisit;
    patientData.timeUp[1].valueNum = response.tugAfter1YrVisit;
    let adl = patientData.adl;
    let iadl = patientData.iadl;
    let timeUp = patientData.timeUp;

    // Nutritional Assessment MNA
    patientData.nutritionAssessement[0].valueNum = response.mnaFirstVisit;
    patientData.nutritionAssessement[1].valueNum = response.mnaAfter1YrV;
    let nutritionAssessement = patientData.nutritionAssessement;

    this.patientForm.patchValue({
      vitals: vitals,
      randomBloodSugar: randomBloodSugar,
      mentalState: mentalState,
      incontinence: incontinence,
      // gaitAndBalance: gaitAndBalance,
      depressionScale: depressionScale,
      socioEconomics: socioEconomics,
      bradenScore: bradenScore,
      adl: adl,
      iadl: iadl,
      timeUp: timeUp,
      nutritionAssessement: nutritionAssessement
    });



  }

  updateDeathDetails() {
    Swal.fire({
      //title: 'Death details',
      text: "Do you want to add death details?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes'
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    })


  }


  clear() {

    this.patientForm.reset();
    let patientData = this.patientForm.value;
    for (var i = patientData.vitals.length - 1; i > 0; i--) {
      this.removeCompany(i);
    }
    this.resetForm;
    this.formData = null;
    this.patientDetails.clear();

    if (this.alive == false) {
      this.deathDetails.clear();
    }

    this.ngOnInit();
  }

}
