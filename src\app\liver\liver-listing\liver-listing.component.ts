import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import * as FileSaver from 'file-saver';
import * as moment from 'moment';
import { Paginator } from 'primeng/components/paginator/paginator';
import { GridOptions } from 'ag-grid-community';

import { RegionDataModel } from '../../common/objectModels/region-model';
import { ICDList } from '../../common/objectModels/icdList-models';
import { LiverListResult } from '../../_models/liverTransplant.model';
import { LiverExportExcel } from 'src/app/_models/liver-export-excel';

import { MasterService } from '../../_services/master.service';
import { LiverService } from "../liver.service";
import { SharedService } from '../../_services/shared.service';

import * as AppCompUtils from '../../common/app.component-utils';
import * as AppUtils from '../../common/app.utils';
import * as AppParam from '../../_helpers/app-param.constants';
import * as CommonConstants from './../../_helpers/common.constants';
import { LiverExcel } from 'src/app/_models/liver-excel';

@Component({
  selector: 'app-liver-listing',
  templateUrl: './liver-listing.component.html',
  styleUrls: ['./liver-listing.component.scss'],
  providers: [LiverService],
})
export class LiverListingComponent implements OnInit {
  @ViewChild('elderlyRegPaginator', { static: false }) paginator: Paginator;
  excelCriteria: any = {};
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;

  liverSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  displayedResults: any[] = [];
  allResults: any[] = [];
  yesNo = AppCompUtils.YES_NO;
  status = AppCompUtils.STATUS_PATIENT;
  gender = AppCompUtils.GENDER;
  icdLiverList: Array<ICDList>;
  liverExcel: Array<LiverExcel> = new Array<LiverExcel>();
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  rowData: Array<LiverListResult> = new Array<LiverListResult>();
  dataList: Array<LiverExportExcel> = new Array<LiverExportExcel>();
  gridOptions: GridOptions = <GridOptions>{
    //enableColResize: true,
    pagination: false,
    //resizable: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  columnDefs: any[];

  constructor(
    private _router: Router,
    private _masterService: MasterService,
    private _liverService: LiverService,
    private _sharedService: SharedService,
    private formBuilder: FormBuilder
  ) {
    this.getMasterData();
    this.liverSearchForm = this.formBuilder.group({
      'civilId': [null],
      'centralRegNo': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'sex': [""],
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'causeLiver': [null],
      'transplant': [""],
      'bloodGroup': [""],
      'transplantReadiness': [null],
      'regType': [null],
      'dialysisDurationFrom': [null],
      'dialysisDurationTo': [null],
      startIndex: [null],
      rowsPerPage: [null]
    });

    this.columnDefs = [
      { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 125, sortable: true },
      { headerName: 'Name', field: 'fullname', minWidth: 150, sortable: true, resizable: true },
      { headerName: 'Age', field: 'age', minWidth: 100, sortable: true },
      { headerName: 'DoB', field: 'dob', minWidth: 150, sortable: true },
      { headerName: 'Gender', field: 'sex', minWidth: 100, sortable: true },
      { headerName: 'Width : Hight = BMI', field: 'bmi', minWidth: 100, sortable: true, cellRenderer: this.getCalculatedBmi },
      { headerName: 'BloodGroup', field: 'bloodGroup', minWidth: 100, sortable: true, cellRenderer: this.renderBloodGroup },
    ];
  }

  ngOnInit() {}

  trackByFn(index, item) {
    return index;
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {});

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {});

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {});

    this._masterService.getRegionsMasterFull();
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
    });

    this._masterService.getIcdLiverShortList();
    this._masterService.icdLiverShortList.subscribe((value) => {
      this.icdLiverList = value;
    });
  }

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }

  bloodGroupSelect(event: any, group?: any) {
    const selectedBloodGroups = this.liverSearchForm.get('bloodGroup') as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;

    let currentSelectedValues = selectedBloodGroups.value ? [...selectedBloodGroups.value] : [];

    const bloodGroup = this.bloodGroupList.find(group => group.value === selectedValue);
    const selectedId = bloodGroup ? bloodGroup.id : null;

    if (isChecked && selectedId && !currentSelectedValues.includes(selectedId)) {
      currentSelectedValues.push(selectedId);
    } else if (!isChecked && selectedId) {
      currentSelectedValues = currentSelectedValues.filter(id => id !== selectedId);
    }

    selectedBloodGroups.setValue(currentSelectedValues);
    this.updateDisplayedResults(currentSelectedValues);
  }

  updateDisplayedResults(selectedValues: string[]) {
    if (selectedValues.length === 0) {
      this.displayedResults = this.allResults;
    } else {
      this.displayedResults = this.allResults.filter(result => selectedValues.includes(result.bloodGroupID.toString()));
    }
  }

  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.liverSearchForm.value['regCode'] && (this.liverSearchForm.value['regCode'] != null || this.liverSearchForm.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.liverSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.liverSearchForm.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.liverSearchForm.value['regCode'] && (this.liverSearchForm.value['regCode'] != null || this.liverSearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.liverSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.liverSearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }

  public onCellDoubleClicked(event: any) {
    //console.log('centralRegno',event.data.centralRegNo)
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['liver/liverRegister'], { state: { centralRegNo: event.data.centralRegNo } });
  }

  clear(e) {
    this.liverExcel = null;
    this.liverSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;
  }

  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  exportExcel() {
    if (this.rowData && this.rowData.length > 0) {
      this.formatData(this.rowData);
    } else {
      Swal.fire('Warning!', 'No Records to export', 'warning');
    }
  }

  formatData(rowData) {
    this.liverExcel = [];
    rowData.forEach(el => {
      let calBmi = this._sharedService.calculateBMI(el.weight, el.height);
      let bmiFull = el.weight + " : " + el.height + " = " + calBmi;
      this.liverExcel.push({
        CentralRegNo: el.centralRegNo,
        civilId: el.civilId, name: el.fullname, age: el.age,
        dob: el.dob, gender: el.sex, bmi: bmiFull, bloodGroup: this.renderBloodGroup({ value: el.bloodGroup })
      });
    });
    this._sharedService.exportAsExcelFile(this.liverExcel, "Liver_Listing");
  }

  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };

  onSearch(event?: any) {
    let body = this.liverSearchForm.value;
    let pageable = {
        page: event ? event.page : 0,
        size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };
    if (body['causeLiver'] == "") {
      body['causeLiver'] = null;
    }
    if (body['dialysis'] == "") {
      body['dialysis'] = null;
    }
    if (body['sex'] == "") {
      body['sex'] = null;
    }
    if (body['transplant'] == "") {
      body['transplant'] = null;
    }
    if (body['bloodGroup'] == "") {
      body['bloodGroup'] = null;
    }
    if (body['transplantReadiness'] == "") {
      body['transplantReadiness'] = null;
    }
    body = { pageable, ...body }
    this._liverService.getLiverListing(body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result']['content'];
        this.totalRecords = res['result']['totalElements'];

        this.excelCriteria = body;
        //console.log(this.rowData);
        for (var i = 0; i < this.rowData.length; i++) {
          this.rowData[i].dialysis = this.yesNo.filter(s => s.id == this.rowData[i].dialysis).map(s => s.value).toString();
          this.rowData[i].transplant = this.yesNo.filter(s => s.id == this.rowData[i].transplant).map(s => s.value).toString();
          this.rowData[i].sex = this.gender.filter(s => s.id == this.rowData[i].sex).map(s => s.value).toString();
          let bmi = this._sharedService.calculateBMI(this.rowData[i].width, this.rowData[i].height);
          if (bmi != null)
            this.rowData[i].bmi = this.rowData[i].width + " : " + this.rowData[i].height + " = " + bmi;
        }

        this.rowData.forEach(el => {
          this.dataList.push({
            centralRegNo: el.centralRegNo, civilId: el.civilId, Name: el.fullname,
            age: el.age, dob: el.dob, gender: el.sex, bmi: el.bmi, bloodGroup: this.renderBloodGroup({ value: el.bloodGroup })
          });
        });
      } else {
        this.rowData = null;
        Swal.fire('Error!', res['message'], 'error');
      }
    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
    });
  }

  initialGetList: () => void;

  renderBloodGroup(params: any): string {
    const bloodGroupID = params.value;
    let bloodGroupLabel = '';

    switch (bloodGroupID) {
      case 1:
        bloodGroupLabel = 'A +';
        break;
      case 2:
        bloodGroupLabel = 'A -';
        break;
      case 3:
        bloodGroupLabel = 'B +';
        break;
      case 4:
        bloodGroupLabel = 'B -';
        break;
      case 5:
        bloodGroupLabel = 'O +';
        break;
      case 6:
        bloodGroupLabel = 'O -';
        break;
      case 7:
        bloodGroupLabel = 'AB +';
        break;
      case 8:
        bloodGroupLabel = 'AB -';
        break;
      default:
        bloodGroupLabel = 'Unknown Blood Group';
        break;
    }

    return bloodGroupLabel;
  }

  getCalculatedBmi = (data) => {
    if (data.data) {
      let weight = data.data.weight;
      let height = data.data.height;
      let bmi = this._sharedService.calculateBMI(weight, height);
      return bmi;
    } else {
      return '';
    }
  };
}


