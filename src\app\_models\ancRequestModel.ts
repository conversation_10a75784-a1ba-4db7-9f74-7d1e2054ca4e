import { TbPatientInfo } from "./patient-info.model";

export class ancRequestModel {

    public requestId: number;
    public statusInLastPregnancy: number;
    public statusDate: Date;
    public status: string;
    public requestedInstRemarks: string;
    public requestedInst: string;
    public requestedDate: Date;
    public civilId: number;
    public instPregnancyId: number;
    public gravida: number;
    public mensturalCycle: number;
    public mensturalLength: string;
    public mensturalDuration: string;
    public ancInstitute: number;
    public eddScan: Date;
    public congAnamoliesYN: string;
    public createdBy: number;
    public createdDate: Date;
    public modifiedDate: Date;
    public modifiedBy: number;
    public parity: number;
    public lastDeliveryDate: Date;
    public lastAbortionDate: Date;
    public lmp: Date;
    public eddCalc: Date;
    public ancNo: String;
    public downloadYn: string;
    public downloadDate: Date;




}