import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { Observable, Subject } from "rxjs";
import { formatDate } from "@angular/common";
import {
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
  FormArray,
} from "@angular/forms";
import { MasterService } from "src/app/_services/master.service";
import { GridNgSelectDataComponent } from "../../common/agGridComponents/grid-ngSelect-data.component";
import { ButtonRendererComponent } from "../../common/agGridComponents/ButtonRendererComponent";
import * as AppUtils from "../../common/app.utils";
import * as AppCompUtils from "../../common/app.component-utils";
import { DeceasedDonorService } from "../deceased-donor.service"; // Import the service
import Swal from "sweetalert2";
import * as CommonConstants from ".././../_helpers/common.constants";
import * as moment from "moment";
import {
  RgTbPARAM,
  RgVwDecDonEthicalApprvls,
  RgTbDonorBrainDeathDtlsDto,
  RgHlaTissueTypeDto,
  RgTbDonorCardiacArstDtlsDto,
  RgTbDonorScreeningDto,
  RgTbCancerSite,
  RgVwOccupationMast,
} from "src/app/_models/deceased-donor.model";
import { LabResultsComponent } from "../../_comments/lab-result-listing/lab-result-listing.component";
import { DecimalPipe } from "@angular/common";
import { SharedService } from "../../_services/shared.service";
import { ChartConfiguration, ChartOptions } from "chart.js";
import { isEditable } from "@syncfusion/ej2-angular-grids";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
@Component({
  selector: "app-deceased-donor-register",
  templateUrl: "./deceased-donor-register.component.html",
  styleUrls: ["./deceased-donor-register.component.scss"],
})
export class DeceasedDonorRegisterComponent implements OnInit {
  isLoading: boolean = false;
  crystalNumber: any;
  currentPage: number = 1; // Current page for pagination
  itemsPerPage: number = 10; // Number of rows per page
  totalPages: number = 1; // Total number of pages
  paginatedDateVitalListNew: any[] = []; // Paginated vital list
  yearRange: string = CommonConstants.YEAR_RANGE;

  currentDatePage: number = 1; // Current page for date pagination
  datesPerPage: number = 10; // Number of dates (columns) per page
  totalDatePages: number = 1; // Total number of date pages
  paginatedLabDatesNew: any[] = []; // Paginated lab dates

  showLabChart: boolean = false;

  showMedicalProcedureTable: boolean = false;
  medicalProcedureTableList: any[] = [];
  showLabResultsTable: boolean = false;
  showOtherInvestigationsTable: boolean = false;
  showVitalResultsTable: boolean = false;
  showActionsColumn: boolean = false;
  showDownload = false;
  showChart = false;
  showAddNewSection = true;

  showLabAddNewSection = true;
  showViewButton = false;
  showVitalChartButton = false;
  showVitalDownloadButton = false;

  showLabChartButton = false;
  showLabownloadButton = false;

  vitalOptions: any[] = [];
  medicalHistoryLabels = [
    { id: 16, value: "Hypertension" },
    { id: 17, value: "Diabetes" },
    { id: 18, value: "Allergy" },
    { id: 19, value: "Systemic autoimmune disease" },
  ];

  traumaToPancreasParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "P",
  };

  traumaToLiverParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "O",
  };

  usLiverParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "U",
  };

  abdomenContrast: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "C",
  };

  renalBiopsy: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "R",
  };

  pleurodesisParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "P",
  };

  evidenceAspirationParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "E",
  };

  ecgDone: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "G",
  };

  echocardiography: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "R",
  };

  kidneyParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "K",
  };

  lungParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "L",
  };

  heartParam: any = {
    donorId: null,
    paramId: null,
    paramValue: null,
    remarks: "",
    screenType: "H",
  };

  cardiacArstDtlsDto: any = {
    donorId: null,
    cardiacArstRevYn: null,
    cprDuration: null,
    remarks: "",
  };

  hlaTissueType: any = {
    runId: null,
    centralRegNo: null,
    donorId: null,
    testType: "",
    a_Test: null,
    a_1_Test: null,
    b_Test: null,
    b_1_Test: null,
    cw_Test: null,
    cw_1_Test: null,
    dr_Test: null,
    dr_1_Test: null,
    drw_Test: null,
    drw_1_Test: null,
    dq_Test: null,
    dq_1_Test: null,
    bw_Test: null,
    bw_1_Test: null,
    activeYn: null,
    createdOn: null,
    remarks: "",
  };

  vitalData: any[] = [];
  newVital: any = {
    vital: null,
    vitalDate: null,
    vitalValue: "",
    vitalRemarks: "",
  };

  brainDeathDtls: RgTbDonorBrainDeathDtlsDto = {
    dtlId: null, // Set this value if available
    donorId: null, // Set this value if available
    eventDate: null,
    admissionDate: null,
    icuStayDays: null,
    brainDeathYn: null,
    brainDeathCause: null,
    brainDeathTime: null,
    brainDeathFormYn: null,
    test1Examiner1Name: null,
    test1Examiner1Date: null,
    test1Examiner2Name: null,
    test1Examiner2Date: null,
    test2Examiner1Name: null,
    test2Examiner1Date: null,
    test2Examiner2Name: null,
    test2Examiner2Date: null,
    remarks: null,
  };

  medicalProcedureForm: FormGroup;
  medicalProcedureList: any[] = [];

  procedureList: any[];
  procedureTableList: any[];
  labList: any[];
  labTest: any;
  //centralRegNo: number = 33;
  labnewList: any[];
  labTestName: any[];
  labResultForm: FormGroup;
  procedureForm: FormGroup;
  vitalForm: FormGroup;
  deceasedDonorForm: FormGroup;
  ethicalApprovalsForm: FormGroup;
  submitted = false;

  //ethicalApprovalLabels: string[] = [];
  ethicalApprovalLabels: { id: number; value: string }[] = [];
  medicalHistoryList: { id: number; value: string }[] = [];
  occupationList: { id: number; value: string }[] = [];
  cancerCategoryList: { id: string; value: string }[] = [];
  brainDeathIcdList: { id: string; value: string }[] = [];
  initialICDList: { id: string; value: string }[] = [];

  donorLabListing: { id: number; value: string }[] = [];
  donorOtherLabListing: { id: number; value: string }[] = [];
  procedureListing: { id: number; value: string }[] = [];

  hplcList = AppCompUtils.HLPC_TEST;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  mapBloodGroupList = AppCompUtils.MAP_BLOOD_GROUP;
  dodnorRegStages = [
    "Brain Death Details",
    "Physical Examination",
    "Ethical Approvals",
    "Medical and Social History",
    "Vital Sign and Cardiorespiratory Status",
    "HLA Class Typing",
    "Hemodynamic Instability Evaluation",
    "Organ Related Investigation",
    "Lab Investigation",
    "Other Investigations",
    "Medical Procedure",
  ];
  activeStageIndex: number = 0;
  nationalityList: any[];

  medicalHistory: any = {
    hypertension: null,
    hypertensionRemarks: "",
    diabetes: null,
    diabetesRemarks: "",
    allergy: null,
    allergyRemarks: "",
    systemAutoImmune: null,
    systemAutoImmuneRemarks: "",
    comments: "",
  };

  ethicalApprovals = [
    "Patient is in the list of refusal",
    "Patient is in the national donors list",
    "A decreased guardian's consent",
    "Any medico-legal obstacle",
    "Any admimistrative obstacle",
  ];
  selectedApprovals: { [key: number]: string } = {};
  institutes: any;
  institutesFilter: any;
  institutesFull: any;
  hospitalsList: any;
  today = new Date();
  vitalList: any[] = [];
  vitalListNew: any[] = [];
  labListNew: any[] = [];
  runId: any;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");

  @ViewChild("LabResults", { static: false }) LabResults: LabResultsComponent;
  formBuilder: any;
  estCode: any;
  civilId: any;
  exDate: any;
  labResultList: any;
  labTestOptions: any;
  donorlabTestOptions: any;
  organRelatedParams: any;
  vitalDates: any[];
  vitalDatesNew: any[];
  frameworkComponents;
  labDates: any[];
  labDatesNew: any[];

  chartData: any; // Data for PrimeNG chart
  chartOptions: any; // Options for PrimeNG chart
  medicalProcedureChartData: {
    labels: any[];
    datasets: {
      label: string;
      data: any[];
      fill: boolean;
      borderColor: string;
    }[];
  };
  otherInvestigationsChartData: {
    labels: any[];
    datasets: {
      label: string;
      data: any[];
      fill: boolean;
      borderColor: string;
    }[];
  };
  showOtherInvestigationsChart: boolean;
  showMedicalProcedureChart: boolean;
  labChartData: {
    labels: any[];
    datasets: {
      label: any;
      data: number[];
      fill: boolean;
      borderColor: string;
      tension: number;
    }[];
  };
  labChartOptions: {
    responsive: boolean;
    plugins: {
      legend: { position: string };
      tooltip: { mode: string; intersect: boolean };
    };
    scales: {
      x: { display: boolean; title: { display: boolean; text: string } };
      y: { display: boolean; title: { display: boolean; text: string } };
    };
  };

  currentVitalPage: number = 1; // Current page for pagination
  itemsPerVitalPage: number = 10; // Number of rows per page
  totalVitalPages: number = 1; // Total number of pages
  paginatedVitalList: any[];
  first: any;
  loginId: any;
  callModal: any;

  constructor(
    private masterService: MasterService,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private deceasedDonorService: DeceasedDonorService,
    private _sharedService: SharedService
  ) {}

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    //this.populateMasterData();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.crystalNumber = this._sharedService.getNavigationData().crystalNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  ngOnInit() {
    this.initiateForm();
    this.loadMasterData();
    this.initializeComponent();
  }

  prepareChartData(): void {
    // Prepare datasets for each vital sign
    const datasets = this.vitalListNew.map((vital) => ({
      label: vital.name, // Name of the vital sign (e.g., HR, BP, Temp)
      data: this.vitalDatesNew.map((date) => vital.values[date] || 0), // Map values for each date
      borderColor: this.getRandomColor(), // Assign a random color for each line
      fill: false, // Do not fill under the line
      tension: 0.4, // Smooth the line
    }));

    // Set the chart data
    this.chartData = {
      labels: this.vitalDatesNew, // Dates as labels on the X-axis
      datasets: datasets, // Datasets for each vital sign
    };

    // Set the chart options
    this.chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: "top", // Position of the legend
        },
        tooltip: {
          enabled: true, // Enable tooltips
          callbacks: {
            label: (context) => {
              return `${context.dataset.label}: ${context.raw}`; // Customize tooltip labels
            },
          },
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: "Time (minutes/hours/days)", // X-axis title
          },
          ticks: {
            autoSkip: true, // Automatically skip labels if too many
            maxRotation: 45, // Rotate labels if needed
            minRotation: 0,
          },
        },
        y: {
          title: {
            display: true,
            text: "Vital Sign Values", // Y-axis title
          },
          beginAtZero: true, // Start Y-axis at 0
        },
      },
    };
  }

  toggleChart(): void {
    if (this.vitalDatesNew.length > 0 && this.vitalListNew.length > 0) {
      this.showChart = !this.showChart; // Toggle between table and chart
      this.showAddNewSection = false;
      this.showVitalResultsTable = true;

      this.prepareChartData(); // Prepare chart data when toggling
    } else {
      Swal.fire({
        icon: "warning",
        title: "Warning",
        text: "No vital signs found.",
      });
    }
  }

  prepareLabChartData(): void {
    const datasets = this.labListNew.map((lab) => ({
      label: lab.name,
      data: this.labDatesNew.map(
        (date) => parseFloat(lab.values[date]) || null
      ),
      fill: false,
      borderColor: this.getRandomColor(),
      tension: 0.1,
    }));

    this.labChartData = {
      labels: this.labDatesNew,
      datasets: datasets,
    };

    this.labChartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: "top",
        },
        tooltip: {
          mode: "index",
          intersect: false,
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: "Dates",
          },
        },
        y: {
          display: true,
          title: {
            display: true,
            text: "Values",
          },
        },
      },
    };
  }

  toggleChartLab(): void {
    this.showLabChart = !this.showLabChart; // Toggle between table and chart
    this.showLabAddNewSection = false;
    this.showLabResultsTable = true;
    this.prepareLabChartData(); // Prepare chart data when toggling
  }

  getRandomColor(): string {
    // Generate a random color for the chart lines
    const letters = "0123456789ABCDEF";
    let color = "#";
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }

  saveOrganRelatedParam(): RgTbDonorScreeningDto[] {
    const screeningParams = [
      { ...this.traumaToPancreasParam, screenType: "P" },
      { ...this.traumaToLiverParam, screenType: "O" },
      { ...this.usLiverParam, screenType: "U" },
      { ...this.abdomenContrast, screenType: "C" },
      { ...this.renalBiopsy, screenType: "R" },
      { ...this.pleurodesisParam, screenType: "P" },
      { ...this.evidenceAspirationParam, screenType: "E" },
      { ...this.ecgDone, screenType: "G" },
      { ...this.echocardiography, screenType: "R" },
      { ...this.kidneyParam, screenType: "K" },
      { ...this.lungParam, screenType: "L" },
      { ...this.heartParam, screenType: "H" },
    ];

    // Map the parameters to the RgTbDonorScreeningDto format
    const donorScreeningDtos: RgTbDonorScreeningDto[] = screeningParams.map(
      (param) => ({
        dtlId: null, // Set this if available
        donorId: null,
        paramId: param.paramId,
        paramValue: param.paramValue,
        remarks: param.remarks,
        screenType: param.screenType,
      })
    );

    return donorScreeningDtos;
  }

  saveVitals(): void {
    if (this.vitalForm.invalid) {
      console.warn("Form is invalid:", this.vitalForm.errors);
      this.vitals.markAllAsTouched();
      return;
    }

    this.showAddNewSection = true;
    const vitalData = this.vitalForm.value.vitals.map((vital) => ({
      paramId: vital.vital.id,
      paramValue: vital.vitalValue,
      readingDate: vital.vitalDate,
      remarks: vital.vitalRemarks,
    }));
  }

  saveHlaTissueType() {
    const hlaTissueTypeDto: RgHlaTissueTypeDto = {
      ...this.hlaTissueType,
    };
  }

  onMedicalHistoryChange(field: string, value: any) {
    this.medicalHistory[field] = value;
  }

  initiateEthicalApprovalsForm() {
    this.ethicalApprovalsForm = this.fb.group({
      approvals: this.fb.array(
        this.ethicalApprovals.map(() => this.fb.control(null))
      ),
      ethicalApprovalComments: ["", [Validators.maxLength(2000)]],
    });
  }

  get approvals(): FormArray {
    return this.ethicalApprovalsForm.get("approvals") as FormArray;
  }

  loadEthicalApprovals(): void {
    this.masterService.getEthicalApprovals().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.ethicalApprovalLabels = data.map((item) => ({
          id: item.id,
          value: item.value,
        }));
      }
      // Dynamically add controls for ethical approvals
      this.ethicalApprovalLabels.forEach((_, index) => {
        this.deceasedDonorForm.addControl(
          `approval_${index}`,
          this.fb.control(null)
        );
      });
    });
  }

  loadOtherDonorLabListing(): void {
    this.masterService.getDonorOtherLabListing().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.donorOtherLabListing = data.map((item) => ({
          id: item.mohTestCode,
          value: item.testName,
        }));
      }
    });
  }

  loadBrainDeathIcdList(): void {
    this.deceasedDonorService
      .getBrainDeathIcdList()
      .subscribe((data: any[]) => {
        if (data && data.length > 0) {
          this.brainDeathIcdList = data.map((item) => ({
            id: item.icd,
            value: item.disease,
          }));
        }
      });
  }

  loadInitialIcdList(): void {
    this.deceasedDonorService.getInitialIcdList().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.initialICDList = data.map((item) => ({
          id: item.icd,
          value: item.disease,
        }));
      }
    });
  }

  loadProcedureListing(): void {
    this.masterService.getProcedureListing().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.procedureListing = data.map((item) => ({
          id: item.procId,
          value: item.procName,
        }));
      }
    });
  }

  loadVitalParamMaster(): void {
    this.masterService.getVitalParam().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.vitalOptions = data.map((item) => ({
          id: item.id,
          value: item.value,
        }));
      }
    });
  }

  loadVitalData(vitalDownloadData): void {
    const vitalsArray = this.vitalForm.get("vitals") as FormArray;

    // Clear existing vitals
    while (vitalsArray.length) {
      vitalsArray.removeAt(0);
    }

    // Clear existing vital list
    this.vitalList = [];

    // Process each vital sign
    vitalDownloadData.forEach((vital) => {
      // Add BP
      if (vital.bpSystolic !== null && vital.bpDiastolic !== null) {
        this.addVitalToList({
          vital: 64,
          vitalValue: `${vital.bpSystolic}/${vital.bpDiastolic}`,
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add Temperature
      if (vital.temperature !== null) {
        this.addVitalToList({
          vital: 63,
          vitalValue: vital.temperature.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add Pulse
      if (vital.pulse !== null) {
        this.addVitalToList({
          vital: 66,
          vitalValue: vital.pulse.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add Respiration
      if (vital.respiration !== null) {
        this.addVitalToList({
          vital: 67,
          vitalValue: vital.respiration.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add SPO2
      if (vital.spo2 !== null) {
        this.addVitalToList({
          vital: 71,
          vitalValue: vital.spo2.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add CVP
      if (vital.cvp !== null) {
        this.addVitalToList({
          vital: 70,
          vitalValue: vital.cvp.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      // Add Heart Rate
      if (vital.heartRate !== null) {
        this.addVitalToList({
          vital: 68,
          vitalValue: vital.heartRate.toString(),
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }

      if (vital.bpDiastolic !== null) {
        this.addVitalToList({
          vital: 65,
          vitalValue: `${vital.bpDiastolic}/${vital.bpDiastolic}`,
          vitalDate: new Date(vital.vitalDate),
          vitalRemarks: "",
          isEditable: false,
        });
      }
    });

    this.currentVitalPage = 1;
    this.first = 0;
    this.updateVitalPagination();
  }

  private addVitalToList(vital: {
    vital: number;
    vitalValue: string;
    vitalDate: Date;
    vitalRemarks: string;
    isEditable: boolean;
  }): void {
    // Add to FormArray
    const vitalsArray = this.vitalForm.get("vitals") as FormArray;
    vitalsArray.push(
      this.fb.group({
        vital: [vital.vital],
        vitalValue: [vital.vitalValue],
        vitalDate: [vital.vitalDate],
        vitalRemarks: [vital.vitalRemarks],
        isEditable: [vital.isEditable],
      })
    );

    // Add to vitalList for table display
    this.vitalList.push({
      id: this.vitalList.length + 1,
      ...vital,
    });
  }

  loadVitalPHData(vitalDownloadData): void {
    this.deceasedDonorForm.patchValue({
      height:
        vitalDownloadData.height !== null
          ? vitalDownloadData.height.toString()
          : null,
      weight:
        vitalDownloadData.weight !== null
          ? vitalDownloadData.weight.toString()
          : null,
    });
  }

  loadRespiratoryData(respiratoryDownloadData): void {
    console.log("respiratoryDownloadData", respiratoryDownloadData);
    this.deceasedDonorForm.patchValue({
      lungPh:
        respiratoryDownloadData.ph !== null
          ? respiratoryDownloadData.ph.toString()
          : null,
      lungPao2:
        respiratoryDownloadData.pao2 !== null
          ? respiratoryDownloadData.pao2.toString()
          : null,
      lungPaco2:
        respiratoryDownloadData.paco2 !== null
          ? respiratoryDownloadData.paco2.toString()
          : null,
      lungPeep:
        respiratoryDownloadData.peep !== null
          ? respiratoryDownloadData.peep.toString()
          : null,
      lungFio2:
        respiratoryDownloadData.fio2 !== null
          ? respiratoryDownloadData.fio2.toString()
          : null,
    });
  }

  loadVitalParam(vitalData: any): void {
    if (vitalData.length > 0) {
      // Reset vitalDates and vitalList
      this.vitalDatesNew = [];
      this.vitalDates = [];
      this.vitalListNew = [
        { name: "Temperature (°C)", values: {} },
        { name: "Pulse (bpm)", values: {} },
        { name: "Respiration (breaths/min)", values: {} },
        { name: "BP Systolic (mmHg)", values: {} },
        { name: "BP Diastolic (mmHg)", values: {} },
        { name: "SpO2 (%)", values: {} },
        { name: "CVP (cmH₂O)", values: {} },
        { name: "Heart Rate (bpm)", values: {} },
      ];

      const allFormattedDates = vitalData
        .map((vital) => this.formatDate(new Date(vital.vitalDate)))
        .sort(
          (a, b) =>
            // Convert back to Date objects (implicitly via getTime) for proper chronological sorting
            new Date(a as string).getTime() - new Date(b as string).getTime()
        );

      this.vitalDatesNew = allFormattedDates;
      // --- MODIFICATION END ---

      // Populate vitalList with data from the API response
      vitalData.forEach((vital) => {
        const formattedDate = this.formatDate(new Date(vital.vitalDate));

        // Map each vital sign to its corresponding list
        this.vitalListNew.forEach((vitalItem) => {
          switch (vitalItem.name) {
            case "Temperature (°C)":
              vitalItem.values[formattedDate] = vital.temperature || null;
              break;
            case "Pulse (bpm)":
              vitalItem.values[formattedDate] = vital.pulse || null;
              break;
            case "Respiration (breaths/min)":
              vitalItem.values[formattedDate] = vital.respiration || null;
              break;
            case "BP Systolic (mmHg)":
              vitalItem.values[formattedDate] = vital.bpSystolic || null;
              break;
            case "BP Diastolic (mmHg)":
              vitalItem.values[formattedDate] = vital.bpDiastolic || null;
              break;
            case "SpO2 (%)":
              vitalItem.values[formattedDate] = vital.spo2 || null;
              break;
            case "CVP (cmH₂O)":
              vitalItem.values[formattedDate] = vital.cvp || null;
              break;
            case "Heart Rate (bpm)":
              vitalItem.values[formattedDate] = vital.heartRate || null;
              break;
          }
        });
      });

      this.currentPage = 1; // Reset to the first page
      this.updatePagination(); // Initialize pagination
    } else {
      console.warn("No valid vital data found in the API response.");
      this.vitalDatesNew = [];
      this.vitalListNew = [];
    }
  }

  loadMedicalHistoryLabels(): void {
    // Dynamically add form controls for medical history
    this.medicalHistoryLabels.forEach((item) => {
      this.deceasedDonorForm.addControl(
        `medicalHistory_${item.id}`,
        this.fb.control(null)
      );
      this.deceasedDonorForm.addControl(
        `medicalHistoryRemarks_${item.id}`,
        this.fb.control("")
      );
    });
  }

  loadMedicalHistoryData(donorScreenings: any[]): void {
    // Dynamically add controls for medical history
    this.medicalHistoryLabels.forEach((item) => {
      if (!this.deceasedDonorForm.get(`medicalHistory_${item.id}`)) {
        this.deceasedDonorForm.addControl(
          `medicalHistory_${item.id}`,
          this.fb.control(null)
        );
      }
      if (!this.deceasedDonorForm.get(`medicalHistoryRemarks_${item.id}`)) {
        this.deceasedDonorForm.addControl(
          `medicalHistoryRemarks_${item.id}`,
          this.fb.control("")
        );
      }
    });

    // Filter donorScreenings for medical history
    const medicalScreenings = donorScreenings.filter(
      (screening) => screening.screenType === "M"
    );
    if (!medicalScreenings || medicalScreenings.length === 0) {
      console.warn("No medical history data found in donorScreenings.");
      return;
    }

    // Map medical history data to form controls
    medicalScreenings.forEach((screening) => {
      const control = this.deceasedDonorForm.get(
        `medicalHistory_${screening.paramId}`
      );
      const remarksControl = this.deceasedDonorForm.get(
        `medicalHistoryRemarks_${screening.paramId}`
      );
      if (control) {
        control.setValue(screening.paramValue); // Set the value (Y/N/U)
      }
      if (remarksControl) {
        remarksControl.setValue(screening.remarks || ""); // Set remarks if available
      }
    });
  }

  loadMasterData() {
    this.loadEthicalApprovals();
    this.getNationalityList();
    this.loadMedicalHistoryLabels();
    //this.loadDonorLabListing();
    this.loadProcedureListing();
    this.loadVitalParamMaster();
    this.loadOtherDonorLabListing();

    this.loadBrainDeathIcdList();
    this.loadInitialIcdList();

    this.masterService.getOrganRelatedParam().subscribe((response) => {
      if (response && response.length > 0) {
        // Ensure the organParams group is initialized
        let organParamsGroup = this.deceasedDonorForm.get(
          "organParams"
        ) as FormGroup;
        if (!organParamsGroup) {
          organParamsGroup = this.fb.group({});
          this.deceasedDonorForm.addControl("organParams", organParamsGroup);
        }

        // Group the organ-related parameters
        this.organRelatedParams = response.reduce((acc, param) => {
          acc[param.organ] = acc[param.organ] || [];
          acc[param.organ].push(param);
          return acc;
        }, {});

        // Dynamically add form controls for each parameter
        response.forEach((param) => {
          if (!organParamsGroup.get(`param_${param.id}`)) {
            organParamsGroup.addControl(
              `param_${param.id}`,
              this.fb.control(null)
            );
          }
          if (!organParamsGroup.get(`remarks_${param.id}`)) {
            organParamsGroup.addControl(
              `remarks_${param.id}`,
              this.fb.control(null)
            );
          }
        });
      }
    });

    this.masterService.getMedicalHistory().subscribe((data: RgTbPARAM[]) => {
      if (data && data.length > 0) {
        this.medicalHistoryList = data.map((item) => ({
          id: item.id, // Assuming `item.id` exists in the API response
          value: item.value,
        }));
      }
    });

    this.masterService
      .getCancerCategory()
      .subscribe((data: RgTbCancerSite[]) => {
        if (data && data.length > 0) {
          this.cancerCategoryList = data.map((item) => ({
            id: item.icd, // Assuming `item.id` exists in the API response
            value: item.cancerSite,
          }));
        }
      });

    this.masterService
      .getOccupationMaster()
      .subscribe((data: RgVwOccupationMast[]) => {
        if (data && data.length > 0) {
          this.occupationList = data.map((item) => ({
            id: Number(item.occupationCode), // Assuming `item.id` exists in the API response
            value: item.occupationName,
          }));
        }
      });

    const hardcodedHospital = {
      estCode: 20068,
      estName: "DGIT",
    };

    this.masterService.getHospitals().subscribe((response) => {
      if (response && response["result"]) {
        this.hospitalsList = [hardcodedHospital, ...response["result"]];
      }
    });

    this.masterService.getInstitutesMasterByUserRoles().subscribe((res) => {
      if (res && res["result"]) {
        this.institutes = res["result"];
        this.institutesFilter = res["result"];
      }
    });

    this.masterService.getInstitutesMasterFull();
    this.masterService.institutesMasterFull.subscribe((value) => {
      this.institutesFull = value;
    });

    this.masterService.getLabMaster().subscribe((res) => {
      this.labTestOptions = res.result;
    });

    this.masterService.getDonorLabListing().subscribe((res) => {
      this.donorlabTestOptions = res.result;
    });
  }

  updateInstitutesList(regInst) {
    if (this.institutes) {
      this.institutesFilter = this.institutes;
      if (regInst) {
        if (this.institutes.filter((s) => s.estCode == regInst).length == 0) {
          let regEst = this.institutesFull.filter(
            (s) => s.estCode == regInst
          )[0];
          this.institutesFilter = [...this.institutesFilter, regEst];
        }
      }
    } else {
      if (regInst) {
        let regEst: any[];
        regEst = this.institutesFull.filter((s) => s.estCode == regInst)[0];
        this.institutesFilter = this.institutesFull;
        this.institutesFilter = this.institutesFull.filter(
          (s) => s.estCode == regInst
        );
      }
    }
  }

  getEstById(estCode) {
    this.masterService
      .getInstitutesMasterByEstCode(estCode)
      .subscribe((res) => {
        this.institutes = res["result"];
        this.institutesFilter = res["result"];
      });
  }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  numberOnlyDecimal(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
      return false;
    }
    return true;
  }

  changeInstitute(obj) {
    if (obj && !obj.regCode) {
      obj = this.institutes.filter((item) => item.estCode == obj)[0];
    }
  }

  get procedures(): FormArray {
    if (!this.procedureForm) {
      this.initializeProcedureForm(); // Or wherever you initialize it
    }
    return this.procedureForm.get("procedures") as FormArray;
  }

  // --- Initialize Procedure Form ---
  initializeProcedureForm(): void {
    this.procedureForm = this.fb.group({
      procedures: this.fb.array([]), // Initialize the FormArray
    });
  }

  saveProcedures(): void {
    const procedureData = this.procedureForm.value.procedures;
  }

  initializeVitalForm(): void {
    this.vitalForm = this.fb.group({
      vitals: this.fb.array(
        this.vitalList.map((vital) => this.createVitalGroup(vital))
      ),
    });
  }

  createVitalGroup(vital): FormGroup {
    return this.fb.group({
      name: [vital.name],
      value: [
        null,
        [
          Validators.required,
          this.getValidationForVital(vital.name), // Custom validation based on vital type
        ],
      ],
    });
  }

  getValidationForVital(vitalName: string) {
    switch (vitalName) {
      case "Temp (°C)":
        return Validators.compose([Validators.min(35), Validators.max(42)]);
      case "Sys BP (mmHg)":
        return Validators.compose([Validators.min(90), Validators.max(180)]);
      case "Dia BP (mmHg)":
        return Validators.compose([Validators.min(60), Validators.max(120)]);
      case "HR (bpm)":
        return Validators.compose([Validators.min(40), Validators.max(200)]);
      case "MAP (mmHg)":
        return Validators.compose([Validators.min(50), Validators.max(150)]);
      case "CVP (cmH₂O)":
        return Validators.compose([Validators.min(0), Validators.max(20)]);
      case "Diuretics":
        return Validators.compose([Validators.min(0), Validators.max(100)]);
      case "Dopamine (mcg/kg/min)":
        return Validators.compose([Validators.min(0), Validators.max(50)]);
      default:
        return Validators.nullValidator;
    }
  }

  initializeMedicalProcedureForm(): void {
    this.medicalProcedureForm = this.fb.group({
      medicalProcedures: this.fb.array([]),
    });
  }

  addMedicalProcedure(): void {
    this.showMedicalProcedureTable = false;

    const medicalProceduresFormArray = this.medicalProcedureForm.get(
      "medicalProcedures"
    ) as FormArray;

    // Add a new medical procedure row to the form array
    medicalProceduresFormArray.push(
      this.fb.group({
        doneDate: [null, Validators.required], // Procedure Date
        procedure: [null, Validators.required], // Procedure Name
        result: [""],
        remarks: [""], // Optional remarks
        isEditable: [true], // Track if the row is editable
      })
    );

    // Update the medicalProcedureList for the data table
    this.medicalProcedureList = medicalProceduresFormArray.value;
    this.showActionsColumn = true;
  }

  removeMedicalProcedure(row: any): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        const rowIndex = this.medicalProcedureList.indexOf(row);
        if (rowIndex !== -1) {
          this.medicalProcedureList.splice(rowIndex, 1);
          this.medicalProcedureTableList.splice(rowIndex, 1);

          if (this.medicalProcedureTableList.length == 0) {
            this.showMedicalProcedureTable = false;
          }

          const medicalProceduresFormArray = this.medicalProcedureForm.get(
            "medicalProcedures"
          ) as FormArray;
          medicalProceduresFormArray.removeAt(rowIndex);

          Swal.fire(
            "Deleted!",
            "Medical Procedure has been deleted.",
            "success"
          );
        } else {
          Swal.fire(
            "Error!",
            "Medical Procedure not found in the list.",
            "error"
          );
        }
      }
    });
  }

  onRowEditMedicalProcedureInit(row: any): void {
    row.isEditable = true;
  }

  onRowEditMedicalProcedureSave(row: any): void {
    const rowIndex = this.medicalProcedureList.indexOf(row);
    this.medicalProcedureList[rowIndex] =
      this.medicalProcedureForm.value.medicalProcedures[rowIndex];
    const data = this.medicalProcedureList[rowIndex];

    // check if required fields are filled
    if (data.doneDate && data.procedure) {
      data.isEditable = false; // Set isEditable to false after saving
    } else {
      Swal.fire("Error!", "Please fill in the required fields.", "error");
      return;
    }

    data.isEditable = false;
  }

  saveMedicalProcedures(): void {
    if (this.medicalProcedureForm.invalid) {
      console.warn(
        "Medical Procedure Form is invalid:",
        this.medicalProcedureForm.errors
      );
      this.medicalProcedures.markAllAsTouched(); // Mark all fields as touched to show validation errors
      return null;
    }

    const medicalProcedureData =
      this.medicalProcedureForm.value.medicalProcedures.map((procedure) => ({
        doneDate: procedure.doneDate,
        procId: procedure.procedure, // Map procedure to paramId
        result: procedure.result, // Map result
        remarks: procedure.remarks, // Map remarks
      }));

    return medicalProcedureData; // Return the data for further processing
  }

  loadMedicalProcedures(medicalProcedures: any[]): void {
    const medicalProceduresFormArray = this.medicalProcedureForm.get(
      "medicalProcedures"
    ) as FormArray;

    // Clear existing rows in the form array
    medicalProceduresFormArray.clear();

    // Populate the form array with medical procedures data
    medicalProcedures.forEach((procedure) => {
      const matchedItem = this.procedureListing.find(
        (item) => String(item.id) === String(procedure.procId)
      );
      const paramId = matchedItem ? matchedItem.id : null; // Handle undefined case

      medicalProceduresFormArray.push(
        this.fb.group({
          doneDate: [
            procedure.doneDate ? new Date(procedure.doneDate) : null,
            Validators.required,
          ], // Convert doneDate to Date object
          procedure: [paramId, Validators.required], // Map procedure to paramId from medicalHistoryList
          result: [procedure.result || ""], // Map result
          remarks: [procedure.remarks || ""], // Map remarks
          isEditable: [false],
        })
      );
    });

    // Update the medicalProcedureList for the data table
    this.medicalProcedureList = medicalProceduresFormArray.value;

    // console.log("Loaded Medical Procedures:", this.medicalProcedureList);
  }

  loadDownloadedMedicalProcedures(medicalProcedures: any[]): void {
    const medicalProceduresFormArray = this.medicalProcedureForm.get(
      "medicalProcedures"
    ) as FormArray;

    medicalProcedures.forEach((procedure) => {
      const matchedItem = this.procedureListing.find(
        (item) => String(item.id) === String(procedure.procedureId)
      );
      if (matchedItem) {
        const paramId = matchedItem.id; // Use the matched item's ID

        medicalProceduresFormArray.push(
          this.fb.group({
            doneDate: [
              procedure.reportDate ? new Date(procedure.reportDate) : null,
              Validators.required,
            ], // Convert doneDate to Date object
            procedure: [paramId, Validators.required], // Map procedure to paramId from medicalHistoryList
            remarks: [procedure.report || ""], // Map remarks
            isEditable: [false],
          })
        );
      }
    });

    // Update the medicalProcedureList for the data table
    this.medicalProcedureList = medicalProceduresFormArray.value;
  }

  loadDownloadedMedicalTableProcedures(medicalProcedures: any[]): void {
    const medicalProceduresFormArray = this.fb.array([]);

    medicalProcedures.forEach((procedure) => {
      const matchedItem = this.procedureListing.find(
        (item) => String(item.id) === String(procedure.procedureId)
      );
      if (matchedItem) {
        const paramId = matchedItem.value; // Use the matched item's ID

        medicalProceduresFormArray.push(
          this.fb.group({
            doneDate: [
              procedure.reportDate ? new Date(procedure.reportDate) : null,
              Validators.required,
            ], // Convert doneDate to Date object
            procedure: [paramId, Validators.required], // Map procedure to paramId from medicalHistoryList
            remarks: [procedure.report || ""], // Map remarks
          })
        );
      }
    });

    this.medicalProcedureTableList = medicalProceduresFormArray.value;
  }

  get medicalProcedures(): FormArray {
    return this.medicalProcedureForm.get("medicalProcedures") as FormArray;
  }
  initiateForm() {
    (this.labResultForm = this.fb.group({
      rgTbLabTests: this.fb.array([]),
    })),
      (this.procedureForm = this.fb.group({
        procedures: this.fb.array([]), // Initialize the FormArray
      }));

    this.initializeVitalForm(); // Initialize the vital form
    this.initializeMedicalProcedureForm();

    if (this.ethicalApprovalLabels && this.ethicalApprovalLabels.length > 0) {
      this.ethicalApprovalLabels.forEach((_, index) => {
        this.deceasedDonorForm.addControl(
          `approval_${index}`,
          this.fb.control(null)
        );
      });
    }

    this.deceasedDonorForm = this.fb.group({
      organParams: this.fb.group({}),
      donorId: [null],
      crystalNo: [null],
      civilId: ["", Validators.required],
      fullName: ["", Validators.required],
      instCode: ["", Validators.required],
      instCode_2: ["", null],
      instPatientId: [null],
      sex: ["", [Validators.maxLength(1)]],
      dob: [null],
      maritalStatus: ["", [Validators.maxLength(1)]],
      nationality: [null],
      occupation: [null],
      bloodGroup: ["", [Validators.maxLength(5)]],
      initialDiag: ["", [Validators.maxLength(10)]],
      donorType: ["", [Validators.maxLength(1)]],
      organCoordinatorName: ["", [Validators.maxLength(50)]],
      height: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      weight: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      bmi: [null, [Validators.pattern(/^\d{1,2}(\.\d{1,2})?$/)]],
      remarks: ["", [Validators.maxLength(2000)]],
      telNo: [null],
      address: ["", [Validators.maxLength(500)]],
      createdDate: [null],
      createdBy: [null],
      modifiedDate: [null],
      modifiedBy: [null],
      skinInspectionYn: ["", [Validators.maxLength(1)]],
      skinInspectionDtls: ["", [Validators.maxLength(1000)]],
      palpationYn: ["", [Validators.maxLength(1)]],
      palpationDtls: ["", [Validators.maxLength(1000)]],
      physicalExamComments: ["", [Validators.maxLength(2000)]],
      ethicalApprovalComments: ["", [Validators.maxLength(2000)]],
      cancerYn: ["", [Validators.maxLength(1)]],
      cancerSite: [{ value: "", disabled: true }, [Validators.maxLength(100)]],
      histology: [{ value: "", disabled: true }, [Validators.maxLength(100)]],
      cancerMetastasisYn: [
        { value: "", disabled: true },
        [Validators.maxLength(1)],
      ],
      cancerDiagDate: [{ value: "", disabled: true }],
      cancerTreatmentDtls: [
        { value: "", disabled: true },
        [Validators.maxLength(2000)],
      ],
      medSocHistoryComments: ["", [Validators.maxLength(2000)]],
      ventilationDt: [null],
      transfusionYn: ["", [Validators.maxLength(1)]],
      prbc: [null],
      platelet: [null],
      ffp: [null],
      albumin: [null],
      hemodynamicComments: ["", [Validators.maxLength(2000)]],
      lungPh: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      lungPao2: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      lungPaco2: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      lungPeep: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      lungFio2: [null, [Validators.pattern(/^\d{1,3}(\.\d{1,2})?$/)]],
      liverComments: ["", [Validators.maxLength(2000)]],
      kidneyComments: ["", [Validators.maxLength(2000)]],
      lungComments: ["", [Validators.maxLength(2000)]],
      heartComments: ["", [Validators.maxLength(2000)]],
      exDate: ["", Validators.required],
      eventDate: [null],
      admissionDate: [new Date()],
      icuStayDays: [null],
      brainDeathYn: [null],
      brainDeathCause: [""],
      brainDeathTime: [null],
      brainDeathFormYn: [null],
      test1Examiner1Name: [""],
      test1Examiner1Date: [null],
      test1Examiner2Name: [""],
      test1Examiner2Date: [null],
      test2Examiner1Name: [""],
      test2Examiner1Date: [null],
      test2Examiner2Name: [""],
      test2Examiner2Date: [null],
      brainDeathremarks: [null],
    });

    // Subscribe to cancerYn changes
    this.deceasedDonorForm.get("cancerYn").valueChanges.subscribe((value) => {
      const cancerControls = [
        "cancerSite",
        "histology",
        "cancerMetastasisYn",
        "cancerDiagDate",
        "cancerTreatmentDtls",
      ];

      cancerControls.forEach((controlName) => {
        const control = this.deceasedDonorForm.get(controlName);
        if (value === "Y") {
          control.enable();
        } else {
          control.disable();
          control.setValue(null);
        }
      });
    });

    rgTbLabTests: this.fb.group({
      instCode: ["", Validators.compose([Validators.required])],
      testDate: ["", Validators.compose([Validators.required])],
      resultSummary: ["", Validators.compose([Validators.required])],
      mohTestCode: ["", Validators.compose([Validators.required])],
      runId: ["", Validators.compose([Validators.required])],
    });
  }

  bmiDetails(width: any, hight: any) {
    this.deceasedDonorForm.patchValue({
      weight: width,
      height: hight,
    });

    let bmi = this._sharedService.calculateBMI(width, hight);
    this.deceasedDonorForm.patchValue({
      bmi: this._decimalPipe.transform(bmi, "1.2-2"),
    });
  }
  getBmi() {
    let w = this.deceasedDonorForm.get("weight").value;
    let h = this.deceasedDonorForm.get("height").value;
    this.bmiDetails(w, h);
  }

  selectStage(index: number): void {
    this.activeStageIndex = index;
  }

  getNationalityList(natCode: any = 0) {
    this.masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationalityList = response.result;
      },
      (error) => {}
    );
  }

  save(): void {
    this.submitted = true;

    //Validate the forms
    if (this.deceasedDonorForm.invalid) {
      Swal.fire("Error", "Please fill all required fields.", "error");
      return;
    }

    this.isLoading = true;

    const deceasedDonorData = this.deceasedDonorForm.value;

    console.log("Deceased Donor Data:", deceasedDonorData);

    deceasedDonorData.civilId = this.civilId;
    //const vitalData = this.vitalForm.value.vitals;
    const procedureData = this.procedureForm.value.procedures;

    if (deceasedDonorData.occupation) {
      const matchedOccupation = this.occupationList.find(
        (item) => item.id === deceasedDonorData.occupation
      );
      const occupationValue = matchedOccupation ? matchedOccupation.id : null;
      deceasedDonorData.occupation = occupationValue;
    }

    if (deceasedDonorData.bloodGroup) {
      const bloodGroupValue =
        this.bloodGroupList.find(
          (item) => item.id === deceasedDonorData.bloodGroup
        ).id || null;
      deceasedDonorData.bloodGroup = bloodGroupValue;
    }

    const vitalDataDOnor = this.saveVitalData();
    if (vitalDataDOnor === null) {
      this.isLoading = false;
      Swal.fire(
        "Error",
        "Please fill all required fields in the vital sign form.",
        "error"
      );
      return;
    }

    const donorProcedureDtlsData = this.saveMedicalProcedures();
    if (donorProcedureDtlsData === null) {
      this.isLoading = false;
      Swal.fire(
        "Error",
        "Please fill all required fields in the Medical Procedure form.",
        "error"
      );
      return;
    }

    // Combine all data into a single payload
    const payload = {
      rgTbDeceasedDonorDto: deceasedDonorData,
      brainDeathDtls: this.saveBrainDeathDetails(),
      cardiacArstDtls: this.cardiacArstDtlsDto,
      hlaTissueTypeDto: this.hlaTissueType,

      donorVitals: vitalDataDOnor,
      //Lab Test Details
      donorLabDtls: this.labResultForm.value.rgTbLabTests.map((lab) => ({
        mohTestCode: lab.mohTestCode, // MOH Test Code
        result: lab.result, // Result
        value: lab.value,
        unit: lab.unit,
        testDate: lab.testDate, // Release Date
        remarks: lab.remarks, // Remarks
      })),
      // Other investigation Details
      findingsSummarys: procedureData.map((procedure) => ({
        findDate: procedure.doneDate,
        paramId: procedure.procedure,
        summary: procedure.result,
        remarks: procedure.remarks,
      })),
      donorScreenings: this.saveScreeningDetails(),
      //
      donorProcedureDtls: donorProcedureDtlsData,
    };

    this.deceasedDonorService.saveDeceasedDonor(payload).subscribe(
      // if success load the data

      (response) => {
        this.isLoading = false;
        Swal.fire(
          "Success",
          "Deceased donor data saved successfully.",
          "success"
        );
        if (response.result) {
          this.getData("", "", response.result);
        }
      },
      (error) => {
        this.isLoading = false;
        Swal.fire("Error", "Failed to save deceased donor data.", "error");
      }
    );
  }

  getMedicalHistoryName(id: number): string {
    const item = this.medicalHistoryList.find((history) => history.id === id);
    return item ? item.value : "";
  }

  getProcedureListName(id: number): string {
    const item = this.procedureListing.find((procedure) => procedure.id === id);
    return item ? item.value : "";
  }

  getOtherDonorLabListName(id: number): string {
    const item = this.donorOtherLabListing.find(
      (procedure) => Number(procedure.id) === Number(id)
    );
    return item ? item.value : "";
  }

  getDonorLabListName(mohTestCode: number): string {
    //console.log("mohTestCode", mohTestCode);
    const item = this.donorlabTestOptions.find(
      (lab) => Number(lab.mohTestCode) === Number(mohTestCode)
    );
    //console.log("item", item);
    return item ? item.testName : "";
  }

  saveVitalData(): any[] | null {
    if (this.vitalForm.invalid) {
      console.warn("Vital form is invalid:", this.vitalForm.errors);
      this.vitals.markAllAsTouched(); // Mark all fields as touched to show validation errors

      return null;
    }

    // Filter out invalid or null values
    const validVitalData = this.vitalForm.value.vitals.filter((vital) => {
      return (
        vital.vital !== null &&
        vital.vitalValue !== null &&
        vital.vitalDate !== null
      );
    });

    // if (validVitalData.length === 0) {
    //   console.warn("No valid vital data to save.");
    //   return null;
    // }

    const vitalData = validVitalData.map((vital) => ({
      paramId: vital.vital, // Map vital ID
      paramValue: vital.vitalValue, // Map vital value
      readingDate: vital.vitalDate, // Map vital date
      remarks: vital.vitalRemarks, // Map vital remarks
    }));

    return vitalData;
  }

  onCancel() {
    this.deceasedDonorForm.reset();
    this.ethicalApprovalsForm.reset();
    this.submitted = false;
  }

  search() {
    //const crystalNo = this.deceasedDonorForm.value.crystalNo;
    if (this.crystalNumber) {
      setTimeout(() => {
        this.getData("crystalNo", this.crystalNumber, "");
        this.crystalNumber = "";
      }, 1000);
    } else {
      this.crystalNumber = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Crystal No",
      });
    }
  }

  getBrainDeathDetails() {
    if (!this.civilId) {
      Swal.fire({
        icon: "warning",
        title: "Please enter Civil ID",
      });
      return;
    }

    this.deceasedDonorService
      .getDeceasedDonorBrainDeathDetails(this.civilId)
      .subscribe((res) => {
        if (res["code"] == "S0000") {
          let tableData = res["result"];
          //console.log("tableData", tableData);
          // START HERE
          if (tableData) {
            tableData.forEach((ele) => {
              let detail = {
                tranId: ele.tranId,
                examinationType: ele.examinationType,
                firstExamDate: ele.firstExamDate,
                firstExamName: ele.firstExamName,
                firstExamByName: ele.firstExamByName,

                secondExamDate: ele.secondExamDate,
                secondExamName: ele.secondExamName,
                secondExamByName: ele.secondExamByName,
              };

              //  console.log("detail", detail);
              if (detail.examinationType == AppUtils.FIRST_EXAMINATION) {
                this.deceasedDonorForm.patchValue({
                  test1Examiner1Name: detail.firstExamByName,
                  test1Examiner1Date:
                    detail.firstExamDate != null
                      ? new Date(detail.firstExamDate)
                      : null,
                  test1Examiner2Name: detail.secondExamByName,
                  test1Examiner2Date:
                    detail.secondExamDate != null
                      ? new Date(detail.secondExamDate)
                      : null,
                });
              }
              if (detail.examinationType == AppUtils.SECOND_EXAMINATION) {
                this.deceasedDonorForm.patchValue({
                  test2Examiner1Name: detail.firstExamByName,
                  test2Examiner1Date:
                    detail.firstExamDate != null
                      ? new Date(detail.firstExamDate)
                      : null,
                  test2Examiner2Name: detail.secondExamByName,
                  test2Examiner2Date:
                    detail.secondExamDate != null
                      ? new Date(detail.secondExamDate)
                      : null,
                });
              }
            });
          }
        } else {
          Swal.fire(
            "",
            "No Brain Death Details Notification Found.",
            "warning"
          );
        }
      });
  }

  getData(searchBy: any, crystalNo: any, civilId: any) {
    //console.log("searchBy", searchBy);
    //console.log("crystalNo", crystalNo);
    //console.log("civilId", civilId);

    let msg;
    let callMPI = true;
    if (searchBy == AppUtils.CALLTYPE_BY_CRYSTALNO) {
      if (!this.deceasedDonorForm.controls["crystalNo"].value) {
        callMPI = false;
        msg = "No Record Found in the registry with the requested crystal no.";
      }
    } else if (searchBy == AppUtils.CALLTYPE_BY_CIVILID) {
      if (
        !(
          this.deceasedDonorForm.controls["civilId"].value &&
          this.deceasedDonorForm.controls["exDate"].value
        )
      ) {
        callMPI = false;
        msg =
          "No Record Found in the registry..</br></br> Please enter civil id and date of birth to fetch Demographic information from ROP.";
      } else {
        msg =
          "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.";
      }
    } else {
      callMPI = false;
    }

    this.deceasedDonorService
      .getDeceasedDonorRegistry(crystalNo, civilId)
      .subscribe(
        (res) => {
          if (res["code"] == "S0000") {
            // get data
            this.submitted = false;
            this.showViewButton = true;
            this.showDownload = true;
            this.showVitalDownloadButton = true;
            this.showLabownloadButton = true;
            //console.log("--decesaedDonorRegistry--", res);

            const donorData = res["result"].rgTbDeceasedDonorDto;

            this.civilId = donorData.civilId;

            //this.estCode = 20068;
            //this.estCode=donorData.instCode;

            // console.log("donorData", donorData);

            this.deceasedDonorForm.controls["civilId"].disable();
            this.deceasedDonorForm.controls["exDate"].disable();
            //this.civilId = donorData["civilId"];

            const brainDeathDto = res["result"].brainDeathDtls;

            if (brainDeathDto) {
              this.setBrainDetailsToForm(brainDeathDto);
            }

            if (donorData.histology !== null) {
              donorData.histology = Number(donorData.histology);
            }

            //Transform the dob field to a Date object
            if (donorData.dob) {
              donorData.dob = new Date(donorData.dob);
            }
            if (donorData.cancerDiagDate) {
              donorData.cancerDiagDate = new Date(donorData.cancerDiagDate);
            }
            if (donorData.ventilationDt) {
              donorData.ventilationDt = new Date(donorData.ventilationDt);
            }

            if (res["result"].hlaTissueTypeDto) {
              this.setHLAData(res["result"].hlaTissueTypeDto);
            }

            if (res["result"].cardiacArstDtls) {
              this.setCardiacArrestDetails(res["result"].cardiacArstDtls);
            }

            if (
              res["result"].donorVitals != null &&
              res["result"].donorVitals.length > 0
            ) {
              this.loadVitals(res["result"].donorVitals);
              this.loadVitalsdata(res["result"].donorVitals);
              this.showVitalChartButton = true;
            }

            // Load procedures
            if (
              res["result"].findingsSummarys != null &&
              res["result"].findingsSummarys.length > 0
            ) {
              this.loadFindingSummary(res["result"].findingsSummarys);
            }

            // Load lab results
            if (
              res["result"].donorLabDtls != null &&
              res["result"].donorLabDtls.length > 0
            ) {
              this.loadLabResults(res["result"].donorLabDtls);
              this.loadLabResultsParam(res["result"].donorLabDtls);
              this.showLabChartButton = true;
            }

            //Load Medical Procedures
            if (
              res["result"].donorProcedureDtls != null &&
              res["result"].donorProcedureDtls.length > 0
            ) {
              this.showActionsColumn = true;
              this.loadMedicalProcedures(res["result"].donorProcedureDtls);
            }

            if (
              res["result"].donorScreenings != null &&
              res["result"].donorScreenings.length > 0
            ) {
              this.loadDonorScreenings(res["result"].donorScreenings);
              this.loadEthicalApprovalData(res["result"].donorScreenings);
              this.loadMedicalHistoryData(res["result"].donorScreenings);
            }

            this.deceasedDonorForm.patchValue(donorData);
          } else if (res["code"] == "ER-404") {
            Swal.fire("", msg, "warning").then((result) => {
              if (callMPI == true) {
                // console.log("calling MPI..");
                this.fetchMpi();
              }
            });
          } else if (res["code"] == "CR-404") {
            Swal.fire("", msg, "warning");
          } else if (res["code"] == "400") {
            Swal.fire("", msg, "warning");
          } else if (res["code"] == "F0000") {
            Swal.fire("", msg, "warning").then((result) => {
              if (callMPI == true) {
                // console.log("calling MPI..");
                this.fetchMpi();
              }
            });
          } else if (res["code"] == "3") {
            Swal.fire(
              "",
              "Error occurred while retrieving user details",
              "error"
            );
          }
        },
        (error) => {
          if (error.status == 401)
            Swal.fire(
              "",
              "Error occurred while retrieving user details",
              "error"
            );
        }
      );
  }

  setCardiacArrestDetails(data: any) {
    this.cardiacArstDtlsDto = data;
    this.deceasedDonorForm.patchValue({
      cardiacArstRevYn: this.cardiacArstDtlsDto.cardiacArstRevYn,
      cprDuration: this.cardiacArstDtlsDto.cprDuration,
      remarks: this.cardiacArstDtlsDto.remarks,
    });
  }

  loadFindingSummary(procedures: any[]): void {
    const proceduresFormArray = this.procedureForm.get(
      "procedures"
    ) as FormArray;

    // Clear existing rows in the form array
    proceduresFormArray.clear();

    // Populate the form array with procedures data
    procedures.forEach((procedure) => {
      const matchedItem = this.donorOtherLabListing.find(
        (item) => String(item.id) === String(procedure.paramId)
      );
      const paramId = matchedItem ? matchedItem.id : null; // Handle undefined case

      proceduresFormArray.push(
        this.fb.group({
          doneDate: [procedure.findDate ? new Date(procedure.findDate) : null], // Convert doneDate to Date object
          procedure: [Number(paramId)], // Map procedure to paramId from medicalHistoryList
          result: [procedure.summary], // Map findings
          remarks: [procedure.remarks], // Map remarks
          isEditable: [false],
        })
      );
    });

    // Update the procedureList for the data table
    this.procedureList = proceduresFormArray.value;
  }

  loadOtherlabResult(labResult: any[]): void {
    const proceduresFormArray = this.procedureForm.get(
      "procedures"
    ) as FormArray;

    labResult.forEach((res) => {
      const matchedItem = this.donorOtherLabListing.find(
        (item) => String(item.id) === String(res.mohTestCode)
      );
      const paramId = matchedItem ? matchedItem.id : null; // Handle undefined case

      // for testing purposes
      if (res.testDate == null) {
        // convert test date to todays date
        res.testDate = new Date();
      }

      if (paramId != null && res.testDate != null) {
        proceduresFormArray.push(
          this.fb.group({
            doneDate: [res.testDate ? new Date(res.testDate) : null], // Convert doneDate to Date object
            // convert paramId to number
            procedure: [paramId], // Map procedure to paramId from medicalHistoryList
            remarks: [null], // Map remarks
            result: [res.resultSummary], // Map findings
            isEditable: [false],
          })
        );
      }
    });

    this.procedureList = proceduresFormArray.value;
  }

  loadOtherlabTableResult(labResult: any[]): void {
    const tempProcedureTableList = this.fb.array([]);
    labResult.forEach((res) => {
      const matchedItem = this.donorOtherLabListing.find(
        (item) => String(item.id) === String(res.mohTestCode)
      );
      const paramId = matchedItem ? matchedItem.value : null; // Handle undefined case

      // for testing purposes
      if (res.testDate == null) {
        // convert test date to todays date
        res.testDate = new Date();
      }

      if (paramId != null && res.testDate != null) {
        tempProcedureTableList.push(
          this.fb.group({
            doneDate: [res.testDate ? new Date(res.testDate) : null], // Convert doneDate to Date object
            procedure: [paramId], // Map procedure to paramId from medicalHistoryList
            remarks: [null], // Map remarks
            result: [res.resultSummary], // Map findings
            isEditable: [false],
          })
        );
      }
    });

    this.procedureTableList = tempProcedureTableList.value;

    console.log("Other Lab Results:", this.procedureTableList);
  }

  loadLabResults(labResults: any[]): void {
    const labResultsFormArray = this.labResultForm.get(
      "rgTbLabTests"
    ) as FormArray;

    // Clear existing rows in the form array
    labResultsFormArray.clear();

    labResults.forEach((lab) => {
      const matchedItem = this.donorlabTestOptions.find(
        (item) => String(item.mohTestCode) === String(lab.mohTestCode)
      );
      const paramId = matchedItem ? matchedItem.mohTestCode : null; // Handle undefined case

      if (lab.value != null) {
        labResultsFormArray.push(
          this.fb.group({
            mohTestCode: paramId, // MOH Test Code
            result: [lab.result], // Result
            testDate: [lab.testDate ? new Date(lab.testDate) : null], // Convert releaseDate to Date object
            // releaseDate: [lab.testDate ? new Date(lab.testDate) : null], // Convert releaseDate to Date object
            remarks: [lab.remarks], // Remarks
            value: [lab.value], // Remarks
            unit: [lab.unit], // Remarks
            isEditable: [false], // Set isEditable to false by default
          })
        );
      }
    });

    this.labResultList = labResultsFormArray.value;

    //this.updateDatePagination();
  }

  loadDownloadedLabResults(labResults: any[]): void {
    const labResultsFormArray = this.labResultForm.get(
      "rgTbLabTests"
    ) as FormArray;

    // Clear existing rows in the form array
    labResultsFormArray.clear();

    // Populate the form array with lab results data
    labResults.forEach((lab) => {
      const matchedItem = this.donorlabTestOptions.find(
        (item) => String(item.mohTestCode) === String(lab.mohTestCode)
      );
      const paramId = matchedItem ? matchedItem.mohTestCode : null; // Handle undefined case

      labResultsFormArray.push(
        this.fb.group({
          mohTestCode: paramId, // MOH Test Code
          result: [lab.resultSummary], // Result
          releaseDate: [lab.releasedDt ? new Date(lab.releasedDt) : null], // Convert releaseDate to Date object
          remarks: null, // Remarks
          isEditable: [false], // Set isEditable to false by default
        })
      );
    });

    // Update the labResultList for the data table
    this.labResultList = labResultsFormArray.value;

    //this.updateDatePagination();
  }

  loadVitals(donorVitals: any[]): void {
    this.showAddNewSection = false;

    if (!donorVitals || donorVitals.length === 0) {
      console.warn("No donorVitals data found.");
      this.vitalDatesNew = [];
      this.vitalListNew = this.vitalOptions.map((vital) => ({
        name: vital.value, // Use the name from vitalOptions
        values: {}, // Initialize an empty object for values
      }));
      return;
    }

    // Reset vitalDates and vitalList
    this.vitalDatesNew = [];
    this.vitalListNew = this.vitalOptions.map((vital) => ({
      name: vital.value, // Use the name from vitalOptions
      values: {}, // Initialize an empty object for values
    }));

    // Extract unique dates with time from donorVitals and format them
    const uniqueDates = Array.from(
      new Set(
        donorVitals.map((vital) => this.formatDate(new Date(vital.readingDate)))
      )
    ).sort(
      (a, b) =>
        new Date(a as string).getTime() - new Date(b as string).getTime()
    );

    this.vitalDatesNew = uniqueDates;

    // Populate vitalList with donorVitals data
    donorVitals.forEach((vital) => {
      const formattedDate = this.formatDate(new Date(vital.readingDate));
      const vitalItem = this.vitalListNew.find(
        (item) => item.name === this.getVitalNames(vital.paramId)
      );
      if (vitalItem) {
        vitalItem.values[formattedDate] = vital.paramValue || ""; // Set the value for the specific date
      }
    });

    this.updatePagination();
  }

  loadVitalsold(donorVitals: any[]): void {
    this.showAddNewSection = false;

    if (!donorVitals || donorVitals.length === 0) {
      console.warn("No donorVitals data found.");
      this.vitalDatesNew = [];
      this.vitalListNew = [
        { name: "Temp (°C)", values: {} },
        { name: "Sys BP (mmHg)", values: {} },
        { name: "Dia BP (mmHg)", values: {} },
        { name: "MAP (mmHg)", values: {} },
        { name: "HR (bpm)", values: {} },
        { name: "CVP (cmH₂O)", values: {} },
        { name: "Diuretics", values: {} },
        { name: "Dopamine (mcg/kg/min)", values: {} },
      ];
      return;
    }
    //this.showViewButton=true;
    // Reset vitalDates and vitalList
    this.vitalDatesNew = [];
    this.vitalListNew = [
      { name: "Temp (°C)", values: {} },
      { name: "Sys BP (mmHg)", values: {} },
      { name: "Dia BP (mmHg)", values: {} },
      { name: "MAP (mmHg)", values: {} },
      { name: "HR (bpm)", values: {} },
      { name: "CVP (cmH₂O)", values: {} },
      { name: "Diuretics", values: {} },
      { name: "Dopamine (mcg/kg/min)", values: {} },
    ];

    // Extract unique dates from donorVitals and format them
    const uniqueDates = Array.from(
      new Set(
        donorVitals.map((vital) => this.formatDate(new Date(vital.readingDate)))
      )
    ).sort(
      (a, b) =>
        new Date(a as string).getTime() - new Date(b as string).getTime()
    );

    this.vitalDatesNew = uniqueDates;

    // Populate vitalList with donorVitals data
    donorVitals.forEach((vital) => {
      const formattedDate = this.formatDate(new Date(vital.readingDate));
      const vitalItem = this.vitalListNew.find(
        (item) => item.name === this.getVitalNames(vital.paramId)
      );
      if (vitalItem) {
        vitalItem.values[formattedDate] = vital.paramValue || ""; // Set the value for the specific date
      }
    });
  }

  formatDateold(date: Date): string {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  formatDate(date: Date): string {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${day}-${month}-${year} ${hours}:${minutes}`; // Include time in the format
  }

  formatDateLab(date: Date): string {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${day}-${month}-${year} ${hours}:${minutes}`;
  }

  getVitalNames(paramId: number): string {
    const vital = this.vitalOptions.find((item) => item.id === paramId);
    return vital ? vital.value : "Unknown Vital";
  }

  loadVitalsdata(donorVitals: any[]): void {
    this.showAddNewSection = true;
    const vitalsFormArray = this.vitalForm.get("vitals") as FormArray;

    // Clear existing rows in the form array
    vitalsFormArray.clear();

    // Populate the form array with donorVitals data
    donorVitals.forEach((vital) => {
      vitalsFormArray.push(
        this.fb.group({
          vital: [vital.paramId], // Map paramId to vital
          vitalValue: [vital.paramValue], // Map paramValue to vitalValue
          vitalDate: [vital.readingDate ? new Date(vital.readingDate) : null], // Convert readingDate to Date object
          vitalRemarks: [vital.remarks], // Map remarks to vitalRemarks
          isEditable: [false], // Set isEditable to false by default
        })
      );
    });

    // Update the vitalList for the data table
    this.vitalList = vitalsFormArray.value;

    this.currentVitalPage = 1;
    this.first = 0;
    this.updateVitalPagination();

    //console.log("Loaded Vitals:", this.vitalList);
  }

  setHLAData(data: any) {
    this.hlaTissueType = data;
    this.deceasedDonorForm.patchValue({
      runId: this.hlaTissueType.runId,
      centralRegNo: this.hlaTissueType.centralRegNo,
      donorId: this.hlaTissueType.donorId,
      testType: this.hlaTissueType.testType,
      a_Test: this.hlaTissueType.a_Test,
      a_1_Test: this.hlaTissueType.a_1_Test,
      b_Test: this.hlaTissueType.b_Test,
      b_1_Test: this.hlaTissueType.b_1_Test,
      cw_Test: this.hlaTissueType.cw_Test,
      cw_1_Test: this.hlaTissueType.cw_1_Test,
      dr_Test: this.hlaTissueType.dr_Test,
      dr_1_Test: this.hlaTissueType.dr_1_Test,
      drw_Test: this.hlaTissueType.drw_Test,
      drw_1_Test: this.hlaTissueType.drw_1_Test,
      dq_Test: this.hlaTissueType.dq_Test,
      dq_1_Test: this.hlaTissueType.dq_1_Test,
      bw_Test: this.hlaTissueType.bw_Test,
      bw_1_Test: this.hlaTissueType.bw_1_Test,
      activeYn: this.hlaTissueType.activeYn,
      createdOn: this.hlaTissueType.createdOn,
      remarks: this.hlaTissueType.remarks,
    });
  }

  formatDateFields(data: any, dateFields: string[]): any {
    const formattedData = { ...data }; // Create a shallow copy of the data
    dateFields.forEach((field) => {
      if (formattedData[field]) {
        formattedData[field] = new Date(formattedData[field]); // Convert to Date object
      }
    });
    return formattedData;
  }

  fetchPatientInfoByCivilID() {
    const civilId = this.deceasedDonorForm.value.civilId;

    // console.log("civilId--", civilId);
    if (civilId == null || civilId == "") {
      Swal.fire("Validation Error", "CivilId is required.", "warning");
      return;
    }

    this.getData("civilId", "", this.deceasedDonorForm.value.civilId);
  }

  fetchPatientInfoByCrystalNo() {
    const crystalNo = this.deceasedDonorForm.value.crystalNo;
    //console.log("crystalNo--", crystalNo);

    if (crystalNo == null || crystalNo == "") {
      Swal.fire("Validation Error", "Crystal Number is required.", "warning");
      return;
    }
    this.getData("crystalNo", this.deceasedDonorForm.value.crystalNo, "");
  }

  setMPIDeceasedDonorDetails(data: any): void {
    // Ensure the result data is valid before setting the form fields
    if (data) {
      this.deceasedDonorForm.controls["fullName"].setValue(
        data.fullName || null
      );
      this.deceasedDonorForm.controls["dob"].setValue(
        data.dob ? new Date(data.dob) : null
      );
      this.deceasedDonorForm.controls["sex"].setValue(
        data.sex === "Male" ? "M" : "F"
      );
      this.deceasedDonorForm.controls["nationality"].setValue(
        data.nationality || null
      );
      this.deceasedDonorForm.controls["maritalStatus"].setValue(
        data.maritalStatus === "Married"
          ? "M"
          : data.maritalStatus === "Single"
          ? "S"
          : data.maritalStatus === "Divorced"
          ? "D"
          : data.maritalStatus === "Widow/Widower"
          ? "W"
          : "O"
      );

      // Example of setting another field to null
      this.deceasedDonorForm.controls["exDate"].setValue(null);

      // console.log("Updated Deceased Donor Form:", this.deceasedDonorForm.value);
    } else {
      console.error("Invalid data provided to setDeceasedDonorDetails");
    }
  }

  fetchMpi(civilIdMPI?: any, dobMPI?: any, expDateMPI?: any): Observable<any> {
    this.isLoading = true;
    const resultSubject = new Subject<any>();

    let civilId: any = !civilIdMPI
      ? this.deceasedDonorForm.value.civilId
      : civilIdMPI;
    let dob = !dobMPI ? this.deceasedDonorForm.value.dob : dobMPI;
    let expDate = !expDateMPI
      ? this.deceasedDonorForm.value.exDate
      : expDateMPI;

    if (!(civilId && (expDate || dob))) {
      Swal.fire(
        "MPI Service",
        "Please Enter the Civil ID & Expiry Date/DOB to bring the personal details from MPI",
        "warning"
      );
      resultSubject.next(false);
      resultSubject.complete();
      return resultSubject.asObservable();
    }

    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    const loginId = curUser["person"].perscode;

    if (dob && moment(dob).isValid()) {
      dob = formatDate(dob, "yyyy-MM-dd", "en");
    } else {
      dob = null;
    }

    if (expDate && moment(expDate).isValid()) {
      expDate = formatDate(expDate, "yyyy-MM-dd", "en");
    } else {
      expDate = null;
    }

    let req = {
      birthDate: dob,
      cardExpiryDate: expDate,
      civilId: civilId,
      requesterPersCode: loginId,
    };

    // console.log("req--", req);

    this.masterService.getMpiV2Details(req).subscribe(
      (response) => {
        this.isLoading = false;
        if (response != null) {
          if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            this.submitted = false;
            this.showViewButton = true;
            this.showDownload = true;
            this.showVitalDownloadButton = true;
            this.showLabownloadButton = true;
            let mpires = [];
            mpires = response["result"];
            this.deceasedDonorForm.patchValue({
              civilId: mpires["civilId"],
              fullName:
                mpires["firstNameEn"] +
                " " +
                mpires["secondNameEn"] +
                " " +
                mpires["thirdNameEn"] +
                " " +
                mpires["sixthNameEn"],
              nationality: mpires["countryID"],
              telNo: mpires["mobileNo"],
              sex: mpires["sex"] === "Male" ? "M" : "F",
              address: mpires["birthTown"],
              maritalStatus:
                mpires["maritalStatus"] === "Married"
                  ? "M"
                  : mpires["maritalStatus"] === "Single"
                  ? "S"
                  : mpires["maritalStatus"] === "Divorced"
                  ? "D"
                  : mpires["maritalStatus"] === "Widow/Widower"
                  ? "W"
                  : "O",
              dob: new Date(mpires["birthDate"]),
            });
            this.deceasedDonorForm.controls["civilId"].disable();
            this.deceasedDonorForm.controls["exDate"].disable();
            this.civilId = mpires["civilId"];
            this.exDate = mpires["exDate"];
            //this.estCode=mpires["estCode"];
          } else if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            Swal.fire(
              "Error!",
              "Error occurred while fetching civil id details </br>" +
                response["result"]["message"],
              "error"
            );
          } else {
            Swal.fire(
              "Error!",
              "Error occurred while fetching civil id details </br>" +
                response["message"],
              "error"
            );
          }
        }
      },
      (error) => {
        this.isLoading = false;
        console.error("Error while fetching MPI details:", error);
        resultSubject.error(error);
      }
    );

    return resultSubject.asObservable();
  }

  getPatientDetails(civilId) {
    if (civilId != null) {
      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      const loginId = curUser["person"].perscode;

      if (curUser) {
        var inst = curUser.institutes.filter((item) => item.defaultYN === "Y");
      }
      this.estCode = inst[0].estCode;

      let exDate = this.deceasedDonorForm.value.exDate;
      if (exDate == "") {
        exDate = null;
      } else if (exDate != null) {
        exDate = formatDate(exDate, "yyyy-MM-dd", "en");
      }
      let req = {
        birthDate: null,
        cardExpiryDate: exDate,
        civilId: this.deceasedDonorForm.value.civilId,
        //"queryType": "string",
        //"requesterCivilId": "string",
        requesterPersCode: loginId,
        // "type": 0
      };
      this.masterService.getMpiV2Details(req).subscribe((response) => {
        if (response != null) {
          if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            let mpires = [];
            mpires = response["result"];
            this.deceasedDonorForm.patchValue({
              civilId: mpires["civilId"],
              fullname:
                mpires["firstNameEn"] +
                " " +
                mpires["secondNameEn"] +
                " " +
                mpires["thirdNameEn"] +
                " " +
                mpires["sixthNameEn"],
              nationality: mpires["countryID"],
              telNo: mpires["mobileNo"],
              sex: mpires["sex"] === "Male" ? "M" : "F",
              address: mpires["birthTown"],
              dob: new Date(mpires["birthDate"]),
            });
            this.deceasedDonorForm.controls["civilId"].disable();
            this.deceasedDonorForm.controls["exDate"].disable();
            this.civilId = mpires["civilId"];
            //this.estCode=mpires["estCode"];
          } else if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            Swal.fire(
              "Error!",
              "Error occurred while fetching civil id details </br>" +
                response["result"]["message"],
              "error"
            );
          } else {
            Swal.fire(
              "Error!",
              "Error occurred while fetching civil id details </br>" +
                response["message"],
              "error"
            );
          }
        }
      });
    }
  }

  setBrainDetailsToForm(brainDeathDtls: any) {
    if (brainDeathDtls.eventDate) {
      brainDeathDtls.eventDate = new Date(brainDeathDtls.eventDate);
    }
    if (brainDeathDtls.admissionDate) {
      brainDeathDtls.admissionDate = new Date(brainDeathDtls.admissionDate);
    }
    if (brainDeathDtls.brainDeathTime) {
      brainDeathDtls.brainDeathTime = new Date(brainDeathDtls.brainDeathTime);
    }
    if (brainDeathDtls.test1Examiner1Date) {
      brainDeathDtls.test1Examiner1Date = new Date(
        brainDeathDtls.test1Examiner1Date
      );
    }
    if (brainDeathDtls.test1Examiner2Date) {
      brainDeathDtls.test1Examiner2Date = new Date(
        brainDeathDtls.test1Examiner2Date
      );
    }
    if (brainDeathDtls.test2Examiner1Date) {
      brainDeathDtls.test2Examiner1Date = new Date(
        brainDeathDtls.test2Examiner1Date
      );
    }
    if (brainDeathDtls.test2Examiner2Date) {
      brainDeathDtls.test2Examiner2Date = new Date(
        brainDeathDtls.test2Examiner2Date
      );
    }

    // Patch the data to the form
    this.deceasedDonorForm.patchValue({
      eventDate: brainDeathDtls.eventDate,
      admissionDate: brainDeathDtls.admissionDate,
      icuStayDays: brainDeathDtls.icuStayDays,
      brainDeathYn: brainDeathDtls.brainDeathYn,
      brainDeathCause: brainDeathDtls.brainDeathCause,
      brainDeathTime: brainDeathDtls.brainDeathTime,
      brainDeathFormYn: brainDeathDtls.brainDeathFormYn,
      test1Examiner1Name: brainDeathDtls.test1Examiner1Name,
      test1Examiner1Date: brainDeathDtls.test1Examiner1Date,
      test1Examiner2Name: brainDeathDtls.test1Examiner2Name,
      test1Examiner2Date: brainDeathDtls.test1Examiner2Date,
      test2Examiner1Name: brainDeathDtls.test2Examiner1Name,
      test2Examiner1Date: brainDeathDtls.test2Examiner1Date,
      test2Examiner2Name: brainDeathDtls.test2Examiner2Name,
      test2Examiner2Date: brainDeathDtls.test2Examiner2Date,
      brainDeathremarks: brainDeathDtls.remarks,
    });
  }

  setDeceasedDonorDetails(data) {
    const momentDate = moment(data.dob, "DD-MM-YYYY").toDate();
    data.dob = momentDate;

    this.deceasedDonorForm.patchValue({
      crystalNo: data.crystalNo,
      civilId: data.civilId,
      fullName: data.fullName,
      instCode: data.instCode,
      instPatientId: data.instPatientId,
      sex: data.sex,
      dob: momentDate,
      maritalStatus: data.maritalStatus,
      nationality: data.nationality,
      occupation: data.occupation,
      bloodGroup: data.bloodGroup,
      initialDiag: data.initialDiag,
      donorType: data.donorType,
      organCoordinatorName: data.organCoordinatorName,
      height: data.height,
      weight: data.weight,
      bmi: data.bmi,
      remarks: data.remarks,
      telNo: data.telNo,
      address: data.address,
      createdDate: data.createdDate,
      createdBy: data.createdBy,
      modifiedDate: data.modifiedDate,
      modifiedBy: data.modifiedBy,
      skinInspectionYn: data.skinInspectionYn,
      skinInspectionDtls: data.skinInspectionDtls,
      palpationYn: data.palpationYn,
      palpationDtls: data.palpationDtls,
      physicalExamComments: data.physicalExamComments,
      ethicalApprovalComments: data.ethicalApprovalComments,
      cancerYn: data.cancerYn,
      cancerSite: data.cancerSite,
      histology: data.histology,
      cancerMetastasisYn: data.cancerMetastasisYn,
      cancerDiagDate: data.cancerDiagDate,
      cancerTreatmentDtls: data.cancerTreatmentDtls,
      medSocHistoryComments: data.medSocHistoryComments,
      ventilationDt: data.ventilationDt,
      transfusionYn: data.transfusionYn,
      prbc: data.prbc,
      platelet: data.platelet,
      ffp: data.ffp,
      albumin: data.albumin,
      hemodynamicComments: data.hemodynamicComments,
      lungPh: data.lungPh,
      lungPao2: data.lungPao2,
      lungPaco2: data.lungPaco2,
      lungPeep: data.lungPeep,
      lungFio2: data.lungFio2,
      liverComments: data.liverComments,
      kidneyComments: data.kidneyComments,
      lungComments: data.lungComments,
      heartComments: data.heartComments,
    });
  }

  setMPIDetails(data) {
    let mpiUser = {};
    mpiUser = data;
    this.deceasedDonorForm.patchValue({
      fullName: mpiUser["firstNameEn"],

      dob: new Date(mpiUser["birthDate"]),
      sex: mpiUser["sex"] === "Male" ? "M" : "F",
      //age: this.calculateAge(new Date(mpiUser["birthDate"])),
      nationality: mpiUser["countryID"],
      maritalStatus:
        mpiUser["maritalStatus"] === "Married"
          ? "M"
          : mpiUser["maritalStatus"] === "Single"
          ? "S"
          : mpiUser["maritalStatus"] === "Divorced"
          ? "D"
          : mpiUser["maritalStatus"] === "Widow/Widower"
          ? "W"
          : "O",
      mobileNo: mpiUser["mobileNo"],
      //kinTelNo: mpiUser["kinTelNo"]
    });
    return this.deceasedDonorForm.value;
  }

  saveBrainDeathDetails() {
    const formValues = this.deceasedDonorForm.value;

    const brainDeathDetails: RgTbDonorBrainDeathDtlsDto = {
      dtlId: null, // Set this value if available
      donorId: null, // Set this value if available
      eventDate: formValues.eventDate,
      admissionDate: formValues.admissionDate,
      icuStayDays: formValues.icuStayDays,
      brainDeathYn: formValues.brainDeathYn,
      brainDeathCause: formValues.brainDeathCause,
      brainDeathTime: formValues.brainDeathTime,
      brainDeathFormYn: formValues.brainDeathFormYn,
      test1Examiner1Name: formValues.test1Examiner1Name,
      test1Examiner1Date: formValues.test1Examiner1Date,
      test1Examiner2Name: formValues.test1Examiner2Name,
      test1Examiner2Date: formValues.test1Examiner2Date,
      test2Examiner1Name: formValues.test2Examiner1Name,
      test2Examiner1Date: formValues.test2Examiner1Date,
      test2Examiner2Name: formValues.test2Examiner2Name,
      test2Examiner2Date: formValues.test2Examiner2Date,
      remarks: formValues.brainDeathremarks,
    };

    return brainDeathDetails;
  }

  onDownloadLabResults() {
    this.FetchAllDonorLabFromAlShifa();
  }

  FetchAllDonorLabFromAlShifa() {
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .fetchAllDonorLabFromShifa(this.estCode, this.civilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null && res["result"].length > 0) {
                const rgTbLabTestsDB: any = res["result"];
                for (let labRList of rgTbLabTestsDB) {
                  this.LabResults.addNewLabResult(
                    labRList.runId,
                    this._sharedService.setDateFormat(labRList.testDate),
                    labRList.mohTestCode,
                    labRList.resultSummary,
                    labRList.instCode,
                    labRList.enteredBy,
                    labRList.enteredDate,
                    labRList.source,
                    false
                  );
                }
              } else {
                Swal.fire(
                  "No Record Found",
                  "No lab results found for the given estCode and civilId.",
                  "info"
                );
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching lab results: " + error.message,
              "error"
            );
          }
        );
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorLabFromAlShifa will not be executed."
      );
    }
  }

  padLeft(text: any, padChar: string, size: number): string {
    if (!text) {
      return null;
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  getDonorDetails(crystalNo, civilId) {
    //this.clear();
    // type=='donorID'  'civilID'
    if (civilId != null) {
      //// console.log("id--" + id + "type--" + type);
      var loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
      if (loggedInUser) {
        var inst = loggedInUser.institutes.filter(
          (item) => item.defaultYN === "Y"
        );
      }
      this.estCode = inst[0].estCode;
      // // console.log("estCode--" + this.estCode);

      this.deceasedDonorService
        .getDeceasedDonorRegistry(crystalNo, civilId)
        .subscribe(async (response) => {
          // && response["result"] != null
          if (response != null) {
            if (response["code"] == "S0000") {
              //this.centralRegNo = this.patientInfoDtlview.centralRegNo;

              this.deceasedDonorForm.patchValue(response["result"]);
              this.deceasedDonorForm.controls["civilId"].disable();
              this.hlaTissueType = response["result"].hlaTissueTypeDto;
              //this.estCode=response["result"].rgTbRenalDonorDto.instCode;
              this.civilId = response["result"].rgTbDeceasedDonorDto.civilId;

              if (this.hlaTissueType) {
                this.hlaTissueType.a_Test = this.padLeft(
                  this.hlaTissueType.a_Test,
                  "0",
                  2
                );
                this.hlaTissueType.a_1_Test = this.padLeft(
                  this.hlaTissueType.a_1_Test,
                  "0",
                  2
                );
                this.hlaTissueType.b_Test = this.padLeft(
                  this.hlaTissueType.b_Test,
                  "0",
                  2
                );
                this.hlaTissueType.b_1_Test = this.padLeft(
                  this.hlaTissueType.b_1_Test,
                  "0",
                  2
                );
                this.hlaTissueType.cw_Test = this.padLeft(
                  this.hlaTissueType.cw_Test,
                  "0",
                  2
                );
                this.hlaTissueType.cw_1_Test = this.padLeft(
                  this.hlaTissueType.cw_1_Test,
                  "0",
                  2
                );
                this.hlaTissueType.dr_Test = this.padLeft(
                  this.hlaTissueType.dr_Test,
                  "0",
                  2
                );
                this.hlaTissueType.dr_1_Test = this.padLeft(
                  this.hlaTissueType.dr_1_Test,
                  "0",
                  2
                );
                this.hlaTissueType.drw_Test = this.padLeft(
                  this.hlaTissueType.drw_Test,
                  "0",
                  2
                );
                this.hlaTissueType.drw_1_Test = this.padLeft(
                  this.hlaTissueType.drw_1_Test,
                  "0",
                  2
                );
                this.hlaTissueType.dq_Test = this.padLeft(
                  this.hlaTissueType.dq_Test,
                  "0",
                  2
                );
                this.hlaTissueType.dq_1_Test = this.padLeft(
                  this.hlaTissueType.dq_1_Test,
                  "0",
                  2
                );

                this.deceasedDonorForm.patchValue(this.hlaTissueType);
              }

              this.deceasedDonorForm.controls["civilId"].disable();
              this.deceasedDonorForm.controls["exDate"].disable();

              if (response["result"].donorLabDtls) {
                const rgTbLabTestsDB: any =
                  response["result"].rgTbDonorLabTests;
                for (let labRList of rgTbLabTestsDB) {
                  this.LabResults.addNewLabResult(
                    labRList.runId,
                    this._sharedService.setDateFormat(labRList.testDate),
                    labRList.mohTestCode,
                    labRList.resultSummary,
                    labRList.instCode,
                    labRList.enteredBy,
                    labRList.enteredDate,
                    labRList.source,
                    false
                  );
                }
              }
            } else if (response["code"] == "C0001") {
              Swal.fire("warning!", response["message"], "warning");
            } else if (response["code"] == "C0002") {
              let exDate = this.deceasedDonorForm.value.exDate;
              let civil = this.deceasedDonorForm.value.civilId;

              if (
                exDate == "" ||
                exDate == null ||
                civil == "" ||
                civil == null
              ) {
                Swal.fire(
                  "warning",
                  "The entered <b>Civil ID</b> is not in the registry file, please enter <b>Civil ID</b> & <b>Expiry Date</b> to get the data from MPI",
                  "warning"
                );
              } else {
                this.getPatientDetails(civil);
              }
            } else {
              Swal.fire(
                "Error!",
                "Error occurred while fetching donor information details </br>" +
                  response["message"],
                "error"
              );
            }
          } else {
            Swal.fire("", response["message"], "error");
          }
        });
    }
  }

  get vitals(): FormArray {
    return this.vitalForm.get("vitals") as FormArray;
  }

  cancelAddNew(): void {
    this.showAddNewSection = false; // Hide the data table and show the view table

    //this.loadVitals(this.vitalList); // Load the existing data into the table
  }

  addVital(): void {
    this.showAddNewSection = true;
    this.showViewButton = true;
    this.showChart = false;
    this.showVitalResultsTable = false; // Ensure the results table is hidden

    const vitalsFormArray = this.vitalForm.get("vitals") as FormArray;

    // push on the top of the array

    vitalsFormArray.insert(
      0,
      this.fb.group({
        vital: [null, Validators.required], // Ensure the vital is selected
        vitalValue: [
          null,
          [
            Validators.required,
            // Dynamically apply validation based on the selected vital type
            (control) => {
              if (!control.parent) {
                return null; // If parent is undefined, skip validation
              }
              const selectedVital = control.parent.get("vital").value;
              if (selectedVital) {
                const validationFn = this.getValidationForVital(
                  this.getVitalName(selectedVital)
                );
                return validationFn ? validationFn(control) : null;
              }
              return null;
            },
          ],
        ],
        vitalDate: [null, Validators.required], // Date is mandatory
        vitalRemarks: [""], // Optional remarks
        isEditable: [true], // Track if the row is editable
      })
    );
    //console.log("Vital List array:", vitalsFormArray);

    this.vitalList = vitalsFormArray.value;
    this.currentVitalPage = 1;
    this.first = 0;
    this.updateVitalPagination();
  }

  // Enable editing for a row
  onRowEditInit(row: any): void {
    row.isEditable = true;
  }

  removeVital(row: any): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        // const vitalsFormArray = this.vitalForm.get('vitals') as FormArray;
        const rowIndex = this.vitalList.indexOf(row);

        //console.log("this.vitalList:", this.vitalList);

        //console.log("rowIndex:", rowIndex);
        this.vitalList.splice(rowIndex, 1);

        this.vitalListNew.splice(rowIndex, 1);

        if (this.vitalListNew.length == 0) {
          this.showVitalResultsTable = false;
          this.showVitalChartButton = false;
        }

        const vitalsFormArray = this.vitalForm.get("vitals") as FormArray;

        vitalsFormArray.removeAt(rowIndex);

        //console.log("vitalsFormArray:", vitalsFormArray);

        this.currentVitalPage = 1;
        this.first = 0;
        this.updateVitalPagination();
        Swal.fire("Deleted!", "Vital has been deleted.", "success");
      }
    });

    //console.log("vitalList:", this.vitalList);
  }

  // Save the edited row
  onRowEditSave(row: any): void {
    const rowIndex = this.vitalList.indexOf(row);
    //console.log("rowIndex:", rowIndex);
    this.vitalList[rowIndex] = this.vitalForm.value.vitals[rowIndex];
    //console.log("vitalList:", this.vitalList[rowIndex]);
    const data = this.vitalList[rowIndex];

    //console.log("data:", data);

    // check if all the required fields are filled
    if (!data.vital || !data.vitalValue || !data.vitalDate) {
      Swal.fire("Please fill all required fields.");
      return;
    }

    data.vitalName = this.vitalOptions
      .filter((s) => s.id === data.vital)
      .map((s) => s.value)[0];
    data.vitalDate = moment(data.vitalDate, "DD-MM-YYYY").format();
    data.isEditable = false;

    this.currentVitalPage = 1;
    this.first = 0;
    this.updateVitalPagination();
    // console.log("Updated Vital Row:", data);

    this.showVitalResultsTable = false;
    this.showAddNewSection = true;
  }

  // Get the name of the vital from the options
  getVitalName(id: number): string {
    const vital = this.vitalOptions.find((item) => item.id === id);
    return vital ? vital.value : "";
  }

  getVitalIds(vitalName: string): number {
    const vital = this.vitalOptions.find((item) => item.value === vitalName);
    return vital ? vital.id : 0;
  }

  addLabResult(): void {
    this.showLabAddNewSection = true; // Set the flag to true to show the section
    this.showLabChart = false; // Ensure the chart is hidden
    this.showLabResultsTable = false; // Ensure the results table is hidden

    // this.showLabChart=!this.showLabChart;

    const labResultsFormArray = this.labResultForm.get(
      "rgTbLabTests"
    ) as FormArray;
    labResultsFormArray.push(
      this.fb.group({
        mohTestCode: [null], // MOH Test Code
        result: [null], // Result
        testDate: [null], // Release Date
        value: [null], // Value
        unit: [null], // Unit
        remarks: [null], // Remarks
        isEditable: [true], // Track if the row is editable
      })
    );
    this.labResultList = labResultsFormArray.value; // Update the lab result list
  }

  // Remove a lab result row
  removeLabResult(row: any): void {
    const labResultsFormArray = this.labResultForm.get(
      "rgTbLabTests"
    ) as FormArray;
    const rowIndex = this.labResultList.indexOf(row);
    labResultsFormArray.removeAt(rowIndex);
    this.labResultList.splice(rowIndex, 1); // Update the lab result list

    // delete row from labResultListNew
    this.labListNew.splice(rowIndex, 1);

    // check if labListNew is empty
    if (this.labListNew.length == 0) {
      this.showLabResultsTable = false;
      this.showLabChartButton = false;
    }
  }

  // Enable editing for a row
  onRowEditLabInit(row: any): void {
    row.isEditable = true;
  }

  getTestName(mohTestCode: number): string {
    const test = this.labTestOptions.find(
      (item) => item.mohTestCode === mohTestCode
    );
    return test ? test.testName : "Unknown Test";
  }

  getDonorLabTestName(mohTestCode: number): string {
    const test = this.donorlabTestOptions.find(
      (item) => item.mohTestCode === mohTestCode
    );
    return test ? test.testName : "Unknown Test";
  }

  // Save the edited row
  onRowEditLabSave(row: any): void {
    const rowIndex = this.labResultList.indexOf(row);
    this.labResultList[rowIndex] =
      this.labResultForm.value.rgTbLabTests[rowIndex];
    const data = this.labResultList[rowIndex];

    //console.log("data:", data);

    // check if all the required fields are filled
    if (!data.value || !data.unit || !data.testDate) {
      Swal.fire("Please fill all required fields.");
      return;
    }

    data.testName = this.labTestOptions
      .filter((s) => s.mohTestCode === data.mohTestCode)
      .map((s) => s.testName)[0];
    data.releaseDate = moment(data.releaseDate, "DD-MM-YYYY").format();
    data.isEditable = false;

    // console.log("Updated Lab Result Row:", data);
  }

  // Enable editing for a procedure row
  onRowEditProcedureInit(row: any): void {
    row.isEditable = true;
  }

  // Save the edited procedure row
  onRowEditProcedureSave(row: any): void {
    const rowIndex = this.procedureList.indexOf(row);
    this.procedureList[rowIndex] =
      this.procedureForm.value.procedures[rowIndex];
    const data = this.procedureList[rowIndex];

    //console.log("data:", data);
    // check if all the required fields are filled
    if (!data.procedure || !data.doneDate || !data.result) {
      Swal.fire("Please fill all required fields.");
      return;
    }

    data.isEditable = false;

    // console.log("Updated Procedure Row:", data);
  }

  getValidationForTest(testCode: number) {
    // Return a validation function based on the test code
    // For example:
    switch (testCode) {
      case 1:
        return Validators.compose([Validators.min(10), Validators.max(100)]);
      case 2:
        return Validators.compose([Validators.min(5), Validators.max(50)]);
      default:
        return Validators.nullValidator;
    }
  }

  addProcedure(): void {
    this.showOtherInvestigationsTable = false;
    

    const proceduresFormArray = this.procedures;

    proceduresFormArray.push(
      this.fb.group({
        procedure: [null, Validators.required], // Ensure the test code is selected
        doneDate: [null, Validators.required], // Date is mandatory
        result: [null, Validators.required], // Date is mandatory
        remarks: [""], // Optional remarks
        isEditable: [true], // Track if the row is editable
      })
    );

    this.procedureList = proceduresFormArray.value;
  }

  removeProcedure(row: any): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        const rowIndex = this.procedureList.indexOf(row);
        if (rowIndex !== -1) {
          this.procedureList.splice(rowIndex, 1);

          const proceduresFormArray = this.procedureForm.get(
            "procedures"
          ) as FormArray;
          proceduresFormArray.removeAt(rowIndex);

          Swal.fire("Deleted!", "Procedure has been deleted.", "success");
        } else {
          Swal.fire("Error!", "Procedure not found in the list.", "error");
        }
      }
    });
  }

  saveScreeningDetails() {
    const donorScreenings = [];

    // Map organ group names to single-character screenType codes
    const screenTypeMapping = {
      "Liver & Pancreas": "P",
      Kidney: "K",
      Lung: "L",
      Heart: "H",
    };

    Object.keys(this.organRelatedParams).forEach((organKey) => {
      const screenType = screenTypeMapping[organKey] || null; // Get the screenType or default to null
      if (!screenType) {
        console.warn(`Unknown organKey: ${organKey}`);
        return;
      }

      this.organRelatedParams[organKey].forEach((param) => {
        // Only save if paramValue is available
        if (param.paramValue) {
          donorScreenings.push({
            dtlId: null, // Set this if available
            donorId: this.deceasedDonorForm.value.donorId || null, // Map donorId if available
            paramId: param.id, // Map paramId
            paramValue: param.paramValue, // Map paramValue (Yes/No)
            remarks: param.remarks || "", // Default remarks to an empty string if null
            screenType: screenType, // Use the mapped single-character screenType
          });
        }
      });
    });

    // Save ethical approvals
    this.ethicalApprovalLabels.forEach((approval, index) => {
      const control = this.deceasedDonorForm.get(`approval_${index}`);

      // console.log("Approval Control:", control);

      if (control && control.value) {
        donorScreenings.push({
          dtlId: null, // Set this if available
          donorId: this.deceasedDonorForm.value.donorId || null, // Map donorId if available
          paramId: approval.id, // Use the ID from ethicalApprovalLabels
          paramValue: control.value, // Yes/No
          remarks: "", // Add remarks if applicable
          screenType: "E", // Ethical approval screen type
        });
      }
    });

    // Save medical history
    this.medicalHistoryList.forEach((history) => {
      const control = this.deceasedDonorForm.get(
        `medicalHistory_${history.id}`
      );
      const remarksControl = this.deceasedDonorForm.get(
        `medicalHistoryRemarks_${history.id}`
      );
      if (control && control.value) {
        donorScreenings.push({
          dtlId: null, // Set this if available
          donorId: this.deceasedDonorForm.value.donorId || null, // Map donorId if available
          paramId: history.id, // Use the ID from medicalHistoryList
          paramValue: control.value, // Yes/No/Unknown
          remarks: remarksControl ? remarksControl.value : "", // Add remarks if available
          screenType: "M", // Medical history screenType
        });
      }
    });

    // console.log("Donor Screenings:", donorScreenings);
    return donorScreenings;
  }

  loadDonorScreeningsNew(donorScreenings: any[]): void {
    // Reset organRelatedParams
    this.organRelatedParams = {};

    donorScreenings.forEach((screening) => {
      // Group by organ (screenType)
      if (!this.organRelatedParams[screening.screenType]) {
        this.organRelatedParams[screening.screenType] = [];
      }

      // Add the screening data to the corresponding organ group
      this.organRelatedParams[screening.screenType].push({
        id: screening.paramId,
        value: screening.paramValue,
        remarks: screening.remarks,
        paramValue: screening.paramValue, // Yes/No
      });
    });

  }

  loadDonorScreenings(donorScreenings: any[]): void {
    donorScreenings.forEach((screening) => {
      // Find the organ group for the screening
      
      const organGroup = Object.keys(this.organRelatedParams).find((key) =>
        this.organRelatedParams[key].some(
          (param) => param.id === screening.paramId
        )
      );

      if (organGroup) {
        // Find the specific parameter and update its values
        const param = this.organRelatedParams[organGroup].find(
          (param) => param.id === screening.paramId
        );
        if (param) {
          param.paramValue = screening.paramValue; // Update paramValue
          param.remarks = screening.remarks || ""; // Update remarks
        }
      }
    });
  }

  loadEthicalApprovalData(donorScreenings: any[]): void {
    // Fetch ethical approvals from the master service
    this.masterService.getEthicalApprovals().subscribe((data: any[]) => {
      // Map the master data to ethicalApprovalLabels
      this.ethicalApprovalLabels = data.map((item) => ({
        id: item.id,
        value: item.value,
      }));

      // Dynamically add controls for ethical approvals
      this.ethicalApprovalLabels.forEach((_, index) => {
        if (!this.deceasedDonorForm.get(`approval_${index}`)) {
          this.deceasedDonorForm.addControl(
            `approval_${index}`,
            this.fb.control(null)
          );
        }
      });

      const ethicalScreenings = donorScreenings.filter(
        (screening) => screening.screenType === "E"
      );
      if (!ethicalScreenings || ethicalScreenings.length === 0) {
        console.warn("No ethical approval data found in donorScreenings.");
        return;
      }

      ethicalScreenings.forEach((screening) => {
        if (screening.paramId !== null) {
          // Find the corresponding master data entry
          const approvalIndex = this.ethicalApprovalLabels.findIndex(
            (approval) => approval.id === screening.paramId
          );
          if (approvalIndex !== -1) {
            const controlName = `approval_${approvalIndex}`;
            const control = this.deceasedDonorForm.get(controlName);

            if (control) {
              control.setValue(screening.paramValue); // Set the value (Y/N)
            } else {
              console.warn(
                `Form control not found for paramId: ${screening.paramId}`
              );
            }
          } else {
            console.warn(
              `No matching ethical approval found for paramId: ${screening.paramId}`
            );
          }
        }
      });
    });
  }

  getCommentControlName(organKey: string): string {
    const commentControlMapping = {
      "Liver & Pancreas": "liverComments",
      Kidney: "kidneyComments",
      Lung: "lungComments",
      Heart: "heartComments",
    };

    return commentControlMapping[organKey] || null;
  }

  saveEthicalApprovalData(): void {
    const donorScreenings = [];

    // Loop through the ethical approval controls
    this.ethicalApprovalLabels.forEach((approval, index) => {
      const approvalValue = this.deceasedDonorForm.get(
        `approval_${index}`
      ).value;
      if (approvalValue) {
        donorScreenings.push({
          dtlId: null, // Set this if available
          donorId: this.deceasedDonorForm.value.donorId || null, // Map donorId if available
          paramId: approval.id, // Use the ID from ethicalApprovalLabels
          paramValue: approvalValue, // Yes/No
          remarks: "", // Add remarks if applicable
          screenType: "E", // Ethical approval screen type
        });
      }
    });

    // Add ethicalApprovalComments to the deceasedDonorForm
    const ethicalApprovalComments = this.deceasedDonorForm.get(
      "ethicalApprovalComments"
    ).value;
  }

  clearForm(): void {
    // Reset all forms
    this.deceasedDonorForm.reset(); // Reset all form controls to their initial state
    this.labResultForm.reset();
    this.procedureForm.reset();
    this.vitalForm.reset();
    this.medicalProcedureForm.reset();

    // Reset UI flags
    this.showAddNewSection = true; // Hide the add new section
    this.showViewButton = false; // Hide the view button
    this.submitted = false; // Reset the submitted flag if used for validation
    this.showDownload = false; // Hide the download button
    this.showVitalChartButton = false;
    this.showVitalDownloadButton = false;

    this.showLabChartButton = false;
    this.showLabownloadButton = false;

    this.vitalDates = []; // Clear the vital dates array
    this.labDatesNew = [];
    this.labListNew = [];
    this.labDates = [];
    this.labList = [];
    this.paginatedLabDatesNew = [];
    this.showLabResultsTable = false; // Hide the lab results table
    this.showLabAddNewSection = false; // Hide the add new section for lab results
    // Clear table data
    //this.vitals.clear();
    this.vitalList = []; // Clear the vital list
    this.vitalDatesNew = []; // Clear the vital dates array
    this.vitalListNew = []; // Clear the vital list
    this.labResultList = []; // Clear the lab result list
    this.procedureList = []; // Clear the procedure list (Other Investigations)
    this.medicalProcedureList = []; // Clear the medical procedure list
    this.paginatedVitalList = [];
    this.showVitalResultsTable = false; // Hide the vital results table

    // Clear other data
    this.hlaTissueType = []; // Clear the HLA tissue type
    this.medicalProcedures.clear(); // Clear the medical procedures FormArray
    this.procedures.clear(); // Clear the procedures FormArray

    for (const key in this.organRelatedParams) {
      if (this.organRelatedParams.hasOwnProperty(key)) {
        this.organRelatedParams[key].forEach((param: any) => {
          param.paramValue = null; // Reset the radio button value
          param.remarks = ""; // Clear the remarks field
        });
      }
    }

    // Clear Cardiac Arrest Arrived section
    this.cardiacArstDtlsDto = {
      cardiacArstRevYn: null, // Reset the radio button value
      cprDuration: "", // Clear the CPR duration field
    };

    this.deceasedDonorForm.controls["civilId"].enable();
    this.deceasedDonorForm.controls["exDate"].enable();

    this.updateVitalPagination();

    // console.log("Form and table data cleared.");
  }

  downloadOtherInvestigations(): void {
    this.deceasedDonorService.getOtherInvestigations().subscribe(
      (response) => {
        this.donorOtherLabListing = response; // Update the table data
        //console.log("Other Investigations:", this.procedureList);
      },
      (error) => {
        console.error("Error fetching other investigations:", error);
        Swal.fire("Error", "Failed to download other investigations.", "error");
      }
    );
  }

  downnloadLabResults() {
    //console.log("called downnloadLabResults");
    // Check if estCode and civilId are not null
    // console.log('estCode: ' + this.estCode + ', civilId: ' + this.civilId);
    //this.civilId = 105866112;
    //this.estCode = 20068;
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .fetchAllDonorLabFromShifa(this.estCode, this.civilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null && res["result"].length > 0) {
                this.showOtherInvestigationsTable = true;
                const rgTbLabTestsDB: any = res["result"];

                //console.log("Lab Results:", rgTbLabTestsDB);
                this.loadOtherlabTableResult(rgTbLabTestsDB);
                this.loadOtherlabResult(rgTbLabTestsDB);
              } else {
                Swal.fire(
                  "No Record Found",
                  "No lab results found for the given estCode and civilId.",
                  "info"
                );
              }
            } else {
              Swal.fire("No Other Investigations Found.");
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching lab results: " + error.message,
              "error"
            );
          }
        );
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorLabFromAlShifa will not be executed."
      );
    }
  }

  async asyndownloadAllLabResults(): Promise<boolean> {
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .getDonorLabDtlFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (
              res["code"] == "S0000" &&
              res["result"] != null &&
              res["result"].length > 0
            ) {
              const rgTbLabTestsDB: any = res["result"];
              this.showLabChartButton = true;
              this.showLabownloadButton = false;
              this.showLabResultsTable = true;
              this.loadLabResultsParam(rgTbLabTestsDB);
              this.loadLabResults(rgTbLabTestsDB);
              resolve(true);
            } else {
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  async asyncdownnloadLabResults(): Promise<boolean> {
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .fetchAllDonorLabFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (
              res["code"] == "S0000" &&
              res["result"] != null &&
              res["result"].length > 0
            ) {
              this.showOtherInvestigationsTable = true;
              const rgTbLabTestsDB: any = res["result"];
              this.loadOtherlabTableResult(rgTbLabTestsDB);
              this.loadOtherlabResult(rgTbLabTestsDB);
              resolve(true);
            } else {
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  async asyncdownloadMedicalProcedures(): Promise<boolean> {
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .fetchAllDonorProcedureFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (
              res["code"] == "S0000" &&
              res["result"] != null &&
              res["result"].length > 0
            ) {
              this.showActionsColumn = false;
              const rgTbLabTestsDB: any = res["result"];
              this.showMedicalProcedureTable = true;
              this.loadDownloadedMedicalTableProcedures(rgTbLabTestsDB);
              this.loadDownloadedMedicalProcedures(rgTbLabTestsDB);
              resolve(true);
            } else {
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  async asyncdownloadVitalInfoFromAlshifa(): Promise<boolean> {
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .getVitalInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (
              res["code"] == "S0000" &&
              res["result"] != null &&
              res["result"].length > 0
            ) {
              const rgTbLabTestsDB: any = res["result"];
              this.loadVitalParam(rgTbLabTestsDB);
              this.loadVitalData(rgTbLabTestsDB);
              this.showVitalChartButton = true;
              this.showVitalDownloadButton = false;
              this.showVitalResultsTable = true;
              this.showAddNewSection = false;
              resolve(true);
            } else {
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  downloadAllLabResults() {
    console.log("called downloadOtherLabResults");
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getDonorLabDtlFromShifa(this.estCode, this.civilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null && res["result"].length > 0) {
                const rgTbLabTestsDB: any = res["result"];
                this.showLabChartButton = true;
                this.showLabownloadButton = false;
                this.showLabResultsTable = true;
                this.loadLabResultsParam(rgTbLabTestsDB);
                this.loadLabResults(rgTbLabTestsDB);
              } else {
                Swal.fire(
                  "No Record Found",
                  "No lab results found for the given estCode and civilId.",
                  "info"
                );
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching lab results: " + error.message,
              "error"
            );
          }
        );
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorLabFromAlShifa will not be executed."
      );
    }
  }

  downloadOtherLabResults() {
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getDonorLabDtlFromShifa(this.estCode, this.civilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null && res["result"].length > 0) {
                const rgTbLabTestsDB: any = res["result"];
                this.showLabChartButton = true;
                this.showLabownloadButton = false;
                this.showLabResultsTable = true;
                this.loadLabResultsParam(rgTbLabTestsDB);
                this.loadLabResults(rgTbLabTestsDB);
              } else {
                Swal.fire(
                  "No Record Found",
                  "No lab results found for the given estCode and civilId.",
                  "info"
                );
              }
            } else {
              Swal.fire("No Lab Results Found.");
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching lab results: " + error.message,
              "error"
            );
          }
        );
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorLabFromAlShifa will not be executed."
      );
    }
  }

  downloadMedicalProcedures() {
    //this.civilId = 22915219;
    //this.estCode = 20068;
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .fetchAllDonorProcedureFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                this.showActionsColumn = false;
                const rgTbLabTestsDB: any = res["result"];
                this.showMedicalProcedureTable = true;
                this.loadDownloadedMedicalTableProcedures(rgTbLabTestsDB);
                this.loadDownloadedMedicalProcedures(rgTbLabTestsDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire("No Medical Procedures Found.");
            }
          },
          error: (error) => {
            console.error("Error loading procedures from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  openModalInfo(content: any, modalName: any): void {
    //console.log(modalName);
    // pass vital as a parameter to the modal
    this.callModal = modalName;
    this.modalService.open(content, { backdrop: "static" });
  }

  downloadVitalInfoFromAlshifa() {
    this.isLoading = true;
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getVitalInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            this.isLoading = false;

            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbLabTestsDB: any = res["result"];
                // console.log(rgTbLabTestsDB, "vital info from alshifa");

                this.loadVitalParam(rgTbLabTestsDB);
                this.loadVitalData(rgTbLabTestsDB);
                this.showVitalChartButton = true;
                this.showVitalDownloadButton = false;
                this.showVitalResultsTable = true;
                this.showAddNewSection = false;
              } else {
                Swal.fire("No Vital Info Details Found.");
              }
            } else {
              Swal.fire("No Vital Info Details Found.");
            }
          },
          error: (error) => {
            console.error("Error loading vital info from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  downloadRespiratoryInfoFromAlshifa() {
    this.isLoading = true;
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getRespiratoryInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            this.isLoading = false;
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbLabTestsDB: any = res["result"];
                this.loadRespiratoryData(rgTbLabTestsDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          error: (error) => {
            console.error(
              "Error loading respiratory info from AlShifa:",
              error
            );
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  downloadVitalPHFromAlshifa() {
    this.isLoading = true;
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getVitalInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            this.isLoading = false;
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbVitalDB: any = res["result"];
                console.log(rgTbVitalDB, "vital info from alshifa");
                if (Array.isArray(rgTbVitalDB) && rgTbVitalDB.length > 0) {
                  // Sort by vitalDate descending and pick the first record
                  const latestVital = rgTbVitalDB
                    .slice() // clone array to avoid mutating original
                    .sort(
                      (a, b) =>
                        new Date(b.vitalDate).getTime() -
                        new Date(a.vitalDate).getTime()
                    )[0];

                  this.loadVitalPHData(latestVital);
                }

                this.getBmi();
                //this.loadVitalPHData(rgTbVitalDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          error: (error) => {
            console.error("Error loading vital info from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  async asyncdownloadVitalPHFromAlshifa(): Promise<boolean> {
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .getVitalInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (
              res["code"] == "S0000" &&
              res["result"] != null &&
              res["result"].length > 0
            ) {
              const rgTbVitalDB: any = res["result"];
              if (Array.isArray(rgTbVitalDB) && rgTbVitalDB.length > 0) {
                // Sort by vitalDate descending and pick the first record
                const latestVital = rgTbVitalDB
                  .slice() // clone array to avoid mutating original
                  .sort(
                    (a, b) =>
                      new Date(b.vitalDate).getTime() -
                      new Date(a.vitalDate).getTime()
                  )[0];
                this.loadVitalPHData(latestVital);
              }

              this.getBmi();
              resolve(true);
            } else {
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  async asyncdownloadRespiratoryInfoFromAlshifa(): Promise<boolean> {
    console.log("asyncdownloadRespiratoryInfoFromAlshifa");
    return new Promise((resolve) => {
      // ...existing code...
      this.deceasedDonorService
        .getRespiratoryInfoFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (res["code"] == "S0000" && res["result"] != null) {
              const rgTbVitalDB: any = res["result"];
              this.loadRespiratoryData(rgTbVitalDB);
              resolve(true);
            } else {
              console.log("No respiratory info found");
              resolve(false);
            }
          },
          error: () => resolve(false),
        });
    });
  }

  downloadLabDtlInfoFromAlshifa() {
    if (this.estCode && this.civilId) {
      this.deceasedDonorService
        .getDonorLabDtlFromShifa(this.estCode, this.civilId)
        .subscribe({
          next: (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbLabTestsDB: any = res["result"];
                this.loadDownloadedLabResults(rgTbLabTestsDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          error: (error) => {
            console.error("Error loading procedures from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  loadLabResultsParam(labData: any): void {
    if (labData.length > 0) {
      this.labDatesNew = [];
      this.labListNew = [];
      this.labDatesNew = labData
        .filter((lab) => lab.value !== null) // Only include labs with non-null values
        .map((lab) => this.formatDateLab(new Date(lab.testDate))); // Format the dates
      const tempLabListNew = this.donorlabTestOptions.map((item) => ({
        name: item.testName, // Test name
        values: {}, // Initialize an empty object for values
      }));
      labData.forEach((lab) => {
        const formattedDate = this.formatDate(new Date(lab.testDate));
        const labItem = tempLabListNew.find(
          (item) => item.name === this.getDonorLabListName(lab.mohTestCode)
        );

        if (labItem && lab.value !== null) {
          // Concatenate the unit with the value
          const valueWithUnit = lab.value
            ? `${lab.value} ${lab.unit || ""}`.trim()
            : "";
          labItem.values[formattedDate] = valueWithUnit; // Set the value for the specific date
        }
      });

      this.labListNew = tempLabListNew.filter((labItem) =>
        Object.values(labItem.values).some((value) => value !== "")
      );

      this.currentDatePage = 1;
      this.updateDatePagination();
    } else {
      console.warn("No valid lab data found in the API response.");
      this.labDatesNew = [];
      this.labListNew = [];
    }
  }

  // Update pagination for dates
  updateDatePagination(): void {
    const startIndex = (this.currentDatePage - 1) * this.datesPerPage;
    const endIndex = startIndex + this.datesPerPage;
    this.paginatedLabDatesNew = this.labDatesNew.slice(startIndex, endIndex);
    this.totalDatePages = Math.ceil(
      this.labDatesNew.length / this.datesPerPage
    );
  }

  // Navigate to the next page of dates
  nextDatePage(): void {
    if (this.currentDatePage < this.totalDatePages) {
      this.currentDatePage++;
      this.updateDatePagination();
    }
  }

  // Navigate to the previous page of dates
  previousDatePage(): void {
    if (this.currentDatePage > 1) {
      this.currentDatePage--;
      this.updateDatePagination();
    }
  }

  updatePagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedDateVitalListNew = this.vitalDatesNew.slice(
      startIndex,
      endIndex
    );
    this.totalPages = Math.ceil(this.vitalDatesNew.length / this.itemsPerPage);
  }

  // Navigate to the next page
  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  // Navigate to the previous page
  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  updateVitalPagination(): void {
    const startIndex = (this.currentVitalPage - 1) * this.itemsPerVitalPage;
    const endIndex = startIndex + this.itemsPerVitalPage;
    this.paginatedVitalList = this.vitalList.slice(startIndex, endIndex);
    this.totalVitalPages = Math.ceil(
      this.vitalList.length / this.itemsPerVitalPage
    );
  }

  onVitalPageChange(event: any): void {
    this.first = event.first;
    this.currentVitalPage = Math.floor(this.first / this.itemsPerVitalPage) + 1;
    this.updateVitalPagination();
  }

  previousVitalPage(): void {
    if (this.currentVitalPage > 1) {
      this.currentVitalPage--;
      this.first = (this.currentVitalPage - 1) * this.itemsPerVitalPage;
      this.updateVitalPagination();
    }
  }

  nextVitalPage(): void {
    if (this.currentVitalPage < this.totalVitalPages) {
      this.currentVitalPage++;
      this.first = (this.currentVitalPage - 1) * this.itemsPerVitalPage;
      this.updateVitalPagination();
    }
  }

  saveInstitute(modal: any): void {
    this.estCode = this.deceasedDonorForm.value.instCode_2;
    const vitalPhFields = [
      "height",
      "weight",
      "lungPh",
      "lungPao2",
      "lungPaco2",
      "lungPeep",
      "lungFio2",
    ];

    const vitalPhAvailable = vitalPhFields.every((field) => {
      const value = this.deceasedDonorForm.get(field).value;
      return value !== null && value !== undefined && value !== "";
    });

    switch (this.callModal) {
      case "vital":
        if (
          (this.paginatedDateVitalListNew &&
            this.paginatedDateVitalListNew.length == 0) ||
          (this.paginatedVitalList && this.paginatedVitalList.length < 0)
        ) {
          this.downloadVitalInfoFromAlshifa();
        }
        break;
      case "lab":
        if (
          (this.paginatedLabDatesNew &&
            this.paginatedLabDatesNew.length == 0) ||
          (this.labListNew && this.labListNew.length == 0)
        ) {
          this.downloadOtherLabResults();
        }
        break;
      case "investigation":
        if (this.procedureList && this.procedureList.length == 0) {
          this.downnloadLabResults();
        }
        break;
      case "medical":
        if (
          this.medicalProcedureList &&
          this.medicalProcedureList.length == 0
        ) {
          this.downloadMedicalProcedures();
        }
        break;
      case "vitalPh":
        if (!vitalPhAvailable) {
          this.downloadVitalPHFromAlshifa();
          this.downloadRespiratoryInfoFromAlshifa();
        }
        break;
      case "all":
        this.downloadAllfromAlshifa();
        break;
      default:
        break;
    }

    modal.close();
  }

  async downloadAllfromAlshifa() {
    this.isLoading = true;
    let foundData = false;

    const vitalHeightWeightFields = ["height", "weight"];

    const vitalPhFields = [
      "lungPh",
      "lungPao2",
      "lungPaco2",
      "lungPeep",
      "lungFio2",
    ];

    const vitalPhAvailable = vitalPhFields.every((field) => {
      const value = this.deceasedDonorForm.get(field).value;
      return value !== null && value !== undefined && value !== "";
    });

    const vitalHeightWeightAvailable = vitalHeightWeightFields.every(
      (field) => {
        const value = this.deceasedDonorForm.get(field).value;
        return value !== null && value !== undefined && value !== "";
      }
    );

    let vitalWeightFound = false;

    if (!vitalHeightWeightAvailable) {
      vitalWeightFound = await this.asyncdownloadVitalPHFromAlshifa();
    }

    let vitalPHFound = false;

    if (!vitalPhAvailable) {
      vitalPHFound = await this.asyncdownloadRespiratoryInfoFromAlshifa();
    }
    let labResultsFound = false;

    if (this.labListNew && this.labListNew.length == 0) {
      labResultsFound = await this.asyndownloadAllLabResults();
    }

    let otherLabResultsFound = false;

    if (this.procedureList && this.procedureList.length == 0) {
      otherLabResultsFound = await this.asyncdownnloadLabResults();
    }

    let medicalProceduresFound = false;

    if (this.medicalProcedureList && this.medicalProcedureList.length == 0) {
      medicalProceduresFound = await this.asyncdownloadMedicalProcedures();
    }

    let vitalInfoFound = false;
    if (
      (this.paginatedDateVitalListNew &&
        this.paginatedDateVitalListNew.length == 0) ||
      (this.paginatedVitalList && this.paginatedVitalList.length == 0)
    ) {
      vitalInfoFound = await this.asyncdownloadVitalInfoFromAlshifa();
    }

    this.isLoading = false;

    foundData =
      vitalWeightFound ||
      vitalPHFound ||
      labResultsFound ||
      otherLabResultsFound ||
      medicalProceduresFound ||
      vitalInfoFound;

    if (!foundData) {
      Swal.fire("No data found in AlShifa for any section.");
    }
  }

  deleteAllVital(): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.vitalForm.reset();
        this.vitalList = [];
        this.vitalDates = [];
        this.vitalListNew = [];
        this.vitalDatesNew = [];
        this.paginatedVitalList = [];
        this.paginatedDateVitalListNew = [];
        this.showVitalResultsTable = false;
        this.showVitalChartButton = false;
        this.showAddNewSection = true; // Hide the add new section
        this.showViewButton = false; // Hide the view button
        this.submitted = false; // Reset the submitted flag if used for validation
        this.showVitalDownloadButton = true;

        // clear page number
        this.currentPage = 1;
        this.updateVitalPagination();
      }
    });
  }

  deleteAllLab(): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.labResultForm.reset();
        this.labResultList = [];
        this.labDates = [];
        this.labList = [];
        this.labDatesNew = [];
        this.labListNew = [];
        this.paginatedLabDatesNew = [];
        this.showLabResultsTable = false; // Hide the lab results table
        this.showLabChartButton = false;
        this.showLabownloadButton = true;
        this.showLabAddNewSection = true; // Hide the add new section for lab results
      }
    });
  }

  deleteAllProcedure(): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.procedureForm.reset();
        this.procedureList = [];
        this.procedureForm.reset();
        this.showOtherInvestigationsTable = false; // Hide the add new section for lab results
        this.showActionsColumn = true;
      }
    });
  }
  deleteAllMedicalProcedure(): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.medicalProcedureForm.reset();
        this.medicalProcedureList = [];
        this.medicalProcedureForm.reset();
        this.showMedicalProcedureTable = false; // Hide the add new section for lab results
        this.showActionsColumn = true;
      }
    });
  }
}
