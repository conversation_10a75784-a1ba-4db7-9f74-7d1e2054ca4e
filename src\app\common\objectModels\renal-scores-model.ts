export class RenalScores {

    public runId: number;
    public centralRegNo: number;
    public createdOn: any;
    public modifiedOn: any;
    public createdBy: string;
    public modifiedBy: string;
    public pra: number;
    public ageScore: number;
    public dialysisPeriod: number;
    public prevFailed: number;
    public hlaMatch: number;
    public bloodGroup: number;
    public ageProximity: number;
    public prevDonor: number;
    public activeYn: string;
    public donorID: number;

    public oldPra: number;
    public oldAgeScore: number;
    public oldDialysisPeriod: number;
    public oldPrevFailed: number;
    public oldHlaMatch: number;
    public oldBloodGroup: number;
    public oldAgeProximity: number;
    public oldPrevDonor: number;
    public oldDonorID: number;

    public status: string;


}