
export class vaccineAdmDetls{

    public  centralRegNo: number;
    public  civilId: number;
    public  dateGiven: Date;
    public  dueDate: Date;
    public  estCode: number;
    public  estName: String;
    public  firstName:String;
    public  month: number;
    public  periodDesc:String;
    public  periodValue: number;
    public  scheduleQty: number;
    public  secondName:String;
    public  syringeBatchNo: String;
    public  targetQty: number;
    public  thirdName:number;
    public  tribe:String;
    public vaccineBatchNo: String;
    public vaccineId: number;
    public vaccineName: String;
    public vacScheduleID:number;
    public validity:number;
    public validityDurationType:String;
    public validFrom: Date;
    public year:number;

}  





	

	
