import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import * as AppUtils from "../common/app.utils";
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ChangePasswordService {

  constructor(private http: HttpClient) { }

  changePassword(data : any) : Observable<any>{
    return this.http.post(AppUtils.CHANGE_PASSWORD, data);
  }
}
