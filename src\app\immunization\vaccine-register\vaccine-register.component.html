<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Vaccine Register</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>


<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

    <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h6> Patient Details
                    <i class="col-lg-2 col-md-2 col-sm-2  fas fa-male " style="color: green; font-size:22px;"
                        title="patient is alive "><span style="padding-left: 5px;"></span></i>

                </h6>

                <div>

                    <span style="padding-left: 5px; color: green; font-size:18px" class="fas"></span>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>


            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <!--[patientForm]="patientForm" -->
            <app-patient-details [submitted]="submitted" [patientForm]="patientForm"
                (uploaded)="fetchVisitInfobyPatientID($event)" (callMethod)="callMpiMethod()" #patientDetails>
            </app-patient-details>
            <div class="row">
                <!-- <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Name</label>
                        <input type="text" class="form-control form-control-sm" formControlName="careGiverName">
                    </div>
                </div> -->

                <!-- <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Tel</label>
                        <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="careGiverTel">
                    </div>
                </div> -->

                <!-- <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Mobile</label>
                        <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm" formControlName="careGiverMobile">
                    </div>
                </div> -->
            </div>
        </ng-template>
    </ngb-panel>
</ngb-accordion>


<form [formGroup]="ImmunizationForm">
    <div class="content-wrapper mt-2">
        <h5>Immunization Schedule</h5>
        <div class="row">
            <div class="col-sm-2">
                <table class="table table-striped table-bordered table-sm ">
                    <thead>
                        <th>Periods</th>
                    </thead>
                    <tbody>
                        <td>
                            <div>
                                <ul class="tree-list" id="options">
                                    <li class="list-group-item" *ngFor="let list of periodList"
                                        [class.active]="active === list.vacScheduleId"
                                        (click)="onClick(list.vacScheduleId)" style="background-color: rgb(250, 244, 244);"
                                        (click)="displayVaccineList(list.vacScheduleId)">{{list.periodDesc}}</li>
                                </ul>
                            </div>
                        </td>
                    </tbody>
                </table>
            </div>

            <div class="col-sm-10" >
              <div class="imm-schedule">
                <ag-grid-angular *ngIf="view==true" style="width: 100%; height: 400px;" class="ag-theme-balham"
                    [rowData]="setData" [columnDefs]="columnDefsData" (gridReady)="onGridReady($event)" (focusOut)="onFocusOut()"
                    [gridOptions]="gridOptions1" enableSorting enableFilter rowSelection="single" singleClickEdit=true
                    [enableColResize]="true" [frameworkComponents]="frameworkComponents" row-height="22">
                </ag-grid-angular>
            </div>


            </div>
        </div>
        <div class="btn-container">
            <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
            <button class="btn btn-sm btn-primary" (click)="saveRegister()">save</button>
        </div>

    </div>

</form>

<div class="content-wrapper mt-2">

    <h5>Immunization History</h5>

    <div style="margin-top:20px">
        <div style="margin-top:20px">

            <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="historyImmunizationList"
                [columnDefs]="columnDefHistory" (gridReady)="onGridReady($event)" [gridOptions]="gridOptions">
            </ag-grid-angular>

        </div>
    </div>
</div>