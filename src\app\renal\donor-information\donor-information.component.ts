import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { Component, OnInit, Input, ViewChild, TemplateRef, ViewChildren } from '@angular/core';
import { NgbModal, ModalDismissReasons, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { GenderType } from '../../_models/gender-type';
import { Nationality } from '../../_models/nationality.model';
import { MasterService } from '../../_services/master.service';
import { RegistryService } from '../../_services/registry.service';
import { RenalService } from '../../renal/renal.service';
import { SharedService } from '../../_services/shared.service';
import { PatientInfo } from '../../_models/patient.model';
import { Router, ActivatedRoute } from "@angular/router";
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { RenalDonorPatient } from '../../_models/renal_donor_patient.model';
import { RenalDonor } from '../../_models/renal-donor.model';
import { BehaviorSubject, Observable } from 'rxjs';
import { MpiModel } from '../../common/objectModels/mpi-model';
import { LoginService } from '../../login/login.service';
import { DatePipe } from '@angular/common';
import { ModalConfig } from '../../config/modal-config';
import * as $ from "jquery";
import 'jqueryui';
import Swal from 'sweetalert2';

import * as AppUtils from '../../common/app.utils';
import * as CommonConstants from '../../_helpers/common.constants';
import { TissueTypeModel } from '../../common/objectModels/tissueTypeModel';
import * as moment from 'moment';
import { DonorPatient } from '../../common/objectModels/DonorPatient-model';
import { supportsScrollBehavior } from '@angular/cdk/platform';
import { formatDate } from '@angular/common';
import { isDefined } from '@angular/compiler/src/util';
import * as AppCompUtils from '../../common/app.component-utils';



@Component({
  selector: 'app-donor-information',
  templateUrl: './donor-information.component.html',
  styleUrls: ['./donor-information.component.scss'],


})
export class DonorInformationComponent implements OnInit {

  @ViewChild('ScoringInfo', { static: false }) public ScoringInfo: TemplateRef<any>;

  @Input() patientForm: FormGroup; donorForm: FormGroup; hlaDonor: FormGroup; donorTissueForm: FormGroup; scoringForm: FormGroup;
  public genderTypeOptions = AppCompUtils.GENDER;;
  private genderTypes = GenderType;

  public nationOption = [];
  private nationalityList = Nationality;
  nationList: any;
  nationListFilter: any;
  relationList: any;
  relationListFilter: any;
  institutes: any[];
  patientInfoView: PatientInfo[];
  newPatientView: any = [];
  PatientsDeceasedDonorList: any = [];
  renalPatinetNewList: any = [];
  patientInfoDtl: PatientInfo[];
  civilId: any;
  patientInfoDtlview: any;
  relationDesc: any;
  relationType: any;
  today = new Date();
  deceasedDonor: any;
  livinRelatedDonor: any;
  livinUnrelatedDonor: any;
  selectedDonateHospital: any;
  selectNationality: any;
  donorType: any;
  value: Date;
  RenalDonorPatient: RenalDonorPatient;
  renalDonor: RenalDonor;
  hlaTissueType: TissueTypeModel
  centralRegNo: number;
  selectPtient: any;
  Ropdata: any;
  MpiModelInfo: MpiModel;
  civilIdInvalid = false;
  @Input() submitted = false;
  sexList: Array<GenderType> = [];
  code: any;
  bloodRelationList: any;
  nonBloodRelationList: any;
  genbloodlist: any;
  hospitalsList: any;
  geneticTypeId = 1;
  testID = 10070;
  thisYear = (new Date()).getFullYear();
  startDate = new Date("1/1/" + this.thisYear);
  defaultFormattedDate = this.datePipe.transform(this.startDate, 'dd-MM-yyyy');
  modalOptions: NgbModalOptions = ModalConfig;

  a_Test: any;
  a_1_Test: any;
  b_Test: any;
  b_1_Test: any;
  BW1: any;
  BW2: any;
  cw_Test: any;
  cw_1_Test: any;
  dr_Test: any;
  dr_1_Test: any;
  drw_Test: any;
  drw_1_Test: any;
  dq_Test: any;
  dq_1_Test: any;
  showSelectPatient: boolean = false;
  newpatientInfoDtlview: any;
  savingTissue: any;
  // donorScore: any;
  // patientScore: any;
  scoreData: any;
  selectedNote: any;
  patientview: any = null;
  HlaByDonorId: any;
  HlaByRegNo: any;
  selectedPatient: any;
  disabledCondition: boolean = true;




  constructor(private modalService: NgbModal, private renalService: RenalService, private formBuilder: FormBuilder, private _masterService: MasterService, private registryService: RegistryService
    , public shared: SharedService, private router: Router, private http: HttpClient, private LoginService: LoginService, private datePipe: DatePipe) {
    this.getNationalityList();
    this.getRelationTypeMast();


    if (this.shared.getNavigationData()) {
      let donorId = this.shared.getNavigationData().donorId;
      this.getDonorDetails(donorId, 'donorID');
    }

  }

  ngOnInit(): void {
    this.initializeFormGroup();


    this.RenalDonorPatient = new RenalDonorPatient;
    this.renalDonor = new RenalDonor;
    // this.renalPatinetNewList = new PatientInfo;
    //this.genderTypeOptions = Object.keys(this.genderTypes);
    this._masterService.institiutes.subscribe(res => {
      this.institutes = res["result"];

    })

    this.registryService.getAllPatient().subscribe(response => {


      this.patientInfoView = response["result"];


      // this.patientInfoView.forEach(element => {
      //   if (element.registerType == 2) {
      //     this.newPatientView.push(element);
      //   }

      // });



      this.patientInfoView.forEach(element => {
        if (element.registerType == 2) {
          let fullName = 'CivilID  ' + element.civilid + ' ,' + element.firstname + ' ' + element.secondname + ' ' + element.thirdname + ' ' + element.tribe + ', Age  ' + element.age;
          element["fullName"] = fullName;
          this.renalPatinetNewList.push(element);

        }

      });




    });

    this._masterService.getBloodRelation().subscribe(response => {
      this.bloodRelationList = response["result"];

    })
    this._masterService.getNonBloodRelation().subscribe(response => {
      this.nonBloodRelationList = response['result'];
    })

    this._masterService.getHospitals().subscribe(response => {
      this.hospitalsList = response['result'];

    })

  }


  private initializeFormGroup() {
    this.donorForm = this.formBuilder.group({

      'civilId': [null],
      'fullname': [""],
      'sex': [""],
      'dob': [null],
      'nationality': [null],
      'bloodGroup': [""],
      'telNo': [null],
      'address': [""],
      'donorType': [""],
      'instCode': [null],
      'instPatientId': [null],
      'relationDesc': [null],
      'relationType': [null],
      'exDate': [""],
    })

    this.patientForm = this.formBuilder.group({
      'relationType': [""],
      'relationDesc': [""],
    })

    this.hlaDonor = this.formBuilder.group({
      'donorId': [null],
      'civilId': [null],
      'name': [null],
      'sex': [""],
      'nationality': [null],
      'dob': [null],
      'bloodGroup': [null],
      'telNo': [null],
      'address': [null]
    })

    this.donorTissueForm = this.formBuilder.group({
      'runId': [null],
      'donorId': [null],
      'a_Test': [null],
      'a_1_Test': [null],
      'b_Test': [null],
      'b_1_Test': [null],
      'cw_Test': [null],
      'cw_1_Test': [null],
      'dr_Test': [null],
      'dr_1_Test': [null],
      'drw_Test': [null],
      'drw_1_Test': [null],
      'dq_Test': [null],
      'dq_1_Test': [null],
      'bw_Test': [null],
      'bw_1_Test': [null],

    })
    this.scoringForm = this.formBuilder.group({
      'pra': [null],
      'ageScore': [null],
      'dialysisPeriod': [null],
      'prevFailed': [null],
      'hlaMatch': [null],
      'bloodGroup': [null],
      'ageProximity': [null],
      'prevDonor': [null],
      'runId': [null],
      'activeYn': [null],
      'donorID': [null],
      'centralRegNo': [null],
    });
    this.donorForm.controls['sex'].disable();
  }

  openModal(linkWithPatient) {
    this.modalService.open(linkWithPatient);
  }

  openModalInfo(PatientInfo, event) {
    this.modalService.open(PatientInfo);
    this.getData(event.civilid);
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationList = response.result;
      this.nationListFilter = this.nationList;
    }, error => {

    });
  }

  getRelationTypeMast(relationCode: any = 0) {
    this._masterService.getRelationTypeMast(relationCode).subscribe(response => {
      this.relationList = response.result;
      this.relationListFilter = this.relationList;
    }, error => {

    });
  }



  getData(civilid) {


    this.patientInfoDtlview = this.renalPatinetNewList.filter(s => s.civilid == civilid)[0];
    this.centralRegNo = this.patientInfoDtlview.centralRegNo;
  }


  savePatientInfo() {
    this.RenalDonorPatient.renalDonorId = this.renalDonor.kidneyDonorId;
    this.RenalDonorPatient.centralRegNo = this.centralRegNo;
    this.RenalDonorPatient.relationType = this.relationType;
    this.RenalDonorPatient.relationDesc = this.relationDesc;
    const datePipe = this.datePipe.transform(this.donorForm.get('dob').value, 'YYYY-MM-DD');



    this.registryService.saveRenalDonorPatient(this.RenalDonorPatient).subscribe(res => {

      if (res['code'] == 0) {
        Swal.fire('Saved!', 'Patient Saved successfully.', 'success');

      } else if (res['code'] == "3") {
        Swal.fire('Saved!', res['message'], 'error');
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Patient ' + err.message, 'error')
    })
  }


  saveDonor() {
    if (this.renalDonor.civilId == null) {
      this.submitted == false;
      Swal.fire({
        title: 'Warning',
        icon: 'question',
        text: 'Civil Id Can not be empty ',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });
    }
    if (this.renalDonor.relationType === null && this.renalDonor.donorType === 'R') {
      this.submitted = false;
      Swal.fire({
        title: 'Warning',
        icon: 'question',
        text: 'Relation Type Can not be empty ',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });
    }
    if (this.renalDonor.instCode === null && this.renalDonor.donorType === 'D') {
      this.submitted = false;
      Swal.fire({
        title: 'Warning',
        icon: 'question',
        text: 'Donating Hospital Name Can not be empty ',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });
    }

    else {
      /*
            this.registryService.saveDonor(this.renalDonor).subscribe
              (response => {
      
                this.submitted = true;
                Swal.fire({
                  title: 'Success',
                  icon: 'success',
                  text: 'Donor has been saved  ',
                  confirmButtonText: 'Ok',
                  confirmButtonColor: '#3085d6'
                });
      
              });
      
            */

      this.registryService.saveDonor(this.renalDonor).subscribe(res => {
        this.submitted = true;
        if (res['code'] == 0) {

          Swal.fire('Saved!', 'Donor has been saved successfully.', 'success');

          this.getDonorDetails(res["result"], 'civilId');

        } else if (res['code'] == "3") {
          Swal.fire('Saved!', res['message'], 'error');
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }

      }, err => {
        Swal.fire('Error!', 'Error occured while saving Donor ' + err.message, 'error')
      })


    }






  }

  clear() {
    /* this.donorForm.get("civilId").patchValue(null);
     this.donorForm.get("fullname").patchValue(null);
     this.donorForm.get("sex").patchValue(null);
     this.donorForm.get("dob").patchValue(null);
     this.donorForm.get("nationality").patchValue([]);
     this.donorForm.get("bloodGroup").patchValue([]);
     this.donorForm.get("telNo").patchValue(null);
     this.donorForm.get("address").patchValue(null);
     this.donorForm.get("donorType").patchValue([]);
     this.donorForm.get("instCode").patchValue([]);
     this.donorForm.get("instPatientId").patchValue([]);
     this.donorForm.get("relationDesc").patchValue([]);
     this.donorForm.get("relationType").patchValue(null);
     */
    this.donorForm.reset();
    this.renalDonor[Object.keys(this.renalDonor)[0]] = '';
    this.donorForm.controls['civilId'].enable();
    this.donorForm.controls['exDate'].enable();
    this.showSelectPatient = false;
    this.PatientsDeceasedDonorList= null;
  }

  //getDonorByDonorID
  getDonorDetails(id, type) {
    //this.clear();
    // type=='donorID'  'civilID'
    if (id != null) {

      this.registryService.getDonorDetails(id, type).subscribe(response => {

        // && response["result"] != null
        if (response != null) {

          if (response["code"] == 'S0000') {
            this.renalDonor = response["result"].rgTbDonorReg;
            this.hlaTissueType = response["result"].rgHlaTissueType;

            this.hlaTissueType.a_Test = this.padLeft(this.hlaTissueType.a_Test, "0", 2);
            this.hlaTissueType.a_1_Test = this.padLeft(this.hlaTissueType.a_1_Test, "0", 2);
            this.hlaTissueType.b_Test = this.padLeft(this.hlaTissueType.b_Test, "0", 2);
            this.hlaTissueType.b_1_Test = this.padLeft(this.hlaTissueType.b_1_Test, "0", 2);
            this.hlaTissueType.cw_Test = this.padLeft(this.hlaTissueType.cw_Test, "0", 2);
            this.hlaTissueType.cw_1_Test = this.padLeft(this.hlaTissueType.cw_1_Test, "0", 2);
            this.hlaTissueType.dr_Test = this.padLeft(this.hlaTissueType.dr_Test, "0", 2);
            this.hlaTissueType.dr_1_Test = this.padLeft(this.hlaTissueType.dr_1_Test, "0", 2);
            this.hlaTissueType.drw_Test = this.padLeft(this.hlaTissueType.drw_Test, "0", 2);
            this.hlaTissueType.drw_1_Test = this.padLeft(this.hlaTissueType.drw_1_Test, "0", 2);
            this.hlaTissueType.dq_Test = this.padLeft(this.hlaTissueType.dq_Test, "0", 2);
            this.hlaTissueType.dq_1_Test = this.padLeft(this.hlaTissueType.dq_1_Test, "0", 2);
            // elm.bw_Test = this.padLeft(elm.bw_Test, "0", 2);
            // elm.bw_1_Test = this.padLeft(elm.bw_1_Test, "0", 2);
            this.donorTissueForm.patchValue(this.hlaTissueType);

            // let res = [];
            // res = response["result"].rgTbDonorReg;

            this.donorForm.patchValue({
              civilId: this.renalDonor["civilId"],
              fullname: this.renalDonor["fullname"],
              nationality: this.renalDonor["nationality"],
              telNo: this.renalDonor["telNo"],
              sex: this.renalDonor["sex"],
              // sex: this.genderTypeOptions.filter(s=> s.id == this.renalDonor["sex"] ).map()  ,
              // sex: this.renalDonor["sex"] === "Male" ? "M" : "F",
              age: this.renalDonor["age"],
              mobileNo: this.renalDonor["mobileNo"],
              address: this.renalDonor["address"],
              dob: new Date(this.renalDonor["dob"])
            })
            this.donorForm.controls['civilId'].disable();
            this.donorForm.controls['exDate'].disable();
          } else if (response["code"] == 'C0001') {
            Swal.fire('warning!', response["message"], 'warning')

          } else if (response["code"] == 'C0002') {
            let exDate = this.donorForm.value.exDate;
            let civil = this.donorForm.value.civilId;

            if (exDate == "" || exDate == null || civil == "" || civil == null) {
              Swal.fire('warning', 'The entered <b>Civil ID</b> is not in the registry file, please enter <b>Civil ID</b> & <b>Expiry Date</b> to get the data from MPI', 'warning')
            } else {
              this.getPatientDetails(id);
            }
          }

          else {
            Swal.fire('Error!', 'Error occurred while fetching donor information details </br>' + response["message"], 'error')
          }

        } else {



        }
      });

    }
  }

  getPatientDetails(civilId) {


    if (civilId != null) {
      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      const loginId = curUser['person'].perscode;

      let exDate = this.donorForm.value.exDate;
      if (exDate == "") {
        exDate = null;
      }
      else if (exDate != null) {
        exDate = formatDate(exDate, 'yyyy-MM-dd', 'en');
      }
      let req = {
        "birthDate": null,
        "cardExpiryDate": exDate,
        "civilId": this.donorForm.value.civilId,
        //"queryType": "string",
        //"requesterCivilId": "string",
        "requesterPersCode": loginId,
        // "type": 0 
      };
      this._masterService.getMpiV2Details(req).subscribe(response => {

        if (response != null) {
          if (response["code"] == AppUtils.RESPONSE_SUCCESS_CODE  && response["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI) {
            let mpires = [];
            mpires = response["result"];
            this.donorForm.patchValue({
              civilId: mpires["civilId"],
              fullname: mpires["firstNameEn"] + " " + mpires["secondNameEn"] + " " + mpires["thirdNameEn"] + " " + mpires["sixthNameEn"],
              nationality: mpires["countryID"],
              telNo: mpires["mobileNo"],
              sex: mpires["sex"] === "Male" ? "M" : "F",
              address: mpires["birthTown"],
              dob: new Date(mpires["birthDate"])
            })
            this.donorForm.controls['civilId'].disable();
            this.donorForm.controls['exDate'].disable();
          } else if (response["code"] == AppUtils.RESPONSE_SUCCESS_CODE  && response["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI) {
            Swal.fire('Error!', 'Error occurred while fetching civil id details </br>' + response["result"]["message"], 'error')
          }
          else {
            Swal.fire('Error!', 'Error occurred while fetching civil id details </br>' + response["message"], 'error')

          }
        }

      });
    }

  }


  ////////////////////// 
  // getScoresByDonorId(donorId) {
  //   if (donorId != null) {
  //     this.registryService.getScoresByDonorId(donorId).subscribe(Response => {
  //       this.donorScore = Response["result"];

  //     })
  //   }


  // }

  // getScoreByCentralRegNo(regNo) {
  //   this.registryService.getScoresByRegNo(regNo).subscribe(Response => {
  //     this.patientScore = Response["result"];
  //   })
  // }

  // getHlaByDonorId(donorId) {
  //   this.registryService.getHlaByDonorId(donorId).subscribe(Response => {

  //     this.HlaByDonorId = Response["result"];

  //   })
  // }

  // getHlaByRegNo(regNo) {
  //   this.http.get(AppUtils.GET_RENAL_DONOR_HLA_BY_REG_NO,
  //     { params: new HttpParams().set("centralRegNo", regNo) }).subscribe(res => {
  //       this.HlaByRegNo = res["result"].rgHlaTissueTypeDto[0];


  //     });
  // }

  getCompareHlaScores(regNo, donorId) {
    this.registryService.getCompareHlaScore(regNo, donorId).subscribe(Response => {
      // this.patientScore = Response["result"].patientScore;
      // this.donorScore = Response["result"].donorScore;
      this.HlaByDonorId = Response["result"].donorHla;
      this.HlaByRegNo = Response["result"].patientHla;
      // this.HlaByRegNo = Response["result"].patientHla.rgHlaTissueTypeDto[0];
      this.showscoreModal(this.ScoringInfo);
    })
  }
  

  public onClickKey($event: any) {
    if ($event.keyCode == 13) {
      this.civilId = $event.target.value;
      this.getDonorDetails(this.civilId, 'civilId');
    } else {
      this.civilIdInvalid = false;
    }
  }
  
  public onExpiryDateSelect(event: any) {
    const civilId = this.donorForm.get('civilId').value;
    this.getDonorDetails(civilId, 'civilId');
  }


  get f() { return this.donorForm.controls; }

  getSexCode(sexDesc: any) {
    let sex: GenderType[];
    sex = this.sexList.filter(item => item.valueOf === sexDesc);
    //this.sexDesc = sex[0].descr;
    return sex[0].valueOf;
  }


  donorSelectModal(donor) {

    //this.donorTissueForm.patchValue({"donorId":this.kidneyDonorId })
    this.hlaDonor.patchValue({
      "civilId": this.donorForm.value["civilId"],
      "name": this.donorForm.value["fullname"],
      "sex": this.donorForm.value["sex"],
      "nationality": this.donorForm.value["nationality"],
      "dob": this.donorForm.value["dob"],
      "bloodGroup": this.donorForm.value["bloodGroup"],
      'telNo': this.donorForm.value["telNo"],
      'address': this.donorForm.value["address"]
    });

    // Object.keys(this.donorTissueForm.controls).forEach(key => {
    //   this.donorTissueForm.controls[key].markAsDirty();
    // });
    let getFromData = this.donorTissueForm.value;


    this.donorTissueForm.patchValue({
      a_Test: this.padLeft(getFromData.a_Test, "0", 2),
      a_1_Test: this.padLeft(getFromData.a_1_Test, "0", 2),
      b_Test: this.padLeft(getFromData.b_Test, "0", 2),
      b_1_Test: this.padLeft(getFromData.b_1_Test, "0", 2),
      cw_Test: this.padLeft(getFromData.cw_Test, "0", 2),
      cw_1_Test: this.padLeft(getFromData.cw_1_Test, "0", 2),
      dr_Test: this.padLeft(getFromData.dr_Test, "0", 2),
      dr_1_Test: this.padLeft(getFromData.dr_1_Test, "0", 2),
      drw_Test: this.padLeft(getFromData.drw_Test, "0", 2),
      drw_1_Test: this.padLeft(getFromData.drw_1_Test, "0", 2),
      dq_Test: this.padLeft(getFromData.dq_Test, "0", 2),
      dq_1_Test: this.padLeft(getFromData.dq_1_Test, "0", 2),
    })

    this.modalService.open(donor, this.modalOptions);
    $(document).ready(function () {
      let modalContent: any = $('.modal-content');
      let modalHeader = $('.modal-header');
      modalHeader.addClass('cursor-all-scroll');
      modalContent.draggable({
        handle: '.modal-header',


      });
    });



  }
  confirm() {


    if (!this.patientview || this.patientview  == null){
      Swal.fire('warning!','Please select patients','warning');
    }else
    {
      Swal.fire({
        text: 'This donor will be mapped to patient ' + this.patientview.fullName,
        // text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'confirm'
      }).then((result) => {
        if (!result.dismiss) {
  
          this.renalService.updateRenalDonorPatient(this.renalDonor.kidneyDonorId,this.patientview.centralRegNo ).subscribe(res=>{

            if (res["code"] == "S0000"){
              Swal.fire('success','record is mapped successfuly','success');
              this.clear();
            }else{
              Swal.fire('warning!','record cannot be mapped ','warning');
            }
            // 
            // "code": "400",
          
          })
       
        
  
        }
      })
    }




  }
  showscoreModal(info) {

    this.modalService.open(info, this.modalOptions);

    $(document).ready(function () {
      let modalContent: any = $('.modal-content');
      let modalHeader = $('.modal-header');
      modalHeader.addClass('cursor-all-scroll');
      modalContent.draggable({
        handle: '.modal-header',


      });
    });

  }
  onRowSelect(event) {
    this.patientview = event.data;

    if (this.patientview.centralRegNo != null) {
      this.scoreData = this.patientview.scoreList;
      this.getCompareHlaScores(this.patientview.centralRegNo, this.renalDonor.kidneyDonorId)
      // this.getScoreByCentralRegNo(this.patientview.centralRegNo);
      // this.getScoresByDonorId(261);
      // this.getHlaByRegNo(this.patientview.centralRegNo);

    }
   

  }


  onRowUnselect(event) {
    this.patientview = null;
  }





  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;

  }

  changeFormat($event) {
    if ($event.value != null) {
      $event.value = this.padLeft($event.value, "0", 2);
    }
  }



  padLeft(text: any, padChar: string, size: number): string {
    if (!text) {
      return null
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  selectPatient() {
    this.showSelectPatient = true;

    this.renalService.getPatientsDeceasedDonor(this.renalDonor.kidneyDonorId).subscribe(res => {


      if (res['code'] == "S0000") {
        this.PatientsDeceasedDonorList = res["result"];

      } else {
        Swal.fire('Error!', res['message'], 'error');
      }
    })

  }

  saveTissueTypeInfo(type: any) {

    this.donorTissueForm.value['donorId'] = this.renalDonor["kidneyDonorId"];
    let donadBody = this.donorTissueForm.value;
    this.savingTissue = [donadBody];



    this.registryService.saveTissueTypeList(this.savingTissue).subscribe(response => {
      // this.submitted= true;

      //  
      Swal.fire('Saved!', 'Patient HLA Saved successfully.', 'success');

      if (response['code'] == "3") {
        Swal.fire('Saved!', response['message'], 'error');
      } else {
        Swal.fire('Error!', response['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Patient HLA ' + err.message, 'error')
    });


  }



}















