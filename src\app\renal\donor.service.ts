// import { Response } from '@angular/http';
import { Observable,BehaviorSubject } from 'rxjs/Rx';
import { Injectable } from '@angular/core';
import { LoginService } from '../login/login.service';
import { PrivilegeModel } from '../common/objectModels/privilege-model';
import { UserPrefModel } from '../common/objectModels/user-pref-model';
import * as AppUtils from '../common/app.utils';

import { UserModel } from '../common/objectModels/user-model';
import * as CommonConstants from '../_helpers/common.constants';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';

@Injectable()
export class DonorService {

    constructor(private http: HttpClient) {
    }


    getAllDonor():Observable<any> {
		return this.http.get(AppUtils.FIND_ALL_RENAL_DONOR);
	}

}