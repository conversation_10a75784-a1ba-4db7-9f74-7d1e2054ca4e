import { User } from './../_models/user.model';
import { Router } from '@angular/router';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { environment } from './../../environments/environment';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError } from 'rxjs/operators';
import * as CommonConstants from './../_helpers/common.constants';
import { map } from 'rxjs/operators';
import * as AppUtils from '../common/app.utils';
import { SharedService } from '../_services/shared.service';
import { Menu } from '../common/menu/menu';
@Injectable({
  providedIn: 'root'
})
export class LoginService {

  private currentUserSubject: BehaviorSubject<User>;
  public currentUser: Observable<User>;
  loggedInUsername: string;
  menus: Array<Menu> = new Array();

  constructor(private http: HttpClient, private router: Router ,  private sharedService: SharedService) {
    this.currentUserSubject = new BehaviorSubject<User>(JSON.parse(localStorage.getItem('currentUser')));
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User {
    return this.currentUserSubject.value;
  }
  userAuthentication(userName, password) {
    const data = '?grant_type=password' + '&client_id=' + CommonConstants.CLIENT_ID + '&username=' + userName + '&password=' + password;
    return this.http.get(environment.centralAuthenticationHost + CommonConstants.AUTH_TOKEN_API + data);
  }

  getUserDetails(): Observable<any> {
    return this.http.get(environment.centralAuthenticationHost + CommonConstants.FIND_USER_BY_LOGIN_AND_SYSTEM + '?systemId=' +
      CommonConstants.SYSTEM_ID).pipe(map(res => res),
        catchError(this.handleError)
      );
  }
  

  private handleError(error: HttpErrorResponse | any) {
    console.error('ApiService::handleError', error);
    return throwError(error);
  }


removeAccount(): void {
  localStorage.clear();
}

  getAuthSystemConfigParams(): Observable<any> {
    return this.http.get(AppUtils.oAUTH_SYSCONFIG_PARAMS);
  }
  checkIf2FAIsRequired(username: string): Observable<string> {
    return this.http.get(AppUtils.OTP_AUTHENTICATE_CHECK_IF_2FA_REQ + username, { responseType: 'text' });
  }

    validateLoggedUser(data:any): Observable<any> {
   return this.http.post(AppUtils.OTP_AUTHENTICATE_VALIDATE_USER ,data);
  }
   getOTP(data: any): Observable<any> {
        return this.http.post(AppUtils.OTP_AUTHENTICATE_GET_OTP, data);
    }
     getMpiDetailsOAuth(mpiInput: any): Observable<any> {
        return this.http.post(AppUtils.MPI_SERVICE_AUTH, mpiInput);
    }

    validateOTP(data: any): Observable<any> {
        return this.http.post(AppUtils.OTP_AUTHENTICATE_VALIDATE_OTP, data);
    }
     saveOtpPersonInfoCmast(data: any): Observable<any> {
        return this.http.post(AppUtils.OTP_AUTHENTICATE_SAVE_PERSON_INFO, data);
    }
    public getUserAttempts(username: string): Observable<any> {
    if (username != null) {
      return this.http
        .get(
          AppUtils.BACKEND_OAUTH_API_URL +AppUtils.AUTH_PREFIX+
          "/userAttempt/userAttempts?username=" +
          username
        )
        .pipe(
          map((res: HttpResponse<any>) => {
            return res;
          })
          // .catch((this.handleErrorResponse))
        );
    }
  }


// setLoggedInUsername(userName: string): void {
//   const loginName = AppUtils.LOGIN_NAME;
//   this.sharedService.getUserData()[loginName] = userName;
//   localStorage.setItem(AppUtils.LOGIN_NAME, userName);
//   const uName = localStorage.getItem(AppUtils.LOGIN_NAME);
//   this.loggedInUsername = uName;
// }

// getMenus(): Array<Menu> {
//   this.menus = JSON.parse(localStorage.getItem(AppUtils.MENU));
//   return this.menus;
// }

// setMenus(data: any): void {
//   this.menus = data;

// }


}
