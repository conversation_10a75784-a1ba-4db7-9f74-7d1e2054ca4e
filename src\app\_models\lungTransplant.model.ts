
export interface LungIndication {
  id: number;
  value: string;
  checked: boolean;
  remarksYn: string;
  remarks: string;
}

export interface FamilyHistory {
  hasHistory: boolean;
  remarks: string;
}


export interface LungRegister {
  lungId: number;
    familyHistYn: string;
    smokingYn: string;
    cigarettePerYr: number;
    cigaretteType: string;
    cigaretteQuitDate: Date;
    recTransYn: string;
    recTransReason: string;
    urgentTransYn: string;
    urgentTransReason: string;
    curMgmtPharma: string;
    priorityOrder: string;
    rgTbRegistryPatient: any; // You may want to create a specific interface for this
    rgTbLungPleurodesisHistory: PleurodesisDto[];
    rgTbLungDiseaseSeverity: LungTBTransplantDiseaseSeverity[];
    rgTbLungCurMgmtNonPharma: LungCurMgmtNonPharmaDto[];
    rgTbLungTransIndication: LungTBTransIndicationDto[];
}

export interface PleurodesisDto {
  //runId: number;
  doneDate: Date;
  laterality: string;
  methodUsed: string;
  indication: string;
  remarks: string;
  isEditable: boolean;
}

export interface LungTBTransplantDiseaseSeverity {
  disId: number;
  score: number;
  classId: number;
  scoreDesc: string;
  remarks: string;
  isEditable: boolean;
}

export interface LungCurMgmtNonPharmaDto {
  lungId: number;
  runId: number;
  pharmaId: number;
  remarks: string;
  isEditable: boolean;
}

export interface LungTBTransIndicationDto {
  transId: number;
  lungId: number;
  indicationId: number;
  diseaseId: number;
  remarks: string;
  isEditable: boolean;
}



export interface StageTransplantData {
  runId: number;
  ckdFailureStage: number;
  dialysisYn: string;
  transplantYn: string;
  preEmptiveYn: string;
  transplantDatr: Date;
  transplantInst: number;
  transplantPlaceDesc: string;
  followupInst: number;
  followUpCountry: number;
  transplantCountry: number;
  transplantReadiness: string;
  transplantWillingness: string;
  packPerYear: number;
  quitDate: Date;
  smokeType: string;
}

export interface LiverCirrhosisStage {
  id: number;
  value: string;
}

export interface ResultDecorator {
  code: string;
  message: string;
  result: any;
  // add other properties if present in ResultDecorator
}

export interface LiverCurrMgmtMasterDto {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperDto[];
}

export interface LiverCurrMgmtSubTyperDto {
  id: number;
  prevId: number;
  value: string;
  active: string;
  remarksYn: string;
  name: string;
}

export interface LiverCurrMgmtUIState {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperUIState[];
}
export interface LiverCurrMgmtSubTyperUIState extends LiverCurrMgmtSubTyperDto {
  isSelected: boolean;
  dose1: number;
  dose2: number;
  date: Date | null;
  comments: string;
}

export interface LiverCurrMgmtSaveDto {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperSaveDto[];
}

export interface LiverCurrMgmtSubTyperSaveDto {
  id: number;
  prevId: number;
  value: string;
  active: string;
  remarksYn: string;
  name: string;
  dose1: number;
  dose2: number;
  date: string;
  comments: string;
}

export interface LiverComplicationDto {
  compId: number;
  liverId: number;
  paramId: number;
  paramYn: string;
  remarks: string;
  noOfTumors: number;
  tumorLength: number;
  milanCriteria: string;
  prevLocoTherapyYn: string;
  prevLocoTherapyDtls: string;
}

export interface LiverManagementDto {
  mgmtId: number;
  liverId: number;
  paramId: number;
  subId: number;
  dose: number;
  frequency: number;
  doneDate: Date;
  remarks: string;
}

export interface RgVwLiverProceduresDto {
  paramId: number;
  paramName: string;
}

export interface RgVwLiverDonorProceduresDto {
  procId: number;
  procedureName: string;
  shortName: string;
  active:string
}

export interface RgProcedureDetailsDto {
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface RgProcedureDetailsSaveDto {
  runId: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface ContactDetails {
  relationType: number;
  phone: number;
  email: string;
}

export interface ProcedureDetails {
  //runId?: number;
  //centralRegNo: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}


export interface LiverTransplantIndicationDto {
  transId: number;
  liverId: number;
  paramId: number;
  paramYn: string;
  remarks: string;
}

export class LiverListResult {

  public centralRegNo: number;
  public civilId: number;
  public fullname: string;

  public dob: Date;
  public age: number;
  public sex: string;
  public estCode: number;
  public walCode: number;
  public causeKidney: string;
  public stages: number;
  public dialysis: string;
  public transplant: string;
  public height: number;
  public width: number;
  public bmi: string;
  public bloodGroup: number;
}

export class LiverRegistryResultDto {
  public regNo: number;
  public civilId: number;
  public dateOfBirth: Date;
  public age: number;
  public fullName: string;
  public sex: string;
  public regInst: string;
  public walCode: string;
  public regCode: string;
  public weight: number;
  public height: number;
  public bloodGroup: number;
  public transplantYn: string;
  public transplantReadiness: string;
  public icd: string;
  public deathCase: number;
  public familyHistYn: string;
  public childPughScore: number;
  public childPughClass: string;
  public meldScore: number;
  public meldNaScore: number;
  public meldExceptionYn: string;
  public meldExceptionDtls: string;
  public peldScore: number;
  public peldExceptionYn: string;
  public peldExceptionDtls: string;
}

export interface LungTransplantIndication {
  paramId: number;
  paramName: string;
  checked: boolean;
  remarksYn: string;
  subOptions: LungDiseaseOption[];
  selectedDiseases: any[];
}

export interface LungDiseaseOption {
  id: number;
  name: string;
}

export interface LungTransplantDiseaseSeverity {
  paramId: number;
  paramName: string;
  checked: boolean;
  comments: string;
  score: number;
}
export interface LungCurrentMgmt {
  paramId: number;
  paramName: string;
   checked: boolean;
  comments: string;
}

export interface Medication {
  id: number;
  medicine: string;
  startDate: Date;
  dose: string;
  frequency: string;
  medicineType: string;
  isEditable: boolean;
}

export interface Pleurodesis {
  id: number;
  doneDate: Date;
  laterality: string;
  methodUsed: string;
  indication: string;
  remarks: string;
  isEditable: boolean;
}

export interface LungTBTransplantCurrentManagement{
  lungId: number;
  pharmaId: number;
  remarks: string;
}

export interface LungPleurodesisMethod {
  paramId: number;
  paramName: string;
}

export interface LungPleurodesisIndication {
  paramId: number;
  paramName: string;
}

export class LungListResult {
  regNo: number;
  civilId: number;
  dateOfBirth: Date;
  age: number;
  fullName: string;
  sex: string;
  regInst: number;
  walCode: number;
  regCode: number;
  weight: number;
  height: number;
  bloodGroup: string;
  transplantYn: string;
  transplantUrgent: string;
  transplantReadiness: string;
  icd: string;
  deathCase: number;
}

