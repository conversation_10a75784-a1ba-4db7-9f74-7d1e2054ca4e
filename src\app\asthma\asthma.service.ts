import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class AsthmaService {

  constructor(private _http: HttpClient) { }


  getAsthmaData(data): Observable<any> {
    return this._http.post(AppUtils.GET_ASTHMA_LIST, data);
  }

  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.ASTHMA_DASHBOARD);
  }

}
