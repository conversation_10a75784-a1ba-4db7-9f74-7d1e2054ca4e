import { Component, Input, OnInit, TemplateRef, ViewChild } from "@angular/core";


import { FormArray, FormBuilder, FormControl, FormControlName, FormGroup, Validators } from '@angular/forms';
import { ColumnApi, GridApi, GridOptions, RowNode } from 'ag-grid-community';

import { invalid } from '@angular/compiler/src/render3/view/util';
import { LoginService } from 'src/app/login/login.service';
import { TbBrainDeathReg } from 'src/app/_models/brain-death-determination-reg-model';
import { TbBrainDeathExamTran } from 'src/app/_models/brain-death-determination-tran-model';
import { data } from 'jquery';
import { Observable } from 'rxjs-compat';
import { Person } from 'src/app/_models/person.model';
import { PersonInfo } from 'src/app/_models/person-info-model';
import * as _ from 'lodash';
import { setRowElements } from '@syncfusion/ej2-angular-grids';
import { AlShifaLoginService } from 'src/app/alshifa/alShifaLogin.service';
import { PatientDetailsComponent } from "../_comments/patient-details/patient-details.component";
import Swal from "sweetalert2";
import * as CommonConstants from '../_helpers/common.constants';
import { formatDate } from '@angular/common';
import { Router } from "@angular/router";
import { MasterService } from "../_services/master.service";
import { HttpClient } from "@angular/common/http";
import { RegionDataModel } from "../common/objectModels/region-model";
import { AppComponent } from "../app.component";
import * as AppCompUtils from '../common/app.component-utils';
import * as AppUtils from 'src/app/common/app.utils';
import { ancRequestModel } from "../_models/ancRequestModel";
import * as moment from "moment";
import { AncRequestService } from "./ancRequest.service";
import { SharedService } from "../_services/shared.service";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { timeout } from "rxjs-compat/operator/timeout";
import { AncService } from "../anc/anc.service";





@Component({
  selector: 'anc-request-registration',
  templateUrl: './anc-request-registration.component.html',
  styleUrls: ['./anc-request-registration.component.scss']
})



export class AncRequestRegistrationComponent implements OnInit {

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('viewSmallANCWindow', { static: false }) public viewSmallANCWindow: TemplateRef<any>;
  searchCivilId: any;
  submitted = false;
  patientAge = 12;
  institeList: any[];
  institeListFilter: any[];
  regionData: RegionDataModel[];
  ancPatientForm: FormGroup;
  ancSearchKey: FormGroup;
  patientForm: FormGroup;
  institutes: any;
  ancRequestForm: FormGroup;
  public ancStatusOptions = [];
  rowData: Array<ancRequestModel> = new Array<ancRequestModel>();
  ancRequestList: Array<ancRequestModel> = new Array<ancRequestModel>();
  ancRequestModelReg: Array<ancRequestModel>;
  loginId: any;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  formData: ancRequestModel;
  ancFormDatateo: ancRequestModel;
  ancRequestF: Array<ancRequestModel>;
  personInfo: any = [];
  columnAncRequestList: any[];
  gridApi: any;
  allAncData: any;
  ancformDetails: any = [];
  ancFormData: any;
  ancNoSearch: any;
  ancSearch: any;
  selectedIndex: number = null;
  ancRequestRows: any = [];
  selectedRow: RowNode[] = [];
  ancDataGridApi: GridApi;
  requestId: any;
  conAnamPreChecked: boolean;
  institutesFilter: any[];
  theRequiredInt: any;
  submittedAncInst: boolean = false;
  patientInfoDetails = [];
  newReqId: boolean = true;
  savedOpenObject: any;
  requestedIdAfterSave: any = null;
  civilIdEntryType: any;
  defaultRequestedDate: Date ;
  @Input('hide-arrow') hideArrow: boolean = false;






  gridOptions1: GridOptions = <GridOptions>{
    enableColResize: true,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions1.api.sizeColumnsToFit();
    }
  }


  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,

  };
  pregnancyOutcome: any;
  menstrualCycle: any;

  getAncInstitute = (ancInstitute) => {
    if (ancInstitute.value) {
     let estName
     this.institutes.forEach(ele => {
       if (ancInstitute.value == ele.estCode){
         estName = ele.estName;
       }})
       return estName
    }else{
     return ' '
    }
   };

   getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };


   getStatusName = (data) => {
    if (data.value) {
      let statusName
      this.ancStatusOptions.forEach(ele => {
        if (data.value == ele.id){
          statusName = ele.value;
        }})
        return statusName
     }else{
      return ' '
     }
  };
  naviData: any;

  ngOnInit() {
    this.getMenstrualCycle();
    this.getPregnancyOutcome();
    this.ancStatusOptions = AppCompUtils.ANC_STATUS;
    this._masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];
    })

    this.requestedIdAfterSave = null;
    

    this.checkOutcome();


  }





  constructor(private _ancService: AncService, private _loginService: LoginService, private _alShifaLoginService: AlShifaLoginService, private _router: Router,
    private _ancReqService: AncRequestService, private modalService: NgbModal,
    private fb: FormBuilder, private _masterService: MasterService, private _http: HttpClient, private _sharedService: SharedService) {

    this.defaultRequestedDate = new Date();

    this.ancSearchKey = this.fb.group({
      ancSearch: []
    });

    this.patientForm = this.fb.group({
      'centralRegNo': [null],
      'patientId': [null, Validators.required],
      'civilId': [null, Validators.required],
      'dob': new FormControl(),
      'age': new FormControl(),
      'tribe': new FormControl(),
      'firstName': [null, Validators.required],
      'secondName': new FormControl(),
      'sex': new FormControl(),
      'thirdName': new FormControl(),
      'village': new FormControl(),
      'walayat': new FormControl(),
      'region': new FormControl(),
      'mobileNo': new FormControl(),
      'kinTelNo': new FormControl(),
      'careGiverTel': new FormControl(),
      'regInst': new FormControl(),
      'createdInstid': new FormControl(),
      'civilIdEntryType': new FormControl(),
      'maritalStatus': new FormControl(),
      // 'createdInstid': ["", Validators.required],
      'registerType': [9],
      'exDate': [""],
    });

    //ancDetails
    this.ancPatientForm = this.fb.group({
      'requestedDate': [this.defaultRequestedDate],
      'requestId': new FormControl(),
      'instCode': new FormControl(),
      'ancNoSearch': new FormControl(),
      'ancNo': new FormControl(),
      'gravida': new FormControl(),
      'parity': new FormControl(),
      'mensturalCycle': new FormControl(null),
      'mensturalLength': new FormControl(),
      'mensturalDuration': new FormControl(),
      'lmp': new FormControl(),
      'eddCalc': new FormControl(),
      'eddScan': new FormControl(),
      'pregnancyStatus': new FormControl(),
      'lastDeliveryDate': new FormControl(),
      'statusInLastPregnancy': new FormControl(null),
      'lastAbortionDate': new FormControl(),
      'status': new FormControl(),
      'requestedInstRemarks': new FormControl(),
      'congAnamoliesYN': new FormControl(),
      'estName': new FormControl(),
      'ancInstitute': new FormControl(),
      'requestedInst': new FormControl(),

    });

    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode;

    if (this._sharedService.getNavigationData()) {
      this.naviData = this._sharedService.getNavigationData();
      this.ancNoSearch = this._sharedService.getNavigationData().ancNo;
      this.ancSearchKey.controls.ancSearch.patchValue(this.ancNoSearch);
      this.search();
      this._sharedService.setNavigationData(null);
    }

  }

  getPregnancyOutcome() {
    this._ancService.getPregnancyOutcome().subscribe(res => {
      this.pregnancyOutcome = res["result"];
    })
  }

  getMenstrualCycle() {
    this._ancService.getMensturalCycle().subscribe(res => {
      this.menstrualCycle = res["result"];
    })
  }

  getAncList(civilId: any, callType: any = null) {
    this._ancReqService.getAnc(civilId, null).subscribe(async res => {

      this.ancRequestList = res['result'].rgTbAncRequest;

    })

    this.columnAncRequestList = [
      { headerName: ' ', width: 50, checkboxSelection: true },
      { headerName: 'ANC NO.', field: 'ancNo', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'ANC Institute', field: 'ancInstitute', minWidth: 125, sortable: true, sort: 'asc' },
      { headerName: 'Request Date', field: 'requestedDate', minWidth: 125, sortable: true, sort: 'asc' },
    ];

  }


  public onGridReady(params: any) {
    this.ancDataGridApi = params.api;
    this.gridApi = params.api;
  }




  columnDefs = [
    { headerName: 'ANC No.', field: 'ancNo', minWidth: 100, sortable: true },
    { headerName: 'ANC Institute', field: 'ancInstitute', minWidth: 125, sortable: true, cellRenderer:this.getAncInstitute },
    { headerName: 'Gravida', field: 'gravida', minWidth: 99, sortable: true },
    { headerName: 'Parity', field: 'parity', minWidth: 99, sortable: true },
    { headerName: 'Request Date', field: 'requestedDate', minWidth: 100, sortable: true, cellRenderer:this.getDateFormat },
    { headerName: 'Status', field: 'status', minWidth: 100, sortable: true, cellRenderer:this.getStatusName},
    { headerName: 'Remarks', field: 'requestedInstRemarks', minWidth: 140, sortable: true }

  ];


  get ancRequestFormt() { return this.ancPatientForm.value; }
  get f() { return this.ancRequestForm.controls; }
  get mandPati() { return this.patientDetails.f; }





  clearData() {
    this.rowData = [];
    this.ancSearchKey.reset();
    this.patientDetails.clear();
    this.ancPatientForm.reset({'requestedDate':new Date()});
    // this.ancSearch.value = '';
    this.submitted = false;
    this.requestedIdAfterSave = null;
    
    
   
  }


  ifChecked(e) {
    if (e.target.checked == true) {
      this.ancRequestFormt.congAnamoliesYN = "Y";
    } else {
      this.ancRequestFormt.congAnamoliesYN = "N";
    }

  }


  validateFields() {
    if (!this.mandPati.firstName.value || !this.mandPati.secondName.value ||
      !this.mandPati.civilId.value || !this.mandPati.patientId.value || !this.mandPati.regInst.value) {
      Swal.fire('Alert', 'Mandatory fields in Patient Details cannot be empty', 'warning');
      return false;
    } else {
      return true;
    }
  }

  ancInstituteValidation() {
    if (!this.ancRequestFormt.ancInstitute) {
      this.submittedAncInst = true;
      return false;
    } else {
      this.submittedAncInst = false;
      return true;

    }

  }



  saveAncData() {
    
    let patientData = this.ancPatientForm.value;
    this.submitted = true;

    if (!this.patientDetails.validateFields()) {
      Swal.fire('Alert', ' Mandatory fields cannot be empty', 'warning');
      return;
    }

    let createdBy;
    let createdDate;
    let modifiedBy;
    let modifiedOn;
    let centralRegNo;

    //change it ......
    if (this.savedOpenObject) {
      if (this.savedOpenObject.requestId != null) {
        createdBy = this.allAncData.rgTbPatientInfo.createdBy;
        createdDate = this.allAncData.rgTbPatientInfo.createdOn;
        modifiedBy = this.loginId;
        modifiedOn = this.currentDate;
      }

    } else {
      createdBy = this.loginId;
      createdDate = this.currentDate;
      modifiedBy = null;
      modifiedOn = null;
    }

    this.ancRequestFormt.civilId = this.patientDetails.f.civilId.value;

    if (this.patientDetails.f.regInst.value) {
      this.ancRequestFormt.requestedInst = null;
      this.ancRequestFormt.requestedInst = this.patientDetails.f.regInst.value;
      this.patientDetails.f.createdInstid = this.ancRequestFormt.requestedInst;
    }

    if (this.ancRequestFormt.congAnamoliesYN) {
      this.ancRequestFormt.congAnamoliesYN = "Y";
    } else {
      this.ancRequestFormt.congAnamoliesYN = "N";
    }

    if (this.requestedIdAfterSave) patientData['requestId'] = this.requestedIdAfterSave;
    if (!this.civilIdEntryType) {
      if (this.allAncData) {
        if (this.allAncData.rgTbPatientInfo) {
          this.civilIdEntryType = this.allAncData.rgTbPatientInfo.civilIdEntryType;
        }
      } else {
        this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
      }
    }

    if (this.requestedIdAfterSave) patientData['requestId'] = this.requestedIdAfterSave;

    let saveData = {
      rgTbPatientInfo: {
        patientId: this.patientDetails.f.patientId.value,
        civilId: this.patientDetails.f.civilId.value,
        createdBy: createdBy,
        createdOn: createdDate,
        dob: this.patientDetails.f.dob.value ? moment(this.patientDetails.f.dob.value).format("DD-MM-yyyy") : null,
        firstName: this.patientDetails.f.firstName.value,
        lastUpdatedBy: modifiedBy,
        lastUpdatedDate: modifiedOn,
        mobileNo: this.patientDetails.f.mobileNo.value,
        regInst: this.patientDetails.f.regInst.value,
        createdInstid: this.patientDetails.f.regInst.value,
        kinTelNo: this.patientDetails.f.kinTelNo.value,
        secondName: this.patientDetails.f.secondName.value,
        civilIdEntryType: this.civilIdEntryType,
        sex: this.patientDetails.f.sex.value,
        thirdName: this.patientDetails.f.thirdName.value,
        tribe: this.patientDetails.f.tribe.value,
        village: this.patientDetails.f.village.value,
        maritalStatus: this.patientDetails.f.maritalStatus.value,
      },
      rgTbAncRequestData: patientData
    }

    if (!this.validateFields()) return;
    if (!this.ancInstituteValidation()) return;

    this._ancReqService.saveAnc(saveData).subscribe(res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE_SAVE) {
        Swal.fire('Saved!', 'ANC details Saved successfully.', 'success');

        this.savedOpenObject = res['result'];

        this.requestedIdAfterSave = res['result'].requestId;

        this.getAncDetailsByRequestId(this.requestedIdAfterSave);

      } else if (res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        Swal.fire('Error!', res['message'], 'error');

      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving ANC Request Details ' + err.message, 'error')
    })

  }



  ngAfterViewInit() {
    setTimeout(() => {

      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaEstCode: any;
        let alShifaValCode: any;
        let alShifaCivilID: any;
        let alShifaPatientId: any;

        this._alShifaLoginService.getAlShifanData().forEach(element => {
          if (element["civilId"]) {
            alShifaCivilID = element["civilId"]
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          } else if (element["patientId"]) {
            alShifaPatientId = element["patientId"];
          }
        });

        if (alShifaCivilID) {


          this.patientDetails.getEstById(alShifaEstCode);
          this.searchCivilId = alShifaCivilID;
          //  this.patientDetails.updateInstitutesList(alShifaEstCode);

          this.getAncdata(this.searchCivilId, AppUtils.CALL_TYPE_AL_SHIFA);
          this.patientDetails.patientForm.patchValue({ createdInstid: alShifaEstCode });
          this.patientDetails.patientForm.patchValue({ patientId: alShifaPatientId });
        }


      }
    }, 1000);
  }

  search() {

    this.ancSearch = this.ancSearchKey.controls.ancSearch.value;
    // this.patientForm["controls"]["ancNoSearch"].value
    if (this.ancSearch) {
      setTimeout(() => {
        //get by anc No 
        this.getAncdataByAncNo(this.ancSearch);
        this.ancNoSearch = "";
      }, 1000);

    } else if (this.naviData.requestId){
      this.getAncDetailsByRequestId(this.naviData.requestId);
    }else {
      this.ancNoSearch = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Anc Number'
      });
    }
  }

  displayPatientDemographic() {
    this.patientInfoDetails.push(this.allAncData.rgTbPatientInfo);
    this.patientInfoDetails.forEach(e => {
      e.estCode = e.createdInstid;
      e.estName = this.institutes.filter(s => e.estCode == s.estCode).map(s => s.estName).toString();

      this.patientDetails.setPatientDetails(this.allAncData);
      this.patientForm.patchValue({ 'regInst': e.createdInstid, 'estName': e.estName });
    })
  }




  onReady(params: any) {
    this.ancDataGridApi = params.api;
  }

  selectedAncNo() {
    this.selectedRow = this.ancDataGridApi.getSelectedNodes()[0].data;
    this.requestId = this.selectedRow["requestId"];
    this.getAncDetailsByRequestId(this.requestId);
  }


  // Open dialgue box 
  openSmallWindow() {
    this.modalService.open(this.viewSmallANCWindow, { size: <any>'lg' });
    this.getAncList(this.patientDetails.f.civilId.value, null);
  }

  clearAllAncData() {
    this.newReqId = false;
    this.allAncData.rgTbAncRequestData = [];
    this.requestedIdAfterSave = null;
    this.modalService.dismissAll(this.viewSmallANCWindow);
    this.ancPatientForm.reset({'requestedDate':new Date()});
    if (this.allAncData) {
      this.rowData = this.allAncData.rgTbAncRequestDetails;
    }


  }


  callMpiMethod() {
    this.getAncdata(this.patientDetails.f.civilId.value);
  }

  getAncdata(civilId: any, callType: any = null) {

    let msg;
    let callMPI = true;
    if (callType == 'RegNo') {
      msg = "No Record Found with Entered Regstration No.";
    } else if (callType == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        msg = "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP."
      }
    }

    this._ancReqService.getAnc(civilId, callType).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.allAncData = res['result'];

        if (this.allAncData.rgTbAncRequest.length > 0) {
          this.openSmallWindow();
        }

        this.displayPatientDemographic();

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire(
          '', "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.", 'warning',
        ).then((result) => {
          if (callMPI) {
            this._sharedService.setPatientData(this.patientDetails);
            this._sharedService.fetchMpi(civilId).subscribe(civilIdEntryType => {
              this.civilIdEntryType = civilIdEntryType !== false ? civilIdEntryType : AppUtils.CIVILID_ENTERY_MANUALLY;
            });
          }

        })
      } else {
        Swal.fire('', res['message'], 'error')
      }
    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });


  }


  get fancDateFormat() { return this.allAncData.rgTbAncRequestData; }



  getAncdataByAncNo(ancSearchNum: any) {
    this._ancReqService.getAncDataByAncNo(ancSearchNum).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.modalService.dismissAll(this.viewSmallANCWindow);
        this.allAncData = res['result'];

        this.displayPatientDemographic();

        this.fancDateFormat.requestedDate = new Date(this.fancDateFormat.requestedDate);
        this.fancDateFormat.lastDeliveryDate = new Date(this.fancDateFormat.lastDeliveryDate);
        this.fancDateFormat.lastAbortionDate = new Date(this.fancDateFormat.lastAbortionDate);
        this.fancDateFormat.lmp = new Date(this.fancDateFormat.lmp);
        this.fancDateFormat.eddCalc = new Date(this.fancDateFormat.eddCalc);
        this.fancDateFormat.eddScan = new Date(this.fancDateFormat.eddScan);

        if (this.allAncData.rgTbAncRequestData.congAnamoliesYN == "Y") {
          this.conAnamPreChecked = true;
        } else {
          this.conAnamPreChecked = false;
        }



        this.ancPatientForm.patchValue(this.allAncData.rgTbAncRequestData);

        this.rowData = this.allAncData.rgTbAncRequestDetails;
        this.checkOutcome()



      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Entered anc No", 'warning');

      } else {
        Swal.fire(' ', 'The entered Anc Number does not match any existing data. Please double-check and re-enter the correct AncNo.', 'warning')
      }

    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });
  }

  getAncDetailsByRequestId(requestId: any) {
    this._ancReqService.getAncDataByRequestId(requestId).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.modalService.dismissAll(this.viewSmallANCWindow);
        this.allAncData = res['result'];

        this.displayPatientDemographic();

        this.fancDateFormat.requestedDate = new Date(this.fancDateFormat.requestedDate);
        this.fancDateFormat.lastDeliveryDate = new Date(this.fancDateFormat.lastDeliveryDate);
        this.fancDateFormat.lastAbortionDate = new Date(this.fancDateFormat.lastAbortionDate);
        this.fancDateFormat.lmp = new Date(this.fancDateFormat.lmp);
        this.fancDateFormat.eddCalc = new Date(this.fancDateFormat.eddCalc);
        this.fancDateFormat.eddScan = new Date(this.fancDateFormat.eddScan);

        if (!this.civilIdEntryType) {
          if (this.allAncData) {
            if (this.allAncData.rgTbPatientInfo) {
              this.civilIdEntryType = this.allAncData.rgTbPatientInfo.civilIdEntryType;
            }
          } else {
            this.civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
          }
        }


        if (this.fancDateFormat.congAnamoliesYN == "Y") {
          this.conAnamPreChecked = true;
        } else {
          this.conAnamPreChecked = false;
        }

        this.ancPatientForm.patchValue(this.allAncData.rgTbAncRequestData);

        if (this.allAncData.rgTbAncRequestData.congAnamoliesYN){
          this.ancPatientForm.patchValue(this.allAncData.rgTbAncRequestData.congAnamoliesYN);
        }
        
        this.rowData = this.allAncData.rgTbAncRequestDetails;

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        await Swal.fire('', "No Record Found with Selected Requested Id ", 'warning');

      } else {
        Swal.fire(' ', 'The Requested Id does not match any existing data. Please double-check the Requested Id .', 'warning')
      }

    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });


  }

  checkOutcome(){
    if (this.ancPatientForm.controls.statusInLastPregnancy.value == 152){
      this.ancPatientForm.controls.lastAbortionDate.enable();
    }
    else{
      this.ancPatientForm.controls.lastAbortionDate.disable();
      this.ancPatientForm.controls.lastAbortionDate.patchValue(null)
    }
    
  }

}



