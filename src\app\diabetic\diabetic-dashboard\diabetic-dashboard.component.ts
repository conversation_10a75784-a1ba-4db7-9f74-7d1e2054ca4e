import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { WallayatDataModel } from 'src/app/common/objectModels/wallayat-model';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import * as CommonConstants from 'src/app/_helpers/common.constants';
import * as AppComponent from '../../common/app.component-utils';
import { DiabeticDashboard } from 'src/app/_models/diabetic-dashboard.model';
import { DiabeticDashboardDisplay } from 'src/app/_models/diabetic-dashboard-display.model';
import * as __ from 'lodash'
import * as AppUtils from '../../common/app.utils';
import * as AppCompUtils from '../../common/app.component-utils';
import { DiabeticService } from '../diabeticService';

@Component({
  selector: 'app-diabetic-dashboard',
  templateUrl: './diabetic-dashboard.component.html',
  styleUrls: ['./diabetic-dashboard.component.scss']
})
export class DiabeticDashboardComponent implements OnInit {

  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: DiabeticDashboard[];
  DashboardDataFilter: DiabeticDashboard[] = [];
  displayDiabeticType: DiabeticDashboardDisplay[];
  displayDiabeticHba1c: DiabeticDashboardDisplay[];
  displayDiabeticKetones: DiabeticDashboardDisplay[];
  displayNewAndOld: DiabeticDashboardDisplay[];
  displayGender: DiabeticDashboardDisplay[];
  displayModeOfPresentation: DiabeticDashboardDisplay[];
  diabetesTypesList: any;
  diabetesSubTypesList: any;
  diabetesHba1cList: any;
  diabeticKetonesList: any;
  newOldList: any;
  createTime: any;
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  optionsPercentage: any;
  charBGColor: any[];
  diabeticTypeChart: any;
  diabeticHba1cChart: any;
  diabeticKetonesChart: any;
  NewAndOldChart: any;
  genderChart: any;
  modeOfPresentationChart: any;
  criteria: any;
  ageTypeData: any;
  bpData: any;
  displayAgeTypeData: DiabeticDashboardDisplay[];
  displayBpData: any[];
  basicData: any;
  basicOptions: any;
  ageChartLabel: any[];
  ageWiseBpData: any;
  monthsName = AppComponent.MONTHS_NAME;
  diabetsTypeMaster: any;
  selectedRegYear: any;
  showCharts: boolean;
  modeOfPrestMaster: any;
  years: { label: string; value: number }[] = [];
  constructor(private formBuilder: FormBuilder, private _masterService: MasterService, private _sharedService: SharedService, private _diabeticService: DiabeticService) {

    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'regMonth': [null],
      'regYear': [null],
      'ageFrom': [null],
      'ageTo': [null],
    });
    this.getMasterData();
    this.options = AppComponent.CHAR_OPTION_2;
    this.optionsPercentage = AppComponent.CHAR_GENERAL_OPTION_PERCENTAGE;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;
    this.newOldList = AppComponent.NEW_OLD;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
    this.generateYears();
  }
  generateYears() {
    const currentYear = new Date().getFullYear(); // Fixed starting year
    const startYear = currentYear - 10; // 10 years back
  
    this.years = [];
    for (let year = currentYear; year >= startYear; year--) {
      this.years.push({ label: year.toString(), value: year });
    }
  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._diabeticService.getMinMaxYear().subscribe(res => {
      let data = res.result;
      this.getDashboardData();
    })

    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });

    this._masterService.getDiabetesTypes().subscribe(value => {
      this.diabetsTypeMaster = value["result"];
    });

    this._masterService.getAllDiabeticSubtypes().subscribe(res => {
      this.diabetesSubTypesList = res.result;
      
    })

    this._masterService.getAllDiabeticDashboard().subscribe(res => {
      this.diabetesHba1cList = res.result;
      this.diabeticKetonesList = res.result;
    })

    this._masterService.getAllDiabetesPresMod().subscribe(value => {
      this.modeOfPrestMaster = value["result"];
    });
 
  }

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }

  /* ------------  filter action   ---------------- */
  getDashboardData() {
    let regYear = this.boardForm.controls.regYear.value
    if (!regYear) {
      let yearValues = [...this.years.map(el => el.value)]
      let maxYear = Math.max(...yearValues);
      this.boardForm.controls.regYear.setValue(maxYear)
      regYear = maxYear;

    }
    this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    this._diabeticService.getDashboardByYear(regYear).subscribe(res => {


      if (res['code'] == "S0000" || res['code'] == "F0000") {


        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'DIABETIC', this.institeList);

        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }

      }

    })
    this.selectedRegYear = regYear

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    this.showCharts = true;
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
    } else {
      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageFrom']) === true && this.isEmptyNullZero(body['ageTo']) === true && body['ageFrom'] < body['ageTo']) {
        let tmpAgelist = [];
        for (var n = body['ageFrom']; n <= body['ageTo']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }

        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['regMonth'] != undefined || body['regMonth'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regMonth == body['regMonth']);
      }

      if (body['regYear'] != undefined || body['regYear'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regYear == body['regYear']);
      }


      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

  displayFilterType() {

    if (this.filterType === "institute") {
      this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);

    } else if (this.filterType === "wilayat") {
      this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);

    } else if (this.filterType === "region") {
      this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

    }
    else {
      this.filterTitle = 'All Regions';
    }

  }

  setChartData() {
    this.displayAgeTypeData = [];
    this.displayBpData = [];
    this.displayDiabeticType = [];
    this.displayDiabeticHba1c = [];
    this.displayDiabeticKetones = [];
    this.displayNewAndOld = [];
    this.displayGender = [];
    this.displayModeOfPresentation = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {

      let dtype = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].diabetesType };
      this.displayDiabeticType.push(dtype);
      this.displayDiabeticHba1c.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].hba1c});
      this.displayDiabeticKetones.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].ketones});


      this.displayGender.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].sex == "M" ? "Male" : this.DashboardDataFilter[i].sex == "F" ? "Female" : null });

      this.displayModeOfPresentation.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].modePresentation });

    }

    this.callChart();
  }

  callChart() {

    this.CallBarChart(this.displayDiabeticType, "diabeticTypeChart", "N");
    this.CallBarChart(this.displayDiabeticHba1c, "diabeticHba1cChart", "N");
    this.CallBarChart(this.displayDiabeticKetones, "diabeticKetonesChart", "N");
    this.CallBarChart(this.displayGender, "genderChart", "N");
    this.CallBarChart(this.displayModeOfPresentation, "modeOfPresentationChart", "N");
  }



  CallBarChart(listData: DiabeticDashboardDisplay[], chartData: any, withNull: any) {

    let charlabels = [];
    let charData = [];
    let listGroup = [];

    // if need data with null , select "Y" 
    if (withNull == "N") {
      listData = listData.filter(s => s.value != null);
    }



    for (var n = 0; n < listData.length; n++) {

      if (listGroup.filter(s => s.icd === listData[n].value).length == 0) {
        const result = listData.filter(s => s.value == listData[n].value).length;
        let a = { icd: listData[n].value }
        if (chartData == "diabeticTypeChart") {
          let debTypeName = ''
          this.diabetsTypeMaster.forEach((item) => {

            if (item.id == listData[n].value) {
              debTypeName = item.value

            }

          });
          charlabels.push(debTypeName);


          
        } else if (chartData == "diabeticHba1cChart") {
          let debHba1c = ''
          this.diabetesHba1cList.forEach((item) => {

            if (item.hba1c == listData[n].value) {
              if(item.hba1c < 7){
                debHba1c = "HBA1C < 7"
              }else if(item.hba1c > 8){
                debHba1c ="HBA1C > 8"
              }else {
                debHba1c ="HBA1C 7-8"
              }

            }

          });
          charlabels.push(debHba1c);
        }

        else if (chartData == "diabeticKetonesChart") {
          let debKetonesName = ''
          this.diabeticKetonesList.forEach((item) => {

            if (item.ketones == listData[n].value) {
              debKetonesName = item.ketones;

            }

          });
          charlabels.push(debKetonesName);
        } else if (chartData == "modeOfPresentationChart") {
          let modOfPre = ''
          this.modeOfPrestMaster.forEach((item) => {

            if (item.id == listData[n].value) {
              modOfPre = item.value

            }

          });
          charlabels.push(modOfPre);
        } else {
          charlabels.push(listData[n].value);
        }
        charData.push(result);
        listGroup.push(a);
      }
    }


    if (chartData == "diabeticTypeChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
      this.diabeticTypeChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "diabeticHba1cChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
    
      // Merge duplicate labels
      let mergedData = {};
      charlabels.forEach((label, index) => {
        if (mergedData[label]) {
          mergedData[label] += charData[index]; // Sum values of duplicate labels
        } else {
          mergedData[label] = charData[index];
        }
      });
    
      // Convert merged data back into arrays
      charlabels = Object.keys(mergedData);
      charData = Object.values(mergedData);
    
      this.diabeticHba1cChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      };
    }

    if (chartData == "diabeticKetonesChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
      this.diabeticKetonesChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "NewAndOldChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
      this.NewAndOldChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "genderChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
      this.genderChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }

    if (chartData == "modeOfPresentationChart") {
      this.charBGColor.sort(() => Math.random() - 0.2);
      this.modeOfPresentationChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }

        ]
      }
    }
    //

  }

  callPieChart(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";

    if (this.filterType === "institute") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);


    //NewAndOldChart
    if (chartData == "NewAndOldChart") {
      this.NewAndOldChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

  }





  callPieChartSpecLabel(listData: any[], chartData: any, chartLabelData: any) {

    let data = Array.from(new Set(listData.map(a => a.centralRegNo)))
      .map(id => {
        return listData.find(a => a.centralRegNo === id)
      })

    let groupByName = [];
    let charTitle: any = "";

    chartLabelData.forEach(function (a) {
      groupByName.push({ "label": a.value, "count": data.filter(s => s.value == a.id).length })
    })

    this.pieOption.title = {
      display: false,
      text: charTitle,
      fontSize: 15
    }

    if (chartData == "this.diabeticTypeChart") {
      this.diabeticTypeChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }

    if (chartData == "this.NewAndOldChart") {
      this.NewAndOldChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
  }

  callChartAgeWiseType() {
    let charlabels = [];
    let charData = [];
    let chartArray = [];

    this.displayAgeTypeData.forEach(element => {
      if (chartArray.filter(s => s.centralRegNo == element.centralRegNo).length == 0) {
        chartArray.push(element);
      }
    })

    let min: number;
    let max: number;
    min = Math.min.apply(Math, chartArray.map(function (o) { return o.value; }));
    max = Math.max.apply(Math, chartArray.map(function (o) { return o.value; }));

    let loopRing: number = 10;
    let newMin: number = min;
    let loopMin: number;
    let loopMax: number;

    while (newMin < max + 1) {
      let totalCount: number = 0;
      loopMin = newMin;
      loopMax = newMin + loopRing;
      let result;
      for (var n = loopMin; n <= loopMax; n++) {
        result = chartArray.filter(s => s.value == n).length;
        totalCount = totalCount + result;
      }
      if (totalCount > 0) {
        if (loopMax > max) {
          let d = loopMin + " : " + max;
          charlabels.push(d);
          charData.push(totalCount);
        }
        else {
          let d = loopMin + " : " + loopMax;
          charlabels.push(d);
          charData.push(totalCount);
        }
      }
      newMin = loopMax + 1;
    }

    this.charBGColor.sort(() => Math.random() - 0.2);
    this.ageTypeData = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }
    this.ageChartLabel = charlabels;
  }


  ageWiseBp() {
    let charlabels = [];
    let charDataControlled = [];
    let charDataUncontrolled = [];

    let chartArray = [];

    this.displayBpData.forEach(element => {
      if (chartArray.filter(s => s.centralRegNo == element.centralRegNo).length == 0) {
        chartArray.push(element);
      }
    })


    let min: number;
    let max: number;
    min = Math.min.apply(Math, chartArray.map(function (o) { return o.age; }));
    max = Math.max.apply(Math, chartArray.map(function (o) { return o.age; }));

    let loopRing: number = 10;
    let newMin: number = min;
    let loopMin: number;
    let loopMax: number;

    while (newMin < max + 1) {
      let totalControlled: number = 0;
      let totalUncontrolled: number = 0;
      loopMin = newMin;
      loopMax = newMin + loopRing;
      let resultControlled;
      let resultUncontrolled;
      for (var n = loopMin; n <= loopMax; n++) {
        resultControlled = chartArray.filter(s => s.age == n && s.bp == 1).length;
        totalControlled = totalControlled + resultControlled;
        resultUncontrolled = chartArray.filter(s => s.age == n && s.bp == 2).length;
        totalUncontrolled = totalUncontrolled + resultUncontrolled;
      }
      if (totalControlled > 0 || totalUncontrolled > 0) {
        if (loopMax > max) {
          let d = loopMin + " : " + max;
          charlabels.push(d);
          charDataControlled.push(Math.round((totalControlled / this.DashboardDataFilter.length) * 100));
          charDataUncontrolled.push(Math.round((totalUncontrolled / this.DashboardDataFilter.length) * 100));
        }
        else {
          let d = loopMin + " : " + loopMax;
          charlabels.push(d);
          charDataControlled.push(Math.round((totalControlled / this.DashboardDataFilter.length) * 100));
          charDataUncontrolled.push(Math.round((totalUncontrolled / this.DashboardDataFilter.length) * 100));
        }
      }
      newMin = loopMax + 1;
    }

    this.charBGColor.sort(() => Math.random() - 0.2);
    this.optionsPercentage.title = {
      display: true,
      fontSize: 15
    }
    this.ageWiseBpData = {
      labels: charlabels,
      datasets: [
        {
          label: 'Controlled',
          backgroundColor: '#1E88E5',
          borderColor: '#1E88E5',
          fontSize: 13,
          data: charDataControlled,
        },
        {
          label: 'Uncontrolled',
          backgroundColor: '#FFA726',
          borderColor: '#FFA726',
          fontSize: 13,
          data: charDataUncontrolled,
        }
      ]
    }
  }

  clear() {
    this.showCharts = false;
    this.criteria = null;
    this.boardForm.reset();
    this.DashboardDataFilter = null;
    setTimeout(() => {
      this.boardForm.controls.regYear.setValue(this.selectedRegYear);
      this.boardForm.controls['regYear'].enable();
    }, 0);
  }


}

