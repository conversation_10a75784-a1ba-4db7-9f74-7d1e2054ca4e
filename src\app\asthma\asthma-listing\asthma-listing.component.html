<h6 class="page-title">Asthma Listing </h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="asthmaListingForm">
        <div class="row">
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Civil ID </label>
                    <input type="number" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Registration No.</label>
                    <input type="number" class="form-control form-control-sm" formControlName="central_Reg">
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Governorate</label>
                    <ng-select appendTo="body" [items]="regions" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="regSelect($event,'regCode')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="wal_code"
                        (change)="walSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Institutes </label>
                    <ng-select [items]="asthmainstitutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="reg_Inst"
                        (change)="changeAncInstitute($event)">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>File Status</label>
                    <ng-select [items]="fileStatusOption" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="file_Status">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>



                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <label>Visit Date</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="last_Visit_DateFrom"
                                monthNavigator="true" dateFormat="{{ dateFormat }}" yearNavigator="true"
                                placeholder="From" yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="last_Visit_DateTo" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <label>Newly registered</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="reg_DateFrom" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="From"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="reg_DateTo" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <label>Active Patients</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="last_Visit_DateFrom"
                                monthNavigator="true" dateFormat="{{ dateFormat }}" yearNavigator="true"
                                placeholder="From" yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="last_Visit_DateTo" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true"
                        placeholder="Select" bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>Age From</label>
                            <input type="text" class="form-control form-control-sm" formControlName="ageFrom">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>Age To</label>
                            <input type="text" class="form-control form-control-sm" formControlName="ageTo">
                        </div>
                    </div>
                </div>
            </div>           

            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality">
                        <option selected [value]="null">Select </option>
                        <option [value]="res.natCode" *ngFor="let res of nationalityList">{{res.nationality}}</option>
                    </select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Smoking Status</label>
                    <ng-select #entryPoint appendTo="body" [items]="smokingStatusOption" [virtualScroll]="true"
                        placeholder="Select" bindLabel="value" bindValue="id" formControlName="last_Smoking_status">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{
                            item.value }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>BMI</label>

                    <ng-select #entryPoint [items]="bmiList" [virtualScroll]="true" placeholder="Select"
                        (change)="pickLowAndHighValues($event)" bindLabel="value" bindValue="id" formControlName="bmi">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Patient on medication</label>
                    <ng-select #entryPoint [items]="MedIdList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="med_Id">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Inhaler Device</label>

                    <ng-select #entryPoint [items]="InhalerList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="dev_Id">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-6 col-xl-4">
                Inhalation Techniques
                <div class="radio-group">
                    <div class="row">
                        <div class="col-sm-4 br-1">
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" formControlName="dev_Good" value="Y" name="test">
                                    Good
                                </label>
                            </div>
                        </div>
                        <div class="col-sm-4 br-1">
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" formControlName="dev_Poor" value="Y" name="test">
                                    Poor
                                </label>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" formControlName="dev_Unchecked"
                                        value="Y" name="test"> Unchecked
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-4">
                <div class="border-box">
                    <h6 class="box-title">Co-morbidities</h6>
                    <div class="chkbx-list w-30" *ngFor="let item of comorbiditieList; let i = index">
                        <input  type="checkbox" formControlName="morbidities" [value]="item.value" (change)="selectItem($event,i,comorbiditieList)"> {{
                        item.label }}
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-xl-4">
                <div class="border-box">
                    <h6 class="box-title">Triggering Factors</h6>
                    <div class="chkbx-list w-25" *ngFor="let item of triggeringFactors ; let i = index">
                        <input type="checkbox" formControlName="triggeringFactors" [value]="item.value" (change)="selectItem($event,i,triggeringFactors)">
                        {{ item.label }}
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-4">
                <div class="border-box">
                    <h6 class="box-title">History</h6>
                    <div class="chkbx-list w-50" *ngFor="let item of history ; let i = index">
                        <input type="checkbox" formControlName="history"[value]="item.value" (change)="selectItem($event,i,history)"> {{
                        item.label }}

                    </div>
                </div>
            </div>

            <div class="text-right col-sm-12">
                <div class="btn-box">
                    <button type="button" (click)="exportExcel()" class="btn btn-primary">EXCEL</button>
                    <button type="button" (click)="clear()" class="btn btn-sm btn-sm btn-secondary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-primary">Search</button>
                </div>
            </div>
        </div>

    </form>

    <div class="grid-panel">
        <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
            [columnDefs]="columnDefs" [gridOptions]="gridOptions">
        </ag-grid-angular>

    </div>