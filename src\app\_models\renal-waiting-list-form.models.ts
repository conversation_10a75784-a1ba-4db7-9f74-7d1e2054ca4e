import { TbPatientInfo } from './patient-info.model';
import { RenalWaitingList } from './renal-waiting-list.model';

export class RenalWaitingListFrom {
    public centralRegNo: number;
    public activeYn: string;
    public civilId: number;
    public completedOn: any;
    public createdBy: number;
    public createdOn: any;
    public instRegDate: any;
    public modifiedBy: number;
    public modifiedOn: any;
    public regInst: number;
    public registerType: number;
    public localRegReferance: string;

    public rgTbPatientInfo: TbPatientInfo;
    public rgTbRenalWaitingList: Array<RenalWaitingList>;
	
}