import { Component, OnInit, Input, ViewChild, ViewChildren, ContentChild, Output,EventEmitter } from '@angular/core';
import { Table } from 'primeng/table';
// import { AngularFrameworkComponentWrapper } from 'ag-grid-angular/lib/angularFrameworkComponentWrapper';
import { combineAll } from 'rxjs/operators';
import { MasterService } from '../../_services/master.service';
import * as moment from 'moment';
import { HttpClient } from '@angular/common/http';
import { PatientDetailsComponent } from '../patient-details/patient-details.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { LoginService } from '../../login/login.service';
import * as AppUtils from '../../common/app.utils';
import Swal from 'sweetalert2';
import { FormArray } from '@angular/forms';
import { formatDate } from '@angular/common';
import * as CommonConstants from '../../_helpers/common.constants';
import { BehaviorSubject } from 'rxjs';
//import { EventEmitter } from 'events';
@Component({
  selector: 'app-vaccination',
  templateUrl: './vaccination.component.html',
  styleUrls: ['./vaccination.component.scss']
})
export class VaccinationComponent implements OnInit {

  @Input() showAddNewButton: boolean = true;
  @Input() showVaccineButton: boolean = true;
  @Output() downloadVaccination = new EventEmitter<void>();

  @Input() submitted = false;
  @Input() calledFromParent = false;
  public vaccinationForm: FormGroup;
  vacList: any[];
  vDate: Date;
  vName: string;
  vacValid: string;
  clonedResult: { [s: string]: any; } = {};
  institutes: any[];
  selectedinstitutes: any;
  civilIdInvalid: boolean;
  selectedVaccination: any;
  selectedDate: any;
  VaccinationView: any;
  vaccinMastList: any;
  today = new Date();
  delRow;
  data: any = [];
  vaccNewList: any[];
  vaccName: any[];
  disVaccineInfoalshifa: any;
  rgTbVaccinationInfo: any = [];
  vaccineFg: any = [];

  dispatientInfoalshifa: any;
  vaccienCivilId: any;
  loginId: any;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  /////////////////////////////////

  @Input() currentCivilId = '';
  @Input() patntId = '';
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  constructor(private _masterService: MasterService, private _http: HttpClient, private formBuilder: FormBuilder, private loginService: LoginService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

  }


  ngOnInit(): void {

    this.vaccinationForm = this.formBuilder.group({

      rgTbVaccinationInfo: this.formBuilder.array([]),

    }),


      this._masterService.getVaccinationMastList().subscribe(response => {
        this.vaccinMastList = response.result;
      }, error => {
      });


    this._masterService.institiutes.subscribe(res => {
      this.institutes = res["result"];

    })



  }

  ////////////////CIVIL ID







  ///////////////////P DATA TABLE



  /*
  runId;
  enteredBy;
  source;
  */

  onAddNewVaccine() {
    this.addNewVaccine('', this.loginId, '', '', '',  '',  this.currentCivilId, 'W', false);
    this.vaccineFg[this.vaccineFg.length - 1].isEditable = true;          //editable last entering row

  }

  addNewVaccine(runId: any = null, enteredBy: any = null, vaccinationDate: any = null, vaccineCode: any = null,  vaccinatedInst: any = null,  remarks: any = null, civilId: any = null, source: any = null, isEditable: any = false): void {
    this.rgTbVaccinationInfo = this.vaccinationForm.get('rgTbVaccinationInfo') as FormArray;

    this.vaccineFg = Object.assign([], this.rgTbVaccinationInfo.value);
    const vaccineItem: any = this.createVaccineItem(runId, enteredBy, vaccinationDate, vaccineCode,  vaccinatedInst,  remarks, civilId, source, isEditable);
    this.rgTbVaccinationInfo.push(this.createVaccineGrpItem(vaccineItem));

    this.vaccineFg.push(vaccineItem);
    // this.vaccineFg[this.vaccineFg.length - 1].isEditable = true;
  }

  createVaccineGrpItem(createVaccineItem: any): FormGroup {
    return this.formBuilder.group(createVaccineItem);
  }

  createVaccineItem(runId: any = null, enteredBy: any = null, vaccinationDate: any = null, vaccineCode: any = null,  vaccinatedInst: any = null, remarks: any = null, civilId: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      enteredBy: enteredBy,
      vaccinationDate: vaccinationDate,
      vaccineCode: vaccineCode,
      vaccinatedInst: vaccinatedInst,

      remarks: remarks,
      civilId: civilId,
      source: source,
      isEditable: isEditable
    };
  }
  // data.vaccineName = this.vaccinMastList.filter(s => s.vaccineId == data.vaccineCode).map( s=> s.vaccineName)[0];
  //   data.instName =

  getVaccineName(vaccineCode) {
    if (vaccineCode) {
      return this.vaccinMastList.filter(s => s.vaccineId == vaccineCode).map(s => s.vaccineName)[0];
    }
  }

  getVaccineInstName(vaccinatedInst) {
    if (vaccinatedInst) {
      return this.institutes.filter(s => s.estCode == vaccinatedInst).map(s => s.estName)[0];
    }

  }


  ///////////////////P DATA TABLE


  ngAfterViewInit() {
  }

  getVaccin(civilId: any) {

    this._masterService.getVaccinationCivilId(civilId).subscribe(response => {
      this.vacList = response.result;
      //console.log(this.vacList , "this.vacList");
      //let result: any = response.result;
      // console.log(response.result.length);
      /*
       if(response.result.length == 0){
         Swal.fire('Error!', 'There is no record found!', 'error');
       }else{
         this.vacList = response.result;
       }*/

    });
  }
  addNew() {

    if (!this.vaccNewList) {
      this.vaccNewList = [];
    }
    this.vaccNewList.push({ vaccinationDate: '', vaccineCode: '', vaccinatedInst: '', remarks: '' });
    this.vaccNewList[this.vaccNewList.length - 1].isEditable = true;
    //setTimeout(() => {
    //const buttons = document.getElementsByClassName('ui-button-info');
    // buttons[3].dispatchEvent(new Event('click'));
    //}, 50);
  }


  onRowEditInit(row: any) {
    row.isEditable = true;
    /*
    //this.clonedResult[item] = {...item};
    this.vacList.filter(row => row.isEditable).map(r => { r.isEditable = false; return r })
    row.isEditable = true;
    this.selectedinstitutes = row.vaccinatedInst;
   // console.log( this.selectedinstitutes," this.selectedinstitutes");
    this.selectedVaccination = row.vaccineCode;
    this.selectedDate = moment(row.vaccinationDate).format("DD-MM-YYYY h:mm:ss");*/

  }

  onRowEditSave(row: any) {

    // check if data is avaiable then only allow to save

  

    let rowIndex = this.vaccineFg.indexOf(row);
    this.vaccineFg[rowIndex] = this.vaccinationForm.value.rgTbVaccinationInfo[rowIndex];
    let data = this.vaccineFg[rowIndex];

    //console.log(data, "data");
    if (!data.vaccinationDate || !data.vaccineCode) {
      Swal.fire("Please fill vaccination date and vaccination name.");
      data.isEditable = true;
      return;
    }

    
    data.vaccineName = this.vaccinMastList.filter(s => s.vaccineId == data.vaccineCode).map(s => s.vaccineName)[0];
    data.instName = this.institutes.filter(s => s.estCode == data.vaccinatedInst).map(s => s.estName)[0];
    data.vaccinationDate = moment(data.vaccinationDate, "DD-MM-YYYY").format();
    //this.vaccineFg[rowIndex].entryDate = moment(this.selectedDate, "DD-MM-YYYY").format(); 
    data.isEditable = false;
    /*

    //delete this.clonedResult[row];
    row.vaccinatedInst = this.selectedinstitutes;
   // console.log("row.vaccinatedInst ",row.vaccinatedInst )
    row.vaccineCode = this.selectedVaccination;
    let t = this.vaccinMastList.filter(r => r.vaccineId == row.vaccineCode).map(r => r.vaccineName);
    row.vaccineNameList = this.vaccinMastList.filter(r => r.vaccineId == row.vaccineCode).map(r => r.vaccineName);
    row.estList = this.institutes.filter(r => r.estCode == row.vaccinatedInst).map(r => r.estName);
    //console.log(row.estList);
    row.vaccinationDate = moment(this.selectedDate, "DD-MM-YYYY").format();



    row.isEditable = false;

    if (row.runId == null) {
      row.civilId = this.tmpCivilId;
      row.enteredBy = this.tmpEnteredBy;
      this._http.post(AppUtils.VACCINATION_SAVE, row).subscribe(res => {
        if (res['code'] == 0) {
          Swal.fire('Saved!', 'Vaccin has been Saved.', 'success')
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }, err => {
        Swal.fire('Error!', 'Error occured while saving Vaccin' + err.message, 'error')
      })
    } else {
      this._http.post(AppUtils.VACCINATION_UPDATE, row).subscribe(res => {
        if (res['code'] == 0) {
          Swal.fire('Saved!', 'Vaccin has been Saved.', 'success')
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }, err => {
        Swal.fire('Error!', 'Error occured while saving Vaccin' + err.message, 'error')
      })
    }*/
  }

  // onRowEditCancel(item: any, index: number) {
  //     this.vacList[index] = this.clonedResult[item];
  //     delete this.clonedResult[item];

  // }
  delete(row: any) {

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbVaccinationInfo = this.vaccinationForm.get('rgTbVaccinationInfo') as FormArray;
        this.delRow = this.vaccineFg.indexOf(row);
        this.vaccineFg.splice(this.delRow, 1);
        this.rgTbVaccinationInfo.removeAt(this.delRow);
      }
    })



  }

  /*
civilId: 121597306
patientId: 10377
remarks: "testing"
vaccinationDate: "2021-03-23T06:21:19.000+0000"
vaccineCode: 53
  */
  getDataFromAlshifa(sendingData: any = 0) {
    // this.vaccNewList = sendingData;
    sendingData.forEach(element => {
      // let vaccinatedInst = null;
      // let instName = null;       //Instituse name depend on  vaccinatedInst
      let enteredBy = null;
      //this.vaccinMastList.filter(s => s.vaccineId == element.vaccineCode).map(s => s.vaccineName)
      this.addNewVaccine(null, enteredBy, element.vaccinationDate, element.vaccineCode,null,  element.remarks, element.civilId, 'S', false);
    });
  }

  getvaccName(vaccineCode) {
    return this.vaccinMastList.find(sur => sur.vaccineId === vaccineCode)['vaccineName'] || '-';
  }


  callFetchDataFromAlShifa() {
    //console.log("this.calledFromParent",this.calledFromParent);
    if (this.calledFromParent) {
      this.downloadVaccination.emit();
    } else {
      this.FetchDataFromAlShifa(20068, this.patntId);
    }
  }

  // callFetchDataFromAlShifa() {
    
  //   this.downloadVaccination.emit();
  //   //console.log("test--"+this.patientDetails.patientForm.controls["patientId"].value);
  //  // this.FetchDataFromAlShifa(20068, this.patientDetails.patientForm.controls["patientId"].value);
  //   this.FetchDataFromAlShifa(20068, this.patntId);

  // }

  // FetchDataFromAlShifa(estcode: any, patientId: any) {
  //   this._masterService.getdisoredVaccineInfo(estcode, patientId).subscribe(res => {
  //     this.disVaccineInfoalshifa = res["result"];
  //     this.getDataFromAlshifa(this.disVaccineInfoalshifa);
  //   })
  // }


  FetchDataFromAlShifa(estcode: any, patientId: any) {
    this._masterService.getVaccineHistroy(estcode, patientId).subscribe(res => {
      this.disVaccineInfoalshifa = res["result"];
      if (this.disVaccineInfoalshifa.length == 0) {
        Swal.fire('Warning!', 'No Records Found ', 'warning')
      }
      else {
        this.getDataFromAlshifa(this.disVaccineInfoalshifa);
      }
    })
  }



  clear() {
    this.vaccineFg = [];
    this.vaccinationForm.reset();

    this.vaccinationForm = this.formBuilder.group({
      rgTbVaccinationInfo: this.formBuilder.array([]),
    })

  }

}