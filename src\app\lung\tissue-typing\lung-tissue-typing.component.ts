import { Component, <PERSON>Child, OnInit, Input } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import {
  NgbModalConfig,
  NgbModal,
  NgbModalOptions,
  NgbActiveModal,
} from "@ng-bootstrap/ng-bootstrap";
import { SharedService } from "../../_services/shared.service";
import { FormGroup, FormBuilder, NgModel, NgControl } from "@angular/forms";
import * as AppUtils from "../../common/app.utils";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";
import { ColumnApi, GridApi, GridOptions } from "ag-grid-community";
import { TissueTypeModel } from "../../common/objectModels/tissueTypeModel";
import { RegistryService } from "../../_services/registry.service";
import { LungDonorRegistryComponent } from "../lung-donor-registry/lung-donor-registry.component";
import { MasterService } from "../../_services/master.service";
import { RenalDonor } from "../../_models/renal-donor.model";
import { VaccinationComponent } from "../../_comments/vaccination/vaccination.component";
import { DraggableModalComponent } from "../../draggableModel/draggable-modal.Component";
import { ModalConfig } from "../../config/modal-config";
import { DragDropModule } from "@angular/cdk/drag-drop";
import * as $ from "jquery";
import "jqueryui";
import Swal from "sweetalert2";
import * as moment from "moment";
import * as CommonConstants from "../../_helpers/common.constants";
import { formatDate } from "@angular/common";
import { variable } from "@angular/compiler/src/output/output_ast";
import { Variable } from "@angular/compiler/src/render3/r3_ast";
import { Router } from "@angular/router";
import { LungService } from "../lung.service";

@Component({
  selector: "app-lung-tissue-typing",
  templateUrl: "./lung-tissue-typing.component.html",
  styleUrls: ["./lung-tissue-typing.component.scss"],
})
export class LungTissueTypingComponent implements OnInit {
  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  @ViewChild("donorDetails", { static: false })
  donorDetails: LungDonorRegistryComponent;
  @ViewChild("Vaccination", { static: false })
  Vaccination: VaccinationComponent;
  @Input() tissueForm: FormGroup;
  @Input() regRenalDonorForm: FormGroup;
  @Input() donorTissueForm: FormGroup;
  regId: any;
  patientForm: FormGroup;
  tissueType: TissueTypeModel[];
  columnDefs: any[];
  rowData: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  submitted = false;
  tissueTypeData: TissueTypeModel;
  savingTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  activeClass2Tab = 1;
  dbTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  patientTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  patientActiveTissue: TissueTypeModel[];
  donorTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  dbcentralRegNo: any;
  centralRegNoExit: boolean = false;
  civilIdInvalid = false;
  renalDonor: RenalDonor;
  hasvalue = true;
  hasSave = true;
  hasEdit = false;
  isDisabled = false;
  nationListFilter: any;
  nationList: any;
  kidneyDonorId: any;
  dbResuld;
  modalOptions: NgbModalOptions = ModalConfig;
  activeTabIndex = 0;
  selectedOptions: { group: string; label: any }[] = [];
  selectedGroups: { [tabIndex: number]: string } = {};
  allRemarks: string = "";
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridReady: () => {
      this.gridOptions.api.setRowData(this.tissueType);
    },
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
  };
  lungRegistryData: any;
  estCode: any;
  patientId: any;
  formData: any;
  antiGenMaster: any;
  centralRegNo: number;
  activeClassTab = 1;
  //antigenList: any;
  onReady(params) {
    this.gridOptions.api.sizeColumnsToFit();
  }

  unacceptableTabs: {
    date: string;
    antigens: any[];
    remarks: string;
    selectedGroup?: string;
    selectedGroups?: string[];
  }[] = [];
  activeUnacceptableTab = 0;
  selectedAntigens: {
    antigen: string;
    seroEquivalent: string;
    antigenName: string;
  }[] = [];
  antigenNames: string[] = [];
  alleleOptions: string[] = [];
  selectedAntigenName: string = "";
  selectedAllele: string = "";
  remarks: string = "";
  alleleOptionsMap: { [key: string]: string[] } = {};
  seroOptionsMap: { [key: string]: string[] } = {};

  showDatePicker = false;
  newTabDate: NgbDateStruct = null;

  get fTissue() {
    return this.tissueForm.controls;
  }

  antigenList: any[] = []; // Fill this from your API
  selectedUniqueAntigens: Set<string> = new Set();
  class1Antigens = ["A", "B", "CW", "BW"];
  class2AntigensLeft = ["DRB1", "DRB3", "DRB4", "DRB5"];
  class2AntigensRight = ["DQB1", "DQA1", "DPB1", "DPA1"];

  constructor(
    private modalService: NgbModal,
    private _sharedService: SharedService,
    private _http: HttpClient,
    private _router: Router,
    private formBuilder: FormBuilder,
    private registryService: RegistryService,
    private _masterService: MasterService,
    private _lungService: LungService
  ) {}

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.centralRegNo =
          this._sharedService.getNavigationData().centralRegNo;
        this.getList(this.centralRegNo);
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.loadFormData();
    this.getNationalityList();
    this.tissueForm.disable();
    //this.hasEdit = true;
    this.loadAntiGenMaster();
  }

  loadFormData() {
    this.tissueForm = this.formBuilder.group({
      centralRegNo: [null],
      donorId: [null],
      donorType: [null],
      methodUsed: [null],
      readingDate: [null],
      // Class 1
      A_ALLELE_1: [null],
      A_ALLELE_2: [null],
      A_SERO_1: [null],
      A_SERO_2: [null],
      B_ALLELE_1: [null],
      B_ALLELE_2: [null],
      B_SERO_1: [null],
      B_SERO_2: [null],
      CW_ALLELE_1: [null],
      CW_ALLELE_2: [null],
      CW_SERO_1: [null],
      CW_SERO_2: [null],
      //disable BW_Alleles and enable BW_SERO_1 and BW_SERO_2
      BW_ALLELE_1: [{ value: null, disabled: true }],
      BW_ALLELE_2: [{ value: null, disabled: true }],
      BW_SERO_1: [null],
      BW_SERO_2: [null],
      // Class 2
      DRB1_ALLELE_1: [null],
      DRB1_ALLELE_2: [null],
      DRB1_SERO_1: [null],
      DRB1_SERO_2: [null],
      DRB3_ALLELE_1: [null],
      DRB3_ALLELE_2: [null],
      DRB3_SERO_1: [null],
      DRB3_SERO_2: [null],
      DRB4_ALLELE_1: [null],
      DRB4_ALLELE_2: [null],
      DRB4_SERO_1: [null],
      DRB4_SERO_2: [null],
      DRB5_ALLELE_1: [null],
      DRB5_ALLELE_2: [null],
      DRB5_SERO_1: [null],
      DRB5_SERO_2: [null],
      DQB1_ALLELE_1: [null],
      DQB1_ALLELE_2: [null],
      DQB1_SERO_1: [null],
      DQB1_SERO_2: [null],
      DQA1_ALLELE_1: [null],
      DQA1_ALLELE_2: [null],
      DQA1_SERO_1: [null],
      DQA1_SERO_2: [null],
      DPB1_ALLELE_1: [null],
      DPB1_ALLELE_2: [null],
      DPB1_SERO_1: [null],
      DPB1_SERO_2: [null],
      DPA1_ALLELE_1: [null],
      DPA1_ALLELE_2: [null],
      DPA1_SERO_1: [null],
      DPA1_SERO_2: [null],
    });
  }

  updateAllRemarksSummary() {
    this.allRemarks = this.unacceptableTabs
      .filter((tab) => tab.remarks && tab.remarks.trim()) // Only include tabs with remarks
      .map((tab) => `${tab.date}: ${tab.remarks}`)
      .join("\n");
  }

  onRemarksChange() {
    this.updateAllRemarksSummary();
  }

  buildAlleleOptionsMap() {
    const antigens = [
      { names: ["A", "B", "CW", "BW"], classType: 1 },
      {
        names: ["DRB1", "DRB3", "DRB4", "DRB5", "DQB1", "DQA1", "DPB1", "DPA1"],
        classType: 2,
      },
    ];

    this.alleleOptionsMap = {};
    for (const group of antigens) {
      for (const antigen of group.names) {
        let alleles = this.antigenList
          .filter(
            (a) =>
              a.antigenName === antigen &&
              a.classType === group.classType &&
              a.activeYn === "Y"
          )
          .map((a) => a.allele)
          .filter((v, i, arr) => arr.indexOf(v) === i); // unique

        // Add empty string for Bw antigen and disable the field and enable seroequivalenet field

        if (antigen === "BW") {
          alleles = ["", ...alleles];

          // set seroOptions for BW
          this.seroOptionsMap[`${antigen}_${group.classType}_BW_ALLELE_1`] =
            this.antigenList
              .filter(
                (a) =>
                  a.antigenName === "Bw" &&
                  a.classType === group.classType &&
                  a.activeYn === "Y"
              )
              .map((a) => a.seroEquivalent)
              .filter((v, i, arr) => arr.indexOf(v) === i); // unique
          this.seroOptionsMap[`${antigen}_${group.classType}_BW_ALLELE_2`] =
            this.antigenList
              .filter(
                (a) =>
                  a.antigenName === "Bw" &&
                  a.classType === group.classType &&
                  a.activeYn === "Y"
              )
              .map((a) => a.seroEquivalent)
              .filter((v, i, arr) => arr.indexOf(v) === i); // unique
        }
        this.alleleOptionsMap[`${antigen}_${group.classType}`] = alleles;
      }
    }
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        this.nationListFilter = this.nationList;
      },
      (error) => {}
    );
  }

  search() {
    //this.clearData();
    this.ngOnInit();
    if (this.regId) {
      this.centralRegNo = this.regId;
      this.getList(this.regId);
      this.regId = "";
      // this.isDisabled == false;
    } else {
      this.regId = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Registration ID",
      });
    }
  }

  getList(centralRegNo: any) {
    this._lungService.getTissueScreening(centralRegNo).subscribe(
      (res) => {
        if (res["code"] === "S0000") {
          // enable form fields except BW_ALLELE_1 and BW_ALLELE_2
          this.tissueForm.enable();
          this.tissueForm.get("BW_ALLELE_1").disable();
          this.tissueForm.get("BW_ALLELE_2").disable();
          this.hasEdit = true;
          const result = res["result"];
          this.centralRegNoExit = true;
          this.lungRegistryData = result;
          this.formData = result;
          this.estCode = this.lungRegistryData.regInst;
          // 1. Load patient details
          if (result.rgTbPatientInfo) {
            this.patientDetails.setPatientDetails(this.lungRegistryData);
          }

          if (result.rgHlaTypingTransDto) {
            //console.log("rgHlaTypingTransDto", result.rgHlaTypingTransDto);
            this.loadTissueForm(result.rgHlaTypingTransDto);
          }

          //console.log("result", result);
          if (
            result.rgHlaUnacceptableHdrDtoList &&
            result.rgHlaUnacceptableHdrDtoList.length > 0
          ) {
            this.unacceptableTabs = result.rgHlaUnacceptableHdrDtoList.map(
              (hdr) => ({
                date: hdr.readingDate ? hdr.readingDate.substring(0, 10) : "",
                remarks: hdr.remarks || "",
                antigens: hdr.rgHlaUnacceptableDtlDtoList
                  ? hdr.rgHlaUnacceptableDtlDtoList.map((dtl) => ({
                      dtlId: dtl.dtlId,
                      group: dtl.antigenGroup,
                      antigenName: dtl.antigenName,
                      allele: dtl.allele,
                      seroEquivalent: dtl.seroEquivalent,
                    }))
                  : [],
                selectedGroup:
                  hdr.rgHlaUnacceptableDtlDtoList &&
                  hdr.rgHlaUnacceptableDtlDtoList.length > 0
                    ? hdr.rgHlaUnacceptableDtlDtoList[0].antigenGroup
                    : this.antigenGroups[0],
                // distinct group only
                selectedGroups: hdr.rgHlaUnacceptableDtlDtoList
                  ? hdr.rgHlaUnacceptableDtlDtoList
                      .map((dtl) => dtl.antigenGroup)
                      .filter((v, i, arr) => arr.indexOf(v) === i) // unique
                  : [],
              })
            );
            this.activeUnacceptableTab = 0;
            this.updateAllRemarksSummary();

            this.sortTabsByDate();

            //console.log( "unacceptableTabs", this.unacceptableTabs);
            // Set selected group for the first tab if it has antigens
            if (this.unacceptableTabs[0].antigens.length > 0) {
              this.isOptionSelected(
                this.unacceptableTabs[0].selectedGroup,
                this.unacceptableTabs[0].antigens[0].allele
              );
            }
          }
        } else {
          const message =
            res["code"] === "F0000"
              ? "No Record Found with Entered Registration No."
              : res["message"];
          this.clearData();
          Swal.fire("Warning", message, "warning");
        }
      },
      (error) => {
        if (error.status === 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      }
    );
  }

  loadTissueForm(dto: any) {
    this.tissueForm.patchValue({
      // Class 1
      A_ALLELE_1: dto.allele1,
      A_ALLELE_2: dto.allele2,
      A_SERO_1: dto.asero1,
      A_SERO_2: dto.asero2,
      B_ALLELE_1: dto.ballele1,
      B_ALLELE_2: dto.ballele2,
      B_SERO_1: dto.bsero1,
      B_SERO_2: dto.bsero2,
      CW_ALLELE_1: dto.cwAllele1,
      CW_ALLELE_2: dto.cwAllele2,
      CW_SERO_1: dto.cwSero1,
      CW_SERO_2: dto.cwSero2,
      BW_ALLELE_1: dto.bwAllele1,
      BW_ALLELE_2: dto.bwAllele2,
      BW_SERO_1: dto.bwSero1,
      BW_SERO_2: dto.bwSero2,
      // Class 2
      DRB1_ALLELE_1: dto.drb1Allele1,
      DRB1_ALLELE_2: dto.drb1Allele2,
      DRB1_SERO_1: dto.drb1Sero1,
      DRB1_SERO_2: dto.drb1Sero2,
      DRB3_ALLELE_1: dto.drb3Allele1,
      DRB3_ALLELE_2: dto.drb3Allele2,
      DRB3_SERO_1: dto.drb3Sero1,
      DRB3_SERO_2: dto.drb3Sero2,
      DRB4_ALLELE_1: dto.drb4Allele1,
      DRB4_ALLELE_2: dto.drb4Allele2,
      DRB4_SERO_1: dto.drb4Sero1,
      DRB4_SERO_2: dto.drb4Sero2,
      DRB5_ALLELE_1: dto.drb5Allele1,
      DRB5_ALLELE_2: dto.drb5Allele2,
      DRB5_SERO_1: dto.drb5Sero1,
      DRB5_SERO_2: dto.drb5Sero2,
      DQB1_ALLELE_1: dto.dqb1Allele1,
      DQB1_ALLELE_2: dto.dqb1Allele2,
      DQB1_SERO_1: dto.dqb1Sero1,
      DQB1_SERO_2: dto.dqb1Sero2,
      DQA1_ALLELE_1: dto.dqa1Allele1,
      DQA1_ALLELE_2: dto.dqa1Allele2,
      DQA1_SERO_1: dto.dqa1Sero1,
      DQA1_SERO_2: dto.dqa1Sero2,
      DPB1_ALLELE_1: dto.dpb1Allele1,
      DPB1_ALLELE_2: dto.dpb1Allele2,
      DPB1_SERO_1: dto.dpb1Sero1,
      DPB1_SERO_2: dto.dpb1Sero2,
      DPA1_ALLELE_1: dto.dpa1Allele1,
      DPA1_ALLELE_2: dto.dpa1Allele2,
      DPA1_SERO_1: dto.dpa1Sero1,
      DPA1_SERO_2: dto.dpa1Sero2,
    });
  }

  getTissueType() {
    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }

    this._http.get(AppUtils.GET_ALL_TISSUE_TYPE).subscribe(
      (res) => {
        this.rowData = res["result"];
      },
      (error) => {
        if (error.status == 401)
          Swal.fire(
            "Error!",
            "Error occured while retrieving user details",
            "error"
          );
      }
    );
  }

  onKeypressEvent(event: any) {}

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    this.onKeypressEvent(event);
    return true;
  }

  padLeft(text: any, padChar: string, size: number): string {
    if (!text) {
      return null;
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  editValue() {
    this.hasSave = true;
    this.hasvalue = false;
    this.hasEdit = true;
    this.tissueForm.enable();
  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(["donor/registry"], {
      state: { donorId: event.data.donorId },
    });
  }

  callCase() {
    this._sharedService.setNavigationData(
      this.patientDetails.patientForm.value
    );
    this._router.navigate(["lung/case-details"], {
      state: { centralRegNo: this.centralRegNo },
    });
  }

  selectGroup(tabIndex: number, groupName: string) {
    this.selectedGroups[tabIndex] = groupName;
  }

  isGroupActive(tabIndex: number, groupName: string): boolean {
    return this.selectedGroups[tabIndex] === groupName;
  }

  onCheckboxChange(event: Event, groupName: string, option: any) {
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const alreadySelected = this.selectedOptions.some(
        (item) => item.group === groupName && item.label === option
      );

      if (!alreadySelected) {
        this.selectedOptions.push({
          group: groupName,
          label: option,
        });
      }
    } else {
      this.selectedOptions = this.selectedOptions.filter(
        (item) => !(item.group === groupName && item.label === option)
      );
    }
  }

  clearData() {
    this.formData = null;
    this.patientDetails.clear();
    this.centralRegNoExit = false;
    this.lungRegistryData = null;
    this.tissueForm.reset();
    this.unacceptableTabs = [];
    this.allRemarks = "";
    this.selectedOptions = [];
    this.antigenList = [];
    this.activeTabIndex = 0;
  }

  loadAntiGenMaster() {
    this._masterService.getAntiGenMaster().subscribe((res) => {
      this.antigenList = res.result;
      this.buildAlleleOptionsMap(); // <-- call here, after antigenList is set
    });
  }

  getSeroOptions(antigenName: string, classType: number) {
    if (!this.antigenList) return [];
    return this.antigenList
      .filter(
        (a) =>
          a.antigenName === antigenName &&
          a.classType === classType &&
          a.activeYn === "Y"
      )
      .map((a) => ({ seroEquivalent: a.seroEquivalent }));
  }

  getSeroOptionsForAllele(
    antigenName: string,
    classType: number,
    allele: string
  ): string[] {
    if (antigenName === "BW") {
      return this.antigenList
        .filter(
          (a) =>
            a.antigenName === "Bw" &&
            a.classType === classType &&
            a.activeYn === "Y"
        )
        .map((a) => a.seroEquivalent)
        .filter((v, i, arr) => arr.indexOf(v) === i); // unique
    }
    if (!allele) return [];
    return this.antigenList
      .filter(
        (a) =>
          a.antigenName === antigenName &&
          a.classType === classType &&
          a.allele === allele &&
          a.activeYn === "Y"
      )
      .map((a) => a.seroEquivalent)
      .filter((v, i, arr) => arr.indexOf(v) === i);
  }

  getAlleleOptions(antigenName: string, classType: number) {
    console.log("antigenName: " + antigenName + " classType: " + classType);
    return this.antigenList
      .filter(
        (a) =>
          a.antigenName === antigenName &&
          a.classType === classType &&
          a.activeYn === "Y"
      )
      .map((a) => a.allele)
      .filter((v, i, arr) => arr.indexOf(v) === i); // unique alleles
  }

  getSeroEquivalentByAllele(
    antigenName: string,
    classType: number,
    allele: string
  ) {
    const found = this.antigenList.find(
      (a) =>
        a.antigenName === antigenName &&
        a.classType === classType &&
        a.allele === allele &&
        a.activeYn === "Y"
    );
    return found ? found.seroEquivalent : "";
  }

  onAlleleChange(
    antigenName: string,
    classType: number,
    controlNameAllele: string,
    controlNameSero: string
  ) {
    const allele = this.tissueForm.get(controlNameAllele).value;
    const key = `${antigenName}_${classType}_${controlNameAllele}`;
    let seroOptions: string[];
    // For BW, if allele is empty, show all seroOptions for BW by default
    if (antigenName === "BW") {
      seroOptions = this.antigenList
        .filter(
          (a) =>
            a.antigenName === "Bw" &&
            a.classType === classType &&
            a.activeYn === "Y"
        )
        .map((a) => a.seroEquivalent)
        .filter((v, i, arr) => arr.indexOf(v) === i); // unique
    } else {
      seroOptions = this.getSeroOptionsForAllele(
        antigenName,
        classType,
        allele
      );
    }
    this.seroOptionsMap[key] = seroOptions;
    if (seroOptions.length === 1) {
      this.tissueForm.get(controlNameSero).setValue(seroOptions[0]);
    } else {
      this.tissueForm.get(controlNameSero).setValue(null);
    }
  }

  mapTissueFormToDto(): any {
    const form = this.tissueForm.value;
    return {
      hlaId: form.hlaId || null,
      centralRegNo: Number(this.centralRegNo),
      donorId: form.donorId || null,
      donorType: form.donorType || null, // should be a single char, e.g. 'D'
      methodUsed: form.methodUsed,
      readingDate: form.readingDate ? new Date(form.readingDate) : null,
      allele1: form.A_ALLELE_1,
      allele2: form.A_ALLELE_2,
      asero1: form.A_SERO_1,
      asero2: form.A_SERO_2,
      ballele1: form.B_ALLELE_1,
      ballele2: form.B_ALLELE_2,
      bsero1: form.B_SERO_1,
      bsero2: form.B_SERO_2,
      cwAllele1: form.CW_ALLELE_1,
      cwAllele2: form.CW_ALLELE_2,
      cwSero1: form.CW_SERO_1,
      cwSero2: form.CW_SERO_2,
      bwAllele1: form.BW_ALLELE_1,
      bwAllele2: form.BW_ALLELE_2,
      bwSero1: form.BW_SERO_1,
      bwSero2: form.BW_SERO_2,
      drb1Allele1: form.DRB1_ALLELE_1,
      drb1Allele2: form.DRB1_ALLELE_2,
      drb1Sero1: form.DRB1_SERO_1,
      drb1Sero2: form.DRB1_SERO_2,
      drb3Allele1: form.DRB3_ALLELE_1,
      drb3Allele2: form.DRB3_ALLELE_2,
      drb3Sero1: form.DRB3_SERO_1,
      drb3Sero2: form.DRB3_SERO_2,
      drb4Allele1: form.DRB4_ALLELE_1,
      drb4Allele2: form.DRB4_ALLELE_2,
      drb4Sero1: form.DRB4_SERO_1,
      drb4Sero2: form.DRB4_SERO_2,
      drb5Allele1: form.DRB5_ALLELE_1,
      drb5Allele2: form.DRB5_ALLELE_2,
      drb5Sero1: form.DRB5_SERO_1,
      drb5Sero2: form.DRB5_SERO_2,
      dqb1Allele1: form.DQB1_ALLELE_1,
      dqb1Allele2: form.DQB1_ALLELE_2,
      dqb1Sero1: form.DQB1_SERO_1,
      dqb1Sero2: form.DQB1_SERO_2,
      dqa1Allele1: form.DQA1_ALLELE_1,
      dqa1Allele2: form.DQA1_ALLELE_2,
      dqa1Sero1: form.DQA1_SERO_1,
      dqa1Sero2: form.DQA1_SERO_2,
      dpb1Allele1: form.DPB1_ALLELE_1,
      dpb1Allele2: form.DPB1_ALLELE_2,
      dpb1Sero1: form.DPB1_SERO_1,
      dpb1Sero2: form.DPB1_SERO_2,
      dpa1Allele1: form.DPA1_ALLELE_1,
      dpa1Allele2: form.DPA1_ALLELE_2,
      dpa1Sero1: form.DPA1_SERO_1,
      dpa1Sero2: form.DPA1_SERO_2,
    };
  }

  mapUnacceptableAntigenToDto(tabIdx: number): any {
    const tab = this.unacceptableTabs[tabIdx];
    return {
      centralRegno: this.patientDetails.f.centralRegNo.value,
      readingDate: tab.date ? tab.date + "T10:00:00.000+0000" : null,
      remarks: tab.remarks || "",
      modifiedDate: null,
      modifiedBy: null,
      rgHlaUnacceptableDtlDtoList: tab.antigens.map((a) => {
        // Find matching antigen in antigenList
        const found = this.antigenList.find(
          (ag) => ag.allele === a.allele && ag.antigenGroup === a.group
        );
        return {
          antigen: a.allele,
          seroEquivalent: found ? found.seroEquivalent : a.seroEquivalent,
        };
      }),
    };
  }

  saveAllUnacceptableAntigenTabs() {
    const payloadList = this.unacceptableTabs.map((tab) => ({
      centralRegno: this.patientDetails.f.centralRegNo.value,
      readingDate: tab.date ? tab.date + "T10:00:00.000+0000" : null,
      remarks: tab.remarks || "",
      rgHlaUnacceptableDtlDtoList: tab.antigens.map((a) => {
        const found = this.antigenList.find(
          (ag) => ag.allele === a.allele && ag.antigenGroup === a.group
        );
        return {
          allele: a.allele,
          seroEquivalent: found ? found.seroEquivalent : a.seroEquivalent,
          antigenGroup: found ? found.antigenGroup : a.group,
          antigenName: found ? found.antigenName : a.antigenName,
        };
      }),
    }));

    //console.log("Payload for all tabs:", payloadList);
    return payloadList;
  }

  hasAtLeastOneAlleleOrSeroSelected(): boolean {
    const form = this.tissueForm.value;
    // List all allele and serological keys
    const keys = [
      // Class 1
      "A_ALLELE_1",
      "A_ALLELE_2",
      "A_SERO_1",
      "A_SERO_2",
      "B_ALLELE_1",
      "B_ALLELE_2",
      "B_SERO_1",
      "B_SERO_2",
      "CW_ALLELE_1",
      "CW_ALLELE_2",
      "CW_SERO_1",
      "CW_SERO_2",
      "BW_ALLELE_1",
      "BW_ALLELE_2",
      "BW_SERO_1",
      "BW_SERO_2",
      // Class 2
      "DRB1_ALLELE_1",
      "DRB1_ALLELE_2",
      "DRB1_SERO_1",
      "DRB1_SERO_2",
      "DRB3_ALLELE_1",
      "DRB3_ALLELE_2",
      "DRB3_SERO_1",
      "DRB3_SERO_2",
      "DRB4_ALLELE_1",
      "DRB4_ALLELE_2",
      "DRB4_SERO_1",
      "DRB4_SERO_2",
      "DRB5_ALLELE_1",
      "DRB5_ALLELE_2",
      "DRB5_SERO_1",
      "DRB5_SERO_2",
      "DQB1_ALLELE_1",
      "DQB1_ALLELE_2",
      "DQB1_SERO_1",
      "DQB1_SERO_2",
      "DQA1_ALLELE_1",
      "DQA1_ALLELE_2",
      "DQA1_SERO_1",
      "DQA1_SERO_2",
      "DPB1_ALLELE_1",
      "DPB1_ALLELE_2",
      "DPB1_SERO_1",
      "DPB1_SERO_2",
      "DPA1_ALLELE_1",
      "DPA1_ALLELE_2",
      "DPA1_SERO_1",
      "DPA1_SERO_2",
    ];
    return keys.some((key) => !!form[key]);
  }

  saveTissueTypeApi() {
    if (this.tissueForm.invalid || !this.hasAtLeastOneAlleleOrSeroSelected()) {
      Swal.fire(
        "Error!",
        "Please select at least one allele or serological field.",
        "error"
      );
      return;
    }

    const payload = {
      centralRegNo: Number(this.centralRegNo),
      rgHlaTypingTransDto: this.mapTissueFormToDto(),
      rgHlaUnacceptableHdrDtoList: this.saveAllUnacceptableAntigenTabs(),
    };

    ///console.log("Result::", payload);
    // Call your API service to save
    this._lungService.saveTissueScreening(payload).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Tissue Details Saved Successfully.", "success");
          this.regId = res["result"];
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire("Error!", "Failed to save tissue typing.", "error");
      }
    );
  }

  getTissueScreening() {
    this._lungService
      .getTissueScreening(this.patientDetails.f.centralRegNo.value)
      .subscribe(
        (res) => {
          if (res["code"] === "S0000") {
            this.tissueForm.patchValue(res["result"]);
          }
          console.log(res);
        },
        (err) => {
          console.log(err);
        }
      );
  }

  getHlaTyping() {
    this._lungService
      .getHlaTyping(this.patientDetails.f.centralRegNo.value)
      .subscribe(
        (res) => {
          if (res["code"] === "S0000") {
            this.tissueForm.patchValue(res["result"]);
          }
          console.log(res);
        },
        (err) => {
          console.log(err);
        }
      );
  }

  onTabChange(idx: number) {
    // get the selected group and the antigens for that group and set it to the active tab
    this.unacceptableTabs[idx].selectedGroup = this.unacceptableTabs[idx].antigens[0].group;
    this.unacceptableTabs[idx].selectedGroups = this.unacceptableTabs[idx].antigens.map((a) => a.group);
    


    this.activeUnacceptableTab = idx;
    this.selectedAntigenName = "";
    this.selectedAllele = "";
    this.alleleOptions = [];
    this.updateAllRemarksSummary();
  }

  onAntigenNameChange() {
    this.alleleOptions = this.antigenList
      .filter((a) => a.antigenName === this.selectedAntigenName)
      .map((a) => a.allele)
      .filter((v, i, arr) => arr.indexOf(v) === i);
    this.selectedAllele = "";
  }

  onAlleleSelect() {
    if (!this.selectedAntigenName || !this.selectedAllele) return;
    const found = this.antigenList.find(
      (a) =>
        a.antigenName === this.selectedAntigenName &&
        a.allele === this.selectedAllele
    );
    if (!found) return;
    // Prevent duplicates
    const tab = this.unacceptableTabs[this.activeUnacceptableTab];
    if (
      !tab.antigens.some(
        (a) =>
          a.antigen === found.allele &&
          a.seroEquivalent === found.seroEquivalent
      )
    ) {
      tab.antigens.push({
        antigen: found.allele,
        seroEquivalent: found.seroEquivalent,
        antigenName: found.antigenName,
      });
    }
    this.selectedAntigenName = "";
    this.selectedAllele = "";
    this.alleleOptions = [];
  }

  removeAntigen(idx: number) {
    this.unacceptableTabs[this.activeUnacceptableTab].antigens.splice(idx, 1);
  }

  saveUnacceptableAntigens() {
    const tab = this.unacceptableTabs[this.activeUnacceptableTab];
    const payload = {
      centralRegno: this.patientDetails.f.centralRegNo.value,
      readingDate: tab.date + "T10:00:00Z",
      remarks: tab.remarks,
      createdDate: new Date().toISOString(),
      //createdBy: this.userId,
      modifiedDate: null,
      modifiedBy: null,
      rgHlaUnacceptableDtlDtoList: tab.antigens.map((a) => ({
        antigen: a.antigen,
        seroEquivalent: a.seroEquivalent,
      })),
    };
    this._lungService.saveHlaUnacceptableHdr(payload).subscribe(
      (res) => Swal.fire("Success!", "Unacceptable Antigens Saved.", "success"),
      (err) => Swal.fire("Error!", "Failed to save.", "error")
    );
  }

  // Add to your component

  onAlleleSelectForTab(tabIdx: number, seroEquivalent: string, allele: string) {
    const tab = this.unacceptableTabs[tabIdx];
    const group = tab.selectedGroup;
    // Find seroEquivalent from antigenList
    if (group === "BW") {
      const found = this.antigenList.find(
        (a) => a.antigenGroup === "Bw" && a.seroEquivalent === seroEquivalent
      );
      if (!found) return;
      if (
        !tab.antigens.some(
          (a) => a.group === group && a.seroEquivalent === found.seroEquivalent
        )
      ) {
        tab.antigens.push({
          group,
          allele,
          seroEquivalent: found.seroEquivalent,
        });
      }
      return;
    }

    const found = this.antigenList.find(
      (a) => a.antigenGroup === group && a.seroEquivalent === seroEquivalent
    );

    if (!found) return;
    if (
      !tab.antigens.some(
        (a) => a.group === group && a.seroEquivalent === seroEquivalent
      )
    ) {
      tab.antigens.push({
        group,
        allele,
        seroEquivalent: found.seroEquivalent,
      });
    }
  }

  removeAntigenFromTab(tabIdx: number, antigenIdx: number) {
    this.unacceptableTabs[tabIdx].antigens.splice(antigenIdx, 1);
  }

  isOptionSelected(group: string, allele: string): boolean {
    const tab = this.unacceptableTabs[this.activeUnacceptableTab];
    return tab.antigens.some(
      (a) => a.group === group && a.seroEquivalent === allele
    );
  }

  addUnacceptableTabWithDate() {
    this.showDatePicker = true;
    this.newTabDate = null;
  }

  confirmAddTab() {
    if (this.newTabDate) {
      const dateStr = `${this.newTabDate.year}-${(
        "0" + this.newTabDate.month
      ).slice(-2)}-${("0" + this.newTabDate.day).slice(-2)}`;
      this.unacceptableTabs.push({
        date: dateStr,
        antigens: [],
        remarks: "",
        selectedGroup: this.antigenGroups[0],
      });
      this.activeUnacceptableTab = this.unacceptableTabs.length - 1;
      this.showDatePicker = false;
    }
  }

  antigenGroups = [
    "A",
    "B",
    "BW",
    "CW",
    "DR",
    "DPA",
    "DPB",
    "DQA",
    "DQB",
  ];

  getAllelesForGroup(group: string) {
    if (group === "BW") {
      // For BW, return all unique seroEquivalents with allele as empty string

      return this.antigenList
        .filter((a) => a.antigenGroup === "Bw" && a.unacceptYn === "Y")
        .map((a) => ({
          allele: "",
          seroEquivalent: a.seroEquivalent,
        }))
        .filter(
          (v, i, arr) =>
            arr.findIndex(
              (x) =>
                x.seroEquivalent === v.seroEquivalent && x.allele === v.allele
            ) === i
        );
    }

    // if (group === "DR51/52/53") {
    //   return this.antigenList
    //     .filter(
    //       (a) =>
    //         a.antigenGroup === "DR" &&
    //         a.unacceptYn === "Y" &&
    //         (a.seroEquivalent === "51" ||
    //           a.seroEquivalent === "52" ||
    //           a.seroEquivalent === "53")
    //     )
    //     .map((a) => ({
    //       allele: a.allele,
    //       seroEquivalent: a.seroEquivalent,
    //     })) // unique seroEquivalent
    //     .filter(
    //       (v, i, arr) =>
    //         arr.findIndex((x) => x.seroEquivalent === v.seroEquivalent) === i
    //     );
    // }

    // for group DR, return all unique seroEquivalents in ascending order as number

    if (group === "DR") {
      return this.antigenList
        .filter(
          (a) =>
            a.antigenGroup === "DR" &&
            a.unacceptYn === "Y"
        )
        .map((a) => ({
          allele: a.allele,
          seroEquivalent: a.seroEquivalent,
        }))
        .filter(
          (v, i, arr) =>
            arr.findIndex(
              (x) =>
                x.seroEquivalent === v.seroEquivalent
            ) === i
        )
        .sort((a, b) => Number(a.seroEquivalent) - Number(b.seroEquivalent));
    }

    // Default: return unique alleles for the group
    //return all unique seroEquivalents
    return this.antigenList
      .filter((a) => a.antigenGroup === group && a.unacceptYn === "Y")
      .map((a) => ({
        allele: a.allele,
        seroEquivalent: a.seroEquivalent,
      }))
      .filter(
        (v, i, arr) =>
          arr.findIndex(
            (x) =>
              x.seroEquivalent === v.seroEquivalent && x.allele === v.allele
          ) === i
      )
      .sort((a, b) => Number(a.seroEquivalent) - Number(b.seroEquivalent));
  }

  onGroupSelect(tabIdx: number, group: string) {
    this.unacceptableTabs[tabIdx].selectedGroups = this.unacceptableTabs[
      tabIdx
    ].selectedGroups.filter((g) => {
      return this.unacceptableTabs[tabIdx].antigens.some(
        (a) => a.group === g && a.seroEquivalent !== ""
      );
    });
    if (!this.unacceptableTabs[tabIdx].selectedGroups.includes(group)) {
      this.unacceptableTabs[tabIdx].selectedGroups.push(group);
    }

    return (this.unacceptableTabs[tabIdx].selectedGroup = group);
  }

  onDateSelect(date: NgbDateStruct) {
    if (date) {
      // Save as yyyy-MM-dd
      const dateStr = `${date.year}-${date.month
        .toString()
        .padStart(2, "0")}-${date.day.toString().padStart(2, "0")}`;

      // duplicate check
      if (this.unacceptableTabs.some((tab) => tab.date === dateStr)) {
        Swal.fire("Error!", "Date already exists.", "error");
        return;
      }

      this.unacceptableTabs.push({
        date: dateStr,
        antigens: [],
        remarks: "",
        selectedGroup: this.antigenGroups[0],
        // add default group A
        selectedGroups: ["A"],
      });

      // date in ascending order
      this.unacceptableTabs.sort((a, b) => {
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      });

      // set active tab to the new date
      this.activeUnacceptableTab = this.unacceptableTabs.findIndex(
        (tab) => tab.date === dateStr
      );

      //this.activeUnacceptableTab = this.unacceptableTabs.length - 1;
      this.showDatePicker = false;
      this.newTabDate = null;
      this.updateAllRemarksSummary();
    }
  }

  getAllUniqueAntigens() {
    const allAntigens = [];
    const uniqueMap = new Map();

    this.unacceptableTabs.forEach((tab, tabIndex) => {
      tab.antigens.forEach((antigen, antigenIndex) => {
        const key = `${antigen.group}-${antigen.seroEquivalent}`;
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, {
            ...antigen,
            tabIndex,
            antigenIndex,
            date: tab.date,
          });
          allAntigens.push(uniqueMap.get(key));
        }
      });
    });

    return allAntigens;
  }

  clearDate() {
    this.newTabDate = null;
  }

  // Cancel date picker without adding tab
  cancelDatePicker() {
    this.showDatePicker = false;
    this.newTabDate = null;
  }

  removeTab(idx: number) {
    if (this.unacceptableTabs.length > 0) {
      this.unacceptableTabs.splice(idx, 1);

      // Adjust active tab index if necessary
      if (this.activeUnacceptableTab >= this.unacceptableTabs.length) {
        this.activeUnacceptableTab = this.unacceptableTabs.length - 1;
      }
      this.updateAllRemarksSummary();
    }
  }

  navigateToRegister() {
    this._sharedService.setNavigationData({
      regNo: this.patientDetails.f.centralRegNo.value,
    });
    this._router.navigate(["lung/registry"], {
      state: { regNo: this.patientDetails.f.centralRegNo.value },
    });
  }

  toggleAlleleSelection(
    tabIdx: number,
    seroEquivalent: string,
    allele: string
  ) {
    const tab = this.unacceptableTabs[tabIdx];
    const group = tab.selectedGroup;

    // Check if the antigen is already selected
    const existingIndex = tab.antigens.findIndex(
      (a) => a.group === group && a.seroEquivalent === seroEquivalent
    );

    if (existingIndex !== -1) {
      // Remove if already selected
      tab.antigens.splice(existingIndex, 1);
    } else {
      // Add if not selected
      if (group === "BW") {
        const found = this.antigenList.find(
          (a) => a.antigenGroup === "Bw" && a.seroEquivalent === seroEquivalent
        );
        if (found) {
          tab.antigens.push({
            group,
            allele,
            seroEquivalent: found.seroEquivalent,
          });
        }
      } 
      // else if (group === "DR51/52/53") {
      //   const found = this.antigenList.find(
      //     (a) => a.antigenGroup === "DR" && a.seroEquivalent === seroEquivalent
      //   );
      //   if (found) {
      //     tab.antigens.push({
      //       group,
      //       allele,
      //       seroEquivalent: found.seroEquivalent,
      //     });
      //   }
      // } 
      else {
        const found = this.antigenList.find(
          (a) => a.antigenGroup === group && a.seroEquivalent === seroEquivalent
        );
        if (found) {
          tab.antigens.push({
            group,
            allele,
            seroEquivalent: found.seroEquivalent,
          });
        }
      }
    }

    // if no seroEquivalent, remove the antigen
    tab.antigens = tab.antigens.filter((a) => a.seroEquivalent !== "");

    // Update the remarks summary after toggle
    this.updateAllRemarksSummary();
  }

  getSelectedGroups(tabIdx: number): string[] {
    const tab = this.unacceptableTabs[tabIdx];
    if (tab.selectedGroups && tab.selectedGroups.length > 0) {
      return tab.selectedGroups;
    }
    // Fallback to single selectedGroup for backward compatibility
    if (tab.selectedGroup) {
      return [tab.selectedGroup];
    }
    // If neither exists, derive from existing antigens
    return [...new Set(tab.antigens.map((a) => a.group))];
  }

  // Method to toggle group selection
  toggleGroupSelection(tabIdx: number, group: string) {
    const tab = this.unacceptableTabs[tabIdx];

    // Initialize selectedGroups if not present
    if (!tab.selectedGroups) {
      tab.selectedGroups = this.getSelectedGroups(tabIdx);
    }

    const index = tab.selectedGroups.indexOf(group);
    if (index > -1) {
      // Remove group if already selected
      tab.selectedGroups.splice(index, 1);
      // Also remove all antigens from this group
      tab.antigens = tab.antigens.filter((a) => a.group !== group);
    } else {
      // Add group if not selected
      tab.selectedGroups.push(group);
    }

    // Clear the old selectedGroup property when using multiple selections
    if (tab.selectedGroups.length !== 1) {
      tab.selectedGroup = undefined;
    } else {
      // Keep backward compatibility for single selection
      tab.selectedGroup = tab.selectedGroups[0];
    }

    this.updateAllRemarksSummary();
  }

  isGroupSelected(tabIdx: number, group: string): boolean {
    return this.unacceptableTabs[tabIdx].selectedGroups.includes(group);
  }

  sortTabsByDate() {
    this.unacceptableTabs.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime(); // Ascending order
    });
  }


}
