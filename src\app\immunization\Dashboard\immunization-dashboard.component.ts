import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { VaccineService } from '../vaccine.service';
import { ImmunizationDashboard } from '../../_models/immunization-dashboard.model';
import { ImmunizationDashboardDisplay } from '../../_models/immunization-dashboard-display.model';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from '../../_helpers/common.constants';
import * as _ from 'lodash';

@Component({
  selector: 'app-immunization-dashboard',
  templateUrl: './immunization-dashboard.component.html',
  styleUrls: ['./immunization-dashboard.component.scss']
})
export class ImmunizationDashboardComponent implements OnInit {


  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: ImmunizationDashboard[];
  DashboardDataFilter: ImmunizationDashboard[];
  displayTotalVaccine: ImmunizationDashboardDisplay[];
  displayVaccine: ImmunizationDashboardDisplay[];
  displayTotalDefaulters : ImmunizationDashboardDisplay[];
  displayTotalDefaultersByDays : {centralRegNo: number; value: number;}[];
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  totalVaccineChart: any;
  vaccineChart: any;
  totalDefaultersPieChart: any;
  totalDefaultersBarChart: any;
  createTime: any;

  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private _vaccineService: VaccineService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit() {
  }


  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'dateGiven': [null]
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this._vaccineService.getImmunizationDashboard().subscribe(res => {

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {


        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'IMMUNIZATION', this.institeList);

      }

      this.callFilter();
    })

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {



      let body = this.boardForm.value;

      this.DashboardDataFilter = this.DashboardDataDB;

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.estCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

displayFilterType(){
  
  if (this.filterType === "institute") {
    this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
    
  } else if (this.filterType === "wilayat") {
    this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
 
  } else if (this.filterType === "region") {
    this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

  }
  else {
    this.filterTitle = 'All Regions';
  }

}
  setChartData() {
    this.displayTotalVaccine = [];
    this.displayVaccine = [];
    this.displayTotalDefaulters = [];
    this.displayTotalDefaultersByDays = [];

    for (var i = 0; i < this.DashboardDataFilter.length; i++) {
      let total = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].vaccineName };
      this.displayTotalVaccine.push(total);

      this.displayVaccine.push({ centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].vaccineName });

      let defaulters = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].defaulter };
      this.displayTotalDefaulters.push(defaulters);

      let defaulterDays = { centralRegNo: this.DashboardDataFilter[i].centralRegNo, value: this.DashboardDataFilter[i].defaulterDays };
      
      
      this.displayTotalDefaultersByDays.push(defaulterDays);
      
    }
    
    this.callChart();
  }

  callChart() {
    this.callTotalVaccinePieChart(this.DashboardDataFilter);
    this.callTotalDefaultersPieChartYn();
    this.callTotalDefaultersBarChart();
    this.callChartVaccine();

  }

  groupDaysByRange(days: string[], rangeSize: number): string[][] {
    let groupedDays: string[][] = [];
    for (let i = 0; i < days.length; i += rangeSize) {
        groupedDays.push(days.slice(i, i + rangeSize));
    }
    return groupedDays;
}

  callTotalDefaultersBarChart() {
    let charlabels = ['0-30','31-60','61-90','91-120','121-150','151-180', '181-210', '211-240', '241-270', '271-300', '301-330', '331-360', '>361'];
    let charData = [0,0,0,0,0,0,0,0,0,0,0,0,0];
   
    let chartArray = [];
    
    this.displayTotalDefaultersByDays = this.displayTotalDefaultersByDays.filter(s => s.value != null);

    chartArray =  this.displayTotalDefaultersByDays

    
    let min: number;
    let max: number;
    min = 0;
    max = 365;

    const days = Math.round((max-min)/2);
  
    let loopRing: number = days;
    let newMin: number = _.cloneDeep(min);
    let loopMin: number;
    let loopMax: number;

    chartArray.forEach(el=>{;
      if (el.value >0){
        if (el.value <= 30){
          charData[0] = charData[0] + 1
        }
        else if (el.value <= 60){
          charData[1] = charData[1]+ 1
        }
        else if (el.value <= 90){
          charData[2] = charData[2] + 1
        }
        else if (el.value <= 120){
          charData[3] = charData[3] + 1
        }
        else if (el.value <= 150){
          charData[4] =charData[4]+ 1
        }
        else if (el.value <= 180){
          charData[5] =charData[5]+ 1
        }
        else if (el.value <= 210){
          charData[6] =charData[6]+ 1
        }
        else if (el.value <= 240){
          charData[7] =charData[7]+ 1
        }
        else if (el.value <= 270){
          charData[8] =charData[8]+ 1
        }
        else if (el.value <= 300){
          charData[9] =charData[9]+ 1
        }
        else if (el.value <= 330){
          charData[10] =charData[10]+ 1
        }
        else if (el.value <= 360){
          charData[11] =charData[11]+ 1
        }
        else{
          charData[12] =charData[12]+ 1
        }
      }
    })
  
    this.charBGColor.sort(() => Math.random() - 0.2);
    this.totalDefaultersBarChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }

      ]
    }
  }

  callTotalDefaultersPieChartYn() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    for (var n = 0; n < this.displayTotalDefaulters.length; n++) {
      if (listGroup.filter(s => s.defaulter === this.displayTotalDefaulters[n].value).length == 0) {
        const result = this.displayTotalDefaulters.filter(s => s.value == this.displayTotalDefaulters[n].value).length;
        let a = { defaulter: this.displayTotalDefaulters[n].value }
        charlabels.push(this.displayTotalDefaulters[n].value== 'Y'? 'Yes':'No');
        charData.push(result);
        listGroup.push(a);
      }
    }


    this.charBGColor.sort(() => Math.random() - 0.2);
    this.totalDefaultersPieChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }

      ]
    }
  }

  callChartVaccine() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];
    this.displayVaccine = this.displayVaccine.filter(s => s.value != null);


    for (var n = 0; n < this.displayVaccine.length; n++) {

      if (listGroup.filter(s => s.vaccineName === this.displayVaccine[n].value).length == 0) {
        const result = this.displayVaccine.filter(s => s.value == this.displayVaccine[n].value).length;
        let a = { vaccineName: this.displayVaccine[n].value }
        charlabels.push(this.displayVaccine[n].value);
        charData.push(result);
        listGroup.push(a);
      }
    }


    this.charBGColor.sort(() => Math.random() - 0.2);
    this.vaccineChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }



  }


  callTotalVaccinePieChart(listData: any[]) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";

    if (this.filterType === "institute") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);
      this.totalVaccineChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    



  }
  /* ------------  call Chart ---------------- */

}
