import { TbPatientInfo } from "./patient-info.model";

export class ancRequestResult{

    public requestId:number;
    public statusInLastPregnancy: number;
    public statusDate: Date;
    public requestedDate:Date;
    public status: string;
    public requestedInstRemarks: string;
    public requestedInst: string;
    public parity:number;
    public civilId: number;
    public instPregnancyId: number;
    public gravida: number;
    public mensturalCycle: number;
    public mensturalLength: string;
    public mensturalDuration: string;
    public ancInstitute:number;
    public lmp: Date;
    public eddCalc:Date;
    public eddScan:Date;
    public congAnamoliesYN: string;
    public createdBy: number;
    public createdDate: Date;
    public modifiedBy: number;
    public modifiedDate: Date;
    public lastDeliveryDate:Date;
    public ancNo:String;
    public lastAbortionDate: Date;
    public rgTbPatientInfo: TbPatientInfo;
    

}