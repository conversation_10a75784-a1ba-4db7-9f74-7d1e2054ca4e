import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import * as AppUtils from '../../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class AncRegisterListingService {

  constructor(private http:HttpClient) { 

  }
  getAncRegisterListing(date): Observable<any> {
  
    return this.http.post(AppUtils.GET_REGISTER_ANC_DETAILS, date);
   
  }
}
