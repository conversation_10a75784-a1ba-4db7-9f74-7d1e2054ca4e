import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { MasterService } from '../../_services/master.service';
import * as AppCompUtils from '../../common/app.component-utils';
import { ICDList } from '../../common/objectModels/icdList-models';
import { RenalListResult } from '../../_models/renal-list-result.model';
import { GridOptions } from 'ag-grid-community';
import * as AppUtils from '../../common/app.utils';
import { RenalService } from '../renal.service';
import { SharedService } from '../../_services/shared.service';
import { Router } from '@angular/router';
import { RenalExportExcel } from 'src/app/_models/renal-export-excel';
import Swal from 'sweetalert2';
import * as FileSaver from 'file-saver';
import * as moment from 'moment';
import { RenalExcel } from 'src/app/_models/renalExcel';
declare var swal: any;
@Component({
  selector: 'app-renal-listing',
  templateUrl: './renal-listing.component.html',
  styleUrls: ['./renal-listing.component.scss']
})
export class RenalListingComponent implements OnInit {

  renalSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  displayedResults: any[] = [];
allResults: any[] = [];
  yesNo = AppCompUtils.YES_NO;
  status = AppCompUtils.STATUS_PATIENT;
  gender = AppCompUtils.GENDER;
  icdList: Array<ICDList>;
  renalExcel : Array<RenalExcel> = new Array<RenalExcel>();
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  rowData: Array<RenalListResult> = new Array<RenalListResult>();
  dataList: Array<RenalExportExcel> = new Array<RenalExportExcel>();
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  columnDefs: any[];
  
  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };
  initialGetList: () => void;
  renderBloodGroup(params: any): string {
    const bloodGroupID = params.value; // Assuming the blood group ID is provided as a number
    let bloodGroupLabel = '';
  

    switch (bloodGroupID) {
      case 1:
        bloodGroupLabel = 'A +';
        break;
      case 2:
        bloodGroupLabel = 'A -';
        break;
        case 3:
        bloodGroupLabel = 'B +';
        break;
      case 4:
        bloodGroupLabel = 'B -';
        break;
        case 5:
        bloodGroupLabel = 'O +';
        break;
      case 6:
        bloodGroupLabel = 'O -';
        break;
        case 7:
        bloodGroupLabel = 'AB +';
        break;
      case 8:
        bloodGroupLabel = 'AB -';
        break;
        default:
    bloodGroupLabel = 'Unknown Blood Group';
    break;
    }
  
    return bloodGroupLabel;
  }

  getCalculatedBmi = (data) => {
    if (data.data) {
      let weight = data.data.weight;
      let height = data.data.height; 
      let bmi = this._sharedService.calculateBMI(weight, height)
      return bmi;
    } else {
      return '';
    }
  };
  
  constructor(private _router: Router, private _masterService: MasterService, private _renalService: RenalService, private _sharedService: SharedService ,private formBuilder: FormBuilder) {

    this.getMasterData();
    this.renalSearchForm =this.formBuilder.group({
      'civilId':  [null],
      'centralRegNo': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'sex': [""],
      'regCode': [null],
      'walCode':[null],
      'estCode': [null],
      'regionData': [null],
      'causeKidney':  [""],
      'stages': [null],
      'dialysis':  [""],
      'trasplant':  [""],
      'bloodGroup':  [""],
      'readiness':  [""],
      'status':  [""],
      'regType': [null],
      'dialysisDurationFrom' : [null],
      'dialysisDurationTo' : [null],
    });



    this.columnDefs = [
      { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125 ,sortable: true},
      { headerName: 'Civil ID', field: 'civilId', minWidth: 125 ,sortable: true},
      { headerName: 'Name', field: 'fullname', minWidth: 150,sortable: true },
      { headerName: 'Age', field: 'age', minWidth: 100 ,sortable: true},
      { headerName: 'DoB', field: 'dob', minWidth: 150 ,sortable: true},
      { headerName: 'Gender', field: 'sex', minWidth: 100,sortable: true },
      { headerName: 'Width : Hight = BMI', field: 'bmi', minWidth: 100,sortable: true, cellRenderer: this.getCalculatedBmi },
      { headerName: 'Dialysis Date',field: 'dialysisDate', cellRenderer: this.getDateFormat},
      { headerName: 'BloodGroup', field: 'bloodGroup', minWidth: 100, sortable: true, cellRenderer: this.renderBloodGroup },
    ];
  }

  ngOnInit() {
    // the below mwthod call to set the list for renal registry
    this._masterService.getIcdList(); 
    this._masterService.getIcdRenalShortList(); 
  }
  trackByFn(index, item) {
    return index;  
  }  
  getMasterData(regCode: any = 0, walCode: any = 0) {
   /* this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {

    });

       this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

      this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {

    });


*/
    this._masterService.getRegionsMasterFull(); 
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });

    //  this._masterService.getInstitutesMaster();
    // this._masterService.institutesMaster.subscribe(value => {
    //   this.institeList = value;
    //   this.institeListFilter = this.institeList;
    // });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res=>{
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
     })

     this._masterService.getIcdList(); 
     this._masterService.icdList.subscribe(value => {
       this.icdList = value;
     });
    // this._masterService.getIcdList().subscribe(response => {
    //   this.icdList = response.result;
    // }, error => {
    // });

  }

  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }
  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }


bloodGroupSelect(event: any, group?: any) {
  const selectedBloodGroups = this.renalSearchForm.get('bloodGroup') as FormControl;
  const selectedValue = event.target.value;
  const isChecked = event.target.checked;

  // Get the current value of selected blood groups
  let currentSelectedValues = selectedBloodGroups.value ? [...selectedBloodGroups.value] : [];

  // Get the ID of the selected blood group
  const bloodGroup = this.bloodGroupList.find(group => group.value === selectedValue);
  const selectedId = bloodGroup ? bloodGroup.id : null;

  // Update the currentSelectedValues array based on the checked state
  if (isChecked && selectedId && !currentSelectedValues.includes(selectedId)) {
      currentSelectedValues.push(selectedId);
  } else if (!isChecked && selectedId) {
      currentSelectedValues = currentSelectedValues.filter(id => id !== selectedId);
  }

  console.log('Updated Selected Values:', currentSelectedValues); // Debugging log

  // Update the selected blood groups with the modified array
  selectedBloodGroups.setValue(currentSelectedValues);

  // Filter the results based on the updated selected blood groups and update the displayed results
  this.updateDisplayedResults(currentSelectedValues);
}


// Function to filter results based on selected blood groups
updateDisplayedResults(selectedValues: string[]) {
  // Implement the logic to filter the results based on the selected blood group IDs
  if (selectedValues.length === 0) {
      // If no blood group is selected, display all results
      this.displayedResults = this.allResults;
  } else {
      // Filter the results to display only those matching the selected blood group IDs
      this.displayedResults = this.allResults.filter(result => selectedValues.includes(result.bloodGroupID.toString()));
  }
}


  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.renalSearchForm.value['regCode'] && (this.renalSearchForm.value['regCode'] != null || this.renalSearchForm.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.renalSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.renalSearchForm.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.renalSearchForm.value['regCode'] && (this.renalSearchForm.value['regCode'] != null || this.renalSearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.renalSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.renalSearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }
  /* ------------  filter action   ---------------- */

  getList() {

    let body = this.renalSearchForm.value;

    if ( body['causeKidney'] == "") {
      body['causeKidney'] = null;
    }
    if ( body['dialysis'] == "") {
      body['dialysis'] = null;
    }
    if ( body['sex'] == "") {
      body['sex'] = null;
    }
    if ( body['trasplant'] == "") {
      body['trasplant'] = null;
    }
    if ( body['bloodGroup'] == "") {
      body['bloodGroup'] = null;
    }

    if ( body['readiness'] == "") {
      body['readiness'] = null;
    }

    if ( body['status'] == "") {
      body['status'] = null;
    }


 
    this._renalService.getRenalListing(body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result'];
        for (var i = 0; i < this.rowData.length; i++) {
          this.rowData[i].dialysis = this.yesNo.filter(s => s.id == this.rowData[i].dialysis).map(s => s.value).toString();
          this.rowData[i].trasplant = this.yesNo.filter(s => s.id == this.rowData[i].trasplant).map(s => s.value).toString();
          this.rowData[i].sex = this.gender.filter(s => s.id == this.rowData[i].sex).map(s => s.value).toString();
          let bmi =  this._sharedService.calculateBMI(this.rowData[i].width, this.rowData[i].hight);
          if (bmi != null)
          this.rowData[i].bmi = this.rowData[i].width+" : "+this.rowData[i].hight + "   = "+bmi;
        }

        this.rowData.forEach(el => {
          

          this.dataList.push({centralRegNo:el.centralRegNo, civilId: el.civilId,Name:el.fullName,
           age: el.age , dob: el.dob, gender : el.sex, bloodGroup: el.bloodGroup})

        })



      } else {
        this.rowData = null;
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

      Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })

  }
  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['renal/registry'], { state: { centralRegNo: event.data.centralRegNo } });
    //this._router.navigate([AppUtils.RENAL_LINK], { state: { centralRegNo: event.data.centralRegNo } });
  }
  clear(e) {
    this.renalExcel = null;
    this.renalSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;
  }

  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }


  // exportExcel() {
  //   let body = this.renalSearchForm.value;
  //   let dialysis;
  //   let trasplant;
  //   let bloodGroup;
  //   let readiness;
  //   let status;

  //   if ( body['causeKidney'] == "") {
  //     body['causeKidney'] = null;
  //   }
  //   if ( body['dialysis'] == "") {
  //     body['dialysis'] = null;
  //   }
  //   if ( body['sex'] == "") {
  //     body['sex'] = null;
  //   }
  //   if ( body['trasplant'] == "") {
  //     body['trasplant'] = null;
  //   }
  //   if ( body['bloodGroup'] == "") {
  //     body['bloodGroup'] = null;
  //   }

  //   if ( body['readiness'] == "") {
  //     body['readiness'] = null;
  //   }

  //   if ( body['status'] == "") {
  //     body['status'] = null;
  //   }
 
  // let searchFilter = {
  //   regType: AppUtils.REG_TYPE_RENAL,
  //   civilId: body.civilId,
  //   centralRegNo: body.centralRegNo,
  //   ageFrom: body.ageFrom,
  //   ageTo: body.ageTo,
  //   sex: body.sex,
  //   regCode: body.regCode,
  //   walCode: body.walCode,
  //   estCode: body.estCode,
  //   regionData:body.regionData,
  //   causeKidney:body.causeKidney,
  //   stages:body.stages,
  //   dialysis:body.dialysis,
  //   trasplant:body.trasplant,
  //   bloodGroup:body.bloodGroup,
  //   readiness:body.readiness,
  //   status:body.status, 
  //   dialysisDurationFrom: body.dialysisDurationFrom,
  //   dialysisDurationTo: body.dialysisDurationTo,
  // };


  // this._renalService.getRenalExcel(searchFilter).subscribe(response => {
  //   this.dataList = response;


  //    const file = new Blob([response], { type: response.type.toString() });
    
  //   FileSaver.saveAs(file, "excel.xlsx");
  

  //  });

  // }

  //renalExcel
  exportExcel(){
    if (this.rowData && this.rowData.length >0){
      this.formatData(this.rowData);
    }else {
      Swal.fire('Warning!', 'No Records to explor', 'warning')
  }
  }

  formatData(rowData){
    this.renalExcel = [];
    rowData.forEach(el => {
      let calBmi =  this._sharedService.calculateBMI(el.width,el.hight);
      let bmiFull = el.width +" : " + el.hight + " = " + calBmi
      this.renalExcel.push({CentralRegNo:el.centralRegNo, 
        CivilId: el.civilId, Name: el.fullName, Age: el.age,
        Dob:el.dob,Gender:el.sex, BMI:bmiFull,Dialysis_Date:el.dialysisDate})
    })
    this._sharedService.exportAsExcelFile(this.renalExcel, "Renal_Listing");
  }


}


   

   












  // exportToExcel() {

  //   if (this.dataList && this.dataList.length > 0) {

  //      this._sharedService.exportAsExcelFile(this.dataList, "Renal_Listing"); }

  //    else {

  //     Swal.fire('Warning!', 'Please search first', 'warning')

  //  }
