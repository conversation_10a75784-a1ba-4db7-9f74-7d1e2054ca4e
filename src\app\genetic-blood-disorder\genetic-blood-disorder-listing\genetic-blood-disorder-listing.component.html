<h6>Genetic Blood Disorder Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="geneticBloodDisorderSearch" (ngSubmit)="onSubmit()">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil Id</label>
                    <input type="text"  class="form-control form-control-sm" formControlName="civilId">
                </div>

                
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registration No</label>
                    <input type="text" type="number" class="form-control form-control-sm"
                        formControlName="centralRegNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Sex</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age (from)</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="ageFrom">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>To</label>
                    <input type="text" type="number" class="form-control form-control-sm" formControlName="ageTo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="regSelect($event,'region')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode"
                        (change)="walSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="estCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Disorder Type</label>
                    <ng-select #entryPoint [items]="disorderType" [virtualScroll]="true" placeholder="Select"
                        bindLabel="geneticTypeDesc" bindValue="geneticTypeId" formControlName="disorderType"
                        (change)="getLabComponents()">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.geneticTypeDesc}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Genotype</label>
                    <ng-select #entryPoint [items]="genotype" [virtualScroll]="true" placeholder="Select"
                        bindLabel="description" bindValue="id" formControlName="genotype">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.description}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Genotype Base</label>
                    <ng-select #entryPoint [items]="genotypeBase" [virtualScroll]="true" placeholder="Select"
                        bindLabel="description" bindValue="id" formControlName="genotypeBase">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.description}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Test Done</label>
                    <ng-select #entryPoint [items]="testDone" [virtualScroll]="true" placeholder="Select"
                        bindLabel="testName" bindValue="mohTestCode" formControlName="testDone"
                        (change)="getLabComponents()">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.testName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Component Done</label>
                    <ng-select #entryPoint [items]="componentTest" [virtualScroll]="true" placeholder="Select"
                        bindLabel="componentTestName" bindValue="componentTestID" formControlName="componentTest">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.componentTestName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Numeric Value</label>
                    <input type="text" type="number" class="form-control form-control-sm"
                        formControlName="numericValue">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Qualitative Value</label>
                    <input type="text" type="number" class="form-control form-control-sm"
                        formControlName="qualitativeValue">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Medicine</label>
                    <ng-select #entryPoint [items]="medicine" [virtualScroll]="true" formControlName="medicine"
                        placeholder="Select" bindLabel="medicineMasterValue" bindValue="medicineMasterID">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{
                            item.medicineMasterValue }}
                        </ng-template>
                    </ng-select>

                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Complication</label>
                    <ng-select #entryPoint [items]="complicationMastList" [virtualScroll]="true" formControlName="complicationMastList"
                    placeholder="Select" bindLabel="description" bindValue="id">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description }}
                    </ng-template>
                  </ng-select>

                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Surgery</label>
                    <ng-select #entryPoint [items]="surgeryMaster" [virtualScroll]="true" placeholder="Select"
                    bindLabel="procName" bindValue="procID" formControlName="surgeryMaster">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.procName }}
                    </ng-template>
                  </ng-select>

                </div>
            </div>

            <!-- <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Vaccination</label>
                    <ng-select #entryPoint [items]="vaccinMastList" [virtualScroll]="true"
                    placeholder="Select" bindLabel="vaccineName" bindValue="vaccineId"
                    formControlName="vaccinMastList">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.vaccineName }}
                    </ng-template>
                </ng-select>

                </div>
            </div> -->



            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="submit"  (click)="exportExcel()" class="btn btn-primary ripple" > Excel</button>
                    <button type="submit" class="btn btn-sm btn-secondary" (click)="clear($event)">Clear</button>
                    <button type="submit" class="btn btn-sm btn-primary" (click)="getList()">Search</button>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="content-wrapper">
    <ag-grid-angular style="width: 100%; height: 450px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" [gridOptions]="gridOptions" (rowDoubleClicked)="onCellDoubleClicked($event)">
    </ag-grid-angular>
</div>