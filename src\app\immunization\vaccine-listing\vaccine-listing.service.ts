import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import * as AppUtils from '../../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class VaccineService {

  constructor(private http: HttpClient) { }

   // vaccine listing
   getVaccineListing(data:any): Observable<any> {
    return this.http.post(AppUtils.SEARCH_VACCINE_REGISTRY, data);
    
  }

  getVaccineStockMast(estCode:any,vaccineId:any): Observable<any>{
    return this.http.get(AppUtils.VACCINE_STOCK_MAST,{
      params: new HttpParams().set("estCode",estCode).set("vaccineId", vaccineId)
    })
    }

  getTbVaccineMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_TB_VACCINE_MASTER);
}

}





    
