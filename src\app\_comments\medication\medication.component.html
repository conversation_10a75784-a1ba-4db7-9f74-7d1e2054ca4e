<div class="content-wrapper mb-2 lab-results">
    <div [formGroup]="medicationForm">
        <div class="text-right pb-2">
            <button *ngIf="showAddNewButton" (click)="onAddNewMed()" class="btn btn-sm btn-primary">Add New</button>
            <button  *ngIf="showMedicineButton" (click)="getMedication()" class="btn btn-sm btn-primary">Download</button>
        </div>
        <p-dataTable [immutable]="false" [value]="medFg" [editable]="true" dataKey="runId" paginator="true" rows="10"
            paginatorTemplate="{CurrentPageReport} {FirstPageLink}
    {PreviousPageLink} {PageLinks} {NextPageLink}
    {LastPageLink} {RowsPerPageDropdown}" rowsPerPageTemplate="5,10,15">


            <p-column field="medName" header="Medicine">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    <ng-container formArrayName="rgTbMedicineDtls">
                        <div [formGroupName]="rowIndex">
                            <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                            <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy">
                            <input type="hidden" class="form-control form-control-sm" formControlName="enteredDt">
                            <div *ngIf="!row.isEditable">
                                {{getMedicineName(row.medicineID)}}
                            </div>
                            <div *ngIf="row.isEditable">
                                <ng-select #entryPoint [items]="medicine" [virtualScroll]="true"
                                    formControlName="medicineID" placeholder="Select Medicine"
                                    bindLabel="medicineMasterValue" bindValue="medicineMasterID">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{item.medicineMasterValue }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>


            <p-column field="startDate" header="Start Date">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    <ng-container formArrayName="rgTbMedicineDtls">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.startDate | date:'dd-MM-yyyy'}}</div>
                            <div *ngIf="row.isEditable">
                                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="startDate"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>


                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>

            <p-column field="dose" header="Dose">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    <ng-container formArrayName="rgTbMedicineDtls">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.dose}}</div>
                            <div *ngIf="row.isEditable">
                                <input type="text" class="form-control form-control-sm" formControlName="dose">
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>
            <p-column field="frequency" header="Frequency">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    <ng-container formArrayName="rgTbMedicineDtls">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">
                            {{getFrequencyName(row.frequency)}}
                                </div>
                            <div *ngIf="row.isEditable">
                                <ng-select #entryPoint [items]="medFrequencyList" [virtualScroll]="true"
                                    formControlName="frequency" placeholder="Select Frequency" bindLabel="coursename"
                                    bindValue="courseId">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.coursename }}
                                    </ng-template>
                                </ng-select>

                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>

            <p-column field="medicineType" header="Medicine Type">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                    <ng-container formArrayName="rgTbMedicineDtls">
                        <div [formGroupName]="rowIndex">
                            <!--  <div *ngIf="!row.isEditable">{{row.medTypeName}} </div> -->
                            <div>
                                <ng-select #entryPoint [items]="medicineTypeList" [virtualScroll]="true"
                                    formControlName="medicineType" placeholder="Select Medicine Type"
                                    bindLabel="description" bindValue="id">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>








            <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                <ng-template let-row="rowData" pTemplate="body">
                    <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                        class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>

                    <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                        class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
                </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                <ng-template let-row="rowData" pTemplate="body">
                    <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
                </ng-template>


            </p-column>

        </p-dataTable>
    </div>

</div>