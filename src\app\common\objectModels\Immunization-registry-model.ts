import { TbPatientInfo } from 'src/app/_models/patient-info.model';
import {rgTbImmunization } from "./Immunization-model";

export class ImmunizationRegistryForm{
     
    public centralRegNo: number;
    public activeYn: string;
    public patientID:number;
    public civilId:number;
    public completedOn: any;
    public createdBy: number;
    public createdOn: any;
    public instRegDate: any;
    public modifiedBy: number;
    public modifiedOn: any;
    public regInst: number;
    public registerType: number;
    public localRegReferance: string;
    public rgTbPatientInfo: TbPatientInfo;
    public estImmunization:Array<rgTbImmunization>;

   
    


}