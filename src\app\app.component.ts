import { Router } from '@angular/router';
import { User } from './_models/user.model';
import { AuthenticationService } from './_services/authentication.service';
import { Component, Input, TemplateRef } from '@angular/core';
import * as AppUtils from './common/app.utils';
import { SharedService } from './_services/shared.service';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { LoaderService } from './_services/loader.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  providers: [SharedService]
})

export class AppComponent {
  modalRef: BsModalRef;
  user: User;
  title = 'eregistry';
  authenticated = false;
  toggleMenu = false;
  menuList: any;
  selectedMenu: any;
  selectedSubMenu: number | null = null;
  currentUser: User;
  loggedInPersonName: string;
  CHAR_GENERAL_OPTION: any;
  CHAR_PIE_OPTION: any;
  CHAR_BG_COLOR: any[];
  submittedPws: boolean;



  constructor(private modalService: BsModalService, public data: AuthenticationService, private router: Router, private authenticationService: AuthenticationService, public _sharedService: SharedService, public loader: LoaderService
  ) {

    this.authenticationService.currentUser.subscribe(x => {
      if (typeof x === 'string' || x instanceof String) {
        x = <User>JSON.parse(x.toString());
      }
      this.currentUser = x;

      if (this.currentUser) {
        this.toggleMenu = true;

        this.loggedInPersonName = this.currentUser.person.personName;
      }
    });
  }

  showSidebar() {
    this.toggleMenu = !this.toggleMenu;
  }

  // select(item) {
  //   this.selected = (this.selected === item ? null : item);
  // }

  selectMenu(menu: any) {
    // Collapse other main menus when clicking a new one
    this.selectedMenu = this.selectedMenu === menu.menuName ? null : menu.menuName;
    this.selectedSubMenu = null; // Reset submenu selection
    //localStorage.setItem('selectedSubMenu', JSON.stringify(submenu.menuId));
    //localStorage.setItem(AppUtils.MENU_ID, JSON.stringify(menu.menuId));
    //console.log('Selected menu:', menu.menuId);
  }

  selectSubMenu(submenu: any) {
    this.selectedSubMenu = submenu.menuId;
    localStorage.setItem('selectedSubMenu', JSON.stringify(submenu.menuId));
    localStorage.setItem(AppUtils.MENU_ID, JSON.stringify(submenu.menuId));
    //console.log('Selected Submenu:', this.selectedSubMenu);
  }

  isActive(item) {
    return this.selectedMenu === item;
  }

  logout() {
    this.toggleMenu = false;
    this.data.logout();
  }
  // menuclick(menuId: number) {
  //   localStorage.setItem(AppUtils.MENU_ID, JSON.stringify(menuId));
  // }

  openModal(template: TemplateRef<any>) {

    this.modalRef = this.modalService.show(template, { class: 'custom-modal' });
  }

  handleCloseEvent(): void {
    this.modalRef.hide()
  }
}
