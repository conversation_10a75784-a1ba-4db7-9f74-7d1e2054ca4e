import { DatePipe } from "@angular/common";
import {Component,AfterViewInit, ViewChild} from "@angular/core";
import { ICellEditorAngularComp } from "ag-grid-angular";
import * as moment from 'moment';
import { Calendar } from "primeng/primeng";
import * as AppUtils from '../../common/app.utils';


@Component({
    selector: 'app-grid-date',
    template: `<p-calendar #calendar class="form-control" appendTo="body" dateFormat="dd-mm-yy" (onSelect)="onSelectDate($event)"
    [showTime]="false" [readonlyInput]="false" showButtonBar="true" monthNavigator="true" yearNavigator="true" (onClearClick)="onClear()"
    yearRange="1901:2030" (onBlur)="onInput($event)" placeholder="dd-mm-yyyy" [(ngModel)]="dateValue" [minDate]="minDate"
    [maxDate]="maxDate"></p-calendar>`,

    styles: [
      `
      .container {
          border-radius: 15px;
          border: 1px solid grey;
          background: #fff;
          width: 190px;
          height: 45px;
          padding-left: 15px;
      }

      .container:focus {
          outline: none;
      }
  `]
  })


export class GridDateComponent implements ICellEditorAngularComp, AfterViewInit {
  private params: any;
  private inline;
  private setColumnValue;
  dateValue : Date;
  column : any;
  minDate: Date;
  maxDate: Date;
  
  @ViewChild("calendar",{ static: false }) calendar : Calendar;

  ngAfterViewInit(){
    setTimeout(() => {
      this.calendar.inputfieldViewChild.nativeElement.focus();
    }, 0);
    
  }

  agInit(params: any): void {
    this.minDate = null;
    this.maxDate = null;
    this.setColumnValue = params.value;
    this.column = params.column.colId
    this.inline = true;
    this.params = params;
    if(this.setColumnValue != null){
      this.dateValue = moment(this.setColumnValue,AppUtils.DATE_WITH_HYPHEN).toDate() ;
    }
    this.getValue();

  //   if (params.column.colId == this.params.date1) {
  //     this.minDate = null;
  //     this.maxDate = moment(this.params.node.data[this.params.date2], AppUtils.DATE_WITH_HYPHEN).toDate();
  // }

  // if (params.column.colId == this.params.date2) {
  //     this.minDate = moment(this.params.node.data[this.params.date1], AppUtils.DATE_WITH_HYPHEN).toDate();
  //     this.maxDate = null;
  // }

  if (params.column.colId == this.params.date1) {
    this.minDate = null;
    this.maxDate = this.params.date2 ? moment(this.params.node.data[this.params.date2], AppUtils.DATE_WITH_HYPHEN).toDate() : null;
}

if (params.column.colId == this.params.date2) {
    this.minDate = this.params.date1 ? moment(this.params.node.data[this.params.date1], AppUtils.DATE_WITH_HYPHEN).toDate() : null;
    this.maxDate = null;
}

if(!params.allowFutureDate && (this.maxDate == <any>"Invalid Date" || !this.maxDate)){
   this.maxDate = new Date((new Date()).getTime());
} 

  }


  getValue() {
    this.params.node.setDataValue(this.params.columnApi.getColumn(this.column), this.setColumnValue);
    
    if(this.params.node.data[this.params.date1] && this.params.node.data[this.params.date2] && this.params.totColumn){
       let date1 = moment(this.params.node.data[this.params.date1], 'YYYY-MM-DD').format('YYYY-MM-DD');
       let date2 = moment(this.params.node.data[this.params.date2], 'YYYY-MM-DD').format('YYYY-MM-DD') 
       let diff = moment(date2).diff(date1, "days")+1;
       this.params.node.setDataValue(this.params.columnApi.getColumn(this.params.totColumn),diff );
    }
   return this.setColumnValue;
}

  
  onSelectDate(event){
    this.getDisplayedValue(event);
    this.inline = false
  }

  onClear(){
    this.setColumnValue = null ;
  }

  getDisplayedValue(event){
      this.setColumnValue = moment(event).format(AppUtils.DATE_WITH_HYPHEN) ;
      this.getValue();
    
    
  }

onInput(value: any){
  if(value.target.value){
    this.validateDate(value);
  }
  
}

private validateDate(date : any)
                        {
                let splitDate =  date.target.value.split("-");
                let month                 =  splitDate[1];
                let day                   =  splitDate[0];
                let year                        =  parseInt(splitDate[2]);
                let daysInMonth         =  0;
                if(parseInt(month) > 12){
      alert("Please enter a valid date, accepted format [dd-mm-yyyy]");
      return false;
                }
                if(this.getSize(parseInt(day)) > 2){
      alert("Please enter a valid date, accepted format [dd-mm-yyyy]");
      return false;
                }
                else{
                if (month === "04" || month === "06" || month === "09"  || month === "11"){
                        daysInMonth = 30;
                        
                        if(daysInMonth < parseInt(day)){
        alert("Month does not have more than 30 days");
        return false;
                        }
                }else if (month === "02") {
//                        daysInMonth = isLeapYear(year)  ? 29 : 28;
                        if(this.isLeapYear(year)){
                                daysInMonth = 29;
                                if(daysInMonth < parseInt(day)){
          alert("Month does not have more than 29 days");
          return false;
                                }
                        }else{
                                daysInMonth = 28;
                                if(daysInMonth < parseInt(day)){
          alert("Month does not have more than 28 days");
          return false;
                                }
                        }
                }else{
                        daysInMonth = 31;
                        if(daysInMonth < parseInt(day)){
        alert("Month does not have more than 31 days");
        return false;
                        }
                }                
                
        }
        }
        
        public isLeapYear(year: number) : boolean {
                  if (year % 4 != 0) {
                    return false;
                  } else if (year % 400 == 0) {
                    return true;
                  } else if (year % 100 == 0) {
                    return false;
                  } else {
                    return true;
                  }
                }
        
        public getSize(number : any) : number {
        return parseInt(number.toString().length);
    }
  

}