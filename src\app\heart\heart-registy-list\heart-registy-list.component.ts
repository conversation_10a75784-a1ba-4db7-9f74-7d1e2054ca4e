import { Component } from '@angular/core';
import { HeartService } from '../heart.service';
import { FormControl, FormGroup } from '@angular/forms';
import * as AppCompUtils from '../../common/app.component-utils';
import * as AppUtils from '../..//common/app.utils';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { GridOptions } from 'ag-grid-community';
import { Router } from '@angular/router';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { ICDHeartList } from 'src/app/common/objectModels/icdList-models';
import { HeartListResult } from 'src/app/_models/heart-list-result.model';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
@Component({
  selector: 'flst',
  templateUrl: './heart-registy-list.component.html',
  styleUrls: ['./heart-registy-list.component.scss'],
  providers: [HeartService]
})
export class HeartRegistyListComponent {
  heartSearchForm: FormGroup;

  totalRecords: number;
  paginationSize = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  regionData: RegionDataModel[] = [];
  wallayatList: any[] = [];
  wallayatListFilter: any[] = [];
  instituteList: any[] = [];
  instituteListFilter: any[] = [];

  yesNoOptions = AppCompUtils.YES_NO;
  genderOptions = AppCompUtils.GENDER;
  bloodGroupOptions = AppCompUtils.BLOOD_GROUP;

  icdHeartList: ICDHeartList[] = [];
  primaryDiagList: any[] = [];
  nyhaClassList: any[] = [];
  PhyBaselineList: any[] = [];
  rowData: HeartListResult[] = [];
  heartExportData: any[] = [];

  inotropeList: any[] = [];
  mcsDeviceList: any[] = [];
  inotropeDropdownSettings: any = {
    singleSelection: false,
    idField: 'paramId',
    textField: 'paramName',
    selectAllText: 'Select All',
    unSelectAllText: 'Unselect All',
    itemsShowLimit: 3,
    allowSearchFilter: true
  };
  mcsDeviceDropdownSettings: any = {
    singleSelection: false,
    idField: 'paramId',
    textField: 'paramName',
    selectAllText: 'Select All',
    unSelectAllText: 'Unselect All',
    itemsShowLimit: 3,
    allowSearchFilter: true
  };
  gridOptions: GridOptions;
  columnDefs: any[];
  constructor(private heartService: HeartService,
    private _router: Router,
    private _masterService: MasterService,
    private _sharedService: SharedService
  ) {
    this.initializeForm();
    this.setupControlSubscriptions();
    this.setupGridColumns();
    this.setupGridOptions();
    this.loadMasterData();
  }



  private initializeForm(): void {
    this.heartSearchForm = new FormGroup({
      regNo: new FormControl(),
      civilId: new FormControl(),
      regType: new FormControl(),
      sex: new FormControl(),
      ageFrom: new FormControl(),
      ageTo: new FormControl(),
      regCode: new FormControl(),
      walCode: new FormControl(),
      regInst: new FormControl(),

      causeHeart: new FormControl(),
      primaryDiag: new FormControl(),
      transplantRecommended: new FormControl(),
      transplantUrgent: new FormControl(),
      bloodGroup: new FormControl(),

      inotropeUsed: new FormControl(),
      mcsDeviceUsed: new FormControl(),
      nyhaClass: new FormControl(),
      physiotherapyBase: new FormControl(),
      inotrope: new FormControl([]),
      mcsDevice: new FormControl([]),
    });
  }

  private setupControlSubscriptions(): void {
    const mcsDeviceUsedCtrl = this.heartSearchForm.get('mcsDeviceUsed');
    if (mcsDeviceUsedCtrl) {
      mcsDeviceUsedCtrl.valueChanges.subscribe((val: any) => {
        if (val !== 'Y') {
          this.heartSearchForm.patchValue({ mcsDevice: [] }, { emitEvent: false });
        }
      });
    }
    const inotropeUsedCtrl = this.heartSearchForm.get('inotropeUsed');
    if (inotropeUsedCtrl) {
      inotropeUsedCtrl.valueChanges.subscribe((val: any) => {
        if (val !== 'Y') {
          this.heartSearchForm.patchValue({ inotrope: [] }, { emitEvent: false });
        }
      });
    }
  }

  private setupGridColumns(): void {
    const dateFormatter = (params: any) =>
      params.data[params.colDef.field]
        ? formatDate(params.data[params.colDef.field], AppCompUtils.DATE_FORMATS.STANDARD, 'en')
        : null;

    this.columnDefs = [
      { headerName: 'Registration No', field: 'regNo', minWidth: 60, sortable: true },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 90, sortable: true },
      { headerName: "Date of Birth", field: "dateOfBirth", minWidth: 120, sortable: true, valueFormatter: dateFormatter },
      { headerName: 'Age', field: 'age', minWidth: 60, sortable: true },
      { headerName: 'Full Name', field: 'fullName', minWidth: 310, sortable: true },
      {
        headerName: "Gender", field: "sex", minWidth: 90, sortable: true,
        valueFormatter: (params: any) => this.formatGender(params)
      },
      {
        headerName: 'Region', field: 'regCode', minWidth: 150, sortable: true,
        valueFormatter: (params: any) => this.formatRegionName(params)
      },
      {
        headerName: 'Wilayat', field: 'walCode', minWidth: 150, sortable: true,
        valueFormatter: (params: any) => this.formatWilayatName(params)
      },
      {
        headerName: 'Institute', field: 'regInst', minWidth: 300, sortable: true,
        valueFormatter: (params: any) => this.formatInstituteName(params)
      },
      { headerName: 'Weight (Kg)', field: 'weight', minWidth: 110, sortable: true },
      { headerName: 'Height (Cm)', field: 'height', minWidth: 110, sortable: true },
      {
        headerName: 'Blood Group', field: 'bloodGroup', minWidth: 130, sortable: true,
        valueFormatter: (params: any) => this.formatBloodGroup(params)
      },

      { headerName: 'Transplant Recommended', field: 'transplantRecommended', minWidth: 180, sortable: true, valueFormatter: (p: any) => this.formatYesNoValue(p) },
      { headerName: 'Transplant Urgent', field: 'transplantUrgent', minWidth: 150, sortable: true, valueFormatter: (p: any) => this.formatYesNoValue(p) },

      { headerName: 'Inotrope Used', field: 'inotropeUsed', minWidth: 140, sortable: true, valueFormatter: (p: any) => this.formatYesNoValue(p) },
      { headerName: 'NYHA Class', field: 'nyhaClass', minWidth: 120, sortable: true, valueFormatter: (p: any) => this.formatNyhaClass(p) },
      { headerName: 'MCS Device Used', field: 'mcsDeviceUsed', minWidth: 160, sortable: true, valueFormatter: (p: any) => this.formatYesNoValue(p) },
      {
        headerName: 'Physiotherapy Baseline', field: 'physiotherapyBase', minWidth: 170, sortable: true,
        valueFormatter: (p: any) => this.formatPhysiotherapyBase(p)
      },

      {
        headerName: 'Primary Diagnosis', field: 'primaryDiag', minWidth: 170, sortable: true, valueFormatter: (p: any) => this.formatPrimaryDiag(p)
      },
      { headerName: 'Cause of Heart Disease', field: 'icd', minWidth: 300, sortable: true, valueFormatter: (p: any) => this.formatIcdName(p) },

      { headerName: 'Death Case', field: 'deathCase', minWidth: 120, sortable: true },
      { headerName: 'Family History', field: 'familyHistory', minWidth: 140, sortable: true, valueFormatter: (p: any) => this.formatYesNoValue(p) }
    ];
  }

  private setupGridOptions(): void {
    this.gridOptions = {
      pagination: false,
      paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
      onGridReady: () => {
        this.gridOptions.api.setRowData(this.rowData);
        this.gridOptions.api.refreshCells({ force: true });
      },
      onGridSizeChanged: () => {
        this.gridOptions.api.sizeColumnsToFit();
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        suppressSizeToFit: true,
      },
      suppressPropertyNamesCheck: true,
      enableCellTextSelection: true
    };
  }

  private loadMasterData(regCode: number = 0, walCode: number = 0): void {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.instituteList = response.result;
      this.instituteListFilter = this.instituteList;
    });

    this._masterService.getIcdHeartShortList();
    this._masterService.icdHeartShortList.subscribe((value) => {
      this.icdHeartList = value;
      this.refreshGrid();
    });


    this._masterService.getAllPrimaryEtiology().subscribe((value) => {
      this.primaryDiagList = value['result'];
      this.refreshGrid();
    });

    this._masterService.getAllHeartNyhaClass().subscribe((value) => {
      this.nyhaClassList = value['result'];
      this.refreshGrid();
    });

    this._masterService.getHeartPhysioBaseline().subscribe((value) => {
      this.PhyBaselineList = value['result'];
      this.refreshGrid();
    });

    this._masterService.getAllHeartInotropes().subscribe((value) => {
      this.inotropeList = value['result'];
      this.refreshGrid();
    });

    this._masterService.getAllMCSHeartDevice().subscribe((value) => {
      this.mcsDeviceList = value['result'];
      this.refreshGrid();
    });

  }

  private isValid(value: any): boolean {
    return value !== undefined && value != null && value !== "null" && value > 0;
  }

  private extractIds(list: any): number[] {
    if (!Array.isArray(list)) return list;
    return list.map(item => {
      if (item && typeof item === 'object') {
        return item.paramId != null ? Number(item.paramId) : Number(item);
      }
      return Number(item);
    });
  }

  onRegionSelect(event: any): void {
    if (event != undefined) {
      if (!this.isValid(event.regCode)) {
        this.wallayatListFilter = this.wallayatList;
        this.instituteListFilter = this.instituteList;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.instituteListFilter = this.instituteList;
    }
  }

  onBloodGroupSelect(event: any): void {
    const selectedBloodGroups = this.heartSearchForm.get('bloodGroup') as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;
    let currentSelectedValues = selectedBloodGroups.value ? [...selectedBloodGroups.value] : [];
    const bloodGroup = this.bloodGroupOptions.find(group => group.value === selectedValue);
    const selectedId = bloodGroup ? bloodGroup.id : null;

    if (isChecked && selectedId && !currentSelectedValues.includes(selectedId)) {
      currentSelectedValues.push(selectedId);
    } else if (!isChecked && selectedId) {
      currentSelectedValues = currentSelectedValues.filter(id => id !== selectedId);
    }

    selectedBloodGroups.setValue(currentSelectedValues);
  }

  onWilayatSelect(event: any): void {
    if (event != undefined) {
      if (!this.isValid(event.walCode)) {
        if (this.heartSearchForm.value['regCode'] && (this.heartSearchForm.value['regCode'] != null || this.heartSearchForm.value['regCode'] != 0)) {
          this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.heartSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.heartSearchForm.value['regCode']);
        } else {
          this.instituteListFilter = this.instituteList;
        }
      } else {
        this.instituteListFilter = this.instituteList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.heartSearchForm.value['regCode'] && (this.heartSearchForm.value['regCode'] != null || this.heartSearchForm.value['regCode'] != 0)) {
        this.instituteListFilter = this.instituteList.filter(s => s.regCode == this.heartSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.heartSearchForm.value['regCode']);
      } else {
        this.instituteListFilter = this.instituteList;
      }
    }
  }


  onCellDoubleClicked(event: any): void {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['heart/heartRegister'], { state: { regNo: event.data.regNo } });
  }

  clearForm(): void {
    this.heartExportData = [];
    this.heartSearchForm.reset();
    // ensure multi-selects cleared explicitly
    this.heartSearchForm.patchValue({ mcsDevice: [], inotrope: [] }, { emitEvent: false });
    this.wallayatListFilter = this.wallayatList;
    this.instituteListFilter = this.instituteList;
    this.rowData = [];
  }

  exportToExcel() {

    if (!this.rowData || this.rowData.length === 0) {
      return Swal.fire('Warning!', 'No Records to export', 'warning');
    }

    const rawForm = { ...this.heartSearchForm.value };
    // clear dependent arrays if flags not 'Y'
    if (rawForm.mcsDeviceUsed !== 'Y') { rawForm.mcsDevice = []; }
    if (rawForm.inotropeUsed !== 'Y') { rawForm.inotrope = []; }
    const { inotrope, mcsDevice, ...rest } = rawForm;
    const searchBody = {
      ...rest,
      inotropes: this.extractIds(inotrope),
      mcsDevices: this.extractIds(mcsDevice),
      getAll: true,
      pageable: { page: 0, size: this.totalRecords }
    };
    this.heartService.getHeartListing(searchBody).subscribe({
      next: (res) => {
        const data = res['result']['content'] || [];
        if (!data.length) {
          return Swal.fire('Warning!', 'No Records to export', 'warning');
        }
        this.prepareExportData(data);
      },
      error: () => Swal.fire('Error!', 'Error occurred during export', 'error')
    });
  }

  private prepareExportData(rowData: HeartListResult[]): void {
    const exportRows: any[] = [];
    const exportableCols = (this.columnDefs || []).filter((c: any) => !!c.field && !!c.headerName);

    rowData.forEach((el) => {
      const row: any = {};
      exportableCols.forEach((colDef: any) => {
        const raw = (el as any)[colDef.field];
        let formatted = raw;
        if (typeof colDef.valueFormatter === 'function') {
          try {
            formatted = colDef.valueFormatter({ value: raw, data: el, colDef });
          } catch {
            formatted = raw;
          }
        }
        row[colDef.headerName] = formatted;
      });
      exportRows.push(row);
    });

    this.heartExportData = exportRows;
    this._sharedService.exportAsExcelFile(exportRows, 'Heart_Listing');
  }

  onSearch(event?: any): void {
    const pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };

    const searchFormValue = { ...this.heartSearchForm.value };

    // Ensure stale selections are not sent if flags not 'Y'
    if (searchFormValue.mcsDeviceUsed !== 'Y') {
      searchFormValue.mcsDevice = [];
    }
    if (searchFormValue.inotropeUsed !== 'Y') {
      searchFormValue.inotrope = [];
    }

    Object.keys(searchFormValue).forEach(key => {
      if (typeof searchFormValue[key] === 'string' && searchFormValue[key]) {
        searchFormValue[key] = searchFormValue[key].toUpperCase();
      }
    });

    const { inotrope, mcsDevice, ...rest } = searchFormValue;
    const searchBody = {
      ...rest,
      inotropes: this.extractIds(inotrope),
      mcsDevices: this.extractIds(mcsDevice),
      pageable
    };

    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }

    this.heartService.getHeartListing(searchBody).subscribe(
      res => {
        if (res['code'] == "S0000") {
          this.rowData = res['result']['content'];
          this.totalRecords = res['result']['totalElements'];

          if (this.gridOptions.api) {
            this.gridOptions.api.setRowData(this.rowData);
            setTimeout(() => {
              this.gridOptions.api.refreshCells({ force: true });
              this.gridOptions.api.redrawRows();
            }, 100);
          }
        } else {
          this.rowData = [];
          Swal.fire('Error!', res['message'], 'error');
        }
      },
      error => {
        if (error.status == 401) {
          Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
        }
      }
    );
  }

  formatYesNoValue(params: any): string {
    const yesNoValue = this.yesNoOptions.find(s => s.id == params.value);
    return yesNoValue ? yesNoValue.value : params.value || '';
  }
  formatPrimaryDiag(params: any): string {
    if (!params.value || !this.primaryDiagList || this.primaryDiagList.length === 0) {
      return params.value || '';
    }
    const diag = this.primaryDiagList.find((d: any) => d.paramId == params.value);
    return diag ? diag.paramName : params.value;
  }
  formatPhysiotherapyBase(params: any): string {
    if (!params.value || !this.PhyBaselineList || this.PhyBaselineList.length === 0) {
      return params.value || '';
    }
    const phyBase = this.PhyBaselineList.find((d: any) => d.paramId == params.value);
    return phyBase ? phyBase.paramName : params.value;
  }
  formatNyhaClass(params: any): string {
    if (!params.value || !this.nyhaClassList || this.nyhaClassList.length === 0) {
      return params.value || '';
    }
    const nyha = this.nyhaClassList.find((d: any) => d.paramId == params.value);
    return nyha ? nyha.paramName : params.value;
  }

  formatGender(params: any): string {
    if (params.value === 'M') return 'Male';
    if (params.value === 'F') return 'Female';
    return params.value || '';
  }

  formatBloodGroup(params: any): string {
    const bloodGroupValue = params.value !== undefined ? params.value : params;
    const bloodGroupId = typeof bloodGroupValue === 'string' ? parseInt(bloodGroupValue, 10) : bloodGroupValue;

    switch (bloodGroupId) {
      case 1: return 'A+';
      case 2: return 'A-';
      case 3: return 'B+';
      case 4: return 'B-';
      case 5: return 'O+';
      case 6: return 'O-';
      case 7: return 'AB+';
      case 8: return 'AB-';
      default: return bloodGroupValue ? bloodGroupValue.toString() : '';
    }
  }

  formatWilayatName(params: any): string {
    if (!params.value || !this.wallayatList || this.wallayatList.length === 0) {
      return params.value || '';
    }
    const walayat = this.wallayatList.find(w => w.walCode == params.value);
    return walayat ? walayat.walName : params.value;
  }

  formatRegionName(params: any): string {
    if (!params.value || !this.regionData || this.regionData.length === 0) {
      return params.value || '';
    }
    const region = this.regionData.find(r => r.regCode == params.value);
    return region ? region.regName : params.value;
  }

  formatInstituteName(params: any): string {
    if (!params.value || !this.instituteList || this.instituteList.length === 0) {
      return params.value || '';
    }
    const institute = this.instituteList.find(i => i.estCode == params.value);
    return institute && institute.estName ? institute.estName : params.value.toString();
  }

  formatIcdName(params: any): string {
    if (!params.value || !this.icdHeartList || this.icdHeartList.length === 0) {
      return params.value || '';
    }
    const icd = this.icdHeartList.find(i => i.code == params.value);
    return icd ? icd.disease : params.value;
  }

  private refreshGrid(): void {
    if (this.rowData && this.rowData.length > 0 && this.gridOptions.api) {
      this.gridOptions.api.setRowData(this.rowData);
      this.gridOptions.api.refreshCells({ force: true });
    }
  }
}
