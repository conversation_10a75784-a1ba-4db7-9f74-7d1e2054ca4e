import { Component, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DonorInformationComponent } from './renal/donor-information/donor-information.component';
import { OrganDonorListingComponent } from './renal/organ-donor/organ-donor-listing/organ-donor-listing.component';
import { UsersManagmentComponent } from './users/users.component';
import { ElderlyDashboardComponent } from './elderly/elderly-dashboard/elderly-dashboard.component';
import { VaccinationComponent } from './_comments/vaccination/vaccination.component';
import { TestComponent } from './test/test.component';
import { DonorInformationListingComponent } from './renal/donor-information-listing/donor-information-listing.component'
import { ElderlyRegisterComponent } from './elderly/elderly-register/elderly-register.component';
import { SearchComponent } from './search/search.component';
import { LoginComponent } from './login/login.component';
import { LoginGuard } from './_helpers/login.guard';
import { AuthGuard } from './_helpers/auth.guard';
import { DashboardComponent } from './dashboard/dashboard.component';
import { LabResultsComponent } from './_comments/lab-result-listing/lab-result-listing.component';
import { RenalWaitingListListingComponent } from './renal/renal-waiting-list-listing/renal-waiting-list-listing.component';
import { RenalWaitingListComponent } from './renal/renal-waiting-list/renal-waiting-list.component';
import { KidneyDiseaseStagesComponent } from './renal/kidney-disease-stages/kidney-disease-stages.component';
import { TransplantFollowUpComponent } from './renal/transplant-follow-up/transplant-follow-up.component';
import { TissueTypingComponent } from './renal/tissue-typing/tissue-typing.component';
import { RenalListingComponent } from './renal/renal-listing/renal-listing.component';
import { RenalDashboardComponent } from './renal/renal-dashboard/renal-dashboard.component';
import { RenalRegisterComponent } from './renal/renal-register/renal-register.component';
import { ElderlyListingComponent } from './elderly/elderly-listing/elderly-listing.component';
import { GeneticBloodDisorderListingComponent } from './genetic-blood-disorder/genetic-blood-disorder-listing/genetic-blood-disorder-listing.component';
import { BloodDisorderComponent } from './genetic-blood-disorder/blood-disorder-registration/blood-disorder.component';
import { GeneticBloodDisorderDashboardComponent } from './genetic-blood-disorder/genetic-blood-disorder-dashboard/genetic-blood-disorder-dashboard.component';
import { clinicalDetailsComponent } from './_comments/clinical-details/clinical-details.component';
import { surgeryComponent } from './_comments/surgery/surgery.component';
import { HomeComponent } from './home/<USER>';
import { AlShifaLoginComponent } from './alshifa/alShifaLogin.component';
import { OrganDonorDashboardComponent } from './renal/organ-donor/organ-donor-dashboard/organ-donor-dashboard.component';
import { DiabeticDashboardComponent } from './diabetic/diabetic-dashboard/diabetic-dashboard.component';
import { DiabeticListingComponent } from './diabetic/diabetic-listing/diabetic-listing.component';
import { DiabeticRegistryComponent } from './diabetic/diabetic-registry/diabetic-registry.component';
import { BrainDeathRegistrationComponent } from './renal/brain-death/brain-death-registration/brain-death-registration.component';

import { BrainDeathListingComponent } from './renal/brain-death/brain-death-listing/brain-death-listing.component';
import { VaccineListingComponent } from './immunization/vaccine-listing/vaccine-lisiting.component';
import { VaccineRegisterComponent } from './immunization/vaccine-register/vaccine-register.component';
import { AncRegisterComponent } from './anc/anc-register/anc-register.component';
import { AncRequestRegistrationComponent } from './ancRequest/anc-request-registration.component';
import { AncListingComponent } from './anc/anc-listing/anc-listing.component';
import { CaseDetailsComponent } from './liver/case-details/case-details.component';
import { LiverRegisterComponent } from './liver/liver-register/liver-register.component';
import { LiverListingComponent } from './liver/liver-listing/liver-listing.component';
import { LiverWaitingListComponent } from './liver/liver-waiting-list/liver-waiting-list.component';
import { LiverWaitingListListingComponent } from './liver/liver-waiting-list-listing/liver-waiting-list-listing.component';
import { LiverDonorInformationListingComponent } from './liver/donor-information-listing/donor-information-listing.component';
import { LiverDonorInformationComponent } from './liver/donor-information/donor-information.component';
import { LiverTranplantComponent } from './liver/liver-transplant/liver-transplant.component';
import { LiverDashboardComponent } from './liver/liver-dashboard/liver-dashboard.component';


import { PreparingLiverWaitingListComponent } from './liver/preparing-liver-waiting-list/preparing-liver-waiting-list.component';

import { AncRegisterListingComponent } from './anc/anc-register-listing/anc-register-listing.component';
import { AsthmaListingComponent } from './asthma/asthma-listing/asthma-listing.component';
import { AsthmaDashboardComponent } from './asthma/asthma-dashboard/asthma-dashboard.component';
import { TargetListingComponent } from './immunization/target-listing/target-lisiting.component';
import { ImmunizationDashboardComponent } from './immunization/Dashboard/immunization-dashboard.component';
import { AncDashboardComponent } from './anc/Dashboard/anc-dashboard.component';
import { ChildListingComponent } from './child/child-listing.component';
import { ChildNutriitonRegisterComponent } from './child-nutrition/child-nutriiton-register/child-nutriiton-register.component';
import { ChildNutritionListingComponent } from './child-nutrition/child-nutrition-listing/child-nutrition-listing.component';
import { NutDashboardComponent } from './child-nutrition/Dashboard/nut-dashboard.component';
import { TransplantRegistrationComponent } from './transplant-registration/transplant-registration.component';

import { CorneaTransplantComponent } from './cornea/cornea-transplant/cornea-transplant.component';
import { CorneaRegistryComponent } from './cornea/cornea-registry/cornea-registry.component';

import { LiverTransplantFollowUpComponent } from './liver/liver-transplant-follow-up/liver-transplant-follow-up.component';

import { MentalRegisterComponent } from './Mental/mental-register/mental-register.component';

import { CorneaRequestListingComponent } from './cornea/cornea-request-listing/cornea-request-listing.component';
import { DeceasedDonorRegisterComponent } from './deceased-donor/deceased-donor-register/deceased-donor-register.component';
import { DeceasedDonorListingComponent } from './deceased-donor/deceased-donor-listing/deceased-donor-listing.component';
import { DeceasedDonorDashboardComponent } from './deceased-donor/deceased-donor-dashboard/deceased-donor-dashboard.component';import { CorneaTransplantListingComponent } from './cornea/cornea-transplant-listing/cornea-transplant-listing.component';
import { LungRegistryComponent } from './lung/lung-registry/lung-registry.component';
import { LungCaseDetailsComponent } from './lung/lung-case-details/lung-case-details.component';
import { LungTranplantComponent } from './lung/lung-transplant/lung-transplant.component';
import { LungListingComponent } from './lung/lung-listing/lung-listing.component';
import { LungTransplantListingComponent } from './lung/lung-transplant-listing/lung-transplant-listing.component';
import { LungDashboardComponent } from './lung/lung-dashboard/lung-dashboard.component';
import { LungDonorListComponent } from './lung/lung-donor-list/lung-donor-list.component';
import { LungDonorRegistryComponent } from './lung/lung-donor-registry/lung-donor-registry.component';
import { CorneaDashboardComponent } from './cornea/cornea-dashboard/cornea-dashboard.component';
import { HeartCaseDetailsComponent } from './heart/heart-case-details/heart-case-details.component';import { HeartRegistyComponent } from './heart/heart-registy/heart-registy.component';
import { HeartRegistyListComponent } from './heart/heart-registy-list/heart-registy-list.component';
import { HeartDonorRegistryComponent } from './heart/heart-donor-registry/heart-donor-registry.component';
import { HeartDonorListingComponent } from './heart/heart-donor-listing/heart-donor-listing.component';
import { LungTissueTypingComponent } from './lung/tissue-typing/lung-tissue-typing.component';
import { HeartDashboardComponent } from './heart/heart-dashboard/heart-dashboard.component';






const routes: Routes = [
  { path: '', redirectTo: 'home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent, canActivate: [AuthGuard] },
  { path: 'login', component: LoginComponent, canActivate: [LoginGuard] }, //canActivate: [LoginGuard]
  { path: 'elderly/list', component: ElderlyListingComponent, canActivate: [AuthGuard] },
  { path: 'elderly/registry', component: ElderlyRegisterComponent, canActivate: [AuthGuard] },
  { path: 'elderly/home', component: ElderlyDashboardComponent, canActivate: [AuthGuard] },
  { path: 'users', component: UsersManagmentComponent },
  { path: 'search', component: SearchComponent },
  { path: 'donor/home', component: OrganDonorDashboardComponent, canActivate: [AuthGuard] },
  { path: 'donor/listing', component: OrganDonorListingComponent, canActivate: [AuthGuard] },
  { path: 'donor/registry', component: DonorInformationComponent, canActivate: [AuthGuard] },
  

  //{ path: 'elderly/list', component: SearchComponent },
  { path: 'renal/listing', component: DonorInformationListingComponent, canActivate: [AuthGuard] },
  { path: 'labResult/listing', component: LabResultsComponent, canActivate: [AuthGuard] },
  { path: 'renal-waiting/registry', component: RenalWaitingListComponent, canActivate: [AuthGuard] },
  { path: 'renal-waiting/listing', component: RenalWaitingListListingComponent, canActivate: [AuthGuard] },
  { path: 'stages', component: KidneyDiseaseStagesComponent, canActivate: [AuthGuard] },
  { path: 'followUp', component: TransplantFollowUpComponent, canActivate: [AuthGuard] },
  { path: 'tissueType', component: TissueTypingComponent, canActivate: [AuthGuard] },
  { path: 'renal/reglist', component: RenalListingComponent, canActivate: [AuthGuard] },
  { path: 'renal/home', component: RenalDashboardComponent, canActivate: [AuthGuard] },
  { path: 'renal/registry', component: RenalRegisterComponent, canActivate: [AuthGuard] },
  { path: 'genetic/registry', component: BloodDisorderComponent, canActivate: [AuthGuard] },
  { path: 'genetic/listing', component: GeneticBloodDisorderListingComponent, canActivate: [AuthGuard] },
  { path: 'genetic/home', component: GeneticBloodDisorderDashboardComponent, canActivate: [AuthGuard] },
  { path: 'cd', component: surgeryComponent, canActivate: [AuthGuard] },
  { path: 'alShifaLogin', component: AlShifaLoginComponent },
  { path: 'diabetic/home', component: DiabeticDashboardComponent, canActivate: [AuthGuard] },
  { path: 'diabetic/listing', component: DiabeticListingComponent, canActivate: [AuthGuard] },
  { path: 'diabetic/registry', component: DiabeticRegistryComponent, canActivate: [AuthGuard] },

  { path: 'braindeath/registration', component: BrainDeathRegistrationComponent, canActivate: [AuthGuard] },

  { path: 'vaccine/listing', component: VaccineListingComponent },
  { path: 'vaccine/target-listing', component: TargetListingComponent },
  { path: 'vaccine/registry', component: VaccineRegisterComponent },
  { path: 'vaccine/immunization-dashboard', component: ImmunizationDashboardComponent },
  { path: 'braindeath/listing', component: BrainDeathListingComponent, canActivate: [AuthGuard] },
  { path: 'anc/ancRegister', component: AncRegisterComponent },
  { path: 'anc/register', component: AncRequestRegistrationComponent, canActivate: [AuthGuard] },
  { path: 'anc/listing', component: AncListingComponent, canActivate: [AuthGuard] },


  { path: 'anc/dashboard', component: AncDashboardComponent, canActivate: [AuthGuard] },
  { path: 'anc/listing', component: AncListingComponent, canActivate: [AuthGuard] },
  { path: 'anc/registerListing', component: AncRegisterListingComponent, canActivate: [AuthGuard] },
  { path: 'anc/registerListing', component: AncRegisterListingComponent, canActivate: [AuthGuard] },
  { path: 'asthma/asthmaListing', component: AsthmaListingComponent, canActivate: [AuthGuard] },
  { path: 'asthma/home', component: AsthmaDashboardComponent, canActivate: [AuthGuard] },
  { path: 'child/childlisting', component: ChildListingComponent, canActivate: [AuthGuard] },
  { path: 'child-nutrition/registry', component: ChildNutriitonRegisterComponent, canActivate: [AuthGuard] },
  { path: 'child-nutrition/listing', component: ChildNutritionListingComponent, canActivate: [AuthGuard] },
  { path: 'child-nutrition/dashboard', component: NutDashboardComponent, canActivate: [AuthGuard] },
  { path: 'transplant/transplantRegistration', component: TransplantRegistrationComponent, canActivate: [AuthGuard] },


  // Saravavana adding routing for liver registry today date  : 2024-11-25

  { path: 'liver/caseDetails', component: CaseDetailsComponent, canActivate: [AuthGuard] },
  { path: 'liver/liverRegister', component: LiverRegisterComponent, canActivate: [AuthGuard] },
  { path: 'liver/reglist', component: LiverListingComponent, canActivate: [AuthGuard] },
  { path: 'liver/home', component: LiverDashboardComponent, canActivate: [AuthGuard] },

  //{ path: 'liver-waiting/registry', component: LiverWaitingListComponent, canActivate: [AuthGuard] },
  { path: 'liver-waiting/listing', component: LiverWaitingListListingComponent, canActivate: [AuthGuard] },
  { path: 'liver-donor/registry', component: LiverDonorInformationComponent, canActivate: [AuthGuard] },
  { path: 'liver-donor/listing', component: LiverDonorInformationListingComponent, canActivate: [AuthGuard] },
  { path: 'liver/transplant', component: LiverTranplantComponent, canActivate: [AuthGuard] },
  { path: 'liver/preparing-liver-waiting-list', component: PreparingLiverWaitingListComponent, canActivate: [AuthGuard] },
  { path: 'liver/follow-up', component: LiverTransplantFollowUpComponent, canActivate: [AuthGuard] },
  //cornea
  { path: 'cornea/transplant', component: CorneaTransplantComponent, canActivate: [AuthGuard] },
  { path: 'cornea/dashboard', component: CorneaDashboardComponent, canActivate: [AuthGuard] },

  { path: 'corneaRequest', component: CorneaRegistryComponent, canActivate: [AuthGuard] },


  { path: 'mental-health/Register', component:  MentalRegisterComponent, canActivate: [AuthGuard] },

  { path: 'cornea/request-listing', component: CorneaRequestListingComponent, canActivate: [AuthGuard] },
  { path: 'cornea/transplant-listing', component: CorneaTransplantListingComponent, canActivate: [AuthGuard] },
//Deceased Donor Registration ERG-580
  { path: 'deceased/registry', component: DeceasedDonorRegisterComponent, canActivate: [AuthGuard] },
  { path: 'deceased/listing', component: DeceasedDonorListingComponent, canActivate: [AuthGuard] },
  { path: 'deceased/dashboard', component: DeceasedDonorDashboardComponent, canActivate: [AuthGuard] },
  { path: 'lung/registry', component: LungRegistryComponent, canActivate: [AuthGuard] },
  { path: 'lung/transplant', component: LungTranplantComponent, canActivate: [AuthGuard] },
  { path: 'lung/case-details', component: LungCaseDetailsComponent, canActivate: [AuthGuard] },
  { path: 'lung/listing', component: LungListingComponent, canActivate: [AuthGuard] },
  { path: 'lung/transplant-listing', component: LungTransplantListingComponent, canActivate: [AuthGuard] },
  { path: 'lung/dashboard', component: LungDashboardComponent, canActivate: [AuthGuard] },

  { path: 'lung-donor/listing', component: LungDonorListComponent, canActivate: [AuthGuard] },
  { path: 'lung-donor/registry', component: LungDonorRegistryComponent, canActivate: [AuthGuard] },
  { path: 'lung/tissuetype', component: LungTissueTypingComponent, canActivate: [AuthGuard] },

  { path: 'dashboard', component: CorneaDashboardComponent, canActivate: [AuthGuard] },

  { path: 'heart/heartRegister', component: HeartRegistyComponent, canActivate: [AuthGuard] },
  { path: 'heart/listing', component: HeartRegistyListComponent, canActivate: [AuthGuard] },
  { path: 'heart/case-details', component: HeartCaseDetailsComponent, canActivate: [AuthGuard] },
  { path: 'heart-donor/registry', component: HeartDonorRegistryComponent, canActivate: [AuthGuard] },
  { path: 'heart-donor/listing', component: HeartDonorListingComponent, canActivate: [AuthGuard] },
  { path: 'heart/dashboard', component: HeartDashboardComponent, canActivate: [AuthGuard] },
  //{ path: 'alShifaLogin/:estCode/:persCode/:type/:patientId/:isTrusted', component: AlShifaLoginComponent },

  //http://localhost:4200/alShifaLogin?estCode=20068&persCode=-1001&type=-999&patientId=112603&isTrusted=Y&regNo=90


  // otherwise redirect to home
  { path: '**', redirectTo: '' }
];


@NgModule({
  imports: [
    RouterModule.forRoot(routes, { useHash: true, onSameUrlNavigation: 'reload' }),
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
