.container-login {
    width: 100%;
    min-height: 100vh;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 15px;
    background: url(../../assets/img/login-bg.png) 0 0 no-repeat;
    background-size: cover;
}

.wrap-login {
    width: 400px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    padding: 42px 45px 45px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    // border: 10px solid #f5f5f5;
}

.login-form {
    width: 100%
}

.brand-logo {
    text-align: center;
    font-size: 30px;
    padding-bottom: 30px;
    color: var(--primary-color);
}

.min-logo {
    width: 160px;
    background: url(../../assets/img/eRegistry.png) center center no-repeat;
    background-size: 100%;
    min-height: 50px;
}

.logo {
    padding: 30px 0 20px;
    text-align: center;
    min-height: 190px;
    background: url(../../assets/img/eRegistry.png) center center no-repeat;
    // background-size: 100px 170px;
}

.login-footer {
    font-size: 13px;
    position: relative;
    z-index: 1;
    padding-top: 10px;
}

.login-footer a {
    color: #ff3b3b;
}

.login-container .animated {
    -webkit-animation-duration: 800ms;
    animation-duration: 800ms;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

@media(max-width:768px) {
    .login-container {
        width: auto;
        padding-top: 30px;
    }
}

.login-container {
    width: 360px;
    margin: 0 auto;
    padding-top: 50px;
}

.login-container .ui-input-group>label,
.login-container .ui-input-group .form-control {
    color: #999;
    border-color: rgba(113, 113, 113, 0.2);
}

.login-container .ui-input-group .form-control {
    border: 0;
    border-bottom: 1px solid #c7c7c7;
    border-radius: 0;
}

.login-container .ui-input-group .form-control:focus {
    border-color: #999
}

.login-container .ui-input-group .input-bar:before,
.login-container .ui-input-group .input-bar:after {
    background: #999;
}

.login-container .ui-input-group>label {
    font-size: 14px;
}

.login-container .ui-input-group>.form-control:focus~label,
.login-container .ui-input-group>.form-control:valid~label {
    color: #999;
}

.login-container .ui-input-group>label {
    font-weight: normal;
}

.login-container .ui-input-group .form-control:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;

}

.loginbg {
    background: url(../../assets/img/login-bg.png) 0 repeat;
    min-height: 100vh;
}