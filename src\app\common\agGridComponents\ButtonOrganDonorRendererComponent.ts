// Author: T4professor

import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams, IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'app-button-renderer',
  template: `
  <button type="button" *ngIf="fileStatus === 'Y' || fileStatus === 'D'" class="btn btn-pdf" (click)="onClick($event)"><i class="fa fa-file-pdf"></i></button>    `
})

export class ButtonOrganDonorRendererComponent implements ICellRendererAngularComp {

  params;
  label: string;
  fileStatus;

  agInit(params): void {
    this.params = params;
    this.label = this.params.label || null;
    this.fileStatus = params.data.fileStatus;
  }

  refresh(params?: any): boolean {
    return true;
  }

  onClick($event) {
    if (this.params.onClick instanceof Function) {
      // put anything into params u want pass into parents component
      const params = {
        event: $event,
        rowData: this.params.node.data
        // ...something
      }
      this.params.onClick(params);

    }
  }
}