import { TbPatientInfo } from './patient-info.model';
import { RenalWaitingList } from './renal-waiting-list.model';

export class RegistryPatient {
    public centralRegNo: number;
    public activeYn: string;
    public civilId: number;
    public completedOn: Date;
    public createdBy: number;
    public createdOn: Date;
    public instRegDate: Date;
    public modifiedBy: number;
    public modifiedOn: Date;
    public regInst: number;
    public regIregisterTypenst: number;
    public localRegReferance: string;
    public rgTbPatientInfo: TbPatientInfo;
    public rgTbRenalWaitingList: Array<RenalWaitingList>;
 // public rgTbRenalWaitingList: Array<RenalWaitingListDisplay>;

    /*
        private List<RgTbVitalSignDto> rgTbVitalSigns;
    
        private List<RgTbEldExamTranDto> rgTbEldExamTrans;
        */
}