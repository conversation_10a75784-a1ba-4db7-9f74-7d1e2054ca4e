import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { GridOptions } from 'ag-grid-community';
import { RgVwCorRequestDtlsDto } from 'src/app/_models/cornea-request-dtls-dto.model';
import * as AppUtils from '../../common/app.utils';
import { CorneaRequestListSearchDto } from 'src/app/_models/cornea-request-list-search.model';
import { MasterService } from 'src/app/_services/master.service';
import { CorneaService } from '../cornea.service';
import Swal from 'sweetalert2';
import { CorneaExcel } from 'src/app/_models/cornea-excel.model';
import { SharedService } from 'src/app/_services/shared.service';
import { Router } from '@angular/router';
import { DATE_FORMATS } from 'src/app/common/app.component-utils';
import { formatDate } from '@angular/common';

@Component({
  selector: 'flst',
  templateUrl: './cornea-request-listing.component.html',
  providers: [CorneaService]
})
export class CorneaRequestListingComponent implements OnInit {
  searchModel!: CorneaRequestListSearchDto;
  corneaSearchForm!: FormGroup;
  rowData: Array<RgVwCorRequestDtlsDto> = [];
  corneaExcel: Array<CorneaExcel> = [];
  dataList: Array<CorneaExcel> = [];
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  columnDefs: any[];
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  institeListFilter: any[] = [];
  reqInstitutesFilter: any[] = [];
  corIndicationTypeFilter: any[] = [];
  corClearZoneFilter: any[] = [];
  corTissueTypeFilter: any[] = [];
  surgeonNamesListFilter: any[] = [];
  selectedInstitute: any;
  today = new Date();
  lastSearchBody: any;

  constructor(
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _corneaService: CorneaService,
    private _sharedService: SharedService,
    private _router: Router
  ) {}

  ngOnInit() {
    this.corneaSearchForm = this.initForm();
    this.getMasterData();
    this.columnDefs = this.initColumnDefs();
  }

  initForm(): FormGroup {
    return this.fb.group({
      requestNo: [null],
      patCivilId: [null],
      patientId: [null],
      reqDateFrom: [null],
      reqDateTo: [null],
      requestedDoctor: [null],
      // requestTo: [null],
      reqInst: [null],
      indication: [null],
      sizeClearZone: [null],
      tissueType: [null],
      intendedArrDateFrom: [null],
      intendedArrDateTo: [null],
      intendedSurDateFrom: [null],
      intendedSurDateTo: [null]
    });
  }

  initColumnDefs(): any[] {
    const format = (params: any) =>
      params.data[params.colDef.field] ? formatDate(params.data[params.colDef.field], DATE_FORMATS.STANDARD, 'en') : null;
    return [
      { headerName: 'Request ID', field: 'requestId', minWidth: 120 },
      { headerName: 'Request No', field: 'requestNo', minWidth: 160 },
      // { headerName: 'Request Status', field: 'reqStatus', minWidth: 150 },
      // { headerName: 'Request Type', field: 'reqType', minWidth: 150 },
      // { headerName: 'Requested By', field: 'requestedBy', minWidth: 180 },
      { headerName: 'Requested Doctor', field: 'doctorName', minWidth: 280 },
      { headerName: 'Request Institute', field: 'estName', minWidth: 280 },
      { headerName: 'Request Date', field: 'reqDate', minWidth: 120, valueFormatter: format },
      // { headerName: 'Request To Institute', field: 'instName', minWidth: 230 },
      // { headerName: 'Address of Request To Institute', field: 'instAddress', minWidth: 230 },
      { headerName: 'Indication Description', field: 'indicationDesc', minWidth: 180 },
      { headerName: 'Primary Diagnosis', field: 'primaryDiagDesc', minWidth: 150 },
      { headerName: 'Clear Zone Size', field: 'sizeClearZone', minWidth: 150 },
      { headerName: 'Other Specification', field: 'otherSpec', minWidth: 160 },
      { headerName: 'Intended Arrival Date', field: 'intendedArrDate', minWidth: 160, valueFormatter: format },
      { headerName: 'Intended Surgery Date', field: 'intendedSurDate', minWidth: 160, valueFormatter: format },
      // { headerName: 'Patient ID', field: 'patientId', minWidth: 120 },
      // { headerName: 'Civil ID', field: 'patCivilId', minWidth: 125 },
      // { headerName: 'Patient Remarks', field: 'patRemarks', minWidth: 180 },
      { headerName: 'Request Remarks', field: 'reqRemarks', minWidth: 180 },
      { headerName: 'Tissue Type', field: 'tissueType', minWidth: 320 }
    ];
  }

  getList(event?: any): void {
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };
    const searchBody = {
      ...this.corneaSearchForm.value,
      pageable
    };
    this.lastSearchBody = { ...searchBody };
    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }
    this._corneaService.getCorneaRequestList(searchBody).subscribe(
      (res) => {
        if (res['code'] === 'S0000') {
          const content = res['result']['content'] || [];
          this.rowData = [...content].sort(
            (a, b) => new Date(b['reqDate']).getTime() - new Date(a['reqDate']).getTime()
          );
          this.totalRecords = res['result']['totalElements'] || 0;
          this.dataList = [...this.rowData];
        } else {
          this.handleError(res['message'] || 'Invalid response');
        }
      },
      (error) => {
        if (error['status'] === 401) {
          this.handleError('Error occurred while retrieving user details');
        } else {
          this.handleError('An unexpected error occurred');
        }
      }
    );
  }

  private handleError(message: string): void {
    this.rowData = [];
    Swal.fire('Error!', message, 'error');
  }

  onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['corneaRequest'], { state: { requestNo: event.data.requestNo } });
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getAllCorReqInstitutes().subscribe(
      response => (this.reqInstitutesFilter = response.result),
      () => {}
    );
    this._masterService.getInstiteList(regCode, walCode).subscribe(
      response => (this.institeListFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorIndicationType().subscribe(
      response => (this.corIndicationTypeFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorClearZone().subscribe(
      response => (this.corClearZoneFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorTissueType().subscribe(
      response => (this.corTissueTypeFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorSurgeonNamesList().subscribe(
      response => (this.surgeonNamesListFilter = response.result),
      () => {}
    );
  }

  clear(): void {
    this.corneaSearchForm.reset();
    this.rowData = [];
    if (this.gridOptions.api) {
      this.gridOptions.api.setRowData([]);
    }
  }

  exportExcel() {
    if (!this.rowData || this.rowData.length === 0) {
      return Swal.fire('Warning!', 'No Records to export', 'warning');
    }
    const searchBody = {
      ...this.corneaSearchForm.value,
      getAll: true,
      pageable: { page: 0, size: this.totalRecords }
    };
    this._corneaService.getCorneaRequestList(searchBody).subscribe({
      next: (res) => {
        const data = res['result']['content'] || [];
        if (!data.length) {
          return Swal.fire('Warning!', 'No Records to export', 'warning');
        }
        const formattedData = data.map(el => ({
          'Request ID': el.requestId,
          'Request No': el.requestNo,
          //'Request Status': el.reqStatus,
          //'Request Type': el.reqType,
          //'Requested By': el.requestedBy,
          'Requested Doctor': el.doctorName,
          'Request Institute': el.estName,
          'Request Date': el.reqDate ? formatDate(el.reqDate, DATE_FORMATS.STANDARD, 'en') : null, 
          //'Request To Institute': el.instName,
         // 'Address of Request To Institute': el.instAddress,
          'Indication Description': el.indicationDesc,
          'Primary Diagnosis': el.primaryDiagDesc,
          'Clear Zone Size': el.sizeClearZone,
          'Other Specification': el.otherSpec,
          'Intended Arrival Date': el.intendedArrDate ? formatDate(el.intendedArrDate, DATE_FORMATS.STANDARD, 'en') : null,
          'Intended Surgery Date': el.intendedSurDate ? formatDate(el.intendedSurDate, DATE_FORMATS.STANDARD, 'en') : null,
          // 'Patient ID': el.patientId,
          // 'Civil ID': el.patCivilId,
          // 'Patient Remarks': el.patRemarks,
          'Request Remarks': el.reqRemarks,
          'Tissue Type': el.tissueType
        }));
        this._sharedService.exportAsExcelFile(formattedData, 'Cornea_Listing');
      },
      error: () => this.handleError('Error occurred during export')
    });
  }
}

