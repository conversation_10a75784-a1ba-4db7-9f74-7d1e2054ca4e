<h6 class="page-title"><p>Deceased Donor Dashboard </p>
</h6>
<div class="row">
    
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <form [formGroup]="boardForm">
                <div class="">
                    <div class="form-group">
                        <label>Region</label>
                        <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                            bindLabel="regName" bindValue="regCode" formControlName="regCode"
                            (change)="locSelect($event,'region')" [(ngModel)]="regCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                            </ng-template>
                        </ng-select>
                    </div>
                    <div class="form-group">
                        <label>Wilayat</label>
                        <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                            bindLabel="walName" bindValue="walCode" formControlName="walCode"
                            (change)="locSelect($event,'wilayat')" [(ngModel)]="walCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                            </ng-template>
                        </ng-select>
                    </div>
                    <div class="form-group">
                        <label>Institute</label>
                        <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true"
                            (change)="locSelect($event,'institute')" placeholder="Select" bindLabel="estName"
                            bindValue="estCode" formControlName="estCode" [(ngModel)]="estCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                            </ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Age</label>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageF" type="number" class="form-control form-control-sm"
                                    placeholder="From">
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageT" type="number" class="form-control form-control-sm"
                                    placeholder="To">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label></label>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 text-right">

                                <button class="btn btn-sm btn-primary" (click)="callFilter()">Search</button>
                            </div>

                        </div>
                    </div>


                    <!--
                   <div class="form-group">
                    <label>Braden Score</label>
                    <div class="row">                        
                        <div class="col-lg-9 col-md-9 col-sm-9">
                            <div class="p-2">
                       

                               <h3>Range: {{rangeValues[0] + ' - ' + rangeValues[1]}}</h3>
                            <p-slider [(ngModel)]="rangeValues" [style]="{'width':'14em'}" [range]="true"></p-slider>
                              
                               
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-3"><span class="badge badge-secondary">{{ rangeValues }}</span></div>
                    </div>                    
                </div>
                -->
                </div>
            </form>

        </div>
        <footer *ngIf="createTime" id="footer"> These statistics data were taken at {{createTime}} for refresh please
            press the <button class='btn btn-sm btn-primary' (click)='callReset()'>reset</button> button. </footer>
    </div>

    <div class="col-lg-9 col-md-9 col-sm-9">
        <div class="inner-content dash-content">
            <div class="d-flex justify-content-center"> <h4>{{this.filterTitle}}</h4></div>
            <div class="row">
                <!-- <div class="col-lg-8 col-md-8 col-sm-8"> -->
                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="widget">
                        <h6>Yearly Death Count</h6>
                        <div class="text-center chartDiv">
                            <p-chart type="bar" [data]="yearlyDeathCountByRegionChart" [options]="options"
                                class="chartjs-render-monitor"></p-chart>
                        </div>
                    </div>
                </div>
                <!-- </div>
            <div class="row"> -->

                <!-- <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4"> -->
                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="widget">
                        <h6>Cause of Death</h6>
                        <p-chart type="pie" [data]="brainDeathCauseChart" [options]="pieOption"></p-chart>
                    </div>
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-4"> -->
                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="widget">
                        <h6>Distribution of Death Diagnosis</h6>
                        <p-chart type="bar" [data]="medicalConditionsByRegionChart" [options]="options" ></p-chart>
                    </div>
                </div>
             
                    <!-- <div class="col-lg-6 col-md-6 col-sm-6">
                      <div class="widget">
                        <h6>Cancer by Region</h6>
                        <p-chart type="bar" [data]="cancerByRegionChart" [options]="options"></p-chart>
                      </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                      <div class="widget">
                        <h6>Hypertension by Region</h6>
                        <p-chart type="bar" [data]="hypertensionByRegionChart" [options]="options"></p-chart>
                      </div>
                    </div>
                  
                  
                  
                    <div class="col-lg-6 col-md-6 col-sm-6">
                      <div class="widget">
                        <h6>Diabetes by Region</h6>
                        <p-chart type="bar" [data]="diabetesByRegionChart" [options]="options"></p-chart>
                      </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                      <div class="widget">
                        <h6>Allergy by Region</h6>
                        <p-chart type="bar" [data]="allergyByRegionChart" [options]="options"></p-chart>
                      </div>
                    </div>
                  
                  
                 
                    <div class="col-lg-6 col-md-6 col-sm-6">
                      <div class="widget">
                        <h6>Systemic Autoimmune Disease by Region</h6>
                        <p-chart type="bar" [data]="systemicAutoimmuneByRegionChart" [options]="options"></p-chart>
                      </div>
                    </div> -->
                  
            </div>

        </div>
    </div>


</div>