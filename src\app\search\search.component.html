<h6 class="page-title">Search</h6>
<div class="content-wrapper">
  <form novalidate #searchForm="ngForm">
    <div class="row">
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Registration No</label>
          <input type="text" class="form-control form-control-sm" [(ngModel)]="centralRegNo" name="centralRegNo">
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
              <label>Age (from)</label>
              <input type="text" class="form-control form-control-sm" [(ngModel)]="ageFrom" name="ageFrom">
            </div>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
              <label>To</label>
              <input type="text" class="form-control form-control-sm" [(ngModel)]="ageTo" name="ageTo">
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Sex</label>
          <select class="form-control form-control-sm" [(ngModel)]="sex" name="sex">
            <option>Male</option>
            <option>Female</option>
          </select>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Reg Institute</label>
          <p-dropdown [options]="institeList" [(ngModel)]="regInst" optionLabel="estName" placeholder="Select Institute"
            filter="true" showClear="true" name="regInst">
          </p-dropdown>

        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Region</label>
          <p-dropdown [options]="regionList" [(ngModel)]="regName" optionLabel="regName" placeholder="Select Region"
            filter="true" showClear="true" name="region">
          </p-dropdown>

        </div>
      </div>
      
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Wilayat</label>
          <p-dropdown [options]="wallayatList" [(ngModel)]="walCode" optionLabel="walName" placeholder="Select Wallayat"
            filter="true" showClear="true" name="walCode" (onChange)="getVillageList($event.value.walCode)">
          </p-dropdown>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Village</label>
          <p-dropdown [options]="villageList" [(ngModel)]="vilCode" optionLabel="vilName" placeholder="Select Village"
            filter="true" showClear="true" name="vilCode">
          </p-dropdown>
          <!-- <ng-select #nationality [items]="wallayatList" [virtualScroll]="true" placeholder="Select"
        bindLabel="walName" bindValue="walCode" formControlName="wallayat">
        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.walName }}
        </ng-template>
      </ng-select> -->
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">

          <div class="row">
            <div class="col-lg-6 col-md-6 col-sm-6">
              <label>RBS</label>
              <p-dropdown [options]="rbsList" [showHeader]="false" [(ngModel)]="rbs" name="rbs"
                optionLabel="frequencyDesc"></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <label>From</label>
              <input type="text" class="form-control form-control-sm" [(ngModel)]="rbsFrom" name="rbsFrom">
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <label>To</label>
              <input type="text" class="form-control form-control-sm" [(ngModel)]="rbsTo" name="rbsTo">
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6">
        <div class="form-group">
          <label>BRADEN</label>
          <p-dropdown [options]="bradenList" [showHeader]="false" [(ngModel)]="braden" name="braden"
            optionLabel="frequencyDesc"></p-dropdown>
        </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-3">
          <label>From</label>
          <input type="text" class="form-control form-control-sm" [(ngModel)]="bradenFrom" name="bradenFrom">
        </div>
        <div class="col-lg-3 col-md-3 col-sm-3">
          <label>To</label>
          <input type="text" class="form-control form-control-sm" [(ngModel)]="bradenTo" name="bradenTo">
        </div>
        </div>
      </div>
      <!-- <div class="col-lg-3 col-md-3 col-sm-3">
      <div class="form-group">
        <label>Value</label>
        <input type="text" class="form-control form-control-sm">
      </div>
    </div> -->
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Mental State</label>
          <p-dropdown [options]="mentalStateList" [showHeader]="false" [(ngModel)]="mentalState" name="mentalState"
            optionLabel="frequencyDesc">
          </p-dropdown>
        </div>
      </div>
   
    
      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <button type="submit" class="btn btn-primary ripple" (click)="onSearch(searchForm.value)">Search</button>
      </div>
      
    </div>
  </form>
</div>
<div class="content-wrapper mt-2">
  <div style="margin-top:20px">


      <div style="margin-top:20px">
          <ag-grid-angular #agGrid  style="width: 100%; height: 350px;"
            [ngClass]="{'advanced-search-max-grid-size': !advancedToggle, 'advanced-search-min-grid-size':advancedToggle}"
            class="ag-theme-balham" [gridOptions]="gridOptions" [columnDefs]="columnDefs" [rowData]="rowData" (gridReady)="onGridReady($event)">
          </ag-grid-angular>
          <!--
          <div *ngIf="rowData.length > 0">
            <p-paginator #pp rows="20" totalRecords="{{totalRecords}}" (onPageChange)="paginatedList($event)"
              showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10">
            </p-paginator>
          </div>-->
        </div>
    <!-- 
    <ag-grid-angular class="ag-theme-balham" style="height: 300px;" [columnDefs]="columnDefs"
      [showToolPanel]="showToolPanel" enableColResize enableSorting enableFilter animateRows
      (gridReady)="onGridReady($event)" [gridOptions]="gridOptions" 
      (gridSizeChanged)="onGridSizeChanged($event)">
    </ag-grid-angular>-->

  </div>

</div>