import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import * as AppCompUtils from '../../common/app.component-utils';
import Swal from 'sweetalert2';
import { CorneaService } from '../cornea.service';
import * as AppUtils from '../../common/app.utils';
import { SharedService } from 'src/app/_services/shared.service';
import { Router } from '@angular/router';
import { formatDate } from '@angular/common';
import { GridOptions } from 'ag-grid-community';

@Component({
  selector: 'flst',
  templateUrl: './cornea-transplant-listing.component.html',
  styleUrls: ['./cornea-transplant-listing.component.scss'],
  providers: [CorneaService]
})
export class CorneaTransplantListingComponent implements OnInit {
  corneaSearchForm: FormGroup;
  rowData: any[] = [];
  columnDefs: any[] = [];
  dataList: any[];
  totalRecords: any;
  lastSearchBody: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };

  gender = AppCompUtils.GENDER;
  eyeOperated = AppCompUtils.EYE_OPERATED;
  corTissueTypeFilter: any[] = [];
  regionDataListFilter: any[] = [];
  wallayatListFilter: any[] = [];
  instituteListFilter: any[] = [];
  visualAcuityListFilter: any;
  transplantOutcomeListFilter: any;

  constructor(
    private _masterService: MasterService,
    private fb: FormBuilder,
    private _corneaService: CorneaService,
    private _sharedService: SharedService,
    private _router: Router
  ) {}

  ngOnInit() {
    this.initializeForm();
    this.getMasterData();
    this.columnDefs = this.initColumnDefs();
  }

  initializeForm() {
    this.corneaSearchForm = this.fb.group({
      civilId: new FormControl(null),
      regNo: new FormControl(null),
      ageFrom: new FormControl(null),
      ageTo: new FormControl(null),
      sex: new FormControl(null),
      regCode: new FormControl(null),
      walCode: new FormControl(null),
      tranInst: new FormControl(null),
      tranDateFrom: new FormControl(null),
      tranDateTo: new FormControl(null),
      eyeOperated: new FormControl(null),
      transplantOutcome: new FormControl(null),
      visAcuityBefore: new FormControl(null),
      visAcuityAfter: new FormControl(null)
    });
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(
      (response) => (this.regionDataListFilter = response.result),
      () => {}
    );
    this._masterService.getWilayatList(regCode).subscribe(
      (response) => (this.wallayatListFilter = response.result),
      () => {}
    );
    this._masterService.getInstiteList(regCode, walCode).subscribe(
      (response) => (this.instituteListFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorVisualAcuity().subscribe(
      (response) => (this.visualAcuityListFilter = response.result),
      () => {}
    );
    this._masterService.getAllCorTransplantOutcome().subscribe(
      (response) => (this.transplantOutcomeListFilter = response.result),
      () => {}
    );
  }

  getList(event?: any): void {
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };

    const searchBody = { ...this.corneaSearchForm.value, pageable };
    this.lastSearchBody = { ...searchBody };

    if (this.gridOptions.api) {
      this.gridOptions.api.showLoadingOverlay();
    }

    this._corneaService.getCorneaTransplantList(searchBody).subscribe(
      (res) => {
        if (res['code'] === 'S0000') {
          const content = res['result']['content'] || [];
          this.rowData = content
            .map((item) => {
              const mappedItem: any = {};
              this.columnDefs.forEach((colDef) => {
                const field = colDef.field;
                if (field) {
                  mappedItem[field] = item[field];
                }
              });
              return mappedItem;
            })
            .sort(
              (a, b) =>
                new Date(b['reqDate']).getTime() - new Date(a['reqDate']).getTime()
            );
          this.totalRecords = res['result']['totalElements'] || 0;
          this.dataList = [...this.rowData];
        } else {
          this.handleError(res['message'] || 'Invalid response');
        }
      },
      (error) => {
        if (error['status'] === 401) {
          this.handleError('Error occurred while retrieving user details');
        } else {
          this.handleError('An unexpected error occurred');
        }
      }
    );
  }

  initColumnDefs(): any[] {
    const format = (params: any) =>
      params.data[params.colDef.field]
        ? formatDate(params.data[params.colDef.field], AppCompUtils.DATE_FORMATS.STANDARD, 'en')
        : null;

    const mapValue = (list: any[], id: any) => {
      const item = list.find((entry) => entry.id === id);
      return item ? item.value : null;
    };

    return [
      { headerName: 'Central Reg No', field: 'regNo', minWidth: 150 },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 150 },
      { headerName: 'Patient ID', field: 'patientId', minWidth: 150 },
      { headerName: 'Patient Name', field: 'fullName', minWidth: 280 },
      { headerName: 'Gender', field: 'sex', minWidth: 100, valueGetter: (params) => mapValue(this.gender, params.data.sex) },
      { headerName: 'Date of Birth', field: 'dob', minWidth: 180, valueFormatter: format },
      { headerName: 'Age', field: 'age', minWidth: 80 },
      { headerName: 'Eye Operated', field: 'eyeOperated', minWidth: 150, valueGetter: (params) => mapValue(this.eyeOperated, params.data.eyeOperated) },
      { headerName: 'Previous Right Graft', field: 'prevRightGraft', minWidth: 180, valueGetter: (params) => mapValue(AppCompUtils.YES_NO, params.data.prevRightGraft) },
      { headerName: 'Previous Left Graft', field: 'prevLeftGraft', minWidth: 180, valueGetter: (params) => mapValue(AppCompUtils.YES_NO, params.data.prevLeftGraft) },
      { headerName: 'Institute Reg Date', field: 'instRegDate', minWidth: 180, valueFormatter: format },
      { headerName: 'Transplant ID', field: 'tranId', minWidth: 150 },
      { headerName: 'Transplant Date', field: 'tranDate', minWidth: 180, valueFormatter: format },
      { headerName: 'Tissue Arrival Date', field: 'tissueArrDate', minWidth: 180, valueFormatter: format },
      { headerName: 'Phone Number', field: 'phoneNo', minWidth: 150 },
      { headerName: 'Surgery Type', field: 'surgeryTypeDesc', minWidth: 180 },
      { headerName: 'Outcome Description', field: 'outcomeDesc', minWidth: 200 },
      { headerName: 'Visual Acuity Before', field: 'visAcuityBeforeDesc', minWidth: 200 },
      { headerName: 'Visual Acuity After', field: 'visAcuityAfterDesc', minWidth: 200 },
      { headerName: 'Village Name', field: 'villageName', minWidth: 150 },
      { headerName: 'Wilayath Name', field: 'wilayathName', minWidth: 150 },
      { headerName: 'Region Name', field: 'regionName', minWidth: 150 },
      { headerName: 'Surgeon Name', field: 'surgeonName', minWidth: 180 },
      { headerName: 'Transplant Institute', field: 'transplantInstitute', minWidth: 200 },
      { headerName: 'Patient Medical History', field: 'patMedHistory', minWidth: 220 },
      { headerName: 'Patient Remarks', field: 'patRemarks', minWidth: 180 }
    ];
  }

  exportExcel() {
    if (!this.rowData || this.rowData.length === 0) {
      return Swal.fire('Warning!', 'No Records to export', 'warning');
    }
    const searchBody = { ...this.corneaSearchForm.value, getAll: true, pageable: { page: 0, size: this.totalRecords } };

    this._corneaService.getCorneaTransplantList(searchBody).subscribe({
      next: (res) => {
        const data = res['result']['content'] || [];
        if (!data.length) {
          return Swal.fire('Warning!', 'No Records to export', 'warning');
        }
        const formattedData = data.map((el) => {
          const mappedItem: any = {};
          this.columnDefs.forEach((colDef) => {
            const headerName = colDef.headerName;
            const field = colDef.field;
            if (headerName && field) {
              mappedItem[headerName] = el[field];
            }
          });
          return mappedItem;
        });
        this._sharedService.exportAsExcelFile(formattedData, 'Cornea_Listing');
      },
      error: () => this.handleError('Error occurred during export')
    });
  }

  clear(): void {
    this.corneaSearchForm.reset();
    this.rowData = [];
    if (this.gridOptions.api) {
      this.gridOptions.api.setRowData([]);
    }
  }

  onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['cornea/transplant'], { state: { regNo: event.data.regNo } });
  }

  private handleError(message: string): void {
    this.rowData = [];
    Swal.fire('Error!', message, 'error');
  }
}
