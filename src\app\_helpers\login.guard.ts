import { AuthenticationService } from './../_services/authentication.service';
import { Injectable } from '@angular/core';
import { Router, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class LoginGuard implements CanActivate {
    constructor(private authenticationService: AuthenticationService,   private router: Router){}
canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    let currentUser;
    this.authenticationService.currentUser.subscribe(x => {
        currentUser = x;
        if(currentUser){
        //  console.log("If" +currentUser);
          if(state.url == '/login'){
            this.router.navigate(['/home']);
          }
        }else{
         // console.log("else");
            return false;
        }
     })
  return true;
}
}