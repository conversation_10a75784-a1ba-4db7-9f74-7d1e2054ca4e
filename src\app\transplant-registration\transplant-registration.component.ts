import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { PatientDetailsComponent } from "../_comments/patient-details/patient-details.component";
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
@Component({
  selector: 'app-transplant-registration',
  templateUrl: './transplant-registration.component.html',
  styleUrls: ['./transplant-registration.component.scss']
})
export class TransplantRegistrationComponent implements OnInit {
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  patientInfoDetails = [];
  submitted = false;
  patientForm: FormGroup;
  today = new Date();
  public page3 = 1;
  InductionForm: FormGroup;
  LateForm: FormGroup;
  EarlyForm: FormGroup;
  ImmediateForm: FormGroup;
  public pageSize = 2;
  recordEnabled: boolean = false;
  recordEnabled1: boolean = false;
  recordEnabled2: boolean = false;
  transplantRegistryForm: FormGroup;
 
  

  

  constructor(private fb: FormBuilder) {}
    ngOnInit(): void {
      
    this.initialsPatientForm();      
    this.transplantRegistryForm = this.fb.group({      
      premptiveTransplant: [null],
      exDate: [null],
      transplantCountry: [null],
      transplantRemarks: [null],
      transplantType:[null],
      transplantSubtype:[null],
      surgeryType:[null],
      surgerySub:[null],
      explantedLiverPath:[null],
      noOfDaysInWard:[null],
      noOfDaysInICU:[null], 
      Others:[null], 
      dateRejection:[null],
      Biopsy:[null],
      RejectionPrescription:[null],
      RejectionGrade:[null],
      PastTransplantRejection:[null],
      InterventionReason:[null],
      InterventionType:[null],
      SurgicalIntervention:[null],
      ReAdmittedDays:[null],
      ReAdmissionReason:[null],
      ReAdmitted:[null],
      regInst: [null],
      
    })

  }
  

  updateRecordStatus() {
     
    console.log('Re-Admitted:', this.recordEnabled);  // True or False based on selection
    console.log('Surgical Intervention:', this.recordEnabled1);  // True or False based on selection
    console.log('Past Transplant Rejection:', this.recordEnabled2);  // True or False based on selection
    
  }
  
  

  
  
  
  submitForm() {
    this.submitted = true;
  }
  
 
  initialsPatientForm() {
    this.patientForm = this.fb.group({
      
            centralRegNo: [null],
            patientId: [null, Validators.required],
            civilId: [null, Validators.required],
            dob: [null, Validators.required],
            age: [null],
            tribe: [null],
            firstName: [null, Validators.required],
            secondName: [null, Validators.required],
            sex: [null],
            maritalStatus: [null],
            thirdName: [null],
            village: [null],
            walayat: [null],
            region: [null],
            mobileNo: [null],
            kinTelNo: [null],
            careGiverTel: [null],
            regInst: [null, Validators.required],
            exDate: [null],
          });
        
        }}


          
        



   