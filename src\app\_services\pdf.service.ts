// enhanced-pdf.service.ts
import { Injectable } from '@angular/core';
import jsPDF  from 'jspdf';
import html2canvas from 'html2canvas';

@Injectable({
  providedIn: 'root'
})
export class PdfService {
    constructor() { }
  
  async generateMultiPagePDF(
    element: HTMLElement, 
    fileName: string,
    options: any = {}
  ) {
    const {
      margin = 2,
      scale = 2,
      header = true,
      footer = true
    } = options;

    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const contentWidth = pageWidth - (margin * 2);
    const contentHeight = pageHeight - (margin * 2) - (header ? 30 : 0) - (footer ? 30 : 0);

    // Prepare content
    this.prepareForPrint(element);

    const canvas = await html2canvas(element, {
      scale: scale,
      useCORS: true,
      logging: false
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = contentWidth;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    let yPosition = margin + (header ? 30 : 0);
    let remainingHeight = imgHeight;
    let currentPage = 1;
    const totalPages = Math.ceil(imgHeight / contentHeight);

    while (remainingHeight > 0) {
      if (currentPage > 1) {
        pdf.addPage();
      }

      // Add header if enabled
      if (header) {
        this.addCustomHeader(pdf, pageWidth, currentPage, totalPages);
      }

      // Calculate what portion of image to show
      const sliceHeight = Math.min(contentHeight, remainingHeight);
      const sourceY = imgHeight - remainingHeight;

      pdf.addImage(
        imgData,
        'PNG',
        margin,
        yPosition - (sourceY * contentHeight / imgHeight),
        imgWidth,
        imgHeight,
        undefined,
        'FAST'
      );

      // Add footer if enabled
      if (footer) {
        this.addCustomFooter(pdf, pageWidth, pageHeight, currentPage, totalPages);
      }

      remainingHeight -= contentHeight;
      currentPage++;
    }

    this.restoreAfterPrint(element);
    pdf.save(fileName);
  }

  private prepareForPrint(element: HTMLElement) {
    // Add print-specific styles
    element.classList.add('printing');
    
    // Hide non-printable elements
    const nonPrintable = element.querySelectorAll('.no-print, [no-print]');
    nonPrintable.forEach(el => {
      (el as HTMLElement).dataset.originalDisplay = (el as HTMLElement).style.display;
      (el as HTMLElement).style.display = 'none';
    });
  }

  private restoreAfterPrint(element: HTMLElement) {
    // Remove print styles
    element.classList.remove('printing');
    
    // Restore hidden elements
    const hiddenElements = element.querySelectorAll('[data-original-display]');
    hiddenElements.forEach(el => {
      (el as HTMLElement).style.display = (el as HTMLElement).dataset.originalDisplay || '';
    });
  }

  private addCustomHeader(pdf: jsPDF, pageWidth: number, currentPage: number, totalPages: number) {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(40);
    
    // Company logo/text
     //pdf.text('Lung Case Details', 20, 15);
     pdf.text('Lung Case Details', pageWidth / 2, 15, { align: 'center' });
    
    // Header separator
    pdf.setDrawColor(220);
    pdf.line(20, 20, pageWidth - 20, 20);
  }

  private addCustomFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, currentPage: number, totalPages: number) {
    pdf.setFontSize(9);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(120);
    
    // Footer separator
    pdf.setDrawColor(220);
    pdf.line(20, pageHeight - 25, pageWidth - 20, pageHeight - 25);
    
    // Footer content
    const date = new Date().toLocaleDateString();
    pdf.text(`Generated: ${date}`, 20, pageHeight - 15);
   // pdf.text('Confidential', pageWidth / 2, pageHeight - 15, { align: 'center' });
    pdf.text(`© ${new Date().getFullYear()}`, pageWidth - 20, pageHeight - 15, { align: 'right' });
  }
}

