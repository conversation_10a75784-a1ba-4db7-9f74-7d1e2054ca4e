export class Menu{
    menuId:number;
    menuName:string;
    activeYn:string;
    sytemId:number;
    subMenu:Array<Menu>;
    templateUrl:string;
    routeName:string;
    displayOrder:number;
    constructor(menuId:number,menuName:string,activeYn:string,systemId:number,subMenu:Array<Menu>,templateUrl:string,routeName:string){
        this.menuId=menuId;
        this.menuName=menuName;
        this.activeYn=activeYn;
        this.sytemId=systemId;
        this.subMenu=subMenu;
        this.templateUrl=templateUrl;
        this.routeName=routeName;
        this.displayOrder=this.displayOrder;

    }

}