<div class="page-title">
    <p>Cornea Dashboard</p>
</div>
<div class="row">
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <form [formGroup]="boardForm">
                <div class="form-group">
                    <label for="region">Region</label>
                    <ng-select #regionSelect [items]="regionData" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="locSelect($event,'region')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{item.regName}}
                        </ng-template>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label for="wilayat">Wilayat</label>
                    <ng-select #wilayatSelect [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode"
                        (change)="locSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label for="institute">Institute</label>
                    <ng-select #instituteSelect [items]="instituteListFilter" [virtualScroll]="true"
                        placeholder="Select" bindLabel="estName" bindValue="estCode" formControlName="estCode"
                        (change)="locSelect($event,'institute')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Request Date</label>
                    <div>
                        <div class="row  mb-2">
                            <div class="col-lg-2 col-md-2 col-sm-6">
                                <label class="m-0">From</label>
                            </div>
                            <div class="col-lg-10 col-md-10 col-sm-6">
                                <p-calendar dateFormat="dd-mm-yy" [showIcon]="true" formControlName="reqDateF"
                                    [ngModelOptions]="{standalone: true}" [monthNavigator]="true" [maxDate]="today"
                                    yearRange="1930:2030" [yearNavigator]="true" [showButtonBar]="true"
                                    (onSelect)="onFromDateSelect($event)">
                                </p-calendar>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2">
                                <label class="m-0 pl-1">To</label>
                            </div>
                            <div class="col-lg-10 col-md-10 col-sm-10">
                                <p-calendar dateFormat="dd-mm-yy" [showIcon]="true" formControlName="reqDateT"
                                    [ngModelOptions]="{standalone: true}" [monthNavigator]="true" [maxDate]="today"
                                    yearRange="1930:2030" [yearNavigator]="true" [showButtonBar]="true"
                                    [minDate]="boardForm.get('reqDateF').value">
                                </p-calendar>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <div class="col-12 text-right">
                            <button type="button" class="btn btn-sm btn-primary" (click)="callFilter()">
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <footer *ngIf="createTime" id="footer">
            These statistics data were taken at {{createTime}} for refresh please
            press the
            <button type="button" class="btn btn-sm btn-primary" (click)="callReset()">
                Reset
            </button>
            button.
        </footer>
    </div>

    <div class="col-lg-10 col-md-10 col-sm-10">
        <div class="inner-content dash-content">
            <div class="d-flex justify-content-center" *ngIf="createTime && filterTitle">
                <h4>{{filterTitle}}</h4>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Count of Indication</h6>
                        <p-chart type="bar" [data]="indicationChart" [options]="options">
                        </p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Primary Diagnosis</h6>
                        <p-chart type="pie" [data]="diagnosisPieChart" [options]="pieOption">
                        </p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Type of Tissues Requested</h6>
                        <p-chart type="pie" [data]="tissuePieChart" [options]="pieOption">
                        </p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Count of Institute</h6>
                        <p-chart type="bar" [data]="instituteChart" [options]="options">
                        </p-chart>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>