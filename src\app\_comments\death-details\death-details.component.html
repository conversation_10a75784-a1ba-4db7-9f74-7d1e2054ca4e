<div class="mb-2 lab-results">
    <form [formGroup]="deatDetailsForm">

        <div class="text-right pb-2">
            <button class="btn btn-danger" (click)="fetchFromShifa()">
                Fetch from Al Shifa </button>
            <button class="btn btn-secondary" (click)="addManualy()">Add Manualy</button>
        </div>

        <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Time of Death:</label>
                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="timeOfDeath"
                        [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                </div>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Place of Death:</label> <span class="mdtr"></span>
                    <select #entryPoint class="form-control form-control-sm" formControlName="placeOfDeath">
                        <option [value]="res.id" *ngFor="let res of placeOfDeath">
                            {{res.value}}</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause Death Direct:</label> <span class="mdtr">*</span>
                    <input type="text" class="form-control form-control-sm" formControlName="causedeathDirect" required>
                </div>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause Death Underlying 1:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="causedeathUnderlying1">
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause Death Underlying 2:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="causedeathUnderlying2">
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause Death Underlying 3:</label>
                    <input type="text" class="form-control form-control-sm" formControlName="causedeathUnderlying3">
                </div>
            </div>
        </div>
    </form>
</div>