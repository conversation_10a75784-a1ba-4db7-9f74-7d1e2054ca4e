<div class="row">
  <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="page-title">Tissue Screening</h6>
  </div>
  <div class="col-lg-3 col-md-3 col-sm-3"></div>
  <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
    <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
      (keyup.enter)="search()">
    <span class="input-group-btn">
      <button class="btn btn-default btn-sm" id="search" (click)="search()" type="submit">
        <i class="fa fa-search"></i>
      </button>
    </span>
  </div>
</div>


<div class="accordion register">
  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

    <ngb-panel id="patientDetails" id="ngb-panel-0">
      <ng-template ngbPanelHeader let-opened="opened">
        <div class="d-flex align-items-center justify-content-between card-head"
          [ngClass]="opened ? 'opened' : 'collapsed'">
          <h6> Patient Details</h6>
          <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
              [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
      </ng-template>
    </ngb-panel>



  </ngb-accordion>
</div>



<div class="content-wrapper mb-2">
  <form [formGroup]="tissueForm">

    <div class="row">
      <div class="col-12">
        <!-- Class Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="activeClassTab === 1" (click)="activeClassTab = 1">Class 1</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="activeClassTab === 2" (click)="activeClassTab = 2">Class 2</button>
          </li>
        </ul>
      </div>
    </div>

    <div class="row">
      <!-- Class 1 Section -->
      <div class="col-md-12" *ngIf="activeClassTab === 1">
        <!-- ...existing Class 1 form code... -->
        <div class="card mb-2">
          <div class="card-body">
            <div class="row">
              <div class="col-md-12 border-right">
                <h6 class="font-weight-bold mb-3">Class 1 Antigens</h6>
                <div class="form-group row gx-1">
                  <div class="col-sm-2"></div>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <div class="col-sm-2"></div>
                </div>
                <ng-container *ngFor="let antigen of ['A', 'B', 'CW', 'BW']">
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">{{antigen}}:</label>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_1']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_1"
                        (change)="onAlleleChange(antigen, 1, antigen + '_ALLELE_1', antigen + '_SERO_1')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <!-- SERO_1: show dropdown if multiple, input if single -->
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 1, tissueForm.get(antigen + '_ALLELE_1').value)?.length > 1; else singleSero1">
                        <ng-select [items]="seroOptionsMap[antigen + '_1_' + antigen + '_ALLELE_1'] || []"
                          formControlName="{{antigen}}_SERO_1" [clearable]="true" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero1>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_1"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_1']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_2"
                        (change)="onAlleleChange(antigen, 1, antigen + '_ALLELE_2', antigen + '_SERO_2')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <!-- SERO_1: show dropdown if multiple, input if single -->
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 1, tissueForm.get(antigen + '_ALLELE_2').value)?.length > 1; else singleSero2">
                        <ng-select [items]="seroOptionsMap[antigen + '_1_' + antigen + '_ALLELE_2'] || []"
                          formControlName="{{antigen}}_SERO_2" [clearable]="true" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero2>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_2"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Class 2 Section -->
      <div class="col-md-12" *ngIf="activeClassTab === 2">
        <div class="card mb-2">
          <div class="card-body">
            <div class="row">
              <!-- DR Section -->
              <div class="col-md-6 border-right">
                <h6 class="font-weight-bold mb-3">DR Antigens</h6>
                <div class="form-group row gx-1">
                  <div class="col-sm-2"></div>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <div class="col-sm-2"></div>
                </div>
                <ng-container *ngFor="let antigen of ['DRB1', 'DRB3', 'DRB4', 'DRB5']">
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">{{antigen}}:</label>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_2']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_1"
                        (change)="onAlleleChange(antigen, 2, antigen + '_ALLELE_1', antigen + '_SERO_1')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 2, tissueForm.get(antigen + '_ALLELE_1').value)?.length > 1; else singleSero3">
                        <ng-select [items]="seroOptionsMap[antigen + '_2_' + antigen + '_ALLELE_1'] || []"
                          formControlName="{{antigen}}_SERO_1" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero3>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_1"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_2']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_2"
                        (change)="onAlleleChange(antigen, 2, antigen + '_ALLELE_2', antigen + '_SERO_2')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 2, tissueForm.get(antigen + '_ALLELE_2').value)?.length > 1; else singleSero4">
                        <ng-select [items]="seroOptionsMap[antigen + '_2_' + antigen + '_ALLELE_2'] || []"
                          formControlName="{{antigen}}_SERO_2" [clearable]="true" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero4>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_2"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                  </div>
                </ng-container>
              </div>
              <!-- DQ/DP Section -->
              <div class="col-md-6">
                <h6 class="font-weight-bold mb-3">DQ / DP Antigens</h6>
                <div class="form-group row gx-1">
                  <div class="col-sm-2"></div>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">ALLELE</label>
                  <label class="col-sm-2 col-form-label text-center font-weight-bold">SEROLOGIC</label>
                  <div class="col-sm-2"></div>
                </div>
                <ng-container *ngFor="let antigen of ['DQB1', 'DQA1', 'DPB1', 'DPA1']">
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">{{antigen}}:</label>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_2']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_1"
                        (change)="onAlleleChange(antigen, 2, antigen + '_ALLELE_1', antigen + '_SERO_1')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 2, tissueForm.get(antigen + '_ALLELE_1').value)?.length > 1; else singleSero5">
                        <ng-select [items]="seroOptionsMap[antigen + '_2_' + antigen + '_ALLELE_1'] || []"
                          formControlName="{{antigen}}_SERO_1" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero5>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_1"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                    <div class="col-sm-2">
                      <ng-select [items]="alleleOptionsMap[antigen + '_2']" bindLabel="allele" bindValue="allele"
                        [virtualScroll]="true" formControlName="{{antigen}}_ALLELE_2"
                        (change)="onAlleleChange(antigen, 2, antigen + '_ALLELE_2', antigen + '_SERO_2')"
                        placeholder="">
                      </ng-select>
                    </div>
                    <div class="col-sm-2">
                      <ng-container
                        *ngIf="getSeroOptionsForAllele(antigen, 2, tissueForm.get(antigen + '_ALLELE_2').value)?.length > 1; else singleSero6">
                        <ng-select [items]="seroOptionsMap[antigen + '_2_' + antigen + '_ALLELE_2'] || []"
                          formControlName="{{antigen}}_SERO_2" [clearable]="true" placeholder="Select Sero">
                        </ng-select>
                      </ng-container>
                      <ng-template #singleSero6>
                        <input type="text" class="form-control form-control-sm" formControlName="{{antigen}}_SERO_2"
                          readonly placeholder="">
                      </ng-template>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </form>



  <!-- Selected antigens and summary side by side -->
  <div class="card mb-2">
    <div class="card-header">Unacceptable Antigens</div>
    <div class="card-body">
      <div class="row">
        <div class="col-sm-6">
          <h6>Selected Antigens</h6>
          <div class="border p-3 rounded min-ht-100">
            <ul class="chips">
              <li *ngFor="let a of getAllUniqueAntigens()" class="chip-item">
                {{ a.group }} - {{ a.seroEquivalent }}
              </li>
            </ul>
          </div>
        </div>
        <div class="col-sm-6">
          <h6>Summary</h6>
          <textarea class="form-control min-ht-100" cols="5" [(ngModel)]="allRemarks" readonly></textarea>
        </div>
      </div>
    </div>

    <div *ngIf="showDatePicker" class="datepicker-overlay">
      <div class="datepicker-popup">
        <ngb-datepicker [(ngModel)]="newTabDate" (select)="onDateSelect($event)"></ngb-datepicker>
        <div class="mt-2 text-center">
          <button class="btn btn-sm btn-secondary" (click)="cancelDatePicker()" type="button">Cancel</button>
        </div>
      </div>
    </div>


    <!--add some margin top-->
    <div *ngIf="centralRegNoExit" class="card-body">
      <div class="row" style="margin-top: 15px;">
        <div class="col-12">
          <!-- Class Tabs -->
          <ul class="nav nav-tabs mb-2">
            <li class="nav-item" *ngFor="let tab of unacceptableTabs; let i = index">
              <button class="nav-link" [class.active]="activeUnacceptableTab === i" (click)="onTabChange(i)">
                {{ tab.date | date:'dd/MM/yy' }}
              </button>
            </li>
            <li class="nav-item">
              <button class="btn btn-sm btn-outline-primary" (click)="addUnacceptableTabWithDate()" type="button">Add
                Antigents
                (+)</button>
            </li>
          </ul>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <div *ngIf="unacceptableTabs.length && unacceptableTabs[activeUnacceptableTab]">
            <div class="row">
              <div class="col-sm-8 border-right">
                <div class="group-box">
                  <button class="btn btn-outline-secondary antigen-btn" *ngFor="let group of antigenGroups"
                    [class.active]="unacceptableTabs[activeUnacceptableTab].selectedGroups.includes(group)"
                    (click)="onGroupSelect(activeUnacceptableTab, group)">
                    {{ group }}
                  </button>
                </div>


                <!-- Allele selection for selected group -->
                <div class="row-cont">
                  <ng-container *ngIf="unacceptableTabs[activeUnacceptableTab].selectedGroup">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <div class="font-weight-bold">
                        Group {{ unacceptableTabs[activeUnacceptableTab].selectedGroup }}
                      </div>
                      <button class="btn btn-sm btn-outline-danger" (click)="removeTab(activeUnacceptableTab)"
                        type="button" [disabled]="unacceptableTabs.length <= 0" title="Remove this tab">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                    <div class="check-row">
                      <button
                        *ngFor="let allele of getAllelesForGroup(unacceptableTabs[activeUnacceptableTab].selectedGroup)"
                        class="btn btn-outline-secondary m-1"
                        [class.active]="isOptionSelected(unacceptableTabs[activeUnacceptableTab].selectedGroup, allele.seroEquivalent)"
                        (click)="toggleAlleleSelection(activeUnacceptableTab, allele.seroEquivalent, allele.allele)">
                        {{ allele.seroEquivalent }}
                      </button>
                    </div>
                  </ng-container>
                </div>

                
              </div>

              <!-- Selected antigens display -->
              <div class="col-sm-4">
                <!-- Remarks -->
                <div class="mb-2">
                  <label>Remarks & Recommendations</label>
                  <textarea class="form-control" [(ngModel)]="unacceptableTabs[activeUnacceptableTab].remarks"
                    maxlength="2000" (ngModelChange)="updateAllRemarksSummary()"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="centralRegNoExit" class="btn-container mr-2">
  <button class="btn btn-primary ripple" (click)="navigateToRegister()">
    Back to register page
  </button>
  <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callCase()">Case Details</button>
  <button class="btn btn-sm btn-secondary" *ngIf="centralRegNoExit" (click)="clearData()"> Clear</button>
  <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="saveTissueTypeApi()"> Save</button>
</div>