import { Component, OnInit } from '@angular/core';
import { SelectItem } from 'primeng/api'
import { MasterService } from '../_services/master.service';
import { RegistryService } from '../_services/registry.service';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { GridOptions, _ } from "ag-grid-community";
@Component({
  selector: 'app-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss'],
  providers: [RegistryService]
})
export class SearchComponent implements OnInit {
  sex: any;
  centralRegNo: any;
  ageFrom: any;
  ageTo: any;
  regInst: any;
  walCode: any;
  vilCode: any;
  estCode: any;
  rbs: any;
  rbsFrom: any;
  rbsTo: any;
  bradenFrom: any;
  bradenTo: any;
  braden: any;
  mentalState: any;
  rbsList: any[];
  bradenList: any[];
  mentalStateList: any[];
  selectedRbs: string[] = [];
  selectedBrraden: string[] = [];
  selectedMentalState: string[] = [];
  wallayatList: any[];
  villageList: any[];
  institeList: any[];
  regionList: any[];
  gridOptions: GridOptions;
  rowData: any[];
  columnDefs: any[];
  rowCount: string;
  gridApi: any;
  gridColumnApi: any;
  paginationPageSize: any;
  cacheBlockSize: any;
  regName;
  showToolPanel;
  dataSource: any;
  advancedToggle: boolean = false;

  public searchData: any = { wallayat: null }

  constructor(private _masterService: MasterService, private _registryService: RegistryService) {
    this.initGridData();
  }

  ngOnInit() {
    this.getWilayatList();
    this.getVillageList();
    this.getInstiteList();
    this.getExamFreqList();
    this.getRegionsList();
  }

  getWilayatList(regCode: any = 0) {
    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
    }, error => {
    });
  }

  getVillageList(walCode: any = 0) {
    this._masterService.getVillageList(walCode).subscribe(response => {
      this.villageList = response.result;
    }, error => {
    });
  }

  getInstiteList(regCode: any = 0, walCode: any = 0) {
    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      //20335
      //let filtered = this.institeList.reduce((result, { estCode, estFullName }) => estCode ==20335 ? result.concat(estFullName) : result, []);
      let filtereda = this.institeList.filter(s => s.estCode == 20335);
      let filtered = filtereda.map(s => s.estName);
      //this.institeList.filter(s => s.estCode == 20335 ).map(s => s.estName);
    }, error => {
    });
  }

  getRegionsList() {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionList = response.result;
    }, error => {
    });
  }

  getExamFreqList(examParam: any = 0) {
    this._masterService.getExamFreqList(examParam).subscribe(response => {
      let result: any = response.result;
      this.rbsList = result.filter(item => item.examParam === 13);
      this.bradenList = result.filter(item => item.examParam === 12);
      this.mentalStateList = result.filter(item => item.examParam === 14);
    });
  }

  private initGridData() {
    // we pass an empty gridOptions in, so we can grab the api out
    this.gridOptions = <GridOptions>{};
    this.createColumnDefs();
    this.gridOptions.cacheOverflowSize = 2;
    this.gridOptions.maxConcurrentDatasourceRequests = 2;
    this.gridOptions.infiniteInitialRowCount = 1;
    this.gridOptions.maxBlocksInCache = 2;
    this.gridOptions.cacheBlockSize = 10;
    this.cacheBlockSize = this.gridOptions.cacheBlockSize;
  }

  private createColumnDefs() {
    this.columnDefs = [
      {
        headerName: '#',
        width: 40,
        minWidth: 20, maxWidth: 40,
        checkboxSelection: true,
        suppressSorting: true,
        suppressMenu: true,
        pinned: true,
        suppressFilter: true,
      },
      {
        headerName: 'Civil Id',
        field: 'civilId',
        width: 150,
        minWidth: 100, maxWidth: 150,
        pinned: true,
        filter: "agTextColumnFilter",
        filterParams: { newRowsAction: 'keep' },
      },
      {
        headerName: 'Reg No',
        field: 'centralRegNo',
        width: 150,
        minWidth: 100, maxWidth: 150,
        pinned: true,
        filter: "agTextColumnFilter",
        filterParams: { newRowsAction: 'keep' }
      },
      {
        headerName: 'firstName',
        field: 'firstName',
        width: 350,
        minWidth: 100, maxWidth: 350,
        pinned: true,
        filter: "agTextColumnFilter",
        filterParams: { newRowsAction: 'keep' }
      },
      {
        headerName: 'SecondName',
        field: 'secondName',
        width: 350,
        minWidth: 100, maxWidth: 350,
        pinned: true,
        filter: "agTextColumnFilter",
        filterParams: { newRowsAction: 'keep' }
      },
      {
        headerName: 'ThirdName',
        field: 'thirdName',
        width: 350,
        minWidth: 100, maxWidth: 350,
        pinned: true,
        filter: "agTextColumnFilter",
        filterParams: { newRowsAction: 'keep' }
      },
      {
        headerName: 'Age',
        field: 'dob',
        width: 100,
        minWidth: 50, maxWidth: 100,
        suppressSorting: true,
        suppressMenu: true,
        pinned: true,
        suppressFilter: true,
        //// type: 'boolean',
      },
      {
        headerName: 'Institute',
        field: 'estName',
        width: 350,
        minWidth: 100, maxWidth: 350,
        suppressSorting: true,
        suppressMenu: true,
        pinned: true,
        suppressFilter: true,
      },
      {
        headerName: 'Wilayat',
        field: 'walName',
        width: 200,
        minWidth: 50, maxWidth: 200,
        suppressSorting: true,
        suppressMenu: true,
        pinned: true,
        suppressFilter: true,
      },

    ];
  }
  //for In-Memory grid refersh of datra (rowdata)
  onGridReady(params: any) {
    //console.log('onGridReady');
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.getSearchData();
  }

  onCellClicked() {
  }

  getSearchData() {
    this._registryService.searchRegistry(this.searchData).subscribe(
      reponse => {
        if (reponse.result != null) {
          this.gridApi.setRowData(reponse.result);
        }
        else {
        }
      },
      error => {
      },
    );
  }

  onSearch(formData: any) {
    //this.searchFilter = [];
    //this.generateFilterModel(formData);
    //this.gridOptions.api.setDatasource(this.dataSource);
    this.searchData = formData;
    this.getSearchData();
  }

  onGridSizeChanged($event: any) {
    //console.log('onGridSizeChanged: ' + $event);
    this.gridOptions.api.sizeColumnsToFit();
  }
}
