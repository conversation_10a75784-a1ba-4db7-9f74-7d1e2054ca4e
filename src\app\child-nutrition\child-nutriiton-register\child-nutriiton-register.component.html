<div class="row">
  <div class="col-sm-9">
    <h6 class="page-title">Child Nutrition Register</h6>
  </div>
  <div class="col-sm-3">
    <form [formGroup]="searchForm">
      <div class="row top-search">
        <label class="col col-form-label text-right">Nutrition No.</label>
        <div class="col">
          <input type="text" class="form-control form-control-sm" formControlName="nutNo" (keyup.enter)="search()">
        </div>
      </div>
    </form>
  </div>
</div>
<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
  <ngb-panel id="patientDetails" id="ngb-panel-0">
    <ng-template ngbPanelHeader let-opened="opened">
      <div class="d-flex align-items-center justify-content-between card-head">
        <h6> Child Details </h6>
      </div>
    </ng-template>
    <ng-template ngbPanelContent>
      <app-patient-details #patientDetails [isChildNut]="true" [submitted]="submitted"
        (callMethod)="callMpiMethod()"></app-patient-details>
    </ng-template>
    <ng-template ngbPanelContent>
      <app-patient-details [submitted]="submitted" [downloadFromShifa]="downloadFromShifa"
        (fetchShifa)="callFetchDataFromAlShifa(0,0,0)" #patientDetails></app-patient-details>
    </ng-template>
  </ngb-panel>

  <!-- REGISTER -->

  <ngb-panel id="patientDetails" id="ngb-panel-0">
    <ng-template ngbPanelHeader let-opened="opened">
      <div class="d-flex align-items-center justify-content-between card-head">
        <h6> Register </h6>
        <span *ngIf="visitsHist && visitsHist.length" class=" float-right btn btn-lg btn-primary"
          (click)="openVisitsHistWindow()">History</span>
      </div>
    </ng-template>
    <ng-template ngbPanelContent>
      <form [formGroup]="childNutritionRegister">

        <div class="row">
          <div class="col-sm-12">
            <div class="row">
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Nutrition Number</label>
                  <input type="text" formControlName="nutritionNo" class="form-control form-control-sm" readonly>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Registration Date <span class="mdtr">*</span></label>
                  <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true" formControlName="regDate"
                    [ngModelOptions]="{standalone: true}" [minDate]="minRegDate" [disabled]="!minRegDate || disableRegDate"
                    monthNavigator="true" yearNavigator="true" yearRange="1930:2035" showButtonBar="true"
                    (onSelect)="onInputClacRCFAssess()">
                  </p-calendar>
                  <span *ngIf="this.childNutritionRegister.get('regDate').hasError('required') && childNutritionRegister.get('regDate').touched" class="tooltiptext">{{ 'Registration Date is required'
                  }}</span>

                </div>
              </div>

              <div class="col-sm-3">

                <div class="form-group">
                  <label>EPI No.</label>
                  <input type="text" formControlName="epiNo" class="form-control form-control-sm">
                </div>

              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Age (in Months)</label>
                  <input type="text" formControlName="ageAtVisit" class="form-control form-control-sm" readonly>
                </div>
              </div>
            </div>
          </div>

          <div class="col-sm-12">
            <div class="row">


              <div class="col-sm-3">
                <div class="form-group">
                  <label>Weight (kg)</label>
                  <input type="number" formControlName="weight" class="form-control form-control-sm"
                    (focusout)="onInputCalc('R',$event)">
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Height/Length (cm)</label>
                  <input type="text" formControlName="height" class="form-control form-control-sm"
                    (focusout)="onInputCalc('R',$event)">
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Head Circumference (cm)</label>
                  <input type="number" formControlName="headCircum" class="form-control form-control-sm"
                    (focusout)="onInputCalc('R',$event)">
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>BMI for Age</label>
                  <input type="number" formControlName="Rbmi" class="form-control form-control-sm" readonly>
                </div>
              </div>
            </div>
          </div>
        </div>




        <div class="row">

          <div class="col-md-3 col-lg-2">
            <div class="form-group">
              <label>Diarrhea</label>
              <div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" formControlName="diarrheaYn" value="Y">
                  Yes
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" formControlName="diarrheaYn" value="N">
                  No
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-lg-2">
            <div class="form-group">
              <label>Oedema </label>
              <div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" formControlName="oedemaYn" value="Y"
                    (input)="onInputCalc('R',$event)"> Yes
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" formControlName="oedemaYn" value="N"
                    (input)="onInputCalc('R',$event)"> No
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-lg-2">
            <div class="form-group">
              <label>Other Disease</label>
              <div>
                <div class="form-check form-check-inline">
                  <input type="radio" class="form-check-input" formControlName="otherDiseasesYn" value='Y' /> Yes
                </div>
                <div class="form-check form-check-inline">
                  <input type="radio" class="form-check-input" formControlName="otherDiseasesYn" value='N'
                    (change)="clearUnSelect(childNutritionRegister.get('otherDiseasesYn'), childNutritionRegister.get('otherDiseases'))" />
                  No
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-lg-2" *ngIf="childNutritionRegister.get('otherDiseasesYn').value == 'Y'">
            <div>
              <div class="form-group">
                <label>Specify</label>
                <input type="text" class="form-control form-control-sm" formControlName="otherDiseases">
              </div>
            </div>
          </div>
          <div class="col-md-3 col-lg-2">
            <div class="form-group">
              <label>Medical Background</label>
              <ng-select #entryPoint [items]="medHistory" [virtualScroll]="true" placeholder="Select"
                bindLabel="paramDesc" bindValue="paramId" formControlName="medHistory">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                </ng-template>
              </ng-select>

            </div>
          </div>
          <div class="col-md-12 col-lg-4">
            <div class="mcard custom-mcard">
              <b>At Birth</b>
              <div class="mcard-body ">
                <div class="row">
                  <div class="col-sm-12">
                    <div class="row">
                      <div class="col-sm-4">
                        <div class="form-group">
                          <label>Weight (kg)</label>
                          <input type="number" formControlName="weightBirth" class="form-control form-control-sm">
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <div class="form-group">
                          <label>Length (cm)</label>
                          <input type="number" formControlName="lengthBirth" class="form-control form-control-sm">
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <div class="form-group">
                          <label>Head Circumference (cm)</label>
                          <input type="number" formControlName="headCrBirth" class="form-control form-control-sm">
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <button type="Button" class="btn btn-sm btn-primary"
                          (click)="DownloadBirthDetails(patientDetails.patientForm.controls['regInst'].value, patientDetails.patientForm.controls['patientId'].value)"
                          style="
                                margin-left: 480px;"> Download </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-2">
          <div class="col-sm-12">
            <table class="table table-sm table-bordered table-striped">
              <thead>
                <tr>
                  <th width="20%">Malnutrition (SD)</th>
                  <th width="10%">Z score</th>
                  <th width="50%">Malnutrition Level</th>
                  <th width="10%">Color Code</th>
                  <th width="10%">Growth Chart</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of malnutritionDataR;index as i">
                  <td> {{ item.name }}</td>
                  <td><input type="text" [formControlName]="item.formCName" class="form-control form-control-sm "
                      readonly>
                  </td>
                  <td>
                    <div [formGroup]="childNutritionRegister" class="form-check-inline radio-list"
                      *ngFor="let level of item.level | slice:0:item.level.length">
                      <label class="form-check-label">
                        <ng-container *ngIf="level.value == childNutritionRegister.get(level.formCLevel)?.value">

                          <input type="radio" class="form-check-input" value="level.value" checked
                            (change)="getColor(level.value)" name="malnutriLevel{{i}}"> {{
                          level.item
                          }}
                        </ng-container>
                        <ng-container *ngIf="!(level.value == childNutritionRegister.get(level.formCLevel)?.value)">

                          <input type="radio" class="form-check-input" value="level.value"
                            (change)="getColor(level.value)" name="malnutriLevel{{i}}" disabled> {{
                          level.item
                          }}
                        </ng-container>
                      </label>
                    </div>
                  </td>

                  <!-- <td><input type="text" [formControlName]="item.formCName"
                                        class="form-control form-control-sm">
                                   </td> -->
                  <td>
                    <div class="color-div">
                      <span *ngFor="let color of item.color | slice:0:item.color.length; index as i"
                        [ngClass]="{'active': childNutritionRegister.get(color.formCLevel)?.value == color.value }"
                        class="mal-ico {{color.name}}"></span>
                    </div>
                  </td>
                  <td>
                    <span class="btn btn-sm icon-btn"
                      (click)="openGrowthChartWindow(item, this.childNutritionRegister.value)"><i
                        class="fa fa-area-chart"></i></span>
                  </td>
                </tr>
              </tbody>
            </table>

          </div>


        </div>
        <div class="divider mt-3">
          <div class="row mt-3">
            <div class="col-sm-4">
              <div class="form-group">
                <label>Assessment Outcome probable cause & malnutrition</label>
                <ng-select #entryPoint [items]="assOutcome" [virtualScroll]="true" placeholder="Select"
                  bindLabel="paramDesc" bindValue="paramId" formControlName="outcomeCause"
                  (change)="clearUnSelect(childNutritionRegister.get('outcomeCause'), childNutritionRegister.get('outcomeCauseSpec'))">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div *ngIf="childNutritionRegister.get('outcomeCause').value == 16" class="col-sm-4">
              <div class="form-group">
                <div class="form-group">
                  <label>Specify</label>
                  <textarea type="text" class="form-control form-control-sm spicfy" rows="1"
                    [ngModelOptions]="{standalone: true}" formControlName="outcomeCauseSpec"
                    (keyup)="autoGrowTextZone($event)" (keydown)="autoGrowTextZone($event)"></textarea>
                </div>
                <span class="tooltiptext"
                  *ngIf="childNutritionRegister.get('outcomeCause').touched && childNutritionRegister.get('outcomeCause').value == 16">
                  Specify is required.
                </span>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label>Health and Physical Activity Assessment</label>
                <input type="text" class="form-control form-control-sm" formControlName="healthAssesment">
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label>Specify action if any</label>
                <ng-select #entryPoint [items]="malStatus" [virtualScroll]="true" placeholder="Select"
                  bindLabel="paramDesc" bindValue="paramId" formControlName="status">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                  </ng-template>
                </ng-select>

              </div>
            </div>
          </div>
          <div class="tab-view">
            <tabset>
              <tab heading="Lactation Assessment/Counselling" id="LactationAssessment"
                *ngIf="childNutritionRegister.get('ageAtVisit').value <= 24">
                <div class="mcard">
                  <div class="mcard-header clearfix">
                    <div class="mb-2">
                      <button type="Button" class="btn btn-sm btn-primary" (click)="AddNewAsses('R')">Add New</button>
                    </div>
                  </div>
                  <div class="mcard-body">
                    <div class="row">
                      <div class="col-md-12 col-lg-6">
                        <table #assess class="table table-sm table-striped">
                          <thead>
                            <tr>
                              <th width="50%">Assesssment</th>
                              <th width="35%">Remarks</th>
                              <th width="5%"></th>

                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of nutAssesList; let i = index">
                              <td>
                                <div class="ng-custom">
                                  <ng-select appendTo="body" [items]="assLact" [(ngModel)]="item.paramId"
                                    [ngModelOptions]="{standalone: true}" [virtualScroll]="true" placeholder="Select"
                                    bindLabel="paramDesc" bindValue="paramId" (change)="addEditAsses('R', item, i)">

                                    <ng-option *ngFor="let c of assLact" [value]="c.paramId">{{
                                      c.paramDesc
                                      }}</ng-option>
                                  </ng-select>
                                </div>
                              </td>
                              <td>
                                <textarea class="form-control h-30" rows="1" [(ngModel)]="item.remarks"
                                  [ngModelOptions]="{standalone: true}" (keyup)="autoGrowTextZone($event)"
                                  (keydown)="autoGrowTextZone($event)" (input)="addEditAsses('R', item, i)"></textarea>

                              </td>
                              <td>
                                <button *ngIf="!item.lactId" class="fas fa-trash action-btn"
                                  (click)="removeAsses('R', i)"></button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </tab>
              <tab heading="Child Nutrition Assessment" id="childNutriAssessment">
                <div class="mcard">
                  <div class="mcard-header">
                    <div class="mb-2">
                      <span *ngIf="cFAssessR && cFAssessR.length" class="  btn btn-lg btn-primary"
                        (click)="opencFAssessHistWindow('R')">Summary</span>
                    </div>
                  </div>
                  <div class="tabs py-3">
                    <div class="row">
                      <div class="col-sm-6">
                        <div *ngIf="cFAssessR && cFAssessR.length" class="mcard-body">
                          <label><b>Questions</b></label>
                          <table #bLabtable class="table table-sm table-striped">
                            <thead>
                              <tr>
                                <th width="80%"></th>
                                <th width="20%"></th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr
                                *ngFor="let item of cFAssessR | slice: (page3-1) * pageSize : (page3-1) * pageSize + pageSize">
                                <td>{{ item.qstnDesc }}<span style="color: red;">*</span></td>
                                <td>
                                  <div>
                                    <div class="form-check form-check-inline">
                                      <input (click)="onChickedCFAssess(item, 'Y')" class="form-check-input"
                                        type="radio" name="{{item.qstnId}}"
                                        [checked]="item.answerYn == 'Y'? true : false">
                                      Yes
                                    </div>
                                    <div class="form-check form-check-inline">
                                      <input (click)="onChickedCFAssess(item, 'N')" class="form-check-input"
                                        type="radio" name="{{item.qstnId}}"
                                        [checked]="item.answerYn == 'N'? true : false">
                                      No
                                    </div>
                                  </div>
                                </td>
                              </tr>

                            </tbody>
                          </table>

                          <ngb-pagination class="d-flex justify-content-center" [(page)]="page3" [pageSize]="pageSize"
                            [collectionSize]="cFAssessR.length">
                          </ngb-pagination>
                        </div>
                      </div>
                      <div class="col-sm-6">
                        <label><b>Remarks</b></label>
                        <textarea class="form-control spicfy" rows="1" [ngModelOptions]="{standalone: true}"
                          (keyup)="autoGrowTextZone($event)" (keydown)="autoGrowTextZone($event)"
                          formControlName="assessmentRemarks"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </tab>
              <tab heading="Counselling/Key Messages" id="counselling">
                <div class="mcard">
                  <div class="mcard-header">
                    <div class="mb-2">
                      <button type="Button" class="btn btn-sm btn-primary" (click)="AddNewKeyMsg('R')">Add New</button>
                    </div>
                  </div>
                  <div class="mcard-body">
                    <div class="row">
                      <div class="col-md-12 col-lg-10">
                        <table #assess class="table table-sm table-striped">
                          <thead>
                            <tr>
                              <th width="50%">Key Message</th>
                              <th width="35%">Remarks</th>
                              <th width="5%"></th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of nutKeyMsgList; let i = index">
                              <td>
                                <div class="ng-custom">
                                  <ng-select appendTo="body" [items]="keyMsg" [(ngModel)]="item.keyId"
                                    [ngModelOptions]="{standalone: true}" [virtualScroll]="true" placeholder="Select"
                                    bindLabel="paramDesc" bindValue="paramId" (change)="addEditKeyMsg('R', item, i)">

                                    <ng-option *ngFor="let c of keyMsg" [value]="c.paramId">{{
                                      c.paramDesc
                                      }}</ng-option>
                                  </ng-select>
                                </div>
                              </td>
                              <td>
                                <textarea class="form-control h-30" rows="1" [(ngModel)]="item.remarks"
                                  [ngModelOptions]="{standalone: true}" (keyup)="autoGrowTextZone($event)"
                                  (keydown)="autoGrowTextZone($event)" (input)="addEditKeyMsg('R', item, i)"></textarea>

                              </td>
                              <td>
                                <button *ngIf="!item.runId" class="fas fa-trash action-btn"
                                  (click)="removeKeyMsg('R', i)"></button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </tab>
              <tab heading="lab" id="lab">
                <div class="mcard">
                  <div *ngIf="nutLabInvestR && nutLabInvestR.length" class="mcard-body">
                    <div class="row">
                      <div class="col-md-12 col-lg-10">
                        <button type="Button" class="btn btn-sm btn-primary"
                          (click)="DownloadLabDetails(patientDetails.patientForm.controls['regInst'].value, patientDetails.patientForm.controls['patientId'].value)">
                          Download </button>
                        <table #bLabtable class="table table-sm table-striped">
                          <thead>
                            <tr>
                              <th width="5%"></th>
                              <th>Test Name</th>
                              <th>Test Result</th>
                              <th>Remark</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              *ngFor="let item of nutLabInvestR | slice: (page1-1) * pageSize : (page1-1) * pageSize + pageSize">
                              <td>
                                <input type="checkbox" [value]="item.paramId" id="check"
                                  (change)="onChicked(item,$event)" [checked]="item.checked"
                                  [ngClass]="{'disableCheckBox': item.invstId}" />
                              </td>
                              <td>{{item.paramDesc}}</td>
                              <td *ngIf="item.paramDesc == 'Haemoglobin'">
                                <input class="form-control form-control-sm" type="text" id='result'
                                  [value]="item.result != undefined ? item.result : null"
                                  (input)="onInput(item,$event,'R')"
                                  [ngStyle]="{'background-color': backgroundColorR, 'font-weight': 'bold'}"
                                  [attr.disabled]="!item.checked ? true : null" />
                              </td>
                              <td *ngIf="item.paramDesc != 'Haemoglobin'">
                                <input class="form-control form-control-sm" type="text" id='result'
                                  [value]="item.result != undefined ? item.result : null" (input)="onInput(item,$event)"
                                  [attr.disabled]="!item.checked ? true : null" />
                              </td>
                              <td>
                                <input class="form-control form-control-sm" type="text" id="remarks"
                                  [value]="item.remarks != undefined ? item.remarks : null"
                                  (input)="onInput(item,$event)" [attr.disabled]="!item.checked ? true : null" />
                              </td>
                            </tr>

                          </tbody>
                        </table>
                        <ngb-pagination class="d-flex justify-content-center" [(page)]="page1" [pageSize]="pageSize"
                          [collectionSize]="nutLabInvestR.length">
                        </ngb-pagination>
                      </div>
                    </div>
                  </div>
                </div>
              </tab>
            </tabset>
          </div>
        </div>
      </form>
    </ng-template>
  </ngb-panel>

  <!-- FOLLOW UP -->

  <ngb-panel id="patientDetails" id="ngb-panel-0">
    <ng-template ngbPanelHeader let-opened="opened">
      <div class="d-flex align-items-center justify-content-between card-head">
        <h6> Follow Up</h6>
      </div>
    </ng-template>
    <ng-template ngbPanelContent>
      <form [formGroup]="followUpForm">
        <div class="row">
          <div style="position: relative;">
            <ngx-spinner bdOpacity=0.9 bdColor="rgba(51,51,51,0.8)" size="medium" color="#fff"
              type="ball-scale-multiple">
              <p style="font-size: 20px; color: white">Fetching from Al Shifa...</p>
            </ngx-spinner>
          </div>
          <div class="col-sm-2">

            <p-calendar class="addNewCale" appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
              [disabled]="!minVisitDate" [minDate]="minVisitDate" monthNavigator="true" yearRange="1930:2035"
              yearNavigator="true" showButtonBar="true" [ngModelOptions]="{standalone: true}"
              (onSelect)="addNewFollowUp($event, 'get')">
            </p-calendar>

            <ul class="list-group followup-nav">
              <li class="list-group-item added-list" *ngFor="let item of nutVisitsList; let i = index"
                (click)="addEditFollowUp(this.followUpForm.value, 'add', item, i);"
                [ngClass]="{'active': selectedFUDate == item.visitDate}">{{
                item.visitDate | date: 'dd-MM-yyyy'}}
                <button *ngIf="item.visitId == null" class="fas fa-trash" (click)="removeFollowUp(i)"></button>
                <button *ngIf="selectedFUDate == item.visitDate" class="fa fa-print"
                  (click)="printVisitSummary($event,item)"></button>

              </li>

            </ul>
          </div>
          <div *ngIf="selectedFUDate" class="col-sm-10 border-left">
            <div class="row">
              <div class="col-sm-2">
                <div class="form-group">
                  <label>FollowUp Institutes <span class="mdtr">*</span></label>
                  <ng-select #entryPoint appendTo="body" [items]="institutesFilter" [virtualScroll]="true"
                    formControlName="visitInst" placeholder="Select" bindLabel="estName" bindValue="estCode"
                    (change)="changeFollowUpInstitute($event)">

                    <ng-option *ngFor="let c of institutes" [value]="c.estCode">{{ c.estName
                      }}</ng-option>
                  </ng-select>
                </div>
                <span class="tooltiptext"
                  *ngIf="followUpForm.get('visitInst').hasError('required') && followUpForm.get('visitInst').touched">
                  FollowUp Institutes is required.
                </span>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label>Age (in Months)</label>
                  <input type="text" class="form-control form-control-sm" formControlName="ageAtVisit" readonly>
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label>Weight (kg)</label>
                  <input type="text" class="form-control form-control-sm" formControlName="weight"
                    (focusout)="onInputCalc('F',$event)" (keydown.enter)="disableEnterKey($event)">
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label>Height/Length (cm)</label>
                  <input type="text" class="form-control form-control-sm" formControlName="height"
                    (focusout)="onInputCalc('F',$event)" (keydown.enter)="disableEnterKey($event)">
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label>Head Circumference (cm)</label>
                  <input type="text" class="form-control form-control-sm" formControlName="headCircum"
                    (focusout)="onInputCalc('F',$event)" (keydown.enter)="disableEnterKey($event)">
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label>BMI for Age</label>
                  <input type="number" formControlName="Fbmi" class="form-control form-control-sm" readonly>
                </div>
              </div>
            </div>
            <div>


              <table class="table table-sm table-bordered table-striped">
                <thead>
                  <tr>
                    <th width="20%">Malnutrition (SD)</th>
                    <th width="10%">Z Score</th>
                    <th width="30%">Malnutrition Level</th>
                    <th width="10%">Color Code</th>
                    <th width="15%">Progress</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of malnutritionDataF; index as i">
                    <!-- Malnutrition Type -->
                    <td>{{ item.name }}</td>

                    <!-- Z Score Input -->
                    <td>
                      <input type="text" [formControlName]="item.formCName"
                        class="form-control form-control-sm text-center" readonly>
                    </td>

                    <!-- Malnutrition Level (Radio Buttons) -->

                    <td>
                      <div [formGroup]="followUpForm" class="form-check-inline radio-list"
                        *ngFor="let level of item.level | slice:0:item.level.length">
                        <label class="form-check-label">
                          <ng-container *ngIf="level.value == followUpForm.get(level.formCLevel)?.value">
                            <input type="radio" class="form-check-input" [value]="level.value" checked
                              (change)="getColor(level.value)" name="malnutriLevel{{i}}">
                            {{ level.item }}
                          </ng-container>
                          <ng-container *ngIf="!(level.value == followUpForm.get(level.formCLevel)?.value)">
                            <input type="radio" class="form-check-input" [value]="level.value"
                              (change)="getColor(level.value)" name="malnutriLevel{{i}}" disabled>
                            {{ level.item }}

                          </ng-container>
                        </label>
                      </div>
                    </td>


                    <!-- Color Code (Colored Circles) -->
                    <td>

                      <span *ngFor="let color of item.color | slice: 0:item.color.length; index as i"
                        [ngClass]="{'active': followUpForm.get(color.formCLevel)?.value == color.value }"
                        class="mal-ico {{color.name}}"></span>

                    </td>

                    <!-- Improvement Input Field -->
                    <!-- <td>
                                            <div [formGroup]="followUpForm">
                                              <input type="text" [formControlName]="item.progress"
                                                class="form-control form-control-sm text-center" >
                                            </div>
                                          </td> -->

                    <td>
                      <div [formGroup]="followUpForm" class="text-center">
                        <div class="banner-img">

                          <!-- Conditional rendering based on progress status -->
                          <ng-container *ngIf="followUpForm.get(item.progress).value === 'Improved'">
                            <label class="form-control-sm text-center" style="font-weight: bold;">
                              {{ followUpForm.get(item.progressDes)?.value }}
                            </label>

                          </ng-container>
                        </div>
                        <div class="banner-img">
                          <ng-container *ngIf="followUpForm.get(item.progress).value === 'Worse'">
                            <label class="form-control-sm text-center" style="font-weight: bold;">
                              {{ followUpForm.get(item.progressDes)?.value }}
                            </label>

                          </ng-container>
                        </div>
                        <div class="banner-img">
                          <ng-container *ngIf="followUpForm.get(item.progress).value === 'Not Improved'">
                            <label class="form-control-sm text-center" style="font-weight: bold;">
                              {{ followUpForm.get(item.progressDes)?.value }}
                            </label>

                          </ng-container>
                        </div>
                      </div>
                    </td>



                  </tr>
                </tbody>
              </table>

            </div>
            <div class="row mt-3 py-3">
              <div class="col-sm-2">
                <div class="form-group">
                  <label>Health Status</label>
                  <input type="text" class="form-control form-control-sm" formControlName="healthStatus">
                </div>
              </div>
              <div class="col-sm-2 border-left">
                <div class="form-group">
                  <label>Diarrhea</label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" formControlName="diarrheaYn" value="Y"> Yes
                    </div>
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" formControlName="diarrheaYn" value="N"> No
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-2 border-left">
                <div class="form-group">
                  <label>Oedema </label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" formControlName="oedemaYn" value="Y"
                        (input)="onInputCalc('F',$event)"> Yes
                    </div>
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" formControlName="oedemaYn" value="N"
                        (input)="onInputCalc('F',$event)"> No
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-2 border-left">
                <div class="form-group">
                  <label>Other Disease</label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" formControlName="otherDiseasesYn"> Yes
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="radio" formControlName="otherDiseasesYn" class="form-check-input" value="N"
                        (change)="clearUnSelect(childNutritionRegister.get('otherDiseasesYn'), childNutritionRegister.get('otherDiseases'))" />
                      No
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-2" *ngIf="followUpForm.get('otherDiseasesYn').value == 'Y'">
                <div class="form-group">
                  <label>Specify</label>
                  <input type="text" class="form-control form-control-sm" formControlName="otherDiseases">
                </div>
              </div>
            </div>

            <div class="tab-view">
              <tabset>
                <tab heading="Lactation Assessment/Counselling" id="followUpLactationAssessment"
                  *ngIf="followUpForm.get('ageAtVisit').value <= 24">
                  <div class="mcard">
                    <div class="mcard-header">
                      <div class="mb-2">
                        <button type="Button" class="btn btn-sm btn-primary" (click)="AddNewAsses('F')">Add New</button>
                      </div>
                    </div>
                    <div class="mcard-body">
                      <table #assess class="table table-sm table-striped">
                        <thead>
                          <tr>
                            <th width="50%">Assesssment</th>
                            <th width="35%">Remarks</th>
                            <th width="5%"></th>

                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of nutAssesListF; let i = index">
                            <td>
                              <div class="ng-custom">
                                <ng-select appendTo="body" [items]="assLact" [(ngModel)]="item.paramId"
                                  [ngModelOptions]="{standalone: true}" [virtualScroll]="true" placeholder="Select"
                                  bindLabel="paramDesc" bindValue="paramId" (change)="addEditAsses('F', item, i)">

                                  <ng-option *ngFor="let c of assLact" [value]="c.paramId">{{
                                    c.paramDesc
                                    }}</ng-option>
                                </ng-select>
                              </div>
                            </td>
                            <td>
                              <textarea class="form-control h-30" rows="1" [(ngModel)]="item.remarks"
                                [ngModelOptions]="{standalone: true}" (keyup)="autoGrowTextZone($event)"
                                (keydown)="autoGrowTextZone($event)" (input)="addEditAsses('F', item, i)"></textarea>

                            </td>
                            <td>
                              <button *ngIf="!item.lactId" class="fas fa-trash action-btn"
                                (click)="removeAsses('F', i)"></button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </tab>
                <tab heading="Child Nutrition Assessment {{periodRangeNameF}}" id="followUpChildNutriAssess">
                  <div class="mcard">
                    <div class="mcard-header">
                      <div class="mb-2">
                        <span *ngIf="cFAssessF && cFAssessF.length" class="btn btn-lg btn-primary"
                          (click)="opencFAssessHistWindow('F')">Summary</span>
                      </div>

                    </div>
                    <div class="row">
                      <div class="col-sm-6" *ngIf="cFAssessF && cFAssessF.length">
                        <label><b>Questions</b></label>
                        <div class="mcard-body">
                          <table #bLabtable class="table table-sm table-striped">
                            <thead>
                              <tr>
                                <th width="80%"></th>
                                <th width="20%"></th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr
                                *ngFor="let item of cFAssessF | slice: (page4-1) * pageSize : (page4-1) * pageSize + pageSize">
                                <td>{{ item.qstnDesc }}<span style="color: red;">*</span></td>
                                <td>
                                  <div>
                                    <div class="form-check form-check-inline">
                                      <input (click)="onChickedCFAssess(item, 'Y')" class="form-check-input"
                                        type="radio" name="{{item.qstnId}}" [checked]="item.answerYn === 'Y'">
                                      Yes
                                    </div>
                                    <div class="form-check form-check-inline">
                                      <input (click)="onChickedCFAssess(item, 'N')" class="form-check-input"
                                        type="radio" name="{{item.qstnId}}" [checked]="item.answerYn === 'N'">
                                      No
                                    </div>
                                  </div>
                                </td>
                              </tr>


                            </tbody>
                          </table>
                          <ngb-pagination class="d-flex justify-content-center" [(page)]="page4" [pageSize]="pageSize"
                            [collectionSize]="cFAssessF.length">
                          </ngb-pagination>
                        </div>

                      </div>
                      <div class="col-sm-6">
                        <label><b>Remarks</b></label>
                        <div>
                          <textarea class="form-control spicfy" rows="1" [ngModelOptions]="{standalone: true}"
                            (keyup)="autoGrowTextZone($event)" (keydown)="autoGrowTextZone($event)"
                            formControlName="assessmentRemarks"></textarea>
                        </div>
                      </div>
                    </div>

                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label>Specify action if any</label>
                        <ng-select #entryPoint [items]="malStatus" [virtualScroll]="true" placeholder="Select"
                          bindLabel="paramDesc" bindValue="paramId" formControlName="status">
                          <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                          </ng-template>
                        </ng-select>

                      </div>
                    </div>
                  </div>
                </tab>
                <tab heading="Counselling/Key Messages" id="followUpCounselling">
                  <div class="mcard">
                    <div class="mcard-header clearfix">
                      <div class="mb-2">
                        <button type="Button" class="btn btn-sm btn-primary" (click)="AddNewKeyMsg('F')">Add
                          New</button>
                      </div>
                    </div>
                    <div class="mcard-body">
                      <table #assess class="table table-sm table-striped">
                        <thead>
                          <tr>
                            <th width="50%">Key Message</th>
                            <th width="35%">Remarks</th>
                            <th width="5%"></th>

                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of nutKeyMsgListF; let i = index">
                            <td>
                              <div class="ng-custom">
                                <ng-select appendTo="body" [items]="keyMsg" [(ngModel)]="item.keyId"
                                  [ngModelOptions]="{standalone: true}" [virtualScroll]="true" placeholder="Select"
                                  bindLabel="paramDesc" bindValue="paramId" (change)="addEditKeyMsg('F', item, i)">

                                  <ng-option *ngFor="let c of keyMsg" [value]="c.paramId">{{
                                    c.paramDesc
                                    }}</ng-option>
                                </ng-select>
                              </div>
                            </td>
                            <td>
                              <textarea class="form-control h-30" rows="1" [(ngModel)]="item.remarks"
                                [ngModelOptions]="{standalone: true}" (keyup)="autoGrowTextZone($event)"
                                (keydown)="autoGrowTextZone($event)" (input)="addEditKeyMsg('F', item, i)"></textarea>

                            </td>
                            <td>
                              <button *ngIf="!item.runId" class="fas fa-trash action-btn"
                                (click)="removeKeyMsg('F',i)"></button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </tab>
                <tab heading="Lab" id="followUpLab">
                  <div class="mcard">
                    <div *ngIf="nutLabInvestF && nutLabInvestF.length" class="mcard-body">
                      <table #fLabtable class="table table-sm table-striped">
                        <thead>
                          <tr>
                            <th width="5%"></th>
                            <th>Test Name</th>
                            <th>Test Result</th>
                            <th>Remark</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="let item of nutLabInvestF | slice: (page2-1) * pageSize : (page2-1) * pageSize + pageSize">
                            <td>
                              <input type="checkbox" [value]="item.paramId" id="check1"
                                (change)="onChicked(item,$event)" [checked]="item.checked"
                                [ngClass]="{'disableCheckBox': item.invstId}" />
                            </td>
                            <td>{{item.paramDesc}}</td>

                            <!-- Hemoglobin Specific -->
                            <td *ngIf="item.paramDesc == 'Haemoglobin'">
                              <input class="form-control form-control-sm" type="text" id="result1"
                                [value]="item.result != undefined ? item.result : null"
                                (input)="onInput(item,$event,'F')"
                                [ngStyle]="{'background-color': backgroundColorF, 'font-weight': 'bold'}"
                                [attr.disabled]="!item.checked ? true : null" />
                            </td>

                            <!-- Other Tests -->
                            <td *ngIf="item.paramDesc != 'Haemoglobin'">
                              <input class="form-control form-control-sm" type="text" id="result1"
                                [value]="item.result != undefined ? item.result : null" (input)="onInput(item,$event)"
                                [attr.disabled]="!item.checked ? true : null" />
                            </td>

                            <!-- Remarks Field -->

                            <td>
                              <input class="form-control form-control-sm" type="text" id="remarks1"
                                [formControl]="followUpForm.get('remarks1')"
                                [value]="item.remarks != undefined ? item.remarks : null" (input)="onInput(item,$event)"
                                [attr.disabled]="!item.checked ? true : null" />
                            </td>

                          </tr>
                        </tbody>
                      </table>
                      <ngb-pagination class="d-flex justify-content-center" [(page)]="page2" [pageSize]="pageSize"
                        [collectionSize]="nutLabInvestF.length">
                      </ngb-pagination>
                    </div>
                  </div>
                </tab>
              </tabset>
            </div>
          </div>
        </div>

      </form>
    </ng-template>
  </ngb-panel>
</ngb-accordion>

<div class="text-right btn-box">
  <button class="btn btn-sm btn-secondary" (click)="clearAll(true);">Clear</button>
  <button class="btn btn-sm btn-primary" (click)="submitForm();">Save</button>
</div>
<div style="position: relative;">
  <ngx-spinner bdOpacity=0.9 bdColor="rgba(51,51,51,0.8)" size="medium" color="#fff" type="ball-scale-multiple">
    <p style="font-size: 20px; color: white">Fetching from Al Shifa...</p>
  </ngx-spinner>
</div>

<!-- //////////////////////////////////////////((((( Growth Chart Small Window ))))\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ -->
<div id="viewGrowthChartWindow">
  <ng-template #viewGrowthChartWindow let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="modal-basic-title">{{growthChartName}}</h5>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>

    </div>

    <div class="modal-body">

      <div class="text-center chartDiv">
        <p-chart #chart type="line" [data]="data" [options]="options"></p-chart>
        <div class="text-right">
          <button class="btn btn-sm btn-primary" (click)="getChartDataUrl(chart)"><i class="fa fa-print"></i></button>

        </div>
      </div>

    </div>
  </ng-template>

</div>


<!-- //////////////////////////////////////////((((( visits history Small Window ))))\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ -->
<ng-template #viewVisitsHistWindow let-modal>
  <div class="modal-header">
    <h5 class="modal-title" id="modal-basic-title">Visits History</h5>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="modal-body">
      <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="visitsHist"
        singleClickEdit="true" [columnDefs]="columnVisitHistList" rowSelection="single"
        (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
  </div>
</ng-template>



<!-- //////////////////////////////////////////((((( visits history Small Window ))))\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ -->
<ng-template #viewCFAssessHistWindow let-modal>
  <div class="modal-header">
    <h5 class="modal-title" id="modal-basic-title">Child Nutrition Assessment History</h5>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="modal-body">
      <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="answeredQes"
        singleClickEdit="true" [columnDefs]="columncFAssessHistList" rowSelection="single"
        (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
  </div>
</ng-template>