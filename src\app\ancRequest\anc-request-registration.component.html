<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="head-title">ANC Request Form</h6>
    </div>

    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <form [formGroup]="ancSearchKey">
            <div class="input-group ">
            <input type="text" placeholder="Search Anc No" id="ancSearch" formControlName="ancSearch" class="form-control input-sm"
                (keyup.enter)="search()">
            <span class="input-group-btn">

                <button class="btn btn-default btn-sm" id="search" (click)="search()">
                    <i class="fa fa-search"></i>
                </button>
            </span>
          </div>
        </form>
    </div>
</div>

<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
        <ngb-panel id="patientDetails" id="ngb-panel-1">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <!-- (uploaded)="fetchVisitInfobyPatientID($event)" -->
                <app-patient-details [submitted]="submitted" [patientForm]="patientForm" #patientDetails
                    (callMethod)="callMpiMethod()">
                </app-patient-details>

            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>

<!-- *****************************************************ANC REQUEST DETAILS************************************************************** -->

<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
    <ngb-panel id="ancStyle" id="ngb-panel-0">

        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h4 class="page-title">ANC Number Request __</h4>
                <div ngbPanelToggle class="btn btn-link p-0">

                </div>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="content-wrapper mb-2">
                <form [formGroup]="ancPatientForm">
                    <div class="row">
                        <div class="col-sm-8">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>ANC Institute <span class="mdtr">*</span></label>
                                        <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                            placeholder="Select" bindLabel="estName" bindValue="estCode"
                                            formControlName="ancInstitute">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">{{
                                                item.estName}}
                                            </ng-template>
                                        </ng-select>
                                        <span *ngIf="submittedAncInst" class="tooltiptext"
                                            style="font-style: bold">{{'Anc Institute is required'}}</span>
                                    </div>

                                    <div class="bg-blk">
                                        <h4 class="page-title">Current Pregnancy__</h4>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>Gravida </label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="gravida">
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>Parity</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="parity">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-blk ">
                                        <div class="form-group">
                                            <label> Congential Anamolies Present ? </label>

                                            <input type="checkbox" formControlName="congAnamoliesYN"
                                                [checked]="conAnamPreChecked" id="conAnamPreChecked" name="conAnamPre"
                                                (change)="ifChecked($event)" class="ml-2">

                                        </div>
                                    </div>

                                </div>
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Requested Date </label>
                                                <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true 
                                                    formControlName="requestedDate"
                                                    [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                    yearRange="1930:2030" yearNavigator="true"
                                                    showButtonBar="true"></p-calendar>
                                            </div>
                                        </div>

                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>ANC Number </label>
                                                <input type="text" class="form-control form-control-sm"
                                                    formControlName="ancNo"
                                                    readonly
                                                    >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-blk">
                                        <h4 class="page-title">Menstrual__</h4>
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>Cycle</label>
                                                    <ng-select appendTo="body" [items]="menstrualCycle" [virtualScroll]="true"
                                                        formControlName="mensturalCycle" [disabled]="true" placeholder="Select"
                                                        bindLabel="paramDesc" bindValue="id" [disabled]="true">
                                                        <ng-option *ngFor="let c of menstrualCycle" [value]="c.id">{{ c.paramDesc
                                                            }}</ng-option>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>Length </label>
                                                    <input style="width: 150px;" type="number"
                                                        class="form-control form-control-sm"
                                                        formControlName="mensturalLength">
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>Duration </label>
                                                    <input style="width: 150px;" type="number"
                                                        class="form-control form-control-sm"
                                                        formControlName="mensturalDuration">
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>LMP</label>
                                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                        formControlName="lmp" [ngModelOptions]="{standalone: true}"
                                                        monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
                                                        showButtonBar="true"></p-calendar>
                                                </div>
                                            </div>

                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>EDD by LMP</label>
                                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                        formControlName="eddCalc" [ngModelOptions]="{standalone: true}"
                                                        monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
                                                        showButtonBar="true"></p-calendar>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>EDD by Scan</label>
                                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                        formControlName="eddScan" [ngModelOptions]="{standalone: true}"
                                                        monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
                                                        showButtonBar="true"></p-calendar>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <h6>Remarks </h6>
                                    <textarea class="form-control form-control-sm"
                                        formControlName="requestedInstRemarks">
                        </textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>Status</label>
                                        <ng-select #entryPoint appendTo="body" [items]="ancStatusOptions" 
                                            [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                            formControlName="status" class="readonly-ng-select" 
                                            >
                                            <ng-template ng-option-tmp let-item="item" let-index="index" >{{
                                                item.value }}
                                            </ng-template>
                                            
                                        </ng-select>

                                    </div>
                                </div>
                            </div>
                            <div class="bg-blk min-ht">
                                <h4 class="page-title">Last Pregnancy__</h4>
                                <div class="row">
                                    <div class="col-sm-8">
                                        <div class="form-group">
                                            <label>Outcome</label>
                                            <ng-select appendTo="body" [items]="pregnancyOutcome" [virtualScroll]="true"
                                                formControlName="statusInLastPregnancy" [disabled]="true" placeholder="Select"
                                                bindLabel="paramDesc" bindValue="id" [disabled]="true" (change)="checkOutcome()"  >
        
                                                <ng-option *ngFor="let c of pregnancyOutcome" [value]="c.id" >{{ c.paramDesc
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="form-group">
                                            <label>Abortion Date</label>
                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                formControlName="lastAbortionDate" [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
                                                showButtonBar="true"></p-calendar>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="form-group">
                                            <label>Delivery Date</label>
                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                formControlName="lastDeliveryDate" [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
                                                showButtonBar="true"></p-calendar>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>

                    </div>

                    <div class="text-right col-lg-12 col-md-12 col-sm-12">
                        <button type="submit" class="btn btn-primary ripple" (click)="saveAncData()">Save</button>
                        <button type="submit" class="btn btn-primary ripple" (click)="clearData()">Clear</button>
                    </div>
                </form>

            </div>
        </ng-template>


        <!-- //////////////////////////////////////////(((((Small Window ))))\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ -->
        <ng-template #viewSmallANCWindow let-modal>
            <div class="modal-header">
                <h5 class="modal-title" id="modal-basic-title">ANC Request List</h5>
                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-body">

                    <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham"
                        [rowData]="ancRequestList" singleClickEdit="true" [columnDefs]="columnAncRequestList"
                        [suppressRowClickSelection]="true" rowSelection="single" (gridReady)="onGridReady($event)"
                        [gridOptions]="ancDataGridApi">
                    </ag-grid-angular>
                    <div>
                        <button type="submit" (click)="clearAllAncData()" *ngIf="newReqId"
                            class="btn btn-primary ripple"> New </button>
                        <button type="submit" (click)="selectedAncNo()" class="btn btn-primary ripple"> Select </button>

                    </div>
                </div>

            </div>

        </ng-template>
    </ngb-panel>




    <!-- *****************************************************ANC REQUEST HISTORY************************************************************** -->

    <ngb-panel id="ancStylePanal" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h4 class="page-title">Pregnancy History __</h4>
                <div ngbPanelToggle class="btn btn-link p-0">

                </div>
            </div>
        </ng-template>

        <ng-template ngbPanelContent>
            <div class="content-wrapper mb-2">

                <div style="margin-top:20px">
                    <ag-grid-angular style="width: 100%; height: 500px;" class="ag-theme-balham" [rowData]="rowData"
                        [gridOptions]="gridOptions" [columnDefs]="columnDefs"> </ag-grid-angular>
                    <!-- (rowDoubleClicked)="getAncdataByAncNo()" -->
                </div>
            </div>
        </ng-template>
    </ngb-panel>

</ngb-accordion>