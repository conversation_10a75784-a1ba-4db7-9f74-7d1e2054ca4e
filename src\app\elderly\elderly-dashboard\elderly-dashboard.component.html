<h6 class="page-title"><p>Elderly Dashboard </p> </h6>
<div class="row">
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <form [formGroup]="boardForm">
                <div class="">
                    <div class="form-group">
                        <label>Region</label>
                        <ng-select #entryPoint  [items]="regionData" [virtualScroll]="true" placeholder="Select"
                            bindLabel="regName" bindValue="regCode" formControlName="regCode"
                            (change)="locSelect($event,'region')" [(ngModel)]="regCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                            </ng-template>
                        </ng-select>
                    </div>
                    <div class="form-group">
                        <label>Wilayat</label>
                        <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                            bindLabel="walName" bindValue="walCode" formControlName="walCode"
                            (change)="locSelect($event,'wilayat')" [(ngModel)]="walCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                            </ng-template>
                        </ng-select>
                    </div>
                    <div class="form-group">
                        <label>Institute</label>
                        <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true"
                            (change)="locSelect($event,'institute')" placeholder="Select" bindLabel="estName"
                            bindValue="estCode" formControlName="estCode" [(ngModel)]="estCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                            </ng-template>
                        </ng-select>
                    </div>
                    
                    <div class="form-group">
                        <label>Age</label>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageF" type="number"
                                    class="form-control form-control-sm" placeholder="From">
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageT" type="number" 
                                    class="form-control form-control-sm" placeholder="To">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label></label>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 text-right" >
                                <button class="btn btn-sm btn-primary" (click)="callFilter()" >Search</button>
                            </div>
                            
                        </div>
                    </div>
              
                </div>
            </form>
        </div>
        <footer *ngIf="createTime" id="footer"> These statistics data were taken at {{createTime}} for refresh please
            press the <button class='btn btn-sm btn-primary' (click)='callReset()'>reset</button> button. </footer>
  
    </div>

    <div class="col-lg-9 col-md-9 col-sm-9">
        <div class="inner-content dash-content">
            <div class="d-flex justify-content-center"> <h4>{{this.filterTitle}}</h4></div>
            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="widget">
                        <h6>Age Wise Distribution</h6>
                        <div class="text-center chartDiv">
                            <p-chart type="bar" [data]="ageData" [options]="options" class="chartjs-render-monitor"></p-chart>
                        </div>
                    </div>
                </div> 
                <div class="col-lg-4 col-md-4 col-sm-4">
                    <div class="widget">
                        <h6>Braden Scores</h6>
                        <p-chart type="pie" [data]="bradenScoresData" [options]="pieOption"></p-chart>
                    </div>
                </div>
            </div>
            <div class="row">
             
                <div class="col-lg-4 col-md-4 col-sm-4">
                    <div class="widget">
                        <h6>Random Blood Sugar</h6>
                        <p-chart type="pie" [data]="rBSData" [options]="pieOption"></p-chart>
                    </div>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-4">
                    <div class="widget">
                        <h6>Depression Score</h6>
                        <p-chart type="pie" [data]="depressionScoreData" [options]="pieOption"></p-chart>
                    </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-4">
                    <div class="widget">
                        <h6>Patients with HypertTension( Abnormal value of BP)</h6>
                        <p-chart type="pie" [data]="HypertTensionData" [options]="pieOption"></p-chart>
                    </div>
                </div>
            </div>

        </div>
    </div>


</div>