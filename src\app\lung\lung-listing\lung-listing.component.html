<h6 class="page-title">Lung Listing</h6>

<div class="content-wrapper mb-2">
  <form [formGroup]="lungSearchForm">
    <div class="row">
      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Civil Id</label>
          <input type="number" class="form-control form-control-sm" formControlName="civilId" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Registration No</label>
          <input type="number" class="form-control form-control-sm" formControlName="regNo" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Date of Birth</label>
          <p-calendar dateFormat="dd-mm-yy" formControlName="dob" monthNavigator="true" yearNavigator="true"
            placeholder="dd-mm-yyyy" yearRange="1930:2030" showButtonBar="true"></p-calendar>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Age (from)</label>
          <input type="number" class="form-control form-control-sm" formControlName="ageFrom" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>To</label>
          <input type="number" class="form-control form-control-sm" formControlName="ageTo" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Region</label>
          <ng-select [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
            bindValue="regCode" formControlName="regCode" (change)="onRegionSelect($event)">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Wilayat</label>
          <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="walName"
            bindValue="walCode" formControlName="walCode" (change)="onWilayatSelect($event)">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.walName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Cause of Lung Disease</label>
          <ng-select [items]="icdLungList" [virtualScroll]="true" placeholder="Select" bindLabel="codeDisease"
            bindValue="code" formControlName="causeLungDisease">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.codeDisease }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Transplant</label>
          <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="transplantYn">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Readiness of Transplant</label>
          <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="transplantReadiness">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Transplant Urgent</label>
          <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="transplantUrgent">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Indication Details</label>
          <ng-select [items]="lungTransplantMasterList" [virtualScroll]="true" placeholder="Select" bindLabel="paramName"
            bindValue="paramId" formControlName="transIndication">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-4 col-md-3 col-sm-3">
        <div class="form-group">
          <label><strong>Blood Group</strong></label><br />
          <div class="chexkBoxList">
            <ng-container *ngFor="let bloodGroup of bloodGroupOptions">
              <input type="checkbox" [value]="bloodGroup.value" (change)="onBloodGroupSelect($event)" /> 
              {{ bloodGroup.value }}
            </ng-container>
          </div>
        </div>
      </div>
      
      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <div class="btn-box">
          <button type="button" (click)="exportToExcel()" class="btn btn-primary ripple">EXCEL</button>
          <button type="reset" (click)="clearForm()" class="btn btn-sm btn-secondary">Clear</button>
          <button type="submit" (click)="onSearch()" class="btn btn-sm btn-primary">Search</button>
        </div>
      </div>
    </div>
  </form>
</div>

<div class="content-wrapper mt-2 grid-container">
  <ag-grid-angular class="ag-theme-balham lung-grid" [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>

  <div *ngIf="rowData && rowData.length > 0">
    <p-paginator #paginator [rows]="paginationSize" [totalRecords]="totalRecords"
      (onPageChange)="onSearch($event)" showCurrentPageReport="true"
      currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
      [rowsPerPageOptions]="[10, 20, 30]">
    </p-paginator>
  </div>
</div>
