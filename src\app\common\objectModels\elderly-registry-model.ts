import { Data } from '@angular/router';
import { TbPatientInfo } from 'src/app/_models/patient-info.model';
import { Diagnosis } from './diagnosis-model';
import { TbEldExamTrans } from './eld-exam-model';
import { TbVitalSigns } from './vital-signs-model';


export class ElderlyRegistryFrom {

    public centralRegNo: number;
    public activeYn: string;
    public civilId:number;
    public completedOn: any;
    public createdBy: number;
    public createdOn: any;
    public instRegDate: any;
    public modifiedBy: number;
    public modifiedOn: any;
    public regInst: number;
    public registerType: number;
    public localRegReferance: string;
    public rgTbPatientInfo: TbPatientInfo;
    public rgTbVitalSigns: Array<TbVitalSigns>;
    public rgTbEldExamTrans: Array<TbEldExamTrans>;
}