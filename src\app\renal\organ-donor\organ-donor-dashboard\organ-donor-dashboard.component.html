<h6 class="page-title"></h6>
<div class="row">
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <form [formGroup]="boardForm">
                <div>
                    <div class="form-group">
                        <label>Nationality</label>
                        <ng-select #entryPoint [items]="natData" [virtualScroll]="true" placeholder="Select"
                            bindLabel="nationality" bindValue="natCode" formControlName="natCode"
                            (change)=" locSelect($event,'nationality')" [(ngModel)]="natCodeSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.nationality}}
                            </ng-template>
                        </ng-select>
                    </div>
                    <div class="form-group">
                        <label>Sex</label>
                        <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                            bindLabel="value" bindValue="id" formControlName="sex" (change)="locSelect($event,'sex')"
                            [(ngModel)]="sexSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                            </ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Status</label>
                        <ng-select #entryPoint [items]="organStatus" [virtualScroll]="true" placeholder="Select"
                            bindLabel="value" bindValue="id" formControlName="afterDeath"
                            (change)="locSelect($event,'afterDeath')" [(ngModel)]="afterDeathSelected">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                            </ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Age</label>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageF" type="number" class="form-control form-control-sm"
                                    placeholder="From">
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageT" type="number" onchange="validate()"
                                    class="form-control form-control-sm" [ngClass]="{'err' : ageValidate}"
                                    placeholder="To">
                                <small class="errText" *ngIf="ageValidate"> To Age must be greater than From Age
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label></label>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 text-right" >
                                <button class="btn btn-sm btn-primary" (click)="callFilter()">Search</button>
                            </div>

                        </div>
                    </div>
                </div>
            </form>



            <div class="container-fluid" >
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="tile" [formGroup]="organDonorForm">
                            <div class="wrapper" formGroupName="totalFormGroup">
                                <div class="header">Donation (Will) Status</div>
                                <div class="stats">
                                    <div>
                                        <strong>Total</strong> <input type="total" formControlName="total"
                                            class="orgon-details" disabled>
                                    </div>
                                    <div *ngIf="!natCodeSelected">
                                        <strong>Omani</strong> <input type="total" formControlName="omani"
                                            class="orgon-details" disabled>
                                    </div>
                                    <div *ngIf="!natCodeSelected">
                                        <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                            class="orgon-details" disabled>
                                    </div>
                                </div>

                                <div class="stats">
                                    <div *ngIf="!sexSelected">
                                        <strong>Male</strong> <input type="total" formControlName="male"
                                            class="orgon-details" disabled>
                                    </div>
                                    <div *ngIf="!sexSelected">
                                        <strong>Female</strong> <input type="total" formControlName="female"
                                            class="orgon-details" disabled>
                                    </div>
                                  
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>




        </div>
    </div>
    <div class="col-lg-10 col-md-10 col-sm-10" [formGroup]="organDonorForm">
        <div class="inner-content dash-content">
            <div class="row">


                <div class="container-fluid" >
                    <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                            <div class="tile">
                                <div class="wrapper" formGroupName="kidneyFormGroup">
                                    <div class="header">Kidney</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/kidney.png" alt="Image 1">
                                    </div>

                                    
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>Female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                      
                                    </div>

                                </div>
                            </div>
                        </div>

             
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                            <div class="tile">
                                <div class="wrapper" formGroupName="pancreasFormGroup">
                                    <div class="header">Pancreas</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/pancreas.png" alt="Image 1">
                                    </div>
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>Female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                   
                                    </div>

                                </div>
                            </div>
                        </div>


                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                            <div class="tile">
                                <div class="wrapper" formGroupName="lungsFormGroup">
                                    <div class="header">Lungs</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/lungs.png" alt="Image 1">
                                    </div>
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>Female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                     
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>







                <div class="container-fluid" >
                    <div class="row">
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                            <div class="tile">
                                <div class="wrapper" formGroupName="heartFormGroup">
                                    <div class="header">Heart</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/heart.png" alt="Image 1">
                                    </div>
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>Female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                      
                                    </div>

                                </div>
                            </div>
                        </div>

             
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                            <div class="tile">
                                <div class="wrapper" formGroupName="liverFormGroup">
                                    <div class="header">Liver</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/liver.png" alt="Image 1">
                                    </div>
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>Female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                     
                                    </div>

                                </div>
                            </div>
                        </div>


                        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                            <div class="tile">
                                <div class="wrapper" formGroupName="corneatFormGroup">
                                    <div class="header">Cornea</div>

                                    <div class="banner-img">
                                        <img class="img-card" src="assets/img/cornea.png" alt="Image 1">
                                    </div>
                                    <div class="stats">
                                        <div>
                                            <strong>Total</strong> <input type="total" formControlName="total"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>Omani</strong> <input type="total" formControlName="omani"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!natCodeSelected">
                                            <strong>NON-Omani</strong> <input type="total" formControlName="nonOmani"
                                                class="orgon-details" disabled>
                                        </div>
                                    </div>

                                    <div class="stats">
                                        <div *ngIf="!sexSelected">
                                            <strong>Male</strong> <input type="total" formControlName="male"
                                                class="orgon-details" disabled>
                                        </div>
                                        <div *ngIf="!sexSelected">
                                            <strong>female</strong> <input type="total" formControlName="female"
                                                class="orgon-details" disabled>
                                        </div>
                                       
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>







            </div>
        </div>
    </div>

</div>