<h6 class="page-title">Child Listing </h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="childListingForm">
        <div class="row">
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Registration No.</label>
                    <input type="number" class="form-control form-control-sm" formControlName="central_RegNo">
                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Civil ID </label>
                    <input type="number" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Patient ID </label>
                    <input type="number" class="form-control form-control-sm" formControlName="patientId">
                </div>
            </div>

            
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Child Id </label>
                    <input type="number" class="form-control form-control-sm" formControlName="child_Id">
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Full Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="fullName">
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>Age From</label>
                            <input type="number" class="form-control form-control-sm" formControlName="ageFrom">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>Age To</label>
                            <input type="number" class="form-control form-control-sm" formControlName="ageTo">
                        </div>
                    </div>
                </div>
            </div> 
          </div>
            <div class="row">
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true" placeholder="Select"
                    bindLabel="value" bindValue="id" formControlName="sex">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                    </ng-template>
                </ng-select>
                    <!-- <ng-select #entryPoint appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true"
                        placeholder="Select" bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                        </ng-template>
                    </ng-select> -->
                </div>
            </div>
   
            
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality">
                        <option selected [value]="null">Select </option>
                        <option [value]="res.natCode" *ngFor="let res of nationalityList">{{res.nationality}}</option>
                    </select>
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Institutes </label>
                    <ng-select [items]="childinstitutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="reg_Inst"
                        (change)="changeAncInstitute($event)">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-xl-2">
                <label>Registration Date</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="reg_DateFrom" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="From"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="reg_DateTo" monthNavigator="true"
                                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To"
                                yearRange="1930:2030">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Governorate</label>
                    <ng-select appendTo="body" [items]="regions" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="regSelect($event,'regCode')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>


            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="wal_Code"
                        (change)="walSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
        

            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>APGAR_1 </label>
                    <input type="number" class="form-control form-control-sm" formControlName="apgar_1">
                </div>
            </div>

            
            <div class="col-lg-3 col-xl-2">
                <div class="form-group">
                    <label>APGAR_5 </label>
                    <input type="number" class="form-control form-control-sm" formControlName="apgar_5">
                </div>
            </div>


          
                <div class="col-lg-8">
                    <h6 class="box-title">Lab Result Info: </h6>   
                    <div class="border-box">                        
                                <div class="chkbx-list"> <input type="checkbox" formControlName="sickle_Cell_Yn"  name="sickle_Cell_Yn" >Sickle Cell</div>
                                
                                <div class="chkbx-list"> <input type="checkbox" formControlName="thalassmia_Yn" name="thalassmia_Yn" >Thalassmia</div>
                                
                                <div class="chkbx-list"> <input type="checkbox" formControlName="congenital_Anamoly_Yn" name="congenital_Anamoly_Yn">Congenital Anamoly</div>
                                
                                <div class="chkbx-list"> <input type="checkbox" formControlName="cord_Tsh_Yn" name="cord_Tsh_Yn" >Cord Tsh</div>
                                
                                <div class="chkbx-list"> <input type="checkbox" formControlName="sickle_Cell_Yn" name="g6PD" >G6PD</div>
                </div>
                </div>
            <div class="text-right col-sm-12">
                <div class="btn-box">
                    <button type="button" (click)="exportExcel()" class="btn btn-primary">EXCEL</button>
                    <button type="button" (click)="clear()" class="btn btn-sm btn-sm btn-secondary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-primary">Search</button>
                </div>
            </div>
        </div>

    </form>

    <div class="grid-panel">
        <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
            [columnDefs]="columnDefs" [gridOptions]="gridOptions">
        </ag-grid-angular>

    </div>