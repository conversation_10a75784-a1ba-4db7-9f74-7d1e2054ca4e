import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { CorneaService } from '../cornea.service';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { WallayatDataModel } from 'src/app/common/objectModels/wallayat-model';
import { CorneaDashboard, CorneaDashboardDisplay } from 'src/app/_models/cornea-dashboard.model';
import * as AppComponent from '../../common/app.component-utils';
import * as CommonConstants from '../../_helpers/common.constants';
@Component({
  selector: 'app-cornea-dashboard',
  templateUrl: './cornea-dashboard.component.html',
  styleUrls: ['./cornea-dashboard.component.scss'],
  providers: [CorneaService]
})
export class CorneaDashboardComponent implements OnInit {

  boardForm: FormGroup;

  regionData: RegionDataModel[] = [];
  wallayatList: WallayatDataModel[] = [];
  wallayatListFilter: WallayatDataModel[] = [];
  instituteList: any[] = [];
  instituteListFilter: any[] = [];

  dashboardDataDB: CorneaDashboard[] = [];
  dashboardDataFilter: CorneaDashboard[] = [];

  today = new Date();
  createTime: string;

  filterType: string = 'all';
  filterTitle: string = 'All Regions';

  pieOption: any;
  options: any;
  chartBGColor: string[] = [];

  indicationChart: any;
  diagnosisPieChart: any;
  tissuePieChart: any;
  instituteChart: any;

  corIndicationTypeFilter: any[] = [];
  corPrimaryDiagFilter: any[] = [];
  corTissueTypeFilter: any[] = [];
  constructor(
    private _masterService: MasterService,
    private _sharedService: SharedService,
    private _corneaService: CorneaService
  ) {
    this.initializeForm();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.chartBGColor = AppComponent.CHAR_BG_COLOR;

    this.chartBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit(): void {
  }

  private initializeForm(): void {
    this.boardForm = new FormGroup({
      regCode: new FormControl(null),
      walCode: new FormControl(null),
      estCode: new FormControl(null),
      reqDateF: new FormControl(null),
      reqDateT: new FormControl(null),
    });
  }

  private getMasterData(regCode: number = 0, walCode: number = 0): void {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = [...this.wallayatList];
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.instituteList = response.result;
      this.instituteListFilter = [...this.instituteList];
    });

    this._masterService.getAllCorIndicationType().subscribe(
      response => this.corIndicationTypeFilter = response.result
    );

    this._masterService.getAllCorPrimaryDiag().subscribe(
      response => this.corPrimaryDiagFilter = response.result
    );

    this._masterService.getAllCorTissueType().subscribe(
      response => this.corTissueTypeFilter = response.result
    );
  }

  locSelect(event: any, field?: string): void {
    const body = this.boardForm.value;

    if (field === "region") {
      this.handleRegionSelection(event, body);
    } else if (field === "wilayat") {
      this.handleWilayatSelection(event, body);
    }
  }

  private handleRegionSelection(event: any, body: any): void {
    if (!event) {
      this.wallayatListFilter = [...this.wallayatList];
      this.instituteListFilter = [...this.instituteList];
      body.walCode = null;
      body.estCode = null;
    } else {
      this.wallayatListFilter = this.wallayatList.filter(s => s.regCode === event.regCode);
      this.instituteListFilter = this.instituteList.filter(s => s.regCode === event.regCode);
    }
  }

  private handleWilayatSelection(event: any, body: any): void {
    if (!event) {
      this.instituteListFilter = body.regCode
        ? this.instituteList.filter(s => s.regCode === body.regCode)
        : [...this.instituteList];
      body.estCode = null;
    } else {
      this.instituteListFilter = this.instituteList.filter(s => s.walCode === event.walCode);
    }
  }

  callReset(): void {
    window.location.reload();
  }

  callFilter(): void {
    if (!this.dashboardDataDB || this.dashboardDataDB.length === 0) {
      this.loadDashboardData();
    } else {
      this.applyFilters();
    }
  }

  private loadDashboardData(): void {
    this.createTime = this._sharedService.setDateFormatDataTime(new Date());

    this._corneaService.getDashboard().subscribe(res => {
      if (res.code === "S0000" || res.code === "F0000") {
        this.dashboardDataDB = this._sharedService.filterDataByUserRole(
          res.result,
          'CORENEA',
          this.instituteList
        );
      }
      this.applyFilters();
    });
  }

  private applyFilters(): void {
    const formValue = this.boardForm.value;
    this.dashboardDataFilter = [...this.dashboardDataDB];

    this.applyDateFilters(formValue);
    this.applyLocationFilters(formValue);

    this.displayFilterType();
    this.callChart();
  }

  private applyDateFilters(formValue: any): void {
    const { reqDateF, reqDateT } = formValue;

    if (!reqDateF && !reqDateT) return;

    this.dashboardDataFilter = this.dashboardDataDB.filter(item => {
      if (!item.reqDate) return false;

      const recordDate = new Date(item.reqDate);

      if (reqDateF && reqDateT) {
        const fromDate = new Date(reqDateF);
        const toDate = new Date(reqDateT);
        fromDate.setHours(0, 0, 0, 0);
        toDate.setHours(23, 59, 59, 999);
        return recordDate >= fromDate && recordDate <= toDate;
      }

      if (reqDateF) {
        const fromDate = new Date(reqDateF);
        fromDate.setHours(0, 0, 0, 0);
        return recordDate >= fromDate;
      }

      if (reqDateT) {
        const toDate = new Date(reqDateT);
        toDate.setHours(23, 59, 59, 999);
        return recordDate <= toDate;
      }

      return true;
    });
  }

  private applyLocationFilters(formValue: any): void {
    const { estCode, walCode, regCode } = formValue;

    if (estCode) {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => s.estCode === estCode);
      this.filterType = 'institute';
    } else if (walCode) {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => s.walCode === walCode);
      this.filterType = 'wilayat';
    } else if (regCode) {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => s.regCode === regCode);
      this.filterType = 'region';
    } else {
      this.filterType = 'all';
    }
  }

  private isEmptyNullZero(val: any): boolean {
    return val !== undefined && val !== null && val !== "null" && val > 0;
  }

  private displayFilterType(): void {
    const formValue = this.boardForm.value;

    switch (this.filterType) {
      case 'institute':
        const institute = this.instituteList.find(s => s.estCode === formValue.estCode);
        this.filterTitle = institute ? institute.estName : 'Selected Institute';
        break;

      case 'wilayat':
        const wilayat = this.wallayatList.find(s => s.walCode === formValue.walCode);
        this.filterTitle = wilayat ? `All Institutes under ${wilayat.walName}` : 'Selected Wilayat';
        break;

      case 'region':
        const region = this.regionData.find(s => s.regCode === formValue.regCode);
        this.filterTitle = region ? `All Wilayat under ${region.regName}` : 'Selected Region';
        break;

      default:
        this.filterTitle = 'All Regions';
    }
  }

  private callChart(): void {
    const indicationData = this.prepareIndicationData();
    const primaryDiagData = this.preparePrimaryDiagData();
    const tissueData = this.prepareTissueData();
    const instituteData = this.prepareInstituteData();

    this.generateBarChart(indicationData, 'indicationChart', true);
    this.generatePieChart(primaryDiagData, 'diagnosisPieChart');
    this.generatePieChart(tissueData, 'tissuePieChart');
    this.generateBarChart(instituteData, 'instituteChart', true);
  }

  private prepareIndicationData(): CorneaDashboardDisplay[] {
    return this.dashboardDataFilter
      .filter(item => item.indication !== null && item.indication !== undefined && item.indication !== 0)
      .map(item => {
        let indicationName = item.indication;

        if (this.corIndicationTypeFilter && this.corIndicationTypeFilter.length > 0) {
          const indicationItem = this.corIndicationTypeFilter.find(
            data => data.paramId == item.indication || data.paramName == item.indication
          );
          indicationName = indicationItem ? indicationItem.paramName : item.indication;
        }

        return {
          requestId: item.requestId,
          value: String(indicationName)
        };
      })
      .filter(item => item.value !== 'null' && item.value !== 'undefined' && item.value !== '0');
  }

  private preparePrimaryDiagData(): CorneaDashboardDisplay[] {
    return this.dashboardDataFilter
      .filter(item => item.primaryDiag !== null && item.primaryDiag !== undefined && item.primaryDiag !== '')
      .map(item => {
        let primaryDiagName = item.primaryDiag;

        if (this.corPrimaryDiagFilter && this.corPrimaryDiagFilter.length > 0) {
          const primaryDiagItem = this.corPrimaryDiagFilter.find(
            data => data.paramId == item.primaryDiag || data.paramName == item.primaryDiag
          );
          primaryDiagName = primaryDiagItem ? primaryDiagItem.paramName : item.primaryDiag;
        }

        return {
          requestId: item.requestId,
          value: String(primaryDiagName)
        };
      })
      .filter(item => item.value !== 'null' && item.value !== 'undefined' && item.value !== '');
  }

  private prepareTissueData(): CorneaDashboardDisplay[] {
    return this.dashboardDataFilter
      .filter(item => item.tissueId !== null && item.tissueId !== undefined && item.tissueId !== 0)
      .map(item => {
        let tissueName = item.tissueId;

        if (this.corTissueTypeFilter && this.corTissueTypeFilter.length > 0) {
          const tissueItem = this.corTissueTypeFilter.find(
            data => data.paramId == item.tissueId || data.paramName == item.tissueId
          );
          tissueName = tissueItem ? tissueItem.paramName : item.tissueId;
        }

        return {
          requestId: item.requestId,
          value: String(tissueName)
        };
      })
      .filter(item => item.value !== 'null' && item.value !== 'undefined' && item.value !== '0');
  }

  private prepareInstituteData(): CorneaDashboardDisplay[] {
    return this.dashboardDataFilter
      .filter(item => item.estCode !== null && item.estCode !== undefined && item.estCode !== 0)
      .map(item => {
        const instituteItem = this.instituteList.find(data => data.estCode === item.estCode);
        const instituteName = instituteItem ? instituteItem.estName : item.estCode;

        return {
          requestId: item.requestId,
          value: String(instituteName)
        };
      })
      .filter(item => item.value !== 'null' && item.value !== 'undefined' && item.value !== '0');
  }

  private generateBarChart(listData: CorneaDashboardDisplay[], chartData: string, withNull: boolean): void {
    const { labels, values } = this.processChartData(listData, withNull);

    if (chartData === "indicationChart") {
      this.indicationChart = {
        labels,
        datasets: [{
          backgroundColor: this.chartBGColor,
          borderColor: '#1E88E5',
          data: values,
        }]
      };
    }
    else if (chartData === "instituteChart") {
      this.instituteChart = {
        labels,
        datasets: [{
          backgroundColor: this.chartBGColor,
          borderColor: '#1E88E5',
          data: values,
        }]
      };
    }
  }

  private generatePieChart(listData: CorneaDashboardDisplay[], chartData: string): void {
    const { labels, values } = this.processChartData(listData, false);

    this.chartBGColor.sort(() => Math.random() - 0.2);

    const chartConfig = {
      labels,
      datasets: [{
        backgroundColor: this.chartBGColor,
        hoverBackgroundColor: this.chartBGColor,
        data: values,
      }]
    };

    if (chartData === 'diagnosisPieChart') {
      this.diagnosisPieChart = chartConfig;
    } else if (chartData === 'tissuePieChart') {
      this.tissuePieChart = chartConfig;
    }
  }

  private processChartData(listData: CorneaDashboardDisplay[], withNull: boolean): { labels: string[], values: number[] } {
    const filteredData = withNull ? listData : listData.filter(s => s.value != null);
    const uniqueValues = new Map<string, number>();

    filteredData.forEach(item => {
      const valueStr = String(item.value);
      const currentCount = uniqueValues.get(valueStr) || 0;
      uniqueValues.set(valueStr, currentCount + 1);
    });

    return {
      labels: Array.from(uniqueValues.keys()),
      values: Array.from(uniqueValues.values())
    };
  }

  onFromDateSelect(event: any): void {
    const fromDate = event;
    const toDate = this.boardForm.get('reqDateT').value;
    if (toDate && new Date(toDate) < new Date(fromDate)) {
      this.boardForm.get('reqDateT').setValue(null);
    }
  }
}
