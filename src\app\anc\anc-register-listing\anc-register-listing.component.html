<h6>ANC Register Listing</h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="ancRegisterSearch">
        <div class="row">

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil ID</label>
                    <input type="text" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>First Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="firstName">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Second Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="secondName">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Third Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="thirdName">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Tribe</label>
                    <input type="text" class="form-control form-control-sm" formControlName="tribe">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Mobile No</label>
                    <input type="text" class="form-control form-control-sm" formControlName="mobileNo">
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age From</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ageFrom">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age To</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ageTo">
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>ANC NO</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ancNo">
                </div>
            </div>

      
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label> ANC Institute</label>
                    <!-- #entryPoint -->
                    <ng-select  [items]="institutes" [virtualScroll]="true"
                    placeholder="Select" bindLabel="estName" bindValue="estCode"
                    formControlName="ancInstitute">
                    <ng-template ng-option-tmp let-item="item" let-index="index">
                        {{item.estName}}
                    </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Requested Institute</label>
                    <ng-select  [items]="institutes" [virtualScroll]="true"
                    placeholder="Select" bindLabel="estName" bindValue="estCode"
                    formControlName="requestInst">
                    <ng-template ng-option-tmp let-item="item" let-index="index">
                        {{item.estName}}</ng-template>
                    </ng-select>

                    
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint appendTo="body" [items]="regions" (change)="changeRegion($event)"
                    [virtualScroll]="true" placeholder="Select" bindLabel="regName" bindValue="regCode"
                    formControlName="regCode">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}
                    </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint appendTo="body" [items]="wallayatsFilter" [virtualScroll]="true" placeholder="Select"
                       bindLabel="walName" bindValue="walCode" formControlName="walCode" (change)="changeWalayat($event)">
                         <ng-option *ngFor="let c of wallayatsFilter" [value]="c">{{ c.walName }}</ng-option>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Village</label>
                    <ng-select #entryPoint appendTo="body" [items]="villagesFilter" [virtualScroll]="true" placeholder="Select"
                 bindLabel="vilName" bindValue="vilCode" formControlName="village">

                <ng-option *ngFor="let c of villagesFilter" [value]="c.vilCode">{{ c.vilName }}</ng-option>
            </ng-select>
                </div>
            </div>


            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="submit" class="btn btn-primary ripple" (click)="exportToExcel()" >Export Excel</button>
                     <button type="submit" class="btn btn-sm btn-primary" (click)="getSearchDetailesList()">Search</button>
                    <button type="reset" class="btn btn-sm btn-primary" (click)="clear();">Clear</button>
                </div>
            </div>
        </div>
    </form>
</div>

<div style="margin-top:20px">
    <div class="grid-container">
        <div class="grid-item">
            <ag-grid-angular style="width: 100%; height: 500px;" class="ag-theme-balham" [rowData]="rowData" 
                [gridOptions]="gridOptions" [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)">
            </ag-grid-angular>

        </div>
        <div class="grid-item">
            <!-- (onPageChange)="getOrganDonorsList($event)"  -->
            <p-paginator *ngIf="rowData.length > 0" #ancRequestListinPaginator rows="10"
                totalRecords="{{totalRecords}}" showCurrentPageReport="true" (onPageChange)="getSearchDetailesList($event)"
                currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
                [rowsPerPageOptions]="[10, 20, 30]">
            </p-paginator>
        </div>
    </div>
</div>
