

 export const GRID_SPECIFICATIONS : Array<Object> = [
     {
        "gridName":"waitingListGrid", 
        "gridApi":"waitingListGridApi",
        "gridColumnApi":"waitingListGridColumnApi",
        "model":"waitingList", 
        "colObject":{
           "runId": null,
           "noPrevTransplant": null,
           "readyTransplantYn": null,
           "readyTransplantYnValue": null,
           "transplantDelayReason": null,
           "transplantDelayReasonValue": null,
           "transplantReasonReamarks": null,
           "transplantUrgentYn": null,
           "transplantUrgentYnValue": null,
           "previousOrganDonor": null,
           "previousOrganDonorValue": null,
           "enteredBy": null,
           "entryDate": null,
           "modifiedDate": null,
           "modifiedBy": null,
           "rgTbRegistryPatient": {"centralRegNo":null}
       }
     } ,
     {
        "gridName":"comorbidDiseaseListGrid", 
        "gridApi":"comorbidDiseaseGridApi",
        "gridColumnApi":"comorbidDiseaseGridColApi",
        "model":"omordidDiseaseList", 
        "colObject":{
           "centralRegNo": null,
           "entryDate": null,
           "enteredBy": null,
           "ICD": null,
           "icdValue": null,
           "icdFlag": null,
           "runId": null,
           "remarks": null
       }
     }
 ]

/*
 "noPrevTransplant": null,
 "readyTransplantYn": null,
 "transplantDelayReason": null,
 "transplantReasonReamarks": null,
 "transplantUrgentYn": null,
 "previousOrganDonor": null,

 */