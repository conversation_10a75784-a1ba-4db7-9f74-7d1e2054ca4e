import { Component, OnInit, ViewChild, Input, Output } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { FormArray, Validators, FormControl } from '@angular/forms';
import { FormGroup, FormBuilder } from '@angular/forms';
import { MasterService } from '../../_services/master.service';
import { PatientInfo } from '../../_models/patient.model';
import * as AppUtils from '../../common/app.utils';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import { SharedService } from '../../_services/shared.service';
import Swal from 'sweetalert2';
import { Row } from 'primeng/primeng';
import { analyzeAndValidateNgModules } from '@angular/compiler';
import { stagesTransplantMode } from '../../common/objectModels/stages-transplant-model';
import { LabResultsComponent } from '../../_comments/lab-result-listing/lab-result-listing.component';
import { VaccinationComponent } from '../../_comments/vaccination/vaccination.component';
import { NgbModal, ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { NgIf } from '@angular/common';
import { Router } from '@angular/router'
import { RenalScores } from 'src/app/common/objectModels/renal-scores-model';
import { RenalService } from '../renal.service';
import * as CommonConstants from '../../_helpers/common.constants';
import { combineAll } from 'rxjs/operators';
import * as moment from 'moment';


@Component({
  selector: 'app-kidney-disease-stages',
  templateUrl: './kidney-disease-stages.component.html',
  styleUrls: ['./kidney-disease-stages.component.scss']
})
export class KidneyDiseaseStagesComponent implements OnInit {

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  // @ViewChild('LabResults', { static: false }) LabResults: LabResultsComponent;
  @ViewChild('Vaccination', { static: false }) Vaccination: VaccinationComponent;

  @Input() caseDetailsForm: FormGroup; patientForm: FormGroup;
  @Input() filterModelForm: FormGroup;
  @Input() submitted = false;
  public labTestForm: FormGroup;
  value: Date;
  dialysis = 'N';
  stage: any;
  transplant = 'N';
  readiness = 'N';
  willingness = 'N';
  preEmptiveTransplant;
  dialysisType;
  selectedTransplantPlace: any;
  selectedfollowUpHospital: any;
  patientInfo: PatientInfo;
  // submitted = false;
  rowdata: stagesTransplantMode;
  nationListFilter: any;
  nationList: any;
  disVaccineInfoalshifa: any;
  vaccinMastList: any;
  //////////////////////
  //tmpCivilId: number = 4444;
  //centralRegNo: number = 33;
  //////////////////////
  vacList: any[];
  vaccNewList: any[];
  vaccName: any[];
  delRow;
  today = new Date();
  institutes: any[];
  labTest: any;
  labList: any[];
  labnewList: any[];
  labListToFixRowSpan: any[];
  labTestName: any[];
  testDone: any[];
  testListToDownload: any[];
  disLabFollowUpInfoalshifa: any;
  testcomponet: any;
  testComponetList: any;
  profileList: any;
  ComponetList: any;
  dropdownSettings: IDropdownSettings;
  centralRegNoExit: boolean = false;
  newtestList: any = [];

  regId: any;
  hospitals: any;
  dialysisCenters: any;
  currentCivilId = '';

  loginId: any;
  rgTbLabTestInfo: any = [];
  labTestFg: any = [];

  newtestDate: any = [];
  est_Code: any = [];
  transfusedDate: any;
  transfusedInst: any;


  constructor(private modalService: NgbModal, private fb: FormBuilder, private _masterService: MasterService, private _http: HttpClient, private formBuilder: FormBuilder,
    private _sharedService: SharedService, private _router: Router, private _renalService: RenalService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

    this.getMasterData();
    this.initializeFormGroup();

    if (this._sharedService.getNavigationData()) {

      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);


    }

    this.getNationalityList();



  }

  ngOnInit() {

    this.getMasterData();


    this.dropdownSettings = {
      singleSelection: false,
      idField: 'mohTestCode',
      textField: 'testName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };

  }


  ngAfterViewInit() {
    //  this.patientDetails.patientForm.disable();
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }

  private getMasterData() {
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    }),

      this._masterService.institiutes.subscribe(res => {
        this.institutes = res["result"];
      })

    this._masterService.getLabTestList().subscribe(res => {
      this.labTest = res.result;
    })

    this._masterService.getLabMaster().subscribe(res => {
      this.testDone = res.result;

    })
    this._masterService.getLabTestToDownload().subscribe(res => {
      this.testListToDownload = res["result"];
    })
    this._masterService.getDialysisHospital().subscribe(res => {
      this.hospitals = res["result"];
    })

    this._masterService.getDialysisCenters().subscribe(res => {
      this.dialysisCenters = res["result"];
    })




    //GET_ALL_MOH_LAB_MASTER

    this._masterService.getAllMohLabMaster().subscribe(res => {
      this.testComponetList = res.result;
      this.profileList = [];
      this.ComponetList = [];
      this.testComponetList.forEach(element => {
        if (this.profileList.filter(s => s.mohTestCode == element.mohTestCode).length == 0) {
          this.profileList.push({ testId: element.mohTestCode, testName: element.testName });
        }

        if (this.ComponetList.filter(s => s.mohTestCode == element.mohTestCode).length == 0) {
          this.ComponetList.push({ componentTestId: element.mohTestCode, componentTestName: element.testName });
        }
      })

      // this._masterService.getAllTestComponent().subscribe(res => {
      //   this.testComponetList = res.result;
      //   this.profileList = [];
      //   this.ComponetList = [];
      //   this.testComponetList.forEach(element => {
      //     if (this.profileList.filter(s => s.testId == element.testId).length == 0) {
      //       this.profileList.push({ testId: element.testId, testName: element.testName });
      //     }

      //     if (this.ComponetList.filter(s => s.componentTestId == element.componentTestId).length == 0) {
      //       this.ComponetList.push({ componentTestId: element.componentTestId, componentTestName: element.componentTestName });
      //     }
      //   });



    })

  }
  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {

      this.nationList = response.result;
      // console.log(this.nationList, "nationlist");
      this.nationListFilter = this.nationList;
    }, error => {

    });
  }

  private initializeFormGroup() {
    this.caseDetailsForm = this.fb.group({
      'rgTbRegistryPatient': this.fb.array([]),

      rgTbCkdStageTransplant: this.fb.group({
        'runId': [null],
        'ckdFailureStage': [null],
        'dialysisYn': [null],
        'transplantYn': [null],
        'preEmptiveYn': [null],
        'transplantDatr': [null],
        'transplantInst': [null],
        'transplantCountry': [null],
        'followupInst': [null],
        'followUpCountry': [null],
        'transplantReadiness': [null],
        'transplantWillingness': [null],
      }),

      // related to diaylsis
      rgTbDialysisInfo: this.fb.group({
        'runId': [null],
        'startDate': [null],
        'dialysisType': [null],
        'dialysisInst': [null],
        'diyAccessAvf': [null],
        'diyAccessAvg': [null],
        'diyAccessPcath': [null],
        'diyAccessPdcath': [null],
      }),

      rgTbBldTransDtls: this.fb.group({
        'runId': [null],
        'transfusedDate': [null],
        'transfusedInst': [null]
      }),



    })

    this.filterModelForm = this.fb.group({
      'fromDate': [null],
      'toDate': [null],
      'profileT': [null]
    })



  }


  placeofTransplant() {
  }
  followUpHospitals() {
  }

  search() {

    if (this.regId) {
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });

    }
  }


  getList(regNo: any) {

    // if (!this.caseDetailsForm == undefined && this.caseDetailsForm.status == 'VALID') {
    this.clear();
    // }

    //GET_RENAL_CASE_DETAILS
    this._renalService.getCaseDetails(regNo).subscribe(res => {

      if (res['code'] == "S0000") {
        this.centralRegNoExit = true;
        this.patientDetails.setPatientDetails(res['result']);
        this.currentCivilId = res['result'].rgTbPatientInfo['civilId'];



        if (res['result'].rgTbCkdStageTransplant && res['result'].rgTbCkdStageTransplant.length > 0) {
          let stag = res['result'].rgTbCkdStageTransplant[0];
          let fgStageTransplant: FormGroup = <FormGroup>this.caseDetailsForm.controls["rgTbCkdStageTransplant"];
          fgStageTransplant.patchValue({
            'runId': stag.runId,
            'ckdFailureStage': stag.ckdFailureStage,
            'dialysisYn': stag.dialysisYn,
            'transplantYn': stag.transplantYn,
            'preEmptiveYn': stag.preEmptiveYn,
            'transplantDatr': new Date(stag.transplantDatr),
            'transplantInst': stag.transplantInst,
            'transplantCountry': stag.transplantCountry,
            'followupInst': stag.followupInst,
            'followUpCountry': stag.followUpCountry,
            'transplantReadiness': stag.transplantReadiness,
            'transplantWillingness': stag.transplantWillingness,
          })



          this.dialysis = stag.dialysisYn;
          this.transplant = stag.transplantYn;

          this.readiness = stag.transplantReadiness;
          this.willingness = stag.transplantWillingness;


          this.preEmptiveTransplant = stag.preEmptiveYn;
          this.stage = stag.ckdFailureStage.toString();

        }



        if (res['result'].rgTbDialysisInfo && res['result'].rgTbDialysisInfo.length > 0) {
          let dial = res['result'].rgTbDialysisInfo[0];
          let fgDialysisInfo: FormGroup = <FormGroup>this.caseDetailsForm.controls["rgTbDialysisInfo"];
          fgDialysisInfo.patchValue({
            'runId': dial.runId,
            'startDate': new Date(dial.startDate),
            'dialysisType': dial.dialysisType,
            'dialysisInst': dial.dialysisInst,
            'diyAccessAvf': dial.diyAccessAvf,
            'diyAccessAvg': dial.diyAccessAvg,
            'diyAccessPcath': dial.diyAccessPcath,
            'diyAccessPdcath': dial.diyAccessPdcath,
          });


          this.dialysisType = dial.dialysisType;
        }



        if (res['result'].rgTbBldTransDtls && res['result'].rgTbBldTransDtls.length > 0) {
          let bldTransDtls = res['result'].rgTbBldTransDtls[0];
          let tranfutionDtl: FormGroup = <FormGroup>this.caseDetailsForm.controls["rgTbBldTransDtls"];
          tranfutionDtl.patchValue({
            'runId': bldTransDtls.runId,
            'transfusedInst': bldTransDtls.transfusedInst,
            'transfusedDate': new Date(bldTransDtls.transfusedDate),
          })
        }


        let DialysisInfoData = this.caseDetailsForm.value.rgTbDialysisInfo;








        for (let labRList of res['result'].rgTbLabTests) {

          let componentTestName = this.testComponetList.filter(s => s.mohTestCode == labRList.mohTestCode).map(s => s.testName)[0].toString();
          let instName = this.institutes.filter(s => s.estCode == labRList.instCode).map(s => s.estName)[0];
          let profileTestName = this.testComponetList.filter(s => s.mohTestCode == labRList.profileTestCode).map(s => s.testName)[0].toString();


          this.addNewLabList(labRList.runId, this._sharedService.setDateFormat(labRList.testDate), this._sharedService.setDateFormat(labRList.releasedDate), labRList.profileTestCode, profileTestName, labRList.mohTestCode, componentTestName, labRList.value, labRList.unit, labRList.instCode, instName, labRList.enteredBy, labRList.enteredDate, labRList.source, false);
        }

        this.labListToFixRowSpan = res['result'].rgTbLabTests;


        // stag.followupPlaceDesc: null
        // stag.transplantPlaceDesc: null
        // dial.runId: 6





        /////////////// Form group VaccinationInfo
        const rgTbVaccinationInfoDb: any = res['result'].rgTbVaccinationInfo;
        for (let vaccinaList of rgTbVaccinationInfoDb) {
          this.Vaccination.addNewVaccine(vaccinaList.runId, vaccinaList.enteredBy, new Date(vaccinaList.vaccinationDate), vaccinaList.vaccineCode, vaccinaList.vaccinatedInst, vaccinaList.remarks, vaccinaList.civilId, vaccinaList.source, false)
        }




      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })


  }

  saveSatages() {
    // only save if there is central Reg No  
    if (this.patientDetails.f.centralRegNo.value != null) {
      let patientInfoData = this.patientDetails.patientForm.value;

      let StageTransplantData = this.caseDetailsForm.value.rgTbCkdStageTransplant;
      let rgTbCkdStageTransplant = [];
      rgTbCkdStageTransplant.push({
        runId: StageTransplantData.runId,
        ckdFailureStage: StageTransplantData.ckdFailureStage,
        dialysisYn: StageTransplantData.dialysisYn,
        transplantYn: StageTransplantData.transplantYn,
        preEmptiveYn: StageTransplantData.preEmptiveYn,
        transplantDatr: StageTransplantData.transplantDatr,
        transplantInst: StageTransplantData.transplantInst,
        transplantPlaceDesc: StageTransplantData.transplantPlaceDesc,
        followupInst: StageTransplantData.followupInst,
        followupPlaceDesc: StageTransplantData.followupPlaceDesc,
        followUpCountry: StageTransplantData.followUpCountry,
        transplantCountry: StageTransplantData.transplantCountry,
        transplantReadiness: StageTransplantData.transplantReadiness,
        transplantWillingness: StageTransplantData.transplantWillingness,
      })


      let DialysisInfoData = this.caseDetailsForm.value.rgTbDialysisInfo;


      if (DialysisInfoData.diyAccessAvf == true || DialysisInfoData.diyAccessAvf == 'F') {
        DialysisInfoData.diyAccessAvf = 'F';
      } else {
        DialysisInfoData.diyAccessAvf = null;
      }

      if (DialysisInfoData.diyAccessAvg == true || DialysisInfoData.diyAccessAvg == 'G') {
        DialysisInfoData.diyAccessAvg = 'G';
      } else {
        DialysisInfoData.diyAccessAvg = null;
      }

      if (DialysisInfoData.diyAccessPcath == true || DialysisInfoData.diyAccessPcath == 'P') {
        DialysisInfoData.diyAccessPcath = 'P';
      } else {
        DialysisInfoData.diyAccessPcath = null;
      }

      if (DialysisInfoData.diyAccessPdcath == true || DialysisInfoData.diyAccessPdcath == 'D') {
        DialysisInfoData.diyAccessPdcath = 'D';
      } else {
        DialysisInfoData.diyAccessPdcath = null;
      }


      let rgTbDialysisInfo = [];
      rgTbDialysisInfo.push({
        runId: DialysisInfoData.runId,
        startDate: DialysisInfoData.startDate,
        dialysisInst: DialysisInfoData.dialysisInst,
        dialysisType: DialysisInfoData.dialysisType,
        diyAccessAvf: DialysisInfoData.diyAccessAvf,
        diyAccessAvg: DialysisInfoData.diyAccessAvg,
        diyAccessPcath: DialysisInfoData.diyAccessPcath,
        diyAccessPdcath: DialysisInfoData.diyAccessPdcath
      })

      let labTestsData = this.labTestForm.value.rgTbLabTestInfo;

      let vaccinationInfoData = this.Vaccination.vaccinationForm.value.rgTbVaccinationInfo;

      let rgTbBldTransDtls: any[] = [];
      rgTbBldTransDtls.push(this.caseDetailsForm.value.rgTbBldTransDtls);
      if (rgTbBldTransDtls[0] && (rgTbBldTransDtls[0].runId == "" || !rgTbBldTransDtls[0].runId)) rgTbBldTransDtls[0].runId = 0;

      let saveData = {
        "centralRegNo": this.patientDetails.f.centralRegNo.value,
        //"activeYn": "Y",
        patientID: this.patientDetails.f.patientId.value,
        //"completedOn": null,
        //"createdBy": -1001,
        //"createdOn": "2021-06-07",
        //"instRegDate": "20-05-2020",
        //"modifiedBy": -1,
        //"modifiedOn": "2021-08-26",
        "regInst": this.patientDetails.f.regInst.value,
        "registerType": AppUtils.REG_TYPE_RENAL,
        //"localRegReferance": null,
        rgTbPatientInfo: patientInfoData,
        rgTbCkdStageTransplant: rgTbCkdStageTransplant,
        rgTbDialysisInfo: rgTbDialysisInfo,
        rgTbLabTests: labTestsData,
        rgTbVaccinationInfo: vaccinationInfoData,
        rgTbBldTransDtls: rgTbBldTransDtls

      }

      // let rgTbBldTransDtls:any = {};
      // rgTbBldTransDtls['transfusedDate']=this.transfusedDate;
      // rgTbBldTransDtls['transfusedDate']=this.transfusedDate;
      this._renalService.SaveCaseDetails(saveData).subscribe(res => {

        if (res['code'] == 0) {
          Swal.fire('Saved!', 'Renal Case Details Saved successfully.', 'success');
          this.regId = res["result"];
          this.search();
        } else if (res['code'] == "3") {
          Swal.fire('Saved!', res['message'], 'error');
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }

      }, err => {
        Swal.fire('Error!', 'An Error occurred while saving Renal Case Details ' + err.message, 'error')
      })


    } else {
      Swal.fire('error!', 'You can not saved with out patient registration No#  ', 'error')
    }








  }

  get f() {
    return this.caseDetailsForm.controls;
  }

  submit() {
    if (this.caseDetailsForm.status === 'VALID') {
    }
  }




  ////////////// Lab Results - start//////////// 
  addNewlab() {
    this.addNewLabList(null, '', '', '', '', '', '', '', '', '', '', this.loginId, '', 'W', true);
  }
  addNewLabList(runId: any = null, testDate: any = null, releasedDate: any = null, profileTestCode: any = null, profileTestName: any = null, mohTestCode: any = null, componentTestName: any = null, value: any = null, unit: any = null, instCode: any = null, instName: any = null, enteredBy: any = null, enteredDate: any = null, source: any = null, isEditable: any = false): void {
    this.rgTbLabTestInfo = this.labTestForm.get('rgTbLabTestInfo') as FormArray;

    this.labnewList = Object.assign([], this.rgTbLabTestInfo.value);
    const labItem: any = this.createLabItem(runId, testDate, releasedDate, profileTestCode, profileTestName, mohTestCode, componentTestName, value, unit, instCode, instName, enteredBy, enteredDate, source, isEditable);
    this.rgTbLabTestInfo.push(this.createLabGrpItem(labItem));

    this.labnewList.push(labItem);

  }

  createLabGrpItem(createLabItem: any): FormGroup {
    return this.formBuilder.group(createLabItem);
  }

  createLabItem(runId: any = null, testDate: any = null, releasedDate: any = null, profileTestCode: any = null, profileTestName: any = null, mohTestCode: any = null, componentTestName: any = null, value: any = null, unit: any = null, instCode: any = null, instName: any = null, enteredBy: any = null, enteredDate: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      testDate: testDate,
      releasedDate: releasedDate,
      profileTestCode: profileTestCode,
      profileTestName: profileTestName,
      mohTestCode: mohTestCode,
      componentTestName: componentTestName,
      unit: unit,
      value: value,
      instCode: instCode,
      instName: instName,
      enteredBy: enteredBy,
      enteredDate: enteredDate,
      source: source,
      isEditable: isEditable

    };
  }
  onRowEditSave(row: any) {
    let rowIndex = this.labnewList.indexOf(row);
    this.labnewList[rowIndex] = this.labTestForm.value.rgTbLabTestInfo[rowIndex];
    let data = this.labnewList[rowIndex];
    // data.componentTestName = this.testComponetList.filter(s => s.componentTestId == data.mohTestCode).map(s => s.componentTestName)[0];
    data.componentTestName = this.testComponetList.filter(s => s.mohTestCode == data.mohTestCode).map(s => s.testName)[0];
    data.instName = this.institutes.filter(s => s.estCode == data.instCode).map(s => s.estName)[0];
    // data.profileTestName = this.testComponetList.filter(s => s.testId == data.profileTestCode).map(s => s.testName)[0];
    data.profileTestName = this.testComponetList.filter(s => s.mohTestCode == data.profileTestCode).map(s => s.testName)[0];
    data.isEditable = false;

  }

  getLabFromNehr(data: any = 0) {

    data.forEach(el => {
      // console.log(el, "el")
      let date = ""



      let componentTestName = this.testComponetList.filter(s => s.mohTestCode == el[7]).map(s => s.testName)[0];
      // let componentTestName = this.testComponetList.filter(s => s.componentTestId == el[7]).map(s => s.componentTestName)[0];
      let instName = this.institutes.filter(s => s.estCode == el[19]).map(s => s.estName)[0];
      // let profileTestName = this.testComponetList.filter(s => s.testId == el[3]).map(s => s.testName)[0];  
      let profileTestName = this.testComponetList.filter(s => s.mohTestCode == el[3]).map(s => s.testName)[0];

      this.addNewLabList(null, el[1], el[5], el[3], profileTestName, el[7], componentTestName, el[9], el[10],
        el[19], instName, this.loginId, date, "S", false);
    });
  }


  deletelab(row: any) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbLabTestInfo = this.labTestForm.get('rgTbLabTestInfo') as FormArray
        this.delRow = this.labnewList.indexOf(row);
        this.labnewList.splice(this.delRow, 1);
        this.rgTbLabTestInfo.removeAt(this.delRow);

      }
    })
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
  }
  ////////////// Lab Results - end//////////// 


  callFetchVaccDataFromAlShifa() {

    this.FetchVaccDataFromAlShifa(this.patientDetails.patientForm.controls["regInst"].value, this.patientDetails.patientForm.controls["patientId"].value);

  }

  callFetchLabDataFromAlShifa() {


    let profileList = [];
    if (this.filterModelForm.controls["profileT"].value != null) {
      this.filterModelForm.controls["profileT"].value.forEach(element => {
        profileList.push(element.mohTestCode);
      });
    }

    let body = {
      "civiLId": this.patientDetails.patientForm.controls["civilId"].value,
      "fromDate": this.filterModelForm.controls["fromDate"].value,
      "toDate": this.filterModelForm.controls["toDate"].value,
      "profile_list": profileList,
    }
    //this.patientDetails.patientForm.controls["civilId"].value;
    this._masterService.getNehrLabTest(body).subscribe(response => {
      this.getLabFromNehr(response.result)
      // console.log("nehr",response["result"]);

      // for (let i =0 ; i<= response.length ; i++)
      // {
      //   console.log("nehr", i,response["result"][i]);
      // }

    })
  }



  FetchVaccDataFromAlShifa(estcode: any, patientId: any) {
    this._masterService.getVaccineHistroy(estcode, patientId).subscribe(res => {
      this.disVaccineInfoalshifa = res["result"];
      if (this.disVaccineInfoalshifa.length == 0) {
        Swal.fire('warning!', 'No Records Found ', 'warning')
      }
      else {
        this.getDataFromAlshifa(this.disVaccineInfoalshifa);
      }
    })
  }


  getDataFromAlshifa(sendingData: any = 0) {
    this.vaccNewList = sendingData;
    this.vaccNewList.forEach(element => {

      this.vaccName = this.getvaccName(element.vaccineId);

    });
  }
  getvaccName(vaccineCode) {
    return this.vaccinMastList.find(sur => sur.vaccineId === vaccineCode)['vaccineName'] || '-';
  }


  openModal(downloadLabTest) {
    this.modalService.open(downloadLabTest);
  }

  onTestChange(event) {
    this.testcomponet = this.testComponetList.filter(s => s.testId == event.mohTestCode);
  }



  clear() {
    this.caseDetailsForm.reset();
    this.patientDetails.clear();
    this.Vaccination.clear();

    this.labTestFg = [];
    this.labTestForm.reset();
    this.labnewList = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    })
  }

  navigateToDashboard() {
    this._router.navigateByUrl('renal/home');
  }

  navigateToRegister() {
    this._sharedService.setNavigationData({ centralRegNo: this.patientDetails.f.centralRegNo.value });
    this._router.navigate(['renal/registry'], { state: { centralRegNo: this.patientDetails.f.centralRegNo.value } });
  }

  callTissue() {
    this._sharedService.setNavigationData({ centralRegNo: this.patientDetails.f.centralRegNo.value });
    this._router.navigate(['/tissueType'], { state: { centralRegNo: this.patientDetails.f.centralRegNo.value } });
  }

}
