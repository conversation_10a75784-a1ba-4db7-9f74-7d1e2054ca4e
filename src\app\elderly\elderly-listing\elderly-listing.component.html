<h6 class="page-title">Elderly Listing</h6>
<div class="content-wrapper">
  <form [formGroup]="elderlySearchForm">
    <div class="row">
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Registration No</label>
          <input type="number" class="form-control form-control-sm" formControlName="centralRegNo">
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
              <label>Age (from)</label>
              <input type="number" class="form-control form-control-sm" formControlName="ageFrom">
            </div>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
              <label>To</label>
              <input type="number" class="form-control form-control-sm" formControlName="ageTo">
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Sex</label>
          <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="sex">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Institute</label>
          <ng-select #entryPoint [items]="institeList" [virtualScroll]="true" placeholder="Select"
            bindLabel="estName" bindValue="estCode" formControlName="estCode">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Region</label>
          <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
            bindValue="regCode" formControlName="regCode" (change)="regSelect($event,'region')">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Wilayat</label>
          <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
            bindLabel="walName" bindValue="walCode" formControlName="walCode" (change)="walSelect($event,'wilayat')">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Village</label>
          <ng-select #entryPoint [items]="villageListFilter" [virtualScroll]="true" placeholder="Select"
            bindLabel="vilName" bindValue="vilCode" formControlName="vilCode" (change)="walSelect($event,'village')">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.vilName}}
            </ng-template>
          </ng-select>

        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">

          <div class="row">
            <div class="col-lg-6 col-md-6 col-sm-6">
              <label>RBS</label>
              <ng-select #entryPoint [items]="rbsList" [virtualScroll]="true" placeholder="Select"
                bindLabel="frequencyDesc" bindValue="freqParam" formControlName="rbs">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.frequencyDesc}}
                </ng-template>
              </ng-select>

            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <label>From</label>
              <input type="number" class="form-control form-control-sm" formControlName="rbsFrom">
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <label>To</label>
              <input type="number" class="form-control form-control-sm" formControlName="rbsTo">
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
              <label>BRADEN</label>
              <ng-select #entryPoint [items]="bradenList" [virtualScroll]="true" placeholder="Select"
                bindLabel="frequencyDesc" bindValue="freqParam" formControlName="braden">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.frequencyDesc}}
                </ng-template>
              </ng-select>

            </div>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>From</label>
            <input type="text" class="form-control form-control-sm" formControlName="bradenFrom">
          </div>
          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>To</label>
            <input type="text" class="form-control form-control-sm" formControlName="bradenTo">
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Mental State</label>
          <ng-select #entryPoint [items]="mentalStateList" [virtualScroll]="true" placeholder="Select"
            bindLabel="frequencyDesc" bindValue="freqParam" formControlName="mentalState">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.frequencyDesc}}
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <!-- <button type="submit"  (click)=" exportToExcel()" class="btn btn-primary ripple" > exportExcel</button> -->
        <button type="submit"  (click)=" exportExcel()" class="btn btn-primary ripple" > exportExcel</button>
        <button type="submit" class="btn btn-primary ripple" (click)="onClear()">Clear</button>
        <button type="submit" class="btn btn-primary ripple" (click)="onSearch()">Search</button>
       
      </div>

    </div>

  </form>
</div>
<div class="content-wrapper mt-2">
  <div style="margin-top:20px">


    <div style="margin-top:20px">

      <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
      </ag-grid-angular>

    </div>

    <div *ngIf="rowData.length > 0">
      <p-paginator #elderlyRegPaginator rows={{paginationSize}} totalRecords="{{totalRecords}}" (onPageChange)=" onSearch($event)"
        showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10">
      </p-paginator>
    </div>
  
  </div>
  

</div>