<p>test works!</p>
<div class="inner-content" id="userDetailsview">
    <div class="inner-wrapper"></div>
    <div class="mainbody ">
        <div class="col-md-3">
            <div class="modal-container">
                <div class="modal-header">
                    Roles
                    <div class="header-controls">
                        <span class="fa fa-plus" data-target="#rolesViewModal" data-toggle="modal"></span>

                    </div>
                </div>
                <div class="modal-mainbody">

                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade custom-modal" id="rolesViewModal" data-backdrop="false" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Roles View</h4>
            </div>

            <div class="modal-body role-modal">

                <div class="row">
                    <div class="col-md-5">
                        <input type="text" [(ngModel)]="searchText" placeholder="Search Role" />
                    </div>
                    <div class="col-md-1 add-remove-controls">
                        <div id="leftRole"><button class="fa fa-chevron-right" type="button"></button></div>
                        <div id="rightRole"> <button class="fa fa-chevron-left" type="button"></button></div>
                        <div id="leftallRole"><button class="fa fa-forward" type="button"></button></div>
                        <div id="rightallRole"><button class="fa fa-backward" type="button"></button></div>
                    </div>
                    <div class="col-md-6">

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-dismiss="modal">Ok</button>
                <button type="button" class="btn btn-light" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<button type="button" class="btn btn-primary" (click)="openModal(template)">Roles View</button>
 
<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Roles View</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="row">
        <div class="col-md-5">
            <input type="text" [(ngModel)]="searchText" placeholder="Search Role" />
        </div>
        <div class="col-md-1 add-remove-controls">
            <div id="leftRole"><button class="fa fa-chevron-right" type="button"></button></div>
            <div id="rightRole"> <button class="fa fa-chevron-left" type="button"></button></div>
            <div id="leftallRole"><button class="fa fa-forward" type="button"></button></div>
            <div id="rightallRole"><button class="fa fa-backward" type="button"></button></div>
        </div>
        <div class="col-md-6">

        </div>
    </div>
  </div>
</ng-template>