export class TbPatientInfo {
public patientId : number ;
public careGiverName : String ;
public careGiverTel : number ;
public cateGiverMob : number ;
public civilId : number ;
public createdBy : number ;
public createdInstid : number ;
public createdOn : Date ;
public dob : Date ;
public firstName : String ;
public lastUpdatedBy : number ;
public lastUpdatedDate : Date ;
public lastUpdatedInstId : number ;
public maritalStatus : String ;
public mobileNo : number ;
public secondName : String ;
public sex : String ;
public thirdName : String ;
public tribe : String ;
public village : number ;
public civilIdEntryType:String;
public nationality:number;


/*
	private RgTbRegistryPatientDto rgTbRegistryPatient= null;
*/
}