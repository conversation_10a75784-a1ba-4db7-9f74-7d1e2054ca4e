{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"eregistry-api": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/eregistry", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@fortawesome/fontawesome-free-webfonts/css/fontawesome.css", "node_modules/@fortawesome/fontawesome-free-webfonts/css/fa-regular.css", "node_modules/@fortawesome/fontawesome-free-webfonts/css/fa-brands.css", "node_modules/@fortawesome/fontawesome-free-webfonts/css/fa-solid.css", "node_modules/bootstrap/scss/bootstrap.scss", "node_modules/primeng/resources/themes/omega/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.scss", "node_modules/sweetalert2/dist/sweetalert2.min.css", "node_modules/primeng/resources/themes/nova-light/theme.css"], "scripts": ["node_modules/chart.js/dist/Chart.js", "node_modules/sweetalert2/dist/sweetalert2.min.js", "node_modules/jquery/dist/jquery.min.js"]}, "configurations": {"prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "8mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "25mb", "maximumError": "25mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "cloud": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.cloud.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "11mb", "maximumError": "11mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "eregistry-api:build"}, "configurations": {"production": {"browserTarget": "eregistry-api:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "eregistry-api:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "eregistry-api:serve"}, "configurations": {"production": {"devServerTarget": "eregistry-api:serve:production"}}}}}}, "defaultProject": "eregistry-api", "cli": {"analytics": false}}