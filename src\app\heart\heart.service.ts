import { Injectable } from "@angular/core";
import { SharedService } from "../_services/shared.service";
import { HttpClient, HttpParams } from "@angular/common/http";
import * as AppUtils from "../common/app.utils";
import { Observable } from "rxjs";
import { RenalDonorPatient } from "../_models/renal_donor_patient.model";
import { ResultDecorator } from "../_models/lungTransplant.model";

@Injectable({
  providedIn: "root",
})
export class HeartService {
  [x: string]: any;
  constructor(private _http: HttpClient) { }

  public getHeartRegistry(regNo, civilId) {
    let params = new HttpParams();
    if (regNo !== undefined) {
      params = params.set("centralRegNo", regNo);
    }

    if (civilId !== undefined) {
      params = params.set("civilId", civilId);
    }
    return this._http.get(AppUtils.FIND_HEART_REGISTRY, { params });
  }

  getHeartListing(date): Observable<any> {
    return this._http.post(AppUtils.SEARCH_Heart, date);
  }

  public saveHeartRegistry(saveList) {
    return this._http.post(AppUtils.SAVE_HEART_REGISTRY, saveList);
  }

  public deleteDiagnosis(runId) {
    return this._http.get(AppUtils.DELETE_LUNG_DIAGNOSIS, {
      params: new HttpParams().set("runId", runId),
    });
  }

  getComplications(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(
      AppUtils.GET_LIVER_COMPLICATION_MAST
    );
  }

  getLiverProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_PROCEDURE_MAST);
  }

  getCurrentManagement(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_CURR_MGMT_MAST);
  }

  getCaseDetails(regNo) {
    return this._http.get(AppUtils.GET_CASE_DETAILS_LUNG, {
      params: new HttpParams().set("regNo", regNo),
    });
  }

  getGeneticMedicineList(): Observable<any> {
    return this._http.get(AppUtils.GET_GEN_MEDICINE_LIST);
  }

  saveMainCaseDetails(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HEART_CASE_DETAILS, data);
  }

  fetchLabFromShifa(estCode: any, civilId: any): Observable<any> {
    return this._http.get(
      AppUtils.FETCH_ALL_HEART_DONOR_LAB_FROM_ALSHIFA +
      "?estCode=" +
      estCode +
      "&civilId=" +
      civilId
    );
  }

  fetchSurgeryFromShifa(estCode: any, civilId: any): Observable<any> {
    return this._http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_SURGERY_FROM_ALSHIFA +
      "?estCode=" +
      estCode +
      "&civilId=" +
      civilId
    );
  }

  getLungRegistryMedicineInfo(estCode: any, patientId: any): Observable<any> {
    return this._http.get(
      AppUtils.GET_LUNG_MEDICINE_INFO_ALSHIFA + estCode + "/" + patientId
    );
  }

  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.GET_HEART_DASHBOARD);
  }

  getDonorDetails(Data, Type) {
    if (Type == "civilId") {
      return this._http.get(AppUtils.REG_HEART_DTL_BY_CIVIL_ID, {
        params: new HttpParams().set("civilId", Data),
      });
    } else {
      return this._http.get(AppUtils.REG_HEART_DTL_BY_ID, {
        params: new HttpParams().set("kidneyDonorId", Data),
      });
    }
  }

  getCompareHlaScore(regNo, donorId) {
    return this._http.get(AppUtils.GET_COMPARE_HLA_SCORE, {
      params: new HttpParams().set("regNo", regNo).set("donorId", donorId),
    });
  }

  getPatientsDonorbyRelationType(
    relationType: number,
    kidneyDonorId: number
  ): Observable<any> {
    return this._http.get(AppUtils.GET_PATIENTS_HEART_DONOR_BY_RELATION_TYPE, {
      params: new HttpParams()
        .set("relationType", relationType.toString())
        .set("kidneyDonorId", kidneyDonorId.toString()),
    });
  }

  saveTissueTypeList(data: any): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_LIST_RETURN_RUN_ID, data);
  }

  saveHeartDonorRegistry(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HEART_DONOR_REGISTRY, data);
  }

  getPatientsDeceasedDonor(kidneyDonorId) {
    return this._http.get(AppUtils.GET_HEART_PATIENTS_DECEASED_DONOR, {
      params: new HttpParams().set("renalDonorId", kidneyDonorId),
    });
  }

  getDonorList(dto) {
    return this._http.post(AppUtils.FIND_HEART_DONOR_LIST, dto);
  }

  getHeartPhysioBaseline() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_PHYSIO_BASELINE);
  }

  getAllHeartInotropes() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_INOTROPE_AGENT);
  }

  getAllHeartPsClearance() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_PS_CLEARANCE);
  }

  getAllHeartMcsDevice() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_MCS_DEVICE);
  }

  getAllHeartNyhaClass() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_NYHA_CLASS);
  }

  getAllHeartPrimaryEtiology() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_PRIMARY_ETIOLOGY);
  }

  getAllHeartIcds() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_ICDS);
  }

  getAllHeartShortIcds() {
    return this._http.get<ResultDecorator>(AppUtils.GET_HEART_SHORT_ICDS);
  }

  getAllHeartDonorPatient(): Observable<any> {
    return this._http.get(AppUtils.GET_ALL_HEART_DONOR_PATIENT);
  }

  fetchAllDonorLabFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_HEART_DONOR_LAB_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);

  }

  fetchAllDonorSurgeryFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_HEART_DONOR_SURGERY_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);
  }

  fetchAllDonorProcedureFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_HEART_DONOR_PROCEDURE_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);
  }

  updateHeartDonorPatient(donorID, relationType, regNo) {
    return this._http.get(AppUtils.UPDATE_HEART_DONOR_PATIENT, { params: new HttpParams().set("donorID", donorID).set("relationType", relationType).set("regNo", regNo) })
  }

  saveHeartDonorPatient(data: RenalDonorPatient): Observable<any> {
    return this._http.post(AppUtils.SAVE_HEART_DONOR_PATIENT, data);
  }

  getAllHeartDonorPatients(): Observable<any> {
    return this._http.get(AppUtils.GET_ALL_HEART_DONOR_PATIENTS);
  }
}
