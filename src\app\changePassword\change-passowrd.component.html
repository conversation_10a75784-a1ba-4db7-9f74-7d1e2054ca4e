<div style="display: block;"  >
    <form [formGroup]="form">
    <div >

      <!-- Modal content-->
      <div >
        <div class="modal-header">         
          <h4 class="modal-title float-left">Change Password</h4>
          <button type="button" class="close" data-dismiss="modal" (click)="emitCloseEvent()">&times;</button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4">
              <div class="ui-input-group">
                <input type="password" formControlName="oldPassword" id="oldPassword" class="form-control" [(ngModel)]="password.oldPassword" required>
                <!-- <span *ngIf="form.controls['latitude'].hasError('pattern') && (isError || form.controls['latitude'].touched)" class="tooltiptext">{{'Enter valid range (+90.000000 to -90.000000)'}}</span> -->
                <div *ngIf="submitted && f.oldPassword.errors" class="tooltiptext">
                    <div *ngIf="f.oldPassword.errors.required">Old Password is required</div>
                </div>
                <span class="input-bar"></span>
                <label>Old Password</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="ui-input-group">
                <input type="password" id="newPassword"  formControlName="newPassword" class="form-control" [(ngModel)]="password.newPassword" required>
                <div *ngIf="submitted && f.newPassword.errors" class="tooltiptext">
                    <div *ngIf="f.newPassword.errors.required">New Password is required</div>
                </div>
                <span class="input-bar"></span>
                <label>New Password</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="ui-input-group">
                <input type="password" id="confirmPassword"  formControlName="confirmPassword" class="form-control" [(ngModel)]="password.confirmPassword" required>
                <div *ngIf="submitted && f.confirmPassword.errors" class="tooltiptext">
                    <div *ngIf="f.confirmPassword.errors.required">Confirm Password is required</div>
                    <div *ngIf="f.confirmPassword.errors.mustMatch">Passwords must match</div>
                </div>
                <span class="input-bar"></span>
                <label>Confirm New Password</label>
              </div>
            </div>
            <!-- <div><label *ngIf="password.confirmPassword != password.newPassword && password.confirmPassword != null" class="invalid-message-pass">Confirm Password NOT match New Password !!! </label></div> -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary ripple" (click)="onSubmit()">Save</button>
          <button type="button" class="btn btn-primary ripple" data-dismiss="modal" (click)="clear()">Cancel</button>
        </div>
      </div>

    </div>
    </form>
    </div>