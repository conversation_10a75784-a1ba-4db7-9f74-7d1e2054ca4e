<!-- <h6>Lab Results</h6> -->
<h6 style="font-weight: bold;">Lab Results </h6>
<div class="content-wrapper mb-2 lab-results">
  <form [formGroup]="labResultForm">
    <div class="text-right pb-2">
      <button *ngIf="showAddNewButton" (click)="onAddNewLabResult()" class="btn btn-sm btn-primary">Add New</button>
      <button *ngIf="showLabButton" class="btn btn-sm btn-primary" (click)="getlabTest()">Download</button>
    </div>
    <p-dataTable [immutable]="false" [value]="labnewList" [editable]="true" dataKey="runId" [responsive]="true">
      <p-column field="testDate" header="Date">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbLabTests">
            <div [formGroupName]="rowIndex">

              <input type="hidden" class="form-control form-control-sm" formControlName="runId">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredDate">
              <input type="hidden" class="form-control form-control-sm" formControlName="source">

              <div *ngIf="!row.isEditable">{{row.testDate | date:'dd-MM-yyyy'}}</div>
              <div *ngIf="row.isEditable">
                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="testDate"
                [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="mohTestCode" header="Test Name">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbLabTests">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">
                {{getTestName(row.mohTestCode)}}
              </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="labTest" [virtualScroll]="true" placeholder="Select" bindLabel="testName"
                  bindValue="mohTestCode" formControlName="mohTestCode">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.testName }}
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="resultSummary" header="Result">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbLabTests">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.resultSummary}}</div>
              <div *ngIf="row.isEditable">
                <input type="text" class="form-control form-control-sm" formControlName="resultSummary">
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>


      <p-column field="instCode" header="Institute">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbLabTests">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">
                {{getInstName(row.instCode)}} </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                  bindLabel="estName" bindValue="estCode" formControlName="instCode">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
          class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>

        <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
          class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
     
        </ng-template>
      </p-column>

      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
        </ng-template>


      </p-column>
    </p-dataTable>
  </form>
</div>