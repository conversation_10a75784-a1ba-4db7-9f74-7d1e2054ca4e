<div class="loader-overlay" *ngIf="isLoading">
  <div class="loader-container">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <div class="loader-text">Fetching Details...</div>
  </div>
</div>
<div class="row">
  <div class="input-group col-sm-10">
    <h4 class="page-title pt-2">Deceased Donor Register</h4>
  </div>
  <div class="input-group col-sm-2 mb-2 text-right">
    <div class="input-group">
      <input type="text" placeholder="Search Crystal No" [(ngModel)]="crystalNumber"
        class="form-control form-control-sm search-input" (keyup.enter)="search()" />
      <div class="input-group-append">
        <button class="btn btn-default btn-sm search-icon" id="search" (click)="search()">
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
  </div>
</div>
<form *ngIf="deceasedDonorForm" [formGroup]="deceasedDonorForm">
  <div class="content-wrapper mb-3">
    <div class="row">
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Crystal No.</label>
          <input type="text" class="form-control form-control-sm" formControlName="crystalNo" readonly />

        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Patient Id</label>
          <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
            formControlName="instPatientId" />
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Civil ID <span class="mdtr">*</span></label>
          <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm"
            formControlName="civilId" (keyup.enter)="fetchPatientInfoByCivilID()" />
        </div>
        <span *ngIf="submitted && deceasedDonorForm.controls['civilId'].errors" class="tooltiptext">{{ "Civil Id is
          required" }}</span>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Expiry Date <span class="mdtr">*</span></label>
          <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true" formControlName="exDate"
            [yearRange]="yearRange" [ngModelOptions]="{ standalone: true }" monthNavigator="true" [minDate]="today"
            yearNavigator="true" showButtonBar="true" (onSelect)="fetchPatientInfoByCivilID()"></p-calendar>
          <span *ngIf="submitted && deceasedDonorForm.controls['exDate'].errors" class="tooltiptext">{{ "Expiry Dt is
            required" }}</span>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Name <span class="mdtr">*</span></label>
          <input type="text" class="form-control form-control-sm" formControlName="fullName" />
          <span *ngIf="submitted && deceasedDonorForm.controls['fullName'].errors" class="tooltiptext">{{ "Name is
            required" }}</span>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Gender</label>
          <select class="form-control form-control-sm" formControlName="sex">
            <option selected [value]="null">Select</option>
            <option value="M">Male</option>
            <option value="F">Female</option>
          </select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Nationality</label>
          <select class="form-control form-control-sm" formControlName="nationality">
            <option selected [value]="null">Select</option>
            <option [value]="res.natCode" *ngFor="let res of nationalityList">
              {{ res.nationality }}
            </option>
          </select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Date of birth</label>
          <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
            monthNavigator="true" [yearRange]="yearRange" [minDate]="today" yearNavigator="true" showButtonBar="true"
            formControlName="dob"></p-calendar>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Blood Group</label>
          <ng-select #entryPoint [items]="bloodGroupList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="bloodGroup">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.value
              }}</ng-template>
          </ng-select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Marital Status</label>
          <select class="form-control form-control-sm" formControlName="maritalStatus">
            <option selected [value]="null">Select</option>
            <option value="S">Single</option>
            <option value="M">Married</option>
          </select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Occupation</label>
          <ng-select #entryPoint [items]="occupationList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="occupation">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.value
              }}</ng-template>
          </ng-select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Hospital <span class="mdtr">*</span></label>
          <ng-select #entryPoint [items]="hospitalsList" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
            bindValue="estCode" formControlName="instCode">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.estName
              }}</ng-template>
          </ng-select>
        </div>
        <span *ngIf="submitted && deceasedDonorForm.controls['instCode'].errors" class="tooltiptext">{{ "Hospital is
          required" }}</span>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Telephone No.</label>
          <input (keypress)="numberOnly($event)" type="text" class="form-control form-control-sm"
            formControlName="telNo" />
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Address</label>
          <input type="text" class="form-control form-control-sm" formControlName="address" />
        </div>
      </div>


      <div class="col-sm-12 text-right">
        <button *ngIf="showDownload" type="button" class="btn btn-sm btn-primary"
          (click)="openModalInfo(addInstituteModal,'all')">
          Download from Alshifa
        </button>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-sm-4 col-md-3 col-lg-2 pr-0">
      <ul class="form-stages">
        <li *ngFor="let item of dodnorRegStages; let i = index" [ngClass]="{ active: activeStageIndex == i }"
          (click)="selectStage(i)">
          {{ item }}
          <!-- <i class="fa fa-info-circle error-1"></i> -->
        </li>
      </ul>
    </div>
    <div class="col-sm-8 col-md-9 col-lg-10">
      <div class="content-wrapper min-ht-458">
        <div *ngIf="activeStageIndex == 0">
          <h6 class="mb-3">Brain Death Details</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-md-6 col-lg-8">
                <div class="row">
                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Date of Event/Accident</label>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [maxDate]="today" [yearRange]="yearRange" yearNavigator="true"
                        formControlName="eventDate" showButtonBar="true"></p-calendar>
                    </div>
                  </div>
                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Date Admission</label>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [maxDate]="today" [yearRange]="yearRange" yearNavigator="true"
                        [defaultDate]="today" readonlyInput="true" formControlName="admissionDate"
                        showButtonBar="true"></p-calendar>
                    </div>
                  </div>
                  <!-- <div class="col-md-6 col-lg-6">
                    <div class="form-group">
                      <label>Initial Diagnosis</label>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="initialDiag"
                      />
                    </div>
                  </div> -->

                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Initial Diagnosis</label>
                      <ng-select [items]="initialICDList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="initialDiag">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.value }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>

                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Brain Death Declared</label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="brainDeathYes" class="form-check-input" value="Y"
                            formControlName="brainDeathYn" />
                          <label for="brainDeathYes" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="brainDeathNo" class="form-check-input" value="N"
                            formControlName="brainDeathYn" />
                          <label for="brainDeathNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Brain Death Date/Time</label>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [maxDate]="today" [yearRange]="yearRange" yearNavigator="true"
                        formControlName="brainDeathTime" showButtonBar="true" showTime="true" hourFormat="24"
                        [hideOnDateTimeSelect]="true"></p-calendar>
                    </div>
                  </div>

                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Cause of Brain Death</label>
                      <ng-select [items]="brainDeathIcdList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="brainDeathCause">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.value }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>

                  <!-- <div class="col-md-6 col-lg-6">
                    <div class="form-group">
                      <label>Cause of Brain Death</label>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="brainDeathCause"
                      />
                    </div>
                  </div> -->
                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>ICU Stay (Days)</label>
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="icuStayDays" />
                    </div>
                  </div>
                  <div class="col-md-6 col-lg-3">
                    <div class="form-group">
                      <label>Organ Coordinator</label>
                      <input type="text" class="form-control form-control-sm" formControlName="organCoordinatorName" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6 col-lg-4">
                <div class="form-group">
                  <label>Comments</label>
                  <textarea class="form-control form-control-sm" cols="5" rows="7"
                    formControlName="brainDeathremarks"></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-sm-2">
              <div class="form-group mt-5">
                <label>Brain Death Form Issued?</label>
                <div>
                  <div class="form-check form-check-inline">
                    <input type="radio" id="brainDeathFormYes" class="form-check-input" value="Y"
                      formControlName="brainDeathFormYn"
                      (change)="deceasedDonorForm.get('brainDeathFormYn').value === 'Y' && getBrainDeathDetails()" />
                    <label for="brainDeathFormYes" class="form-check-label">Yes</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input type="radio" id="brainDeathFormNo" class="form-check-input" value="N"
                      formControlName="brainDeathFormYn" />
                    <label for="brainDeathFormNo" class="form-check-label">No</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-10">
              <table class="table table-striped border">
                <thead>
                  <tr>
                    <th>Examination Type</th>
                    <!-- <th>Examiner One</th> -->
                    <th>First Examination Day/Time</th>
                    <!--<th>Examiner Two</th> -->
                    <th>Second Examination Date/Time</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Initial Examination</td>
                    <!-- <td>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="test1Examiner1Name"
                      />
                    </td> -->
                    <td>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [yearRange]="yearRange" yearNavigator="true"
                        formControlName="test1Examiner1Date" showButtonBar="true"></p-calendar>
                    </td>
                    <!-- <td>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="test1Examiner2Name"
                      />
                    </td> -->
                    <td>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [yearRange]="yearRange" yearNavigator="true"
                        formControlName="test1Examiner2Date" showButtonBar="true"></p-calendar>
                    </td>
                  </tr>
                  <tr>
                    <td>Second Examination</td>
                    <!-- <td>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="test2Examiner1Name"
                      />
                    </td> -->
                    <td>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" formControlName="test2Examiner1Date" [yearRange]="yearRange"
                        yearNavigator="true" showButtonBar="true"></p-calendar>
                    </td>
                    <!-- <td>
                      <input
                        type="text"
                        class="form-control form-control-sm"
                        formControlName="test2Examiner2Name"
                      />
                    </td> -->
                    <td>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" formControlName="test2Examiner2Date" [yearRange]="yearRange"
                        yearNavigator="true" showButtonBar="true"></p-calendar>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 1">
          <h6 class="mb-3">Physical Examination</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-sm-3 col-md-4 col-lg-2">
                <div class="form-group">
                  <label>Height</label>
                  <input type="text" (keypress)="numberOnlyDecimal($event)" class="form-control form-control-sm"

                    pattern="^(?:[1-9]|[1-9][0-9]|1[0-8][0-9]|190)(\.\d{1,2})?$" formControlName="height" (blur)="getBmi()" />

                  <div class="tooltiptext" *ngIf="deceasedDonorForm.controls['height'].errors?.pattern">
                    {{ "Height should be not more than 190" }}
                  </div>
                </div>
              </div>
              <div class="col-sm-3 col-md-4 col-lg-2">
                <div class="form-group">
                  <label>Weight</label>
                  <input type="text" (keypress)="numberOnlyDecimal($event)" class="form-control form-control-sm"

                    pattern="^(?:[1-9]|[1-9][0-9]|1[0-3][0-9]|140)(\.\d{1,2})?$" formControlName="weight" (blur)="getBmi()" />
                  <div class="tooltiptext" *ngIf="deceasedDonorForm.controls['weight'].errors?.pattern">
                    {{ "Width should be not more than 140" }}
                  </div>
                </div>
              </div>
              <div class="col-sm-3 col-md-4 col-lg-2">
                <div class="form-group">
                  <label>BMI</label>
                  <input type="text" class="form-control form-control-sm" formControlName="bmi" readonly />
                </div>
              </div>
              <div class="col-sm-3 col-md-6 col-lg-3 border-left">
                <div class="form-group">
                  <label>General Skin Inspection </label>
                  <small>Needle track,scars,tattoos,sore or rashes</small>
                  <div>
                    <div class="form-check form-check-inline">
                      <input type="radio" id="skinInspectionYes" class="form-check-input" value="Y"
                        formControlName="skinInspectionYn" />
                      <label for="skinInspectionYes" class="form-check-label">Done</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="radio" id="skinInspectionNo" class="form-check-input" value="N"
                        formControlName="skinInspectionYn" />
                      <label for="skinInspectionNo" class="form-check-label">Not Done</label>
                    </div>

                    <div class="form-group mt-2">
                      <label for="skinInspectionDtls" class="form-check-label">Remarks
                      </label>
                      <textarea class="form-control form-control-sm" formControlName="skinInspectionDtls"></textarea>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-md-6 col-lg-3 border-left">
                <div class="form-group">
                  <label>Palpation of obvious lumps or masses </label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input type="radio" id="palpationYes" class="form-check-input" value="Y"
                        formControlName="palpationYn" />
                      <label for="palpationYes" class="form-check-label">Done</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="radio" id="palpationNo" class="form-check-input" value="N"
                        formControlName="palpationYn" />
                      <label for="palpationNo" class="form-check-label">Not Done</label>
                    </div>

                    <div class="form-group mt-2">
                      <label for="palpationDtls" class="form-check-label">Remarks</label>
                      <textarea class="form-control form-control-sm" formControlName="palpationDtls"></textarea>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-md-6 col-lg-6">
                <div class="form-group">
                  <label>Comments</label>
                  <textarea class="form-control form-control-sm" formControlName="remarks"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 2">
          <h6 class="mb-3">Ethical Approvals</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-md-6 col-lg-4 mb-2 border-right"
                *ngFor="let approval of ethicalApprovalLabels; let i = index">
                <div class="form-group">
                  <label>{{ approval.value }}</label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input type="radio" [id]="'approvalYes' + i" class="form-check-input"
                        [formControlName]="'approval_' + i" value="Y" />
                      <label [for]="'approvalYes' + i" class="form-check-label">Yes</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="radio" [id]="'approvalNo' + i" class="form-check-input"
                        [formControlName]="'approval_' + i" value="N" />
                      <label [for]="'approvalNo' + i" class="form-check-label">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-12 col-lg-4">
                <div class="form-group">
                  <label>Comments</label>
                  <textarea class="form-control form-control-sm" formControlName="ethicalApprovalComments"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 3">
          <h6 class="mb-3">Medical and Social History</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-sm-6 border-right">
                <div class="row">
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Cancer?</label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cancerYes" class="form-check-input" value="Y"
                            formControlName="cancerYn" />
                          <label for="cancerYes" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cancerNo" class="form-check-input" value="N"
                            formControlName="cancerYn" />
                          <label for="cancerNo" class="form-check-label">No</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cancerUnknown" class="form-check-input" value="U"
                            formControlName="cancerYn" />
                          <label for="cancerUnknown" class="form-check-label">Unknown</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Site of cancer category</label>
                      <ng-select [items]="cancerCategoryList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="cancerSite">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.value }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Histology</label>
                      <ng-select [items]="medicalHistoryList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="histology">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.value }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Cancer metastasis</label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cancerMetastasisYes" class="form-check-input" value="Y"
                            formControlName="cancerMetastasisYn" />
                          <label for="cancerMetastasisYes" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cancerMetastasisNo" class="form-check-input" value="N"
                            formControlName="cancerMetastasisYn" />
                          <label for="cancerMetastasisNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Date of cancer diagnosis</label>
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [yearRange]="yearRange" yearNavigator="true" showButtonBar="true"
                        formControlName="cancerDiagDate"></p-calendar>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group">
                      <label>Cancer treatment</label>
                      <input type="text" class="form-control form-control-sm" formControlName="cancerTreatmentDtls" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="row">
                  <div class="col-sm-6 border-right mb-2" *ngFor="let item of medicalHistoryLabels">
                    <div class="form-group btm-divider">
                      <label>{{ item.value }}</label>
                      <div class="d-flex align-items-center">
                        <div class="form-check form-check-inline">
                          <input type="radio" [id]="'medicalHistoryYes_' + item.id" class="form-check-input"
                            [formControlName]="'medicalHistory_' + item.id" value="Y" />
                          <label [for]="'medicalHistoryYes_' + item.id" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" [id]="'medicalHistoryNo_' + item.id" class="form-check-input"
                            [formControlName]="'medicalHistory_' + item.id" value="N" />
                          <label [for]="'medicalHistoryNo_' + item.id" class="form-check-label">No</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" [id]="'medicalHistoryUnknown_' + item.id" class="form-check-input"
                            [formControlName]="'medicalHistory_' + item.id" value="U" />
                          <label [for]="'medicalHistoryUnknown_' + item.id" class="form-check-label">Unknown</label>
                        </div>
                        <input type="text" class="form-control form-control-sm ml-2" placeholder="Details (if any)"
                          [formControlName]="'medicalHistoryRemarks_' + item.id" />
                      </div>
                    </div>
                  </div>

                  <div class="col-sm-12">
                    <div class="form-group">
                      <label>Comments</label>
                      <textarea class="form-control form-control-sm" placeholder="Additional comments"
                        formControlName="medSocHistoryComments"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 4">
          <h6 class="mb-3">Vital Sign and Cardiorespiratory Status</h6>
          <div class="content-wrapper mb-2">
            <form [formGroup]="vitalForm">
              <div class="text-right pb-2">
                <button *ngIf="showDownload" (click)="addVital()" class="btn btn-sm btn-primary">
                  Add New
                </button>

                <button *ngIf="showVitalDownloadButton && (!vitalList || vitalList.length === 0)"
                  class="btn btn-sm btn-primary" (click)="openModalInfo(addInstituteModal,'vital')">
                  Download
                </button>

                <button *ngIf="showVitalChartButton" (click)="toggleChart()" class="btn btn-sm btn-primary">
                  {{ showChart ? "Show Table" : "Show Chart" }}
                </button>
                <button *ngIf="(vitalList.length > 0)" class="btn btn-sm btn-primary" (click)="deleteAllVital()">
                  Delete All
                </button>
              </div>
              <div *ngIf="showVitalResultsTable && !showChart" class="table-responsive">
                <table class="table table-bordered text-center">
                  <thead>
                    <tr>
                      <th>Vital</th>
                      <th *ngFor="let date of paginatedDateVitalListNew">
                        {{ date }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let vital of vitalListNew">
                      <td>{{ vital.name }}</td>
                      <td *ngFor="let date of paginatedDateVitalListNew">
                        {{ vital.values[date] || "" }}
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="pagination-controls text-center">
                  <button class="btn btn-sm btn-secondary" [disabled]="currentPage === 1" (click)="previousPage()">
                    Previous
                  </button>
                  <span>Page {{ currentPage }} of {{ totalPages }}</span>
                  <button class="btn btn-sm btn-secondary" [disabled]="currentPage === totalPages" (click)="nextPage()">
                    Next
                  </button>
                </div>
              </div>
              <div *ngIf="showChart" style="width: 600px; height: 400px">
                <p-chart type="line" [data]="chartData" [options]="chartOptions"
                  style="width: 100%; height: 100%"></p-chart>
              </div>
              <!-- <div *ngIf="showChart">
                          <canvas baseChart
                                  [data]="lineChartData"
                                  [options]="lineChartOptions"
                                  chartType="line">
                          </canvas>
                        </div> -->
              <div *ngIf="showAddNewSection && !showChart">
                <p-dataTable [immutable]="false" [value]="paginatedVitalList" [editable]="true" dataKey="id"
                  [responsive]="true" [rows]="itemsPerVitalPage" [paginator]="false" [totalRecords]="vitalList.length"
                  [(first)]="first" (onPage)="onVitalPageChange($event)">
                  <!-- Vital Name -->
                  <p-column field="vital" header="Vital" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="vitals">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ getVitalName(row.vital) }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <ng-select [items]="vitalOptions" bindLabel="value" bindValue="id" formControlName="vital"
                              placeholder="Select Vital"></ng-select>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Vital Value -->
                  <p-column field="vitalValue" header="Value" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="vitals">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.vitalValue }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                              formControlName="vitalValue" placeholder="Enter value" />
                            <div *ngIf="
                              vitals.controls[rowIndex].get('vitalValue')
                                .invalid &&
                              vitals.controls[rowIndex].get('vitalValue')
                                .touched
                            " class="text-danger">
                              <small *ngIf="
                                vitals.controls[rowIndex]
                                  .get('vitalValue')
                                  .hasError('required')
                              ">Value is required.</small>
                              <small *ngIf="
                                vitals.controls[rowIndex]
                                  .get('vitalValue')
                                  .hasError('min')
                              ">Value is too low.</small>
                              <small *ngIf="
                                vitals.controls[rowIndex]
                                  .get('vitalValue')
                                  .hasError('max')
                              ">Value is too high.</small>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Vital Date -->
                  <p-column field="vitalDate" header="Date" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="vitals">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ row.vitalDate | date : "dd-MM-yyyy HH:mm" }}
                            <!-- Display date and time -->
                          </div>
                          <div *ngIf="row.isEditable">
                            <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="vitalDate"
                              [ngModelOptions]="{ standalone: true }" monthNavigator="true" [yearRange]="yearRange"
                              yearNavigator="true" showButtonBar="true" showTime="true" hourFormat="24"
                              [hideOnDateTimeSelect]="true" appendTo="body"></p-calendar>
                            <div *ngIf="
                              vitals.controls[rowIndex].get('vitalDate')
                                .invalid &&
                              vitals.controls[rowIndex].get('vitalDate').touched
                            " class="text-danger">
                              <small *ngIf="
                                vitals.controls[rowIndex]
                                  .get('vitalDate')
                                  .hasError('required')
                              ">Date and Time is required.</small>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Vital Remarks -->
                  <p-column field="vitalRemarks" header="Remarks" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="vitals">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ row.vitalRemarks }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="vitalRemarks"
                              placeholder="Enter remarks" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Actions -->
                  <p-column field="" header="Actions" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body">
                      <button (click)="onRowEditInit(row)" *ngIf="row.isEditable === false"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button (click)="onRowEditSave(row)" *ngIf="row.isEditable === true"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i>
                      </button>
                      <button (click)="removeVital(row)" class="btn btn-sm btn-danger">
                        <i class="fa fa-trash"></i>
                      </button>
                    </ng-template>
                  </p-column>
                </p-dataTable>
                <div class="pagination-controls text-center mt-2">
                  <button class="btn btn-sm btn-secondary" [disabled]="currentVitalPage === 1"
                    (click)="previousVitalPage()">
                    Previous
                  </button>
                  <span class="mx-2">Page {{ currentVitalPage }} of {{ totalVitalPages }}</span>
                  <button class="btn btn-sm btn-secondary" [disabled]="currentVitalPage === totalVitalPages"
                    (click)="nextVitalPage()">
                    Next
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 5">
          <h6 class="mb-3">HLA Class Typing</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-md-4 border-right">
                <div class="row">
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">A1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.a_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">A2</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.a_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">B1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.b_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">B2</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.b_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">C1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.cw_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2">C2</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.cw_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="row">
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DR1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.dr_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DR2</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.dr_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DQB</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.dq_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DQB</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.dq_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DQA</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.drw_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DQA</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.drw_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DP1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.bw_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                  <div class="col-md-6 d-flex align-items-center mb-3">
                    <label class="mr-2 minw-label">DP1</label>
                    <input type="text" (keypress)="numberOnly($event)" class="form-control"
                      [(ngModel)]="hlaTissueType.bw_1_Test" [ngModelOptions]="{ standalone: true }" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 6">
          <h6 class="mb-3">Hemodynamic Instability Evaluation</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div class="col-sm-6 border-right">
                <div class="row">
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Cardiac Arrest Arrived?</label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cardiacArstRevYes" class="form-check-input" name="cardiacArstRevYn"
                            value="Y" [(ngModel)]="cardiacArstDtlsDto.cardiacArstRevYn"
                            [ngModelOptions]="{ standalone: true }" />
                          <label for="cardiacArrestYes" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="cardiacArstRevNo" class="form-check-input" name="cardiacArstRevYn"
                            value="N" [(ngModel)]="cardiacArstDtlsDto.cardiacArstRevYn"
                            [ngModelOptions]="{ standalone: true }" />
                          <label for="cardiacArrestNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-8">
                    <div class="form-inline">
                      <div class="form-group">
                        <label>CPR Duration</label>
                        <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm mx-4"
                          [(ngModel)]="cardiacArstDtlsDto.cprDuration" [ngModelOptions]="{ standalone: true }" />
                        <small class="text-muted">Min</small>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Ventilation Date & Time</label>

                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" [ngModelOptions]="{ standalone: true }"
                        monthNavigator="true" [yearRange]="yearRange" yearNavigator="true" showButtonBar="true"
                        formControlName="ventilationDt"></p-calendar>
                      <!-- <input type="text" class="form-control form-control-sm" formControlName="ventilationDt"> -->
                    </div>
                  </div>
                  <div class="col-sm-8">
                    <div class="form-group">
                      <label>Transfusions?</label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="transfusionYes" class="form-check-input" value="Y"
                            formControlName="transfusionYn" />
                          <label for="transfusionYes" class="form-check-label">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input type="radio" id="transfusionNo" class="form-check-input" value="N"
                            formControlName="transfusionYn" />
                          <label for="transfusionNo" class="form-check-label">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Platelets</label>
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="platelet" />
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>FFP</label>
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="ffp" />
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>Albumin</label>
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="albumin" />
                    </div>
                  </div>
                  <div class="col-sm-12">
                    <div class="form-group">
                      <label>Comments</label>
                      <textarea class="form-control form-control-sm" formControlName="hemodynamicComments"></textarea>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="row">
                  <div class="col-sm-6">
                    <div class="form-inline mb-3">
                      <div class="form-group">
                        <label class="minw-label">PH</label>
                        <input type="text" (keypress)="numberOnlyDecimal($event)"
                          class="form-control form-control-sm mx-2" formControlName="lungPh" />
                      </div>
                      <!-- <div class="col-sm-2">
                      <button style="margin-top: -5px; align-self:right;"
                        type="button"
                        class="btn btn-sm btn-primary"
                        (click)="openModalInfo(addInstituteModal,'vitalPh')"
                      >
                        Download
                      </button>
                      </div> -->
                    </div>
                    <div class="form-inline mb-3">
                      <div class="form-group">
                        <label class="minw-label">PaO2</label>
                        <input type="text" (keypress)="numberOnlyDecimal($event)"
                          class="form-control form-control-sm mx-2" formControlName="lungPao2" />
                        <small class="text-muted">mmHg</small>
                      </div>
                    </div>
                    <div class="form-inline mb-3">
                      <div class="form-group">
                        <label class="minw-label">PaCO2</label>
                        <input type="text" (keypress)="numberOnlyDecimal($event)"
                          class="form-control form-control-sm mx-2" formControlName="lungPaco2" />
                        <small class="text-muted">mmHg</small>
                      </div>
                    </div>
                    <div class="form-inline mb-3">
                      <div class="form-group">
                        <label class="minw-label">PEEP</label>
                        <input type="text" (keypress)="numberOnlyDecimal($event)"
                          class="form-control form-control-sm mx-2" formControlName="lungPeep" />
                        <small class="text-muted">cms</small>
                      </div>
                    </div>
                    <div class="form-inline mb-3">
                      <div class="form-group">
                        <label class="minw-label">FiO2</label>
                        <input type="text" (keypress)="numberOnlyDecimal($event)"
                          class="form-control form-control-sm mx-2" formControlName="lungFio2" />
                        <small class="text-muted">% Oxygen Conc.</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 7">
          <h6 class="mb-3">Organ Related Investigation</h6>
          <div class="content-wrapper mb-2">
            <div class="row">
              <div *ngFor="let organGroup of organRelatedParams | keyvalue" class="col-sm-6 col-md-4 col-lg-3">
                <div class="border-right">
                  <h6 class="mb-3">{{ organGroup.key }}</h6>
                  <div *ngFor="let param of organGroup.value" class="form-group btm-divider">
                    <label>{{ param.value }}</label>
                    <div class="d-flex align-items-center">
                      <div class="form-check form-check-inline">
                        <input type="radio" [id]="'yes_' + param.id" class="form-check-input"
                          [name]="'param_' + param.id" value="Y" [(ngModel)]="param.paramValue"
                          [ngModelOptions]="{ standalone: true }" />
                        <label [for]="'yes_' + param.id" class="form-check-label">Yes</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input type="radio" [id]="'no_' + param.id" class="form-check-input"
                          [name]="'param_' + param.id" value="N" [(ngModel)]="param.paramValue"
                          [ngModelOptions]="{ standalone: true }" />
                        <label [for]="'no_' + param.id" class="form-check-label">No</label>
                      </div>
                      <input type="text" class="form-control form-control-sm ml-2" [(ngModel)]="param.remarks"
                        [ngModelOptions]="{ standalone: true }" placeholder="Details (if any)" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label>{{ organGroup.key }} Comments</label>
                  <textarea class="form-control form-control-sm"
                    [formControlName]="getCommentControlName(organGroup.key)"
                    placeholder="Enter comments for {{ organGroup.key }}"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 8">
          <h6 class="mb-3">Lab Investigations</h6>
          <div class="content-wrapper mb-2">
            <form [formGroup]="labResultForm">
              <div class="text-right pb-2">
                <button *ngIf="showDownload" (click)="addLabResult()" class="btn btn-sm btn-primary">
                  Add New
                </button>
                <button *ngIf="showLabownloadButton && (!labResultList || labResultList.length === 0)"
                  (click)="openModalInfo(addInstituteModal,'lab')" class="btn btn-sm btn-primary">
                  Download
                </button>

                <button *ngIf="showLabChartButton" (click)="toggleChartLab()" class="btn btn-sm btn-primary">
                  {{ showLabChart ? "Show Table" : "Show Chart" }}
                </button>
                <button *ngIf="(labResultList && labResultList.length > 0)" class="btn btn-sm btn-primary"
                  (click)="deleteAllLab()">
                  Delete All
                </button>
              </div>
              <div *ngIf="showLabResultsTable && !showLabChart" class="table-responsive">
                <table class="table table-bordered text-center">
                  <thead>
                    <tr>
                      <th>Test Name</th>
                      <th *ngFor="let date of paginatedLabDatesNew">
                        {{ date }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let lab of labListNew">
                      <td>{{ lab.name }}</td>
                      <td *ngFor="let date of paginatedLabDatesNew">
                        {{ lab.values[date] || "" }}
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="pagination-controls text-center">
                  <button class="btn btn-sm btn-secondary" [disabled]="currentDatePage === 1"
                    (click)="previousDatePage()">
                    Previous
                  </button>
                  <span>Page {{ currentDatePage }} of {{ totalDatePages }}</span>
                  <button class="btn btn-sm btn-secondary" [disabled]="currentDatePage === totalDatePages"
                    (click)="nextDatePage()">
                    Next
                  </button>
                </div>
              </div>

              <div *ngIf="showLabChart" style="width: 600px; height: 400px">
                <p-chart type="line" [data]="labChartData" [options]="labChartOptions"
                  style="width: 100%; height: 100%"></p-chart>
              </div>

              <div *ngIf="!showLabResultsTable">
                <p-dataTable *ngIf="showLabAddNewSection && !showLabChart" [immutable]="false" [value]="labResultList"
                  [editable]="true" dataKey="id" [responsive]="true" [paginator]="true" [rows]="10"
                  [showCurrentPageReport]="true">
                  <p-column field="mohTestCode" header="Test Name" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="rgTbLabTests">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ getDonorLabTestName(row.mohTestCode) }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <ng-select [items]="donorlabTestOptions" bindLabel="testName" bindValue="mohTestCode"
                              formControlName="mohTestCode" placeholder="Select Test"></ng-select>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Result -->
                  <!-- <p-column
                field="result"
                header="Result"
                [style]="{ 'text-align': 'center', width: '54px' }"
              >
                <ng-template
                  let-row="rowData"
                  pTemplate="body"
                  let-rowIndex="rowIndex"
                >
                  <ng-container formArrayName="rgTbLabTests">
                    <div [formGroupName]="rowIndex">
                      <div *ngIf="!row.isEditable">{{ row.result }}</div>
                      <div *ngIf="row.isEditable">
                        <input
                          type="text"
                          class="form-control form-control-sm"
                          formControlName="result"
                          placeholder="Enter result"
                        />
                      </div>
                    </div>
                  </ng-container>
                </ng-template>
              </p-column> -->

                  <p-column field="value" header="Value" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="rgTbLabTests">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.value }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                              formControlName="value" placeholder="Enter Value" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <p-column field="unit" header="Unit" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="rgTbLabTests">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.unit }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="unit"
                              placeholder="Enter Unit" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Release Date -->
                  <p-column field="testDate" header="Test Date" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="rgTbLabTests">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ row.testDate | date : "dd-MM-yyyy HH:mm" }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="testDate"
                              [ngModelOptions]="{ standalone: true }" monthNavigator="true" [yearRange]="yearRange"
                              yearNavigator="true" showButtonBar="true" showTime="true" timeFormat="HH:mm"
                              hourFormat="24"></p-calendar>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Remarks -->
                  <p-column field="remarks" header="Remarks" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="rgTbLabTests">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.remarks }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="remarks"
                              placeholder="Enter remarks" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Actions -->
                  <p-column field="" header="Actions" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body">
                      <button (click)="onRowEditLabInit(row)" *ngIf="row.isEditable === false"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button (click)="onRowEditLabSave(row)" *ngIf="row.isEditable === true"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i>
                      </button>
                      <button (click)="removeLabResult(row)" class="btn btn-sm btn-danger">
                        <i class="fa fa-trash"></i>
                      </button>
                    </ng-template>
                  </p-column>
                </p-dataTable>
              </div>
            </form>
          </div>
          <!-- <div class="content-wrapper mb-2">
                        <app-lab-results [submitted]="submitted" [calledFromParent]="true" #LabResults (downloadLabResults)="onDownloadLabResults()"></app-lab-results>
                    
                    </div>  -->
        </div>
        <div *ngIf="activeStageIndex == 9">
          <h6 class="mb-3">Other Investigations</h6>
          <div class="content-wrapper mb-2">
            <form [formGroup]="procedureForm">
              <div class="text-right pb-2">
                <button *ngIf="showDownload" type="button" class="btn btn-sm btn-primary" (click)="addProcedure()">
                  Add New
                </button>
                <button type="button" *ngIf="showDownload && (!procedureList || procedureList.length === 0)"
                  class="btn btn-sm btn-primary" (click)="openModalInfo(addInstituteModal,'investigation')">
                  Download
                </button>
                <button *ngIf="(procedureList && procedureList.length > 0)" class="btn btn-sm btn-primary"
                  (click)="deleteAllProcedure()">
                  Delete All
                </button>
              </div>
              <div *ngIf="showOtherInvestigationsTable">
                <p-dataTable [value]="procedureTableList" [responsive]="true">
                  <p-column field="procedure" header="Test Name"></p-column>
                  <p-column field="doneDate" header="Test Date">
                    <ng-template let-col let-row="rowData" let-rowIndex="rowIndex" pTemplate="body">
                      {{ row.doneDate | date : "dd-MM-yyyy" }}
                    </ng-template>
                  </p-column>
                  <p-column field="result" header="Result"></p-column>
                </p-dataTable>
              </div>
              <div *ngIf="!showOtherInvestigationsTable">
                <p-dataTable [immutable]="false" [value]="procedureList" [editable]="true" dataKey="id"
                  [responsive]="true" [paginator]="true" [rows]="10" [showCurrentPageReport]="true">
                  <!-- Done Date -->

                  <!-- Procedure -->
                  <p-column field="procedure" header="Test Name" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="procedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ getOtherDonorLabListName(row.procedure) }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <ng-select [items]="donorOtherLabListing" bindLabel="value" bindValue="id"
                              formControlName="procedure" placeholder="Select Test">
                            </ng-select>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                  <p-column field="doneDate" header="Test Date" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="procedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ row.doneDate | date : "dd-MM-yyyy" }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="doneDate"
                              [ngModelOptions]="{ standalone: true }" monthNavigator="true" [yearRange]="yearRange"
                              yearNavigator="true" showButtonBar="true"></p-calendar>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                  <p-column field="result" header="Result" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="procedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.result }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="result"
                              placeholder="Enter result" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                  <p-column field="remarks" header="Remarks" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="procedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">{{ row.remarks }}</div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="remarks"
                              placeholder="Enter Remarks" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Actions -->
                  <p-column field="" header="Actions" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body">
                      <button (click)="onRowEditProcedureInit(row)" *ngIf="row.isEditable === false"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button (click)="onRowEditProcedureSave(row)" *ngIf="row.isEditable === true"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i>
                      </button>
                      <button (click)="removeProcedure(row)" class="btn btn-sm btn-danger">
                        <i class="fa fa-trash"></i>
                      </button>
                    </ng-template>
                  </p-column>
                </p-dataTable>
              </div>
            </form>
          </div>
        </div>
        <div *ngIf="activeStageIndex == 10">
          <h6 class="mb-3">Medical Procedure</h6>
          <div class="content-wrapper mb-2">
            <form [formGroup]="medicalProcedureForm">
              <div class="text-right pb-2">
                <button type="button" *ngIf="showDownload" class="btn btn-sm btn-primary"
                  (click)="addMedicalProcedure()">
                  Add New
                </button>

                <button type="button"
                  *ngIf="showDownload && (!medicalProcedureList || medicalProcedureList.length === 0)"
                  class="btn btn-sm btn-primary" (click)="openModalInfo(addInstituteModal,'medical')">
                  Download
                </button>
                <button *ngIf="(medicalProcedureList.length > 0)" class="btn btn-sm btn-primary"
                  (click)="deleteAllMedicalProcedure()">
                  Delete All
                </button>
              </div>

              <div *ngIf="showMedicalProcedureTable">
                <p-dataTable [value]="medicalProcedureTableList" [responsive]="true">
                  <p-column field="procedure" header="Procedure"></p-column>
                  <p-column field="doneDate" header="Procedure Date">
                    <ng-template let-col let-row="rowData" let-rowIndex="rowIndex" pTemplate="body">
                      {{ row.doneDate | date : "dd-MM-yyyy" }}
                    </ng-template>
                  </p-column>
                  <!-- <p-column  field="remarks" header="Remarks" class="truncate-text"></p-column> -->

                  <p-column field="remarks" header="Remarks">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="medicalProcedures">
                        <div [formGroupName]="rowIndex">
                          <div class="truncate-text">
                            {{ row.remarks }}
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                </p-dataTable>
              </div>
              <div *ngIf="!showMedicalProcedureTable">
                <p-dataTable [immutable]="false" [value]="medicalProcedureList" [editable]="true" dataKey="id"
                  [responsive]="true" [paginator]="true" [rows]="10" [showCurrentPageReport]="true">
                  <!-- Procedure -->
                  <p-column field="procedure" header="Procedure" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="medicalProcedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ getProcedureListName(row.procedure) }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <ng-select [items]="procedureListing" bindLabel="value" bindValue="id"
                              formControlName="procedure" placeholder="Select Procedure">
                            </ng-select>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                  <!-- Done Date -->
                  <p-column field="doneDate" header="Procedure Date"
                    [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="medicalProcedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable">
                            {{ row.doneDate | date : "dd-MM-yyyy" }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="doneDate"
                              [ngModelOptions]="{ standalone: true }" monthNavigator="true" [yearRange]="yearRange"
                              yearNavigator="true" showButtonBar="true"></p-calendar>
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>
                  <!-- Findings -->
                  <p-column field="remarks" header="Remarks" [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                      <ng-container formArrayName="medicalProcedures">
                        <div [formGroupName]="rowIndex">
                          <div *ngIf="!row.isEditable" class="truncate-text">
                            {{ row.remarks }}
                          </div>
                          <div *ngIf="row.isEditable">
                            <input type="text" class="form-control form-control-sm" formControlName="remarks"
                              placeholder="Enter Remarks" />
                          </div>
                        </div>
                      </ng-container>
                    </ng-template>
                  </p-column>

                  <!-- Actions -->
                  <p-column *ngIf="showActionsColumn" field="" header="Actions"
                    [style]="{ 'text-align': 'center', width: '54px' }">
                    <ng-template let-row="rowData" pTemplate="body">
                      <button (click)="onRowEditMedicalProcedureInit(row)" *ngIf="row.isEditable === false"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button (click)="onRowEditMedicalProcedureSave(row)" *ngIf="row.isEditable === true"
                        class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i>
                      </button>
                      <button (click)="removeMedicalProcedure(row)" class="btn btn-sm btn-danger">
                        <i class="fa fa-trash"></i>
                      </button>
                    </ng-template>
                  </p-column>
                </p-dataTable>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="content-wrapper mt-2">
    <div class="row">
      <div class="col-sm-12 text-right">
        <button *ngIf="showDownload" type="button" class="btn btn-secondary btn-sm" (click)="clearForm()">
          Clear
        </button>
        <button *ngIf="showDownload" type="button" class="btn btn-sm btn-primary" (click)="save()">
          Save
        </button>
      </div>
    </div>
  </div>
</form>
<ng-template #addInstituteModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Select Institute</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <form [formGroup]="deceasedDonorForm" (ngSubmit)="saveInstitute(modal)">
    <div class="modal-body">
      <div class="form-group">
        <label>Institute Name</label>

        <ng-select #entryPoint appendTo="body" [items]="hospitalsList" [virtualScroll]="true" placeholder="Select"
          bindLabel="estName" bindValue="estCode" formControlName="instCode_2">
          <ng-template ng-option-tmp let-item="item" let-index="index">{{
            item.estName
            }}</ng-template>
        </ng-select>
      </div>
    </div>
    <div class="modal-footer  mr-2">
      <button type="submit" class="btn btn-success" [disabled]="deceasedDonorForm.invalid">Fetch</button>
      <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    </div>
  </form>
</ng-template>