import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { GridOptions } from 'ag-grid-community';
import { Paginator } from 'primeng/primeng';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { DiabeticListResult } from 'src/app/_models/diabetic-list-result.model';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
 import Swal from 'sweetalert2';
import * as AppCompUtils from '../../common/app.component-utils';
import * as AppUtils from '../../common/app.utils';
import { DiabeticService } from '../diabeticService';



@Component({
  selector: 'app-diabetic-listing',
  templateUrl: './diabetic-listing.component.html',
  styleUrls: ['./diabetic-listing.component.scss']
})
export class DiabeticListingComponent implements OnInit {


  @ViewChild('diabeticPaginator', { static: false }) paginator: Paginator;
  diabeticSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  diabetsTypeMaster: any[];
  diabetesSubTypesList: any[];
  modeRepMaster: any[];
  ketonesList = AppCompUtils.KETONES;
  modeOfPrestList: any[];
  typeList:any[];
  yesNo = AppCompUtils.YES_NO;
  gender = AppCompUtils.GENDER;
  hba1cList=AppCompUtils.HBA1C_OPTION
  rowData: Array<DiabeticListResult> = new Array<DiabeticListResult>();
  rowFilteredData: Array<DiabeticListResult> = new Array<DiabeticListResult>();
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  columnDefs: any[];
  totalRecords:any;
  paginationSize:any=AppUtils.E_REGISTRY_PAGINATION_SIZE;
  showSubType:boolean=false;
  showInsulinType:boolean=false;
  criteria: any={};
  nationalityList: any[];
  // modeRepList: any[];
  currentDate:any =  new Date();
  institutesFilter: any[];
  institutes: any[];


  constructor(private _router: Router, private _masterService: MasterService, private _diabeticService: DiabeticService,
              private _sharedService: SharedService ,private formBuilder: FormBuilder,  public datepipe: DatePipe) {

    this.getMasterData();
    this.diabeticSearchForm =this.formBuilder.group({
      'civilId':  [null],
      'centralRegNo': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'sexCode': [null],
      'regCode': [null],
      'walCode':[null],
      'regInst': [null],
      'diabetesType':[null],
      'diabetesSubType':[null],
      'regionData': [null],
      'regDateFrom': [null],
      'regDateTo': [null],
      'nationality':[null],
      'modePresentaion':[null],
      'regType': [null],
      'insulin': [null],
      'injGlpiAgonist': [null],
      'metabolicProcedure': [null],
      'oralHypoDrugs': [null],
      'lifestyleModify': [null],
      'insulinType': [null],
      'bp': [null],
      'bmi': [null],
      'footUlcer': [null],
      'sensoryNeuropathy': [null],
      'retinopathy': [null],
      'lowerLimpAmputation': [null],
      'cvd10Year': [null],
      'ldl': [null],
      'hba1c': [null],
      'egfr': [null],
      'urineAcr': [null],
      'fileStatus': [null],
      'diaDateFrom':[null],
      'diaDateTo':[null],
      'ketones' : [null],
      'patientId' : [null]


  });

  this.columnDefs = [
    { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125 ,sortable: true},
    { headerName: 'Civil ID', field: 'civilId', minWidth: 125 ,sortable: true},
    { headerName: 'Name', field: 'firstName', minWidth: 150,sortable: true },
    { headerName: 'Age', field: 'age', minWidth: 100 ,sortable: true},
    { headerName: 'Gender', field: 'sexDesc', minWidth: 100,sortable: true },
    { headerName: 'Nationality', field: 'nationality', minWidth: 100, sortable: true},
    { headerName: 'Diabtes Type', field: 'diabetesTypeValue', minWidth: 100,sortable: true },
    { headerName: 'Ketones', field: 'ketones', minWidth: 100,sortable: true },
    { headerName: 'HbA1c', field: 'hba1c', minWidth: 100,sortable: true },
    // { headerName: 'ModePresentaion', field: 'modePresentaion', minWidth: 100, sortable: true},
  ];
}

  ngOnInit() {
    this._masterService.getAllDiabetesPresMod();

  }

  get f() { return this.diabeticSearchForm.controls; }

  getMasterData(regCode: any = 0, walCode: any = 0) {

    this._masterService.getRegionsMaster();
    this._masterService.regionsMaster.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });
    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institutes = res['result'];
      this.institutesFilter = res['result'];
    })

    //  this._masterService.getInstitutesMaster();
    // this._masterService.institutesMaster.subscribe(value => {
    //   this.institeList = value;
    //   this.institeListFilter = this.institeList;
    // });

    this._masterService.getInstitutesMasterByUserRoles().subscribe(res=>{
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
     });

    this._masterService.getDiabetesTypes().subscribe(value => {
      this.diabetsTypeMaster = value["result"];
    });

    this._masterService.getAllDiabeticSubtypes().subscribe(res => {
      this.diabetesSubTypesList = res.result;
    });


 //   this._masterService.getAllDiabetesPresMod();
    this._masterService.getAllDiabetesPresMod().subscribe(value => {
      this.modeOfPrestList = value["result"];
    });

    this.getNationalityList();
  }

   /* ------------  get DropDownList ---------------- */
   isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }
  changeInstitute(obj) {

    if (obj && !obj.regCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];

    }
  }
  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.diabeticSearchForm.value['regCode'] && (this.diabeticSearchForm.value['regCode'] != null || this.diabeticSearchForm.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.diabeticSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.diabeticSearchForm.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.diabeticSearchForm.value['regCode'] && (this.diabeticSearchForm.value['regCode'] != null || this.diabeticSearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.diabeticSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.diabeticSearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }

  getDiabetesPresMod()
  {
    this._masterService.getAllDiabetesPresMod().subscribe(res => {
    this.modeRepMaster= res.result;
    });
  }




  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationalityList = response.result;
    }, error => {
    });
  }


  /* ------------  filter action   ---------------- */
   getList(event?:any) {
    let body = this.diabeticSearchForm.value;
    let pageable = {
      page: event?event.page:0,
      size: event?event.rows:AppUtils.E_REGISTRY_PAGINATION_SIZE
    }

    if ( body['sexCode'] == "") {
      body['sexCode'] = null;
    }

    if ( body['patientId'] == "") {
      body['patientId'] = null;
    }

    if ( body['diabetesType'] == "") {
      body['diabetesType'] = null;
    }

    // body['regDateFrom'] = body['regDateFrom']? this.datepipe.transform(body['regDateFrom'], 'dd-MM-yyyy'):null;
    // body['regDateTo'] = body['regDateTo']? this.datepipe.transform(body['regDateTo'], 'dd-MM-yyyy'):null;
    body['lifestyleModify'] = body['lifestyleModify']? 'Y':null;
    body['oralHypoDrugs'] = body['oralHypoDrugs']? 'Y':null;
    body['metabolicProcedure'] = body['metabolicProcedure']? 'Y':null;
    body['injGlpiAgonist'] = body['injGlpiAgonist']? 'Y':null;
    body['footUlcer'] = body['footUlcer']? 'Y':null;
    body['sensoryNeuropathy'] = body['sensoryNeuropathy']? 'Y':null;
    body['retinopathy'] = body['retinopathy']? 'Y':null;
    body['lowerLimpAmputation'] = body['lowerLimpAmputation']? 'Y':null;
    body['insulin'] = body['insulin']? 'Y':null;
    body['hba1c'] = body['hba1c'];

    if (event) {
      body["startIndex"] = event.first;
      body["rowsPerPage"] = event.rows
    } else {
      if (this.paginator) { this.paginator.first = AppUtils.INDEX; }

      body["startIndex"] = AppUtils.INDEX;

      if (body["rowsPerPage"] == null || body["rowsPerPage"] == typeof ('undefined')) {
        body["rowsPerPage"] = AppUtils.E_REGISTRY_PAGINATION_SIZE;
      }
    }

    body = {pageable, ...body}
    this._diabeticService.getDiabeticListing(body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result']['content'];
        this.totalRecords = res['result']['totalElements'] ;

        this.criteria = body;
        for (var i = 0; i < this.rowData.length; i++) {
         if(this.rowData[i].diabetesType != null){
          this.rowData[i].diabetesTypeValue = this.diabetsTypeMaster.filter(s => s.id == this.rowData[i].diabetesType).map(s => s.value).toString();
         }
          this.rowData[i].sexCode = this.gender.filter(s => s.id == this.rowData[i].sexCode).map(s => s.value).toString();
        }
      } else {
        this.rowData = null;
        Swal.fire('Error!', res['message'], 'error')
      }
    }, error => {
      Swal.fire("Error!", "Error occurred while retrieving user details", "error");
    });
  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['diabetic/registry'], { state: { centralRegNo: event.data.centralRegNo } });
  }

  clear(e) {
    this.diabeticSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;
    this.showSubType = false;
    this.showInsulinType = false;
    this.criteria = null;
  }

  exportToExcel() {
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
        this._sharedService.exportAsExcelFile(this.rowData, "Diabetic_Listing");
      } else {
        this._diabeticService.getDiabeticListing(this.criteria).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['paginatedList'];
            if (excelData && excelData.length > 0) {
              this._sharedService.exportAsExcelFile(excelData, "Diabetic_Listing");
            }
          }
        });
      }
    } else {
      Swal.fire('Warning!', 'Please search first', 'warning')
    }
  }

  onChange(event,type){
    if(event && event['id'] == AppUtils.DIABETIC_SPECIFIC_TYPE)
      this.showSubType = true;
    else{
      this.diabeticSearchForm.controls['diabetesSubType'].setValue(null);
      this.showSubType = false;
    }
  }

  onClick(event,type){
    if(event && event.target.checked)
      this.showInsulinType = true;
    else{
      this.diabeticSearchForm.controls['insulinType'].setValue(null);
      this.showInsulinType = false;
    }
  }

  pickValues(event: any) {
    if (event.id){
    let id = event.id;

    }

  }

  showDashboard(){
    this._sharedService.setNavigationData(this.rowData);
    this.criteria["startIndex"] = AppUtils.INDEX;
    this.criteria['rowsPerPage'] = this.totalRecords;
    this._sharedService.setCriteria(this.criteria);
    this._router.navigate(['diabetic/home'], { state: null });
  }

  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }
}
