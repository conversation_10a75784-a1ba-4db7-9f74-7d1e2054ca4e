

// body {
//   font-family: "Poppins", Aria<PERSON>, sans-serif;
//   font-size: 16px;
//   line-height: 1.8;
//   font-weight: normal;
//   background: #f8f9fd;
//   color: gray;
// }

// a {
//   -webkit-transition: .3s all ease;
//   -o-transition: .3s all ease;
//   transition: .3s all ease;
//   color: #BDD6EE;
// }

// a:hover,
// a:focus {
//   text-decoration: none !important;
//   outline: none !important;
//   -webkit-box-shadow: none;
//   box-shadow: none;
// }

// h1,
// h2,
// h3,
// h4,
// h5,
// .h1,
// .h2,
// .h3,
// .h4,
// .h5 {
//   line-height: 1.5;
//   font-weight: 400;
//   font-family: "Poppins", Arial, sans-serif;
//   color: #000;
// }

.bg-primary {
  background: #BDD6EE !important;
}

.ftco-section {
  padding: 7em 0;
}

.ftco-no-pt {
  padding-top: 0;
}

.ftco-no-pb {
  padding-bottom: 0;
}

.heading-section {
  font-size: 28px;
  color: #000;
}

.img {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.table-wrap {
  overflow-x: auto;
}

.table {
  min-width: 1000px !important;
  width: 100%;
  background: #fff;
  -webkit-box-shadow: 0px 5px 12px -12px rgba(0, 0, 0, 0.29);
  -moz-box-shadow: 0px 5px 12px -12px rgba(0, 0, 0, 0.29);
  box-shadow: 0px 5px 12px -12px rgba(0, 0, 0, 0.29);
}

.table thead.thead-primary {
  background: #BDD6EE;
}

.table thead th {
  // border: none;
  padding: 10px;
  font-size: 15px;
  font-weight: 900;
  color: black;
}

.table tbody tr {
  margin-bottom: 10px;
}

.table tbody th,
.table tbody td {
  // border: none;
  padding: 10px;
  font-size: 14px;
  // background: #fff;
  border-bottom: 4px solid #f8f9fd;
  vertical-align: middle;
}

.table tbody td.quantity {
  width: 10%;
}

.table tbody td .img {
  width: 100px;
  height: 80px;
}

.table tbody td .email span {
  display: block;
}

.table tbody td .email span:last-child {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.3);
}

.table tbody td .close span {
  font-size: 12px;
  color: #dc3545;
}

.checkbox-wrap {
  display: block;
  position: relative;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox-wrap input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "\f0c8";
  font-family: "FontAwesome";
  position: absolute;
  color: rgba(0, 0, 0, 0.1);
  font-size: 20px;
  margin-top: -14px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (prefers-reduced-motion: reduce) {
  .checkmark:after {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

/* Show the checkmark when checked */
.checkbox-wrap input:checked~.checkmark:after {
  display: block;
  content: "\f14a";
  font-family: "FontAwesome";
  color: rgba(0, 0, 0, 0.2);
}

/* Style the checkmark/indicator */
.checkbox-primary {
  color: #BDD6EE;
}

.checkbox-primary input:checked~.checkmark:after {
  color: #BDD6EE;
}


/* exam background  #BDD6EE ==> #BDD6EE */

// table > thead > tr > th:nth-child(2) {
.table2 thead th:nth-child(2),
.table2 tbody td:nth-child(2) {
  background: #C5E0B3;
}

.table2 thead th:nth-child(3),
.table2 tbody td:nth-child(3) {
  background: #FFE599;
}

/* start p-dataTable style*/
.p-tables::ng-deep {  
  border-collapse: collapse !important;
  // border: 1px solid !important;
}

.p-tables::ng-deep tr {
  border-width: 3px !important;
  border-color: white !important;
}
// .p-tables::ng-deep th {
//   background: #BDD6EE !important;
//   padding: 10px !important;
//   font-size: 15px !important;
//   font-weight: 900 !important;
//   color: black !important;
//   text-align: left !important;
  
// }
//.p-tables::ng-deep th, 
.p-tables::ng-deep td  {
  padding:10px !important;
}
//.p-tables::ng-deep th:nth-child(2),
.p-tables::ng-deep td:nth-child(2) {
  background-color: #C5E0B3 !important;

}

//.p-tables::ng-deep th:nth-child(3),
.p-tables::ng-deep td:nth-child(3) {
  background-color: #FFE599 !important;
}
/* end p-dataTable style*/


#spaceq{
  margin-right: 7px;
}

#spacer{
  margin-right: 7px;
}


#spacef{
  margin-right: 7px;
}

.inline-block{
  display: inline-block;
}

#spacic{
  padding-top: 20px;
}


.input-6{
  .col-md-6:first-child{
      padding-right:0;
  }
  
}
.remarks{
  margin-top: 5px;
}

.form-control, label{
  font-size: 0.875rem;
}
textarea{
  // resize: none;
  font-size: 0.813rem;
}


#secondSpace{
  padding-right: 200px;

}

.custom-table{
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #e9e9e9;

  thead{
    th{
      padding: 5px;
      background: #ebebeb;

    }
  }
  tbody{
    tr{
      td{
        padding: 5px;
        border: 1px solid #e9e9e9;
        cursor: pointer;
      }

      &:hover{
        td{
          background: #f6f6f6;
        }
      }
    }
  }
}


#spacei{
 padding-right: 50px;
}




.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  width: 33.33333%;
}

.custom-input{
  display: inline-block;
  width: auto;  
  margin-left: 15px;
}
.input-md{
  width: 28%;
}
.input-sm{
  width: 20%;
}
.input-lg{
  width: 50%;
}
.exam .custom-input{
  margin: 0;
}

.table-row-active{
  tr.active{
    td{
      background: #dddddd;
    }
  }
}