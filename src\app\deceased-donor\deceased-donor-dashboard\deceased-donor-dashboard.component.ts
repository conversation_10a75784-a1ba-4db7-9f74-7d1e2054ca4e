import { Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { NgSelectComponent } from '@ng-select/ng-select';
import { DeceasedDonorService } from '../deceased-donor.service';  
import { RenalDashboard } from '../../_models/renal-dashboard.model';
import { RenalDashboardDisplay } from '../../_models/renal-dashboard-display.model';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from '../../_helpers/common.constants';
import { data } from 'jquery';
import { DeceasedDonorDashboard, DeceasedDonorDashboardDisplay } from 'src/app/_models/deceased-donor.model';
import { formatDate } from '@angular/common';
import * as moment from 'moment';
import { DateFilterUI } from '@syncfusion/ej2-angular-grids';

@Component({
  selector: 'app-deceased-donor-dashboard',
  templateUrl: './deceased-donor-dashboard.component.html',
  styleUrls: ['./deceased-donor-dashboard.component.scss']
})
export class DeceasedDonorDashboardComponent implements OnInit {

  yearlyDeathCountChart: any;
  yearlyDeathCountByRegionChart: any;
  yearlyDeathCountByWilayatChart: any;
  selectedChartView: string = 'yearly'; // Default view
  cancerByRegionChart: any;
hypertensionByRegionChart: any;
diabetesByRegionChart: any;
allergyByRegionChart: any;
systemicAutoimmuneByRegionChart: any;

  boardForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: WallayatDataModel[];
  wallayatListFilter: WallayatDataModel[];
  institeList: any[];
  institeListFilter: any[];
  regCodeSelected: any;
  walCodeSelected: any;
  estCodeSelected: any;
  DashboardDataDB: DeceasedDonorDashboard[];
  DashboardDataFilter: DeceasedDonorDashboard[];
  displayICDPatients: DeceasedDonorDashboardDisplay[];
  displayBrainDeathCause: DeceasedDonorDashboardDisplay[];
  displayTransplantPatients: DeceasedDonorDashboardDisplay[];
  filterType: any;
  filterTitle: any;
  pieOption: any;
  options: any;
  charBGColor: any[];
  KidneyPatientsChart: any;
  dialysisPatientsChart: any;
  transplantPatientsChart: any;
  createTime: any;
  displayDialysisPatients: any[];
  brainDeathIcdList: { id: any; value: any; }[];
  brainDeathCauseChart: any;
  medicalConditionsByRegionChart: any;
  barOptions: any;
  constructor(private _masterService: MasterService, private _sharedService: SharedService, private formBuilder: FormBuilder, private deceasedDonorService: DeceasedDonorService) {
    this.callFrom();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit(
    
  ) {
    this.loadBrainDeathIcdList();
  }

  

  callFrom() {
    this.boardForm = this.formBuilder.group({
      'regCode': [null],
      'walCode': [null],
      'estCode': [null],
      'ageF': [null],
      'ageT': [null],
    });

  }


  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {
    });

    //this.loadBrainDeathIcdList();

  }

  loadBrainDeathIcdList(): void {
    this.deceasedDonorService.getBrainDeathIcdList().subscribe((data: any[]) => {
      this.brainDeathIcdList = data.map((item) => ({
        id: item.icd,
        value: item.disease,
      }));
    });
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }


  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
  /* ------------  filter action   ---------------- */
  getDashboardData() {
    this.deceasedDonorService.getDashboard().subscribe(res => {

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      if (res['code'] == "S0000" || res['code'] == "F0000") {
       // console.log("---res---", res['result']);
        
        this.DashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'DECEASED', this.institeList);
      }
      
      this.callFilter();
    })

  }

  callReset() {
    window.location.reload();
  }

  callFilter() {
    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
      this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    } else {



      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['estCode'] != undefined || body['estCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.instCode == body['estCode']);
        this.filterType = 'institute';
      } else if (body['walCode'] != undefined || body['walCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.walCode == body['walCode']);
        this.filterType = 'wilayat';
      } else if (body['regCode'] != undefined || body['regCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.regCode == body['regCode']);
        this.filterType = 'region';
      } else {
        this.filterType = 'all';
      }
      this.displayFilterType();
      this.setChartData();
    }
  }

displayFilterType(){
  
  if (this.filterType === "institute") {
    this.filterTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['instCode']).map(s => s.estName);
    
  } else if (this.filterType === "wilayat") {
    this.filterTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
 
  } else if (this.filterType === "region") {
    this.filterTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);

  }
  else {
    this.filterTitle = 'All Regions';
  }

}
  setChartData() {
    // this.displayICDPatients = [];
    // this.displayDialysisPatients = [];
    // this.displayTransplantPatients = [];

    // for (var i = 0; i < this.DashboardDataFilter.length; i++) {
    //   let kin = { crystalNo: this.DashboardDataFilter[i].crystalNo, value: this.DashboardDataFilter[i].initalDiag };
    //   this.displayICDPatients.push(kin);

    //   this.displayDialysisPatients.push({ crystalNo: this.DashboardDataFilter[i].crystalNo, value: this.DashboardDataFilter[i].brainDeathCause });

    //   let tra = { crystalNo: this.DashboardDataFilter[i].crystalNo, value: this.DashboardDataFilter[i].cancerYn };
    //   this.displayTransplantPatients.push(tra);
    // }
    this.callChart();
  }

  callChart() {
    //console.log("this.DashboardDataFilter", this.DashboardDataFilter);  
    this.generateYearlyDeathCountByRegionChart();
    this.generateBrainDeathCauseChart();
    this.generateMedicalConditionsByRegionChart();
    //this.generateIndividualConditionsByRegionCharts(); 

  }

  callChartKidneyPatients() {
    let charlabels = [];
    let charData = [];
    let listGroup = [];

    //let chartArray: any[];

    //chartArray = this.DashboardDataFilter;
    this.displayICDPatients = this.displayICDPatients.filter(s => s.value != null);


    for (var n = 0; n < this.displayICDPatients.length; n++) {

      if (listGroup.filter(s => s.icd === this.displayICDPatients[n].value).length == 0) {
        const result = this.displayICDPatients.filter(s => s.value == this.displayICDPatients[n].value).length;
        let a = { icd: this.displayICDPatients[n].value }
        charlabels.push(this.displayICDPatients[n].value);
        charData.push(result);
        listGroup.push(a);
      }
    }
    /* for (var n = 0; n < this.DashboardDataFilter.length; n++) {
 
       if (listGroup.filter(s => s.icd === this.DashboardDataFilter[n].icd).length == 0) {
         const result = this.DashboardDataFilter.filter(s => s.icd == this.DashboardDataFilter[n].icd).length;
         let a = { icd: this.DashboardDataFilter[n].icd }
         charlabels.push(this.DashboardDataFilter[n].icd);
         charData.push(result);
         listGroup.push(a);
       }
     }
 */


    this.charBGColor.sort(() => Math.random() - 0.2);
    this.KidneyPatientsChart = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: charData,
        }

      ]
    }



  }

medicalBarchartOptions() {
  this.barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: false,
        title: {
          display: true,
          text: 'Regions'
        }
      },
      y: {
        stacked: false,
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Patients'
        },
        ticks: {
          precision: 0 // Only show whole numbers
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.raw || 0;
            return `${label}: ${value}`;
          }
        }
      }
    }
  };
}


  callPieChart(listData: any[], chartData: any) {
    let data = listData;
    let groupByName = [];
    let id = {};

    let reg = this.regionData;
    let wal = this.wallayatList;
    let est = this.institeList;

    let charTitle: any = "";

    if (this.filterType === "institute") {
      // charTitle = ' ' + this.institeList.filter(s => s.estCode == this.boardForm.value['estCode']).map(s => s.estName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.label == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "wilayat") {
      // charTitle = 'All Institute under ' + this.wallayatList.filter(s => s.walCode == this.boardForm.value['walCode']).map(s => s.walName);
      data.forEach(function (a) {
        id = { "id": est.filter(s => s.estCode == a.estCode).map(s => s.estCode), "label": est.filter(s => s.estCode == a.estCode).map(s => s.estName), "count": data.filter(s => s.estCode == a.estCode).length };
        if (groupByName.filter(s => s.id == a.estCode).length == 0) {
          groupByName.push(id);
        }
      })
    } else if (this.filterType === "region") {
      // charTitle = 'All Wilayat under ' + this.regionData.filter(s => s.regCode == this.boardForm.value['regCode']).map(s => s.regName);
      data.forEach(function (a) {
        id = { "id": wal.filter(s => s.walCode == a.walCode).map(s => s.walCode), "label": wal.filter(s => s.walCode == a.walCode).map(s => s.walName), "count": data.filter(s => s.walCode == a.walCode).length };
        if (groupByName.filter(s => s.id == a.walCode).length == 0) {
          groupByName.push(id);
        }
      })
    }
    else {
      // charTitle = 'All Regions';
      data.forEach(function (a) {
        id = { "id": reg.filter(s => s.regCode == a.regCode).map(s => s.regCode), "label": reg.filter(s => s.regCode == a.regCode).map(s => s.regName), "count": data.filter(s => s.regCode == a.regCode).length };
        if (groupByName.filter(s => s.id == a.regCode).length == 0) {
          groupByName.push(id);
        }
      })
    }


    this.pieOption.title = {
      display: true,
      text: charTitle,
      fontSize: 15
    }



    this.charBGColor.sort(() => Math.random() - 0.3);

    if (chartData == "this.dialysisPatientsChart") {
      this.dialysisPatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }
    if (chartData == "this.transplantPatientsChart") {
      this.transplantPatientsChart = {
        labels: groupByName.map(s => s.label),
        datasets: [
          {
            data: groupByName.map(s => s.count),
            backgroundColor: this.charBGColor,
            hoverBackgroundColor: this.charBGColor,
          }]
      };
    }



  }
  /* ------------  call Chart ---------------- */


  generateYearlyDeathCountByRegionChart() {
    // Group data by region and year
   
    const regionYearCounts = {};

    //console.log("this.DashboardDataFilter", this.DashboardDataFilter);
    
    this.DashboardDataFilter.forEach(record => {
      //console.log("record", record);
      
      if (record.brianDeathTime != null && record.regCode!=null) {
        
        const year = new Date(record.brianDeathTime).getFullYear();
        const regionName = this.getRegionName(record.regCode);
        
        //console.log("regionName", regionName);
        //console.log("year", year);

        if (!regionYearCounts[regionName]) {
          regionYearCounts[regionName] = {};
        }
        
        if (!regionYearCounts[regionName][year]) {
          regionYearCounts[regionName][year] = 0;
        }
        
        regionYearCounts[regionName][year]++;
      }
    });
    
    // Sort years chronologically
    const allYears = new Set();
    Object.values(regionYearCounts).forEach(yearData => {
      Object.keys(yearData).forEach(year => allYears.add(year));
    });
    const years = Array.from(allYears).sort();
    
    // Create datasets for each region
    const datasets = Object.keys(regionYearCounts).map((regionName, index) => {
      const color = this.charBGColor[index % this.charBGColor.length];
      return {
        label: regionName,
        backgroundColor: color,
        borderColor: color,
        data: years.map(year => regionYearCounts[regionName][year] || 0)
      };
    });
    
    // Create chart data
    this.yearlyDeathCountByRegionChart = {
      labels: years,
      datasets: datasets
    };
  }

  getRegionName(regCode: number): string {
    if (!this.regionData) return 'Unknown Region';
    const region = this.regionData.find(r => r.regCode === regCode);
    return region ? region.regName : 'Unknown Region';
  }


  formatDate(dateString: Date, format: string = 'dd-MM-YYYY'): string {
    if (!dateString) return '';
    return moment(dateString).format(format);
  }

  generateBrainDeathCauseChart(): void {
    // Check if ICD list is loaded
    if (!this.brainDeathIcdList || this.brainDeathIcdList.length === 0) {
      console.warn('Brain death ICD list not loaded yet');
      return;
    }
    
    // Group data by brain death cause
    const causeCounts = {};
    const causeNames = {};
    
    this.DashboardDataFilter.forEach(record => {
      if (record.briainDeathCause) {
        // Find the disease name from the ICD code
        const icdCode = record.briainDeathCause;
        const icdItem = this.brainDeathIcdList.find(item => item.id === icdCode);
        
        // Use the disease name if found, otherwise use the code
        const causeName = icdItem ? icdItem.value : `Code: ${icdCode}`;
        
        // Store the mapping for later use
        causeNames[icdCode] = causeName;
        
        // Count occurrences
        if (!causeCounts[icdCode]) {
          causeCounts[icdCode] = 0;
        }
        causeCounts[icdCode]++;
      }
    });
    
    // Prepare data for the chart
    const icdCodes = Object.keys(causeCounts);
    const labels = icdCodes.map(code => causeNames[code]);
    const data = icdCodes.map(code => causeCounts[code]);
    
    // Sort data by count (descending)
    const combined = icdCodes.map((code, index) => ({
      code,
      label: labels[index],
      count: data[index]
    }));
    
    combined.sort((a, b) => b.count - a.count);
    
    // Create chart data
    this.charBGColor.sort(() => Math.random() - 0.2);
    this.brainDeathCauseChart = {
      labels: combined.map(item => item.label),
      datasets: [
        {
          label: 'Brain Death Causes',
          backgroundColor: this.charBGColor,
          borderColor: '#1E88E5',
          data: combined.map(item => item.count),
        }
      ]
    };
  }
  generateMedicalConditionsByRegionChart(): void {
    if (!this.DashboardDataFilter || this.DashboardDataFilter.length === 0 || !this.regionData) {
      console.warn('No data available for medical conditions by region chart');
      return;
    }
    
    // Define the conditions we want to track
    const conditions = [
      { key: 'cancer', label: 'Cancer' },
      { key: 'hypertension', label: 'Hypertension' },
      { key: 'diabetes', label: 'Diabetes' },
      { key: 'allergy', label: 'Allergy' },
      { key: 'systemicAutoimmuneDisease', label: 'Systemic Autoimmune' }
    ];
    
    // Get unique regions from the data
    const regionCodes = [...new Set(this.DashboardDataFilter.map(record => record.regCode))];
    const regionNames = regionCodes.map(code => this.getRegionName(code));
    
    // Count "Yes" responses for each condition by region
    const datasets = conditions.map((condition, index) => {
      // For each region, count records where this condition is "Y"
      const regionData = regionCodes.map(regCode => {
        return this.DashboardDataFilter.filter(
          record => record.regCode === regCode && record[condition.key] === 'Y'
        ).length;
      });
      
      // Generate a color for this condition
      const color = this.charBGColor[index % this.charBGColor.length];
      
      return {
        label: condition.label,
        backgroundColor: color,
        borderColor: color,
        data: regionData
      };
    });
    
    // Create chart data
    this.medicalConditionsByRegionChart = {
      labels: regionNames,
      datasets: datasets
    };
  }


  generateIndividualConditionsByRegionCharts(): void {
    if (!this.DashboardDataFilter || this.DashboardDataFilter.length === 0 || !this.regionData) {
      console.warn('No data available for individual conditions by region charts');
      return;
    }
    
    // Generate chart for each condition
    this.cancerByRegionChart = this.generateConditionByRegionChart('cancer', 'Cancer');
    this.hypertensionByRegionChart = this.generateConditionByRegionChart('hypertension', 'Hypertension');
    this.diabetesByRegionChart = this.generateConditionByRegionChart('diabetes', 'Diabetes');
    this.allergyByRegionChart = this.generateConditionByRegionChart('allergy', 'Allergy');
    this.systemicAutoimmuneByRegionChart = this.generateConditionByRegionChart('systemicAutoimmuneDisease', 'Systemic Autoimmune Disease');
  }
  
  // Helper method to generate a chart for a specific condition by region
  generateConditionByRegionChart(conditionKey: string, conditionLabel: string): any {
    // Get unique regions from the data
    const regionCodes = [...new Set(this.DashboardDataFilter.map(record => record.regCode))];
    const regionNames = regionCodes.map(code => this.getRegionName(code));
    
    // Count "Yes" and "No" responses for this condition by region
    const yesData = regionCodes.map(regCode => {
      return this.DashboardDataFilter.filter(
        record => record.regCode === regCode && record[conditionKey] === 'Y'
      ).length;
    });
    
    const noData = regionCodes.map(regCode => {
      return this.DashboardDataFilter.filter(
        record => record.regCode === regCode && record[conditionKey] === 'N'
      ).length;
    });
    
    // Create chart data
    return {
      labels: regionNames,
      datasets: [
        {
          label: 'Yes',
          backgroundColor: '#4CAF50',
          borderColor: '#4CAF50',
          data: yesData
        },
        {
          label: 'No',
          backgroundColor: '#F44336',
          borderColor: '#F44336',
          data: noData
        }
      ]
    };
  }

}
