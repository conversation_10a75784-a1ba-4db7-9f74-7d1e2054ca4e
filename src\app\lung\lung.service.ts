import { Injectable } from '@angular/core';
import { SharedService } from '../_services/shared.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import * as AppUtils from '../common/app.utils';
import { Observable } from 'rxjs';
import { RenalDonorPatient } from '../_models/renal_donor_patient.model';
import { ResultDecorator } from '../_models/lungTransplant.model';

@Injectable({
  providedIn: 'root'
})
export class LungService {
  [x: string]: any;
  constructor(private _http: HttpClient) { }

  public getLungRegistry(regNo, civilId) {
    let params = new HttpParams();
    if (regNo !== undefined) {
      params = params.set('centralRegNo', regNo);
    }

    if (civilId !== undefined) {
      params = params.set('civilId', civilId);
    }
    return this._http.get(AppUtils.FIND_LUNG_REGISTRY, { params });
  }

  public saveLungRegistry(saveList) {
    return this._http.post(AppUtils.SAVE_LUNG_REGISTRY, saveList)
  }

  public deleteDiagnosis(runId) {
    return this._http.get(AppUtils.DELETE_LUNG_DIAGNOSIS, { params: new HttpParams().set("runId", runId) })
  }

  public getLungTransplantList(searchBody) {
    return this._http.post(AppUtils.GET_LUNG_TRANSPLANT_LIST, searchBody);
  }

  getLiverTransplant(regNo, regType, civilId) {
    return this._http.get(AppUtils.GET_LUNG_TRANSPLANT, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_LUNG.toString()).set("civilId", civilId)
    })
  }

  saveLiverTransplant(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LUNG_TRANSPLANT, data);
  }

  getLungListing(date): Observable<any> {
    return this._http.post(AppUtils.SEARCH_LUNG, date);
  }

  getComplications(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_COMPLICATION_MAST);
  }

  getLiverProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_PROCEDURE_MAST);
  }
  getLiverDonorProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_DONOR_PROCEDURE_MAST);
  }
  getCurrentManagement(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_CURR_MGMT_MAST);
  }

  getCaseDetails(regNo) {
    return this._http.get(AppUtils.GET_CASE_DETAILS_LUNG, { params: new HttpParams().set("regNo", regNo) })
  }

  getGeneticMedicineList(): Observable<any> {
    return this._http.get(AppUtils.GET_GEN_MEDICINE_LIST);
  }

  saveMainCaseDetails(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LUNG_CASE_DETAILS, data);
  }

  fetchLabFromShifa(estCode: any, civilId: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_LAB_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);

  }

  fetchSurgeryFromShifa(estCode: any, civilId: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_SURGERY_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);

  }

  getLungRegistryMedicineInfo(estCode: any, patientId: any): Observable<any> {
    return this._http.get(AppUtils.GET_LUNG_MEDICINE_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  public fetchLungTransplantList(dto) {
    return this._http.post(AppUtils.GET_LUNG_TRANSPLANT_LIST, dto);
  }

  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.GET_LUNG_DASHBOARD);
  }

  getDonorDetails(Data, Type) {
    if (Type == 'civilId') {
      return this._http.get(AppUtils.REG_LUNG_DTL_BY_CIVIL_ID, { params: new HttpParams().set("civilId", Data) });
    } else {
      return this._http.get(AppUtils.REG_LUNG_DTL_BY_ID, { params: new HttpParams().set("kidneyDonorId", Data) });
    }
  }

  updateLungDonorPatient(donorID, relationType, regNo) {
    return this._http.get(AppUtils.UPDATE_LUNG_DONOR_PATIENT, { params: new HttpParams().set("donorID", donorID).set("relationType", relationType).set("regNo", regNo) })
  }

  getCompareHlaScore(regNo, donorId) {
    return this._http.get(AppUtils.GET_COMPARE_HLA_SCORE, { params: new HttpParams().set("regNo", regNo).set("donorId", donorId) })
  }

  saveLungDonorPatient(data: RenalDonorPatient): Observable<any> {
    return this._http.post(AppUtils.SAVE_LUNG_DONOR_PATIENT, data);
  }

  getPatientsDonorbyRelationType(relationType: number, kidneyDonorId: number): Observable<any> {
    return this._http.get(AppUtils.GET_PATIENTS_DONOR_BY_RELATION_TYPE, {
      params: new HttpParams().set("relationType", relationType.toString()).set("kidneyDonorId", kidneyDonorId.toString())
    });
  }

  saveTissueTypeList(data: any): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_LIST_RETURN_RUN_ID, data);
  }

  getAllLungDonorPatient(): Observable<any> {
    return this._http.get(AppUtils.GET_ALL_LUNG_DONOR_PATIENT);
  }

  fetchAllDonorLabFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LUNG_DONOR_LAB_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);

  }

  fetchAllDonorSurgeryFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LUNG_DONOR_SURGERY_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);
  }

  fetchAllDonorProcedureFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LUNG_DONOR_PROCEDURE_FROM_ALSHIFA + "?civilId=" + civilId + "&estCode=" + estCode);
  }

  saveHeartDonorRegistry(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HEART_DONOR_REGISTRY, data);
  }

  getPatientsDeceasedDonor(kidneyDonorId) {
    return this._http.get(AppUtils.GET_HEART_PATIENTS_DECEASED_DONOR, { params: new HttpParams().set("renalDonorId", kidneyDonorId) })
  }

  searchLungRegisterList(dto) {
    return this._http.post(AppUtils.FIND_LUNG_REGISTER_LIST, dto);
  }

  getDonorList(dto) {
    return this._http.post(AppUtils.FIND_LUNG_DONOR_LIST, dto);
  }

  fetchAllDonorVaccineFromShifa(civilId: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_VACCINE_FROM_ALSHIFA + "?civilId=" + civilId);

  }

  fetchVaccineFromShifa(estCode: any, civilId: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_VACCINE_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);
  }

  saveLungDonorRegistry(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LUNG_DONOR_REGISTRY, data);
  }

  saveHlaTyping(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_TYPING, data);
  }

  getHlaTyping(regNo): Observable<any> {
    return this._http.get(AppUtils.GET_HLA_TYPING + '?centralRegNo=' + regNo);
  }

  saveHlaUnacceptableHdr(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_ANIGENT_HDR, data);
  }

  getTissueScreening(regNo): Observable<any> {
    return this._http.get(AppUtils.GET_HLA_TISSUE_SCREENING + '?centralRegNo=' + regNo);
  }

  getTissueScreeningbyDonorId(donorId, donorType): Observable<any> {
    return this._http.get(AppUtils.GET_HLA_TISSUE_SCREENING_BY_DONOR_ID + '?donorId=' + donorId + '&donorType=' + donorType);
  }

  saveTissueScreening(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_TISSUE_SCREENING, data);
  }

}