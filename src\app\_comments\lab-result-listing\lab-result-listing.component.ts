import { Component, OnInit,EventEmitter,Output,Input } from '@angular/core';
import { MasterService } from '../../_services/master.service';
import { RegistryService } from '../../_services/registry.service';
import { labTestModel } from '../../common/objectModels/labTestModel';
import { HttpClient } from '@angular/common/http';
import * as AppUtils from '../../common/app.utils';
import Swal from 'sweetalert2';
import * as moment from 'moment';
import { SharedService } from '../../_services/shared.service';
import { FormArray } from '@angular/forms';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { formatDate } from '@angular/common';
import * as CommonConstants from '../../_helpers/common.constants';

@Component({
  selector: "app-lab-results",
  templateUrl: "./lab-result-listing.component.html",
  styleUrls: ["./lab-result-listing.component.scss"],
})
export class LabResultsComponent implements OnInit {
  @Input() showLabButton: boolean = true;
  @Input() showAddNewButton: boolean = true;
  @Output() downloadLabResults = new EventEmitter<void>();
  @Input() calledFromParent = false;
  results: any[];
  date: Date;
  tests: any[];
  rgTbLabTests: any = [];
  clonedResult: { [s: string]: any } = {};
  data: any = [];
  delRow: any;
  institutes: any[];
  labTest: any;
  code: any;
  labtestresult: labTestModel;
  today = new Date();
  labList: any[];
  centralRegNo: number;
  selectedinstitutes: any;
  selectedTest: any;
  selectedDate: any;
  labnewList: any[];
  labTestName: any[];
  labResultForm: FormGroup;
  loginId: any;
  currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");

  constructor(
    private _masterService: MasterService,
    private _sharedService: SharedService,
    private formBuilder: FormBuilder,
    private _http: HttpClient
  ) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;
    // this.getlabTest();
  }

  ngOnInit() {
    /* this.labResultForm = this.formBuilder.group({
       rgTbLabTests: this.formBuilder.array([this.formBuilder.group({
         rgTbLabTests: ''
       })
 
       ]),
     })
     
 */
    (this.labResultForm = this.formBuilder.group({
      rgTbLabTests: this.formBuilder.array([]),
    })),
      this._masterService.institiutes.subscribe((res) => {
        this.institutes = res.result;
      });
    this._masterService.getLabMaster().subscribe((res) => {
      this.labTest = res.result;
    });

    // this.getlabTest();
  }

  getlabTest() {
    if (this.calledFromParent) {
      //this.showDownloadButton
      this.downloadLabResults.emit();
    } else {
      this._masterService
        .getLabByRegNo(this.centralRegNo)
        .subscribe((response) => {
          this.labList = response.result;
        });
    }
  }

  addNew() {
    if (!this.labnewList) {
      //this.labnewList = [];
      this.onAddNewLabResult();
    } else {
      let labItem = {
        runId: null,
        testDate: null,
        mohTestCode: null,
        resultSummary: null,
        instCode: null,
        enteredBy: this.loginId,
        enteredDate: this.currentDate,
        instName: null,
        isEditable: true,
        mohTestName: null,
        source: "W",
      };
      //this.rgTbLabTests.push(this.createLabGrpItem(labItem));
      this.labnewList.push(labItem);

      //  this.rgTbLabTests.push(labItem);

      // //this.labnewList[this.labnewList.length - 1].isEditable = true;
    }
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
  }
  onRowEditSave(row: any) {
    let rowIndex = this.labnewList.indexOf(row);
    this.labnewList[rowIndex] = this.labResultForm.value.rgTbLabTests[rowIndex];
    let data = this.labnewList[rowIndex];

    if (!data.mohTestCode || !data.instCode ) {
      Swal.fire({
        title: "Warning",
        icon: "warning",
        text: "Please fill all required Lab fields (Test Name, Institute) before saving.",
        confirmButtonText: "Ok",
        confirmButtonColor: "#3085d6",
      });
      data.isEditable = true;
      return;
    }

    data.mohTestName = this.labTest
      .filter((s) => s.mohTestCode == data.mohTestCode)
      .map((s) => s.testName)[0];
    data.instName = this.institutes
      .filter((s) => s.estCode == data.instCode)
      .map((s) => s.estName)[0];
    data.testDate = moment(data.testDate, "DD-MM-YYYY").format();
    data.isEditable = false;
  }
  save(row: any) {
    /*
       row.instCode = this.selectedinstitutes;
    
       
        row.mohTestCode = this.selectedTest;
        let t = this.labTest.filter(r => r.id == row.mohTestCode).map(r => r.value);
        row.labNameList = this.labTest.filter(r => r.id == row.mohTestCode).map(r => r.value);
        row.estList = this.institutes.filter(r => r.estCode == row.instCode).map(r => r.estName);
    
        row.testDate = moment(this.selectedDate, "DD-MM-YYYY").format();
         row.isEditable = false;
    
      if (row.runId == null) {
        row.centralRegNo = 33;
        this._http.post(AppUtils.SAVE_LAB_TEST, row).subscribe(res => {
          if (res['code'] == 0) {
            Swal.fire('Saved!', 'lab Test has been Saved.', 'success')
          } else {
            Swal.fire('Error!', res['message'], 'error');
          }
        }, err => {
          Swal.fire('Error!', 'Error occured while saving lab Test' + err.message, 'error')
        })
      }else {
          this._http.post(AppUtils.UPDATE_LAB_RESULT, row).subscribe(res => {
            if (res['code'] == 0) {
              Swal.fire('Saved!', 'lab Test has been Saved.', 'success')
            } else {
              Swal.fire('Error!', res['message'], 'error');
            }
          }, err => {
            Swal.fire('Error!', 'Error occured while saving lab Test' + err.message, 'error')
          })
        }*/
  }

  delete(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.rgTbLabTests = this.labResultForm.get("rgTbLabTests") as FormArray;
        this.delRow = this.labnewList.indexOf(row);
        this.labnewList.splice(this.delRow, 1);
        this.rgTbLabTests.removeAt(this.delRow);
      }
    });
  }

  // runId,testDate, mohTestCode, resultSummary, instCode,instName,enteredBy, enteredDate,source, isEditable, mohTestName
  getDataFromAlshifa(sendingData: any = 0) {
    //this.labnewList = sendingData;

    sendingData.forEach((element) => {
      if (
        this.labTest.filter((s) => s.mohTestCode == element.mohTestCode)
          .length > 0
      ) {
        // let instcode = 20068;
        this.addNewLabResult(
          null,
          element.testDate,
          element.mohTestCode,
          element.resultSummary,
          element.instcode,
          element.orderedBy,
          element.releasedDt,
          "S",
          false
        );
      }
    });
  }
  getLabTestName(mohTestCode) {
    return (
      this.labTest.find((s) => s.mohTestCode === mohTestCode)["testName"] || "-"
    );
  }

  ///////////////////P DATA TABLE

  onAddNewLabResult() {
    this.addNewLabResult(
      null,
      null,
      null,
      null,
      null,
      this.loginId,
      this.currentDate,
      "W",
      false
    );
    this.labnewList[this.labnewList.length - 1].isEditable = true; //editable last entering row
  }

  addNewLabResult(
    runId: any = null,
    testDate: any = null,
    mohTestCode: any = null,
    resultSummary: any = null,
    instCode: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ): void {
    this.rgTbLabTests = this.labResultForm.get("rgTbLabTests") as FormArray;

    this.labnewList = Object.assign([], this.rgTbLabTests.value);
    const labItem: any = this.createLabResultItem(
      runId,
      testDate,
      mohTestCode,
      resultSummary,
      instCode,
      enteredBy,
      enteredDate,
      source,
      isEditable
    );
    this.rgTbLabTests.push(this.createLabGrpItem(labItem));

    this.labnewList.push(labItem);
    //this.labnewList[this.labnewList.length - 1].isEditable = true;
  }

  createLabGrpItem(labItem: any): FormGroup {
    return this.formBuilder.group(labItem);
  }

  createLabResultItem(
    runId: any = null,
    testDate: any = null,
    mohTestCode: any = null,
    resultSummary: any = null,
    instCode: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      //testDate: new Date(testDate),
      testDate: testDate,
      mohTestCode: mohTestCode,
      resultSummary: resultSummary,
      instCode: instCode,
      enteredBy: enteredBy,
      enteredDate: enteredDate,
      source: source,
      isEditable: isEditable,
    };
  }

  getTestName(mohTestCode) {
    if (mohTestCode) {
      return this.labTest
        .filter((s) => s.mohTestCode == mohTestCode)
        .map((s) => s.testName)[0];
    }
  }

  getInstName(instCode) {
    if (instCode) {
      return this.institutes
        .filter((s) => s.estCode == instCode)
        .map((s) => s.estName)[0];
    }
  }

  ///////////////////P DATA TABLE

  clear() {
    this.labnewList = [];
    this.labResultForm.reset();

    this.labResultForm = this.formBuilder.group({
      rgTbLabTests: this.formBuilder.array([]),
    });
  }
}


