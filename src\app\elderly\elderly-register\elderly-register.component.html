<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Elderly Register</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>

<!-- <ngb-accordion #acc="ngbAccordion" activeIds="patientDetails"> -->
<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

    <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h6> Patient Details
                    <i class="col-lg-2 col-md-2 col-sm-2  fas fa-male " *ngIf="alive" (click)="updateDeathDetails()"
                        style="color: green; font-size:22px;" title="patient is alive "><span
                            style="padding-left: 5px;"></span></i>
                    <i class="col-lg-2 col-md-2 col-sm-2  fas fa-male " *ngIf="!alive"
                        style="color: black; font-size:22px;" title="patient is dead "><span
                            style="padding-left: 5px;"></span></i>
                </h6>

                <div>

                    <span style="padding-left: 5px; color: green; font-size:18px" class="fas" *ngIf="alive"
                        (click)="updateDeathDetails()">De-Activate</span>


                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>


            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <!--[patientForm]="patientForm" -->
            <app-patient-details [submitted]="submitted" [patientForm]="patientForm"
                (uploaded)="fetchVisitInfobyPatientID($event)" (callMethod)="callMpiMethod()" #patientDetails>
            </app-patient-details>
            <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Name</label>
                        <input type="text" class="form-control form-control-sm" formControlName="careGiverName">
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Tel</label>
                        <input type="number" class="form-control form-control-sm" formControlName="careGiverTel">
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3" [formGroup]="patientForm">
                    <div class="form-group">
                        <label>Care Giver Mobile</label>
                        <input type="number" class="form-control form-control-sm" formControlName="careGiverMobile">
                    </div>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
</ngb-accordion>




<ngb-accordion #acca="ngbAccordion" activeIds="ngb-panel-0" *ngIf="! alive">

    <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h6> Death Details</h6>
                <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                        [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <app-death-details [currentCivilId]="currentCivilId" [estCode]="estCode" [patientId]="patientId"
                #deathDetails>
            </app-death-details>

        </ng-template>
    </ngb-panel>
</ngb-accordion>


<div class="accordion register">

    <form [formGroup]="patientForm">

        <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">


            <ngb-panel id="vitalSigns">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Vital Signs </h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <table>
                        <thead>
                            <tr>
                                <th>Date (every 6 month)</th>
                                <th>BP</th>
                                <th>PULSE</th>
                                <th>TEMP.</th>
                                <th>Wt.</th>
                                <th>Ht.</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody formArrayName="vitals">
                            <tr [formGroupName]="i"
                                *ngFor="let product of  getControls() ; let i = index ; let last = last">
                                <!--    <td>
                            <input type="text" class="form-control" [id]="'productId' + i" placeholder="productId"  formControlName="productId">
                            <div *ngIf="product.get('productId').errors?.required &&
                          product.get('productId').touched">
                          ProductId is required


                  </div>
                        </td> -->
                                <td>
                                    <p-calendar appendTo="body" type="number" class="form-control  form-control-sm"
                                        dateFormat="dd-mm-yy" container="body" monthNavigator="true"
                                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
                                        formControlName="entryDate">
                                    </p-calendar>
                                </td>
                                <td>

                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="BPsrunID" />
                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="BPdrunID" />
                                    <div style="display: inline-block"><input class="form-control form-control-sm"
                                            style="width: 60px; display: inline-block" type="number"
                                            formControlName="BPs" />/
                                        <input class="form-control form-control-sm"
                                            style="width: 60px; display: inline-block" type="number"
                                            formControlName="BPd" />
                                    </div>

                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="PULSErunID" />
                                    <input class="form-control form-control-sm" type="number" formControlName="PULSE" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="TemprunID" />
                                    <input class="form-control form-control-sm" type="number" formControlName="Temp" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="WtrunID" />
                                    <input class="form-control form-control-sm" type="number" formControlName="Wt" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="hidden"
                                        formControlName="HtrunID" />
                                    <input class="form-control form-control-sm" type="number" formControlName="Ht" />
                                </td>
                                <td>
                                    <button *ngIf="last" class="btn btn-sm btn-default" type="button"
                                        (click)="addProductButtonClick()" title="Add New"><i
                                            class="fas fa-plus"></i></button>
                                    <button (click)="removeCompany(i)" *ngIf="!last" class="btn btn-sm btn-default"
                                        title="Delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                                <!--       <td>
                            <input type="text" class="form-control" [id]="'price' + i" placeholder="price"  formControlName="price">
                            <div *ngIf="product.get('price').errors?.required &&
                          product.get('price').touched">
                          price is required
                  </div>
                        </td> -->
                                <!--   <td>
                            <button (click)="removeCompany(i)" *ngIf="!last">Remove company</button>
                            <button type="button" (click)="addProductButtonClick()" *ngIf="last">Add</button>
                        </td> -->
                            </tr>
                        </tbody>
                    </table>
                </ng-template>
            </ngb-panel>
            <!--       <ngb-panel id="vitalSigns">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Vital Signs </h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    fieldArray : {{ fieldArray | json}}
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>BP</th>
                                <th>PULSE</th>
                                <th>TEMP.</th>
                                <th>Wt.</th>
                                <th>Ht.</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let field of fieldArray; let i = index">
                                <td>
                                    <input [(ngModel)]="field.date" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.date}}" />
                                </td>
                                <td>
                                    <input [(ngModel)]="field.bp" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.bp}}" />
                                </td>
                                <td>
                                    <input [(ngModel)]="field.pulse" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.pulse}}" />
                                </td>
                                <td>
                                    <input [(ngModel)]="field.temp" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.temp}}" />
                                </td>
                                <td>
                                    <input [(ngModel)]="field.weight" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.weight}}" />
                                </td>
                                <td>
                                    <input [(ngModel)]="field.height" class="form-control-plaintext form-control-sm"
                                        readonly type="text" name="{{field.height}}" />
                                </td>

                                <td>
                                    <button class="btn btn-sm btn-default" (click)="deleteFieldValue(i)"
                                        title="Delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <p-calendar type="number" class="form-control  form-control-sm" dateFormat="dd-mm-yy" container="body"
                                    monthNavigator="true" [maxDate]=today yearRange="1930:2030"  [(ngModel)]="newAttribute.date" yearNavigator="true" showButtonBar="true"
                                    (onSelect)="getAge($event)">
                                </p-calendar>
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="text" id="newAttributeBp"
                                        [(ngModel)]="newAttribute.bp" name="newAttributeBp" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="text" id="newAttributePulse"
                                        [(ngModel)]="newAttribute.pulse" name="newAttributePulse" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="text" id="newAttributeTemp"
                                        [(ngModel)]="newAttribute.temp" name="newAttributeTemp" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="text" id="newAttributeWeight"
                                        [(ngModel)]="newAttribute.weight" name="newAttributeWeight" />
                                </td>
                                <td>
                                    <input class="form-control form-control-sm" type="text" id="newAttributeHeight"
                                        [(ngModel)]="newAttribute.weight" name="newAttributeHeight" />
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-default" type="button" (click)="addFieldValue()"
                                        title="Add New"><i class="fas fa-plus"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </ng-template>
            </ngb-panel> -->





            <ngb-panel id="RandomBloodSugarEntry">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>RBS (Below Every Six Months)</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <table class="table table-striped table-bordered table-sm" formArrayName="randomBloodSugar">
                        <thead>
                            <tr>
                                <th>1st Visit</th>
                                <th>2nd Visit</th>
                                <th>3rd Visit</th>
                                <th>4th Visit</th>
                                <th>5th Visit</th>
                                <th>6th Visit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td *ngFor="let obj of randomBloodSugar.controls;let i=index" [formGroupName]="i">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                                    <input type="number" class="form-control form-control-sm"
                                        formControlName="valueNum">
                                    <div class="remarks"><textarea class="form-control" formControlName="valueText"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <!--   <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="secondVisit">
                                    <div class="remarks"><textarea class="form-control" formControlName="secondVisitRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="thirdVisit">
                                    <div class="remarks"><textarea class="form-control" formControlName="thirdVisitRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="fourthVisit">
                                    <div class="remarks"><textarea class="form-control" formControlName="fourthVisitRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="fifthVisit">
                                    <div class="remarks"><textarea class="form-control" formControlName="fifthVisitRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="sixthVisit">
                                    <div class="remarks"><textarea class="form-control" formControlName="sixthVisitRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>
                </ng-template>
            </ngb-panel>

            <ngb-panel id="MentalState">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Mental State</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <table class="table table-striped table-bordered table-sm" formArrayName="mentalState">
                                <thead>
                                    <tr>
                                        <th colspan="3" class="text-center">MMME</th>
                                    </tr>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>2nd Visit</th>
                                        <th>3rd Visit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of mentalState.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!-- <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="secondYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="secondYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="thirdYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="thirdYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <table class="table table-striped table-bordered table-sm" formArrayName="depressionScale">
                                <thead>
                                    <tr>
                                        <th colspan="3" class="text-center">Depression</th>
                                    </tr>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>2nd Visit</th>
                                        <th>3rd Visit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of depressionScale.controls;let i=index"
                                            [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!--  <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="secondYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="secondYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="thirdYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="thirdYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel>

            <ngb-panel id="counting-inconsistence">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Inconsistence Assessment</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6" style="display: none;">
                            <table class="table table-striped table-bordered table-sm" formArrayName="countingMoney">
                                <thead>
                                    <tr>
                                        <th class="text-center">Counting Money</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of countingMoney.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!--   <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstVisit">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstVisitRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <table class="table table-striped table-bordered table-sm" formArrayName="incontinence">
                                <thead>
                                    <tr>
                                        <th class="text-center">Inconsistence Screening</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of incontinence.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!--   <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstVisit">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstVisitRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel>
            <ngb-panel id="SEAssessment">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Socioeconomic Enviormental Assessment </h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12" formArrayName="socioEconomics">
                            <table class="table table-striped table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>After 3yrs</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of socioEconomics.controls;let i=index" [formGroupName]="i">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control" placeholder="Remarks"
                                                    formControlName="valueText"></textarea></div>
                                            <div></div>
                                        </td>
                                        <!--   <td><input type="text" class="form-control form-control-sm" formControlName="thirdYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="thirdYearRemarks"
                                                placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel>
            <ngb-panel id="BradenScore">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Braden Risk of Bed Sores </h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <table class="table table-striped table-bordered table-sm" formArrayName="bradenScore">
                        <thead>
                            <tr>
                                <th>1st Visit</th>
                                <th>After 1yr</th>
                                <th>After 3yrs</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td *ngFor="let obj of bradenScore.controls;let i=index" [formGroupName]="i">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                                    <input type="number" class="form-control form-control-sm"
                                        formControlName="valueNum">
                                    <div class="remarks"><textarea class="form-control" placeholder="Remarks"
                                            formControlName="valueText"></textarea></div>
                                </td>
                                <!--  <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="firstYear">
                                    <div class="remarks"><textarea class="form-control" formControlName="firstYearRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" formControlName="thirdYear">
                                    <div class="remarks"><textarea class="form-control" formControlName="thirdYearRemarks"
                                            placeholder="Remarks"></textarea></div>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>
                </ng-template>
            </ngb-panel>
            <ngb-panel id="MovementandActivity">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Movement and Activity</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-sm-4">
                            <table class="table table-striped table-bordered table-sm" formArrayName="adl">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="text-center">ADL</th>
                                    </tr>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>1 year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of adl.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4">
                            <table class="table table-striped table-bordered table-sm" formArrayName="iadl">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="text-center">IADL</th>
                                    </tr>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>1 year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of iadl.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4">
                            <table class="table table-striped table-bordered table-sm" formArrayName="timeUp">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="text-center">Timed Up & Go</th>
                                    </tr>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>1 year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of timeUp.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel>

            <ngb-panel id="NutritionalAssessment">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Nutritional Assessment MNA</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <table class="table table-striped table-bordered table-sm"
                                formArrayName="nutritionAssessement">
                                <thead>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>After 1year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of nutritionAssessement.controls;let i=index"
                                            [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!--  <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstVisit">
                                            <div class="remarks"><textarea class="form-control"  formControlName="firstVisitRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel>

            <ngb-panel id="balanceAndGait">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6>Balance & Gait</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas "
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>
                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <table class="table table-striped table-bordered table-sm" formArrayName="gaitAndBalance">
                                <thead>
                                    <tr>
                                        <th>1st Visit</th>
                                        <th>1 1year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td *ngFor="let obj of gaitAndBalance.controls;let i=index" [formGroupName]="i">
                                            <input type="hidden" class="form-control form-control-sm"
                                                formControlName="runId">
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="valueNum">
                                            <div class="remarks"><textarea class="form-control"
                                                    formControlName="valueText" placeholder="Remarks"></textarea></div>
                                        </td>
                                        <!--    <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstVisit"> 
                                            <div class="remarks"><textarea class="form-control"  formControlName="firstVisitRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" formControlName="firstYear">
                                            <div class="remarks"><textarea class="form-control" formControlName="firstYearRemarks"
                                                    placeholder="Remarks"></textarea></div>
                                        </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>
            </ngb-panel> -->

        </ngb-accordion>
        <div class="btn-container">
            <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
            <button class="btn btn-sm btn-primary" (click)="saveRegister()">Submit</button>
        </div>
    </form>

</div>