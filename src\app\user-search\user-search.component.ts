import { Component, OnInit,ViewChild,EventEmitter,Output } from '@angular/core';
import {DataTableModule} from 'primeng/primeng';
import { HttpClient,HttpHeaders } from '@angular/common/http';
import * as AppUtils from '../common/app.utils';
import { FormGroup, FormControl, FormArray, Validators } from "@angular/forms";
import { UserMasterDataModel } from '../common/objectModels/user-master-model';
import { Establishment } from '../common/objectModels/EstablishmentModel';
import { RoleDataModel } from '../common/objectModels/role-model';
import { SelectItem ,MultiSelect} from 'primeng/primeng';
import { MasterDataService } from '../_services/app.master.service';
import { SaveUserDataModel } from '../common/objectModels/save-user-model';
import { SystemIdDataModel } from '../common/objectModels/system-id-model';
import { LoaderService } from '../loader/loader.service';
import { SortListPipe} from '../pipes/sort.list.pipe';
import * as CommonConstants from '../_helpers/common.constants';
import Swal from 'sweetalert2';
import { SharedService } from '../_services/shared.service';

// import fontawesome from 'fortawesome';
//import { element } from '@angular/core/src/render3';


// fontawesome.config = { autoReplaceSvg: false }

@Component({
  selector: 'user-search',  
  templateUrl: './user-search.component.html',
  providers: [SortListPipe]
})

export class UserSearchComponent implements OnInit {
  @Output() unAuthorizedEvent = new EventEmitter<boolean>();
  showSelected: boolean;
  myForm: FormGroup;
  usersList: Array<UserMasterDataModel> = new Array<UserMasterDataModel>();
  userSearchCriteria:UserMasterDataModel;
  instituteData: Establishment[] = [];
  assignedInst: Array<Establishment> = [];
  availableInst: Array<Establishment> = [];
  roleData: RoleDataModel[] = [];
  assignedRole: RoleDataModel[] = [];
  availableRole: RoleDataModel[] = [];
  staffRoles: SelectItem[];
  staffInst: SelectItem[];
  showuserview: boolean = false;
  userDetails: SaveUserDataModel;
  assignedInstitutes: any = [];
  assignedRoles: any = [];
  availableInstitutes: any = [];
  searchTerm: string;
  availableRoles: any = [];
  applayMessage: boolean = false;
  selectedInst: any;
  selectedRole: any;
  navigatedParam: any;
  selectedItem: any;
  systemIdData:SystemIdDataModel;
  assignedSystemId: Array<SystemIdDataModel> = [];
  isNewUser:boolean=false;
  // @ViewChild('fieldPersCode')
  fieldPersCode: any;
  institutes:[];
  userMinimumLevel=100;
  dropdownList = [];
  selectedItems = [];
  dropdownSettings = {};
  searchText:any='';
  availableFullRoles: any = [];
  totalRecords: number = 0;
  loggedInUserRoles:any=JSON.parse(localStorage.getItem(AppUtils.USER_ROLES));
  constructor(private http: HttpClient,public masterDataService: MasterDataService, public _sharedService: SharedService,private loaderService: LoaderService,private sortListPipe:SortListPipe) {

    this.myForm = new FormGroup({
      'personName': new FormControl(),
      'perscode': new FormControl(),
      'roles': new FormControl(),
      'institute': new FormControl(),
      'loginId': new FormControl()
    });

    this.masterDataService.getInstitute().subscribe(response => {
      this.instituteData = <any>response;
      this.availableInst = this.instituteData;
      let tempInst: Establishment[];
      this.staffInst = [];
      tempInst = <any>response;
      // for(let i=0;i<500;i++){
      //   this.staffInst.push({
      //     label: tempInst[i].estName, value: tempInst[i].estCode
      //   });
      // }
     
      tempInst.forEach(el => {
        this.staffInst.push({
          label: el.estName, value: el.estCode
        });
      });
    });

    this.masterDataService.getRole().subscribe(response => {
      this.roleData = response['result'];
      this.availableRole = this.roleData;
      let tempRoles: RoleDataModel[];
      this.staffRoles = [];
      tempRoles = response['result'];

      tempRoles.forEach(el => {
        this.staffRoles.push({
          label: el.name, value: el.id
        });
        
      });

 
      //Set User level 
    /*  for(let element of this.loggedInUserRoles){
        if(element.roleLevel!=null && element.roleLevel!=undefined && element.roleLevel < this.userMinimumLevel){
          this.userMinimumLevel=element.roleLevel;
        }
      }
        //Filter the available role > this.userMinimumLevel;Below check is for to check whether Central auth implimented or not, after implimenting we can remove
   /* if(this.userMinimumLevel!=100){
          this.availableRole = this.availableRole.filter(item => item.roleLevel && item.roleLevel > this.userMinimumLevel);
        }*/
    }); 
  }


  ngOnInit() {

    this.dropdownSettings = {
      singleSelection: false,
      idField: 'value',
      textField: 'label',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 2,
      allowSearchFilter: true,
      clearSearchFilter:true,
      searchPlaceholderText:'Search',
      maxWidth:'3500'
    };
  
  //Disabled as per the Request from BA
    //this.getUsers();
  }

  // onItemSelect(item: any) {
  //   console.log(item);
  // }
  // onSelectAll(items: any) {
  //   console.log(items);
  // }

  getUsers(event?:any) {
    // console.log('event ------ ',event);
    let param:any= null;
    if(event){
      param = '?pageNo=' + (event.page+1) + '&size=' + event.rows
    }else{
      param = '?pageNo=1' + '&size=' + 10
      this.totalRecords = 0;
    }
    
    let body = this.myForm.value;
    if(this.institutes){
      body['institute']=this.institutes.map(inst=>inst['value'])
    }
    if(body['loginId'] && body['loginId'].trim() !=""){
      body['loginId'] = body['loginId'].trim();
    }else{
      body['loginId'] = null;
    }
    if(body['perscode'] && body['perscode'].trim() !=""){
      body['perscode'] = body['perscode'].trim();
    }else{
      body['perscode'] = null;
    }

    let userSearchLink:any= null;
    if(param){
      userSearchLink = CommonConstants.SEARCH_USERS + param;
    }else{
      userSearchLink = CommonConstants.SEARCH_USERS;

    }

    this.isNewUser=false;
    body.systemId = CommonConstants.SYSTEM_ID;
    this.loaderService.display(true);
    this.http.post(userSearchLink, body).subscribe(response => {
      if (response['code'] == 0) {
          this.totalRecords = response['result'][0]["totalCount"]; 
                this.loaderService.display(false);
                this.usersList = this.sortListPipe.transform(response['result'],'name','ASC') as [UserMasterDataModel];
                this.usersList.forEach(row => {                 
                  row['instituteName'] = row.institute.map(inst=>inst.estName);
                  row['rolesName'] = row.roles.map(role=>role.name);
                });
      } else {
        this.loaderService.display(false);
        this.usersList = [];
        this.userDetails=null;
        this.showuserview = false;
        Swal.fire('Error!', response['message'], 'error')
      }
    }, error => {
      if (error.status == 401)
        this.showuserview = false;
        this.unAuthorizedEvent.emit(true);
        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    });

    
  }

  clear(e) {
        //this.userDetails=null;
        this.myForm.get("institute").patchValue([]);
        this.myForm.get("roles").patchValue([]);
        this.myForm.get("personName").patchValue(null);
        this.myForm.get("perscode").patchValue(null);
        this.myForm.get("loginId").patchValue(null);
        this.usersList=[];
        this.totalRecords = 0;
        this.userDetails=null;
        this.showuserview = false;
        //this.getUsers();
      /*   let dataSource = {
          getRows(params: any) { 
            params.successCallback([], 0);
          }
        }; */
  }

  addNewUser() {
    this.showuserview = false;
    this.isNewUser=true;
    this.userDetails = new SaveUserDataModel();
    this.moveAllInstToR();
    this.moveAllRoleToR();
    setTimeout(() => this.fieldPersCode.nativeElement.focus(), 0);
  }

  keyDownFunction(event) { 
    if (event.keyCode == 13) {
      this.getuserbyperscode(this.userDetails.person.perscode);
    }
  }
      
  getuserbyperscode(e) {
        this.http.post(CommonConstants.GET_USER_BY_PERSCODE + this.userDetails.person.perscode, null).subscribe(response => {
          if (response['code'] == 0) {
            let userByPerscode = response['result'];
            let existingUser = this.usersList.filter(item => item.perscode===userByPerscode.perscode);
           // for (let person of this.usersList) {
              //if (person.loginId == userByPerscode.loginId) {
            if (existingUser != null && existingUser.length > 0) {
              this.applayMessage = true;
              // this.onRowSelect({ data: person });
             // this.onRowSelect({ data: existingUser });
              // break;
            }
            else {
              this.applayMessage = false;
              this.userDetails.person.personName = response['result'].personName;
              this.userDetails.loginId = response['result'].loginId;
              if (userByPerscode.workStatus != null) {
                this.userDetails.institutes = userByPerscode.workStatus;
              }
              this.clearnewuser();
            }
            //}
          }   
          else {
            Swal.fire("Error!", "PERSCODE DOESN'T EXIST !", "error");
            this.userDetails.person.personName = null;
            this.userDetails.loginId = null;
            this.moveAllInstToR();
            this.moveAllRoleToR();
          }
    
        });
  }

  saveUser() {
    // Set the (systemId=3) as by default for IRS
    this.systemIdData = new SystemIdDataModel();
    this.systemIdData.systemId = CommonConstants.SYSTEM_ID;
    this.assignedSystemId[0] = this.systemIdData;
    this.userDetails.userSystem = this.assignedSystemId;
    let userDtlsWithDfltInst=new SaveUserDataModel;
    //Fix to Stamp active = Y  in case of New user. If active is null or undefined then stamp as Y - Temporary Fix for Corona
    if(this.userDetails.active==undefined){
      this.userDetails.active =true;
    }
    userDtlsWithDfltInst=Object.assign({}, this.userDetails);
    userDtlsWithDfltInst.institutes=userDtlsWithDfltInst.institutes.filter(item => item.defaultYN==='Y');
    if (userDtlsWithDfltInst.institutes == null || userDtlsWithDfltInst.institutes.length === 0) {
      Swal.fire('Error!', 'Please Select Default Institute', 'error')
    } else {
      if (userDtlsWithDfltInst.roles.length === 0) {
        Swal.fire('Error!', 'Please Select Role', 'error')
      } else {
        this.http.post(CommonConstants.SAVE_USERS, userDtlsWithDfltInst).subscribe(res => {
          if (res['code'] == 0) {
            Swal.fire('Saved!', 'User Information has been Saved.', 'success')
            //this.getUsers();
          } else {
            Swal.fire('Error!', res['message'], 'error');
          }
        }, err => {
          Swal.fire('Error!', 'Error occured while saving user', 'error')
        });
      }
  }
  }

  onRowSelect(event: any) {
    this.showuserview=true;
    this.applayMessage = false;
    this.isNewUser=false;
    this.navigatedParam = event.data;
    this.userDetails = new SaveUserDataModel();
    this.userDetails.id = this.navigatedParam.id;
    this.userDetails.person = {
      perscode: this.navigatedParam.perscode,
      personName: this.navigatedParam.name
    }
    this.userDetails.loginId = this.navigatedParam.loginId;
    this.userDetails.active = this.navigatedParam.status;
    this.userDetails.institutes = this.navigatedParam.institute;
    this.userDetails.roles = this.navigatedParam.roles;
    
    this.selectedInst = this.userDetails.institutes[0];
    this.selectedRole = this.userDetails.roles[0];
    this.assignedInst = this.userDetails.institutes;
    this.assignedRole = this.userDetails.roles;

    if (this.userDetails) {
      this.availableInst = this.minusInst(this.instituteData, this.assignedInst);
      this.availableRole = this.minusRole(this.roleData, this.assignedRole);
    }
  }

  clearnewuser() {
    this.applayMessage = false;
    this.moveAllInstToR();
    this.moveAllRoleToR();
  }

  cleardata() {
    this.userDetails.person.perscode = null;
    this.userDetails.person.personName = null;
    this.userDetails.loginId = null;
    this.userDetails.active = null;
    this.userDetails.institutes = [];
    this.userDetails.roles = [];
    this.moveAllInstToR();
    this.moveAllRoleToR();
    this.applayMessage = false;
  }

  // Used for Institute Modal (Start)
  moveAllInstToL() {
    this.assignedInst = this.instituteData;
    this.userDetails.institutes = this.assignedInst;
    this.availableInst = [];
  }

  moveAllInstToR() {
    this.assignedInst.forEach(el => {
      this.availableInst.push(el);
      this.assignedInst = [];
      this.userDetails.institutes = this.assignedInstitutes;
    });
  }

  moveLtoRInst() {
    this.availableInstitutes.forEach(element => {
      this.instituteData.forEach(el => {
        if (el.estCode == element) {
          this.assignedInst = this.assignedInst.concat(el);
          this.userDetails.institutes = this.userDetails.institutes.concat(el);
        }
      })
    });
    this.availableInstitutes.forEach(el => {
      for (let i = 0; i < this.availableInst.length; i++) {
        if (el == this.availableInst[i].estCode) {
          this.availableInst.splice(i, 1);
        }
      }
    });
  }

  moveRtoLInst() {
    this.assignedInstitutes.forEach(element => {
      this.instituteData.forEach(el => {
        if (el.estCode == element) {
          this.availableInst = this.availableInst.concat(el);
        }
      })
    });
    this.assignedInstitutes.forEach(el => {
      for (let i = 0; i < this.assignedInst.length; i++) {
        if (el == this.assignedInst[i].estCode) {
          this.assignedInst.splice(i, 1);
        }
      }
    });
  }

  minusInst(firstArr: any = [], secondArr: any = []): any[] {
    let newListOfInst = {}
    secondArr.forEach(function (obj) {
      newListOfInst[obj.estCode] = obj;
    });
    return firstArr.filter(function (obj) {
      return !(obj.estCode in newListOfInst);
    });
  }

  removeInst(e) {
    let arr = [];
    let arr1 = [];
    this.userDetails.institutes.forEach(element1 => {
      if (element1.estCode != this.selectedItem.estCode) {
        arr.push(element1);
        arr1.push(element1);
      }
    });
    this.userDetails.institutes = arr;
    this.assignedInst = arr1;
    this.availableInst.push(this.selectedItem);
    this.selectedItem = this.userDetails.institutes[0];
  }
  // Used for Institute Modal (End)

  // Used for Role Modal (Start)
  moveAllRoleToL() {
    this.assignedRole = this.roleData;
    this.userDetails.roles = this.assignedRole;
    this.availableRole = [];
  }

  moveAllRoleToR() {
    this.assignedRole.forEach(el => {
      this.availableRole.push(el);
      this.assignedRole = [];
      this.userDetails.roles = this.assignedRoles;
    });
  }

  moveLtoRRole() {
    if(this.assignedRoles.length == 0){
      this.availableRoles.forEach(element => {
        this.roleData.forEach(el => {
          if (el.id == element) {
            this.assignedRole = this.assignedRole.concat(el);
            this.userDetails.roles = this.userDetails.roles.concat(el);
  
          }
        })
      });
      this.availableRoles.forEach(el => {
        for (let i = 0; i < this.availableRole.length; i++) {
          if (el == this.availableRole[i].id) {
            this.availableRole.splice(i, 1);
          }
        }
      });
      this.availableRoles.length =0;
    }else{
      this.assignedRoles.length =0;
    }

  }

  moveRtoLRole() {
    if(this.availableRoles.length == 0 ){
      this.assignedRoles.forEach(element => {
        this.roleData.forEach(el => {
          if (el.id == element) {
            this.availableRole = this.availableRole.concat(el);
          }
        })
      });
      this.assignedRoles.forEach(el => {
        for (let i = 0; i < this.assignedRole.length; i++) {
          if (el == this.assignedRole[i].id) {
            this.assignedRole.splice(i, 1);
            this.userDetails.roles.splice(i, 1);
          }
        }
      });
      this.assignedRoles.length =0;
      
    }else{
      this.availableRoles.length =0;
      
    }
    
  }

  removeRole(e) {
    let arr = [];
    let arr1 = [];
    this.userDetails.roles.forEach(element1 => {
      if (element1.id != this.selectedItem.id) {
        arr.push(element1);
        arr1.push(element1);
      }
    });
    this.userDetails.roles = arr;
    this.assignedRole = arr1;
    this.availableRole.push(this.selectedItem);
    this.selectedItem = this.userDetails.roles[0];
  }

  minusRole(firstArr: any = [], secondArr: any = []): any[] {
    let newListOfRole = {}
    secondArr.forEach(function (obj) {
      newListOfRole[obj.id] = obj;
    });
    return firstArr.filter(function (obj) {
      return !(obj.id in newListOfRole);
    });
  }
  // Used for Role Modal (End)

  listClick(event, newValue) {
    this.selectedItem = newValue;
  }

  shouldDisplay(name: string): boolean {
    if (this.searchTerm == null) {
      return true;
    }
    return name.toLowerCase().indexOf(this.searchTerm.toLowerCase()) !== -1;
  }

  resetPassword() {
    this.http.get(CommonConstants.RESET_PASSWORD + this.userDetails.loginId).subscribe(response => {      
      if (response['code'] == 0) {
        Swal.fire('RESET!', 'Password has been Reseted.', 'success')
      } else {
        Swal.fire('Error!', 'Error occured while reset password.', 'error')
      }     
    });
  }

  resetForm(){
    this.userSearchCriteria = new UserMasterDataModel();
  }

  onRowUnselect(event:any) {
  }
  selectDefaultInst(inst:any ,event:any){
      if(this.userDetails!=null && this.userDetails.institutes!=null){
        this.userDetails.institutes.forEach(element => {
            if(element.estCode===inst.estCode){
              element.defaultYN="Y";
            }else{
              element.defaultYN="N";
            }
        });
      }
  }

  hasPrivilege(privilege: any) {
    return this._sharedService.hasPrivilege(privilege);
}

filterRole(){
}
}