<div class="row">
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <div class="">
                <h6>Filters</h6>
                <div class="form-group">
                    <label>Region</label>
                    <select class="form-control form-control-sm">
                        <option disabled selected>Select</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Wilayat</label>
                    <select class="form-control form-control-sm">
                        <option disabled selected [value]="0">Select</option>
                        <option [value]="1">1</option>
                        <option [value]="2">2</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Institute</label>
                    <select class="form-control form-control-sm">
                        <option disabled selected>Select</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Age</label>
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <input type="text" class="form-control form-control-sm" placeholder="From">
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <input type="text" class="form-control form-control-sm" placeholder="To">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Braden Score</label>
                    <div class="row">                        
                        <div class="col-lg-9 col-md-9 col-sm-9">
                            <div class="p-2">
                                <p-slider [(ngModel)]="val" [min]="0" [max]="100"></p-slider>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-3"><span class="badge badge-secondary">{{ val }}</span></div>
                    </div>                    
                </div>
                
            </div>
        </div>
    </div>
    <div class="col-lg-10 col-md-10 col-sm-10">
        <div class="inner-content dash-content">
            <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <div class="widget">
                            <h6>Region Wise</h6>
                            <div class="text-center chartDiv">
                                <p-chart type="doughnut" [data]="data"></p-chart>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <div class="widget">
                            <h6>Age Wise Distribution</h6>
                            <div class="text-center chartDiv">
                                <p-chart type="bar" [data]="data"></p-chart>
                            </div>
                        </div>
                    </div>                    
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <div class="widget">
                            <h6>Wilayat Wise</h6>
                            <div class="text-center pt-12 chartDiv"><img src="../assets/img/no-data-available.png"></div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <div class="widget">
                            <h6>Institute Wise</h6>
                            <div class="text-center pt-12 chartDiv"><img src="../assets/img/no-data-available.png"></div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>

