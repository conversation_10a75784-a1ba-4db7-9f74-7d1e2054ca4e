import { Component, ViewChild, OnInit, Input, } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { NgbModalConfig, NgbModal, NgbModalOptions, NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedService } from '../../_services/shared.service';
import { FormGroup, FormBuilder, NgModel, NgControl } from '@angular/forms';
import * as AppUtils from '../../common/app.utils';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { TissueTypeModel } from '../../common/objectModels/tissueTypeModel';
import { RegistryService } from '../../_services/registry.service';
import { DonorInformationComponent } from '../donor-information/donor-information.component';
import { MasterService } from '../../_services/master.service';
import { RenalDonor } from '../../_models/renal-donor.model';
import { VaccinationComponent } from '../../_comments/vaccination/vaccination.component';
import { DraggableModalComponent } from '../../draggableModel/draggable-modal.Component';
import { ModalConfig } from '../../config/modal-config';
import { DragDropModule } from '@angular/cdk/drag-drop'
import * as $ from "jquery";
import 'jqueryui';
import Swal from 'sweetalert2';
import * as moment from "moment";
import * as CommonConstants from '../../_helpers/common.constants';
import { formatDate } from '@angular/common';
import { variable } from '@angular/compiler/src/output/output_ast';
import { Variable } from '@angular/compiler/src/render3/r3_ast';
import { Router } from '@angular/router';

@Component({
  selector: 'app-tissue-typing',
  templateUrl: './tissue-typing.component.html',
  styleUrls: ['./tissue-typing.component.scss']
})
export class TissueTypingComponent implements OnInit {

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('donorDetails', { static: false }) donorDetails: DonorInformationComponent;
  @ViewChild('Vaccination', { static: false }) Vaccination: VaccinationComponent;
  @Input() tissueForm: FormGroup;
  @Input() regRenalDonorForm: FormGroup;
  @Input() donorTissueForm: FormGroup;
  regId: any;
  patientForm: FormGroup;
  tissueType: TissueTypeModel[];
  columnDefs: any[];
  rowData: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  submitted = false;
  tissueTypeData: TissueTypeModel;
  savingTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();

  dbTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  patientTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  patientActiveTissue: TissueTypeModel[];
  donorTissue: Array<TissueTypeModel> = new Array<TissueTypeModel>();
  dbcentralRegNo: any;
  centralRegNoExit: boolean = false;
  civilIdInvalid = false;
  renalDonor: RenalDonor;
  hasvalue = true;
  hasSave = true;
  hasEdit = false;
  isDisabled = false;
  nationListFilter: any;
  nationList: any;
  kidneyDonorId: any;
  dbResuld;
  modalOptions: NgbModalOptions = ModalConfig;
  activeTabIndex = 0;
  tabs = [
    {
      title: '15/08/25',
      groups: [
        {
          name: 'A',
          options: [1, 2, 3, 11, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 36, 43, 66, 68, 69, 74, 80]
        },
        {
          name: 'B',
          options: [
            7, 8, 13, 18, 27, 35, 37, 38, 39, 41,
            42, 44, 45, 46, 47, 48, 49, 50, 51, 52,
            53, 54, 55, 56, 57, 58, 59, 60, 61, 62,
            63, 64, 65, 67, 71, 72, 73, 75, 76, 77,
            78, 81, 82, 83
          ]
        },
        {
          name: 'Bw',
          options: [4, 6]
        },
        {
          name: 'Cw',
          options: [1, 2, 4, 5, 6, 7, 8, 9, 10, 12, 14, 15, 16, 17, 18]
        },
        {
          name: 'DR',
          options: [1, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, "0103"]
        },
        {
          name: 'DPA',
          options: [1, 2, 3, 4]
        },
        {
          name: 'DPB',
          options: [
            1, 3, 5, 6, 8, 9, 10, 11, 13, 14,
            15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
            25, 26, 27, 28, 30, 40, 85,
            "0201", "0202", "0401", "0402"
          ]
        },
        {
          name: 'DQA',
          options: [1, 2, 3, 4, 5, 6]
        },
        {
          name: 'DQB',
          options: [2, 4, 5, 6, 7, 8, 9]
        }
      ]
    },
    {
      title: '02/08/25',
      groups: [
        {
          name: 'A',
          options: [29, 30, 31, 32, 33, 34, 36, 43, 66, 68, 69, 74, 80]
        },
        {
          name: 'B',
          options: [
            7, 8, 13, 18, 27, 35, 37, 38, 39, 41
          ]
        },
        {
          name: 'Bw',
          options: [4, 6]
        }

      ]
    },


  ];
  selectedOptions: { group: string; label: any }[] = [];
  selectedGroups: { [tabIndex: number]: string } = {};

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridReady: () => {
      this.gridOptions.api.setRowData(this.tissueType);
    },
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  onReady(params) {
    this.gridOptions.api.sizeColumnsToFit();
  }



  constructor(private modalService: NgbModal, private _sharedService: SharedService, private _http: HttpClient, private _router: Router,
    private formBuilder: FormBuilder, private registryService: RegistryService, private _masterService: MasterService) {

    // this.tabs.forEach((tab, index) => {
    //   this.selectedGroups[index] = tab.groups[0]?.name || '';
    // });

    this.getNationalityList();


    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.getList(this._sharedService.getNavigationData().centralRegNo);
        this._sharedService.setNavigationData(null);
      }, 1000);

    }



    this.regRenalDonorForm = this.formBuilder.group({
      'civilId': [null],
      'fullName': [null],
      'dob': [null],
      'nationality': [null],
      'sex': [null],
      'bloodGroup': [null],
      'telNo': [null],
      'address': [null],
    })

    this.tissueForm = this.formBuilder.group({
      'centralRegNo': [null],
      'a_Test': [null],
      'a_1_Test': [null],
      'a_2_Test': [null],
      'a_3_Test': [null],

      'b_Test': [null],
      'b_1_Test': [null],
      'b_2_Test': [null],
      'b_3_Test': [null],

      'cw_Test': [null],
      'cw_1_Test': [null],
      'cw_2_Test': [null],
      'cw_3_Test': [null],

      'bw_Test': [null],
      'bw_1_Test': [null],
      'bw_2_Test': [null],
      'bw_3_Test': [null],

      'drb1_Test': [null],
      'drb1_1_Test': [null],
      'drb1_2_Test': [null],
      'drb1_3_Test': [null],

      'drb3_Test': [null],
      'drb3_1_Test': [null],
      'drb3_2_Test': [null],
      'drb3_3_Test': [null],

      'drb4_Test': [null],
      'drb4_1_Test': [null],
      'drb4_2_Test': [null],
      'drb4_3_Test': [null],

      'drb5_Test': [null],
      'drb5_1_Test': [null],
      'drb5_2_Test': [null],
      'drb5_3_Test': [null],

      'dqb1_Test': [null],
      'dqb1_1_Test': [null],
      'dqb1_2_Test': [null],
      'dqb1_3_Test': [null],

      'dqa1_Test': [null],
      'dqa1_1_Test': [null],
      'dqa1_2_Test': [null],
      'dqa1_3_Test': [null],

      'dpb1_Test': [null],
      'dpb1_1_Test': [null],
      'dpb1_2_Test': [null],
      'dpb1_3_Test': [null],

      'dpa1_Test': [null],
      'dpa1_1_Test': [null],
      'dpa1_2_Test': [null],
      'dpa1_3_Test': [null],

      'remarks': '',
      // bw_Test : [null],
      // bw_1_Test : [null],
    })

    this.donorTissueForm = this.formBuilder.group({
      'a_Test': [null],
      'a_1_Test': [null],
      'b_Test': [null],
      'b_1_Test': [null],
      'cw_Test': [null],
      'cw_1_Test': [null],
      'dr_Test': [null],
      'dr_1_Test': [null],
      'drw_Test': [null],
      'drw_1_Test': [null],
      'dq_Test': [null],
      'dq_1_Test': [null],
      // 'bw_Test': [null],
      // 'bw_1_Test': [null],
      'centralRegNo': [null],
      'donorId': [null],
    })


    this.columnDefs = [

      { headerName: 'Donor Id', field: 'donorId', sortable: true, cellStyle: { color: 'blue', 'text-decoration': 'underline' } },
      { headerName: 'Created', field: 'createedOn', sortable: true },
      // { headerName: 'Test Type', field: 'testType'  },
      {
        headerName: 'A', field: 'a_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].a_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'A1', field: 'a_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].a_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'B', field: 'b_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].b_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'B 1', field: 'b_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].b_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'CW', field: 'cw_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].cw_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'CW 1', field: 'cw_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].cw_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DR', field: 'dr_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].dr_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DR 1', field: 'dr_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].dr_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DRW', field: 'drw_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].drw_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DRW 1', field: 'drw_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].drw_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DQ', field: 'dq_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].dq_Test) {
            return { color: 'red' };
          }
          return null;
        }
      },
      {
        headerName: 'DQ 1', field: 'dq_1_Test',
        cellStyle: params => {
          if (params.value !== this.patientActiveTissue[0].dq_1_Test) {
            return { color: 'red' };
          }
          return null;
        }
      }
    ];


  }


  get fTissue() { return this.tissueForm.controls; }
  ngOnInit(): void {



    this.tissueTypeData = new TissueTypeModel;
    this.renalDonor = new RenalDonor;
    this.getTissueType();
    this.tissueForm.disable();
    this.hasEdit = true;
    // document.getElementById["HLAButton"].disable = true;


  }
  ngAfterViewInit() {
    this.patientDetails.patientForm.disable();
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationList = response.result;
      this.nationListFilter = this.nationList;
    }, error => {

    });
  }

  donorSelectModal(donor) {
    this.donorTissueForm.patchValue({ "donorId": this.kidneyDonorId })

    this.getRegDonor(this.kidneyDonorId);
    this.modalService.open(donor, this.modalOptions);
    $(document).ready(function () {
      let modalContent: any = $('.modal-content');
      let modalHeader = $('.modal-header');
      modalHeader.addClass('cursor-all-scroll');
      modalContent.draggable({
        handle: '.modal-header',


      });
    });
  }

  search() {
    this.clear();
    if (this.regId) {
      this.getList(this.regId);
      this.regId = "";
      // this.isDisabled == false;
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });

    }
  }

  getList(regNo: any) {
    this._http.get(AppUtils.GET_RENAL_DONOR_HLA_BY_REG_NO, { params: new HttpParams().set("centralRegNo", regNo) }).subscribe(res => {

      if (res['code'] == "S0000") {

        this.patientDetails.setPatientDetails(res['result']);
        this.isDisabled = false;
        //this.tissueForm.enable();

        this.dbResuld = res['result'];
        this.dbcentralRegNo = res['result'].centralRegNo;
        this.centralRegNoExit = true;

        let maxDate;

        this.dbTissue = res['result'].rgHlaTissueTypeDto;

        this.dbTissue.forEach(elm => {
          elm.a_Test = this.padLeft(elm.a_Test, "0", 2);
          elm.a_1_Test = this.padLeft(elm.a_1_Test, "0", 2);
          elm.a_2_Test = this.padLeft(elm.a_2_Test, "0", 2);
          elm.a_3_Test = this.padLeft(elm.a_3_Test, "0", 2);
          elm.b_Test = this.padLeft(elm.b_Test, "0", 2);
          elm.b_1_Test = this.padLeft(elm.b_1_Test, "0", 2);
          elm.cw_Test = this.padLeft(elm.cw_Test, "0", 2);
          elm.cw_1_Test = this.padLeft(elm.cw_1_Test, "0", 2);
          elm.dr_Test = this.padLeft(elm.dr_Test, "0", 2);
          elm.dr_1_Test = this.padLeft(elm.dr_1_Test, "0", 2);
          elm.drw_Test = this.padLeft(elm.drw_Test, "0", 2);
          elm.drw_1_Test = this.padLeft(elm.drw_1_Test, "0", 2);
          elm.dq_Test = this.padLeft(elm.dq_Test, "0", 2);
          elm.dq_1_Test = this.padLeft(elm.dq_1_Test, "0", 2);
          // elm.bw_Test = this.padLeft(elm.bw_Test, "0", 2);
          // elm.bw_1_Test = this.padLeft(elm.bw_1_Test, "0", 2);


        })



        this.patientTissue = this.dbTissue.filter(s => s.testType == "S");

        this.donorTissue = this.dbTissue.filter(s => s.testType == "D");

        this.donorTissue.sort((a, b) => new Date(b.createedOn).getTime() - new Date(a.createedOn).getTime());


        this.donorTissue.forEach(elm => {
          elm.createedOn = new Date(elm.createedOn).toLocaleDateString('en-GB');
        })

        this.patientActiveTissue = this.patientTissue.filter(s => s.activeYn == "Y");

        if (this.patientActiveTissue.length > 0) {
          this.hasSave = false;
          this.hasEdit = false;
          this.tissueForm.disable();
        } else {
          this.hasvalue = false;
          this.tissueForm.enable();
        }
        this.tissueForm.patchValue(this.patientActiveTissue[0]);

        this.donorTissueForm.patchValue({ "centralRegNo": regNo })


        res['result'].rgTbRenalDonorPatientDto.forEach(element => {
          //element.createdDate
          if (maxDate == null) {
            maxDate = element.createdDate;
            this.kidneyDonorId = element.renalDonorId;
          } else if (maxDate < element.createdDate) {
            maxDate = element.createdDate;
            this.kidneyDonorId = element.renalDonorId;
          }

        });


        // this. = false;


      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })
  }




  getTissueType() {

    if (this.gridOptions.api) { this.gridOptions.api.showLoadingOverlay(); }

    this._http.get(AppUtils.GET_ALL_TISSUE_TYPE).subscribe(res => {

      this.rowData = res['result'];
      ;
    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })

  }


  saveTissueTypeInfo(type: any) {
    this.submitted = true;

    if (type == 'S') {

      if (this.tissueForm.invalid) {
        return false;
      }

      let body = this.tissueForm.value;
      body['centralRegNo'] = this.patientDetails.patientForm.get("centralRegNo").value;
      body['testType'] = type;

      this.savingTissue[0] = body;

      this.patientTissue.filter(s => s.activeYn == 'Y').forEach(element => {
        this.savingTissue.push(element);
      });




    } else if (type == 'D') {

      if (this.donorTissueForm.controls.a_1_Test.value != this.tissueForm.controls.a_1_Test.value ||
        this.donorTissueForm.controls.a_Test.value != this.tissueForm.controls.a_Test.value ||
        this.donorTissueForm.controls.b_Test.value != this.tissueForm.controls.b_Test.value ||
        this.donorTissueForm.controls.b_1_Test.value != this.tissueForm.controls.b_1_Test.value ||
        this.donorTissueForm.controls.cw_Test.value != this.tissueForm.controls.cw_Test.value ||
        this.donorTissueForm.controls.cw_1_Test.value != this.tissueForm.controls.cw_1_Test.value ||
        this.donorTissueForm.controls.dr_Test.value != this.tissueForm.controls.dr_Test.value ||
        this.donorTissueForm.controls.dr_1_Test.value != this.tissueForm.controls.dr_1_Test.value ||
        this.donorTissueForm.controls.drw_Test.value != this.tissueForm.controls.drw_Test.value ||
        this.donorTissueForm.controls.drw_1_Test.value != this.tissueForm.controls.drw_1_Test.value ||
        this.donorTissueForm.controls.dq_Test.value != this.tissueForm.controls.dq_Test.value ||
        this.donorTissueForm.controls.dq_1_Test.value != this.tissueForm.controls.dq_1_Test.value
      ) {
        Swal.fire('Info!', 'There Are Mismatch between Patient HLA and Donor HLA', 'info');
      }
      let donadBody = this.donorTissueForm.value;

      this.savingTissue = [donadBody];

    }




    this.registryService.saveTissueTypeList(this.savingTissue).subscribe(response => {
      if (response['code'] == 0) {
        this.regId = response["result"];
        this.clear();

        Swal.fire('Saved!', 'Patient HLA Saved successfully.', 'success');
        this.search();
        this.hasSave = false;
        this.hasvalue = true;
        if (type == 'D') {
          if (this.donorTissueForm.controls.a_1_Test.value != this.tissueForm.controls.a_1_Test.value ||
            this.donorTissueForm.controls.a_Test.value != this.tissueForm.controls.a_Test.value ||
            this.donorTissueForm.controls.b_Test.value != this.tissueForm.controls.b_Test.value ||
            this.donorTissueForm.controls.b_1_Test.value != this.tissueForm.controls.b_1_Test.value ||
            this.donorTissueForm.controls.cw_Test.value != this.tissueForm.controls.cw_Test.value ||
            this.donorTissueForm.controls.cw_1_Test.value != this.tissueForm.controls.cw_1_Test.value ||
            this.donorTissueForm.controls.dr_Test.value != this.tissueForm.controls.dr_Test.value ||
            this.donorTissueForm.controls.dr_1_Test.value != this.tissueForm.controls.dr_1_Test.value ||
            this.donorTissueForm.controls.drw_Test.value != this.tissueForm.controls.drw_Test.value ||
            this.donorTissueForm.controls.drw_1_Test.value != this.tissueForm.controls.drw_1_Test.value ||
            this.donorTissueForm.controls.dq_Test.value != this.tissueForm.controls.dq_Test.value ||
            this.donorTissueForm.controls.dq_1_Test.value != this.tissueForm.controls.dq_1_Test.value) {
            Swal.fire('Info', 'Donor HLA Saved successfully <br> Note: There Are Mismatch between Patient HLA and Donor HLA', 'info');
          }

        }
      } else if (response['code'] == "3") {
        Swal.fire('Saved!', response['message'], 'error');
      } else {
        Swal.fire('Error!', response['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Patient HLA ' + err.message, 'error')
    });
    // }







  }

  /*
  public onClickKey($event: any) {
    if ($event.keyCode == 13) {
      let civilId: any = $event.target.value;
     // this.getPatientDetails(civilId);
     console.log( $event.target.value);
      this.getRegDonor(civilId);
    }
    else {
      this.civilIdInvalid = false;
    }
  }
  */
  onKeypressEvent(event: any) {

  }


  getPatientDetails(civilId: any) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    const loginId = curUser['person'].perscode;

    let dob = this.patientDetails.patientForm.value.dob;
    if (dob != null) {
      dob = formatDate(dob, 'yyyy-MM-dd', 'en');
    }

    let exDate = this.patientDetails.patientForm.value.exDate;
    if (exDate != null) {
      exDate = formatDate(exDate, 'yyyy-MM-dd', 'en');
    }
    let req = {
      "birthDate": dob,
      "cardExpiryDate": exDate,
      "civilId": this.patientDetails.patientForm.value.civilId,
      //"queryType": "string",
      //"requesterCivilId": "string",
      "requesterPersCode": loginId,
      // "type": 0 
    };

    // this._masterService.getMpiDetails(civilId).subscribe(response => {
    this._masterService.getMpiV2Details(req).subscribe(response => {


      if (response !== null && response.result != "False") {


        this.renalDonor.civilId = response.civilId;
        this.renalDonor.sex = response.sex;
        this.renalDonor.telNo = response.mobileNo;
        //this.renalDonor.nationality 
        this.renalDonor.nationality = response.countryID;
        this.renalDonor.address = response.birthTown;
        //  this.renalDonor.dob = response.birthDate;



      }
    }

    );
  }

  getRegDonor(kidneyDonorId: any) {
    this._masterService.getRegDonorDetailsById(kidneyDonorId).subscribe(response => {

      let donorDtl = response["result"];
      this.regRenalDonorForm.patchValue({
        civilId: donorDtl['civilId'],
        fullName: donorDtl['fullname'],
        dob: moment(donorDtl["dob"]).format("DD-MM-YYYY"),
        nationality: donorDtl["nationality"],
        sex: donorDtl["sex"],
        bloodGroup: donorDtl["bloodGroup"],
        telNo: donorDtl["telNo"],
        address: donorDtl["address"],

      });
    });

  }

  numberOnly(event): boolean {

    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    this.onKeypressEvent(event);
    return true;

  }



  // method donor tissue form data validation (font color change for mismatch with patient hla) and padding 0 before single digit
  changeFormat($event, type) {

    if ($event.value != null) {
      $event.value = this.padLeft($event.value, "0", 2);
    }

    if (type == "donor") {
      // if( this.donorTissueForm.get($event.id).value != this.tissueForm.get($event.id).value ){
      if (this.padLeft(this.donorTissueForm.get($event.id).value, "0", 2) != this.padLeft(this.tissueForm.get($event.id).value, "0", 2)) {
        $event.style.color = "red";
      } else {
        $event.style.color = "black";
      }
    }
  }
  // END-OFF  changeFormat()

  padLeft(text: any, padChar: string, size: number): string {
    if (!text) {
      return null
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  editValue() {
    this.hasSave = true;
    this.hasvalue = false;
    this.hasEdit = true;
    this.tissueForm.enable();

  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['donor/registry'], { state: { donorId: event.data.donorId } });
  }

  callCase() {
    this._sharedService.setNavigationData(this.dbResuld);
    this._router.navigate(['stages'], { state: { centralRegNo: this.dbcentralRegNo } });
  }

  clear() {
    this.hasvalue = true;
    this.hasSave = true;
    this.hasEdit = true;
    this.centralRegNoExit = false;
    this.patientDetails.clear();
    this.regRenalDonorForm.reset();
    this.tissueForm.reset();
    this.donorTissueForm.reset();
    this.donorTissue = null;
  }




  selectGroup(tabIndex: number, groupName: string) {
    this.selectedGroups[tabIndex] = groupName;
  }

  isGroupActive(tabIndex: number, groupName: string): boolean {
    return this.selectedGroups[tabIndex] === groupName;
  }

  onCheckboxChange(event: Event, groupName: string, option: any) {
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const alreadySelected = this.selectedOptions.some(
        item => item.group === groupName && item.label === option
      );

      if (!alreadySelected) {
        this.selectedOptions.push({
          group: groupName,
          label: option
        });
      }
    } else {
      this.selectedOptions = this.selectedOptions.filter(
        item => !(item.group === groupName && item.label === option)
      );
    }

  }

  isOptionSelected(groupName: string, option: any): boolean {
    return this.selectedOptions.some(
      item => item.group === groupName && item.label === option
    );
  }


  removeSelectedOption(item: { group: string, label: any }) {

    this.selectedOptions = this.selectedOptions.filter(
      i => !(i.group === item.group && i.label === item.label)
    );


    const activeTab = this.tabs[this.activeTabIndex];
    const checkboxId = `${activeTab.title}-${item.group}-${item.label}`;
    const checkbox = document.getElementById(checkboxId) as HTMLInputElement;

    if (checkbox) {
      checkbox.checked = false;
      this.onCheckboxChange({ target: checkbox } as unknown as Event, item.group, item.label);
    }

  }

}