export class HeartListResult {
    regNo: number;
    civilId: string;
    dateOfBirth: Date;
    age: number;
    fullName: string;
    sex: string;
    regInst: number;
    walCode: number;
    regCode: number;
    weight: number;
    height: number;
    bloodGroup: string;
    transplantRecommended: string;
    transplantUrgent: string;
    inotropeUsed: string;
    nyhaClass: number;
    mcsDeviceUsed: string;
    physiotherapyBase: number;
    primaryDiag: number;
    icd: string;
    deathCase: number;
    familyHistory: string;
}

export class HeartExportExcel {
    regNo: number;
    civilId: string;
    dateOfBirth: Date;
    age: number;
    fullName: string;
    sex: string;
    regInst: string | number;
    walCode: string | number;
    regCode: string | number;
    weight: number;
    height: number;
    bloodGroup: string;
    transplantRecommended: string;
    transplantUrgent: string;
    inotropeUsed: string;
    nyhaClass: string | number;
    mcsDeviceUsed: string | number;
    physiotherapyBase: string | number;
    primaryDiag: number;
    icd: string;
    deathCase: number;
    familyHistory: string;
}


export class HeartExcel {
    regNo: number;
    civilId: string;
    dateOfBirth: Date;
    age: number;
    fullName: string;
    sex: string;
    regInst: string | number;
    walCode: string | number;
    regCode: string | number;
    weight: number;
    height: number;
    bloodGroup: string;
    transplantRecommended: string;
    transplantUrgent: string;
    inotropeUsed: string;
    nyhaClass: number | string;
    mcsDeviceUsed: string | number;
    physiotherapyBase: string | number;
    primaryDiag: number | string;
    icd: string;
    deathCase: number;
    familyHistory: string;
}