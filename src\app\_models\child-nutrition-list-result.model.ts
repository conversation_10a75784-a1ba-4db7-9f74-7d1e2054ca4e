export class ChildNutritionListResult {

    public rowId : number;
	public epiNo : String;
	public nutritionNo : String;
	public headCrBirth : number;
	public lengthBrith : number;
	public weightBrith	 : number;
	public outComeCause : number;
    public regDate : Date;
	public status : number;
	public nutritionId : number;
	public firstName : String;
	public SecondName : String;
	public thirdName	 : String;
	public tribe	 : String;
	public bloodGroup	 : String;
	public dob	 : Date;
	public age	 : number;
	public maritalStatus	 : String;
	public nationality	 : number;
	public mobileNo	 : number;
	public sex	 : String;
	public patientId	 : number;
	public village	 : number;
	public walCode	 : number;
	public regCode	 : number;
	public centralRegNo	 : number;
	public visitId	 : number;
	public visitType	 : String;
	public diarrheaYn	 : String;
	public hbLevel	 : number;
	public oedemaYn	 : String;
	public visitDate	 : Date;
	public ageAtVisit	 : number;
	public height	 : number;
	public weight	 : number;
	public headCircum	 : number;
	public haZscore	 : number;
	public haRating	 : String;
	public waZscore	 : number;
	public waRating	 : String;
	public whZscore	 : number;
	public whRating : String;
}