<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 194 201">
  <defs>
    <style>
      .cls-1 {
        stroke-linecap: round;
      }

      .cls-1, .cls-2 {
        fill: none;
        stroke: #fff;
        stroke-miterlimit: 10;
        stroke-width: 13px;
      }
    </style>
  </defs>
  <ellipse class="cls-2" cx="98.5" cy="100.5" rx="76" ry="72"/>
  <line class="cls-1" x1="96.5" y1="6.5" x2="96.5" y2="51.5"/>
  <line class="cls-1" x1="187.5" y1="104.5" x2="151.5" y2="104.5"/>
  <line class="cls-1" x1="96.5" y1="150.5" x2="96.5" y2="194.5"/>
  <line class="cls-1" x1="43.5" y1="104.5" x2="6.5" y2="104.5"/>
  <ellipse class="cls-2" cx="97" cy="101" rx="32.5" ry="29.5"/>
</svg>