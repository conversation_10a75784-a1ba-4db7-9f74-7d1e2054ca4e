<h6>Brain Death Listing</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="brainDeathSearchForm">
        <div class="row">

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil ID</label>
                    <input type="text" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Patient ID</label>
                    <input type="text" class="form-control form-control-sm" formControlName="patientId">
                </div>
            </div>

            <div class="col-lg-1 col-md-2 col-sm-2">
                <div class="form-group">
                    <label>Age (from)</label>
                       <input  type="text" class="form-control form-control-sm" formControlName="ageFrom" >
                </div>
            </div>
            <div class="col-lg-1 col-md-2 col-sm-2">
                <div class="form-group">
                    <label>To</label>
                    <input type="text" class="form-control form-control-sm" formControlName="ageTo" >
               
                </div>
            </div>





            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="estCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>



            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>


            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Set Count</label>
                    <ng-select #entryPoint
                        [items]="[{ id: 1, value: 'have 1 set' }, { id: 2, value: 'have 2 set' },  { id: 2, value: 'All' }]"
                        [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                        formControlName="setCount">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group" style="display: flex;">
                    <div>
                        <div style="display: flex;"> Set 1:
                            <div>
                                Ex1 <ng-select #entryPoint [items]="setCheck" [virtualScroll]="true" placeholder="Select"
                                    bindLabel="value" bindValue="id" formControlName="set1Ex1">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                    </ng-template>
                                </ng-select>
                            </div>
                            <div> Ex2
                                <ng-select #entryPoint [items]="setCheck" [virtualScroll]="true" placeholder="Select"
                                bindLabel="value" bindValue="id" formControlName="set1Ex2">
                                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                </ng-template>
                            </ng-select>
                            </div>
                        </div>
                        <div style="display: flex;"> Set 2:
                            <div>
                                Ex1 <ng-select #entryPoint [items]="setCheck" [virtualScroll]="true" placeholder="Select"
                                    bindLabel="value" bindValue="id" formControlName="set2Ex1">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                    </ng-template>
                                </ng-select>
                            </div>
                            <div> Ex2
                                <ng-select #entryPoint [items]="setCheck" [virtualScroll]="true" placeholder="Select"
                                bindLabel="value" bindValue="id" formControlName="set2Ex2">
                                <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                </ng-template>
                            </ng-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Set 1 Exam 1</label>
                    <div style="display: flex;">
                        <div>
                            <label>From</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set1Ex1DateFrom" monthNavigator="true"
                                yearNavigator="true" yearRange="2000:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                        <div>
                            <label>To</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set1Ex1DateTo" monthNavigator="true"
                                yearNavigator="true" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Set 1 Exam 2</label>
                    <div style="display: flex;">
                        <div>
                            <label>From</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set1Ex2DateFrom" monthNavigator="true"
                                yearNavigator="true" yearRange="2000:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                        <div>
                            <label>To</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set1Ex2DateTo" monthNavigator="true"
                                yearNavigator="true" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Set 2 Exam 1</label>
                    <div style="display: flex;">
                        <div>
                            <label>From</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set2Ex1DateFrom" monthNavigator="true"
                                yearNavigator="true" yearRange="2000:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                        <div>
                            <label>To</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set2Ex1DateTo" monthNavigator="true"
                                yearNavigator="true" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Set 2 Exam 2</label>
                    <div style="display: flex;">
                        <div>
                            <label>From</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set2Ex2DateFrom" monthNavigator="true"
                                yearNavigator="true" yearRange="2000:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                        <div>
                            <label>To</label>
                            <p-calendar dateFormat="dd-mm-yy" formControlName="set2Ex2DateTo" monthNavigator="true"
                                yearNavigator="true" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>


            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <button type="submit" class="btn btn-primary ripple" (click)="clearData()">Clear</button>
                <button type="submit" class="btn btn-primary ripple" (click)="listBrainDeath()">Search</button>
            </div>

        </div>

    </form>
</div>


<div class="content-wrapper mb-2">
    <div class="grid-container">
        <div class="grid-item">
            <ag-grid-angular style="width: 100%; height: 500px;" class="ag-theme-balham" [rowData]="rowData"
                [gridOptions]="gridOptions" [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)">
            </ag-grid-angular>

        </div>
        <div class="grid-item">
            <p-paginator *ngIf="rowData.length > 0" #ListingPaginator rows="{{paginationSize}}"
                totalRecords="{{totalRecords}}" (onPageChange)="listBrainDeath($event)" showCurrentPageReport="true"
                currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
                [rowsPerPageOptions]="[10, 20, 30]">
            </p-paginator>
        </div>
    </div>

</div>


