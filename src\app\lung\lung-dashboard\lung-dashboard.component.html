<h6 class="page-title">
    <p>Lung Dashboard</p>
</h6>
<div class="row">
    <div class="col-lg-2 col-md-2 col-sm-2">
        <div class="side-panel">
            <form [formGroup]="boardForm">
                <div>
                    <div class="form-group">
                        <label>Region</label>
                        <ng-select [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
                            bindValue="regCode" formControlName="regCode" (change)="locSelect($event,'region')">
                            <ng-template ng-option-tmp let-item="item">{{ item.regName }}</ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Wilayat</label>
                        <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                            bindLabel="walName" bindValue="walCode" formControlName="walCode"
                            (change)="locSelect($event,'wilayat')">
                            <ng-template ng-option-tmp let-item="item">{{ item.walName }}</ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Institute</label>
                        <ng-select [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                            bindLabel="estName" bindValue="estCode" formControlName="estCode">
                            <ng-template ng-option-tmp let-item="item">{{ item.estName }}</ng-template>
                        </ng-select>
                    </div>

                    <div class="form-group">
                        <label>Age</label>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageF" type="number" class="form-control form-control-sm"
                                    placeholder="From" min="0">
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6">
                                <input formControlName="ageT" type="number" class="form-control form-control-sm"
                                    placeholder="To" min="0">
                            </div>
                        </div>
                        <div *ngIf="boardForm.hasError('ageRange') && (boardForm.get('ageF').touched || boardForm.get('ageT').touched)" class="text-danger" style="font-size: 12px;">
                            'To' age cannot be less than 'From' age.
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 text-right">
                                <button type="button" class="btn btn-sm btn-primary"
                                    (click)="callFilter()" [disabled]="boardForm.invalid">Search</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <footer *ngIf="createTime" id="footer">
            These statistics data were taken at {{ createTime }}. To refresh please press the
            <button type="button" class="btn btn-sm btn-primary" (click)="callReset()">Reset</button>
            button.
        </footer>
    </div>

    <div class="col-lg-10 col-md-10 col-sm-10">
        <div class="inner-content dash-content">
            <div class="d-flex justify-content-center">
                <h4>{{ filterTitle }}</h4>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Cause of Lung Disease (ICD)</h6>
                        <p-chart type="bar" [data]="icdPieChart" [options]="options"></p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Transplant</h6>
                        <p-chart type="pie" [data]="transplantPieChart" [options]="pieOption"></p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Urgent Transplant</h6>
                        <p-chart type="pie" [data]="urgentTransplantPieChart" [options]="pieOption"></p-chart>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="widget">
                        <h6>Indication</h6>
                        <p-chart type="bar" [data]="indicationChart" [options]="options"></p-chart>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>