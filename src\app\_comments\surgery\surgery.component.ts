import { Component, OnInit, Input,EventEmitter,Output } from '@angular/core';
import { MasterService } from '../../_services/master.service';
import { RegistryService } from '../../_services/registry.service';
import { labTestModel } from '../../common/objectModels/labTestModel';
import { HttpClient } from '@angular/common/http';
import * as AppUtils from '../../common/app.utils';
import Swal from 'sweetalert2';
import * as moment from 'moment';
import { SharedService } from '../../_services/shared.service';
import { FormGroup, FormBuilder } from '@angular/forms';
import { FormArray } from '@angular/forms';
import { formatDate } from '@angular/common';
import * as CommonConstants from '../../_helpers/common.constants';

@Component({
  selector: 'app-surgery',
  templateUrl: './surgery.compnent.html',
  styleUrls: ['./surgery.component.scss']
})
export class surgeryComponent implements OnInit {
  @Input() showAddNewButton: boolean = true;
  @Input() showSurgeryButton: boolean = true;
  @Output() downloadSurgery = new EventEmitter<void>();

  surgeryMaster: any[];
  surgeryList: any[];
  today = new Date();
  selectedDate: any;
  delRow: any;
  data: any = [];
  //centralRegNo = 33;
  selectedSurgery: any;
  surgList: any[];
  @Input() surgeryForm: FormGroup;
  surgeryID: any;
  surgeryDt: any;
  remarks: any;
  surgerySaveList: [];
  surgeryData: []
  params: any;
  surgeryName: any[];
  surgeries: any[];
  rgTbSurgeryDtls: any = [];
  loginId: any;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  constructor(private _masterService: MasterService, private _http: HttpClient, private formBuilder: FormBuilder) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

  }

  ngOnInit() {
    this.surgeryForm = this.formBuilder.group({

      rgTbSurgeryDtls: this.formBuilder.array([]),

    })
    this._masterService.getSurgeryMaster().subscribe(res => {
      this.surgeryMaster = res.result;

    })



  }


  ///////////////////P DATA TABLE

  /*
    private long runId;
    private long enteredBy;
    private Date enteredDt;
    private long surgeryID;
    private Date surgeryDt;
    private String remarks;
    private long instCode;
    private String source;
  
    */

  onAddNewSurgery() {
    this.addNewSurgery(null, null, null, null, this.loginId, this.currentDate, 'W', false);
    this.surgList[this.surgList.length - 1].isEditable = true;          //editable last entering row

  }

  addNewSurgery(runId: any = null, surgeryID: any = null, surgeryDt: any = null, remarks: any = null, enteredBy: any = null, enteredDt: any = null, source: any = null, isEditable: any = false): void {
    this.rgTbSurgeryDtls = this.surgeryForm.get('rgTbSurgeryDtls') as FormArray;

    this.surgList = Object.assign([], this.rgTbSurgeryDtls.value);
    const surItem: any = this.createSurgeryItem(runId, surgeryID, surgeryDt, remarks, enteredBy, enteredDt, source, isEditable);
    this.rgTbSurgeryDtls.push(this.createSurgeryGrpItem(surItem));

    this.surgList.push(surItem);
    //this.surgList[this.surgList.length - 1].isEditable = true;
  }

  createSurgeryGrpItem(createSurgeryItem: any): FormGroup {
    return this.formBuilder.group(createSurgeryItem);
  }

  createSurgeryItem(runId: any = null, surgeryID: any = null, surgeryDt: any = null, remarks: any = null, enteredBy: any = null, enteredDt: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      surgeryID: surgeryID,
      //surgeryDt: new Date(surgeryDt),
      surgeryDt: surgeryDt,
      remarks: remarks,
      enteredBy: enteredBy,
      enteredDt: enteredDt,
      source: source,
      isEditable: isEditable,
    };
  }

  getSurgery(surgeryID) {
    if (surgeryID) {
      return this.surgeryMaster.filter(s => s.procID == surgeryID).map(s => s.procName)[0];
    }

  }
  // 


  ///////////////////P DATA TABLE
  callFetchDataFromAlShifa() {
    this.downloadSurgery.emit();
  }



  addNew() {

    if (!this.surgList) {
      this.surgList = [];
    }
    this.surgList.push({ surgeryID: '', surgeryDt: '', remarks: '' });
    this.surgList[this.surgList.length - 1].isEditable = true;

    //let surgeryData = this.surgeryForm.value;



    /*
    this.surgeryData.forEach(data => {
        items.push({ "surgeryID": data[this.params.objectData[0]], "surgeryDt": data[this.params.objectData[1]],"remarks": data[this.params.objectData[2]] });
    })*/


  }

  delete(row: any) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbSurgeryDtls = this.surgeryForm.get('rgTbSurgeryDtls') as FormArray;
        this.delRow = this.surgList.indexOf(row);
        this.surgList.splice(this.delRow, 1);
        this.rgTbSurgeryDtls.removeAt(this.delRow);

      }
    })



  }




  onRowEditInit(row: any) {
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }
  onRowEditSave(row: any) {

    let rowIndex = this.surgList.indexOf(row);
    this.surgList[rowIndex] = this.surgeryForm.value.rgTbSurgeryDtls[rowIndex];
    let data = this.surgList[rowIndex];

    if (!data.surgeryID || !data.surgeryDt) {
      Swal.fire({
        title: "Warning",
        icon: "warning",
        text: "Please fill Surgery Name and Surgery Date before saving.",
        confirmButtonText: "Ok",
        confirmButtonColor: "#3085d6",
      });
      data.isEditable = true;
      return;
    }

    data.surgeryName = this.surgeryMaster.filter(s => s.procID == data.surgeryID).map(s => s.procName)[0];
    data.surgeryDt = moment(data.surgeryDt, "DD-MM-YYYY").format();
    data.isEditable = false;


  }

  /*
  civilId: 12250561
patientId: 4563481
surgeryDate: "2008-05-31T20:00:00.000+0000"
surgeryId: 7302
surgeryRemarks: "Past
*/
  //addNewSurgery(runId: any = null, surgeryID: any = null, surgeryDt: any = null, remarks: any = null,enteredBy: any = null,enteredDt: any = null,source: any = null, isEditable: any = false , surgeryName: any = null)
  getDataFromAlshifa(sendingData: any = 0) {
    // this.surgList = sendingData;

    sendingData.forEach(element => {
      this.addNewSurgery(null, element.surgeryId, element.surgeryDate, element.surgeryRemarks, null, element.surgeryDate, 'S', false);
      ;
    });
  }

  getSurgeryName(surgeryId) {
    return this.surgeryMaster.find(sur => sur.procID === surgeryId)['procName'] || '-';
  }


  clear() {
    this.surgList = [];
    this.surgeryForm.reset();

    this.surgeryForm = this.formBuilder.group({
      rgTbSurgeryDtls: this.formBuilder.array([]),
    })

  }

}