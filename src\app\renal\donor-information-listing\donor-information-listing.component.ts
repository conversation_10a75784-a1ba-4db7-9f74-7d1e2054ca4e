import { Component, OnInit,Input, ɵALLOW_MULTIPLE_PLATFORMS } from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { RenalDonor } from '../../_models/renal-donor.model';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { GenderType } from '../../_models/gender-type';
import { Nationality } from '../../_models/nationality.model';
import { MasterService } from '../../_services/master.service';
import { DonorService} from '../donor.service';
import { GridNgSelectDataComponent } from '../../common/agGridComponents/grid-ngSelect-data.component';
import * as GridUtils from '../../common/agGridComponents/app.grid-spec-utils';
import Swal from 'sweetalert2';

import * as AppUtils from '../../common/app.utils';

@Component({
    selector: 'app-donor-information-listing',
    templateUrl: './donor-information-listing.component.html',
    styleUrls: ['./donor-information-listing.component.scss']
})
export class DonorInformationListingComponent implements OnInit {

    @Input() searchForm:FormGroup;
    today = new Date();
    advancedToggle: boolean = false;
    donorList: Array<RenalDonor> = new Array<RenalDonor>();
    nationList: any;
    nationListFilter: Nationality;
    relationList: any;
    relationListFilter: any;
    searchData: any;
    totalRecords: number = 0;
    renalDonorSearch: FormGroup;
    rowData : Array<RenalDonor> = new Array<RenalDonor>();
    selectNationality:any;
    institutes: any[];
    donorInfo : RenalDonor[];
    allD : any;
    listGrid: GridOptions;
    columnDefs: any[];
    grids: any = [];
    gridOptions: GridOptions = <GridOptions>{
      enableColResize: true,
      pagination: true,
      
      paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
      onGridReady: ()=> {
        this.gridOptions.api.setRowData(this.donorInfo);
    },
      onGridSizeChanged: () => {
        this.gridOptions.api.sizeColumnsToFit();
      }
    };
    onReady(params) {
      this.gridOptions.api.sizeColumnsToFit();
    }


    constructor(fb: FormBuilder, private http: HttpClient, private _masterService: MasterService,
        private formBuilder: FormBuilder, private donorService: DonorService ) {

          this.searchForm = new FormGroup({
         
            'civilId': new FormControl,
            'fullname': new FormControl,
            'sex': new FormControl,
            'dob': new FormControl,
            'nationality': new FormControl,
            'bloodGroup': new FormControl,
            'telNo': new FormControl,
            'address':new FormControl,
            'donorType':new FormControl,
            'instCode':new FormControl,
            'instPatientId' : new FormControl,
            'relationDesc': new FormControl,
            'relationType':new FormControl,
          })
    
        this.getNationalityList();
        this.getRelationTypeMast();
    

        this.columnDefs = [
    
          { headerName: 'Donor ID', field: 'kidneyDonorId', minWidth: 100 },
          { headerName: 'Civil ID', field: 'civilId', minWidth: 100 },
          { headerName: 'Name' , field: 'fullname', minWidth: 150 },
          { headerName: 'Date of Birth',field: 'dob', minWidth: 60 },
          { headerName: 'gender',field: 'sex', minWidth: 60 },
          { headerName: 'Blood Group',field: 'bloodGroup' , minWidth:60},
          { headerName: 'Nationality',field: 'nationality' , minWidth:60},
          { headerName: 'Telephone Number',field: 'telNo' , minWidth:60},
          { headerName: 'Address',field: 'address' , minWidth:60},
          { headerName: ' Donating Hospital Name',field: 'instCode' , minWidth:60},
         
        ];
     
      
    }

    ngOnInit(): void {
        
      this.getList()
        
        this._masterService.institiutes.subscribe(res => {
          this.institutes = res["result"];
        })
      }

    getNationalityList(natCode: any = 0) {
        this._masterService.getNationalityList(natCode).subscribe(response => {
            this.nationList = response.result;
            this.nationListFilter = this.nationList;
            
        }, error => {

        });
    }

    getRelationTypeMast(relationCode: any = 0) {
        this._masterService.getRelationTypeMast(relationCode).subscribe(response => {
            this.relationList = response.result;
            this.relationListFilter = this.relationList;
        }, error => {

        });
    }


    getList() {

      if (this.gridOptions.api)
       { this.gridOptions.api.showLoadingOverlay(); }
  
      //let rowsPerPage = 10;
      //let startIndex = 0;
      let body = this.searchForm.value;
      body['rowsPerPage'] =10;
      body['startIndex'] =0;

      this.http.post(AppUtils.SEARCH_DONOR, body).subscribe(res => {
    
        if(res['code'] == "S0000") {
          this.rowData = res['result']['paginatedList'];
         
        } else {
          this.rowData =null;
          Swal.fire('Error!', res['message'], 'error')
        }
  
      }, error => {
        if (error.status == 401)
  
          Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
      })
  
    }


    clear(e) {
        this.searchForm.get("civilId").patchValue(null);
        this.searchForm.get("fullname").patchValue(null);
        this.searchForm.get("sex").patchValue(null);
        this.searchForm.get("dob").patchValue(null);
        this.searchForm.get("nationality").patchValue([""]);
        this.searchForm.get("bloodGroup").patchValue([""]);
        this.searchForm.get("telNo").patchValue(null);
        this.searchForm.get("address").patchValue(null);
        this.searchForm.get("instCode").patchValue([]);
        this.searchForm.get("instPatientId").patchValue([]);
     
      }


    

    
   


  


    

     

     
  
 

     
   
   


  


  


   

  
}
