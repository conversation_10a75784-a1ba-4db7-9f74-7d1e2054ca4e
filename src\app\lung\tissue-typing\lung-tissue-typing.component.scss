.entry-center {
  .form-control {
    text-align: center;
  }
}

.fontstyle {
  color: red;
  font-weight: bold;
}

#donor {
  position: relative;
}

.modal-dialog {
  position: fixed;
  width: 100%;
  margin: 0;
  padding: 10px;
}

.custome-height {
  height: 220px;
}

::ng-deep .tab-content {
  border: 1px solid #dee2e6;
  padding: 20px 10px;
  border-radius: 5px;
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

::ng-deep .antigens h6 {
  font-family: var(--font-bold);
  margin: 5px 0 10px;
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 5px;
}

.row-cont {
  padding: 15px;
  border-radius: 5px;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;

  &.selected-group {
    border-color: #007bff;
    background-color: #f8f9ff;
  }
}

.col-box {
  background: #f7f7f7;
  padding: 10px 15px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.check-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.check {
  display: flex;
  align-items: center;
  display: inline-block !important;
  margin-right: 0;

  input {
    display: none;
  }

  label {
    margin-bottom: 0;
    padding: 4px 7px;
    min-width: 55px;
    border: 1px solid #ccc;
    border-radius: 15px;
    text-align: center;

    &:hover {
      cursor: pointer;
      background: #dfdfdf;
    }
  }

  input[type="checkbox"]:checked + label {
    background-color: #dfdfdf;
  }
}

.chips {
  li {
    display: inline-block;
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    margin-right: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    background: #6c757d;

    i {
      margin-left: 10px;
      cursor: pointer;
    }
  }
}

.antigen-btn {
  min-width: 60px;
  margin-right: 5px;
  border-radius: 20px;
  // background: #fff;
  border-color: transparent;
  padding: 5px;
  transition: 0.3s;

  &:hover {
    border-color: #ccc;
    color: #444;
    background: none;
  }

  &.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
  }
}

.min-ht-100 {
  min-height: 100px;
}

.group-box {
  background: #f7f7f7;
  border-radius: 5px;
  padding: 15px;
  text-align: center;
}

.entry-center {
  label {
    white-space: nowrap;
  }
}

.row.gx-1 > [class*="col-"] {
  padding-left: 5px;
  padding-right: 5px;
}

.card .card-header {
  border: 0;
}


.datepicker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.datepicker-popup {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
}

// Selected group title styling
.selected-group-title {
  color: #007bff !important;
  font-weight: bold;
}