<h6 class="page-title">Cornea Transplant Listing</h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="corneaSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil Id</label>
                    <input type="number" min="0" (keyup.enter)="getList()" class="form-control form-control-sm"
                        formControlName="civilId" />
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Central Reg No</label>
                    <input type="number" (keyup.enter)="getList()" min="0" class="form-control form-control-sm"
                        formControlName="regNo" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Age (from)</label>
                    <input type="number" min="0" class="form-control form-control-sm" formControlName="ageFrom" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Age (to)</label>
                    <input type="number" min="0" class="form-control form-control-sm" formControlName="ageTo" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionDataListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Transplant Institute</label>
                    <ng-select [items]="instituteListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="regCode" formControlName="tranInst">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Transplant Date (from)</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="tranDateFrom" monthNavigator="true"
                        yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" showButtonBar="true">
                    </p-calendar>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Transplant Date (to)</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="tranDateTo" monthNavigator="true"
                        yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" showButtonBar="true">
                    </p-calendar>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Grafted Eye</label>
                    <ng-select #entryPoint [items]="eyeOperated" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="eyeOperated">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Transplant Outcome</label>
                    <ng-select [items]="transplantOutcomeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="description" bindValue="id" formControlName="transplantOutcome">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Visual acuity Before</label>
                    <ng-select [items]="visualAcuityListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="description" bindValue="id" formControlName="visAcuityBefore">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-4">
                <div class="form-group">
                    <label>Visula acuity After</label>
                    <ng-select [items]="visualAcuityListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="description" bindValue="id" formControlName="visAcuityAfter">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.description
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="button" (click)="exportExcel()" class="btn btn-primary ripple"> EXCEL</button>
                    <button type="reset" (click)="clear()" class="btn btn-sm btn-secondary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
                </div>
            </div>
        </div>
    </form>
</div>


<div style="margin-top:20px">
    <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
    </ag-grid-angular>
    <div *ngIf="rowData && rowData.length > 0">
        <p-paginator #paginator rows={{paginationSize}} totalRecords="{{totalRecords}}" (onPageChange)="getList($event)"
            showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
            [rowsPerPageOptions]="[10, 20, 30]">
        </p-paginator>
    </div>
</div>