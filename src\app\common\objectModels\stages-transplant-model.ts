import { RegistryPatient } from "../../_models/registry-patient.model";
export class stagesTransplantMode{

    runId: number;

	 rgTbRegistryPatient:{
		centralRegNo: number;
	 }


	 ckdFailureStage : number;
	
	 dialysisYn : string;
	
	 transplantYn : string;
	
	 preEmptiveYn : string;

	 transplantDatr : Date; 

	 transplantInst : number;
	
	 transplantPlaceDesc : string;

	 followupInst : number;

	 followupPlaceDesc : string;


	 constructor() {
        this.rgTbRegistryPatient = { centralRegNo: null };
    }
}

