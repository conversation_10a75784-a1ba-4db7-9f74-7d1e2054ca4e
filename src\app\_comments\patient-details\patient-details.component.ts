import { formatDate } from '@angular/common';
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import * as moment from "moment";
import Swal from 'sweetalert2';
import * as AppCompUtils from '../../common/app.component-utils';
import { LoginService } from '../../login/login.service';
import { GenderType } from '../../_models/gender-type';
import { MaritalStatusType } from '../../_models/marital-status.type';
import { MasterService } from '../../_services/master.service';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from '../../_helpers/common.constants';

@Component({
  selector: 'app-patient-details',
  templateUrl: './patient-details.component.html',
  styleUrls: ['./patient-details.component.scss']
})
export class PatientDetailsComponent implements OnInit {

  @Input() patientForm: FormGroup;
  @Output() uploaded = new EventEmitter<string>();
  @Output() fetchShifa = new EventEmitter<string>();
  @Output() callMethod = new EventEmitter<string>();
  @Output() checkAge = new EventEmitter<string>();
  private genderTypes = GenderType;
  public genderTypeOptions = [];
  private maritalStatus = MaritalStatusType;
  public maritalStatusOptions = [];
  wallayats: any[];
  wallayatsFilter: any[];
  regions: any[];
  villages: any[];
  menuList: any[];
  villagesFilter: any[];
  institutes: any[];
  institutesFull: any[];
  institutesFilter: any[];
  nationalityList: any[];
  @Input() submitted = false;
  @Input() downloadFromShifa = false;
  @Input() nationalityForDiabetic = false;
  @Input() callRenalWaitingLisitng = false;
  @Input() isChildNut = false;
  today = new Date();
  yearRange: string;


  constructor(private formBuilder: FormBuilder, private sharedService: SharedService, private masterService: MasterService, private loginService: LoginService) {
    this.initializeFormGroup();
  }

  ngOnInit() {

    //this.genderTypeOptions = Object.keys(this.genderTypes);
    this.genderTypeOptions = AppCompUtils.GENDER;
    //this.maritalStatusOptions = Object.keys(this.maritalStatus);
    this.maritalStatusOptions = AppCompUtils.Marital_Status_Type;
    this.getNationalityList();

    this.loadMasters();
    const currentYear = new Date().getFullYear();
    this.yearRange = `1930:${currentYear + 10}`;
  }

  private initializeFormGroup() {
    this.patientForm = this.formBuilder.group({
      'centralRegNo': [null],
      'patientId': [null, Validators.required],
      'civilId': [null, Validators.required],
      'dob': [null, Validators.required],
      'age': [null],
      'tribe': null,
      // 'tribe': ["", Validators.required],
      'firstName': [null, Validators.required],
      // 'firstExamDate': ["", Validators.required],
      'secondName': [null, Validators.required],
      'sex': [null],
      'thirdName': [null],
      'maritalStatus': [null],
      'createdInstid': [null],
      'village': [null],
      'walayat': [null],
      'region': [null],
      'kinTelNo': ['', [Validators.pattern('^[0-9]{8,16}$')]], // Allows between 8 and 16 digits
      'mobileNo': ['', [Validators.pattern('^[0-9]{8,16}$')]],  // Allows between 8 and 16 digits
      'tel': [null],
      'regInst': [null, Validators.required],
      'exDate': [null],
      'nationality': [null],
    })
  }

  get f() { return this.patientForm.controls; }

  loadMasters() {
    this.masterService.getRegionsMasterFull();
    this.masterService.regionsMasterFull.subscribe(value => {
      this.regions = value;
    });

    this.masterService.getVillagesMasterFull();
    this.masterService.villagesMast.subscribe(value => {
      this.villages = value;
    });


    this.masterService.getWallayatMasterFull();
    this.masterService.wallayatMasterFull.subscribe(value => {
      this.wallayats = value;
    });



    this.masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institutes = res['result'];
      this.institutesFilter = res['result'];
    })

    this.masterService.getInstitutesMasterFull();
    this.masterService.institutesMasterFull.subscribe(value => {
      this.institutesFull = value;
    });
  }

  // Add register file Institute to the institutes' list if it's not in user privilege. 
  updateInstitutesList(regInst) {

    if (this.institutes) {
      this.institutesFilter = this.institutes;
      if (regInst) {
        if (this.institutes.filter(s => s.estCode == regInst).length == 0) {
          let regEst = this.institutesFull.filter(s => s.estCode == regInst)[0];
          this.institutesFilter = [...this.institutesFilter, regEst];
        }
      }
    } else {
      if (regInst) {
        let regEst: any[];
        regEst = this.institutesFull.filter(s => s.estCode == regInst)[0];
        this.institutesFilter = this.institutesFull;
        this.institutesFilter = this.institutesFull.filter(s => s.estCode == regInst);
      }
    }

  }

  getEstById(estCode) {
    this.masterService.getInstitutesMasterByEstCode(estCode).subscribe(res => {
      this.institutes = res['result'];
      this.institutesFilter = res['result'];
    })
  }

  getAge(event) {
    this.patientForm.patchValue({ age: this.sharedService.ageFromDateOfBirthday(event) });
    this.checkAge.emit();
  }

  calculateAge(dob: any) {
    let years: any = moment().diff(dob, 'years', false);
    return years;
  }

  getDob(age) {
    this.checkAge.emit();
    age = age != null ? age.target.value : null;
    if (age) {
      this.patientForm.patchValue({ dob: this.sharedService.dobFromAge(age) });
    }
  }

  changeRegion(obj) {
    this.patientForm.patchValue({ village: null });
    this.patientForm.patchValue({ walayat: null });
    this.patientForm.patchValue({ region: obj.regCode });

    this.villagesFilter = [];
    this.wallayatsFilter = [];

    if (obj && obj.regCode) {
      this.wallayatsFilter = this.wallayats.filter(s => s.regCode == obj.regCode);
      this.patientForm.patchValue({ walayat: obj.walCode });
    }
  }

  changeWalayat(obj) {
    this.patientForm.patchValue({ village: null });
    this.villagesFilter = [];
    if (obj && obj.walCode) {
      this.villagesFilter = this.villages.filter(s => s.walCode == obj.walCode);
    }
  }




  changeInstitute(obj) {

    if (obj && !obj.regCode) {
      obj = this.institutes.filter(item => item.estCode == obj)[0];

    }
  }

  setPatientDetails(data) {
    //console.log("data--", data);
    this.updateInstitutesList(data.regInst);
    const momentDate = moment(data.rgTbPatientInfo.dob, "DD-MM-YYYY").toDate();

    data.rgTbPatientInfo.dob = momentDate; //formatDate(new Date(data.rgTbPatientInfo.dob), 'dd-MM-yyyy', 'en');
    this.patientForm.patchValue({ age: this.calculateAge(momentDate) });

    this.patientForm.patchValue(data.rgTbPatientInfo);
    this.patientForm.patchValue({ 'centralRegNo': data.centralRegNo, 'regInst': data.regInst });
    let reg;
    let wal;

    if (data.rgTbPatientInfo.village != null && this.villages != null && this.wallayats != null) {
      wal = parseInt(this.villages.filter(s => s.vilCode == data.rgTbPatientInfo.village).map(s => s.walCode).toString());
      reg = parseInt(this.wallayats.filter(s => s.walCode == wal).map(s => s.regCode).toString());
      let obj = { "regCode": reg, "walCode": wal };

      this.changeRegion(obj);
      this.changeWalayat(obj);
      this.patientForm.patchValue({ 'village': data.rgTbPatientInfo.village });
    }

  }

  setPatientDetailsAlShifa(data) {
    // const momentDate = moment(data.dob, "DD-MM-YYYY").toDate();

    const momentDate = this.sharedService.setDateFormat(data.dob);
    data.dob = momentDate; //
    this.patientForm.patchValue({ age: this.calculateAge(momentDate) });

    this.patientForm.patchValue(data);
    // this.patientForm.patchValue({ 'centralRegNo': data.centralRegNo, 'regInst': data.regInst });
    let status = data.maritalStatus === 2 ? "M" : (data.maritalStatus === 1) ? "S" : (data.maritalStatus === 4) ? "D" : (data.maritalStatus === 3) ? "W" : "O";
    this.patientForm.patchValue({ 'maritalStatus': status });

    let reg;
    let wal;
    if (data.village != null) {
      wal = parseInt(this.villages.filter(s => s.vilCode == data.village).map(s => s.walCode).toString());
      reg = parseInt(this.wallayats.filter(s => s.walCode == wal).map(s => s.regCode).toString());
      let obj = { "regCode": reg, "walCode": wal };

      this.changeRegion(obj);
      this.changeWalayat(obj);
      this.patientForm.patchValue({ 'village': data.village });
    }

  }

  clear() {
    this.updateInstitutesList(null);
    this.initializeFormGroup();
    // this.patientForm.reset();
  }
  //this.patientForm.patchValue({ age: this.calculateAge(momentDate) });

  setMPIDetails(data) {
    let mpiUser = {};
    mpiUser = data;
    this.patientForm.patchValue({
      firstName: mpiUser["firstNameEn"],
      secondName: mpiUser["secondNameEn"],
      // firstExamDate: mpiUser["firstExamDate"],
      thirdName: mpiUser["thirdNameEn"],
      tribe: mpiUser["sixthNameEn"],
      //dob: moment(mpiUser["birthDate"]).format("DD-MM-YYYY"),
      dob: new Date(mpiUser["birthDate"]),
      sex: mpiUser["sex"] === "Male" ? "M" : "F",
      age: this.calculateAge(new Date(mpiUser["birthDate"])),
      nationality: mpiUser["countryID"],
      maritalStatus: mpiUser["maritalStatus"] === "Married" ? "M" : (mpiUser["maritalStatus"] === "Single") ? "S" : (mpiUser["maritalStatus"] === "Divorced") ? "D" : (mpiUser["maritalStatus"] === "Widow/Widower") ? "W" : "O",
      mobileNo: mpiUser["mobileNo"],
      kinTelNo: mpiUser["kinTelNo"]
    });
    return this.patientForm.value;
  }


  fetchUser($event: any) {
    if (this.f.civilId.value !== "" && this.f.exDate.value !== "") {
      let mpiUser = {};

      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      const loginId = curUser['person'].perscode;

      let dob = this.f.dob.value;
      if (dob != null) {
        dob = formatDate(dob, 'yyyy-MM-dd', 'en');
      }

      let exDate = this.f.exDate.value;
      if (exDate != null) {
        exDate = formatDate(exDate, 'yyyy-MM-dd', 'en');
      }
      let req = {
        "birthDate": dob,
        "cardExpiryDate": exDate,
        "civilId": this.f.civilId.value,
        //"queryType": "string",
        //"requesterCivilId": "string",
        "requesterPersCode": loginId,
        // "type": 0 
      };
      // this.loaderService.display(true);
      //      this.loginService.getMpiDetails(this.f.civilId.value).subscribe(res => {
      this.masterService.getMpiV2Details(req).subscribe(res => {
        if (res["result"]) {
          mpiUser = res["result"];
          this.patientForm.patchValue({
            firstName: mpiUser["firstNameEn"],
            secondName: mpiUser["secondNameEn"],
            // firstExamDate: mpiUser["firstExamDate"],
            thirdName: mpiUser["thirdNameEn"],
            tribe: mpiUser["sixthNameEn"],
            //dob: moment(mpiUser["birthDate"]).format("DD-MM-YYYY"),
            dob: new Date(mpiUser["birthDate"]),
            sex: mpiUser["sex"] === "Male" ? "M" : "F",
            age: mpiUser["age"],
            nationality: mpiUser["countryID"],
            maritalStatus: mpiUser["maritalStatus"] === "Married" ? "M" : (mpiUser["maritalStatus"] === "Single") ? "S" : (mpiUser["maritalStatus"] === "Divorced") ? "D" : (mpiUser["maritalStatus"] === "Widow/Widower") ? "W" : "O",
            mobileNo: mpiUser["kinTelNo"],
            kinTelNo: mpiUser["mobileNo"]
          });
        } else {
          Swal.fire({
            title: 'Not found',
            text: 'The entered Civil Id did not match any record',
            icon: 'error'
          });

        }
      });
      //this.loaderService.display(false);
    } else {
      Swal.fire({
        title: '',
        text: 'The entered Civil Id and Expiry Date ',
        icon: 'error'
      });

    }
  }


  getNationalityList(natCode: any = 0) {
    this.masterService.getNationalityList(natCode).subscribe(response => {
      this.nationalityList = response.result;
    }, error => {
    });
  }

  fetchPatientInfo() {
    /*    var loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
       if (loggedInUser) {
         var inst = loggedInUser.institutes.filter(item => item.defaultYN === 'Y');
       }
       //inst[0].estCode
       this.masterService.findElderlyPatientVisitInfo(20068, this.f.patientId.value).subscribe(response => {
         var response = response["result"];
       }); */
    this.uploaded.emit('complete');
  }
  fetchPatientInfoByCivilID() {
    this.callMethod.emit();
  }
  callFetchDataFromAlShifa(estcode: any, patientId: any, valCode: any) {
    this.fetchShifa.emit();
  }
  callListingPage() {
    this.callMethod.emit();
  }

  validateFields() {
    if (!this.f.secondName.value || !this.f.firstName.value || !this.f.civilId.value || !this.f.patientId.value || !this.f.regInst.value || !this.f.dob.value) {
      Swal.fire({
        title: '',
        text: 'Mandatory Field is required ! ',
        icon: 'error'
      });
      this.submitted = true;
      return false;
    } else {
      this.submitted = false;
      return true;
    }
  }

  validateNationality() {
    if (!this.f.nationality.value || this.f.nationality.value === "null") {
      Swal.fire({
        title: 'Alert',
        text: 'To proceed, please select the Nationality',
        icon: 'warning'
      });
      return false;
    } else {
      return true;
    }
  }



  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }




}