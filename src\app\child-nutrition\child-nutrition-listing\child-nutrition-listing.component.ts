import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { GridOptions } from 'ag-grid-community';
import { Paginator } from 'primeng/primeng';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import Swal from 'sweetalert2';
import * as AppCompUtils from '../../common/app.component-utils';
import * as AppUtils from '../../common/app.utils';
import { ChildNutritionListResult } from 'src/app/_models/child-nutrition-list-result.model';
import { ChildNutritionService } from '../child-nutrition.service';
import * as moment from 'moment';
import { CellRenderer } from '@syncfusion/ej2-angular-grids';



@Component({
  selector: 'app-child-nutrition-listing',
  templateUrl: './child-nutrition-listing.component.html',
  styleUrls: ['./child-nutrition-listing.component.scss'],
  providers: [ChildNutritionService]
})
export class ChildNutritionListingComponent implements OnInit {


  @ViewChild('childNutritionPaginator', { static: false }) paginator: Paginator;
  childNutritionSearchForm: FormGroup;
  yesNo = AppCompUtils.YES_NO;
  visitTypeList = AppCompUtils.VISIT_TYPE;
  hbLevelListDiagnosis = AppCompUtils.HB_LEVEL;
  whProgressList = AppCompUtils.WH_PROGRESS;
  waProgressList = AppCompUtils.WA_PROGRESS;
  haProgressList = AppCompUtils.HA_PROGRESS;
  hcaProgressList = AppCompUtils.HCA_PROGRESS;
  haRatingList = AppCompUtils.HA_RATING;
  waRatingList = AppCompUtils.WA_RATING;
  whRatingList = AppCompUtils.WH_RATING;
  gender = AppCompUtils.GENDER;
  fileActivation =  AppCompUtils.ACTIVE_YN;
  rowData: Array<ChildNutritionListResult> = new Array<ChildNutritionListResult>();
  rowFilteredData: Array<ChildNutritionListResult> = new Array<ChildNutritionListResult>();
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  showSubType: boolean = false;
  showInsulinType: boolean = false;
  criteria: any = {};
  // modeRepList: any[];

  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };

  getWhProgressCellRender = (whProgress) => {
    if (whProgress.value) {
      let whProgressType
      this.whProgressList.forEach(ele => {
        if (whProgress.value == ele.id) {
          whProgressType = ele.value;
        }
      })
      return whProgressType
    } else {
      return ' '
    }
  };

  getWaProgressCellRender = (waProgress) => {
    if (waProgress.value) {
      let waProgressType
      this.waProgressList.forEach(ele => {
        if (waProgress.value == ele.id) {
          waProgressType = ele.value;
        }
      })
      return waProgressType
    } else {
      return ' '
    }
  };
  gethaProgressCellRender = (haProgress) => {
    if (haProgress.value) {
      let haProgressType
      this.haProgressList.forEach(ele => {
        if (haProgress.value == ele.id) {
          haProgressType = ele.value;
        }
      })
      return haProgressType
    } else {
      return ' '
    }
  };
  gethcaProgressCellRender = (hcaProgress) => {
    if (hcaProgress.value) {
      let hcaProgressType
      this.hcaProgressList.forEach(ele => {
        if (hcaProgress.value == ele.id) {
          hcaProgressType = ele.value;
        }
      })
      return hcaProgressType
    } else {
      return ' '
    }
  };

  

  getVisitTypeCellRender = (visit) => {
    if (visit.value) {
      let visitType
      this.visitTypeList.forEach(ele => {
        if (visit.value == ele.id) {
          visitType = ele.value;
        }
      })
      return visitType
    } else {
      return ' '
    }
  };

  getHaRatingCellRender = (haR) => {
    if (haR.value) {
      let haRating;
      this.haRatingList.forEach(ele => {
        if (haR.value == ele.id) {
          haRating = ele.value;
        }
      })
      return haRating
    } else {
      return ' '
    }
  };

  getWaRatingCellRender = (waR) => {
    if (waR.value) {
      let waRating;
      this.waRatingList.forEach(ele => {
        if (waR.value == ele.id) {
          waRating = ele.value;
        }
      })
      return waRating
    } else {
      return ' '
    }
  };

  getWhRatingCellRender = (whR) => {
    if (whR.value) {
      let whRating;
      this.whRatingList.forEach(ele => {
        if (whR.value == ele.id) {
          whRating = ele.value;
        }
      })
      return whRating
    } else {
      return ' '
    }
  };

  getRegionCellRender = (reg) => {
    if (reg.value) {
      let regName;
      this.regionData.forEach(ele => {
        if (reg.value == ele.regCode) {
          regName = ele.regName;
        }
      })
      return regName
    } else {
      return ' '
    }
  };

  getWilayatCellRender = (wal) => {
    if (wal.value) {
      let walName;
      this.wallayatList.forEach(ele => {
        if (wal.value == ele.walCode) {
          walName = ele.walName;
        }
      })
      return walName
    } else {
      return ' '
    }
  };

  getInstituteCellRender = (inst) => {
    if (inst.value) {
      let instName;
      this.institeList.forEach(ele => {
        if (inst.value == ele.estCode) {
          instName = ele.estName;
        }
      })
      return instName
    } else {
      return ' '
    }
  };

  getGenderCellRender = (sex) => {
    if (sex.value) {
      let sexName;
      this.gender.forEach(ele => {
        if (sex.value == ele.id) {
          sexName = ele.value;
        }
      })
      return sexName
    } else {
      return ' '
    }
  };



  columnDefs = [
    { headerName: 'EPI NO', field: 'epiNo', minWidth: 125, sortable: true },
    { headerName: 'NUTRITION NO', field: 'nutritionNo', minWidth: 125, sortable: true },
    { headerName: 'Region', field: 'regCode', minWidth: 125, sortable: true, cellRenderer: this.getRegionCellRender },
    { headerName: 'Wilayat', field: 'walCode', minWidth: 125, sortable: true, cellRenderer: this.getWilayatCellRender},
    { headerName: 'Institute', field: 'regInst', minWidth: 250, sortable: true, cellRenderer: this.getInstituteCellRender }, 
    { headerName: 'Gender', field: 'sex', minWidth: 125, sortable: true, cellRenderer: this.getGenderCellRender }, 
    { headerName: 'Cause of Outcome', field: 'outComeCause', minWidth: 125, sortable: true },
    { headerName: 'Registration Date', field: 'regDate', minWidth: 125, sortable: true, cellRenderer: this.getDateFormat },
    { headerName: 'Specified Action', field: 'status', minWidth: 125, sortable: true },
    { headerName: 'CENTRAL REG NO', field: 'centralRegNo', minWidth: 125, sortable: true },
    { headerName: 'VISIT TYPE', field: 'visitType', minWidth: 125, sortable: true, cellRenderer: this.getVisitTypeCellRender },
    {
      headerName: 'DIARRHEA', field: 'diarrheaYn', minWidth: 125, sortable: true,
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        } else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        } else {
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85
    },
    { headerName: 'HB LEVEL', field: 'hbLevel', minWidth: 125, sortable: true },
    {
      headerName: 'OEDEMA', field: 'oedemaYn', minWidth: 125, sortable: true,
      cellRenderer: (params: any) => {
        let result = '';
        if (params.value == "Y") {
          result = result + '<span><i class="fa fa-check text-success"></i></span>';
        } else if (params.value == "N") {
          result = result + '<span><i class="fa fa-times text-danger"></i></span>';
        } else {
          result = result + '<span><i class="far fa-clock fa-pulse"></i></span>';
        }
        return result;
      },
      width: 85
    },
    { headerName: 'VISIT DATE', field: 'visitDate', minWidth: 125, sortable: true, cellRenderer: this.getDateFormat },
    { headerName: 'Height for Age Z-Score', field: 'haZscore', minWidth: 125, sortable: true },
    { headerName: 'Stunting', field: 'haRating', minWidth: 125, sortable: true, cellRenderer: this.getHaRatingCellRender },
    { headerName: 'Weight for Age Z-Score', field: 'waZscore', minWidth: 125, sortable: true },
    { headerName: 'Underweight', field: 'waRating', minWidth: 125, sortable: true, cellRenderer: this.getWaRatingCellRender },
    { headerName: 'Weight for Height Z-Score', field: 'whZscore', minWidth: 125, sortable: true },
    { headerName: 'Wasting', field: 'whRating', minWidth: 125, sortable: true, cellRenderer: this.getWhRatingCellRender },
  ];
  diFaliedYnValue: boolean = null;
  oedFaliedYnValue: boolean = null;
  malStatus: any[];
  assOutcome: any[];
  regionData: any;
  wallayatList: any;
  wallayatListFilter: any;
  institeList: any;
  institeListFilter: any;
  constructor(private _router: Router, private _masterService: MasterService, private _ChildNutritionService: ChildNutritionService,
    private _sharedService: SharedService, private formBuilder: FormBuilder, public datepipe: DatePipe) {

    this.getMasterData();

    this.childNutritionSearchForm = this.formBuilder.group({
      'epiNo': [null],
      'nutritionNo': [null],
      'outComeCause': [null],
      'regDateFrom': [null],
      'regDateTo': [null],
      'status': [null],
      'centralRegNo': [null],
      'visitType': [null],
      'diarrheaYn': [null],
      'hbLevelFrom': [null], 
      'hbLevelTo': [null] ,   
      'hbLevel': [null],
      'hbDiagnosis':[null],
      'oedemaYn': [null],
      'visitDateFrom': [null],
      'visitDateTo': [null],
      'haZscore': [null],
      'haRating': [null],
      'waZscore': [null],
      'waRating': [null],
      'whZscore': [null],
      'whRating': [null],
      'visitDate': [null],
      'regDate': [null],
      'regCode': [null],
      'walCode': [null],
      'regInst': [null],
      'sex': [null],
      'activeYn':[null],
      'whProgress':[null],
      'waProgress':[null],
      'haProgress':[null],
      'hcaProgress':[null]
      
    });

  }

  ngOnInit() {

  }

  getMasterData(regCode: any = 0, walCode: any = 0) {

    this._ChildNutritionService.getChildNutritionMalStatusMast().subscribe(value => {
      this.malStatus = value.result;
    });

    this._ChildNutritionService.getChildNutritionAssOutcomeMast().subscribe(value => {
      this.assOutcome = value.result;
    });

    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {

    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {

    });
  }



  setDiaFaliedYn(value) {
    this.diFaliedYnValue = null;

    this.childNutritionSearchForm.get('diarrheaYn').patchValue(value);
    if (value == 'Y') {
      this.diFaliedYnValue = true;
    } else if (value == 'N') {
      this.diFaliedYnValue = false;
    } else if (value == null) {
      this.diFaliedYnValue = null;
    }
    // else{
    //   this.diFaliedYnValue = undefined;
    // }    
  }

  setOedFaliedYn(value) {
    this.childNutritionSearchForm.get('oedemaYn').patchValue(value);
    if (value == 'Y') {
      this.oedFaliedYnValue = true;
    } else if (value == 'N') {
      this.oedFaliedYnValue = false;
    } else if (value == null) {
      this.oedFaliedYnValue = null;
    }
    // else{
    //   this.oedFaliedYnValue = undefined;
    // }
  }


  get f() { return this.childNutritionSearchForm.controls; }


  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  /* ------------  filter action   ---------------- */
  getList(event?: any) {
    let body = this.childNutritionSearchForm.value;
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    }
    body = { pageable, ...body }

    this._ChildNutritionService.getChildNutritionListing(body).subscribe(res => {
      if (res['code'] == "S0000") {
        this.rowData = res['result']['content'];
        this.totalRecords = res['result']['totalElements'];

        this.criteria = body;
      } else {
        this.rowData = null;
        Swal.fire('Error!', res['message'], 'error')
      }
    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })
  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['child-nutrition/registry'], { state: { nutritionNo: event.data.nutritionNo } });
  }

  clear(e) {
    this.childNutritionSearchForm.reset();
    this.rowData = null;
    this.criteria = null;
    this.oedFaliedYnValue = null;
    this.diFaliedYnValue = null;
    this.institeListFilter = this.institeList;
    this.wallayatListFilter = this.wallayatList;
  }

  exportToExcel() {
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
        this._sharedService.exportAsExcelFile(this.rowData, "Child_Nutrition_Listing");
      } else {
        this._ChildNutritionService.getChildNutritionListing(this.criteria).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['paginatedList'];
            if (excelData && excelData.length > 0) {
              this._sharedService.exportAsExcelFile(excelData, "Child_Nutrition_Listing");
            }
          }
        });
      }
    } else {
      Swal.fire('Warning!', 'Please search first', 'warning')
    }
  }

  showDashboard() {
    this._sharedService.setNavigationData(this.rowData);
    this.criteria["startIndex"] = AppUtils.INDEX;
    this.criteria['rowsPerPage'] = this.totalRecords;
    this._sharedService.setCriteria(this.criteria);
    // this._router.navigate(['diabetic/home'], { state: null });
  }

  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  regSelect(event: any, field?: any) {
    this.childNutritionSearchForm.get('walCode').setValue(null);
    this.childNutritionSearchForm.get('regInst').setValue(null);
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }

  walSelect(event: any, field?: any) {
    this.childNutritionSearchForm.get('regInst').setValue(null);
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.childNutritionSearchForm.value['regCode'] && (this.childNutritionSearchForm.value['regCode'] != null || this.childNutritionSearchForm.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.childNutritionSearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.childNutritionSearchForm.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.childNutritionSearchForm.value['regCode'] && (this.childNutritionSearchForm.value['regCode'] != null || this.childNutritionSearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.childNutritionSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.childNutritionSearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }
}
