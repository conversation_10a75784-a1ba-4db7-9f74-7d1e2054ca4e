export interface CommunicationDetail {
  commId: number;
  instId?: number;
  sendDate?: Date;
  replyDate?: Date;
  remarks?: string;
  decision?: string;
  reason?: string;
  tissueNo?: string;
  receivedDate?: Date;
  rgTbCorReqAttachDtls?: AttachmentDetail[]; 
}

export interface AttachmentDetail {
  attachId: number;
  fileName?: string;
  filePath?: string;
  createdOn?: Date;
  createdBy?: number;
}
