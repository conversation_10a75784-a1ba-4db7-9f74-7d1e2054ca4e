import { environment } from 'src/environments/environment';

//  prod deploy -->  ng build --base-href=/eregistry/ --c prod
//  qa deploy -->  ng build --base-href=/eregistry/ --c qa

import { AUTH_TOKEN_API } from '../_helpers/common.constants';

export const BACKEND_OAUTH_API_URL: string = environment.centralAuthenticationHost;
export const AUTH_PREFIX: string = 'OauthServer';

//export const BACKEND_OAUTH_API_URL_OTP: string = 'http://**********:8083/OauthServer';

export const BACKEND_API_URL: string = environment.eregistryApi;
export const oAUTH_SYSCONFIG_PARAMS: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/getAuthConfigParams';
export const OTP_AUTHENTICATE_CHECK_IF_2FA_REQ: string = BACKEND_OAUTH_API_URL+ AUTH_PREFIX+'/otpLogin/checkIf2FAIsRequired/';
export const OTP_AUTHENTICATE_VALIDATE_USER: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/validateLoggedUser';
export const OTP_AUTHENTICATE_VALIDATE_OTP: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/validateOtp';
export const OTP_AUTHENTICATE_GET_OTP: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/getOtp';
export const MPI_SERVICE_AUTH: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/validateMpiDetails';
export const OTP_AUTHENTICATE_SAVE_PERSON_INFO: string = BACKEND_OAUTH_API_URL + AUTH_PREFIX+ '/otpLogin/saveOTPValidatedPerInfo';

export const BTC_END_POINT: string = BACKEND_API_URL + '/simple-captcha-endpoint';
export const BTC_VERIFICATION: string = BACKEND_API_URL + '/captchaVerification';








export const GET_USER_ROLES_AND_MENUS: string = BACKEND_OAUTH_API_URL + 'user/me?systemId=';

export const SYSTEM_ID_REGSTRY = 11;

export const INSTITUTE_MASTER: string = BACKEND_API_URL + 'api/master/getInstitutes';
export const USER_ROLES: string = 'reg-user-roles';
export const INSTITUTES: string = 'institutes';

export const MENU_ID: string = 'menu-id';

export const MASTER_CONTROL: string = BACKEND_API_URL + "api/master/";
export const INSTITUTES_USER_ROLES: string = MASTER_CONTROL + 'institutesUser';
export const GET_ALL_STATUS: string = BACKEND_API_URL + 'api/master/getAllStatus';
export const NATIONALITY_MASTER: string = BACKEND_API_URL + 'api/master/getNationality';
export const REGION_MASTER: string = BACKEND_API_URL + 'api/master/getRegions';
export const WALLAYAT_MASTER: string = BACKEND_API_URL + 'api/master/getWallayats?regCode=';
export const GET_MENU_SHOURT_NAME_LIST: string = BACKEND_API_URL + 'api/master/findRegShortNameList';

// User
export const CHANGE_PASSWORD: string = BACKEND_OAUTH_API_URL + 'OauthServer/user/changePwd';




export const GET_ALL_MOH_LAB_MASTER: string = MASTER_CONTROL + 'getLabMohTestMast';
export const GET_PLACE_OF_DEATH: string = MASTER_CONTROL + 'getPlaceOfDeath';
//getLabMohTestMast

//Renal Master
export const GET_DIALYSIS_HOSPITAL: string = MASTER_CONTROL + 'getDialysisHospital';
export const GET_DIALYSIS_CENTERS: string = MASTER_CONTROL + 'getDialysisCenters';


//Master Data
export const GET_REGION_MASTER: string = BACKEND_API_URL + 'api/master/region-mast';
export const GET_WALLAYAT_MASTER: string = BACKEND_API_URL + 'api/master/getWallayatMast';
export const GET_VILLAGE_MASTER: string = BACKEND_API_URL + 'api/master/getVillageMast';
export const GET_INSTITE_MASTER: string = BACKEND_API_URL + 'api/master/getInstiteMast';

// hospitals service
export const GET_HOSPITALS: string = BACKEND_API_URL + 'api/master/hospitals';
export const GET_NATION_MASTER: string = BACKEND_API_URL + 'api/master/getNationMast';
export const GET_RELATION_TYPE_MASTER: string = BACKEND_API_URL + 'api/master/getRelationTypeMast';
export const GET_DONOR_TYPE_MASTER: string = BACKEND_API_URL + 'api/master/getDonorTypeMast';
export const GET_ICD_LIST: string = BACKEND_API_URL + 'api/master/getAllIcd';
export const GET_ICD_RENAL_SHORT_LIST: string = BACKEND_API_URL + 'api/master/getShortRenalIcd';
export const GET_COMPLICATION_MAST: string = BACKEND_API_URL + 'api/master/getComplicationsMast';
export const GET_BLOOD_RELATION: string = BACKEND_API_URL + 'api/master/getBloodRelation';
export const GET_NON_BLOOD_RELATION: string = BACKEND_API_URL + 'api/master/getNonBloodRelationType';
export const GET_NOTIFICATION_TYPES: string = BACKEND_API_URL + 'api/master/getRegUrl';
///export const GET_INSTITE_MASTER: string = BACKEND_API_URL+'api/master/institutes';
export const GET_INSTITE_MASTER_BY_EST_CODE: string = BACKEND_API_URL + 'api/master/institutesById';

/// comment by saravana on 12-01-2024 for liver registry
export const GET_ICD_LIVER_SHORT_LIST: string = BACKEND_API_URL + 'api/master/getShortLiverIcd';

export const GET_ICD_DEATH_LIST: string = BACKEND_API_URL + 'api/master/getAllDeathIcd';
//export const GET_ICD_LIVER_LIST: string = BACKEND_API_URL + 'api/master/getAllLiverIcd';
export const GET_LIVER_TRANS_FWUP_COMP_LIST: string = BACKEND_API_URL + 'api/master/getAllLiverTransFwupComp';
export const GET_LIVER_TRANS_FWUP_MED_LIST: string = BACKEND_API_URL + 'api/master/getAllLiverTransFwupMed';
export const GET_LIVER_TRANS_FWUP_DISEASE_LIST: string = BACKEND_API_URL + 'api/master/getAllLiverTransFwupDisease';
export const GET_COR_SURGERY_TYPE_LIST: string = BACKEND_API_URL + 'api/master/getAllCorSurgeryType';
export const GET_COR_SURGEON_NAMES_LIST: string = BACKEND_API_URL + 'api/master/getAllCorSurgeonList';
export const GET_COR_VISUAL_ACUITY_LIST: string = BACKEND_API_URL + 'api/master/getAllCorVisualAcuity';
export const GET_COR_TRANSPLANT_OUTCOME_LIST: string = BACKEND_API_URL + 'api/master/getAllCorTransplantOutcome';
export const GET_COR_TRANS_DISEASE_LIST: string = BACKEND_API_URL + 'api/master/getAllCorTransDisease';
export const GET_COR_INDICATION_TYPE_LIST: string = BACKEND_API_URL + 'api/master/getAllCorIndicationType';
export const GET_COR_PRE_OP_COND_LIST: string = BACKEND_API_URL + 'api/master/getAllCorPreOpCond';
export const GET_RG_VW_COR_CHECK_LIST: string = BACKEND_API_URL + 'api/master/getAllCorCheckList';
export const GET_RG_VW_COR_CLEAR_ZONE_LIST: string = BACKEND_API_URL + 'api/master/getAllCorClearZone';
export const GET_RG_VW_COR_PRIMARY_DIAG_LIST: string = BACKEND_API_URL + 'api/master/getAllCorPrimaryDiag';
export const GET_RG_VW_COR_TISSUE_TYPE_LIST: string = BACKEND_API_URL + 'api/master/getAllCorTissueType';
export const GET_RG_VW_COR_REQ_INSTITUTES_LIST: string = BACKEND_API_URL + 'api/master/getAllCorReqInstitutes';
export const SAVE_RG_VW_COR_REQ_INSTITUTES: string = BACKEND_API_URL + 'api/master/saveCorReqInstitutes';
export const GET_RG_VW_COR_TRANS_DISEASE_PROC_BY_PREV_ID_LIST: string = BACKEND_API_URL + 'api/master/getAllCorTransDiseaseProcByPrevId';
export const GET_RG_VW_COR_OPERATIVE_DTLS_LIST: string = BACKEND_API_URL + 'api/master/getAllCorOperativeDtls';
export const GET_RG_VW_COR_OPER_DTLS_LIST: string = BACKEND_API_URL + 'api/master/getAllCorOperDtlsList';

export const GET_LUNG_ICD_LIST: string = BACKEND_API_URL + 'api/master/getAllLungIcd';
export const GET_ICD_LUNG_SHORT_LIST: string = BACKEND_API_URL + 'api/master/getShortLungIcd';

export const GET_HEART_ICD_LIST: string = BACKEND_API_URL + 'api/master/getAllHeartIcds';
export const GET_ICD_HEART_SHORT_LIST: string = BACKEND_API_URL + 'api/master/getShortHeartIcd';

export const GET_ALL_PRIMARY_ETOLOGY: string = BACKEND_API_URL + 'api/master/getAllPrimaryEtiology';

export const GET_ALL_HEART_NYHA_CLASS: string = BACKEND_API_URL + 'api/master/getAllHeartNyhaClass';

export const GET_ALL_HEART_INOTROPES: string = BACKEND_API_URL + 'api/master/getAllHeartInotropes';
export const GET_ALL_HEART_MCS_DEVICE: string = BACKEND_API_URL + 'api/master/getAllHeartMcsDevice';

export const GET_HEART_PHYSIO_BASELINE: string = BACKEND_API_URL + 'api/master/getHeartPhysioBaseline';

// Headers HTTP
export const STORAGE_ACCOUNT_ACCESS_TOKEN = 'access_token';
export const STORAGE_ACCOUNT_REFRESH_TOKEN = 'reg-refrresh-token';
export const STORAGE_ACCOUNT_EXPIRES_IN = 'expires-in';
// export const LOGIN_ID: string = 'ereg-user';
export const REDIRECT_LOGOUT_URI: string = '/login';


// alShifa Login
export const TRUSTED_LOGIN_CENTRAL_SERVICE: string = "trustedLogin/authenticateTrustedLogin?systemId=" + SYSTEM_ID_REGSTRY + "&estCode=";
// export const ALSHIFA_TRUSTED_PATIENT_ID: string = "ereg-patientId";
// export const EREG_TYPE_CODE: string = "ereg-type";
export const ALSHIFA_TRUSTED_CIVIL_ID: string = "ereg-civilId";
export const ALSHIFA_TRUSTED_REG_NO: string = "ereg-regNo";
// export const ALSHIFA_TRUSTED_LOGIN: string = "ereg-isTrusted";
// export const ALSHIFA_TRUSTED_LOGIN_INST: string = "ereg-trusted-inst";
export const DEFAULT_INSTITUTE: string = 'defaultInstitute';
// export const MENU: string = 'ereg-menus';
export const PERSON_NAME: string = 'ereg-person-name';
export const LOGIN_NAME: string = 'ereg-user-name';
// export const LOGIN_NAME: string = 'ereg-user-name';
export const CURRENT_USER: string = 'currentUser';
export const BLOOD_TYPE_CODE: number = -999;

export const ALSHIFA_TRUSTED_INFO: string = "alshifa-info";

//Vaccination
export const VACCINATION: string = 'vaccination/';
export const GET_VACCINATION_MAST_LIST: string = BACKEND_API_URL + VACCINATION + 'list-mast';
export const GET_VACCINATION_CIVILID: string = BACKEND_API_URL + VACCINATION + 'get-civilid';
export const VACCINATION_SAVE: string = BACKEND_API_URL + VACCINATION + 'save';
export const VACCINATION_UPDATE: string = BACKEND_API_URL + VACCINATION + 'update';
export const VACCINATION_HISTORY: string = BACKEND_API_URL + 'vaccineHistory/'

//Elderly  eregistry/api/eldr/findElderlyRegistry
export const ELDERLY: string = BACKEND_API_URL + "api/eldr";
export const GET_ELDERLY_REGISTRY: string = ELDERLY + "/get-regsitry";
export const SAVE_ELDERLY_REGISTRY: string = ELDERLY + "/registries/save";
export const ELDERLY_DASHBOARD: string = ELDERLY + "/dashboard";
export const FIND_ELDERLY_PATIENT_VISIT_INFO: string = BACKEND_API_URL + 'elderly/visit-info/';
export const SEARCH_ELDERLY_REGISTRY: string = ELDERLY + "/findElderlyRegistry";


//Renal Donor
export const GET_ALL_PATIENT: string = BACKEND_API_URL + 'api/renalDonor/allpatient';
export const GET_PATIENT_BY_CIVILID: string = BACKEND_API_URL + 'api/renalDonor/patient/';
export const SAVE_RENAL_DONOR_PATIENT: string = BACKEND_API_URL + 'api/renalDonor/patient';
export const SAVE_RENAL_DONOR: string = BACKEND_API_URL + 'api/renalDonor/save';
export const FIND_ALL_RENAL_DONOR: string = BACKEND_API_URL + 'api/renalDonor/all';
export const PATIENT_DTL: string = BACKEND_API_URL + 'renalDonor/patientInfo';
export const REG_DONOR_DTL: string = BACKEND_API_URL + 'api/renalDonor/Donor/';
export const REG_DONOR_DTL_BY_ID: string = BACKEND_API_URL + 'api/renalDonor/';

//Liver Donor
export const GET_ALL_LIVER_DONOR_PATIENT: string = BACKEND_API_URL + 'api/liverDonor/allPatient';
export const GET_PATIENT_BY_LIVERDONOR_CIVILID: string = BACKEND_API_URL + 'api/liverDonor/patient/';
export const SAVE_LIVER_DONOR_PATIENT: string = BACKEND_API_URL + 'api/liverDonor/patient';
export const SAVE_LIVER_DONOR: string = BACKEND_API_URL + 'api/liverDonor/save';
export const SAVE_LIVER_DONOR_REGISTRY: string = BACKEND_API_URL + 'api/liverDonor/saveLiverDonorRegistry';
export const FIND_ALL_LIVER_DONOR: string = BACKEND_API_URL + 'api/liverDonor/all';
export const LIVER_DONOR_PATIENT_DTL: string = BACKEND_API_URL + 'liverDonor/patientInfo';
export const REG_LIVER_DTL: string = BACKEND_API_URL + 'api/liverDonor/Donor/';
export const REG_LIVER_DTL_BY_ID: string = BACKEND_API_URL + 'api/liverDonor/';

export const GET_LIVER_PATIENTS_DONOR_BY_RELATION_TYPE: string = BACKEND_API_URL + 'api/liverDonor/getLiverPatientsDonorbyRelationType';

export const FETCH_ALL_LIVER_DONOR_LAB_FROM_ALSHIFA: string = BACKEND_API_URL + "api/liverDonor/fetch-all-liverdonor-lab-detail-from-alshifa";
export const FETCH_ALL_LIVER_DONOR_SURGERY_FROM_ALSHIFA: string = BACKEND_API_URL + "api/liverDonor/fetch-all-liverdonor-surgery-detail-from-alshifa";
export const FETCH_ALL_LIVER_DONOR_PROCEDURE_FROM_ALSHIFA: string = BACKEND_API_URL + "api/liverDonor/fetch-all-liverdonor-procedure-detail-from-alshifa";
export const FETCH_ALL_LIVER_DONOR_VACCINE_FROM_ALSHIFA: string = BACKEND_API_URL + "api/liverDonor/fetch-all-liverdonor-vaccine-detail-from-alshifa";


export const E_REGISTRY_PAGINATION_SIZE = 10;
export const INDEX = 0;

/**************** Renal Registry Controls ****************/
//Renal
export const RENAL: string = BACKEND_API_URL + "api/renal";
export const RENAL_DASHBOARD: string = RENAL + "/dashboard";
export const GET_RENAL_REGISTRY: string = RENAL + "/get-regsitry";
export const SAVE_RENAL_REGISTRY: string = RENAL + "/save-regsitry";
export const DELETE_DIAGNOSIS: string = RENAL + "/delete-diagnosis";
export const SEARCH_DONOR: string = BACKEND_API_URL + 'api/renalDonor/search';
export const SEARCH_RENAL: string = RENAL + "/search";
export const SAVE_CASE_STAGES: string = BACKEND_API_URL + "api/stages/save";     //should be deleted
export const SAVE_DIAYLSIS_INFO: string = BACKEND_API_URL + "api/diaylsis/saveDialysis"; //should be deleted
export const GET_RENAL_CASE_DETAILS: string = RENAL + "/get-case-details";
export const SAVE_RENAL_CASE_DETAILS: string = RENAL + "/save-case-details";
export const GET_PATIENTS_DECEASED_DONOR: string = RENAL + "/get-patients-for-deceased-donor";
export const UPDATE_RENAL_DONOR_PATIENT: string = RENAL + "/update-renal-donor-patient";



/**************** Brain Death Controls ****************/
//BrainDeath
export const BRAINDEATH: string = BACKEND_API_URL + "api/BrainDeath";
export const RENAL_BRAIN_DEATH_DETEMINATION: string = BRAINDEATH + "/brain-death";
export const RENAL_BRAIN_DEATH_EXAM_PARA: string = RENAL_BRAIN_DEATH_DETEMINATION + "/get-exam-para";
export const GET_RENAL_BRAIN_DEATH_REGISTER: string = RENAL_BRAIN_DEATH_DETEMINATION + "/get-register";
export const SAVE_RENAL_BRAIN_DEATH_REGISTER: string = RENAL_BRAIN_DEATH_DETEMINATION + "/save-register";
export const NOTIFY_RENAL_BRAIN_DEATH_REGISTER: string = RENAL_BRAIN_DEATH_DETEMINATION + "/sms";
// export const SEARCH_BRAIN_DEATH: string = RENAL_BRAIN_DEATH_DETEMINATION + "/getBrainDeathInfo";
export const SEARCH_BRAIN_DEATH: string = RENAL_BRAIN_DEATH_DETEMINATION + "/listing";
export const SEARCH_BRAIN_DEATH_LIST: string = BRAINDEATH + "/findBrainDeathList";

//
//Renal Waiting List
export const RENAL_WAITING_LIST: string = RENAL + "/waiting-list/";
export const FIND_RENAL_WAITING_LIST: string = RENAL_WAITING_LIST + "search";
export const FIND_RENAL_WAITING_LIST_RESULT: string = RENAL_WAITING_LIST + "get-by-central-reg-no";
export const SAVE_RENAL_WAITING_LIST: string = RENAL_WAITING_LIST + "save-all";
export const DELETE_RENAL_WAITING_LIST: string = RENAL_WAITING_LIST + "delete";






//Renal Priority Scoring
export const SAVE_RENAL_SCORES: string = RENAL + "/save-scores"
export const GET_RENAL_SCORES: string = RENAL + "/get-scores"

export const GET_RENAL_SCORES_BY_DONOR_ID: string = RENAL + "/get-scores-by-donorId"
export const GET_RENAL_SCORES_BY_REGNO: string = RENAL + "/get-scores-by-regNo"

//grid
export const GRID_PAGINATION_SIZE = 10;


//Organ Donors
export const ORGAN_DONORS: string = BACKEND_API_URL + 'api/organDonors';
export const GET_ORGAN_DONORS: string = ORGAN_DONORS + '/get';
export const GET_ORGAN_DONORS_DASHBOARD: string = ORGAN_DONORS + '/dashboard';
export const GENERATE_ORGAN_DONORS_PDF: string = ORGAN_DONORS + '/generate-pdf';
export const GENERATE_ORGAN_DONORS_PDF_CIVIL_ID: string = ORGAN_DONORS + '/generate-pdf-civilId';
export const SEARCH_ORGAN_REGISTRY: string = ORGAN_DONORS + "/findOrganDonors";





//lab
export const GET_ALL_TEST_NAME: string = BACKEND_API_URL + 'api/master/getAllLabTests';
export const SAVE_LAB_TEST: string = BACKEND_API_URL + 'api/lab/save';
export const UPDATE_LAB_RESULT: string = BACKEND_API_URL + 'api/lab/update';
export const GET_LAB_REGNO: string = BACKEND_API_URL + 'api/lab/get-centralRegNo';

export const GET_MPI_V2_DETAILS: string = BACKEND_API_URL + 'api/master/getMpiV2';



// tissue type api/hla/get-by-central-reg-no
export const DONOR_HLA: string = BACKEND_API_URL + 'api/hla/';
export const GET_ALL_TISSUE_TYPE: string = DONOR_HLA + 'all';
export const SAVE_TISSUE: string = DONOR_HLA + 'save';
export const SAVE_TISSUE_LIST: string = DONOR_HLA + 'save-list';
export const DELETE_TISSUE: string = DONOR_HLA + 'delete';
export const GET_RENAL_DONOR_HLA_BY_REG_NO: string = DONOR_HLA + 'get-by-central-reg-no';
export const GET_COMPARE_HLA_SCORE: string = DONOR_HLA + 'hla-score-compare';

export const SAVE_HLA_LIST_RETURN_RUN_ID: string = DONOR_HLA + 'saveListReturnRunId';

// Antigen Save HLA
export const SAVE_HLA_TYPING: string = DONOR_HLA + 'save-hla-typing-trans';
export const GET_HLA_TYPING: string = DONOR_HLA + 'get-hla-typing-trans';
export const SAVE_HLA_ANIGENT_HDR: string = DONOR_HLA + 'save-hla-unacceptable-hdr';
export const GET_HLA_ANIGENT_HDR: string = DONOR_HLA + 'get-hla-unacceptable-hdr';
export const GET_HLA_TISSUE_SCREENING: string = DONOR_HLA + 'get-tissue-screening';
export const GET_HLA_TISSUE_SCREENING_BY_DONOR_ID: string = DONOR_HLA + 'get-tissue-screening-by-donorId';
export const SAVE_HLA_TISSUE_SCREENING: string = DONOR_HLA + 'save-hla-tissue-screening';



//Renal Transplant
export const GET_Renal_Transplant_Followup: string = BACKEND_API_URL + 'api/renalTransplant/getFollowUp';
export const GET_Renal_Transplant_COMPLICATION: string = BACKEND_API_URL + 'api/renalTransplant/getComplication';
export const SAVE_Renal_Transplant_Followup: string = BACKEND_API_URL + 'api/renalTransplant/saveFollowUp';
export const SAVE_Renal_Transplant_COMPLICATION: string = BACKEND_API_URL + 'api/renalTransplant/saveComplication';

// Master Genetic Blood Disorder
export const GET_GENETIC_BLOOD: string = BACKEND_API_URL + 'api/master/getGeneticBlood?geneticTypeId=';
export const GET_COMPONENT_TEST: string = BACKEND_API_URL + 'api/master/getComponentTest?';
export const GET_GENO_TYPE_DESC: string = BACKEND_API_URL + 'api/master/getGenotypeDesc';
export const GET_GENO_TYPE_BASE: string = BACKEND_API_URL + 'api/master/getGenotypeBase';
export const GET_MEDICINE_MASTER: string = BACKEND_API_URL + 'api/master/getMedicineMaster';
export const GET_LAB_MASTER: string = BACKEND_API_URL + 'api/master/getLabMaster';
export const GET_SURGERY_MASTER: string = BACKEND_API_URL + 'api/master/getSurgeryMaster';
export const GET_VACCINE_MASTER: string = BACKEND_API_URL + 'api/master/getVaccineMaster';
export const GET_DISORDER_TYPE: string = BACKEND_API_URL + 'api/master/getDisorderType';
export const GET_MED_FREQUENCY: string = BACKEND_API_URL + 'api/master/getMedFrequency';
export const GET_MEDICINE_TYPE: string = BACKEND_API_URL + 'api/master/getMedicineType';
export const GET_PERSCODE_INFO: string = BACKEND_API_URL + 'api/master/getpersCode/';
export const GET_DIABETES_EXAM_PARAMS: string = BACKEND_API_URL + 'api/master/getDiabetesExamParams/';
export const GET_ALL_DIABETES_EXAM_PARAMS: string = BACKEND_API_URL + 'api/master/getAllDiabetesExamParams';


//GENETIC
export const GENETIC: string = BACKEND_API_URL + "api/genetic";
export const SEARCH_GENETIC: string = GENETIC + "/search";
export const GET_GENETIC_REGISTRY: string = GENETIC + '/get-regsitry';
export const SAVE_GENETIC_REGISTRY: string = GENETIC + '/save-regsitry';
export const CHECK_GENETIC_REGISTRY: string = GENETIC + '/check-regsitry';
export const GENETIC_DASHBOARD: string = GENETIC + "/dashboard";
export const FETCH_GENETIC_REGISTRY_FROM_ALSHIFA: string = GENETIC + "/fetch-from-alshifa";
export const FETCH_ALL_HPLC_FROM_ALSHIFA: string = GENETIC + "/fetch-all-hplc-from-alshifa";


//GENETIC Master Data
export const GET_GEN_COMPLICTION_FREQ: string = GENETIC + '/getGenComplicationsFrequency';
export const GET_GEN_VACCINE_LIST: string = GENETIC + '/getGenVaccineList';
export const GET_GEN_ICD_LIST: string = GENETIC + '/getGenIcdList';
export const GET_GEN_MEDICINE_LIST: string = GENETIC + '/getGenMedicineList';

//diagnosis

export const GET_ALL_DIAGNOSIS: string = BACKEND_API_URL + 'diagnosis/getAll';
export const GET_DIAGNOSIS_BY_CENTRAL_NO: string = BACKEND_API_URL + 'diagnosis/getByCentralRegNo';
export const SAVE_DIAGNOSIS: string = BACKEND_API_URL + 'diagnosis/save';


//surgery
export const GET_ALL_SURGERY: string = BACKEND_API_URL + 'surgery/getAll';
export const GET_SURGERY_BY_CENTRAL_REG_NO: string = BACKEND_API_URL + 'surgery/getByCentralRegNo';

//fetch blood disorder patient info from al shifa
export const GET_BLOOD_DISORDERD_PATIENT_INFO_ALSHIFA: string = BACKEND_API_URL + 'bloodDisorder/visit-info/';
export const GET_BLOOD_DISORDERD_CLINICAL_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderClinical/visit-info/';
export const GET_BLOOD_DISORDERD_SURGERY_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderSurgery/visit-info/';
export const GET_BLOOD_DISORDERD_LAB_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderLab/visit-info/';
export const GET_BLOOD_DISORDERD_BLOOD_TRANS_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderBloodTrans/visit-info/';
export const GET_BLOOD_DISORDERD_MEDICINE_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderMedicine/visit-info/';
export const GET_BLOOD_DISORDERD_LAB_FOLLOW_UP_INFO_ALSHIFA: string = BACKEND_API_URL + 'disorderLabFollowUp/visit-info/';
export const GET_BLOOD_DISORDERD_VACCINE_ALSHIFA: string = BACKEND_API_URL + 'disorderVaccine/visit-info/';
export const FETCH_PATIENT_DEATH_DETAILS_FROM_ALSHIFA: string = BACKEND_API_URL + "deathDetails";


/// getting Complication List View
export const GET_COMPLICATION_LIST: string = BACKEND_API_URL + 'api/master/getGenComplications';

/// getting Complication List View
export const GET_LIVER_DONOR_COMPLICATION_LIST: string = BACKEND_API_URL + 'api/master/getLiverDonorComplications';

// Transaction table data will handle when call Genetic Disorder Registry


// lab test component services and download lab from NEHR
export const GET_LAB_TEST_COMPONENT: string = BACKEND_API_URL + 'api/renal/getComponent?testid=';
export const LAB_TEST_TO_DOWNLOAD: string = BACKEND_API_URL + 'api/master/getTestLabToDownload';
// export const NEHR_LAB_TEST_DOWNLOAD : string = BACKEND_API_URL + 'nehrLabInfo';
export const NEHR_LAB_TEST_DOWNLOAD: string = BACKEND_API_URL + 'api/master/searchNehrLabTest';


// Get visit class count fron NEHR by Civil ID
export const NEHR_VISIT_CLASS_COUNT: string = BACKEND_API_URL + 'nehrVisitClassCount';

// DIABETIC
//export const DIABETIC: string = BACKEND_API_URL + "api/diabetic";
export const DIABETIC: string = BACKEND_API_URL + 'api/diabetic';
export const SEARCH_DIABETIC: string = DIABETIC + '/search';
export const SEARCH_DIABETIC_REGISTRY: string = DIABETIC + '/findDiabeticRegistry';
export const SAVE_DIABETIC_REGISTRY: string = DIABETIC + '/saveDiabetes';
export const SAVE_VISIT_DIABETIC_REGISTRY: string = DIABETIC + '/saveVisitDiabetes';
export const GET_DIAB_SENSOR_MASTER_DATA: string = DIABETIC + '/getDiabSensorsMasterData';
export const GET_DIAB_PUMPS_MASTER_DATA: string = DIABETIC + '/getDiabPumpTypesMasterData';
export const GET_DIAB_Registry_BY_CIVIL_ID: string = DIABETIC + "/getDiabeticRegistryByCivilId";




export const GET_DIABETIC_REGISTRY: string = DIABETIC + '/get-regsitry';

//export const GET_DIABETES_TYPES: string = BACKEND_API_URL + 'api/master/getDiabetesTypes';


//DIABETIC Master Data
export const GET_DIAB_LAB_INVST: string = BACKEND_API_URL + 'api/master/getDiabLabInvst';






export const DIABETIC_DASHBOARD_BY_REG_YEAR: string = DIABETIC + "/dashboardByRegYear";
export const DIABETIC_GET_MIN_MAX_YEAR: string = DIABETIC + "/getMinMaxYear";
export const GET_DIABETES_TYPES: string = BACKEND_API_URL + 'api/master/getDiabetesTypes';
export const GET_ALLDIABETIC_SUBTYPES: string = BACKEND_API_URL + 'api/master/getAllDiabeticSubtypes'
export const GET_ALLDIABETIC_DASHBOARD: string = BACKEND_API_URL + 'api/master/getAllDiabeticDashboard'
export const GET_DIABETES_PRES_MOD: string = BACKEND_API_URL + 'api/master/getDiabeticPresMode';



//export to excel
export const ELDERLY_EXPORTEXCEL: string = BACKEND_API_URL + 'api/export/excel';
export const ELDERLY_EXPORTEXCEL2: string = BACKEND_API_URL + 'api/export/excel2';

//export to genetic blood excel
export const GENETIC_GENETICBLOODEXCEL: string = BACKEND_API_URL + 'api/export-genetic-blood';


//Renal Export to excel
export const RENAL_RENALEXCEL: string = BACKEND_API_URL + 'api/export-renal-list';





//Vaccine
export const VACCINE: string = BACKEND_API_URL + "api/vaccine";
export const SEARCH_VACCINE_REGISTRY: string = VACCINE + "/findVaccineAdmDetls";

export const GETVACCINE: string = BACKEND_API_URL + "vaccine";
export const VACCINE_STOCK_MAST: string = GETVACCINE + "/get-Vaccine";
export const GET_TB_VACCINE_MASTER: string = BACKEND_API_URL + 'api/getVaccineMast';

// Vaccine Target
export const VACCINE_TARGET: string = BACKEND_API_URL + "Vaccine_Targets";
export const GET_VACCINE_TARGET_LIST: string = VACCINE_TARGET + "/findEstVwVaccineTargets";

//eregistry/api/vaccine/findImmunizationRegistry
export const GET_Imunization_REGISTRY: string = VACCINE + "/get-immunization-register";
export const GET_Imunization: string = VACCINE + "/getImmunizationPerPerson";

// save immunization
export const SAVE_Imunization_REGISTRY: string = VACCINE + "/save-immunization";

// get immunization dashboard
export const IMMUNIZATION_DASHBOARD: string = VACCINE + "/ImmunizationDashboard";




/**************** Define alll constant values here ****************/
// Registry Type
export const REG_TYPE_ELDERELY = 1; //Elderely Care Registry
export const REG_TYPE_RENAL = 2;    //Renal Registry
export const REG_TYPE_LIVER = 14;
export const REG_TYPE_ANC = 3;    //ANC Registry
export const REG_TYPE_DIABETES = 4;    //Diabetes Registry
export const REG_TYPE_GENETIC_BLOOD = 8;    //GENETIC BLOOD Registry
export const REG_TYPE_CHILD_NUT = 13;    //CHILD NUT Registry
export const REG_TYPE_IMMUNIZATION = 9; //IMMUNIZATION Care Registry
export const REG_TYPE_LUNG = 18;
export const REG_TYPE_HEART = 19;

// general constant for API response
export const RESPONSE_SUCCESS_CODE: string = 'S0000';
export const RESPONSE_NO_ECORD: string = 'F0000';
export const RESPONSE_SUCCESS_CODE_SAVE: number = 0;
export const RESPONSE_ERROR_CODE: number = 3;
export const RESPONSE_SUCCESS_CODE_MPI: number = 0;


// general constant for common Component use
export const CALLTYPE_BY_CIVILID = 'civilId';
export const CALLTYPE_BY_CRYSTALNO = 'crystalNo';
export const CALLTYPE_BY_REGISTRATION_NO = 'regNo';


// diabatic constant
export const DIABATIC_EYE_EXAM: string = 'E';
export const DIABATIC_FOOT_EXAM: string = 'F';
export const DIABETIC_SPECIFIC_TYPE = 1530;
export const DIAB_EYE_EXAM_PARAM_PREVID: number = 91;
export const DIAB_FOOT_EXAM_PARAM_PREVID: number = 90;

//Brain Death constant
export const FIRST_EXAMINATION: number = 1;
export const SECOND_EXAMINATION: number = 2;
export const FIRST_EXAM_SET: number = 1;
export const SECOND_EXAM_SET: number = 2;


//Call Type
export const CALL_TYPE_AL_SHIFA: string = "S";
export const CALL_TYPE_E_REGISTRY: string = "E";

//Asthma
export const ASTHMA: string = BACKEND_API_URL + "api/asthma";
export const ASTHMA_DASHBOARD: string = ASTHMA + "/dashboard";

// ANC

export const ANC_DASHBOARD: string = BACKEND_API_URL + 'api/ANC/dashboard';
// ANC Request
export const ANC: string = BACKEND_API_URL + "api/ANCCardRequest/anc";
export const SAVE_ANC_REQUEST: string = ANC + '/save-register';
export const GET_ANC_REQUEST: string = ANC + "/get-register";
export const GET_ANC_REQUEST_BY_ANC_NO: string = ANC + "/get-register-by-ancNo";
export const GET_ANC_REQUEST_BY_REQUESTED_ID: string = ANC + "/get-register-by-requestId";
export const GET_REQUEST_ANC_DETAILS: string = ANC + "/getAncRequestDetls";


export const DATE_WITH_HYPHEN: string = 'YYYY-DD-MM';


// ANC Register
export const ANC_REG: string = BACKEND_API_URL + "api/ANC/anc";
//export const ANC_REGISTER: string = BACKEND_API_URL + "api/ANC";
export const SAVE_ANC_REGISTER: string = ANC_REG + '/save-register';
export const GET_ANC_REGISTER_BY_ANC_NO: string = ANC_REG + "/get-registryByAncNo";
export const GET_ANC_REGISTER_BY_CIVILID: string = ANC_REG + "/get-registryByCivilId";
export const GET_VW_ANC_DTLS_BY_ANC_NO: string = ANC_REG + "/get-vwDtlsByAncNo";
export const GET_VW_ANC_REQUEST_DTLS_BY_ANC_NO: string = ANC_REG + "/get-vwAncRequestDtlsByAncNo";
export const GET_ANC_REGISTER: string = BACKEND_API_URL + "api/ANC/get-regsitry";
export const GET_REGISTER_ANC_DETAILS: string = ANC_REG + "/getAncRegisterList";



// ANC Master
export const ANC_MASTER: string = BACKEND_API_URL + "api/ANCMaster";
export const GET_ANC_LAB_INVEST: string = ANC_MASTER + "/getAllAncLabInvest";
export const GET_ANC_MIDICAL_HISTORY: string = ANC_MASTER + "/getAllMedicalHistory";
export const GET_ANC_BIRTH_SPACING: string = ANC_MASTER + "/getAllBirthSpacing";
export const GET_ABORTION_TYPE: string = ANC_MASTER + "/getAllAbortionType";
export const GET_DELIVERY_MODE: string = ANC_MASTER + "/getAllDeliveryMode";
export const GET_PERINEAL_TEARS: string = ANC_MASTER + "/getAllPerinealTears";
export const GET_DELIVERY_TYPE: string = ANC_MASTER + "/getAllDeliveryType";
export const GET_PREGNANCY_OUTCOME: string = ANC_MASTER + "/getAllPregnancyOutcome";
export const GET_MENSTURAL_CYCLE: string = ANC_MASTER + "/getAllMensturalCycle";

export const BD_DATE_FORMAT: string = 'DD-MM-YYYY HH:mm';



export const PATIENT_TYPE_ADULT: string = 'A';
export const PATIENT_TYPE_PEDIATIRICS_AND_NEONATES: string = 'P';

export const BD_INSTITUTE_NOTIFIED: string = 'Y';

export const CIVILID_ENTERY_MANUALLY: string = 'M';
export const CIVILID_ENTERY_NRS: string = 'N';



//Asthma Listing
export const GET_MED_ID_LIST: string = BACKEND_API_URL + 'api/master/getAllMedId';
export const GET_INHALER_LIST: string = BACKEND_API_URL + 'api/master/getInhalerDevId';
export const GET_BMI_VALUE_LIST: string = BACKEND_API_URL + 'api/master/getbmiList';
export const ASTHMA_LISTING: string = BACKEND_API_URL + "api/asthma";
export const GET_ASTHMA_LIST: string = ASTHMA_LISTING + "/listing";

//Child Listing
export const CHILD_LISTING: string = BACKEND_API_URL + "api/childRegistry";
export const GET_CHILD_LISTING: string = CHILD_LISTING + "/listing";


// Child Nutrition Master
export const CHILD_NUTRITION_MASTER = 'api/childNutritionMaster/'
export const GET_ASS_OUTCOME: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getAssessmentOutcome';
export const GET_MAL_STATUS: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getMalnutritionStatus';
export const GET_NUT_LAB: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getLabInvestigationsData';
export const GET_NUT_ASSESS_LACT: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getLactationData';
export const GET_NUT_CHECK_LIST: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getChecklistMasterData';
export const GET_NUT_KEY_MSG: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getKeyMessagesMasterData';
export const GET_NUT_MED_HISTORY: string = BACKEND_API_URL + CHILD_NUTRITION_MASTER + 'getNutMedHistoryMasterData';

//Child Nutrition
export const CHILD_NUTRITION: string = BACKEND_API_URL + "api/childNutrition";
export const GET_CHILD_NUTRITION_LISTING: string = CHILD_NUTRITION + "/listing";
export const GET_NUTRITION_PERIOD: string = CHILD_NUTRITION + "/getNutPeriod";
export const GET_CHILD_NUTRITION_Registry: string = CHILD_NUTRITION + "/getChildNutritionRegsitry";
export const GET_CHILD_NUTRITION_Registry_BY_CIVIL_ID: string = CHILD_NUTRITION + "/getChildNutritionRegsitryByCivilId";
export const GET_CHILD_NUTRITION_Z_SCORE_CALC: string = CHILD_NUTRITION + "/getZScoreCalc";
export const GET_CHILD_NUTRITION_Z_SCORE_CALCR: string = CHILD_NUTRITION + "/getZScoreCalcR";
export const SAVE_NUT_REGISTER: string = CHILD_NUTRITION + "/saveChildNutrition";
export const CHILD_NUT_DASHBOARD: string = CHILD_NUTRITION + "/dashboard";
export const GET_ZSCORE_DATASET: string = CHILD_NUTRITION + "/getZscoreDataset";
export const FETCH_CHILD_REGISTRY_FROM_ALSHIFA: string = CHILD_NUTRITION + "/fetch-from-alshifa";
export const FETCH_CHILD_LAB_DETAILS: string = CHILD_NUTRITION + "/getLabTestDetails";
export const CHECK_CHILD_REGISTRY: string = CHILD_NUTRITION + '/check-regsitry';


export const GET_CASE_DETAILS_lIVER: string = 'http://localhost:3000/'




/**************** Liver Registry Controls ****************/
//Liver
export const LIVER: string = BACKEND_API_URL + "api/liver";
export const LIVER_DASHBOARD: string = LIVER + "/dashboard";
export const GET_LIVER_REGISTRY: string = LIVER + "/get-regsitry";
export const SAVE_LIVER_REGISTRY: string = LIVER + "/save-regsitry";
export const LIVER_DELETE_DIAGNOSIS: string = LIVER + "/delete-diagnosis";
export const LIVER_SEARCH_DONOR: string = BACKEND_API_URL + 'api/liverDonor/search';
export const SEARCH_LIVER: string = LIVER + "/search";
export const LIVER_SAVE_CASE_STAGES: string = BACKEND_API_URL + "api/stages/save";     //should be deleted
export const LIVER_SAVE_DIAYLSIS_INFO: string = BACKEND_API_URL + "api/diaylsis/saveDialysis"; //should be deleted
export const GET_LIVER_CASE_DETAILS: string = LIVER + "/get-case-details";
export const SAVE_LIVER_CASE_DETAILS: string = LIVER + "/save-case-details";
export const SAVE_LIVER_TRANSPLANT: string = LIVER + "/save-transplant";
export const GET_LIVER_TRANSPLANT: string = LIVER + "/get-transplant";
export const LIVER_GET_PATIENTS_DECEASED_DONOR: string = LIVER + "/get-patients-for-deceased-donor";
export const LIVER_GET_PATIENTS_LIVER_DECEASED_DONOR: string = LIVER + "/get-patients-for-liver-deceased-donor";
export const UPDATE_LIVER_DONOR_PATIENT: string = LIVER + "/update-liver-donor-patient";

//liver Waiting List
export const LIVER_WAITING_LIST: string = LIVER + "/waiting-list";
export const FIND_LIVER_WAITING_LIST: string = LIVER_WAITING_LIST + "/search";

export const FIND_LIVER_WAITING_LIST_RESULT: string = LIVER_WAITING_LIST + "/get-by-central-reg-no";
export const SAVE_LIVER_WAITING_LIST: string = LIVER_WAITING_LIST + "/save-all";
export const DELETE_LIVER_WAITING_LIST: string = LIVER_WAITING_LIST + "/delete";

// Liver Master
export const GET_LIVER_COMPLICATION_MAST: string = BACKEND_API_URL + 'api/master/getLiverComplicationMast';
export const GET_LIVER_CURR_MGMT_MAST: string = BACKEND_API_URL + 'api/master/getLiverCurrMgmtListMast';
export const GET_LIVER_TRANS_PLANT_MAST: string = BACKEND_API_URL + 'api/master/getLiverTransPlantTypeListMast';
export const GET_LIVER_TRANS_PLANT_SUB_TYPE_MAST: string = BACKEND_API_URL + 'api/master/getLiverTransPlantSubTypeListMast';
export const GET_LIVER_TRANS_PLANT_MED_LIST: string = BACKEND_API_URL + 'api/master/getLiverTransPlantMedList';
export const GET_LIVER_SURGICAL_INFO_MAST: string = BACKEND_API_URL + 'api/master/getLiverSurgicalInfoMast';
export const GET_LIVER_SURGICAL_SUB_INFO_MAST: string = BACKEND_API_URL + 'api/master/getLiverSurgicalSubInfoMast';
export const GET_LIVER_TRANS_IND_MAST: string = BACKEND_API_URL + 'api/master/getLiverTransplantIndicationMast';
export const GET_LIVER_PROCEDURE_MAST: string = BACKEND_API_URL + 'api/master/getLiverProcedureMast';
export const GET_LIVER_DONOR_PROCEDURE_MAST: string = BACKEND_API_URL + 'api/master/getLiverDonorProcedureMast';






//LIVER Transplant

export const GET_LIVER_TRANSPLANT_FOLLOWUP: string = LIVER + '/getFollowUp';
export const GET_LIVER_TRANSPLANT_FOLLOWUP_BY_ID: string = LIVER + '/getFollowUpById';

export const GET_LIVER_TRANSPLANT_COMPLICATION: string = LIVER + '/getComplication';
export const SAVE_LIVER_TRANSPLANT_FOLLOWUP: string = LIVER + '/saveFollowUp';
export const SAVE_LIVER_TRANSPLANT_COMPLICATION: string = LIVER + '/saveComplication';
// ddl for Liver Transplant Follow Up
export const GET_MEDICINES_LIST = INSTITUTE_MASTER + '/medicines/list';
export const GET_DISEASE_LIST = INSTITUTE_MASTER + '/disease/list';

export const GET_LIVER_TRANSPLANT_MEDICINE: string = BACKEND_API_URL + 'api/master/liverTrasnplantMedList';

// Deceased Donor ERG - 580

export const DECEASED_DONOR_ETHICAL_APPROVAL_PARAM: string = BACKEND_API_URL + 'api/master/getEthicalApprovals';
export const DECEASED_DONOR_MEDICAL_HISTORY_PARAM: string = BACKEND_API_URL + 'api/master/getMedicalSocialHistory';
export const DECEASED_DONOR_ORGAN_RELATED_PARAM: string = BACKEND_API_URL + 'api/master/getOrganRelatedInvestigations';
export const DECEASED_DONOR_HEMODYNAMIC_PARAM: string = BACKEND_API_URL + 'api/master/getHemodynamicInstabilityEvaluation';
export const DECEASED_DONOR_OCCUPATION_MASTER: string = BACKEND_API_URL + 'api/master/getOccupationMaster';
export const DECEASED_DONOR_CANCER_SITE_LIST: string = BACKEND_API_URL + 'api/master/getCancerCategory';

export const DECEASED_DONOR_LAB_LIST: string = BACKEND_API_URL + 'api/master/getDonorLabListing';
export const DECEASED_DONOR_OTHER_LAB_LIST: string = BACKEND_API_URL + 'api/master/getDonorOtherLabListing';
export const DECEASED_DONOR_PROCEDURE_LIST: string = BACKEND_API_URL + 'api/master/getDonorProcedureListing';
export const DECEASED_DONOR_VITAL_LIST: string = BACKEND_API_URL + 'api/master/getDonorVitalSignListing';


export const DECEASED_DONOR: string = BACKEND_API_URL + "api/deceased-donors";
export const DECEASED_DONOR_DASHBOARD: string = DECEASED_DONOR + "/dashboard";
export const GET_DECEASED_DONOR_BYID: string = DECEASED_DONOR + "/get-deceased-donor";
export const GET_DECEASED_DONOR_REGISTRY: string = DECEASED_DONOR + "/get-deceased-donor-regsitry";
export const GET_DECEASED_DONOR_REGISTRY_CIVIL_ID: string = DECEASED_DONOR + "/get-deceased-donor-by-civil-id";
export const GET_DECEASED_DONOR_REGISTRY_CRYSTAL_NO: string = DECEASED_DONOR + "/get-deceased-donor-by-crystal-no";
export const GET_DECEASED_DONOR_BRAIN_DEATH_DETAIL: string = DECEASED_DONOR + "/get-brain-death-reg";
export const SAVE_DECEASED_DONOR_REGISTRY: string = DECEASED_DONOR + "/save-deceased-donor";
export const GET_DECEASED_DONOR_REGISTRY_LISTING: string = DECEASED_DONOR + "/get-deceased-donor-listing";
export const GET_DECEASED_DONOR_VITAL_INFO_ALSHIFA: string = DECEASED_DONOR + "/fetchVitalInfoFromShifa";
export const GET_DECEASED_DONOR_RESPIRATORY_INFO_ALSHIFA: string = DECEASED_DONOR + "/fetchRespReadingsFromShifa";
export const GET_DECEASED_DONOR_LAB_DTL_ALSHIFA: string = DECEASED_DONOR + "/fetchLabDtlInfoFromShifa";
export const DECEASED_DONOR_BRAIN_DEATH_ICD_LIST: string = DECEASED_DONOR + '/getBrainDeathAllIcd';
export const DECEASED_DONOR_INITIAL_ICD_LIST: string = DECEASED_DONOR + '/getInitialDiagAllIcd';



export const CORNEA: string = BACKEND_API_URL + "api/cornea";
export const FIND_CORNEA_PATIENT_RESULT: string = CORNEA + "/getCorTransplantDetails";
export const SAVE_CORNEA_PATIENT_RESULT: string = CORNEA + "/saveCorTransplantDetails";
export const SAVE_CORNEA_REQUEST: string = CORNEA + "/saveCorneaRequest";
export const GET_CORNEA_REQUEST_LIST: string = CORNEA + "/searchCorneaRequestList";
export const GET_CORNEA_REQUEST_BY_ID: string = CORNEA + "/getCorneaRequestByRequestNo";
export const GET_CORNEA_TRANSPLANT_LIST: string = CORNEA + "/searchCorneaTransplantList";
export const SAVE_CORNEA_UPLOAD_ATTACHMENT: string = CORNEA + "/uploadCorneaAttachment";
export const GET_CORNEA_FILE: string = CORNEA + "/getCorneaFile";
export const SAVE_CORNEA_REQ_SEND_DTLS: string = CORNEA + "/saveCorReqSendDtls";
export const SEND_EMAILS: string = CORNEA + "/sendEmails";

export const GET_SMS_NOTIFICATION: string = CORNEA + '/getSmsNotificationBySmsType';
export const GET_SMS_MSG: string = CORNEA + '/getRgTbSmsMsgBySmsType';
export const GET_CORNEA_REQ_SEND_DTLS_BY_ID: string = CORNEA + '/getReqSendDtlsByRequestId';
export const GET_CORNEA_DASHBOARD: string = CORNEA + "/dashboard";
// Lung Registry

export const LUNG: string = BACKEND_API_URL + "api/lung";
export const FIND_LUNG_REGISTRY: string = LUNG + "/getLungRegistry";
export const SAVE_LUNG_REGISTRY: string = LUNG + "/saveLungRegistry";
export const GET_CASE_DETAILS_LUNG: string = LUNG + "/getCaseDetails";
export const SAVE_LUNG_CASE_DETAILS: string = LUNG + "/saveCaseDetails";

export const DELETE_LUNG_DIAGNOSIS: string = LUNG + "/delete-diagnosis";

export const GET_LUNG_TRANSPLANT: string = LUNG + "/get-transplant";
export const SAVE_LUNG_TRANSPLANT: string = LUNG + "/save-transplant";
export const GET_LUNG_TRANSPLANT_LIST: string = LUNG + "/getLungTransplantList";
export const SEARCH_LUNG: string = LUNG + "/searchLungRegisterList";
export const GET_LUNG_DASHBOARD: string = LUNG + "/dashboard";

export const GET_LUNG_TRANS_IND_MAST: string = BACKEND_API_URL + 'api/master/getAllLungTransIndication';
export const GET_LUNG_TRANS_IND_DIS_MAST: string = BACKEND_API_URL + 'api/master/getAllLungTransIndDiseaseByPrevId';
export const GET_LUNG_DIS_MAST: string = BACKEND_API_URL + 'api/master/getAllLungDisClass';
export const GET_LUNG_MEDICINE_DTL_MAST: string = BACKEND_API_URL + 'api/master/getAllLungMedicineDtls';
export const GET_LUNG_PLEURODESIS_IND_MAST: string = BACKEND_API_URL + 'api/master/getAllLungPleurodesisInd';
export const GET_LUNG_ICD_MAST: string = BACKEND_API_URL + 'api/master/getAllLungIcd';
export const GET_LUNG_CURR_MGMT_MAST: string = BACKEND_API_URL + 'api/master/getAllLungCurNonPharma';
export const GET_PLEURODESIS_METHOD: string = BACKEND_API_URL + 'api/master/getAllLungPleurodesisMethod';
export const GET_LUNG_PLEURODESIS_TRANS_IND: string = BACKEND_API_URL + 'api/master/getAllLungPleurodesisInd';
export const GET_LUNG_MEDICINE_INFO_ALSHIFA: string = BACKEND_API_URL + 'lungRegistry/visit-info/';
export const FIND_LUNG_DONOR_LIST: string = LUNG + "/searchLungDonorList";

export const REG_LUNG_DTL_BY_ID: string = LUNG + '/getLungDonorByKidneyDonorId';
export const REG_LUNG_DTL_BY_CIVIL_ID: string = LUNG + '/getLungDonorByCivilId';
export const UPDATE_LUNG_DONOR_PATIENT: string = LUNG + "/updateLungDonorPatient";
export const SAVE_LUNG_DONOR_PATIENT: string = LUNG + '/savePatient';
export const GET_ALL_LUNG_DONOR_PATIENT: string = LUNG + '/getAllPatients';
export const GET_LUNG_DONOR_PATIENT_BY_CIVIL_ID: string = LUNG + '/getPatient';
export const GET_PATIENTS_DONOR_BY_RELATION_TYPE: string = LUNG + '/getPatientsDonorbyRelationType';

export const SMS_WEB_SERVICE: string = "http://eservices.healthnet.gov.om/util/local/sendSMSJSON"

export const GET_LUNG_PROCEDURES_MAST: string = BACKEND_API_URL + 'api/master/getLungProceduresMast';
export const GET_LUNG_DONOR_PROCEDURE_MAST: string = BACKEND_API_URL + 'api/master/getLungDonorProcedureMast';
export const GET_LUNG_DONOR_COMPLICATION_LIST: string = BACKEND_API_URL + 'api/master/getLungDonorComplications';
export const GET_HEART_DONOR_PROCEDURE_MAST: string = BACKEND_API_URL + 'api/master/getHeartDonorProcedureMast';
export const GET_HEART_DONOR_COMPLICATION_LIST: string = BACKEND_API_URL + 'api/master/getHeartDonorComplications';
export const FETCH_ALL_LUNG_DONOR_LAB_FROM_ALSHIFA: string = LUNG + "/fetch-all-lung-donor-lab-detail-from-alshifa";
export const FETCH_ALL_LUNG_DONOR_SURGERY_FROM_ALSHIFA: string = LUNG + "/fetch-all-lung-donor-surgery-detail-from-alshifa";
export const FETCH_ALL_LUNG_DONOR_PROCEDURE_FROM_ALSHIFA: string = LUNG + "/fetch-all-lung-donor-procedure-detail-from-alshifa";


export const SAVE_LUNG_DONOR_REGISTRY: string = LUNG + '/saveLungDonorRegistry';
export const FIND_LUNG_REGISTER_LIST: string = LUNG + '/searchLungRegisterList';
export const GET_LUNG_PATIENTS_DECEASED_DONOR: string = LUNG + "/get-patients-for-deceased-donor";

// Heart Registry

export const Heart: string = BACKEND_API_URL + "api/heart";
export const FIND_HEART_REGISTRY: string = Heart + "/getHeartRegistry";
export const SAVE_HEART_REGISTRY: string = Heart + "/saveHeartRegistry";

export const CASE_DETAILS_HEART: string = BACKEND_API_URL + "api/case-details-heart";
export const GET_CASE_DETAILS_HEART: string = CASE_DETAILS_HEART + "/getCaseDetails";
export const SAVE_HEART_CASE_DETAILS: string = CASE_DETAILS_HEART + "/saveCaseDetails";


export const GET_HEART_INOTROPE_AGENT: string = BACKEND_API_URL + 'api/master/getAllHeartInotropes';
export const GET_HEART_PS_CLEARANCE: string = BACKEND_API_URL + 'api/master/getAllHeartPsClearance';
export const GET_HEART_MCS_DEVICE: string = BACKEND_API_URL + 'api/master/getAllHeartMcsDevice';
export const GET_HEART_NYHA_CLASS: string = BACKEND_API_URL + 'api/master/getAllHeartNyhaClass';
export const GET_HEART_PRIMARY_ETIOLOGY: string = BACKEND_API_URL + 'api/master/getAllPrimaryEtiology';
export const GET_HEART_ICDS: string = BACKEND_API_URL + 'api/master/getAllHeartIcds';
export const GET_HEART_SHORT_ICDS: string = BACKEND_API_URL + 'api/master/getShortHeartIcds';
export const GET_ALL_HEART_DONOR_PATIENT: string = BACKEND_API_URL + 'api/master/getAllPatients';
export const REG_HEART_DTL_BY_ID: string = Heart + '/getHeartDonorByKidneyDonorId';
export const REG_HEART_DTL_BY_CIVIL_ID: string = Heart + '/getHeartDonorByCivilId';
export const SEARCH_Heart: string = Heart + "/search";
export const SAVE_HEART_DONOR_REGISTRY: string = Heart + '/saveHeartDonorRegistry';
export const GET_HEART_PATIENTS_DECEASED_DONOR: string = Heart + "/get-patients-for-deceased-donor";

export const FETCH_ALL_HEART_DONOR_LAB_FROM_ALSHIFA: string = Heart + "/fetch-all-heart-donor-lab-detail-from-alshifa";
export const FETCH_ALL_HEART_DONOR_SURGERY_FROM_ALSHIFA: string = Heart + "/fetch-all-heart-donor-surgery-detail-from-alshifa";
export const FETCH_ALL_HEART_DONOR_PROCEDURE_FROM_ALSHIFA: string = Heart + "/fetch-all-heart-donor-procedure-detail-from-alshifa";

export const GET_PATIENTS_HEART_DONOR_BY_RELATION_TYPE: string = Heart + '/getPatientsDonorbyRelationType';
export const UPDATE_HEART_DONOR_PATIENT: string = Heart + "/updateHeartDonorPatient";
export const SAVE_HEART_DONOR_PATIENT: string = Heart + '/savePatient';
export const GET_ALL_HEART_DONOR_PATIENTS: string = Heart + '/getAllPatients';
export const GET_HEART_DONOR_PATIENT_BY_CIVIL_ID: string = Heart + '/getPatient';
export const FIND_HEART_DONOR_LIST: string = Heart + '/searchHeartDonorList';
export const GET_HEART_DASHBOARD: string = Heart + "/dashboard";




//Mental Master Data
export const GET_VISIT_PURPOSE_LIST: string = BACKEND_API_URL + 'api/master/getAllVisitPurpose';
export const GET_DURATION_SYMPTOMS_LIST: string = BACKEND_API_URL + 'api/master/getAllHealthDuration';
export const GET_READMISSION_LIST: string = BACKEND_API_URL + 'api/master/getAllReadmission';
export const GET_DIAGNOSIS_ICD_LIST: string = BACKEND_API_URL + 'api/master/getAllDiagnosisIcd';
export const GET_SYMPTOM_LIST: string = BACKEND_API_URL + 'api/master/getAllSymptom';
export const GET_MEDICATION_LIST: string = BACKEND_API_URL + 'api/master/getAllMedications';
export const GET_COMORBIDITIES_LIST: string = BACKEND_API_URL + 'api/master/getAllComorbidities';
export const GET_PSYCHOTHER_LIST: string = BACKEND_API_URL + 'api/master/getAllHealthPsychotherapy';
export const GET_SCALE_LIST : string = BACKEND_API_URL + 'api/master/getAllHealthScales';
export const GET_CONDITIONS_LIST : string = BACKEND_API_URL + 'api/master/getAllHealthCondition';


//Mental Registry
export const MENTAL_REGISTERY: string = BACKEND_API_URL + "api/mentalHealth";
export const GET_MENTAL_REGISTRY_BY_CIVIL_ID: string = MENTAL_REGISTERY + "/getMentalRegistryByCivilId";
export const GET_MENTAL_REGISTRY_BY_CENTRAL_NO: string = MENTAL_REGISTERY + "/getMentalRegistryByCentralNo";
export const SAVE_MENTAL_REGISTRY: string = MENTAL_REGISTERY + '/saveMentalHealth';
export const GET_MEN_LAB: string = MENTAL_REGISTERY + '/getLabInvestigationsData';






// HLA Master
export const GET_ANIT_GEN_MASTER: string = BACKEND_API_URL + 'api/master/getHlaAntigenMast';