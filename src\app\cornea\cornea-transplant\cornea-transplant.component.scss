label,td{
  color: #212529 !important;
}

:host ::ng-deep .disease-right .dropdown-multiselect__caret:before,
:host ::ng-deep .left-transplant .dropdown-multiselect__caret:before,
:host ::ng-deep .operative-section .dropdown-multiselect__caret:before {
  border-width: 5px 5px 0 !important;
  right: -4px !important;
}

:host ::ng-deep .disease-right .multiselect-dropdown,
:host ::ng-deep .left-transplant .multiselect-dropdown,
:host ::ng-deep .operative-section .multiselect-dropdown,
:host ::ng-deep .disease-left .multiselect-dropdown {

  font-size: larger !important;
  border-color: #cccccc !important;
  width: 300px;
}

:host ::ng-deep .disease-right .ng-star-inserted,
:host ::ng-deep .left-transplant .ng-star-inserted,
:host ::ng-deep .operative-section .multiselect-dropdown .ng-star-inserted  {
  color: #999999;
  font-size: 14px;
}

:host ::ng-deep .transplant-section .ng-select.ng-select-single .ng-select-container ,
:host ::ng-deep .disease-section .ng-select.ng-select-single .ng-select-container,
:host ::ng-deep .operative-section .ng-select.ng-select-single .ng-select-container {
  width: 300px !important;
}

.disease-section input[type="number"],
.operative-section input[type="number"] {
  width: 300px !important;

}

.margin-custom  input[type="number"],
:host ::ng-deep .margin-custom .multiselect-dropdown,
:host ::ng-deep .margin-custom .ng-select.ng-select-single 
{
  margin-left: 10px !important;
}

:host ::ng-deep .multiselect-dropdown .selected-item{
      margin: 2px;
}