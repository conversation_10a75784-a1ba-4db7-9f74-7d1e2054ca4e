import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MasterService } from '../../_services/master.service';
import { medictionModel } from '../../common/objectModels/mediction-model';
import { FormArray } from '@angular/forms';
import { formatDate } from '@angular/common';
import * as CommonConstants from '../../_helpers/common.constants';
import * as moment from 'moment';
import Swal from 'sweetalert2';
import { GeneticService } from '../../genetic-blood-disorder/genetic.service';

@Component({
  selector: 'app-medication',
  templateUrl: './medication.component.html',
  styleUrls: ['./medication.component.scss']
})
export class MedicationComponent implements OnInit {
  @Output() uploaded = new EventEmitter<string>();
  @Output("callGenMedList") callGenMedList: EventEmitter<any> = new EventEmitter();
  @Output() downloadMedicationDtsAlshifa = new EventEmitter<void>();
  @Input() showAddNewButton: boolean = true;
  @Input() showMedicineButton: boolean = true;

  selectedDate: any;
  medList: any[];
  today = new Date();
  medicationForm: FormGroup;
  medicine: medictionModel[] = [];
  medicineTypeList: any[];
  medFrequencyList: any[];
  selectedMedicine: any;
  selectedMedicineType: any;
  delRow: any;
  data: any = [];
  // medicineId:any;
  medName: any = [];
  name: string;
  rgTbMedicineDtls: any = [];
  medFg: any = [];
  loginId: any;

  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  constructor(private _masterService: MasterService, private formBuilder: FormBuilder, private _geneticService: GeneticService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode
  }


  ngOnInit() {


    this.medicationForm = this.formBuilder.group({

      rgTbMedicineDtls: this.formBuilder.array([]),

    });

    this._masterService.getMedicineMaster().subscribe(res => {
      this.medicine = res.result;
      this.callGenMedList.emit(); //use to call special list if there.
    });

    this._masterService.getMedicineType().subscribe(res => {
      this.medicineTypeList = res.result;
    });

    this._masterService.getMedFrequency().subscribe(res => {
      this.medFrequencyList = res.result;

    });


  }



  ///////////////////P DATA TABLE


  onAddNewMed() {
    this.addNewMed('', this.loginId, this.currentDate, '', '', '', '', '', 'W', false)
    this.medFg[this.medFg.length - 1].isEditable = true;          //editable last entering row
  }
  addNewMed(runId: any = null, enteredBy: any = null, enteredDt: any = null, medicineID: any = null, startDate: any = null, dose: any = null, frequency: any = null, medicineType: any = null, source: any = null, isEditable: any = false): void {
    this.rgTbMedicineDtls = this.medicationForm.get('rgTbMedicineDtls') as FormArray;

    this.medFg = Object.assign([], this.rgTbMedicineDtls.value);
    const medItem: any = this.createMedItem(runId, enteredBy, enteredDt, medicineID, startDate, dose, frequency, medicineType, source, isEditable);
    this.rgTbMedicineDtls.push(this.createMedGrpItem(medItem));

    this.medFg.push(medItem);
    //this.medFg[this.medFg.length - 1].isEditable = true;
  }

  createMedGrpItem(createMedItem: any): FormGroup {
    return this.formBuilder.group(createMedItem);
  }

  createMedItem(runId: any = null, enteredBy: any = null, enteredDt: any = null, medicineID: any = null, startDate: any = null, dose: any = null, frequency: any = null, medicineType: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      enteredBy: enteredBy,
      enteredDt: enteredDt,
      medicineID: medicineID,
      startDate: startDate,
      dose: dose,
      frequency: frequency,
      medicineType: medicineType,
      source: source,
      isEditable: isEditable,

    };
  }

  getMedicineName(medicineID) {
    if (medicineID) {
      return this.medicine.filter(s => s.medicineMasterID == medicineID).map(s => s.medicineMasterValue)[0];
    }
  }
  getFrequencyName(frequency) {
    if (frequency) {
      return this.medFrequencyList.filter(s => s.courseId == frequency).map(s => s.coursename)[0];
    }

  }


  ///////////////////P DATA TABLE




  addNewMedication() {
    if (!this.medList) {
      this.medList = [];
    }
    this.medList.push({ medicineId: '', startDate: '', dose: '', frequency: '', medicineType: '' });

    this.medList[this.medList.length - 1].isEditable = true;

  }



  getMedication() {
  this.downloadMedicationDtsAlshifa.emit();
  }
  onRowEditInit(row: any) {
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");

  }
  onRowEditSave(row: any) {
    let rowIndex = this.medFg.indexOf(row);
    this.medFg[rowIndex] = this.medicationForm.value.rgTbMedicineDtls[rowIndex];
    let data = this.medFg[rowIndex]

    if (!data.medicineID || !data.startDate || !data.dose || !data.frequency || !data.medicineType) {
      Swal.fire("Please fill all required fields.");
      data.isEditable = true;
      return;
    }

    data.entryDate = moment(this.selectedDate, "DD-MM-YYYY").format();
    data.name = this.medicine.filter(s => s.medicineMasterID == data.medicineID).map(s => s.medicineMasterValue)[0];
    data.medTypeName = this.medicineTypeList.filter(s => s.id == data.medicineType).map(s => s.description)[0];
    data.frequencyName = this.medFrequencyList.filter(s => s.courseId == data.frequency).map(s => s.coursename)[0];
    data.isEditable = false;
  }
  delete(row: any) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbMedicineDtls = this.medicationForm.get('rgTbMedicineDtls') as FormArray;
        this.delRow = this.medFg.indexOf(row);
        this.medFg.splice(this.delRow, 1);
        this.rgTbMedicineDtls.removeAt(this.delRow);

      }
    })




  }

  /*
civilId: 121597306
        dose: "1 Mg"
      frequency: 100
                          medicineId: 815
      medicineType: null
patientId: 10377
reason: null
      startDate: "2021-03-22T06:46:24.000+0000"
strength: 1
strengthUnit: 254

runId,enteredBy,enteredDt, medicineID, startDate, dose, frequency,frequencyName, medicineType,medTypeName,source, isEditable, name
*/
  getDataFromAlshifa(sendingData: any = 0) {
    //this.medList = sendingData;   //this is not use inn datatable
    sendingData.forEach(element => {
      this.addNewMed(null, null, element.startDate, element.medicineId, element.startDate,
        element.dose, element.frequency, element.medicineType, 'S', false);
    });
  }

  getMedName(medId) {
    return this.medicine.find(med => med.medicineMasterID === medId)['medicineMasterValue'] || '-';
  }

  clear() {
    this.medFg = [];
    this.medicationForm.reset();

    this.medicationForm = this.formBuilder.group({
      rgTbMedicineDtls: this.formBuilder.array([]),
    })
  }

}
