import { Establishment } from './EstablishmentModel';
import {RoleDataModel} from './role-model'; 
import {RegistriesDataModel} from './registries-model'; 
import { SystemIdDataModel } from './system-id-model'


export class UserMasterDataModel {
    id:number;
    perscode : Number;
    name : String;
    loginId : String;
    status : boolean;
    institute:Array<Establishment>;
    roles: Array<RoleDataModel>;
    registry : Array<RegistriesDataModel>;
    systems : Array<SystemIdDataModel>;

}