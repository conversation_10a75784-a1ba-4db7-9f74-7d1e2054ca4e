import { Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import Swal from "sweetalert2";
import * as FileSaver from "file-saver";
import * as moment from "moment";
import { Paginator } from "primeng/components/paginator/paginator";
import { GridOptions } from "ag-grid-community";

import { RegionDataModel } from "../../common/objectModels/region-model";
import { ICDList } from "../../common/objectModels/icdList-models";

import { MasterService } from "../../_services/master.service";
import { DeceasedDonorService } from "../deceased-donor.service";
import { SharedService } from "../../_services/shared.service";

import * as AppCompUtils from "../../common/app.component-utils";
import * as AppUtils from "../../common/app.utils";
import * as AppParam from "../../_helpers/app-param.constants";
import * as CommonConstants from "../../_helpers/common.constants";
import { LiverExcel } from "src/app/_models/liver-excel";
import {
  bmiList,
  DeceasedDonorExportExcel,
  RgTbDeceasedDonorDto,
  RgVwOccupationMast,
} from "src/app/_models/deceased-donor.model";

@Component({
  selector: "app-deceased-donor-listing",
  templateUrl: "./deceased-donor-listing.component.html",
  styleUrls: ["./deceased-donor-listing.component.scss"],
})
export class DeceasedDonorListingComponent implements OnInit {
  @ViewChild("elderlyRegPaginator", { static: false }) paginator: Paginator;
  excelCriteria: any = {};
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  occupationList: { id: number; value: string }[] = [];
  deceasedDonorSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  displayedResults: any[] = [];
  allResults: any[] = [];
  yesNo = AppCompUtils.YES_NO;
  status = AppCompUtils.STATUS_PATIENT;
  gender = AppCompUtils.GENDER;
  icdLiverList: Array<ICDList> = new Array<ICDList>();
  deceasedDonorExcel: Array<DeceasedDonorExportExcel> =
    new Array<DeceasedDonorExportExcel>();
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  rowData: Array<RgTbDeceasedDonorDto> = new Array<RgTbDeceasedDonorDto>();
  dataList: Array<DeceasedDonorExportExcel> =
    new Array<DeceasedDonorExportExcel>();
  gridOptions: GridOptions = <GridOptions>{
    //enableColResize: true,
    pagination: false,
    //resizable: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
  };
  columnDefs: any[];
  hospitalsList: any;
  dateFormat = "dd/mm/yy";

  nationalityList: any[];

  brainDeathIcdList: { id: string; value: string }[] = [];
  initialICDList: { id: string; value: string }[] = [];
  mapBloodGroupList = AppCompUtils.MAP_BLOOD_GROUP;
  lowValue: any;
  highValue: any;
  bmiList: Array<bmiList>;

  maritalStatusList: any[] = [
    { id: "M", value: "Married" },
    { id: "S", value: "Single" },
    { id: "D", value: "Divorced" },
    { id: "W", value: "Widowed" },
    { id: "U", value: "Unmarried" },
  ];

  constructor(
    private _router: Router,
    private _masterService: MasterService,
    private _deceasedDonorService: DeceasedDonorService,
    private _sharedService: SharedService,
    private formBuilder: FormBuilder
  ) {
    this.getMasterData();
    this.deceasedDonorSearchForm = this.formBuilder.group({
      civilId: [null],
      crystalNo: [null],
      sex: [""],
      estCode: [null],
      bloodGroup: [""],
      instCode: [null],
      dob: [null],
      occupation: [null],
      ventilationDt_from: [null],
      ventilationDt_to: [null],
      admissionDate_from: [null],
      admissionDate_to: [null],
      brainDeathTime_from: [null],
      brainDeathTime_to: [null],
      brainDeathCause: [null],
      cancerYn: [null],
      transfusionYn: [null],
      bmi: [null],
      initialDiag: [null],
      nationality: [null],
      donorType: [null],
      skinInspectionYn: [null],
      palpationYn: [null],
      palpationDt_from: [null],
      palpationDt_to: [null],
      age: [null],
      ageFrom: [null],
      ageTo: [null],
      fullName: [null],
      donorId: [null],
      bmiLow: [null],
      bmiHigh: [null],

      startIndex: [null],
      rowsPerPage: [null],
    });

    this.columnDefs = [
      {
        headerName: "Crystal No",
        field: "crystalNo",
        minWidth: 100,
        sortable: true,
        resizable: true,
      },
      {
        headerName: "Civil ID",
        field: "civilId",
        minWidth: 100,
        sortable: true,
        resizable: true,
      },
      {
        headerName: "Name",
        field: "fullName",
        minWidth: 100,
        sortable: true,
        resizable: true,
      },
      {
        headerName: "DOB",
        field: "dob",
        minWidth: 100,
        sortable: true,
        resizable: true,
        cellRenderer: this.getDateFormat,
      },
      {
        headerName: "Institute",
        field: "instCode",
        minWidth: 100,
        sortable: true,
        resizable: true,
        valueFormatter: this.getInstituteName,
      },
      { headerName: "Gender", field: "sex", minWidth: 100, sortable: true },
      {
        headerName: "Weight : Height = BMI",
        field: "bmi",
        minWidth: 100,
        sortable: true,
      },
      {
        headerName: "BloodGroup",
        field: "bloodGroup",
        minWidth: 100,
        sortable: true,
        resizable: true,
        cellRenderer: this.renderBloodGroup,
      },
      {
        headerName: "Nationality",
        field: "nationality",
        minWidth: 100,
        sortable: true,
        valueFormatter: this.getNationlityName,
      },
      {
        headerName: "Ventilation Date",
        field: "ventilationDt",
        minWidth: 100,
        sortable: true,
        cellRenderer: this.getDateFormat,
      },
      {
        headerName: "Admission Date",
        field: "admissionDate",
        minWidth: 100,
        sortable: true,
        cellRenderer: this.getDateFormat,
      },
      {
        headerName: "Brain Death Time",
        field: "brainDeathTime",
        minWidth: 100,
        sortable: true,
        resizable: true,
        cellRenderer: this.getDateFormat,
      },
      {
        headerName: "Brain Death Cause",
        field: "brainDeathCause",
        minWidth: 100,
        sortable: true,
        resizable: true,
        valueFormatter: this.getBrainDeathIcdName,
      },
      {
        headerName: "Cancer Y/N",
        field: "cancerYn",
        minWidth: 100,
        sortable: true,
      },
      {
        headerName: "Transfusion Y/N",
        field: "transfusionYn",
        minWidth: 100,
        sortable: true,
      },
      {
        headerName: "Initial Diagnosis",
        field: "initialDiag",
        minWidth: 100,
        sortable: true,
        resizable: true,
        valueFormatter: this.getinitialICDName,
      },
    ];
  }

  ngOnInit() {
    this.getMasterData();
  }

  trackByFn(index, item) {
    return index;
  }

  getMasterData() {
    this.loadBrainDeathIcdList();
    this.loadInitialIcdList();
    this.getNationalityList();
    this._masterService.getbmiValueList();
    this._masterService.bmiList.subscribe((value) => {
      this.bmiList = value;
    });

    this._masterService.getHospitals().subscribe((response) => {
      this.hospitalsList = response["result"];
    });

    this._masterService
      .getOccupationMaster()
      .subscribe((data: RgVwOccupationMast[]) => {
        if (data && data.length > 0) {
          this.occupationList = data.map((item) => ({
            id: Number(item.occupationCode), // Assuming `item.id` exists in the API response
            value: item.occupationName,
          }));
        }
      });
  }

  getNationlityName = (nationalityCode: any): string => {
    if (nationalityCode != null && nationalityCode.value) {
      let nationalityName = "";
      this.nationalityList.forEach((item) => {
        if (item.natCode == nationalityCode.value) {
          nationalityName = item.nationality;
        }
      });
      return nationalityName;
    } else {
      return "";
    }
  };

  getOccupationName = (occupationCode: any): string => {
    if (occupationCode != null && occupationCode.value) {
      let occupationName = "";
      this.occupationList.forEach((item) => {
        if (item.id == occupationCode.value) {
          occupationName = item.value;
        }
      });
      return occupationName;
    } else {
      return "";
    }
  };

  getMaritalStatusName = (maritalStatusCode: any): string => {
    if (maritalStatusCode != null && maritalStatusCode.value) {
      let maritalStatusName = "";
      this.maritalStatusList.forEach((item) => {
        if (item.id == maritalStatusCode.value) {
          maritalStatusName = item.value;
        }
      });
      return maritalStatusName;
    } else {
      return "";
    }
  };

  getInstituteName = (params: any): string => {
    //console.log('params',params)
    if (params != null && params.value) {
      let instituteName = "";

      if (this.hospitalsList && this.hospitalsList.length > 0) {
        this.hospitalsList.forEach((item) => {
          if (item.estCode == params.value) {
            instituteName = item.estName;
          }
        });
        return instituteName;
      }
    } else {
      return "";
    }
  };

  getinitialICDName = (icdCode: any): string => {
    if (icdCode != null && icdCode.value) {
      let icdName = "";
      this.initialICDList.forEach((item) => {
        if (item.id == icdCode.value) {
          icdName = item.value;
        }
      });
      return icdName;
    } else {
      return "";
    }
  };

  getBrainDeathIcdName = (icdCode: any): string => {
    if (icdCode != null && icdCode.value) {
      let icdName = "";
      this.brainDeathIcdList.forEach((item) => {
        if (item.id == icdCode.value) {
          icdName = item.value;
        }
      });
      return icdName;
    } else {
      return "";
    }
  };

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationalityList = response.result;
      },
      (error) => {}
    );
  }

  isEmptyNullZero(val) {
    return val === undefined || val == null || val === "null" || val <= 0
      ? false
      : true;
  }

  bloodGroupSelect(event: any, group?: any) {
    const selectedBloodGroups = this.deceasedDonorSearchForm.get(
      "bloodGroup"
    ) as FormControl;
    const selectedValue = event.target.value;
    const isChecked = event.target.checked;

    let currentSelectedValues = selectedBloodGroups.value
      ? [...selectedBloodGroups.value]
      : [];

    const bloodGroup = this.bloodGroupList.find(
      (group) => group.value === selectedValue
    );
    const selectedId = bloodGroup ? bloodGroup.id : null;

    if (
      isChecked &&
      selectedId &&
      !currentSelectedValues.includes(selectedId)
    ) {
      currentSelectedValues.push(selectedId);
    } else if (!isChecked && selectedId) {
      currentSelectedValues = currentSelectedValues.filter(
        (id) => id !== selectedId
      );
    }

    selectedBloodGroups.setValue(currentSelectedValues);
    this.updateDisplayedResults(currentSelectedValues);
  }

  updateDisplayedResults(selectedValues: string[]) {
    if (selectedValues.length === 0) {
      this.displayedResults = this.allResults;
    } else {
      this.displayedResults = this.allResults.filter((result) =>
        selectedValues.includes(result.bloodGroupID.toString())
      );
    }
  }

  public onCellDoubleClicked(event: any) {
    // console.log('crystalNo',event.data.crystalNo)
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(["deceased/registry"], {
      state: { crystalNo: event.data.crystalNo },
    });
  }

  clear(e) {
    this.deceasedDonorExcel = null;
    this.deceasedDonorSearchForm.reset();
    this.rowData = null;
  }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  exportExcel() {
    if (this.rowData && this.rowData.length > 0) {
      this.formatData(this.rowData);
    } else {
      Swal.fire("Warning!", "No Records to export", "warning");
    }
  }

  formatData(rowData) {
    this.deceasedDonorExcel = [];
    rowData.forEach((el) => {
      //let calBmi = this._sharedService.calculateBMI(el.weight, el.height);
      // let bmiFull = el.weight + " : " + el.height + " = " + calBmi;
      this.deceasedDonorExcel.push({
        CrystalNo: el.crystalNo,
        CivilId: el.civilId,
        Name: el.fullName,
        Dob: this.getDateFormat({ value: el.dob }),
        Gender: el.sex,
        BloodGroup: this.renderBloodGroup({ value: el.bloodGroup }),
        InstCode: this.getInstituteName({ value: el.instCode }),
        MaritalStatus: this.getMaritalStatusName({ value: el.maritalStatus }),
        Nationality: this.getNationlityName({ value: el.nationality }),
        Occupation: this.getOccupationName({ value: el.occupation }),
        Bmi: el.bmi,
        CancerYn: el.cancerYn,
        VentilationDt: this.getDateFormat({ value: el.ventilationDt }),
        AdmissionDate: this.getDateFormat({ value: el.admissionDate }),
        BrainDeathCause: this.getBrainDeathIcdName({
          value: el.brainDeathCause,
        }),
        BrainDeathTime: this.getDateFormat({ value: el.brainDeathTime }),
        TransfusionYn: el.transfusionYn,
        InitialDiag: this.getinitialICDName({ value: el.initialDiag }),
      });
    });
    this._sharedService.exportAsExcelFile(
      this.deceasedDonorExcel,
      "Deceased_Donor_Listing"
    );
  }

  getDateFormat = (data) => {
    if (data != null && data.value) {
      const formattedDate = moment(data.value).format("DD-MM-yyyy");
      return formattedDate;
    } else {
      return "";
    }
  };

  onSearch(event?: any) {
    let body = this.deceasedDonorSearchForm.value;
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE,
    };

    if (body["sex"] == "") {
      body["sex"] = null;
    }

    if (body["bloodGroup"] == "") {
      body["bloodGroup"] = null;
    }

    // crystalNo to uppercase
    if (body["crystalNo"]) {
      body["crystalNo"] = body["crystalNo"].toUpperCase();
    }

    // Name to uppercase
    if (body["fullName"]) {
      body["fullName"] = body["fullName"].toUpperCase();
    }

    body = { pageable, ...body };
    this._deceasedDonorService.getDeceasedDonorListing(body).subscribe(
      (res) => {
        if (res["code"] == "S0000") {
          this.rowData = res["result"];
          // this.totalRecords = res['result']['totalElements'];

          this.excelCriteria = body;
          //console.log(this.rowData);
          this.rowData.forEach((el) => {
            this.dataList.push({
              CrystalNo: el.crystalNo,
              CivilId: el.civilId,
              Name: el.fullName,
              Dob: this.getDateFormat(el.dob),
              Gender: el.sex,
              BloodGroup: this.renderBloodGroup({ value: el.bloodGroup }),
              InstCode: this.getInstituteName(el.instCode),
              MaritalStatus: this.getMaritalStatusName(el.maritalStatus),
              Nationality: this.getNationlityName(el.nationality),
              Occupation: this.getOccupationName(el.occupation),
              Bmi: el.bmi,
              CancerYn: el.cancerYn,
              VentilationDt: this.getDateFormat(el.ventilationDt),
              AdmissionDate: this.getDateFormat(el.admissionDate),
              BrainDeathCause: this.getBrainDeathIcdName(el.brainDeathCause),
              BrainDeathTime: this.getDateFormat(el.brainDeathTime),
              TransfusionYn: el.transfusionYn,
              InitialDiag: this.getinitialICDName(el.initialDiag),
            });
          });
        } else if (res["code"] == "F0000") {
          this.rowData = null;
          Swal.fire("Warning!", res["message"], "warning");
        } else {
          this.rowData = null;
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (error) => {
        if (error.status == 401)
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
      }
    );
  }

  initialGetList: () => void;

  renderBloodGroup(params: any): string {
    const bloodGroupID = Number(params.value);

    let bloodGroupLabel = "";

    switch (bloodGroupID) {
      case 1:
        bloodGroupLabel = "A +";
        break;
      case 2:
        bloodGroupLabel = "A -";
        break;
      case 3:
        bloodGroupLabel = "B +";
        break;
      case 4:
        bloodGroupLabel = "B -";
        break;
      case 5:
        bloodGroupLabel = "O +";
        break;
      case 6:
        bloodGroupLabel = "O -";
        break;
      case 7:
        bloodGroupLabel = "AB +";
        break;
      case 8:
        bloodGroupLabel = "AB -";
        break;
      default:
        bloodGroupLabel = "Unknown Blood Group";
        break;
    }

    return bloodGroupLabel;
  }

  loadBrainDeathIcdList(): void {
    this._deceasedDonorService
      .getBrainDeathIcdList()
      .subscribe((data: any[]) => {
        if (data && data.length > 0) {
          this.brainDeathIcdList = data.map((item) => ({
            id: item.icd,
            value: item.disease,
          }));
        }
      });
  }

  loadInitialIcdList(): void {
    this._deceasedDonorService.getInitialIcdList().subscribe((data: any[]) => {
      if (data && data.length > 0) {
        this.initialICDList = data.map((item) => ({
          id: item.icd,
          value: item.disease,
        }));
      }
    });
  }

  pickLowAndHighValues(event: any) {
    this.deceasedDonorSearchForm.controls["bmiLow"].setValue(event.low);
    this.deceasedDonorSearchForm.controls["bmiHigh"].setValue(event.high);
  }
}
