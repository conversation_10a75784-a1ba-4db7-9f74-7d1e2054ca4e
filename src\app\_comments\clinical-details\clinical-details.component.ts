import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MasterService } from '../../_services/master.service';
import { RegistryService } from '../../_services/registry.service';
import { labTestModel } from '../../common/objectModels/labTestModel';
import { HttpClient } from '@angular/common/http';
import * as AppUtils from '../../common/app.utils';
import Swal from 'sweetalert2';
import * as moment from 'moment';
import { SharedService } from '../../_services/shared.service';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { FormArray } from '@angular/forms';
import * as CommonConstants from '../../_helpers/common.constants';
import { formatDate } from '@angular/common';
import { GeneticService } from '../../genetic-blood-disorder/genetic.service';
import * as AppComponent from '../../common/app.component-utils';
@Component({
  selector: 'app-clinical-details',
  templateUrl: './clinical-details.component.html',
  styleUrls: ['./clinical-details.component.scss'],




})
export class clinicalDetailsComponent implements OnInit {
  @Input() clinicalForm: FormGroup;
  @Output() uploaded = new EventEmitter<string>();
  clinicalList: any[];
  complicationList: any[];
  today = new Date();
  diagnosisMastList: any;
  icdList: any;
  centralRegNo: any;
  diagnosisByCentralList: any;
  selectedDate: any;
  delRow: any;
  data: any = [];
  rgTbDiagnosis: any = [];
  diagFg: any = [];
  loginId: any;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  icdFlagList = AppComponent.ICD_FLAG;

  //entryDate:any;

  constructor(private _masterService: MasterService, private _http: HttpClient, private formBuilder: FormBuilder, private _geneticService: GeneticService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode
    //this.getdiagnosis();

  }
  ngOnInit() {

    this.clinicalForm = this.formBuilder.group({
      // 'icd':[null],
      // 'icdFlag': [""],
      // 'remarks': [""],
      // 'entryDate':  [""],
      rgTbDiagnosis: this.formBuilder.array([]),

    }),


      //this.getdiagnosis();
      this._masterService.getDiagnosisMastList().subscribe(response => {
        this.diagnosisMastList = response.result;
      });


    //get master data for Genetic ICD List
    this._geneticService.getGeneticICDList().subscribe(response => {
      this.icdList = response.result;
    });

  }


  ///////////////////P DATA TABLE


  //this.loginId, this.currentDate
  onAddNewIcd() {
    this.addNewIcd('', '', '',  this.loginId, '', '', 'W', false);  //add new empty row with uneditable 
    this.diagFg[this.diagFg.length - 1].isEditable = true;          //editable last entering row
  }
  /*
  
    private Long runId;
    private String icdFlag;
    private String icd;
    private Long enteredBy;
    private Date entryDate;
    private String remarks;
    private String source;*/
  addNewIcd(runId: any = null, icd: any = null,  icdFlag: any = null,  enteredBy: any = null, entryDate: any = null, remarks: any = null, source: any = null, isEditable: any = false): void {
    this.rgTbDiagnosis = this.clinicalForm.get('rgTbDiagnosis') as FormArray;

    this.diagFg = Object.assign([], this.rgTbDiagnosis.value);
    const diagnosisItem: any = this.createDiagnosistem(runId, icd,  icdFlag,  enteredBy, entryDate, remarks, source, isEditable);
    this.rgTbDiagnosis.push(this.createCompGrpItem(diagnosisItem));

    this.diagFg.push(diagnosisItem);
    //this.diagFg[this.diagFg.length - 1].isEditable = true;
  }

  createCompGrpItem(createDiagnosistem: any): FormGroup {
    return this.formBuilder.group(createDiagnosistem);
  }

  createDiagnosistem(runId: any = null, icd: any = null, icdFlag: any = null, enteredBy: any = null, entryDate: any = null, remarks: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      icd: icd,
      icdFlag: icdFlag,
      enteredBy: enteredBy,
      entryDate: entryDate,
      remarks: remarks,
      source: source,
      isEditable: isEditable
    };
  }

  getICD(icd) {
    if (icd) {
      return this.icdList.filter(s => s.icd == icd).map(s => s.disease)[0];
    }
  }
  getICDFlag(icdFlag) {
    if (icdFlag) {
      return this.icdFlagList.filter(s => s.id == icdFlag).map(s => s.value)[0];
    }

  }



  ///////////////////P DATA TABLE


  /*
  
  civilId: 12345678
            icd: "D57.0"
  icdEnteredBy: -1001
            icdEnteredDt: "2021-05-26T06:12:25.000+0000"
            icdFlag: "O"
            icdRemarks: "Sickle-cell anaemia with crisis                            "
  onsetDate: "2021-05-30T08:36:42.000+0000"
  patientId: 77232
  */




  addNew() {
    this.clinicalList.push({ icd: '', icdFlag: '', icdRemarks: '', icdEnteredDt: '' });
    this.clinicalList[this.clinicalList.length - 1].isEditable = true;
  }



  getdiagnosis() {
    this._masterService.getDiagnosisRegNo(this.centralRegNo).subscribe(response => {
      this.clinicalList = response.result;
    });
  }



  onRowEditInit(row: any) {
    //this.clinicalList.filter(row => row.isEditable).map(r => { r.isEditable = false; return r })
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }

  onRowEditSave(row: any) {
    let rowIndex = this.diagFg.indexOf(row);
    this.diagFg[rowIndex] = this.clinicalForm.value.rgTbDiagnosis[rowIndex];
    let data = this.diagFg[rowIndex];
    data.icdFlagName = this.icdFlagList.filter(s => s.id == data.icdFlag).map(s => s.value)[0];
    data.icdName = this.icdList.filter(s => s.icd == data.icd).map(s => s.disease)[0];
    data.entryDate = moment(data.entryDate, "DD-MM-YYYY").format();
    data.isEditable = false;

  }

  delete(row: any) {

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbDiagnosis = this.clinicalForm.get('rgTbDiagnosis') as FormArray;
        this.delRow = this.diagFg.indexOf(row);
        this.diagFg.splice(this.delRow, 1);
        this.rgTbDiagnosis.removeAt(this.delRow);

      }
    })



  }

  /*
  civilId: 18193813
icd: "D57.0"
icdEnteredBy: 0
icdEnteredDt: "2014-03-17T06:15:29.000+0000"
icdFlag: "P"
icdRemarks: "Sickle-cell anaemia with crisis                            "
onsetDate: "2021-05-27T04:09:25.000+0000"
patientId: 288755
  */
  getDataFromAlshifa(sendingData: any = 0) {
    //this.clinicalList = sendingData;  //this is not used in UI p-dataTable
    //this.diagFg = sendingData;   
    //runId:any = null, icd: any = null,icdName:any =null, icdFlag: any = null, icdFlagName:any =null, enteredBy: any = null,  entryDate: any = null, remarks: any = null,source:any= null, isEditable: any = false)
    sendingData.forEach(element => {
      // let icdFlagName = this.icdFlagList.filter(s => s.id == element.icdFlag).map(s => s.value)[0];
      // let icdName = this.icdList.filter(s => s.icd == element.icd).map(s => s.disease)[0];
      this.addNewIcd(null, element.icd,  element.icdFlag,  element.icdEnteredBy, element.icdEnteredDt, element.icdRemarks, 'S', false);
    });

  }

  clear() {

    this.rgTbDiagnosis = null;
    this.clinicalList = [];
    this.diagFg = [];
    this.clinicalForm.reset();

    this.clinicalForm = this.formBuilder.group({
      rgTbDiagnosis: this.formBuilder.array([]),
    })

  }
}