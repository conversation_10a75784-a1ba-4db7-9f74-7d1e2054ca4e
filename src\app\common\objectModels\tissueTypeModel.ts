export class TissueTypeModel {
	runId: number;
	centralRegNo: number;
	donorId: number;
	testType: string;
	activeYn: string;
	createedOn: any;

	// a_Test: number;
	// a_1_Test: number;
	// b_Test: number;
	// b_1_Test: number;
	// cw_Test: number;
	// cw_1_Test: number;
	// dr_Test: number;
	// dr_1_Test: number;
	// drw_Test: number;
	// drw_1_Test: number;
	// dq_Test: number;
	// dq_1_Test: number;
	// bw_Test: number;
	// bw_1_Test: number;

	a_Test: string;
	a_1_Test: string;
	a_2_Test: string;
	a_3_Test: string;
	b_Test: string;
	b_1_Test: string;
	b_2_Test: string;
	b_3_Test: string;
	cw_Test: string;
	cw_1_Test: string;
	cw_2_Test: string;
	cw_3_Test: string;
	dr_Test: string;
	dr_1_Test: string;
	dr_2_Test: string;
	dr_3_Test: string;
	drw_Test: string;
	drw_1_Test: string;
	drw_2_Test: string;
	drw_3_Test: string;
	dq_Test: string;
	dq_1_Test: string;
	dq_2_Test: string;
	dq_3_Test: string;
	bw_Test: string;
	bw_1_Test: string;
	bw_2_Test: string;
	bw_3_Test: string;
}