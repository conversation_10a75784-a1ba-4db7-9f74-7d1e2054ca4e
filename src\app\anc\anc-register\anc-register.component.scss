.table {
   
    min-width: 1200px;
}
.pr-0{
    padding-right: 0;
}

:host ::ng-deep {
    .enc-register {
        .accordion {
                
                .card{
                    &:nth-child(4), &:nth-child(5){
                        display: inline-block;
                        width: 49%;
                        margin: 0 0.5%;
                        vertical-align: top;
                        margin-bottom: 10px;         
                    }
                    .card-body{
                        min-height: 170px;
                    }
                }
        }
    }
    
}
.btn-ftr-container{
    text-align: right;
    padding: 10px;
}
.inline-list {
    padding: 0 10px;
    display: inline-block;

    li {
        display: inline-block;
        padding: 0 5px;

        label {
          margin: 0;
        }
        input{
          margin-right: 5px;
        }
        &:last-child{
            border: 0;
        }
      }
}
.divider-right{
    border-right: 1px solid #e5e5e5;
}
.mcard{
    border: 1px solid #efefef;
    border-radius: 3px;
    margin-bottom: 10px;

    .mcard-header{
        font-size: 14px;
        padding: 5px 10px;
        background: #f7f7f7;
        font-weight: bold;
        color: #8f8f8f;
    }
    .mcard-body{
        padding: 10px;

        table{
            color: #666;
            margin-bottom: 0;
                
        }

    }
}
.border-box{
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 3px;
    padding: 10px;
}
.followup-nav {
    .list-group-item{
        padding: 5px;
        cursor: pointer;
        &.active{
            background: #fffbfb;
            border-color: #d9534f;
            font-weight: bold;
            color: #d9534f;
        }
        &:hover{
            background: #f7f7f7;
        }
    }
}
.radio-inline{
    display: inline-block;
    padding: 0 10px 0 0;
    input,label{
        vertical-align: middle;
        margin: 0;
    }
}
.c-label{
    padding-right: 10px;
    display: inline-block;
    text-align: right;
}
.label-space{
    margin-top: 30px;
}
.sub-title{
    margin-bottom: 15px;
    background: #f3f3f3;
    padding: 3px 6px;
    font-size: 15px;
    border-radius: 3px;
}
.checkbox input{
    margin-right: 5px;
}

.top-search{
    padding-right: 6px;
    margin-bottom: 8px;
    .col-form-label{
        white-space: nowrap;
        padding-right: 0;
        line-height: 10px;
    }
    .form-control{
        height: 25px;
    }
}

.add-btn{
    width: 100%;
    margin-bottom: 10px;
    border: 1px dashed #d9534f;
    color: #d9534f;
    transition: .3s linear;

    &:hover{
        background: #d9534f;
        color: #fff;
        border-color: transparent;

    }
}
.add-btn-link {
    float: right;
    padding: 0;
    color: #d9534f;
    line-height: 1;
    font-weight: bold;
}
.custom-btn{
    padding:4px 10px !important
}

.hide-txt{
    .ui-inputtext{
        display: none;
    }
}
.added-list{
    position: relative;
    button{
        position: absolute;
        right: 5px;
        border: 0;
        background: none;
        color: #d9534f;
    }
}

.action-btn{
    border: 0;
    background: #d9534f;
    color: white;
    font-size: 14px;
    margin-top: 5px;
    padding: 7px;
    margin-right: 8px;
    border-radius: 3px;
}
.form1{
display: block;
    /* width: 100%; */
    
    height: 31px;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

textarea.form1 {
    resize: none;
    overflow-y: auto;
    max-height: 100px;
}
.mcard-body {
    overflow-x: auto !important;
    max-width: 100%;
}


:host ::ng-deep .section .ng-select {
    width: 200px; /* Select box width */
  }
  
  :host ::ng-deep .ng-dropdown-panel {
    min-width: 500px !important; /* Dropdown options panel width */
  }