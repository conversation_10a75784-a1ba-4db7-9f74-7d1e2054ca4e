export class ElderlyVisitInfo {
    patientId: number;
    bpF6Months: string;
    pulseF6Months: number;
    tempF6Months: number;
    wtF6Months: number;
    htF6Months: number;
    bpS6Months: string;
    pulseS6Months: number;
    tempS6Months: number;
    wtS6Months: number;
    htS6Months: number;
    seeaFirstVisit: number;
    seeaAfter3YrVisit: string;
    brFirstVisit: string;
    brAfter1YrVisit: string;
    brAfter2YrVisit: string;
    brAfter3YrVisit: string;
    rbsFirstVisit: string;
    rbsSecondVisit: string;
    rbsThirdVisit: string;
    rbsFourthVisit: string;
    rbsFifthVisit: string;
    rbsSixthVisit: string;
    mmmseFirstYear: string;
    mmmseSecondYear: string;
    mmmseThirdYear: string;
    gdFirstYear: string;
    gdSecondYear: string;
    gdThirdYear : string;
    incScrFirstVisit: string;
    adlFirstVisit: string;
    adlAfter1YrVisit: string;
    tugFirstVisit: string;
    tugAfter1YrVisit: string;
    mnaFirstVisit: string;
    mnaAfter1YrV: string;
}