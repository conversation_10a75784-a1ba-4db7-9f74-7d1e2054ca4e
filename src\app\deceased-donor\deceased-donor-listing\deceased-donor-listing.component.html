<h6 class="page-title">Deceased Donor Listing</h6>

<div class="content-wrapper mb-2">
  <form [formGroup]="deceasedDonorSearchForm">
    <div class="row">
      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Civil Id</label>
          <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
            formControlName="civilId" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Crystal No</label>
          <input type="text" class="form-control form-control-sm" formControlName="crystalNo" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Name</label>
          <input type="text" class="form-control form-control-sm" formControlName="fullName" />
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Gender</label>
          <ng-select [items]="gender" [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
            formControlName="sex">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.value
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-2 col-md-3 col-sm-3">
        <div class="form-group">
          <label>Hospital</label>
          <ng-select #entryPoint [items]="hospitalsList" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
            bindValue="estCode" formControlName="instCode">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.estName
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-lg-3 col-xl-2">
        <label>Age</label>
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group">
              <input type="number" placeholder="From" class="form-control form-control-sm" formControlName="ageFrom" />
            </div>
          </div>
          <div class="col-sm-6 pl-0">
            <div class="form-group">
              <input type="number" placeholder="To" class="form-control form-control-sm" formControlName="ageTo" />
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-xl-2">
        <label>Ventilation Date</label>
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="ventilationDt_from" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="From" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
          <div class="col-sm-6 pl-0">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="ventilationDt_to" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-xl-2">
        <label>Admission Date</label>
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="admissionDate_from" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="From" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
          <div class="col-sm-6 pl-0">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="admissionDate_to" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-xl-2">
        <label>Brain Death Date</label>
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="brainDeathTime_from" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="From" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
          <div class="col-sm-6 pl-0">
            <div class="form-group">
              <p-calendar dateFormat="dd-mm-yy" formControlName="brainDeathTime_to" monthNavigator="true"
                dateFormat="{{ dateFormat }}" yearNavigator="true" placeholder="To" yearRange="1930:2030">
              </p-calendar>
            </div>
          </div>
        </div>
      </div>

      <!-- Blood Group -->
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Blood Group</label>
          <ng-select #entryPoint [items]="bloodGroupList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="bloodGroup">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{
              item.value
              }}</ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Nationality</label>
          <select class="form-control form-control-sm" formControlName="nationality">
            <option selected [value]="null">Select</option>
            <option [value]="res.natCode" *ngFor="let res of nationalityList">
              {{ res.nationality }}
            </option>
          </select>
        </div>
      </div>

      <div class="col-lg-3 col-xl-2">
        <div class="form-group">
          <label>BMI</label>

          <ng-select #entryPoint [items]="bmiList" [virtualScroll]="true" placeholder="Select"
            (change)="pickLowAndHighValues($event)" bindLabel="value" bindValue="id" formControlName="bmi">
            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Initial Diagnosis</label>
          <ng-select [items]="initialICDList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="initialDiag">
            <ng-template ng-option-tmp let-item="item" let-index="index">
              {{ item.value }}
            </ng-template>
          </ng-select>
        </div>
      </div>

      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Cause of Brain Death</label>
          <ng-select [items]="brainDeathIcdList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
            bindValue="id" formControlName="brainDeathCause">
            <ng-template ng-option-tmp let-item="item" let-index="index">
              {{ item.value }}
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Cancer?</label>
          <div>
            <div class="form-check form-check-inline">
              <input type="radio" id="cancerYes" class="form-check-input" value="Y" formControlName="cancerYn" />
              <label for="cancerYes" class="form-check-label">Yes</label>
            </div>
            <div class="form-check form-check-inline">
              <input type="radio" id="cancerNo" class="form-check-input" value="N" formControlName="cancerYn" />
              <label for="cancerNo" class="form-check-label">No</label>
            </div>
            <div class="form-check form-check-inline">
              <input type="radio" id="cancerUnknown" class="form-check-input" value="U" formControlName="cancerYn" />
              <label for="cancerUnknown" class="form-check-label">Unknown</label>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-4 col-sm-3 col-lg-2">
        <div class="form-group">
          <label>Transfusions?</label>
          <div>
            <div class="form-check form-check-inline">
              <input type="radio" id="transfusionYes" class="form-check-input" value="Y"
                formControlName="transfusionYn" />
              <label for="transfusionYes" class="form-check-label">Yes</label>
            </div>
            <div class="form-check form-check-inline">
              <input type="radio" id="transfusionNo" class="form-check-input" value="N"
                formControlName="transfusionYn" />
              <label for="transfusionNo" class="form-check-label">No</label>
            </div>
          </div>
        </div>
      </div>

      <div class="text-right col-lg-12 col-md-12 col-sm-12">
        <div class="btn-box">
          <button type="submit" (click)="exportExcel()" class="btn btn-primary ripple">
            EXCEL
          </button>
          <button type="reset" (click)="clear($event)" class="btn btn-sm btn-secondary">
            Clear
          </button>
          <button type="submit" (click)="onSearch()" class="btn btn-sm btn-primary">
            Search
          </button>
        </div>
      </div>
    </div>
  </form>
</div>

<div style="margin-top: 20px">
  <ag-grid-angular style="width: 100%; height: 300px" class="ag-theme-balham" [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>

  <div *ngIf="rowData && rowData.length > 0">
    <p-paginator #paginator rows="{{ paginationSize }}" totalRecords="{{ totalRecords }}"
      (onPageChange)="onSearch($event)" showCurrentPageReport="true"
      currentPageReportTemplate="(Total: {{ totalRecords }} records)" pageLinkSize="10"
      [rowsPerPageOptions]="[10, 20, 30]">
    </p-paginator>
  </div>
</div>