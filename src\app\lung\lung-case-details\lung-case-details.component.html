<div #printSection id="case-details-form">
  <div class="row">
    <div class="input-group col-sm-10">
      <h4 class="page-title pt-2">Lung - Case Details</h4>
    </div>
    <div class="input-group col-sm-2 mb-2 text-right">
      <div class="input-group">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId"
          class="form-control form-control-sm search-input" (keyup.enter)="search()" />
        <div class="input-group-append">
          <button class="btn btn-default btn-sm search-icon" id="search" (click)="search()">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">

      <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Patient Details</h6>
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>

  <div>
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
      <ngb-panel id="RegistrationINFO" id="ngb-panel-1">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6> Registration Information</h6>
            <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <form [formGroup]="caseDetailsForm" (ngSubmit)="submit()">
            <div class="case-details-panel">
              <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-3" formGroupName="lungRegister">
                  <div class="form-group">
                    <label>Priority</label>
                    <ng-select #entryPoint appendTo="body" [items]="priorityOrder" [virtualScroll]="true"
                      placeholder="Select" bindLabel="value" bindValue="id" [dropdownPosition]="'auto'"
                      formControlName="priorityOrder">
                      <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value
                        }}</ng-template>
                    </ng-select>
                  </div>
                </div>
              </div>
              <row class="row">

                <div class="col-lg-12 col-md-12 col-sm-12">
                  <div class="card mt-4 theme-card">
                    <div class="mcard-header">Smoking Related Queries</div>
                    <div class="card-body">
                      <div class="row" formGroupName="lungRegister">

                        <div class="col-lg-3 col-md-4 col-sm-3">
                          <div class="form-group">
                            <label>Cigerette Smoking?</label>
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="smokingYn"
                                    formControlName="smokingYn" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="smokingYn"
                                    formControlName="smokingYn" value="N">No
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="smokingYn"
                                    formControlName="smokingYn" value="U">Smoked in Past
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-3">
                          <div class="form-group">
                            <label>Pack per year</label>
                            <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                              formControlName="cigarettePerYr">
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-4">
                          <div class="form-group">
                            <label>Quit Date</label>
                            <!-- <p-calendar [(ngModel)]="value" formControlName="transplantDatr"></p-calendar> -->

                            <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon="true"
                              formControlName="cigaretteQuitDate" [ngModelOptions]="{standalone: true}"
                              monthNavigator="true" [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                              showButtonBar="true"></p-calendar>
                          </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-3">
                          <div class="form-group">
                            <label>Type</label>
                            <input type="text" class="form-control form-control-sm" formControlName="cigaretteType">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <br>
                </div>
              </row>
            </div>

            <div class="case-details-panel">

              <row class="row">
                <div class="col-md-12">
                  <div class="content-wrapper mb-2 h-100">



                    <div class="row">
                      <div class="col-lg-6 col-md-2 col-sm-6" formGroupName="lungRegister">
                        <div class="form-group">
                          <div class="card-header"><strong>Recommended for Transplant</strong></div>
                          <div class="card-body">
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="recTransYn"
                                    formControlName="recTransYn" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="recTransYn"
                                    formControlName="recTransYn" value="N">No
                                </label>
                              </div>
                              <div class="form-group mt-2">
                                <label for="recTransReason" class="form-check-label">Reason
                                </label>
                                <textarea class="form-control form-control-sm"
                                  formControlName="recTransReason"></textarea>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-6 col-md-2 col-sm-6" formGroupName="lungRegister">
                        <div class="form-group">
                          <div class="card-header"><strong>Urgent Transplant Needed ?</strong></div>
                          <div class="card-body">
                            <div class="mt-1">
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="urgentTransYn"
                                    formControlName="urgentTransYn" value="Y">Yes
                                </label>
                              </div>
                              <div class="form-check-inline">
                                <label class="form-check-label">
                                  <input type="radio" class="form-check-input" name="urgentTransYn"
                                    formControlName="urgentTransYn" value="N">No
                                </label>
                              </div>

                              <div class="form-group mt-2">
                                <label for="urgentTransReason" class="form-check-label">Reason
                                </label>
                                <textarea class="form-control form-control-sm"
                                  formControlName="urgentTransReason"></textarea>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </row>
            </div>
          </form>


          <div class="case-details-panel">
            <div class="row">
              <!-- Contact Details Column -->
              <div class="col-md-6">
                <div class="content-wrapper mb-2 h-100"> <!-- Added h-100 class -->
                  <div [formGroup]="contactDetailsForm" class="d-flex flex-column h-100"> <!-- Added flex structure -->
                    <!-- <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">
                          Contact Details
                          <button class="btn btn-sm float-right"></button>
                        </h6> -->
                    <div class="mcard-header">Contact Details
                      <!-- Added flex-grow-1 -->

                      <button *ngIf="showDownloadButton" (click)="onAddNewContact()"
                        class="btn btn-sm btn-primary float-right">
                        Add New
                      </button>

                    </div>
                    <div class="content-wrapper mb-2 lab-results flex-grow-1">
                      <p-dataTable [immutable]="false" [value]="contacts" [editable]="true" dataKey="runId"
                        [responsive]="true">
                        <p-column field="name" header="Name">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" class="form-control form-control-sm" formControlName="name" />
                                  <div
                                    *ngIf="getNameControl(rowIndex).touched && getNameControl(rowIndex).errors?.required"
                                    class="text-danger">
                                    <small>Name is required.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="relation" header="Relations">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">
                                  {{ getRelationName(row.relation) }}
                                </div>
                                <div *ngIf="row.isEditable">
                                  <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                    placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                    formControlName="relation" [dropdownPosition]="'auto'">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">
                                      {{ item.relationName }}
                                    </ng-template>
                                  </ng-select>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="phone" header="Phone">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">{{ row.phone }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" maxlength="16" (keypress)="numberOnly($event)"
                                    class="form-control form-control-sm" formControlName="phone" />
                                  <div
                                    *ngIf="getPhoneControl(rowIndex).touched && getPhoneControl(rowIndex).errors?.invalidPhone"
                                    class="text-danger">
                                    <small>Phone number is invalid.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="email" header="Email">
                          <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbContactDetails">
                              <div [formGroupName]="rowIndex">
                                <div *ngIf="!row.isEditable">{{ row.email }}</div>
                                <div *ngIf="row.isEditable">
                                  <input type="text" class="form-control form-control-sm" formControlName="email" />
                                  <div
                                    *ngIf="getEmailControl(rowIndex).touched && getEmailControl(rowIndex).errors?.invalidEmail"
                                    class="text-danger">
                                    <small>Email is invalid.</small>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </ng-template>
                        </p-column>
                        <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                          styleClass="foo">
                          <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="onRowEditInitContact(row)" *ngIf="row.isEditable == false"
                              class="btn btn-sm btn-primary">
                              <i class="fa fa-edit"></i>
                            </button>
                            <button (click)="onRowEditSaveContact(row)" *ngIf="row.isEditable == true"
                              class="btn btn-sm btn-primary" [disabled]="!isContactValid(row)">
                              <i class="fa fa-save"></i>
                            </button>

                          </ng-template>
                        </p-column>
                        <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                          styleClass="foo">
                          <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="deleteContact(row)" class="btn btn-sm btn-primary">
                              <i class="fa fa-trash"></i>
                            </button>
                          </ng-template>
                        </p-column>
                      </p-dataTable>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Family History Column -->

              <div class="col-md-6">
                <div class="content-wrapper mb-2 h-100">
                  <div class="d-flex flex-column h-100">

                    <div class="mcard-header">Family History of Liver Diseases
                      &nbsp;&nbsp;
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" [value]="'Y'" [(ngModel)]="lungRegister.familyHistYn"
                            class="form-check-input" name="familyHistYn" />
                          Yes
                        </label>
                      </div>
                      <div class="form-check-inline">
                        <label class="form-check-label">
                          <input type="radio" [value]="'N'" [(ngModel)]="lungRegister.familyHistYn"
                            class="form-check-input" name="familyHistYn" />
                          No
                        </label>
                      </div>
                      <button *ngIf="lungRegister.familyHistYn === 'Y'" (click)="onAddNewFH()"
                        class="btn btn-sm btn-primary float-right">
                        Add New
                      </button>
                    </div>
                    <div>



                      <div *ngIf="lungRegister.familyHistYn === 'Y'" [formGroup]="familyHistoryForm"
                        class="content-wrapper mb-2 lab-results">

                        <p-dataTable *ngIf="lungRegister.familyHistYn === 'Y'" [immutable]="false" [value]="famHistory"
                          [editable]="true" dataKey="runId" [responsive]="true">
                          <!-- Existing p-column definitions remain the same -->
                          <p-column field="name" header="Name">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                                  <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                                  <div *ngIf="!row.isEditable">{{ row.name }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" class="form-control form-control-sm" formControlName="name" />
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="relation" header="Relations">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">
                                    {{ getRelationName(row.relation) }}
                                  </div>
                                  <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                      placeholder="Select Relation" bindLabel="relationName" bindValue="relationCode"
                                      formControlName="relation">
                                      <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{ item.relationName }}
                                      </ng-template>
                                    </ng-select>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="patientID" header="PatientID">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">{{ row.patientID }}</div>
                                  <div *ngIf="row.isEditable">
                                    <input type="text" (keypress)="numberOnly($event)"
                                      class="form-control form-control-sm" formControlName="patientID" />
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="instID" header="Institute">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                              <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                  <div *ngIf="!row.isEditable">
                                    {{ getInstName(row.instID) }}
                                  </div>
                                  <div *ngIf="row.isEditable">
                                    <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                      placeholder="Select" bindLabel="estName" bindValue="estCode"
                                      formControlName="instID">
                                      <ng-template ng-option-tmp let-item="item" let-index="index">
                                        {{ item.estName }}
                                      </ng-template>
                                    </ng-select>
                                  </div>
                                </div>
                              </ng-container>
                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="onRowEditInitFH(row)"
                                *ngIf="row.source == 'W' && row.isEditable == false" class="btn btn-sm btn-primary">
                                <i class="fa fa-edit"></i>
                              </button>
                              <button (click)="onRowEditSaveFH(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                                class="btn btn-sm btn-primary">
                                <i class="fa fa-save"></i>
                              </button>
                            </ng-template>
                          </p-column>
                          <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }"
                            styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                              <button (click)="delete(row)" class="btn btn-sm btn-primary">
                                <i class="fa fa-trash"></i>
                              </button>
                            </ng-template>
                          </p-column>
                        </p-dataTable>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="case-details-panel">
            <div class="row">
              <div class="col-sm-4">
                <div class="card h-100">
                  <div class="mcard-header">Indication of Lung Transplant</div>
                  <div class="card-body p-2">
                    <ul class="list-item list-unstyled mb-0">
                      <li *ngFor="let option of lungTransIndications; let i = index" class="checklist-item">
                        <div class="d-flex align-items-center">
                          <!-- Checkbox with Label -->
                          <div class="checkbox-wrapper me-2">
                            <div class="form-check d-flex align-items-center">
                              <input type="checkbox" [(ngModel)]="option.checked" [id]="'checkbox' + option.paramId"
                                (change)="onIndicationChange(option,option.selectedDiseases)"
                                class="form-check-input" />
                              <label [for]="'checkbox' + option.paramId" class="form-check-label ms-2">
                                {{ option.paramName }}
                              </label>
                            </div>
                          </div>

                          <!-- Dropdown -->
                          <div class="select-wrapper flex-grow-1">
                            <ng-select [items]="option.subOptions" [multiple]="true" [closeOnSelect]="false"
                              [searchable]="true" bindLabel="name" bindValue="id" placeholder="Select diseases"
                              [(ngModel)]="option.selectedDiseases" (change)="onDiseaseSelect(option)"
                              [disabled]="!option.checked" class="compact-select">
                              <ng-template ng-option-tmp let-item="item">
                                <input type="checkbox" [checked]="item.selected" /> {{item.name}}
                              </ng-template>
                            </ng-select>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <!-- Disease Severity Section -->
              <div class="col-sm-4">
                <div class="card h-100">
                  <div class="mcard-header">Disease Severity</div>
                  <div class="card-body p-2">
                    <ul class="list-item list-unstyled">
                      <li *ngFor="let severity of lungTransDiseaseSeverity" class="mb-2">
                        <div class="d-flex align-items-center gap-2">
                          <!-- Checkbox -->
                          <div class="checkbox-container me-3">
                            <input type="checkbox" [(ngModel)]="severity.checked" [id]="'severity_' + severity.paramId"
                              (change)="onSeverityChange(severity)" />

                            &nbsp;&nbsp;
                            <label [for]="'severity_' + severity.paramId" class="ms-2">
                              {{ severity.paramName }}
                            </label>


                            <!-- <label [for]="'severity_' + severity.paramId" class="ms-2">
                {{ severity.paramName }}
              </label> -->
                          </div>

                          <!-- Score Input -->
                          <div class="mx-2">
                            <input type="text" class="form-control form-control-sm" style="width: 80px;"
                              [(ngModel)]="severity.score" placeholder="Score" (keypress)="numberOnly($event)"
                              [disabled]="!severity.checked" />
                          </div>

                          <!-- Comments -->
                          <div class="flex-grow-1">
                            <textarea class="form-control form-control-sm" [(ngModel)]="severity.comments"
                              placeholder="Comments" rows="1" [disabled]="!severity.checked"></textarea>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <!-- Current Management Section -->
              <div class="col-sm-4">
                <div class="card h-100">
                  <div class="mcard-header">Current Management</div>
                  <div class="card-body p-2">
                    <!-- Pharmacological Textarea -->
                    <div class="form-group mb-3">
                      <label>Pharmacological</label>
                      <textarea class="form-control form-control-sm" [(ngModel)]="lungRegister.curMgmtPharma"
                        placeholder="Enter pharmacological notes" rows="2"></textarea>
                    </div>

                    <!-- Management List -->
                    <ul class="list-item list-unstyled">
                      <li *ngFor="let management of lungCurrentMgmt" class="mb-2">
                        <div class="d-flex align-items-center">
                          <!-- Checkbox -->
                          <div class="me-2">
                            <input type="checkbox" [(ngModel)]="management.checked"
                              [id]="'management_' + management.paramId" (change)="onManagementChange(management)" />
                            &nbsp;&nbsp;
                            <label [for]="'management_' + management.paramId" class="ms-2">
                              {{ management.paramName }}
                            </label>
                          </div>

                          <!-- Comments -->
                          <div class="flex-grow-1">
                            <input type="text" class="form-control form-control-sm" [(ngModel)]="management.comments"
                              placeholder="Comments" [disabled]="!management.checked" />
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>



            </div>
          </div>





        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>
  
  <div class="case-details-panel">
    <div class="content-wrapper mb-2 ">
      <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">Medication </h6>
      <app-medication [submitted]="submitted" [showAddNewButton]="showButton"
        [showMedicineButton]="showDownloadButton && (!Medication.medFg || Medication.medFg.length === 0)" #Medication
        (callGenMedList)="callGenMedList()"
        (downloadMedicationDtsAlshifa)="fetchMedicineDtlsFromAlShifa()"></app-medication>
    </div>
  </div>

  <div class="case-details-panel">

    <div class="content-wrapper mb-2">
      <div [formGroup]="pleurodesisMgmtForm">
        <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">
          Pleurodesis History
          <button class="btn btn-sm float-right"></button>
        </h6>
        <div class="content-wrapper mb-2 lab-results">
          <div class="text-right pb-2">
            <button *ngIf="showDownloadButton" (click)="onAddNewPleurodesis()" class="btn btn-sm btn-primary">
              Add New
            </button>
          </div>

          <p-dataTable [immutable]="false" [value]="pleurodesisList" [editable]="true" dataKey="runId"
            [responsive]="true">
            <p-column field="doneDate" header="Done Date">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbPleurodesis">
                  <div [formGroupName]="rowIndex">
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                    <div *ngIf="!row.isEditable">{{ row.doneDate | date:'dd-MM-yyyy' }}</div>
                    <div *ngIf="row.isEditable">
                      <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="doneDate"
                        [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="laterality" header="Laterality">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbPleurodesis">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">{{ row.laterality }}</div>
                    <div *ngIf="row.isEditable">
                      <input type="text" maxlength="1" class="form-control form-control-sm"
                        formControlName="laterality" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="methodUsed" header="Method Used">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbPleurodesis">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ getMethodName(row.methodUsed) }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select [items]="lungPleurodesisMethod" bindLabel="paramName" bindValue="paramId"
                        placeholder="Select Method" formControlName="methodUsed" [clearable]="false">
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="indication" header="Indication">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbPleurodesis">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ getIndicationName(row.indication) }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select [items]="lungPleurodesisIndications" bindLabel="paramName" bindValue="paramId"
                        placeholder="Select Indication" formControlName="indication" [clearable]="false">
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="remarks" header="Remarks">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbPleurodesis">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">{{ row.remarks }}</div>
                    <div *ngIf="row.isEditable">
                      <input type="text" class="form-control form-control-sm" formControlName="remarks" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{'text-align':'center', width:'54px'}" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="onRowEditInitPleurodesis(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-edit"></i>
                </button>
                <button (click)="onRowEditSavePleurodesis(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-save"></i>
                </button>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{'text-align':'center', width:'54px'}" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="deletePleurodesis(row)" class="btn btn-sm btn-primary">
                  <i class="fa fa-trash"></i>
                </button>
              </ng-template>
            </p-column>
          </p-dataTable>
        </div>
      </div>
    </div>


  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2 ">
      <h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">Surgeries </h6>
      <app-surgery [submitted]="submitted" [showAddNewButton]="showButton"
        [showSurgeryButton]="showDownloadButton && (!surgery.surgList || surgery.surgList.length === 0)"
        (downloadSurgery)="fetchSurgeryFromAlShifa()" #surgery></app-surgery>
    </div>
  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <div [formGroup]="labTestForm">
        <!-- <div class="card-header">
      Lab Results
    </div> -->
        <h6 style="font-weight: bold; color: #973633;
    font-family: open_sanssemibold;
    background: #fff;">
          Lab Results
          <button class="btn btn-sm float-right"></button>

        </h6>
        <div class="content-wrapper mb-2 lab-results">
          <div class="text-right pb-2">
            <button *ngIf="showDownloadButton" (click)="addNewlab()" class="btn btn-sm btn-primary">
              Add New
            </button>
            <button *ngIf="showDownloadButton && (!labnewList || labnewList.length === 0)"
              class="btn btn-sm btn-primary" (click)="openModal(downloadLabTest)">Download</button>
          </div>

          <p-dataTable [immutable]="false" [value]="labnewList" [editable]="true" dataKey="runId" [responsive]="true">
            <p-column field="testDate" header="Date">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="enteredDate" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="source" />
                    <input type="hidden" class="form-control form-control-sm" formControlName="releasedDate" />

                    <div *ngIf="!row.isEditable">
                      {{ row.testDate | date : "dd-MM-yyyy" }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <p-calendar dateFormat="dd-mm-yy" showIcon="true" formControlName="testDate"
                        [ngModelOptions]="{ standalone: true }" monthNavigator="true" [maxDate]="today"
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                      </p-calendar>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="profileTestCode" header="Profile Name">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.profileTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="profileList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="testName" bindValue="testId" formControlName="profileTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.testName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="mohTestCode" header="Test Component">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.componentTestName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="ComponetList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="componentTestName" bindValue="componentTestId" formControlName="mohTestCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.componentTestName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="value" header="Result">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.value }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" (keypress)="numberOnly($event)" class="form-control form-control-sm"
                        formControlName="value" (input)="validateNumberInput($event)" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="unit" header="Unit">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.unit }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <input type="text" class="form-control form-control-sm" formControlName="unit" />
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="instCode" header="Institute">
              <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                <ng-container formArrayName="rgTbLabTestInfo">
                  <div [formGroupName]="rowIndex">
                    <div *ngIf="!row.isEditable">
                      {{ row.instName }}
                    </div>
                    <div *ngIf="row.isEditable">
                      <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="instCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                          {{ item.estName }}
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                </ng-container>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-edit"></i>
                </button>
                <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                  class="btn btn-sm btn-primary">
                  <i class="fa fa-save"></i>
                </button>
              </ng-template>
            </p-column>

            <p-column field="" header="" [style]="{ 'text-align': 'center', width: '54px' }" styleClass="foo">
              <ng-template let-row="rowData" pTemplate="body">
                <button (click)="deletelab(row)" class="btn btn-sm btn-primary">
                  <i class="fa fa-trash"></i>
                </button>
              </ng-template>
            </p-column>
          </p-dataTable>
          <div *ngIf="labnewList && labnewList.length > 0">
            <p-paginator #labTestPaginator [rows]="paginationSize" [totalRecords]="totalLabRecords"
              (onPageChange)="onLabPageChange($event)" showCurrentPageReport="true"
              currentPageReportTemplate="(Total: {{totalLabRecords}} records)" pageLinkSize="10">
            </p-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="case-details-panel">
    <div class="content-wrapper mb-2">
      <app-vaccination [showAddNewButton]="showButton"
        [showVaccineButton]="showButton && (!Vaccination.vaccineFg || Vaccination.vaccineFg.length === 0)"
        [calledFromParent]="true" #Vaccination (downloadVaccination)="fetchVaccineFromAlShifa()"></app-vaccination>
      <!-- <app-vaccination [showAddNewButton]="showVaccineButton" [showVaccineButton]="showDownloadButton && (!Vaccination.vaccineFg || Vaccination.vaccineFg.length === 0)" [submitted]="submitted" [currentCivilId]="currentCivilId" [patntId]="patntId" #Vaccination>
  </app-vaccination> -->
    </div>
  </div>

  <div class="col-sm-12">
    <div class="btn-container">
      <button *ngIf="showButton" type="submit" class="btn btn-primary" (click)="saveDetails()">
        Save
      </button>
      <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
      <button class="btn btn-primary ripple" (click)="navigateToRegister()">
        Back to register page
      </button>
      <button *ngIf="isPrint" (click)="generateJsPDF()" class="btn btn-sm btn-primary">Download PDF</button>
      <!-- <button  *ngIf="isPrint" class="btn btn-sm btn-secondary" (click)="print()">Print</button> -->
    </div>
  </div>


  <ng-template #downloadLabTest let-modal>

    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">Download Lab Test</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body" style="height: 250px;">

      <form [formGroup]="filterModelForm">
        <div class="row">
          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>From Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="fromDate">
            </p-calendar>

          </div>


          <div class="col-lg-3 col-md-3 col-sm-3">
            <label>To Date</label>
            <p-calendar type="number" dateFormat="dd-mm-yy" monthNavigator="true" [appendTo]="'body'"
              [baseZIndex]="100000" [maxDate]=today yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
              formControlName="toDate">
            </p-calendar>
          </div>

        </div>
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6">
            <br>
            <label>Test Name</label>
            <div>
              <ng-multiselect-dropdown
                class=" multiappend custom-dropdown custom_color custom_box_color check_box_custom-color"
                [placeholder]="'Add Test'" [data]="testListToDownload" [settings]="dropdownSettings"
                formControlName="profileT">
              </ng-multiselect-dropdown>
            </div>

          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-sm btn-primary" (click)="callFetchLabDataFromAlShifa()"
        (click)="modal.dismiss('Cross click')">Download</button>
      <button type="button" class="btn btn-sm btn-secondary" (click)="resetFormModal()">Reset</button>
    </div>
  </ng-template>
</div>
