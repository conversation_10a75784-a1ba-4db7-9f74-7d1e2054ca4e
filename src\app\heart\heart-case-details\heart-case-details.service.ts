import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import * as AppUtils from "../../common/app.utils";

@Injectable({
  providedIn: "root",
})
export class HeartCaseDetailService {
  // private apiUrl = 'http://localhost:3000/';

  constructor(private http: HttpClient) {}

  getCaseDetails(regNo) {
    return this.http.get(AppUtils.GET_CASE_DETAILS_HEART, {
      params: new HttpParams().set("regNo", regNo),
    });
  }

  saveCaseDetails(data): Observable<any> {
    return this.http.post(AppUtils.SAVE_HEART_CASE_DETAILS, data);
  }

  getLungRegistryMedicineInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(
      AppUtils.GET_LUNG_MEDICINE_INFO_ALSHIFA + estCode + "/" + patientId
    );
  }

  fetchSurgeryFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_SURGERY_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  fetchVaccineFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_VACCINE_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  fetchLabFromShifa(estCode: any, civilId: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_LAB_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }

  fetchAllDonorProcedureFromShifa(civilId: any, estCode: any): Observable<any> {
    return this.http.get(
      AppUtils.FETCH_ALL_LIVER_DONOR_PROCEDURE_FROM_ALSHIFA +
        "?estCode=" +
        estCode +
        "&civilId=" +
        civilId
    );
  }
}
