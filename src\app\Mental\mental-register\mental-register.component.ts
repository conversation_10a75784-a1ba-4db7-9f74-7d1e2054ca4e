import { ChangeDetector<PERSON>ef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ICDList } from 'src/app/_models/icdList-models';
import { Medications } from 'src/app/_models/medications.model';
import { ReAdmission } from 'src/app/_models/ReAdmission.model';
import { Symptoms } from 'src/app/_models/Symptoms.model';
import { VisitPurpose } from 'src/app/_models/VisitPurpose.model';
import { MasterService } from 'src/app/_services/master.service';
import * as AppCompUtils from "../../common/app.component-utils";
import * as AppUtils from '../../common/app.utils';

import { AppComponent } from 'src/app/app.component';
import { DecimalPipe } from '@angular/common';
import { SharedService } from 'src/app/_services/shared.service';
import { Psychotherapy } from 'src/app/_models/psychotherapy.model';
import { PatientDetailsComponent } from 'src/app/_comments/patient-details/patient-details.component';
import { MentalService } from '../mentalService';
import Swal from 'sweetalert2';
import * as _ from 'lodash';
import * as moment from 'moment';
import { RgTbMenHealthRegister } from 'src/app/_models/rgTbMenHealthRegister.model';
import { DurSymptoms } from 'src/app/_models/DurSymptoms.model';
import { Comorbidities } from 'src/app/_models/comorbidities-model';
import { Scale } from 'src/app/_models/scale.model';
import { Conditions } from 'src/app/_models/conditions.model';
import * as CommonConstants from '../../_helpers/common.constants';
import { RgTbMentalLabInvest } from 'src/app/_models/RgTbMentalLabInvest.model';

@Component({
  selector: 'app-mental-register',
  templateUrl: './mental-register.component.html',
  styleUrls: ['./mental-register.component.scss']
})
export class MentalRegisterComponent {


  patientForm: FormGroup;
  searchForm: FormGroup;
  today = new Date();
  registrationInformationForm: FormGroup;
  riskAssessment: FormGroup;
  managementplan: FormGroup;
  submitted = false;
  page1 = 2;
  pageSize = 3;
  form: FormGroup;
  followUpForm: FormGroup;
  visitPurposeList: Array<VisitPurpose>;
  durSymptomsList: Array<DurSymptoms>;
  reAdmissionList: Array<ReAdmission>;
  diagnosisIcdList: Array<ICDList>;
  symptomsList: Array<Symptoms>;
  medicationsList: Array<Medications>;
  comorbiditiesList: Array<Comorbidities>;
  psychotherapyList: Array<Psychotherapy>;
  conditionsList: Array<Conditions>;
  scaleList: Array<Scale>;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  dataFetched: boolean;
  retrivedData: any;
  mentalRegDetails: any;
  selectedDates: Date[] = [];
  selectedFUDate: any;
  menVisitDetails: any[] = [];
  diagnosisDetails: any[] = [];
  mentalSymptomsDetailsR: any[] = [];
  mentalScalesDetailsR: any[] = [];
  mentalPsychoDetailsR: any[] = [];
  mentalPharmaDetailsR: any[] = [];
  menLabInvestR: any[];
  menLabInvestF: any[];
  menLabInvest: any[];
  Math = Math;
  selectedSymptomsR: any[];
  loginId: any;
  currentDate = new Date();


  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;

  constructor(private fb: FormBuilder, private _masterService: MasterService, private _sharedService: SharedService, private _mentalService: MentalService, private __MentalService: MentalService, private cdr: ChangeDetectorRef) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode

    this.getMasterData();
    this.registrationForm();


  }



  registrationForm() {
    this.registrationInformationForm = this.fb.group({
      //rgTbPatientInfo
      bloodGroup: new FormControl(null),
      //RgTbMenHealthRegister
      regDate: new FormControl(null, Validators.required),
      reAdmission: new FormControl(null, Validators.required),
      familyHistory: new FormControl(null),
      // //RgTbMenHealthVisits
      visitType: new FormControl("R"),
      visitPurpose: new FormControl(null),
      durSymptoms: new FormControl(null, Validators.required),
      alcoholUse: new FormControl(null),
      substanceMisuse: new FormControl(null),
      weight: new FormControl(null),
      height: new FormControl(null),
      comId: [[]],
      //RgTbMenHealthSymptoms
      symptomId: [[]],
      //RgTbDiagnosis
      diagnosisIcd: new FormControl(null, Validators.required),
      bmi: new FormControl(null),
      //RgTbMentalLabInvest
      result: new FormControl(null),
      remarks: new FormControl(null),
      testId: new FormControl(null),
    });

    this.riskAssessment = this.fb.group({
      // rgTbMenHealthVisits
      suicidalRisk: new FormControl(null),
      homicidalRisk: new FormControl(null),
      infanticidalRisk: new FormControl(null),
      violenceRisk: new FormControl(null),
      // rgTbMenHealthScales
      scaleId: new FormControl(null),
    });


    this.managementplan = this.fb.group({
      //RgTbMenHealthVisits
      lifestyleMod: [false],
      // rgTbMenHealthVisits
      pharmacological: [false],
      //RgTbMenHealthPsycho
      psychotherapy: [false],
      medId: [[]],
      //rgTbMenHealthPharma
      medicationId: new FormControl(null),
      controlCond: new FormControl(null),
    });


  }

  getBmi() {
    let w = this.registrationInformationForm.get('weight').value;
    let h = this.registrationInformationForm.get('height').value;
    this.bmiDetails(w, h);
  }

  bmiDetails(weight: number, height: number) {
    let bmi = this._sharedService.calculateBMI(weight, height);
    this.registrationInformationForm.patchValue({ 
      bmi: this._decimalPipe.transform(bmi, "1.2-2") 
    });
  }
  

  ngOnInit() {
    this.getMasterData();
    this.initialsMainList();
    this.menLabInvestR = [];
    this.menLabInvestF = [];
    this.__MentalService.getMentalLabInvest().subscribe(value => {
      this.menLabInvest = value.result;
      this.menLabInvestR = _.cloneDeep(this.menLabInvest);
      this.menLabInvestF = _.cloneDeep(this.menLabInvest);
    });


    this.registrationForm();
    this.initialsFollowUpForm();

    this.registrationInformationForm.get('weight').valueChanges
      .subscribe(() => this.getBmi());
    this.registrationInformationForm.get('height').valueChanges
      .subscribe(() => this.getBmi());

    const control = this.registrationInformationForm.get('symptomId');
    if (control) {
      control.valueChanges.subscribe(values => {
        this.allSelected = values && values.length === this.symptomsList.length;
      });
    }

  }

  // Track select all state
  allSelected = false;
  allComSelected = false;
  allScaleSelected = false;
  allPsychotherapySelected = false;
  allPharmacologicalSelected = false;

  // Toggle for symptoms multi-select
  toggleSelectAll(event: Event) {
    const control = this.registrationInformationForm.get('symptomId');
    if (!control) return;

    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const allIds = this.symptomsList.map(item => item.id);
      control.setValue(allIds);
      this.allSelected = true;
    } else {
      control.setValue([]);
      this.allSelected = false;
    }
  }

  // Toggle for comorbidities multi-select
  toggleSelectAllComorbidities(event: Event) {
    const control = this.registrationInformationForm.get('comId');
    if (!control) return;

    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const allIds = this.comorbiditiesList.map(item => item.icd); 
      control.setValue(allIds);
      this.allComSelected = true;
    } else {
      control.setValue([]);
      this.allComSelected = false;
    }
  }

  toggleSelectAllScale(event: Event) {
    const control = this.riskAssessment.get('scaleId');
    if (!control) return;

    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const allIds = this.scaleList.map(item => item.id); 
      control.setValue(allIds);
      this.allScaleSelected = true;
    } else {
      control.setValue([]);
      this.allScaleSelected = false;
    }
  }


  toggleSelectAllPsychotherapy(event: Event) {
    const control = this.managementplan.get('medId');
    if (!control) return;

    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const allIds = this.psychotherapyList.map(item => item.id); 
      control.setValue(allIds);
      this.allPsychotherapySelected = true;
    } else {
      control.setValue([]);
      this.allPsychotherapySelected = false;
    }
  }

  toggleSelectAllPharmacological(event: Event) {
    const control = this.managementplan.get('medicationId');
    if (!control) return;

    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      const allIds = this.medicationsList.map(item => item.id); 
      control.setValue(allIds);
      this.allPharmacologicalSelected = true;
    } else {
      control.setValue([]);
      this.allPharmacologicalSelected = false;
    }
  }



  get startIndex(): number {
    return (this.page1 - 1) * this.pageSize + 1;
  }

  get endIndex(): number {
    return Math.min(this.page1 * this.pageSize, this.menLabInvestR.length);
  }

  private initialsFollowUpForm() {
    this.followUpForm = this.fb.group({
      visitId: new FormControl(null),
      visitDate: new FormControl(null, Validators.required),
      visitType: new FormControl("F"),

    });
  }





  getMasterData() {
    this._masterService.getVisitPurpose();
    this._masterService.visitPurposeList.subscribe(value => {
      this.visitPurposeList = value;
    });

    this._masterService.getdurSymptoms();

    this._masterService.durSymptomsList.subscribe(value => {
      this.durSymptomsList = value;
    });

    this._masterService.getreAdmission();
    this._masterService.reAdmissionList.subscribe(value => {
      this.reAdmissionList = value;
    });

    this._masterService.getDiagnosisIcd();
    this._masterService.diagnosisIcdList.subscribe(value => {
      this.diagnosisIcdList = value;
    });

    this._masterService.getSymptoms();

    this._masterService.symptomsList.subscribe(value => {
      this.symptomsList = value;
    });

    this._masterService.getMedications();
    this._masterService.medicationsList.subscribe(value => {
      this.medicationsList = value;
    });

    this._masterService.getComorbidities();
    this._masterService.comorbiditiesList.subscribe(value => {
      this.comorbiditiesList = value;
    });

    this._masterService.getPsychotherapy();
    this._masterService.psychotherapyList.subscribe(value => {
      this.psychotherapyList = value;
    });

    this._masterService.getScale();
    this._masterService.scaleList.subscribe(value => {
      this.scaleList = value;
    });

    this._masterService.getConditions();
    this._masterService.conditionsList.subscribe(value => {
      this.conditionsList = value;
    });
  }


  cellRendererBack = (data) => {
    if (data) {
      if (data > 0) {
        const formattedDate = data
        return formattedDate;
      } else {
        const [day, month, year] = data.split('-').map(Number);
        const dateObject = new Date(year, month - 1, day);
        const formattedDate = dateObject.getTime();
        return formattedDate;
      }
    } else {
      return '';
    }
  };

  onChicked(item: any, event: any) {
    item.checked = event.target.checked;
    if (!item.checked) {
      item.result = null;
      item.remarks = null;
    }
  }

  onCheckedChanged(item: any) {
    if (!item.checked) {
      item.result = null;
      item.remarks = null;
    }
  }

  onInput(item, event, type?) {
    const inputElement = event.target as HTMLInputElement;

    if (item.checked === true) {
      // Update the result for the checked test
      item[inputElement.id.replace(/[0-9]/g, '')] = inputElement.value;
    }
  }

  getVisitPurpose(purpose) {
    if (purpose) {
      return this.visitPurposeList.filter(s => s.id == purpose).map(s => s.description)[0];
    }
  }

  getAllHealthDuration(durSymptoms) {
    if (durSymptoms) {
      return this.durSymptomsList.filter(s => s.id == durSymptoms).map(s => s.description)[0];
    }
  }

  getAllReadmission(reAdmission) {
    if (reAdmission) {
      return this.reAdmissionList.filter(s => s.id == reAdmission).map(s => s.description)[0];
    }
  }

  getDiagnosisIcd(icd) {
    if (icd) {
      return this.diagnosisIcdList.filter(s => s.icd == icd).map(s => s.disease)[0];
    }
  }

  getSymptoms(symptoms) {
    if (symptoms) {
      return this.symptomsList.filter(s => s.id == symptoms).map(s => s.description)[0];
    }
  }

  getMedications(medical) {
    if (medical) {
      return this.medicationsList.filter(s => s.id == medical).map(s => s.description)[0];
    }
  }
  getComorbidities(comor) {
    if (comor) {
      return this.comorbiditiesList.filter(s => s.icd == comor).map(s => s.disease)[0];
    }
  }

  getScale(scale) {
    if (scale) {
      return this.scaleList.filter(s => s.id == scale).map(s => s.description)[0];
    }
  }

  getConditions(con) {
    if (con) {
      return this.conditionsList.filter(s => s.id == con).map(s => s.description)[0];
    }
  }



  clear() {
    this.patientDetails.clear();
    this.followUpForm.reset();
    this.initialsMainList();
    this.registrationInformationForm.reset();
    this.managementplan.reset();
    this.riskAssessment.reset();
    this.menLabInvestR = _.cloneDeep(this.menLabInvest);
  }

  getMentalRegistryByCentralNo(centralNo) {
    this._mentalService.getMentalRegistryByCentralNo(centralNo).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.clear();
        this.dataFetched = true;
        this.retrivedData = res['result'];

        this.patientDetails.setPatientDetails(this.retrivedData);
        this.getRegisterform(this.retrivedData);

        if (this.retrivedData && this.retrivedData.rgTbMenHealthRegister && this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits && this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits.length) {
          this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.menVisitDetails.push(el);
            }
            this.menVisitDetails.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());

          });
          //   this.loadFollowup();

        }

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {

      } else {
        Swal.fire(' ', 'The entered Central Number does not match any existing data. Please double-check and re-enter the correct Central Number.', 'warning')
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });

  }


  callMpiMethod() {
    this._mentalService.getMentalRegistryByCivilId(this.patientDetails.patientForm.value.civilId).subscribe(async res => {
      if (res['code'] == AppUtils.RESPONSE_SUCCESS_CODE) {
        this.clear();
        this.dataFetched = true;
        this.retrivedData = res['result'];

        this.patientDetails.setPatientDetails(this.retrivedData);
        this.getRegisterform(this.retrivedData);

        if (this.retrivedData && this.retrivedData.rgTbMenHealthRegister && this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits && this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits.length) {
          this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits.forEach((el, index) => {
            if (el.visitType == 'F') {
              el.index = index;
              this.menVisitDetails.push(el);
            }
            this.menVisitDetails.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());

          });
          //   this.loadFollowup();

        }

      } else if (res['code'] == AppUtils.RESPONSE_NO_ECORD || res['code'] == AppUtils.RESPONSE_ERROR_CODE) {
        this.getData('civilId', '', this.patientDetails.patientForm.value.civilId);
      } else {
        Swal.fire(' ', 'The entered Civil ID does not match any existing data. Please double-check and re-enter the correct Civil ID.', 'warning')
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('', 'Error occured while retrieving details', 'error')
    });

  }

  getData(searchby: any, regNo: any, civilId: any) {

    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (!(this.patientDetails.f.civilId.value && (this.patientDetails.f.exDate || this.patientDetails.f.dob))) {
        callMPI = false;
        msg = "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP."
      } else {
        Swal.fire('', 'No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.', 'warning');
      }
    }

    if (callMPI == true) {
      this._sharedService.setPatientData(this.patientDetails);
      this._sharedService.fetchMpi().subscribe(res => {
      });

    } else (
      Swal.fire('', msg, 'warning')
    )

  }


  cellRenderer = (data) => {
    if (data && data.value) {
      return '';
    }
    if (data && data.value != null) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    }
    if (data) {
      const formattedDate = moment(data).format('DD-MM-yyyy');
      return formattedDate;
    }
    else {
      return '';
    }
  };

  getRegisterform(data) {


    if (data && data.rgTbPatientInfo) {
      let bloodGroupValue = data.rgTbPatientInfo.bloodGroup;

      if (bloodGroupValue != null) {
        bloodGroupValue = String(bloodGroupValue).trim();

        const match = this.bloodGroupList.find(
          bg => String(bg.id).trim() === bloodGroupValue
        );

        if (this.registrationInformationForm && match) {
          this.registrationInformationForm.patchValue({ bloodGroup: match.id });
        }
      }
    }



    this.diagnosisDetails = data.rgTbDiagnosis;
    if (this.diagnosisDetails && this.diagnosisDetails.length > 0) {
      const apiIcdP = this.diagnosisDetails
        .filter(s => s.icdFlag === 'P')
        .map(s => s.icd);

      if (apiIcdP.length > 0) {
        const selectedIcdP = this.diagnosisIcdList.find(s => s.icd === apiIcdP[0]);
        this.registrationInformationForm.patchValue({
          diagnosisIcd: selectedIcdP ? selectedIcdP.icd : null,
        });
      }


      const apiIcdO = this.diagnosisDetails
        .filter(s => s.icdFlag === 'O')
        .map(s => s.icd);

      if (apiIcdO.length > 0) {
        this.registrationInformationForm.patchValue({
          comId: apiIcdO.length ? apiIcdO : []
        });
      }
    }




    if (data && data.rgTbMenHealthRegister) {
      let MenRegInfo = data.rgTbMenHealthRegister;
      if (MenRegInfo) {
        MenRegInfo.regDate = MenRegInfo.regDate ? this.cellRenderer(MenRegInfo.regDate) : null;
        //rgTbMenHealthVisits
        let rVisit = MenRegInfo.rgTbMenHealthVisits.filter(el => el.visitType == "R")[0];
        this.registrationInformationForm.patchValue(rVisit);
        this.mentalRegDetails = rVisit;
        this.riskAssessment.patchValue({
          suicidalRisk: this.mentalRegDetails.suicidalRisk,
          homicidalRisk: this.mentalRegDetails.homicidalRisk,
          infanticidalRisk: this.mentalRegDetails.infanticidalRisk,
          violenceRisk: this.mentalRegDetails.violenceRisk
        });

        this.managementplan.patchValue({
          lifestyleMod: this.mentalRegDetails.lifestyleMod === 'Y',
          psychotherapy: this.mentalRegDetails.psychotherapy === 'Y',
          pharmacological: this.mentalRegDetails.pharmacological === 'Y',
          controlCond: this.mentalRegDetails.controlCond
        });


        //rgTbMenHealthSymptoms
        this.mentalSymptomsDetailsR = this.mentalRegDetails.rgTbMenHealthSymptoms;
        if (rVisit && rVisit.rgTbMenHealthSymptoms && rVisit.rgTbMenHealthSymptoms.length) {
          const apiSymptomIds = rVisit.rgTbMenHealthSymptoms.map(s => s.symptomId);
          this.selectedSymptomsR = this.symptomsList.filter(s => apiSymptomIds.includes(s.id));
          this.registrationInformationForm.patchValue({
            symptomId: apiSymptomIds.length ? apiSymptomIds : []
          });

        }



        //rgTbMenHealthScales
        this.mentalScalesDetailsR = this.mentalRegDetails.rgTbMenHealthScales;
        if (this.mentalScalesDetailsR && this.mentalScalesDetailsR.length > 0) {
          const apiScales = rVisit.rgTbMenHealthScales.map(s => s.scaleId);
          this.riskAssessment.patchValue({
            scaleId: apiScales.length ? apiScales : []
          });
        }



        //rgTbMenHealthPsycho 
        this.mentalPsychoDetailsR = this.mentalRegDetails.rgTbMenHealthPsycho;
        if (this.mentalPsychoDetailsR && this.mentalPsychoDetailsR.length > 0) {
          const apiPsycho = this.mentalPsychoDetailsR.map(s => s.medId);
          setTimeout(() => {
            this.managementplan.patchValue({
              medId: apiPsycho.length ? apiPsycho : []
            });
          }, 0);
        }



        //rgTbMenHealthPharma
        this.mentalPharmaDetailsR = rVisit.rgTbMenHealthPharma;
        if (rVisit && rVisit.rgTbMenHealthPharma && rVisit.rgTbMenHealthPharma.length) {
          const apiPharma = this.mentalPharmaDetailsR.map(s => s.medId);
          setTimeout(() => {
            this.managementplan.patchValue({
              medicationId: apiPharma.length ? apiPharma : []
            });
          }, 0);
        }




        // //rgTbMentalLabInvest
        if (rVisit && rVisit.rgTbMentalLabInvest && rVisit.rgTbMentalLabInvest.length > 0) {
          this.menLabInvestR.forEach(item => {
            const retrievedData = rVisit.rgTbMentalLabInvest.find(x => x.testId === item.paramId);
            if (retrievedData) {
              item.result = retrievedData.result;
              item.remarks = retrievedData.remarks;
              item.testId = retrievedData.testId;
              item.invstId = retrievedData.invstId;
              item.createdBy = retrievedData.createdBy;
              item.createdDate = retrievedData.createdDate;
              item.checked = true;
            }
          });

          rVisit.rgTbMentalLabInvest.forEach(retrievedData => {
            const exists = this.menLabInvestR.some(item => item.paramId === retrievedData.testId);
            if (!exists) {
              let newLab: any = {
                paramId: retrievedData.testId,
                paramName: retrievedData.testName || '',
                result: retrievedData.result,
                remarks: retrievedData.remarks,
                testId: retrievedData.testId,
                invstId: retrievedData.invstId,
                createdBy: retrievedData.createdBy,
                createdDate: retrievedData.createdDate,
                checked: true
              };
              this.menLabInvestR.push(newLab);
            }
          });

        }


        const momentDate = moment(MenRegInfo.regDate, "DD-MM-YYYY").toDate();
        MenRegInfo.regDate = momentDate;

        // pass data to form
        this.registrationInformationForm.patchValue(MenRegInfo);



      }
    }



  }

  addEditFollowUp(data, action?, item?, i?) {
    data.visitType = 'F';

  }
  removeFollowUp(index: number) {
    if (index > -1 && index < this.selectedDates.length) {
      this.selectedDates.splice(index, 1);
      this.followUpForm.reset();

    }
  }

  search() {
    console.log('Search submitted:', this.searchForm.value);

  }

  openSmallWindow() {
    console.log('Open small window called');

  }

  initialsMainList() {
    this.retrivedData = this.retrivedData ? this.retrivedData : {};
    this.retrivedData.rgTbMenHealthRegister = this.retrivedData.rgTbMenHealthRegister ? this.retrivedData.rgTbMenHealthRegister : {};
    this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits = this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits ? this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits : [];
  }
  addNewFollowUp(e, action?) {
    if (this.registrationInformationForm.get('regDate').value) {
      this.initialsMainList();


      let dateExist = false;
      this.menVisitDetails.forEach(el => {
        if (this.cellRenderer(el.visitDate) == this.cellRenderer(e)) {
          dateExist = true;
          Swal.fire("", "Visit Date already exist!", "error");
          return;
        }

      })

      if (!dateExist) {
        this.menVisitDetails.push({ visitDate: e, visitId: null, ...e });
        this.retrivedData.rgTbMenHealthRegister.rgTbMenHealthVisits.push({ visitDate: e, visitId: null, ...e })


        if (this.selectedFUDate) {
          this.addEditFollowUp(this.followUpForm.value, '', this.menVisitDetails[this.followUpForm.get('index').value], this.followUpForm.get('index').value);
        }

        if (action === 'get') {
          this.getFollowUpVisit(this.menVisitDetails[this.menVisitDetails.length - 1], this.menVisitDetails.length - 1);
        }
      }
    } else {
      Swal.fire("Missing Data", "Register Form (Registration Date) Must Not Be Empty", "warning");
    }
  }

  getFollowUpVisit(data, indx) {

  }

  validatedData() {
    let regDate = this.registrationInformationForm.controls.regDate.value;
    let durSymptoms = this.registrationInformationForm.controls.durSymptoms.value;
    let diagnosisIcd = this.registrationInformationForm.controls.diagnosisIcd.value;
    let medId = this.registrationInformationForm.controls.medId.value;
    let reAdmission = this.registrationInformationForm.controls.reAdmission.value;


    if (!regDate || !durSymptoms || !diagnosisIcd || !medId || !reAdmission) {
      Swal.fire('Warning', 'Please fill in all mandatory fields.', 'warning');
      return false;

    }
    return true;
  }

  inputValidation(): boolean {
    if (!this.registrationInformationForm.get('regDate').value) {
      Swal.fire('Alert!', 'Register Date should not be null or empty.', 'warning');
      return false;
    }
    if (!this.registrationInformationForm.get('reAdmission').value) {
      Swal.fire('Alert!', 'Re-admissions should not be null or empty.', 'warning');
      return false;
    }
    if (!this.registrationInformationForm.get('durSymptoms').value) {
      Swal.fire('Alert!', 'Duration Of Symptoms should not be null or empty.', 'warning');
      return false;
    }
    if (!this.registrationInformationForm.get('diagnosisIcd').value) {
      Swal.fire('Alert!', 'ICD-10 Diagnosis should not be null or empty.', 'warning');
      return false;
    }
    if (!this.registrationInformationForm.get('symptomId').value) {
      Swal.fire('Alert!', 'Main Symptom should not be null or empty.', 'warning');
      return false;
    }

    if (!this.patientDetails.patientForm.get('regInst').value) {
      Swal.fire('Alert!', 'Institutes should not be null or empty.', 'warning');
      return false;
    }

    if (!this.patientDetails.patientForm.get('patientId').value) {
      Swal.fire('Alert!', 'patientId should not be null or empty.', 'warning');
      return false;
    }
    return true;
  }

  private boolToChar(value: boolean): string {
    return value ? 'Y' : 'N';
  }

  save() {
    this.submitted = true;

    if (!this.inputValidation()) {
      return;
    }

    // If it's a follow-up form submission
    if (this.selectedFUDate) {


      let indexControl = this.followUpForm.get('index');
      let index = indexControl ? indexControl.value : null;

      // Find the correct index based on the selectedFUDate
      if (index === null || index === undefined) {
        index = this.menVisitDetails.findIndex(item =>
          this.cellRenderer(item.visitDate) === this.cellRenderer(this.selectedFUDate)
        );

        // If the date is not found in the list, it means it's a new entry
        if (index === -1) {
          index = this.menVisitDetails ? this.menVisitDetails.length : 0;
        }
      }

      this.addEditFollowUp(
        this.followUpForm.value,
        '',
        this.menVisitDetails[index],
        index
      );
    }


    // Registration logic
    const register: RgTbMenHealthRegister = { ...this.registrationInformationForm.value };
    register.regDate = this.cellRendererBack(register.regDate);
    this.initialsFollowUpForm();
    this.followUpForm.patchValue(this.registrationInformationForm.value);
    let rVisit = _.cloneDeep(this.followUpForm.value);
    this.initialsFollowUpForm();
    rVisit.visitDate = this.cellRendererBack(this.registrationInformationForm.get('regDate').value);
    this.initialsMainList();

    // Check if Visit with Type 'F' and same Visit ID already exists
    if (this.mentalRegDetails) {
      rVisit.visitId = this.mentalRegDetails.visitId;
      rVisit.visitType = "R";  // Assign existing visitId to rVisit
    } else {
      rVisit.visitId = null;  // New visit
      rVisit.visitType = "R";
    }



    //rgTbMenHealthVisits
    Object.assign(rVisit, {
      visitPurpose: this.getValue(this.registrationInformationForm, 'visitPurpose'),
      durSymptoms: this.getValue(this.registrationInformationForm, 'durSymptoms'),
      substanceMisuse: this.getValue(this.registrationInformationForm, 'substanceMisuse'),
      alcoholUse: this.getValue(this.registrationInformationForm, 'alcoholUse'),
      height: this.getValue(this.registrationInformationForm, 'height'),
      weight: this.getValue(this.registrationInformationForm, 'weight'),

      suicidalRisk: this.getValue(this.riskAssessment, 'suicidalRisk'),
      homicidalRisk: this.getValue(this.riskAssessment, 'homicidalRisk'),
      infanticidalRisk: this.getValue(this.riskAssessment, 'infanticidalRisk'),
      violenceRisk: this.getValue(this.riskAssessment, 'violenceRisk'),

      lifestyleMod: this.boolToChar(this.getValue(this.managementplan, 'lifestyleMod') || false),
      psychotherapy: this.boolToChar(this.getValue(this.managementplan, 'psychotherapy') || false),
      pharmacological: this.boolToChar(this.getValue(this.managementplan, 'pharmacological') || false),
      controlCond: this.getValue(this.managementplan, 'controlCond'),

      createdDate: this.currentDate,
      createdBy: this.loginId
    });


    //rgTbMenHealthSymptoms
    this.mentalSymptomsDetailsR = this.getMenSymptomsDetails();
    rVisit.rgTbMenHealthSymptoms = this.mentalSymptomsDetailsR;

    //rgTbMenHealthScales
    this.mentalScalesDetailsR = this.getScalesDetails();
    rVisit.rgTbMenHealthScales = this.mentalScalesDetailsR;

    //rgTbMenHealthPsycho 
    this.mentalPsychoDetailsR = this.getPsychoDetails();
    rVisit.rgTbMenHealthPsycho = this.mentalPsychoDetailsR;


    //rgTbMenHealthPharma
    this.mentalPharmaDetailsR = this.getPharmaDetails();
    rVisit.rgTbMenHealthPharma = this.mentalPharmaDetailsR;

    //rgTbMentalLabInvest
    const isAtLeastOneLabChecked = this.menLabInvestR.some(
      item => item.checked === true
    );
    if (isAtLeastOneLabChecked) {
      rVisit.rgTbMentalLabInvest = [];
      this.menLabInvestR.forEach(e => {
        if (e.checked) {
          let lab = {
            invstId: e.invstId ? e.invstId : null,
            modifiedDate: e.createdDate ? new Date() : null,
            modifiedBy: e.createdBy ? this.loginId : null,
            createdDate: e.createdDate ? e.createdDate : new Date(),
            createdBy: e.createdBy ? e.createdBy : this.loginId,
            testId: e.paramId,
            result: e.result,
            remarks: e.remarks,
            visitType: 'R',
          };
          rVisit.rgTbMentalLabInvest.push(lab);
        }
      });
    } else {
      rVisit.rgTbMentalLabInvest = [];

    }

    // Collect visit data
    let allVisit = [];
    if (this.menVisitDetails && this.menVisitDetails.length) {
      this.menVisitDetails.forEach(el => {
        el.visitDate = this.cellRendererBack(this.cellRenderer(el.visitDate));
        allVisit.push(el);
      });
      allVisit.push({ ...rVisit });
    } else {
      allVisit.push(rVisit);
    }

    register['rgTbMenHealthVisits'] = allVisit;

    this.retrivedData.rgTbMenHealthRegister.civilId = this.patientDetails.patientForm.controls.civilId.value;
    this.retrivedData.rgTbMenHealthRegister = register;

    //rgTbDiagnosis
    this.diagnosisDetails = this.getMenDiagnosisDetails();
    this.retrivedData.rgTbDiagnosis = this.diagnosisDetails;


    //rgTbPatientInfo
    if (this.patientDetails.patientForm.valid) {
      this.retrivedData.rgTbPatientInfo = this.patientDetails.patientForm.value;
      this.retrivedData.rgTbPatientInfo.createdInstid = this.patientDetails.patientForm.controls.regInst.value;
      this.retrivedData.rgTbPatientInfo.bloodGroup = this.registrationInformationForm.controls.bloodGroup.value;
      this.retrivedData.rgTbPatientInfo.dob = this.cellRendererBack(this.cellRenderer(this.retrivedData.rgTbPatientInfo.dob));
    }

    let data = _.cloneDeep(this.retrivedData);
    data.rgTbMenHealthRegister.civilID = this.patientDetails.patientForm.controls.civilId.value;
    data.regInst = this.patientDetails.patientForm.controls.regInst.value;


    this._mentalService.saveMental(data).subscribe(
      res => {
        if (res && res["code"] == AppUtils.RESPONSE_SUCCESS_CODE_SAVE && res["result"] !== null) {
          Swal.fire({
            icon: 'success',
            title: 'SAVED',
            text: 'Registered Successfully',
            showConfirmButton: false,
            timer: 2000,
          });
          let centralNo = res["result"];
          this.clear();
          this.getMentalRegistryByCentralNo(centralNo);
        } else {
          Swal.fire("Error", " Error occurred while saveing Mental details", "error");
        }
      },
      err => {

        Swal.fire('', 'Error occurred while retrieving details', 'error');

      }
    );
  }
  getMenDiagnosisDetails(): any[] {
    const diagnosisList: any[] = [];

    
    const selectedPrimary = this.registrationInformationForm.controls['diagnosisIcd'].value;
    if (selectedPrimary) {
      const existingPrimary = (this.diagnosisDetails || []).find(x => x.icd === selectedPrimary && x.icdFlag === 'P');
      diagnosisList.push({
        runId: existingPrimary ? existingPrimary.runId : null,
        icdFlag: 'P',
        icd: selectedPrimary,
        enteredBy: existingPrimary ? existingPrimary.enteredBy : this.loginId,
        entryDate: existingPrimary ? existingPrimary.entryDate : this.currentDate,
        remarks: existingPrimary ? existingPrimary.remarks : null,
        source: existingPrimary ? existingPrimary.source : null
      });
    }

    
    const selectedOthers = this.registrationInformationForm.controls['comId'].value || [];
    selectedOthers.forEach((m: any) => {
      const comId = (m && m.icd !== undefined) ? m.icd : m;
      const existingOther = (this.diagnosisDetails || []).find(x => x.icd === comId && x.icdFlag === 'O');
      diagnosisList.push({
        runId: existingOther ? existingOther.runId : null,
        icdFlag: 'O',
        icd: comId,
        enteredBy: existingOther ? existingOther.enteredBy : this.loginId,
        entryDate: existingOther ? existingOther.entryDate : this.currentDate,
        remarks: existingOther ? existingOther.remarks : null,
        source: existingOther ? existingOther.source : null
      });
    });

    return diagnosisList;
  }


  getPharmaDetails(): any {

    const selectedMedicationId = this.managementplan.controls.medicationId.value || [];
    return selectedMedicationId.map((m: any) => {
      const medId = (m && m.id !== undefined) ? m.id : m;
      const existing = (this.mentalPharmaDetailsR || []).find(x => x.medId === medId);
      return {
        runId: existing ? existing.runId : null,  
        medId: medId
      };
    });
  }
  getPsychoDetails(): any[] {
    const selectedMeds = this.managementplan.controls.medId.value || [];
    return selectedMeds.map((m: any) => {
      const medId = (m && m.id !== undefined) ? m.id : m;
      const existing = (this.mentalPsychoDetailsR || []).find(x => x.medId === medId);
      return {
        runId: existing ? existing.runId : null,  
        medId: medId
      };
    });
  }
  getScalesDetails(): any[] {
    const selectedScales = this.riskAssessment.controls.scaleId.value || [];
    return selectedScales.map((s: any) => {
      const scaleId = (s && s.id !== undefined) ? s.id : s;
      const existing = (this.mentalScalesDetailsR || []).find(x => x.scaleId === scaleId);
      return {
        runId: existing ? existing.runId : null,  
        scaleId: scaleId
      };
    });
  }

  getMenSymptomsDetails(): any[] {
    const selectedSymptoms = this.registrationInformationForm.controls.symptomId.value || [];

    return selectedSymptoms.map((s: any) => {
      const symptomId = (s && s.id !== undefined) ? s.id : s; 

      const existing = (this.mentalSymptomsDetailsR || []).find(x => x.symptomId === symptomId);

      return {
        runId: existing ? existing.runId : null,
        symptomId: symptomId
      };
    });
  }


  getMenDetails(): any {
    return {
      regDate: this.registrationInformationForm.controls.regDate.value,
      reAdmission: this.registrationInformationForm.get('reAdmission').value,
      familyHistory: this.registrationInformationForm.get('familyHistory').value,
    };
  }
  private getValue(form: FormGroup, controlName: string) {
    return form.get(controlName) ? form.get(controlName).value : null;
  }



}



