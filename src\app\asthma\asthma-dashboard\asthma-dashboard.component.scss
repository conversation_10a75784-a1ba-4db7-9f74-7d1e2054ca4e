.inner-content{
    background: var(--white-color);
    padding: 20px;
    border:1px solid  #e8e8e8;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.06);
    border-radius: 10px;
}
.widget{
    min-height: 360px;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    margin-bottom: 20px;
    h6{
        padding: 10px;
        border-bottom:1px solid #f3f3f3 ;
    }
}
.side-panel{
    // min-height: calc(100vh - 100px);    /
    padding-left: 10px;

    .form-control{
        background: #fdfdfd;
        border-color: #f1f1f1;
        border-radius: 20px;
    }
    h6{
        margin-bottom: 10px;
        text-transform: uppercase;
        color: #bbb;
        font-size: 14px;
    }
    .badge{
        font-size: 14px;
    }
}
.dash-content{
    min-height: calc(100vh - 100px);
}
.pt-12{
    padding-top: 12% !important;
}
.chartDiv{
    padding: 10px;
}

#footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2.5rem;            /* Footer height */
  }