<h6>Heart Donor Information</h6>
<div class="content-wrapper mb-2">
    <form [formGroup]="donorForm">
        <div class="row">
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>Civil ID<span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm" formControlName="civilId"
                                [placeholder]="'Enter Civil ID'" (keyup)="onClickKey($event)"
                                placeholder="Enter Civil ID" title="Civil ID">
                        </div>
                        <span *ngIf="submitted && heartDonor.civilId==null" class="tooltiptext">{{'Civil Id is required'}}</span>
                    </div>


                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>Expiry Date <span class="mdtr">*</span></label>
                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="exDate"
                                [ngModelOptions]="{standalone: true}" monthNavigator="true" yearRange="1930:2030"
                                yearNavigator="true" showButtonBar="true"
                                (onSelect)="onExpiryDateSelect($event)"></p-calendar>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="fullname"
                        placeholder="Enter full name" title="Full name">
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint appendTo="body" [items]="genderTypeOptions" [virtualScroll]="true"
                        placeholder="Select" bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality"
                        title="Select nationality">
                        <option selected [value]="null">All Nationality</option>
                        <option [value]="res.natCode" *ngFor="let res of nationalityListFilter">{{res.nationality}}
                        </option>
                    </select>

                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Date of Birth</label>

                    <p-calendar dateFormat="dd-mm-yy" formControlName="dob" monthNavigator="true" [maxDate]=today
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                    </p-calendar>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Institutes <span class="mdtr">*</span></label>
                    <ng-select #entryPoint appendTo="body" [items]="institutes" [virtualScroll]="true"
                        formControlName="instCode" placeholder="Select" bindLabel="estName" bindValue="estCode">
                        <ng-option *ngFor="let institute of institutes" [value]="institute.estCode">
                            {{ institute.estName }}
                        </ng-option>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Patient Id <span class="mdtr">*</span></label>
                    <input type="text" class="form-control form-control-sm" formControlName="instPatientId"
                        placeholder="Enter patient ID" title="Patient ID Max 14 Characters" maxlength="14">
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4 heart-padding">
                <div class="form-group">
                    <label>Blood Group</label>
                    <ng-select class="form-control-sm" formControlName="bloodGroup" bindLabel="value" bindValue="id"
                        placeholder="Select">
                        <ng-option *ngFor="let bg of bloodGroupOptions" [value]="bg.id">{{bg.value}}</ng-option>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Telephone Number</label>
                    <input type="text" class="form-control form-control-sm" formControlName="telNo" minlength="8"
                        placeholder="Enter Telephone Number" maxlength="8" pattern="^\d{8}$" inputmode="numeric"
                        (keypress)="numberOnly($event)" title="Only numbers allowed 8 digits only">
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Address</label>
                    <textarea class="form-control form-control-sm" rows="1" formControlName="address"
                        title="Address"></textarea>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Occupation</label>
                    <input type="text" class="form-control form-control-sm" formControlName="occupation"
                        placeholder="Enter occupation" title="Occupation">
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Marital Status</label>
                    <ng-select #entryPoint appendTo="body" [items]="maritalStatusOptions" [virtualScroll]="true"
                        placeholder="Select" bindLabel="value" bindValue="id" formControlName="martialStatus">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Height <span class="mdtr">( cm )</span></label>
                    <input type="number" class="form-control form-control-sm"
                        pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b" formControlName="height"
                        placeholder="Enter height in cm" title="Height in cm">
                </div>
                <div class="tooltiptext" *ngIf="donorForm.get('height').errors?.pattern">
                    {{ "Height should be not more than 190" }}
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Body Weight <span class="mdtr">( kg )</span></label>
                    <input type="number" class="form-control form-control-sm"
                        pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b" formControlName="weight"
                        placeholder="Enter weight in kg" title="Weight in kg">
                </div>
                <div class="tooltiptext" *ngIf="donorForm.get('weight').errors?.pattern">
                    {{ "Width should be not more than 140" }}
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Name Of Kin</label>
                    <input type="text" class="form-control form-control-sm" formControlName="kinName"
                        pattern="[A-Za-z ]*" title="Only letters and spaces allowed" (input)="onKinNameInput($event)"
                        placeholder="Enter name of kin">

                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Kin Phone</label>
                    <input type="text" class="form-control form-control-sm" formControlName="kinPhone" minlength="8"
                        maxlength="8" pattern="^\d{8}$" inputmode="numeric" (keypress)="numberOnly($event)"
                        title="Only numbers allowed 8 digits only" placeholder="Enter Kin Phone">
                </div>
            </div>

            <div class="col-lg-6 col-md-6 col-sm-6">
                <div>
                    <div class="form-group">
                        <label>Donor Type</label>
                        <div class="box r-divider">
                            <div class="form-check-inline">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" name="donorType"
                                        formControlName="donorType" value="D" (change)="onDonorTypeChange('D')">Deceased
                                    Donor
                                </label>
                            </div>
                            <div class="form-check-inline">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" name="donorType"
                                        formControlName="donorType" value="R" (change)="onDonorTypeChange('R')">Living
                                    Related Donor
                                </label>
                            </div>
                            <div class="form-check-inline">
                                <label class="form-check-label">
                                    <input type="radio" class="form-check-input" name="donorType"
                                        formControlName="donorType" value="U" (change)="onDonorTypeChange('U')">Living
                                    Unrelated Donor
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border-top" *ngIf="heartDonor.donorType =='R'">
                    <div class="mt-2 row align-items-center">

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Relation Type <span class="text-danger">*</span></label>
                                <ng-select #entryPoint appendTo="body" [items]="bloodRelationList"
                                    [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                    formControlName="relationType">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Liver Relation Degree <span class="text-danger">*</span></label>
                                <select class="form-control form-control-sm" formControlName="relationDegree">
                                    <option [value]="res.id" *ngFor="let res of relatedDonorDegreeList">
                                        {{res.value}}</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-3"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null && heartDonor.relationType != null ">
                            <div class="linkpadding">
                                <a class="link" (click)="openModal(linkWithPatient)">
                                    <i class="fas fa-link"></i>
                                    Link with Patient
                                </a>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-3 btn-container"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null && heartDonor.relationType != null ">
                            <button type="button" class="btn btn-sm btn-primary" id="HLAButtonRelated"
                                (click)="donorSelectModal(donor)" title="Add HLA For Donor"
                                aria-label="Add HLA For Donor">
                                Add HLA For Donor
                            </button>
                        </div>


                    </div>
                </div>

                <div class="border-top" *ngIf="heartDonor.donorType =='U'">
                    <div class="mt-2 row align-items-center">
                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Relation Type <span class="text-danger">*</span></label>
                                <ng-select #entryPoint appendTo="body" [items]="nonBloodRelationList"
                                    [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                    formControlName="relationType">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Please Specify<span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-sm" placeholder="Please Specify"
                                    formControlName="relationDesc">
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-3"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null &&  heartDonor.relationType != null ">
                            <div class="linkpadding"><a class="link" (click)="openModal(linkWithPatient)"><i
                                        class="fas fa-link"></i>
                                    Link with Patient</a></div>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-3 btn-container"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null && heartDonor.relationType != null ">
                            <button type="button" class="btn btn-sm btn-primary" id="HLAButtonUnrelated"
                                (click)="donorSelectModal(donor)" title="Add HLA For Donor"
                                aria-label="Add HLA For Donor">Add
                                HLA
                                For Donor</button>
                        </div>
                    </div>
                </div>

                <div class="border-top" *ngIf="heartDonor.donorType =='D'">

                    <div class="mt-2 row align-items-center">
                        <div class="col-lg-4 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Donating Hospital Name <span class="text-danger"
                                        *ngIf="heartDonor.donorType == 'D'">*</span></label>
                                <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                                    bindLabel="estName" bindValue="estCode"
                                    [formControl]="donorForm.get('instCodeReadonly')" [readonly]="true"
                                    [disabled]="true">
                                    <ng-option *ngFor="let institute of institutes" [value]="institute.estCode">
                                        {{ institute.estName }}
                                    </ng-option>
                                </ng-select>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-4 col-sm-4">
                            <div class="form-group">
                                <label>Donor Hospital File No.</label>
                                <input type="text" class="form-control form-control-sm readonly-input"
                                    [formControl]="donorForm.get('instPatientIdReadonly')" readonly
                                    placeholder="Donor Hospital File No." title="Donor Hospital File No.">
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-3 col-sm-3 btn-container"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null">
                            <button type="button" class="btn btn-sm btn-primary" id="HLAButtonDeceased"
                                (click)="donorSelectModal(donor)" title="Add HLA" aria-label="Add HLA">Add
                                HLA</button>
                        </div>

                        <div class="col-lg-2 col-md-3 col-sm-3 btn-container"
                            *ngIf="heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null">
                            <button type="button" class="btn btn-sm btn-primary mr-2" id="selectPatientButton"
                                (click)="selectPatient()" title="Select Patient" aria-label="Select Patient">Select
                                Patient</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>

    <div *ngIf="showSelectPatient == true && heartDonor.donorType =='D'">
        <hr>
        <button class="btn btn-sm btn-primary mb-2" (click)="confirm()">Confirm Patient</button>
        <div>

            <div class="content-wrapper mb-2 lab-results">

                <p-dataTable [immutable]="false" [value]="patientsDeceasedDonorList" [editable]="true"
                    selectionMode="single" (onRowSelect)="onRowSelect($event)" [(selection)]="selectedPatient"
                    (onRowUnselect)="onRowUnselect($event)" [metaKeySelection]="false" dataKey="civilID"
                    [trackByFn]="trackByFn">


                    <p-column field="" header="Civil ID">
                        <ng-template let-row="rowData" pTemplate="body">
                            <div *ngIf="!row.isEditable">{{row.civilID }}</div>
                        </ng-template>
                    </p-column>

                    <p-column field="" header="Full Name">
                        <ng-template let-row="rowData" pTemplate="body">
                            <div *ngIf="!row.isEditable">{{row.fullName}}</div>
                        </ng-template>
                    </p-column>

                    <p-column field="" header="Wilayat">
                        <ng-template let-row="rowData" pTemplate="body">
                            <div *ngIf="!row.isEditable">{{row.wilayat}}</div>
                        </ng-template>
                    </p-column>

                    <p-column field="" header="Village">
                        <ng-template let-row="rowData" pTemplate="body">
                            <div *ngIf="!row.isEditable">{{row.village}}</div>
                        </ng-template>
                    </p-column>
                </p-dataTable>
            </div>
        </div>
    </div>
    <div>
        <div class="content-wrapper mb-2">
            <h6 class="section-title">Medical Procedures </h6>
            <div>
                <div class="card mb-2 h-100">
                    <div class="card-header">
                        <div class="row">
                            <div class="text-right col-sm-12">
                                <button *ngIf="showButton && (!procedureDetails || procedureDetails.length === 0) "
                                    class="btn btn-sm btn-primary"
                                    (click)="fetchAllDonorProcedureFromAlShifa()">Download
                                    Procedures</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="selected-procedures">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>Procedure Type</th>
                                            <th>Done Date</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let name of uniqueProcedureTypes">
                                            <td>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input form-check-input-sm" type="checkbox"
                                                        [id]="'proc_' + name" [checked]="isProcedureSelected(name)"
                                                        (change)="onProcedureTypeSelect(name)">
                                                    <label class="form-check-label" [for]="'proc_' + name">
                                                        {{ name }}
                                                    </label>
                                                </div>
                                            </td>
                                            <td>
                                                <p-calendar dateFormat="dd-mm-yy" showIcon="true"
                                                    [ngModel]="getProcedureDetail(name)?.doneDate"
                                                    (ngModelChange)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'doneDate', $event)"
                                                    [disabled]="!isProcedureSelected(name)" monthNavigator="true"
                                                    [maxDate]="today" yearRange="1930:2030" yearNavigator="true"
                                                    showButtonBar="true" appendTo="body">
                                                </p-calendar>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm"
                                                    style="width: 100%;" [value]="getProcedureDetail(name)?.remarks"
                                                    (input)="updateProcedureDetails(getProcedureDetail(name)?.procId, 'remarks', $event.target.value)"
                                                    placeholder="Enter remarks" [disabled]="!isProcedureSelected(name)">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-wrapper mb-2">
            <h6 class="section-title">Complication </h6>
            <div class="content-wrapper mb-2 lab-results">
                <form [formGroup]="complicationForm">

                    <div class="text-right pb-2">
                        <button type="button" *ngIf="showButton" (click)="onAddNewComp()" class="btn btn-sm btn-primary"
                            title="Add Complication" aria-label="Add new complication">Add
                            New</button>
                    </div>

                    <p-dataTable [immutable]="false" [value]="compFg" [editable]="true" dataKey="runId"
                        [responsive]="true">

                        <p-column field="paramId" header="Complication">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                                <ng-container formArrayName="rgTbGenComplication">
                                    <div [formGroupName]="rowIndex">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="runid">
                                        <div *ngIf="!row.isEditable">
                                            {{getComplicationName(row.paramId)}}
                                        </div>
                                        <div *ngIf="row.isEditable">
                                            <ng-select #entryPoint [items]="complicationMastList" [virtualScroll]="true"
                                                formControlName="paramId" placeholder="Select Complication"
                                                bindLabel="paramValue" bindValue="paramId">
                                                <ng-template ng-option-tmp let-item="item" let-index="index">{{
                                                    item.paramValue }}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>
                        <p-column field="remarks" header="Remarks">
                            <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                                <ng-container formArrayName="rgTbGenComplication">
                                    <div [formGroupName]="rowIndex">
                                        <div *ngIf="!row.isEditable">{{row.remarks}}</div>
                                        <div *ngIf="row.isEditable">
                                            <input type="text" class="form-control form-control-sm"
                                                formControlName="remarks">
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </p-column>


                        <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <button type="button" (click)="onRowEditInit(row)" *ngIf=" row.isEditable == false"
                                    class="btn btn-sm btn-primary" title="Edit row" aria-label="Edit row">
                                    <i class="fa fa-edit"></i>
                                </button>

                                <button type="button" (click)="onRowEditSave(row)" *ngIf="row.isEditable == true"
                                    class="btn btn-sm btn-primary" title="Save row" aria-label="Save row">
                                    <i class="fa fa-save"></i>
                                </button>
                            </ng-template>
                        </p-column>

                        <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                            <ng-template let-row="rowData" pTemplate="body">
                                <button type="button" (click)="delete(row)" class="btn btn-sm btn-primary"
                                    title="Delete row" aria-label="Delete row">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </ng-template>
                        </p-column>

                    </p-dataTable>
                </form>

            </div>
        </div>


        <div class="content-wrapper mb-2 ">
            <h6 class="section-title">Surgeries in Past </h6>
            <app-surgery [showAddNewButton]="showButton"
                [showSurgeryButton]="showButton && (!surgery.surgList || surgery.surgList.length === 0)"
                [submitted]="submitted" #surgery (downloadSurgery)="onDownloadSurgery()"></app-surgery>
        </div>

        <div class="content-wrapper mb-2">
            <app-lab-results [showAddNewButton]="showButton"
                [showLabButton]="showButton && (!LabResults.labnewList || LabResults.labnewList.length === 0)"
                [submitted]="submitted" [calledFromParent]="true" #LabResults
                (downloadLabResults)="onDownloadLabResults()"></app-lab-results>
        </div>

    </div>
    <div class="btn-container">
        <a *ngIf=" heartDonor.kidneyDonorId && heartDonor.kidneyDonorId != null" class="btn btn-sm btn-primary"
            href="http://nehr-app-gw.healthnet.gov.om/nehr/#/login" target="_blank" rel="noreferrer noopener">
            NEHR</a>
        <button class="btn btn-sm btn-primary" (click)="clear()">Clear</button>
        <button class="btn btn-sm btn-primary" (click)="saveDonor()">Save</button>
    </div>
</div>

<ng-template #linkWithPatient let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Link with Patient</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <form [formGroup]="patientForm">
            <div class="form-group">
                <label>Choose Patient</label>
                <ng-select #entryPoint id="ChoosePatient" appendTo="body" [items]="heartPatinetNewList"
                    formControlName="relationType" [virtualScroll]="true" placeholder="Select" bindLabel="fullName"
                    bindValue="civilid" (change)="openModalInfo(PatientInfo, $event)">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{item.fullName}}

                    </ng-template>
                </ng-select>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="form-control form-control-sm" rows="2" formControlName="relationDesc"
                    placeholder="Enter relationship description" title="Relationship description"></textarea>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-secondary" (click)="modal.dismiss('Cancel')">Cancel</button>
        <button type="button" class="btn btn-sm btn-primary" (click)="savePatientInfo()">Save</button>
    </div>
</ng-template>

<ng-template #PatientInfo let-modal>
    <div class="modal-header">
        <h4 class="modal-title">Patient Information</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="col-md-12">
            <div class="row" *ngIf="patientInfoDtlview">
                <label class="col-md-12"><b>Full Name</b> : {{patientInfoDtlview.firstName}}
                    {{patientInfoDtlview.secondName}}
                    {{patientInfoDtlview.thirdName}} {{patientInfoDtlview.tribe}}
                </label>
                <label class="col-md-12"><b>Civil Id</b> : {{patientInfoDtlview.civilid}}</label>
                <label class="col-md-12"><b>Patient Id</b> : {{patientInfoDtlview.patientid}}</label>
                <label class="col-md-12"><b>Nationality</b> : {{patientInfoDtlview.nationality}}</label>
                <label class="col-md-12"><b>Gender</b> : {{patientInfoDtlview.sexDesc}}</label>
                <label class="col-md-12"><b>DOB</b> : {{patientInfoDtlview.dob | date: 'dd-MM-yyyy'}}</label>
                <label class="col-md-12"><b>Age</b> : {{patientInfoDtlview.age}}</label>

            </div>
        </div>
    </div>


    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-primary" (click)="modal.close(PatientInfo)">Close</button>
    </div>
</ng-template>

<ng-template #donor id="donor" let-c="close" let-d="dismiss" role="dialog">
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Add Donor</h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">


        <form [formGroup]="hlaDonor">
            <div class="row">

                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Civil ID</label>
                        <input type="text" class="form-control form-control-sm" formControlName="civilId" disabled>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control form-control-sm" formControlName="name" disabled>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-4 ">
                    <div class="form-group">
                        <label>Gender</label>
                        <select class="form-control form-control-sm select-custom" formControlName="sex" disabled>
                            <option value='M'>Male</option>
                            <option value='F'>Female</option>
                        </select>

                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Nationality</label>
                        <select class="form-control form-control-sm" formControlName="nationality" disabled>
                            <option selected [value]="null">All Nationality</option>
                            <option [value]="res.natCode" *ngFor="let res of nationalityListFilter">{{res.nationality}}
                            </option>
                        </select>

                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Date of Birth</label>
                        <p-calendar dateFormat="dd-mm-yy" monthNavigator="true" yearRange="1930:2030"
                            yearNavigator="true" showButtonBar="true" formControlName="dob" [disabled]="true">
                        </p-calendar>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-4 heart-padding">
                    <div class="form-group">
                        <label>Blood Group</label>
                        <select class="form-control form-control-sm" formControlName="bloodGroup" disabled
                            title="Blood Group">
                            <option value="">Select Blood Group</option>
                            <option *ngFor="let bg of bloodGroupOptions" [value]="bg.id">{{ bg.value }}</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Telephone Number</label>
                        <input type="text" class="form-control form-control-sm" formControlName="telNo" minlength="8"
                            maxlength="8" pattern="^\d{8}$" inputmode="numeric" (keypress)="numberOnly($event)"
                            placeholder="Enter Telephone Number" title="Only numbers allowed 8 digits only" disabled>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group">
                        <label>Address</label>
                        <textarea class="form-control form-control-sm" rows="1" formControlName="address"
                            disabled></textarea>
                    </div>
                </div>
            </div>
        </form>
        <br>

        <form [formGroup]="donorTissueForm">
            <div class="row mb-2">
                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="card mb-2">
                        <div class="card-header text-center">Class 1</div>
                        <div class="card-body entry-center">
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">A:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="a_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="a_1_Test">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">B:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="b_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="b_1_Test">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">CW:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="cw_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="cw_1_Test">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-6">
                    <div class="card mb-2">
                        <div class="card-header text-center ">Class 2</div>
                        <div class="card-body entry-center custome-height">
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">DR:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="dr_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="dr_1_Test">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">DRW:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="drw_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="drw_1_Test">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-md-2 col-form-label text-right">DQ:</label>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="2"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="dq_Test">
                                </div>
                                <div class="col-sm-5 col-md-5">
                                    <input type="text" class="form-control form-control-sm" value="" maxlength="3"
                                        min="0" max="500" (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                                        (focusout)="changeFormat($event.target)" formControlName="dq_1_Test">
                                </div>
                            </div>


                        </div>
                    </div>



                </div>
            </div>
        </form>



    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-secondary" (click)="d('Cancel')">Cancel</button>
        <button type="button" class="btn btn-sm btn-primary" (click)="saveTissueTypeInfo('D'); c('Save')">Save</button>
    </div>
</ng-template>

<ng-template #ScoringInfo let-modal let-c="close" let-d="dismiss" role="dialog" id="ScoringInfo">

    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Priority Scoring</h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div>
            <ngb-tabset [destroyOnHide]="false">

                <ngb-tab>
                    <ng-template ngbTabTitle>HLA Tissue
                        Screeing</ng-template>
                    <ng-template ngbTabContent>

                        <table class="table table-hover">
                            <tbody>
                                <tr>
                                    <td></td>
                                    <th>Donor</th>
                                    <th>Patient</th>

                                    <td></td>
                                    <th>Donor</th>
                                    <th>Patient</th>
                                </tr>
                                <tr>
                                    <td class="talbeHeader">A:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.a_Test ===  HlaByRegNo.a_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.a_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.a_Test ===  HlaByRegNo.a_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.a_Test}}</td>

                                    <td class="talbeHeader">A1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.a_1_Test ===  HlaByRegNo.a_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.a_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.a_1_Test ===  HlaByRegNo.a_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.a_1_Test}}</td>
                                </tr>

                                <tr>
                                    <td class="talbeHeader">B:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.b_Test ===  HlaByRegNo.b_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.b_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.b_Test ===  HlaByRegNo.b_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.b_Test}}</td>


                                    <td class="talbeHeader">B1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.b_1_Test ===  HlaByRegNo.b_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.b_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.b_1_Test ===  HlaByRegNo.b_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.b_1_Test}}</td>

                                </tr>


                                <tr>
                                    <td class="talbeHeader">BW:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.bw_Test ===  HlaByRegNo.bw_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.bw_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.bw_Test ===  HlaByRegNo.bw_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.bw_Test}}</td>

                                    <td class="talbeHeader">BW1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.bw_1_Test ===  HlaByRegNo.bw_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.bw_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.bw_1_Test ===  HlaByRegNo.bw_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.bw_1_Test}}</td>
                                </tr>

                                <tr>
                                    <td class="talbeHeader">CW:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.cw_Test ===  HlaByRegNo.cw_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.cw_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.cw_Test ===  HlaByRegNo.cw_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.cw_Test}}</td>

                                    <td class="talbeHeader">CW1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.cw_1_Test ===  HlaByRegNo.cw_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.cw_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.cw_1_Test ===  HlaByRegNo.cw_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.cw_1_Test}}</td>
                                </tr>

                                <tr>
                                    <td class="talbeHeader">DR:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dr_Test ===  HlaByRegNo.dr_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.dr_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dr_Test ===  HlaByRegNo.dr_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.dr_Test}}</td>

                                    <td class="talbeHeader">DR1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dr_1_Test ===  HlaByRegNo.dr_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.dr_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dr_1_Test ===  HlaByRegNo.dr_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.dr_1_Test}}</td>
                                </tr>


                                <tr>
                                    <th class="talbeHeader">DRW:</th>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.drw_Test ===  HlaByRegNo.drw_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.drw_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.drw_Test ===  HlaByRegNo.drw_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.drw_Test}}</td>

                                    <th class="talbeHeader">DRW1:</th>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.drw_1_Test ===  HlaByRegNo.drw_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.drw_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.drw_1_Test ===  HlaByRegNo.drw_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.drw_1_Test}}</td>
                                </tr>

                                <tr>
                                    <td class="talbeHeader">DQ:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dq_Test ===  HlaByRegNo.dq_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.dq_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dq_Test ===  HlaByRegNo.dq_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.dq_Test}}</td>

                                    <td class="talbeHeader">DQ1:</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dq_1_Test ===  HlaByRegNo.dq_1_Test ? 'black' : 'red' }">
                                        {{HlaByDonorId.dq_1_Test}}</td>
                                    <td
                                        [ngStyle]="{'color': HlaByDonorId.dq_1_Test ===  HlaByRegNo.dq_1_Test ? 'black' : 'red' }">
                                        {{HlaByRegNo.dq_1_Test}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </ng-template>
                </ngb-tab>

            </ngb-tabset>
        </div>


    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-primary" (click)="c('Close')">Close</button>
    </div>

</ng-template>