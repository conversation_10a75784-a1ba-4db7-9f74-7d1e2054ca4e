import { Injectable } from '@angular/core';
import {Observable} from 'rxjs/Observable';
import {HttpResponse, HttpClient} from '@angular/common/http';
import * as AppUtils from '../common/app.utils';

@Injectable()
export class AlShifaLoginService {
    alShifanData: any;
    constructor(public http: HttpClient) {}

    /**
     * To invoke trusted authentication service.
     * @param estcode establishment code
     * @param persCode staff code
     * @return user details on success.
     */
    trustedAuthenticate(estCode: number, persCode: number): Observable<any> {
        // return this.http.get(AppUtils.LOGIN_CENTRAL_SERVER_NAME + AppUtils.LOGIN_CENTRAL_SERVICE_CONTEXT + AppUtils.TRUSTED_LOGIN_CENTRAL_SERVICE + estCode + '&persCode=' + persCode)
        return this.http.get(AppUtils.BACKEND_OAUTH_API_URL+'OauthServer/' + AppUtils.TRUSTED_LOGIN_CENTRAL_SERVICE + estCode + '&persCode=' + persCode)
            .catch(this.handleError);
    }

    private extractData(res: HttpResponse<any>) {
        const body = res;
        return body || {};
    } 

    private handleError(error: any) {
        return error.message || error;
    }

    setAlShifanData(data: any) {
        this.alShifanData = Array();
        this.alShifanData = data;
        
      }
    
      getAlShifanData() {
        return this.alShifanData;
      }
}
