import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from 'rxjs-compat';
import * as AppUtils from '../common/app.utils';

@Injectable({
    providedIn: 'root'
})
export class MentalService {

    constructor(private _http: HttpClient) { }

    getMentalRegistryByCivilId(civilId): Observable<any> {

        return this._http.get(AppUtils.GET_MENTAL_REGISTRY_BY_CIVIL_ID,{
          params: new HttpParams().set("civilId", civilId)
          });
        }

        getMentalRegistryByCentralNo(centralNo): Observable<any> {

          return this._http.get(AppUtils.GET_MENTAL_REGISTRY_BY_CENTRAL_NO,{
            params: new HttpParams().set("centralNo", centralNo)
            });
          }

        //MENTAL SAVING
        saveMental(data): Observable<any> {
          return this._http.post(AppUtils.SAVE_MENTAL_REGISTRY, data);
        }
        
        getMentalLabInvest(): Observable<any> {

          return this._http.get(AppUtils.GET_MEN_LAB);
          
          }



}