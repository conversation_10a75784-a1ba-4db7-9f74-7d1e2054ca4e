<div class="loginbg">
  <div class="login-container">

    <div class="wrap-login">

      <div class="logo"></div>
      <div class="brand-logo">eRegistry</div>
      <form [formGroup]="loginForm" (ngSubmit)="authenticate();" novalidate>
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" class="form-control" formControlName="userName" placeholder="Username"
            onkeyup="this.value = this.value.toUpperCase();">
        </div>
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" class="form-control" formControlName="password" placeholder="Password">
        </div>
        <div class="form-group" [ngStyle]="{'display' : showCaptcha ? 'block' : 'none'}">
          <botdetect-captcha captchaStyleName="loginCaptcha"></botdetect-captcha>
          <input id="captchaCode" name="captchaCode" type="text" class="form-control" formControlName="captchaCode">
        </div>

        <div class="col-md-12" *ngIf="showOTP">
          <label for="otpCode">Enter OTP <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="otpCode" formControlName="otpCode" maxlength="4">
          <span class="text-danger">Otp sent to Mobile no: {{ maskedMobileNumber }}</span>
        </div>

        <!--<div class="ui-input-group" [ngStyle]="{'display' : showOTP ? 'block' : 'none'}">
                            <input id="otpCode" name="userOtpInput" ngModel #userOtpInput="ngModel" type="text"
                              class="form-control form-control-sm" style="width: 150px;" placeholder="Enter the OTP">
                            <span>SENT to MOBILE no: {{ maskedMobileNumber }}</span>
                          </div> -->


        <div class="col-md-12" *ngIf="showOTP">
          <div class="col-md-12 text-end mb-3">
            <button type="button" class="btn btn-secondary btn-sm" [disabled]="resendDisabled"
              (click)="validateOtpFields()">
              Resend Otp <span *ngIf="resendDisabled">({{ countdown }}s)</span>
            </button>
          </div>
        </div>

        <div class="col-md-12 text-right">
          <button type="submit" class="btn btn-primary">Login</button>
        </div>



      </form>
      <div class="alert alert-danger mt-2" role="alert" *ngIf="message">
        {{message}}
      </div>
    </div>
    <ng-template #updateOtpProfile let-close="close">
      <form autocomplete="off" [formGroup]="updateOtpProfileForm">
        <div class="modal-header bg-danger text-white rounded-top shadow-sm">
          <h5 class="modal-title"><i class="fas fa-user-edit mr-2"></i>Update User Profile for OTP</h5>
          <button type="button" class="close text-white" (click)="close()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <div class="modal-body bg-light rounded-bottom shadow-sm p-4">
          <div class="row g-3">
            <!-- Civil ID -->
            <div class="col-md-6">
              <div class="form-group position-relative">
                <label for="civilId">Civil ID <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                  <input type="text" class="form-control form-control-sm" id="civilId" formControlName="civilId"
                    (change)="validateCivilCardInfo()">
                </div>
              </div>
              <div *ngIf="uop.civilId.errors && (uop.civilId.dirty || uop.civilId.touched)">
                <div *ngIf="uop.civilId.errors && !uop.civilId.errors.required " class="tooltiptext">civilId is invalid
                </div>
                <div *ngIf="uop.civilId.errors.required" class="tooltiptext">civilId is required</div>
              </div>
            </div>

            <!-- Civil Card Expiry Date -->
            <div class="col-md-6">
              <div class="form-group position-relative">
                <label for="civilExpiryDate">Civil Card Expiry Date <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                  <input type="date" class="form-control form-control-sm" id="civilExpiryDate"
                    formControlName="civilExpiryDate" (blur)="onBlurCivilId()">
                </div>
                <span class="text-success small" *ngIf="validateCivilCardFlag">{{mpiResponseCivilIdMsg}}</span>
                <span class="text-danger small" *ngIf="!validateCivilCardFlag">{{mpiResponseCivilIdMsg}}</span>
                <div *ngIf="uop.civilExpiryDate.errors && (uop.civilExpiryDate.dirty || uop.civilExpiryDate.touched)">
                  <div *ngIf="uop.civilExpiryDate.errors.required" class="tooltiptext">civilExpiryDate is required</div>
                </div>
              </div>
            </div>

            <!-- Email ID -->
            <div class="col-md-6">
              <div class="form-group position-relative">
                <label for="emailId">Email ID <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                  <input type="email" class="form-control form-control-sm" id="emailId" formControlName="emailId">
                </div>
              </div>
              <div *ngIf="uop.emailId.errors && (uop.emailId.dirty || uop.emailId.touched)">
                <div *ngIf="uop.emailId.errors && !uop.emailId.errors.required" class="tooltiptext">emailId is invalid
                </div>
                <div *ngIf="uop.emailId.errors.required" class="tooltiptext">Email Id is required</div>
              </div>
            </div>

            <!-- Mobile Number -->
            <div class="col-md-3">
              <div class="form-group position-relative">
                <label for="mobileNo">Mobile No <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-mobile-alt"></i></span>
                  <input type="text" class="form-control form-control-sm" id="mobileNo" formControlName="mobileNo">
                </div>
              </div>
              <div *ngIf="uop.mobileNo.errors && (uop.mobileNo.dirty || uop.mobileNo.touched)">
                <div *ngIf="uop.mobileNo.errors && !uop.mobileNo.errors.required" class="tooltiptext">Gsm is invalid
                </div>
                <div *ngIf="uop.mobileNo.errors.required" class="tooltiptext">Gsm is required</div>
              </div>
            </div>

            <!-- WhatsApp Number -->
            <div class="col-md-3">
              <div class="form-group position-relative">
                <label for="whatsAppNo">WhatsApp No</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fab fa-whatsapp"></i></span>
                  <input type="text" class="form-control form-control-sm" id="whatsAppNo" formControlName="whatsAppNo">
                </div>
              </div>
            </div>

            <!-- OTP Field - Show only after validation -->
            <div class="col-md-6" *ngIf="showOtpProfUpdate">
              <div class="form-group position-relative">
                <label for="otpCode">Enter OTP <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-key"></i></span>
                  <input type="text" class="form-control form-control-sm" maxlength="4" id="otpCode"
                    formControlName="otpCode">
                </div>
                <span class="text-danger small">Otp sent to Mobile no: {{ maskedMobileNumber }}</span>
              </div>
            </div>
          </div>

          <!-- Validate OTP Button -->
          <div class="row mt-3" *ngIf="!showOtpProfUpdate">
            <div class="col-md-12 text-end">
              <button type="button" class="btn btn-secondary btn-sm px-4" (click)="validateOtpFields()">
                <i class="fas fa-paper-plane"></i> Request Otp
              </button>
            </div>
          </div>
          <div class="row mt-3" *ngIf="showOtpProfUpdate">
            <div class="col-md-12 text-end">
              <button type="button" class="btn btn-secondary btn-sm px-4" [disabled]="resendDisabled"
                (click)="validateOtpFields()">
                <i class="fas fa-sync-alt"></i> Resend Otp <span *ngIf="resendDisabled">({{ countdown
                  }}s)</span></button>
            </div>
          </div>
        </div>

        <div class="modal-footer bg-white border-top-0 rounded-bottom shadow-sm">
          <button class="btn btn-primary btn-sm px-4" [disabled]="!showOtpProfUpdate || !validateCivilCardFlag"
            (click)="onSubmitOtpProfileFormForm()">
            <i class="fas fa-check"></i> Submit
          </button>
        </div>
      </form>
    </ng-template>
  </div>

  <div class="text-center login-footer animated fadeIn">
    <div>Copyright © {{currentYear}} <a href="#">DGIT, Ministry of Health.</a> All rights reserved.<br>Sultanate of Oman
    </div>
  </div>
</div>