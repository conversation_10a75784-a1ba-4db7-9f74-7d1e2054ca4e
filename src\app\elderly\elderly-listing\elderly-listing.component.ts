import { Component, OnInit, ViewChild } from '@angular/core';
import { SelectItem } from 'primeng/api'
import { MasterService } from '../../_services/master.service';
import { RegistryService } from '../../_services/registry.service';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { GridOptions, _ } from "ag-grid-community";
import * as AppCompUtils from '../../common/app.component-utils';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import * as AppParam from '../../_helpers/app-param.constants';
import * as AppUtils from '../../common/app.utils';
import { Router } from '@angular/router';
import { SharedService } from '../../_services/shared.service';
import * as CommonConstants from './../../_helpers/common.constants';
import { ElderlyListResult } from 'src/app/_models/elderly-list-result.model';
import { ElderlyExportExcel } from 'src/app/_models/elderly-export-excel.model';
import { ElderlyService } from '../elderly.service';
import * as FileSaver from 'file-saver';
import Swal from 'sweetalert2';
import { Paginator } from 'primeng/primeng';


declare var swal: any;
@Component({
  selector: 'app-elderly-listing',
  templateUrl: './elderly-listing.component.html',
  styleUrls: ['./elderly-listing.component.scss'],
  providers: [RegistryService]
})
export class ElderlyListingComponent implements OnInit {
 
  
  @ViewChild('elderlyRegPaginator', { static: false }) paginator: Paginator;
  rowDataExported:any[];
  elderlySearchForm: FormGroup;
  gender = AppCompUtils.GENDER;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  villageListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  rbsList: any[];
  bradenList: any[];
  mentalStateList: any[];
  rowData: Array<ElderlyListResult> = new Array<ElderlyListResult>();
  elderlyData: Array<ElderlyExportExcel> = new Array<ElderlyExportExcel>();
  gridOptions: GridOptions = <GridOptions>{
  enableColResize: true,
    pagination: false,
    resizable: true,
   paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };
  columnDefs: any[];
  excelCriteria: any={};
  totalRecords: any;
  paginationSize:any=AppUtils.E_REGISTRY_PAGINATION_SIZE;
  constructor(private _masterService: MasterService, private _registryService: RegistryService, private _elderlyService: ElderlyService, private formBuilder: FormBuilder, private _router: Router, private _sharedService: SharedService) {
    this.getMasterData();
    this.elderlySearchForm = this.formBuilder.group({
      'centralRegNo': [null],
      'ageFrom': [null],
      'ageTo': [null],
      'sex': [""],
      'estCode': [null],
      'regCode': [null],
      'walCode': [null],
      'vilCode': [null],
      'rbs': [null],
      'rbsFrom': [null],
      'rbsTo': [null],
      'braden': [null],
      'bradenFrom': [null],
      'bradenTo': [null],
      'mentalState': [null],
      'regType': [null],
    });

    this.columnDefs = [
      { headerName: 'Registration No', field: 'centralRegNo', minWidth: 125 },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 125 },
      // { headerName: 'firstName', field: 'firstName', minWidth: 150 },
      // { headerName: 'secondName', field: 'secondName', minWidth: 150 },
      // { headerName: 'thirdName', field: 'thirdName', minWidth: 150 },
      { headerName: 'fullName', field: 'fullname', minWidth: 150 },
      { headerName: 'DoB', field: 'dob', minWidth: 150 },
      { headerName: 'Institute', field: 'instname', minWidth: 150 },
      { headerName: 'Wilayat', field: 'walName', minWidth: 150 },
     

    ];

  }


  ngOnInit() {

  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsMaster();
    this._masterService.regionsMaster.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayatList = value;
      this.wallayatListFilter = this.wallayatList;
    });


    // this._masterService.getInstitutesMaster();
    // this._masterService.institutesMaster.subscribe(value => {
    //   this.institeList = value;
    //   this.institeListFilter = this.institeList;
    // });
    this._masterService.getInstitutesMasterByUserRoles().subscribe(res => {
      this.institeList = res['result'];
      this.institeListFilter = this.institeList;
    })

    // this._masterService.getWallayatMasterFull();
    this._masterService.getVillagesMasterFull();

    let examParam: any = 0
    this._masterService.getExamFreqList(examParam).subscribe(response => {
      let result: any = response.result;
      this.rbsList = result.filter(item => item.examParam === AppParam.RANDOM_BLOOD_SUGAR);
      this.bradenList = result.filter(item => item.examParam === AppParam.BRADEN_SCORE);
      this.mentalStateList = result.filter(item => item.examParam === AppParam.MENTAL_STATE);
    });
  }
  /* ------------  get DropDownList ---------------- */
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  /* ------------  filter action   ---------------- */
  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      }
      else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }
  walSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.walCode) === false) {
        if (this.elderlySearchForm.value['regCode'] && (this.elderlySearchForm.value['regCode'] != null || this.elderlySearchForm.value['regCode'] != 0)) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == this.elderlySearchForm.value['regCode']);
          this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.elderlySearchForm.value['regCode']);
        } else {
          this.institeListFilter = this.institeList;
        }
      }
      else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }
    } else {
      if (this.elderlySearchForm.value['regCode'] && (this.elderlySearchForm.value['regCode'] != null || this.elderlySearchForm.value['regCode'] != 0)) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.elderlySearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.elderlySearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    }
  }


  getWilayatList(regCode: any = 0) {

   }

  onSearch(event?:any) {
    let body = this.elderlySearchForm.value;
    let rbs;
    let braden;
    let mentalState;

    if (body['sex'] == "") {
      body['sex'] = null;
    }

    if (body['rbs'] === "" || body['rbs'] === null) {
      rbs = null;
    } else {
      rbs = {
        examParam: AppParam.RANDOM_BLOOD_SUGAR,
        freqParam: body['rbs'],
      };
    }

    if (body['braden'] === "" || body['braden'] === null) {
      braden = null;
    } else {
      braden = {

        examParam: AppParam.BRADEN_SCORE,
        freqParam: body['braden'],

      };
    }

    if (body['mentalState'] === "" || body['mentalState'] === null) {
      mentalState = null;
    } else {
      mentalState = {
        examParam: AppParam.MENTAL_STATE,
        freqParam: body['mentalState'],

      };
    }

    if (event) {
      body["startIndex"] = event.first;
      body["rowsPerPage"] = event.rows
    } else {
      if (this.paginator) { this.paginator.first = AppUtils.INDEX; }

      body["startIndex"] = AppUtils.INDEX;

      if (body["rowsPerPage"] == null || body["rowsPerPage"] == typeof ('undefined')) {
        body["rowsPerPage"] = AppUtils.E_REGISTRY_PAGINATION_SIZE;
      }
    }



    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    //  if (curUser['roles'].filter(s => s.name == 'EREGISTRY_ADMIN').length > 0) {
    //   } else if (curUser['roles'].filter(s => s.name == 'REG_ADMIN_ELD').length > 0) {
    //   } else 
    if (curUser['roles'].filter(s => s.name == 'REG_REGION_USER_ELD').length > 0) {
      // Note: if user have region roles, will set regCode depend do defults institutes
      if (body.regCode == null) {
        body.regCode = curUser['institutes'][0].regCode;
      }
    } else if (curUser['roles'].filter(s => s.name == 'REG_EST_USER_ELD').length > 0) {
      // Note: if user have establishment roles, will set estCode depend do defults institutes
      if (body.estCode == null) {
        body.estCode = curUser['institutes'][0].estCode;
      }
    }


    let searchFilter = {
      centralRegNo: body.centralRegNo,
      ageFrom: body.ageFrom,
      ageTo: body.ageTo,
      sex: body.sex,
      estCode: body.estCode,
      regCode: body.regCode,
      vilCode: body.vilCode,
      walCode: body.walCode,
      rbs: rbs,
      rbsFrom: body.rbsFrom,
      rbsTo: body.rbsTo,
      braden: braden,
      bradenFrom: body.bradenFrom,
      bradenTo: body.bradenTo,
      mentalState: mentalState,
      startIndex: body["startIndex"],
      rowsPerPage:body["rowsPerPage"]


    };

    
    this._elderlyService.getEldelyListing(searchFilter).subscribe(res => {
      
      if (res['code'] == "S0000") {
        this.rowData = res['result']['paginatedList'] || [];;
        this.totalRecords = res['result']['totalRecords'];
        this.excelCriteria = body;
        // this.rowData.forEach(el => {
        //   this.elderlyData.push({
        //     centralRegNo: el.centralRegNo, civilid: el.civilId,fullName:el.fullname,
           
        //     instname:el.instname, dob: el.dob, walName: el.walName, 
        //   })
        // })
      } else {
        this.rowData = null;
        swal('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving user details', 'error')
    })


  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['elderly/registry'], { state: { centralRegNo: event.data.centralRegNo } });
  }



  onClear() {
    this.elderlySearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = [];
    //  this.exportToExcel = null;
    this.exportExcel = null;
    this.excelCriteria = null;
    this.elderlyData = null;

  }

  exportExcel() {
    //  this.onSearch();
    this. elderlyData = new Array<ElderlyExportExcel>();
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
        this.rowData.forEach(el => {
          this.elderlyData.push({
            centralRegNo: el.centralRegNo, civilid: el.civilId,fullName:el.fullname,
            instname:el.instname, dob: el.dob, walName: el.walName, 
          })
        });
      
  
        this._sharedService.exportAsExcelFile(this. elderlyData, "Eld_Listing");
      
      } else {
        this.excelCriteria["startIndex"] = AppUtils.INDEX;
        this.excelCriteria['rowsPerPage'] = this.totalRecords;
        this._elderlyService.getEldelyListing(this.excelCriteria).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['paginatedList'];
           
            if (excelData && excelData.length > 0) {
              excelData.forEach(el => {
                this.elderlyData.push({
                  centralRegNo: el.centralRegNo, civilid: el.civilId,fullName:el.fullname,
           
                  instname:el.instname, dob: el.dob, walName: el.walName, 
                })
              })
              this._sharedService.exportAsExcelFile(this.elderlyData, "Elderly_Listing");
            }
          }
        });
      }//
    } else {
      Swal.fire('Warning!', 'Please search first', 'warning')

     

    
  }
}



  // exportToExcel() {

  //   if (this.elderlyData && this.elderlyData.length > 0) {
  //      this._sharedService.exportAsExcelFile(this.elderlyData, "Elderly_Listing"); }
  //   else {
  //     Swal.fire('Warning!', 'Please search first', 'warning')
  //   }

}
