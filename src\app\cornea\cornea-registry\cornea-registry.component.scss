:host ::ng-deep {
    .cornea-register {
        .accordion {
            .card {
                &:nth-child(4),
                &:nth-child(5) {
                    display: inline-block;
                    width: 49%;
                    margin: 0 0.5%;
                    vertical-align: top;
                    margin-bottom: 10px;
                }
                .card-body {
                    min-height: 170px;
                }
            }
        }
    }
}

.mcard {
    border: 1px solid #efefef;
    border-radius: 3px;
    margin-bottom: 10px;

    .mcard-header {
        font-size: 14px;
        padding: 8px 12px 21px;
        background: #f7f7f7;
        font-weight: bold;
        color: #8f8f8f;
    }

    .mcard-body {
        padding: 10px;

        .mcard-row {
            color: #666;
            margin-bottom: 0;
        }
    }
}

.fs-5 {
    font-size: 15px;
}

.mcard-row:nth-of-type(even) {
    background-color: rgba(0, 0, 0, .05);
}

.w-120 {
    width: 120px !important;
}

.zoom-img {
    transition: transform 0.2s;
    cursor: zoom-in;
    max-width: 80px;
    max-height: 80px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.zoom-img:hover {
    transform: scale(4.5);
    z-index: 1000;
    position: relative;
    box-shadow: 0 0 8px #888;
    background: #fff;
}

.custom-div {
    width: fit-content;
    max-width: 80%;
}

.disabled-trash {
    color: #ccc !important;
    cursor: not-allowed !important;
}

.padding-feild{
    padding-left: 26px !important;
}