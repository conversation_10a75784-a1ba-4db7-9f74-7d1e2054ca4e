import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { HeartService } from '../heart.service';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { RenalDonor } from 'src/app/_models/renal-donor.model';
import { TissueTypeModel } from 'src/app/common/objectModels/tissueTypeModel';
import { LabResultsComponent } from 'src/app/_comments/lab-result-listing/lab-result-listing.component';
import { surgeryComponent } from 'src/app/_comments/surgery/surgery.component';
import * as AppCompUtils from "../../common/app.component-utils";
import { formatDate } from '@angular/common';
import { PatientInfo } from 'src/app/_models/patient.model';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { ModalConfig } from 'src/app/config/modal-config';
import { RenalDonorPatient } from 'src/app/_models/renal_donor_patient.model';
import { RgProcedureDetailsDto, RgProcedureDetailsSaveDto, RgVwLiverDonorProceduresDto } from 'src/app/_models/liverTransplant.model';
import { MasterService } from 'src/app/_services/master.service';
import { AlShifaLoginService } from 'src/app/alshifa/alShifaLogin.service';
import { SharedService } from 'src/app/_services/shared.service';
import { NotificationService } from 'src/app/_services/notification.service';
import * as CommonConstants from "../../_helpers/common.constants";
import * as moment from 'moment';
import * as AppUtils from "../../common/app.utils";
@Component({
  selector: 'flst',
  templateUrl: './heart-donor-registry.component.html',
  styleUrls: ['./heart-donor-registry.component.scss'],
  providers: [HeartService]
})
export class HeartDonorRegistryComponent implements OnInit {
  donorForm: FormGroup;
  heartDonor: RenalDonor = new RenalDonor();
  patientsDeceasedDonorList: any = [];
  selectedPatient: any = null;
  @Input() submitted = false;
  @Input() patientForm: FormGroup;
  @Input() complicationForm: FormGroup;
  hlaTissueType: TissueTypeModel;
  @ViewChild("LabResults", { static: false }) LabResults: LabResultsComponent;
  @ViewChild("surgery", { static: false }) surgery: surgeryComponent;
  hospitalsList: any;
  genderTypeOptions = AppCompUtils.GENDER;
  bloodGroupOptions = AppCompUtils.BLOOD_GROUP;
  today: Date = new Date();
  nationalityListFilter: any[] = [];
  institutes: any[];
  compFg: any = [];
  currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");
  maritalStatusOptions: any[] = [];
  relatedDonorDegreeList: any[] = [];
  heartPatinetNewList: any = [];
  patientInfoView: PatientInfo[];
  nonBloodRelationList: any;
  showButton: boolean = false;
  bloodRelationList: any;
  estCode: any;
  modalOptions: NgbModalOptions = ModalConfig;
  hlaDonor: FormGroup;
  donorTissueForm: FormGroup;
  scoringForm: FormGroup;
  showSelectPatient: boolean = false;
  patientview: any = null;
  HlaByDonorId: any;
  HlaByRegNo: any;
  patientInfoDtlview: any;
  centralRegNo: number;
  rgTbGenComplication: any = [];
  heartDonorPtient: RenalDonorPatient;
  relationType: number;
  relationDesc: string;
  civilIdInvalid = false;
  savingTissue: any;
  procedureDetails: RgProcedureDetailsSaveDto[] = [];
  selectedProcedures: number[] = [];
  relationListFilter: any;
  procedures: RgVwLiverDonorProceduresDto[] = [];
  uniqueProcedureTypes: string[] = [];
  paginatedProcedureTypes: string[] = [];
  currentProcedurePage: number = 1;
  proceduresPerPage: number = 3;
  civilId: any;
  delRow: any;
  complicationMastList: any;
  loginId: any;
  selectedDate: string;
  selectedPatientCivilId: any;
  runId: any;
  constructor(
    private _modalService: NgbModal,
    private _masterService: MasterService,
    private _heartService: HeartService,
    private _alShifaLoginService: AlShifaLoginService,
    private _sharedService: SharedService,
    private formBuilder: FormBuilder,
    private changeDetectorRef: ChangeDetectorRef,
    private notificationService: NotificationService
  ) {
    this.getNationalityList();
    this.getRelationTypeMast();
  }
  @ViewChild("ScoringInfo", { static: false })
  public ScoringInfo: TemplateRef<any>;

  ngAfterViewInit() {
    setTimeout(() => {
      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaPathentId: any;
        let alShifaEstCode: any;
        let alShifaRegNo: any;
        let alShifaValCode: any;
        let alShifaCivilId: any;

        this._alShifaLoginService.getAlShifanData().forEach((element) => {
          if (element["regNo"]) {
            alShifaRegNo = element["regNo"];
          } else if (element["patientId"]) {
            alShifaPathentId = element["patientId"];
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          } else if (element["civilId"]) {
            alShifaCivilId = element["civilId"];
          }
        });

        if (alShifaCivilId) {
          this.getDonorDetails(alShifaCivilId, "civilId");
        }
      }

      if (this._sharedService.getNavigationData()) {
        let civilId = this._sharedService.getNavigationData().civilId;
        this.getDonorDetails(civilId, "civilId");
        this._sharedService.setNavigationData(null);
      }
    }, 1000);
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.heartDonorPtient = new RenalDonorPatient();
    this.maritalStatusOptions = AppCompUtils.Marital_Status_Type;
    this.relatedDonorDegreeList = AppCompUtils.RELATED_DONOR_DEGREE_LIST;
    this.donorForm.valueChanges.subscribe(val => {
      this.heartDonor = { ...this.heartDonor, ...val };
    });
    this.populateMasterData();
  }

  populateMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getBloodRelation().subscribe((response) => {
      this.bloodRelationList = response["result"];
    });

    this._masterService.getHospitals().subscribe((response) => {
      this.hospitalsList = response["result"];
    });

    this._masterService.getNonBloodRelation().subscribe((response) => {
      this.nonBloodRelationList = response["result"];
    });

    this.loadProcedures();

    this.complicationForm = this.formBuilder.group({
      rgTbGenComplication: this.formBuilder.array([]),
    });

    this._masterService.getHeartDonorComplicationMast().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.complicationMastList = response.result;
        } else {
          this.complicationMastList = [];
        }
      },
      error: (error) => {
        this.complicationMastList = [];
      },
    });

    this._heartService.getAllHeartDonorPatients().subscribe((response) => {
      this.patientInfoView = response["result"];

      if (response["result"].liverProcedureDtos) {
        this.procedureDetails = [];

        response["result"].liverProcedureDtos.forEach((proc) => {
          this.procedureDetails.push({
            runId: proc.runId,
            procId: proc.procId,
            doneDate: proc.doneDate ? new Date(proc.doneDate) : null,
            remarks: proc.remarks || "",
          });
        });

        this.selectedProcedures = this.procedureDetails.map(
          (proc) => proc.procId
        );

        if (!this.procedures.length) {
          this.loadProcedures();
        }
      }

      this.patientInfoView.forEach((element) => {
        let nameparts = [];
        if (element["firstName"]) nameparts.push(element["firstName"]);
        if (element["secondName"]) nameparts.push(element["secondName"]);
        if (element["thirdName"]) nameparts.push(element["thirdName"]);

        let fullName =
          "CivilID  " +
          element.civilid +
          " ," +
          nameparts.join(" ") +
          (element.tribe ? " " + element.tribe : "") +
          ", Age  " +
          element.age;
        element["fullName"] = fullName;
        this.heartPatinetNewList.push(element);
      });
    });
  }

  get f() {
    return this.donorForm.controls;
  }

  initializeFormGroup(): void {
    this.donorForm = new FormGroup({
      civilId: new FormControl(null),
      fullname: new FormControl({ value: '', disabled: true }, Validators.required),
      sex: new FormControl({ value: '', disabled: true }, Validators.required),
      dob: new FormControl({ value: '', disabled: true }, Validators.required),
      nationality: new FormControl({ value: '', disabled: true }, Validators.required),
      bloodGroup: new FormControl(null),
      telNo: new FormControl(null),
      address: new FormControl({ value: null, disabled: true }, Validators.required),
      donorType: new FormControl(null),
      instCode: new FormControl(null),
      instCodeReadonly: new FormControl({ value: null, disabled: true }),
      instPatientId: new FormControl(null),
      instPatientIdReadonly: new FormControl({ value: null, disabled: true }),
      relationDesc: new FormControl(null),
      relationType: new FormControl(null),
      exDate: new FormControl(null),
      relationDegree: new FormControl(null),
      martialStatus: new FormControl(null),
      occupation: new FormControl(null),
      height: new FormControl(null),
      weight: new FormControl(null),
      kinName: new FormControl(null),
      kinPhone: new FormControl(null),
      regType: new FormControl(null)
    });

    const instPatientIdControl = this.donorForm.get('instPatientId');
    const instPatientIdReadonly = this.donorForm.get('instPatientIdReadonly');

    if (instPatientIdControl && instPatientIdReadonly) {
      instPatientIdControl.valueChanges.subscribe(value => {
        instPatientIdReadonly.setValue(value, { emitEvent: false });
      });
    }

    const instCodeControl = this.donorForm.get('instCode');
    const instCodeReadonly = this.donorForm.get('instCodeReadonly');
    if (instCodeControl && instCodeReadonly) {
      instCodeControl.valueChanges.subscribe(value => {
        instCodeReadonly.setValue(value, { emitEvent: false });
      });
    }

    this.patientForm = new FormGroup({
      relationType: new FormControl(null),
      relationDesc: new FormControl(null),
    });

    this.hlaDonor = new FormGroup({
      donorId: new FormControl((this.heartDonor && this.heartDonor.kidneyDonorId) || null),
      civilId: new FormControl(null),
      name: new FormControl(null),
      sex: new FormControl(null),
      nationality: new FormControl(null),
      dob: new FormControl(null),
      bloodGroup: new FormControl(null),
      telNo: new FormControl(null),
      address: new FormControl(null),
    });

    this.donorTissueForm = new FormGroup({
      runId: new FormControl(null),
      donorId: new FormControl(null),
      a_Test: new FormControl(null),
      a_1_Test: new FormControl(null),
      b_Test: new FormControl(null),
      b_1_Test: new FormControl(null),
      cw_Test: new FormControl(null),
      cw_1_Test: new FormControl(null),
      dr_Test: new FormControl(null),
      dr_1_Test: new FormControl(null),
      drw_Test: new FormControl(null),
      drw_1_Test: new FormControl(null),
      dq_Test: new FormControl(null),
      dq_1_Test: new FormControl(null),
      bw_Test: new FormControl(null),
      bw_1_Test: new FormControl(null),
    });

    this.scoringForm = new FormGroup({
      pra: new FormControl(null),
      ageScore: new FormControl(null),
      dialysisPeriod: new FormControl(null),
      prevFailed: new FormControl(null),
      hlaMatch: new FormControl(null),
      bloodGroup: new FormControl(null),
      ageProximity: new FormControl(null),
      prevDonor: new FormControl(null),
      runId: new FormControl(null),
      activeYn: new FormControl(null),
      donorID: new FormControl(null),
      centralRegNo: new FormControl(null),
    });
  }

  openModal(linkWithPatient) {
    this._modalService.open(linkWithPatient, this.modalOptions);
  }

  openModalInfo(PatientInfo, event) {
    this._modalService.open(PatientInfo);
    this.getData(event.civilid);
  }

  onClickKey($event: any) {
    if ($event.keyCode == 13) {
      this.civilId = $event.target.value;
      this.getDonorDetails(this.civilId, "civilId");
    } else {
      this.civilIdInvalid = false;
    }
  }

  onExpiryDateSelect(event: any): void {
    const civilId = this.f.civilId.value;
    this.getDonorDetails(civilId, "civilId");
  }

  getNationalityList(natCode: any = 0): void {
    this._masterService.getNationalityList(natCode).subscribe(
      response => this.nationalityListFilter = response.result,
      error => { }
    );
  }

  getRelationTypeMast(relationCode: any = 0): void {
    this._masterService.getRelationTypeMast(relationCode).subscribe(
      response => this.relationListFilter = response.result,
      error => { }
    );
  }

  donorSelectModal(donor) {
    const currentDonorId = (this.heartDonor && this.heartDonor.kidneyDonorId) || null;
    const formValues = this.donorForm.getRawValue();

    this.hlaDonor.enable();

    const patchObject = {
      donorId: currentDonorId,
      civilId: formValues.civilId || null,
      name: formValues.fullname || null,
      sex: formValues.sex || "",
      nationality: formValues.nationality || null,
      dob: formValues.dob || null,
      bloodGroup: formValues.bloodGroup || null,
      telNo: formValues.telNo || null,
      address: formValues.address || null,
    };

    this.hlaDonor.patchValue(patchObject);

    this.normalizeAndPatchHlaForm();

    this.changeDetectorRef.detectChanges();
    this._modalService.open(donor, this.modalOptions);
  }

  patchFormFromModel(): void {
    if (!this.heartDonor || !this.donorForm) {
      return;
    }
    const patch: any = {};
    Object.keys(this.donorForm.controls).forEach((key) => {
      if ((this.heartDonor as any).hasOwnProperty(key)) {
        const val = (this.heartDonor as any)[key];
        patch[key] = key === 'dob' && val ? new Date(val) : val;
      }
    });
    this.donorForm.patchValue(patch);
  }

  patchModelFromForm(): void {
    this.heartDonor = { ...this.heartDonor, ...this.donorForm.getRawValue() };
  }

  getDonorDetails(id, type) {
    if (id != null) {
      const loggedInUser = JSON.parse(localStorage.getItem("currentUser"));
      if (loggedInUser) {
        const inst = loggedInUser.institutes.filter((item) => item.defaultYN === "Y");
        if (inst && inst.length > 0) {
          this.estCode = inst[0].estCode;
        }
      }

      this._heartService
        .getDonorDetails(id, type)
        .subscribe(async (response) => {
          if (response != null) {
            if (response["code"] == "S0000") {
              this.showButton = true;
              this.heartDonor = response["result"].rgTbRenalDonorDto;
              if (this.heartDonor && (this.heartDonor as any).bloodGroup) {
                const normalizedBg = this.normalizeBloodGroup((this.heartDonor as any).bloodGroup);
                if (normalizedBg) {
                  (this.heartDonor as any).bloodGroup = normalizedBg;
                }
              }
              this.hlaTissueType = response["result"].rgHlaTissueType;
              this.civilId = response["result"].rgTbRenalDonorDto.civilId;

              this.hlaDonor.patchValue({
                donorId: this.heartDonor.kidneyDonorId || null
              });

              if (this.hlaTissueType) {
                this.hlaTissueType = this.normalizeHlaModel(this.hlaTissueType);
                this.donorTissueForm.patchValue(this.hlaTissueType);
              }

              this.patchFormFromModel();
              this.donorForm.controls["civilId"].disable();
              this.donorForm.controls["exDate"].disable();

              if (response["result"].rgTbDonorComplication) {
                const rgTbGenComplication: any = response["result"].rgTbDonorComplication;
                this.compFg = [];
                this.complicationForm.setControl(
                  "rgTbGenComplication",
                  this.formBuilder.array([])
                );
                for (let compList of rgTbGenComplication) {
                  this.addNewCompl(
                    compList.runid,
                    compList.paramId,
                    compList.remarks,
                  );
                }
              }

              if (response["result"].rgTbDonorLabTests) {
                const rgTbLabTestsDB: any = response["result"].rgTbDonorLabTests;
                for (let labRList of rgTbLabTestsDB) {
                  this.LabResults.addNewLabResult(
                    labRList.runId,
                    this._sharedService.setDateFormat(labRList.testDate),
                    labRList.mohTestCode,
                    labRList.resultSummary,
                    labRList.instCode,
                    labRList.enteredBy,
                    labRList.enteredDate,
                    labRList.source,
                    false
                  );
                }
              }

              if (response["result"].rgTbDonorSurgeryDtls) {
                const rgTbSurgeryDtlsDB: any = response["result"].rgTbDonorSurgeryDtls;
                for (let surList of rgTbSurgeryDtlsDB) {
                  this.surgery.addNewSurgery(
                    surList.runId,
                    surList.surgeryID,
                    this._sharedService.setDateFormat(surList.surgeryDt),
                    surList.remarks,
                    surList.enteredBy,
                    surList.enteredDt,
                    "W",
                    false
                  );
                }
              }

              if (response["result"].rgTbDonorProcedureDtls) {
                this.procedureDetails = response["result"].rgTbDonorProcedureDtls.map((proc) => ({
                  runId: proc.runId,
                  procId: proc.procId,
                  doneDate: proc.doneDate ? new Date(proc.doneDate) : null,
                  remarks: proc.remarks || "",
                }));
                this.selectedProcedures = this.procedureDetails.map(
                  (proc) => proc.procId
                );
              }
            } else if (response["code"] == "C0001") {
              this.notificationService.showWarning('Warning!', response["message"]);
            } else if (response["code"] == "C0002") {
              const exDate = this.donorForm.value.exDate;
              const civil = this.donorForm.value.civilId;
              if (exDate == "" || exDate == null || civil == "" || civil == null) {
                this.notificationService.showWarning('Warning', 'The entered Civil ID is not in the registry file, please enter Civil ID & Expiry Date to get the data from MPI');
              } else {
                this.getPatientDetails(id);
              }
            } else {
              this.notificationService.showError('Error!', 'Error occurred while fetching donor information details: ' + response["message"]);
            }
          } else {
            this.heartDonor = new RenalDonor();
            this.notificationService.showError('', response["message"]);
          }
        });
    }
  }

  getPatientDetails(civilId) {
    if (civilId != null) {
      let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
      const loginId = curUser["person"].perscode;

      if (curUser) {
        var inst = curUser.institutes.filter((item) => item.defaultYN === "Y");
      }
      this.estCode = inst[0].estCode;

      let exDate = this.donorForm.value.exDate;
      if (exDate == "") {
        exDate = null;
      } else if (exDate != null) {
        exDate = formatDate(exDate, "yyyy-MM-dd", "en");
      }
      let req = {
        birthDate: null,
        cardExpiryDate: exDate,
        civilId: this.donorForm.value.civilId,
        requesterPersCode: loginId,
      };
      this._masterService.getMpiV2Details(req).subscribe((response) => {
        if (response != null) {
          if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            let mpires = [];
            mpires = response["result"];
            this.donorForm.patchValue({
              civilId: mpires["civilId"],
              fullname:
                mpires["firstNameEn"] +
                " " +
                mpires["secondNameEn"] +
                " " +
                mpires["thirdNameEn"] +
                " " +
                mpires["sixthNameEn"],
              nationality: mpires["countryID"],
              telNo: mpires["mobileNo"],
              sex: mpires["sex"] === "Male" ? "M" : "F",
              address: mpires["birthTown"],
              dob: new Date(mpires["birthDate"]),
            });
            this.donorForm.controls["civilId"].disable();
            this.donorForm.controls["exDate"].disable();
            this.civilId = mpires["civilId"];
          } else if (
            response["code"] == AppUtils.RESPONSE_SUCCESS_CODE &&
            response["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI
          ) {
            this.notificationService.showError('Error!', 'Error occurred while fetching civil id details: ' + response["result"]["message"]);
          } else {
            this.notificationService.showError('Error!', 'Error occurred while fetching civil id details: ' + response["message"]);
          }
        }
      });
    }
  }

  padLeft(text: any, padChar: string, size: number): string | null {
    if (!text) {
      return null;
    }
    return (String(padChar).repeat(size) + text).substr(size * -1, size);
  }

  private normalizeHlaModel<T extends { [key: string]: any }>(model: T): T {
    if (!model) return model;
    const copy: any = { ...model };
    AppCompUtils.HLA_FIELD_KEYS.forEach((key) => {
      copy[key] = this.padLeft(copy[key], '0', 2);
    });
    return copy as T;
  }

  private normalizeAndPatchHlaForm(): void {
    if (!this.donorTissueForm) return;
    const normalized = this.normalizeHlaModel(this.donorTissueForm.value || {});
    this.donorTissueForm.patchValue(normalized);
  }
  
  private normalizeBloodGroup(raw: any): string | null {
    if (raw === null || raw === undefined || raw === '') return null;
    const rawStr = String(raw).trim().toLowerCase();

    if (AppCompUtils.BLOOD_GROUP.some(bg => bg.id === raw || bg.id === rawStr)) {
      return AppCompUtils.BLOOD_GROUP.find(bg => bg.id === raw || bg.id === rawStr).id;
    }

    let found = AppCompUtils.BLOOD_GROUP.find(bg => bg.value && bg.value.trim().toLowerCase() === rawStr);
    if (found) return found.id;

    if ((AppCompUtils as any).MAP_BLOOD_GROUP) {
      found = (AppCompUtils as any).MAP_BLOOD_GROUP.find(bg => bg.value && bg.value.trim().toLowerCase() === rawStr);
      if (found) return found.id;
    }
    return null;
  }              

  selectPatient() {
    this.showSelectPatient = true;

    this._heartService.getPatientsDeceasedDonor(this.heartDonor.kidneyDonorId)
      .subscribe((res) => {
        if (res["code"] == "S0000") {
          this.patientsDeceasedDonorList = res["result"];
        } else if (res["code"] == "F0000") {
          this.notificationService.showInfo('Info!', 'No donor record found for mapping.');
        }
        else {
          this.notificationService.showError('Error!', res["message"]);
        }
      });
  }

  clear() {
    this.submitted = false;
    this.showButton = false;
    this.donorForm.reset();
    this.heartDonor[Object.keys(this.heartDonor)[0]] = "";
    this.donorForm.controls["civilId"].enable();
    this.donorForm.controls["exDate"].enable();
    this.showSelectPatient = false;
    this.patientsDeceasedDonorList = null;
    this.surgery.clear();

    this.LabResults.clear();
    this.surgery.surgList = [];

    this.LabResults.rgTbLabTests = [];

    this.compFg = [];
    this.rgTbGenComplication = [];
    this.complicationForm.reset();
    this.complicationForm = this.formBuilder.group({
      rgTbGenComplication: this.formBuilder.array([]),
    });

    this.procedureDetails = [];
    this.selectedProcedures = [];
    this.updateProcedurePagination();
  }

  confirm() {
    if (!this.patientview || this.patientview == null) {
      this.notificationService.showWarning('Warning!', 'Please select patients');
    } else {
      this.notificationService.showConfirmationDialog(
        'Confirm Mapping',
        `This donor will be mapped to patient ${this.patientview.fullName}`,
        'Confirm'
      ).then((confirmed) => {
        if (confirmed) {
          this._heartService
            .updateHeartDonorPatient(
              this.heartDonor.kidneyDonorId,
              this.patientview.civilID,
              this.patientview.centralRegNo
            )
            .subscribe((res) => {
              if (res["code"] == "S0000") {
                this.notificationService.showSuccess('Success', res["result"]);
              } else {
                this.notificationService.showError('Error!', res["message"]);
              }
            });
        }
      });
    }
  }

  onRowSelect(event) {
    this.selectedPatient = event.data;
    this.patientview = event.data;

    if (this.patientview.centralRegNo != null) {
      this.getCompareHlaScores(
        this.patientview.centralRegNo,
        this.heartDonor.kidneyDonorId
      );
    }
  }

  onRowUnselect(event) {
    this.selectedPatient = null;
    this.patientview = null;
  }

  trackByFn(index: number, item: any): any {
    return item.civilID || index;
  }

  getCompareHlaScores(regNo, donorId) {
    this._heartService
      .getCompareHlaScore(regNo, donorId)
      .subscribe((Response) => {
        this.HlaByDonorId = Response["result"].donorHla;
        this.HlaByRegNo = Response["result"].patientHla;
        if (this.HlaByRegNo != null) {
          this.showscoreModal(this.ScoringInfo);
        }
      });
  }

  showscoreModal(info) {
    this._modalService.open(info, this.modalOptions);
  }

  getData(civilid) {
    this.patientInfoDtlview = this.heartPatinetNewList.filter(
      (s) => s.civilid == civilid
    )[0];
    this.centralRegNo = this.patientInfoDtlview.centralRegNo;
  }

  savePatientInfo() {
    if (!this.patientForm.get('relationType') || !this.patientForm.get('relationType').value) {
      this.notificationService.showWarning('Warning!', 'Please select a patient first.');
      return;
    }

    const selectedRelationType = this.patientForm.get('relationType').value;

    this._heartService.getPatientsDonorbyRelationType(selectedRelationType, this.heartDonor.kidneyDonorId).subscribe(
      (checkResponse) => {
        if (checkResponse === true || (checkResponse && checkResponse["result"] === true)) {
          this.notificationService.showWarning('Warning!', 'Relation type is already selected for this donor.');
          return;
        }

        this.heartDonorPtient.renalDonorId = this.heartDonor.kidneyDonorId;
        this.heartDonorPtient.relationType = selectedRelationType;
        this.heartDonorPtient.relationDesc = this.patientForm.get('relationDesc').value;

        this._heartService.saveHeartDonorPatient(this.heartDonorPtient).subscribe(
          (res) => {
            if (res["code"] == "0") {
              this.notificationService.showSuccess('Saved!', res["message"] || 'Record has been saved successfully');
              this.patientForm.reset();
              this.relationType = null;
              this.relationDesc = null;
              this.selectedPatient = null;
              this._modalService.dismissAll();
            } else if (res["code"] == "3") {
              this.notificationService.showError('Saved!', res["message"]);
            } else {
              this.notificationService.showError('Error!', res["message"]);
            }
          },
          (err) => {
            this.notificationService.showError('Error!', 'Error occured while saving Patient: ' + err.message);
          }
        );
      },
      (error) => {
        this.notificationService.showError('Error!', 'Error occurred while checking existing relation: ' + error.message);
      }
    );
  }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onKinNameInput(event: any): void {
    const input = event.target;
    input.value = input.value.replace(/[^A-Za-z ]/g, '');

    if (this.donorForm && this.donorForm.get('kinName')) {
      this.donorForm.get('kinName').setValue(input.value, { emitEvent: false });
    }
  }

  changeFormat($event) {
    if ($event.value != null) {
      $event.value = this.padLeft($event.value, "0", 2);
    }
  }

  saveTissueTypeInfo(type: any) {
    this.donorTissueForm.value["donorId"] = this.heartDonor["kidneyDonorId"];

    if (this.donorTissueForm.value["runId"] == null) {
      this.donorTissueForm.value["runId"] = this.runId != null ? this.runId : null;
    }

    let donadBody = this.donorTissueForm.value;
    this.savingTissue = [donadBody];

    this._heartService.saveTissueTypeList(this.savingTissue).subscribe(
      (response) => {
        this._modalService.dismissAll();
        if (response["code"] == "0") {
          this.notificationService.showSuccess('Saved!', 'Patient HLA Saved successfully.');
          this.runId = response["result"];
          this.donorTissueForm.patchValue({ runId: this.runId });
        } else {
          this.notificationService.showError('Error!', response["message"]);
        }
      },
      (err) => {
        this.notificationService.showError('Error!', 'Error occured while saving Patient HLA: ' + err.message);
      }
    );
  }

  loadProcedures() {
    this._masterService.getHeartProceduresMast().subscribe((response) => {
      this.procedures = response["result"];
      this.uniqueProcedureTypes = [
        ...new Set(this.procedures.map((p) => p.procedureName)),
      ];
      this.updateProcedurePagination();
    });
  }

  updateProcedurePagination() {
    if (!this.uniqueProcedureTypes || this.uniqueProcedureTypes.length === 0) {
      this.paginatedProcedureTypes = [];
      return;
    }

    const startIndex = (this.currentProcedurePage - 1) * this.proceduresPerPage;
    const endIndex = Math.min(
      startIndex + this.proceduresPerPage,
      this.uniqueProcedureTypes.length
    );
    this.paginatedProcedureTypes = this.uniqueProcedureTypes.slice(
      startIndex,
      endIndex
    );
  }

  isProcedureSelected(type: string): boolean {
    return this.procedureDetails.some((pd) => {
      const proc = this.procedures.find((p) => p.procId === pd.procId);
      return proc ? proc.procedureName === type : false;
    });
  }

  delete(row: any) {
    this.notificationService.showConfirmationDialog(
      "Are you sure?",
      "You won't be able to revert this!",
      "Yes, delete it!"
    ).then((confirmed) => {
      if (confirmed) {
        this.rgTbGenComplication = this.complicationForm.get(
          "rgTbGenComplication"
        ) as FormArray;
        this.delRow = this.compFg.indexOf(row);
        this.compFg.splice(this.delRow, 1);
        this.rgTbGenComplication.removeAt(this.delRow);
      }
    });
  }

  getComplicationName(paramId) {
    if (
      paramId &&
      this.complicationMastList &&
      this.complicationMastList.length > 0
    ) {
      const complication = this.complicationMastList.filter(
        (s) => s.paramId == paramId
      );
      return complication && complication.length > 0
        ? complication[0].paramValue
        : "";
    }
    return "";
  }

  fetchAllDonorProcedureFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this._heartService.fetchAllDonorProcedureFromShifa(this.civilId, this.donorForm.get('instCode').value).subscribe({
        next: (res) => {
          if (res["code"] === "S0000") {
            const procedureData = res["result"];

            if (Array.isArray(procedureData) && procedureData.length > 0) {
              this.procedureDetails = procedureData.map((proc) => ({
                runId: null,
                procId: proc.procedureId,
                doneDate: proc.reportDate,
                remarks: proc.report || "",
              }));

              this.selectedProcedures = this.procedureDetails.map(
                (proc) => proc.procId
              );
            } else {
              this.notificationService.showInfo(
                "No Procedures Found",
                "No donor procedure records found for the provided Civil ID and Establishment Code."
              );
            }
          } else if (res["code"] === "F0000") {
            this.notificationService.showInfo(
              "No Records Found",
              res["message"] || "No procedure information is available for this donor."
            );
          } else {
            this.notificationService.showInfo(
              "No Procedures Found",
              "No donor procedure records found for the provided Civil ID and Establishment Code."
            );
          }
        },
        error: (error) => {
          this.notificationService.showError(
            "Server Error",
            "An error occurred while fetching procedure data. Please try again later."
          );
        },
      });
    } else {
      this.notificationService.showWarning(
        "Missing Information",
        "Please enter both Civil ID and Institute Code before fetching donor procedure data."
      );
    }
  }

  getProcedureType(procId: number): string {
    const procedure = this.procedures.find((p) => p.procId === procId);
    return procedure ? procedure.procedureName : "";
  }

  getProcedureDetail(procedureType: string): RgProcedureDetailsSaveDto | undefined {
    return this.procedureDetails.find((detail) => this.getProcedureType(detail.procId) === procedureType);
  }

  private isMissing(val: any): boolean {
    return val === null || val === undefined || (typeof val === 'string' && val.trim() === '');
  }

  onProcedureTypeSelect(name: string) {
    const procedure = this.procedures.find((p) => p.procedureName === name);
    if (procedure) {
      const existingIndex = this.procedureDetails.findIndex(
        (pd) => pd.procId === procedure.procId
      );

      if (existingIndex === -1) {
        this.procedureDetails.push({
          runId: null,
          procId: procedure.procId,
          doneDate: null,
          remarks: "",
        });
      } else {
        this.procedureDetails.splice(existingIndex, 1);
      }
    }

    this.updateProcedurePagination();
  }

  updateProcedureDetails(
    procId: number,
    field: keyof RgProcedureDetailsSaveDto | 'doneDate' | 'remarks',
    value: any
  ) {
    const detail = this.procedureDetails.find((pd) => pd.procId === procId);
    if (detail) {
      (detail as any)[field] = value;
    }
  }

  onAddNewComp() {
    this.addNewCompl(null, null, null, null);
    this.compFg[this.compFg.length - 1].isEditable = true;
  }

  addNewCompl(
    runid: any = null,
    paramId: any = null,
    remarks: any = null,
    isEditable: any = false
  ): void {
    this.rgTbGenComplication = this.complicationForm.get(
      "rgTbGenComplication"
    ) as FormArray;

    this.compFg = Object.assign([], this.rgTbGenComplication.value);
    const familyHistItem: any = this.createCOPMtem(
      runid,
      paramId,
      remarks,
      isEditable
    );
    this.rgTbGenComplication.push(this.createCompGrpItem(familyHistItem));

    this.compFg.push(familyHistItem);
  }

  createCOPMtem(
    runid: any = null,
    paramId: any = null,
    remarks: any = null,
    isEditable: any = false
  ) {
    return {
      runid: runid,
      paramId: paramId,
      remarks: remarks,
      isEditable: isEditable,
    };
  }

  createCompGrpItem(familyHistItem: any): FormGroup {
    return this.formBuilder.group(familyHistItem);
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }

  onRowEditSave(row: any) {
    let rowIndex = this.compFg.indexOf(row);
    if (!this.complicationForm.value.rgTbGenComplication[rowIndex].paramId) {
      this.notificationService.showWarning("Warning!", "Please select a Complication before saving.");
      return;
    }
    this.compFg[rowIndex] =
      this.complicationForm.value.rgTbGenComplication[rowIndex];
    let data = this.compFg[rowIndex];
    data.complicationName = this.complicationMastList
      .filter((s) => s.paramId == data.paramId)
      .map((s) => s.paramValue)[0];
    data.entryDate = moment(this.selectedDate, "DD-MM-YYYY").format();
    data.isEditable = false;
  }

  onDownloadLabResults() {
    if (this.civilId) {
      this.fetchAllDonorLabFromAlShifa();
    } else {
      this.notificationService.showWarning(
        "Warning!",
        "Please enter civilId before fetching data."
      );
    }
  }

  fetchAllDonorLabFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this._heartService.fetchAllDonorLabFromShifa(this.civilId, this.donorForm.get('instCode').value).subscribe(
        (res) => {
          if (res["code"] === "S0000") {
            const labResults = res["result"];
            if (labResults && labResults.length > 0) {
              for (let lab of labResults) {
                this.LabResults.addNewLabResult(
                  lab.runId,
                  this._sharedService.setDateFormat(lab.testDate),
                  lab.mohTestCode,
                  lab.resultSummary,
                  lab.instCode,
                  lab.enteredBy,
                  lab.enteredDate,
                  lab.source,
                  false
                );
              }
            } else {
              this.notificationService.showInfo(
                "No Lab Results",
                "No lab results were found for the given Civil ID and Establishment Code."
              );
            }
          } else if (res["code"] === "F0000") {
            this.notificationService.showInfo(
              "No Records Found",
              res["message"] || "No data available for the provided Civil ID."
            );
          } else {
            this.notificationService.showInfo(
              "No Lab Results",
              "No lab results were found for the given Civil ID and Establishment Code."
            );
          }
        },
        (error) => {
          this.notificationService.showError(
            "Server Error",
            "Unable to retrieve lab data due to a server error. Please try again later."
          );
        }
      );
    } else {
      this.notificationService.showWarning(
        "Missing Information",
        "Please ensure both Civil ID and Establishment Code are entered before fetching lab results."
      );
    }
  }

  onDownloadSurgery() {
    if (this.civilId) {
      this.fetchAllDonorSurgeryFromAlShifa();
    } else {
      this.notificationService.showWarning(
        "Warning!",
        "Please enter civilId before fetching data."
      );
    }
  }

  fetchAllDonorSurgeryFromAlShifa() {
    if (this.civilId && this.donorForm.get('instCode').value) {
      this._heartService.fetchAllDonorSurgeryFromShifa(this.civilId, this.donorForm.get('instCode').value).subscribe(
        (res) => {
          if (res["code"] === "S0000") {
            const surgeryData = res["result"];

            if (Array.isArray(surgeryData) && surgeryData.length > 0) {
              for (let sur of surgeryData) {
                this.surgery.addNewSurgery(
                  sur.runId,
                  sur.surgeryId,
                  this._sharedService.setDateFormat(sur.surgeryDate),
                  sur.surgeryRemarks,
                  sur.enteredBy,
                  sur.enteredDt,
                  "W",
                  false
                );
              }
            } else {
              Swal.fire(
                "No Surgery Records",
                "No donor surgery records found for the provided Civil ID.",
                "info"
              );
            }
          } else if (res["code"] === "F0000") {
            Swal.fire(
              "No Records Found",
              res["message"] || "No surgery details are available for this Civil ID.",
              "info"
            );
          } else {
            Swal.fire(
              "No Surgery Records",
              "No surgery records were found for the given Civil ID and Establishment Code.",
              "info"
            );
          }
        },
        (error) => {
          Swal.fire(
            "Server Error",
            "An error occurred while fetching surgery data. Please try again later.",
            "error"
          );
        }
      );
    } else {
      Swal.fire(
        "Missing Information",
        "Please enter a Civil ID before attempting to fetch donor surgery data.",
        "warning"
      );
    }
  }

  saveDonor() {
    this.patchModelFromForm();
    let rgTbDonorSurgeryDtls = this.surgery.surgeryForm.value.rgTbSurgeryDtls;
    let labTestsData = this.LabResults.labResultForm.value.rgTbLabTests;
    let complicationsaveData = this.complicationForm.value.rgTbGenComplication;

    const invalidComplications = (complicationsaveData || []).filter(c => c && !c.paramId);
    if (invalidComplications.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all complication records. Complication type is required for all complication entries.");
      return;
    }
    complicationsaveData = (complicationsaveData || []).filter(c => c && c.paramId);
    
    const invalidSurgeries = (rgTbDonorSurgeryDtls || []).filter(s => s && (!s.surgeryID || !s.surgeryDt));
    if (invalidSurgeries.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all surgery records. Surgery Type and Surgery Date are required for all surgery entries.");
      return;
    }
    rgTbDonorSurgeryDtls = (rgTbDonorSurgeryDtls || []).filter(s => s && s.surgeryID && s.surgeryDt);
    
    const invalidLabTests = (labTestsData || []).filter(l => l && (!l.mohTestCode));
    if (invalidLabTests.length > 0) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Please complete all lab test records. Test Name is required for all lab test entries.");
      return;
    }
    labTestsData = (labTestsData || []).filter(l => l && l.mohTestCode);

    const saveData = {
      kidneyDonorId: this.heartDonor.kidneyDonorId,
      rgTbRenalDonorDto: this.heartDonor,
      rgTbDonorSurgeryDtls: rgTbDonorSurgeryDtls,
      rgTbDonorProcedureDtls: this.saveProcedureDetails(),
      rgTbDonorLabTests: labTestsData,
      rgTbDonorComplication: complicationsaveData,
    };

    if (!this.heartDonor.civilId) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Civil Id Can not be empty");
      return;
    }
    const raw = this.donorForm.getRawValue();
    if (!raw.fullname) {
      this.submitted = false;
      this.notificationService.showWarning(
        'Warning',
        'Name cannot be empty. Please fetch donor details using Civil ID and Expiry Date.'
      );
      return;
    }
    if (this.heartDonor.donorType === "R") {
      const missing: string[] = [];
      if (this.isMissing(this.heartDonor.relationType)) {
        missing.push('Relation Type');
      }
      if (this.isMissing(this.heartDonor.relationDegree)) {
        missing.push('Relation Degree');
      }
      if (missing.length) {
        this.submitted = false;
        this.notificationService.showWarning(
          "Warning",
          `${missing.join(' and ')} ${missing.length > 1 ? 'are' : 'is'} mandatory for Living Related Donor`
        );
        return;
      }
    }
    if (this.heartDonor.donorType === "U") {
      const missingU: string[] = [];
      if (this.isMissing(this.heartDonor.relationType)) {
        missingU.push('Relation Type');
      }
      if (this.isMissing(this.heartDonor.relationDesc)) {
        missingU.push('Please Specify');
      }
      if (missingU.length) {
        this.submitted = false;
        this.notificationService.showWarning(
          "Warning",
          `${missingU.join(' and ')} ${missingU.length > 1 ? 'are' : 'is'} mandatory for Living Unrelated Donor`
        );
        return;
      }
    }
    if (!this.heartDonor.instCode) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Institutes can not be empty");
      return;
    }
    if (!this.heartDonor.instPatientId) {
      this.submitted = false;
      this.notificationService.showWarning("Warning", "Patient Id can not be empty");
      return;
    }
    else {
      this._heartService.saveHeartDonorRegistry(saveData).subscribe(
        (res) => {
          this.submitted = true;
          if (res["code"] == 0) {
            this.notificationService.showSuccess(
              "Saved!",
              "Donor has been saved successfully."
            );
            this.clear();
            this.getDonorDetails(res["result"], "civilId");
          } else if (res["code"] == "3") {
            this.notificationService.showError("Saved!", res["message"]);
          } else {
            this.notificationService.showError("Error!", res["message"]);
          }
        },
        (err) => {
          this.notificationService.showError(
            "Error!",
            "Error occured while saving Donor " + err.message
          );
        }
      );
    }
  }

  saveProcedureDetails(): RgProcedureDetailsDto[] {
    return this.uniqueProcedureTypes
      .filter((name) => this.isProcedureSelected(name))
      .map((name) => {
        const detail = this.getProcedureDetail(name);
        if (!detail) return null;

        let doneDate = null;
        if (detail.doneDate) {
          doneDate = detail.doneDate instanceof Date ? detail.doneDate : new Date(detail.doneDate);
        }

        return {
          procId: detail.procId,
          doneDate: doneDate,
          remarks: detail.remarks || "",
        };
      })
      .filter((detail) => detail !== null);
  }

  onDonorTypeChange(selectedValue: any) {
    this.selectedPatient = null;
    this.showSelectPatient = false;
    this.patientsDeceasedDonorList = [];
    this.patientview = null;
    this.submitted = false;

    if (selectedValue === "D") {
      const instCode = this.donorForm.get('instCode') ? this.donorForm.get('instCode').value : null;
      const instPatientId = this.donorForm.get('instPatientId') ? this.donorForm.get('instPatientId').value : null;

      if (!instCode || !instPatientId) {
        Swal.fire({
          title: "Warning",
          icon: "warning",
          text: "Please fill Institute Name and Institute Patient ID before selecting Deceased Donor type",
          confirmButtonText: "Ok",
          confirmButtonColor: "#3085d6",
        });

        const donorTypeControl = this.donorForm.get('donorType');
        if (donorTypeControl) {
          donorTypeControl.setValue(null);
        }
        this.heartDonor.donorType = '';
        return;
      }
    }

    const relationTypeCtrl = this.donorForm.get('relationType');
    const relationDegreeCtrl = this.donorForm.get('relationDegree');
    const relationDescCtrl = this.donorForm.get('relationDesc');
    if (relationTypeCtrl && relationDegreeCtrl && relationDescCtrl) {
      relationTypeCtrl.clearValidators();
      relationDegreeCtrl.clearValidators();
      relationDescCtrl.clearValidators();

      relationTypeCtrl.setValue(null);
      relationDescCtrl.setValue(null);

      if (selectedValue === 'R') {
        relationDegreeCtrl.setValue(null);
        relationTypeCtrl.setValidators([Validators.required]);
        relationDegreeCtrl.setValidators([Validators.required]);
      } else if (selectedValue === 'U') {
        relationDegreeCtrl.setValue(null);
        relationTypeCtrl.setValidators([Validators.required]);
        relationDescCtrl.setValidators([Validators.required]);
      } else {
        relationDegreeCtrl.setValue(null);
      }

      relationTypeCtrl.updateValueAndValidity({ emitEvent: false });
      relationDegreeCtrl.updateValueAndValidity({ emitEvent: false });
      relationDescCtrl.updateValueAndValidity({ emitEvent: false });
    }

    // Also clear in model
    this.heartDonor.relationType = null;
    this.heartDonor.relationDesc = null;
    this.heartDonor.relationDegree = selectedValue === 'R' ? null : null;

    this.heartDonor.donorType = selectedValue;
    this.changeDetectorRef.detectChanges();
  }
}
