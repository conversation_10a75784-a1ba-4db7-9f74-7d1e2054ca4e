﻿import { HttpClient } from '@angular/common/http';
import { Directive, Injectable, Input, ViewChild } from '@angular/core';
import * as moment from 'moment'
import * as AppUtils from '../common/app.utils';
import { InstituteDataModel } from '../common/objectModels/institute-model';
import { MasterDataService } from './app.master.service';
import { SelectItem } from 'primeng/api';
import { NotificationActionsModel } from '../common/objectModels/notification-actions.model';
import { PatientDetailsComponent } from "../_comments/patient-details/patient-details.component";
import { RegionDataModel } from '../common/objectModels/region-model';
import { NationalityDataModel } from '../common/objectModels/nationality-model';
import { WallayatDataModel } from '../common/objectModels/wallayat-model';
import { DatePipe, DecimalPipe, formatDate } from '@angular/common';
import * as CommonConstants from './../_helpers/common.constants';
import { MasterService } from "../_services/master.service";
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import Swal from 'sweetalert2';
import { AlShifaLoginService } from '../alshifa/alShifaLogin.service';
import { Observable, Subject, BehaviorSubject } from 'rxjs';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({ providedIn: 'root' })
export class SharedService {
  public patientDetails: PatientDetailsComponent;
  @Input() isTrusted;

  backNavigationData: any;
  patientData: any;
  navigationData: any;
  criteria: any;
  userData: any = {};
  priviliges: any = [];
  userInstitutes: any = [];
  selectInst = new Array<SelectItem>();
  institutes: InstituteDataModel[];
  instituteData: InstituteDataModel[] = [];
  allInstitutesData: InstituteDataModel[] = [];
  notificationSpec = { statuses: new Array<Object>(), notificationActions: new Array<NotificationActionsModel>() };
  allRegionResponse: RegionDataModel[] = [];
  nationalities: NationalityDataModel[] = [];
  wallayats: WallayatDataModel[] = [];
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  searchCivilId: any;

  // Reactive privileges signals
  private privilegesSubject = new BehaviorSubject<any[]>([]);
  public privileges$ = this.privilegesSubject.asObservable();
  private privilegesReadySubject = new BehaviorSubject<boolean>(false);
  public privilegesReady$ = this.privilegesReadySubject.asObservable();

  constructor(private _http: HttpClient, private masterDataService: MasterDataService, public datepipe: DatePipe,
    private _masterService: MasterService, private _alShifaLoginService: AlShifaLoginService) { }

  public setPatientData(patientDetails: PatientDetailsComponent) {
    this.patientDetails = patientDetails;
  }

  public ageFromDateOfBirthday(dateOfBirth: any): number {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  public dobFromAge(age: any): any {
    const date = this.getDate(age, "Y");
    return moment(date['_d']).toDate();
  }

  public getDate(age, mode) {
    let date = moment();
    if (mode === 'Y') {
      date.subtract(age, 'years');
    } else if (mode === 'M') {
      date.subtract(age, 'months');
    } else if (mode === 'D') {
      date.subtract(age, 'days');
    }
    return date;
  }



  setNavigationData(data: any) {
    this.navigationData = {};
    this.navigationData = data;

  }

  getNavigationData() {
    return this.navigationData;
  }

  setBackNavigationData(data: any) {
    this.backNavigationData = {};
    this.backNavigationData = data;
  }

  getBackNavigationData() {
    return this.backNavigationData;
  }



  // Lab service
  saveLabTest(row) {
    return this._http.post(AppUtils.SAVE_LAB_TEST, row);
  }

  updateLabTest(row) {
    return this._http.post(AppUtils.UPDATE_LAB_RESULT, row);
  }


  /**
* setUserData
*/
  public setUserData(data: any) {
    this.userData = data;
  }


  /**
   * getUserData
   */
  public getUserData() {
    return this.userData;
  }

  ///////////////////moved all following method from other shared service for or-orgnizing ////////////////////
  /**
   * To fetch the local storage value, provided the key.
   */
  readLocalStorageValue(key: string): string {
    return localStorage.getItem(AppUtils[key]);
  }


  hasPrivilege(privilege: string): boolean {
    if (!this.getPrivileges()[0]) {
      this.setPrivileges();
    }
    const flat = (this.getPrivileges() || []).reduce((acc: any[], arr: any[]) => acc.concat(arr || []), []);
    return flat.some((item: any) => item && item.name === privilege);
  }

  setPrivileges() {
    const pr: any[] = [];
    try {
      const curr = localStorage.getItem('currentUser');
      if (curr) {
        const parsed = JSON.parse(curr);
        if (parsed && Array.isArray(parsed.roles)) {
          for (let i = 0; i < parsed.roles.length; i++) {
            const role = parsed.roles[i];
            pr.push(role && role.privileges ? role.privileges : []);
          }
        }
      }
    } catch (e) {
      // ignore JSON parse error, leave pr as []
    }
    this.priviliges = pr;
    // Notify subscribers that privileges are (re)loaded
    this.privilegesSubject.next(this.priviliges);
    this.privilegesReadySubject.next(true);
  }

  // Allow components to force (re)loading from storage on init
  refreshPrivilegesFromStorage(): void {
    this.setPrivileges();
  }

  getPrivileges() {
    return this.priviliges;
  }


  async getApplicationRequiredData(): Promise<any> {
    this.userInstitutes = JSON.parse(localStorage.getItem(AppUtils.INSTITUTES));
    this.selectInst = [];
    this.institutes = [];

    const result = await this.masterDataService.getApplicationSpecification().toPromise().then(response => {
      this.allInstitutesData = response[1];
      this.instituteData = response[1];
      this.notificationSpec.statuses = response[0];
      this.allRegionResponse = response[2];
      this.nationalities = response[3];
      this.wallayats = response[4];

    });
  }


  // set date format  (01-02-2003) => (02-01-2003),
  setDateFormat(date: any) {
    if (date != null) {
      return new Date(date);
    } else {
      return null;
    }
  }

  setDateFormat2(date: any) {
    if (date != null) {
      return this.datepipe.transform(date, 'yyyy-MM-dd');
    } else {
      return null;
    }
  }

  setDateFormatDataTime(date: any) {
    if (date != null) {
      return this.datepipe.transform(date, 'yyyy-MM-dd HH:mm');
    } else {
      return null;
    }
  }

  setDateFormatDataTime2(date: any) {
    if (date != null) {
      return this.datepipe.transform(date, 'MM/dd/yyyy HH:mm');
    } else {
      return null;
    }
  }

  setCriteria(data: any) {
    this.criteria = {};
    this.criteria = data;

  }

  getCriteria() {
    return this.criteria;
  }


  filterDataByUserRole(date: any, regType: any, institeList: any) {
    // This service used to filter the result value depending on the user roles.
    let tmpList = [];

    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    if (curUser['roles'].filter(s => s.name == 'EREGISTRY_ADMIN').length > 0 || curUser['roles'].filter(s => s.name == 'REG_ADMIN_' + regType).length > 0) {
      tmpList = date;
    } else if (curUser['roles'].filter(s => s.name == 'REG_REGION_USER_' + regType).length > 0) {
      date.forEach(element => {
        if (institeList.filter(s => s.regCode == element.regCode).length > 0) {
          tmpList.push(element);
        }
      });
    } else if (curUser['roles'].filter(s => s.name == 'REG_EST_USER_' + regType).length > 0 || curUser['roles'].filter(s => s.name == 'REG_USER_' + regType).length > 0) {
      date.forEach(element => {
        if (institeList.filter(s => s.estCode == element.estCode).length > 0) {
          tmpList.push(element);
        }
      });
    }
    return tmpList;
  }

  //calculate Body Mass
  calculateBMI(width: any, hight: any) {
    if ((width && width > 0) && (hight && hight > 0)) {
      let hight_seqare = (hight / 100) * (hight / 100);
      let bmi = width / hight_seqare;
      return this._decimalPipe.transform(bmi, "1.2-2");

    } else {
      return null;
    }
  }





  //////////////////////// export to Excel ///////////////////////
  public exportAsExcelFile(json: any[], excelFileName: string) {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);

    // Adjust column widths based on content length
    const columnWidths = Object.keys(json[0] || {}).map(key => ({
      wch: Math.max(
        key.length, 
        ...json.map(row => (row[key] ? row[key].toString().length : 0))
      )
    }));
    worksheet['!cols'] = columnWidths;

    const workbook: XLSX.WorkBook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }
  private saveAsExcelFile(buffer: any, fileName: string) {
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    FileSaver.saveAs(data, fileName + '_' + this.setDateFormatDataTime(new Date()) + EXCEL_EXTENSION);
  }


  ///////////////////////////{fetch from MPI}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  // call to get MPI details by civilId id and expired date.
  fetchMpi(civilIdMPI?: any, dobMPI?: any, expDateMPI?: any): Observable<any> {
    const resultSubject = new Subject<any>();

    let civilId: any = !civilIdMPI ? this.patientDetails.patientForm.value.civilId : civilIdMPI;
    let dob = !dobMPI ? this.patientDetails.patientForm.value.dob : dobMPI;
    let expDate = !expDateMPI ? this.patientDetails.patientForm.value.exDate : expDateMPI;

    if (!(civilId && (expDate || dob))) {
      Swal.fire('MPI Service', 'Please Enter the Civil ID & Expiry Date/DOB to bring the personal details from MPI', 'warning');
      resultSubject.next(false);
      resultSubject.complete();
      return resultSubject.asObservable();
    }

    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    const loginId = curUser['person'].perscode;

    if (dob && moment(dob).isValid()) {
      dob = formatDate(dob, 'yyyy-MM-dd', 'en');
    } else {
      dob = null;
    }

    if (expDate && moment(expDate).isValid()) {
      expDate = formatDate(expDate, 'yyyy-MM-dd', 'en');
    } else {
      expDate = null;
    }

    let req = {
      "birthDate": dob,
      "cardExpiryDate": expDate,
      "civilId": civilId,
      "requesterPersCode": loginId,
    };

    this._masterService.getMpiV2Details(req).subscribe(res => {
      if (res != null) {
        let civilIdEntryType;
        if (res["code"] == AppUtils.RESPONSE_SUCCESS_CODE && res["result"]["code"] == AppUtils.RESPONSE_SUCCESS_CODE_MPI) {

          if (res["result"]["message"] == null) {
            this.patientData = this.patientDetails.setMPIDetails(res['result']);
            civilIdEntryType = AppUtils.CIVILID_ENTERY_NRS;
          } else if (res["result"]["message"] != null) {
            if (res['result']['civilId'] == null) {
              Swal.fire('', 'Error occurred while fetching civilId id details </br>' + res["result"]["message"], 'error');
              this.patientData = this.patientDetails.setMPIDetails(res['result']);
              this.patientDetails.patientForm.controls['exDate'].setValue(null);
              this.patientDetails.patientForm.controls['dob'].setValue(null);
              this.patientDetails.patientForm.controls['age'].setValue(null);
            } else {
              this.patientData = this.patientDetails.setMPIDetails(res['result']);
            }
          } else {
            Swal.fire('', 'Error occurred while fetching civilId id details </br>' + res["result"]["message"], 'error');
            this.patientDetails.patientForm.controls['exDate'].setValue(null);
            this.patientDetails.patientForm.controls['dob'].setValue(null);
          }

        } else if (res["code"] == AppUtils.RESPONSE_SUCCESS_CODE && res["result"]["code"] != AppUtils.RESPONSE_SUCCESS_CODE_MPI) {

          Swal.fire('', 'Error occurred while fetching civilId id details </br>' + res["result"]["message"], 'error');
          civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
          this.patientDetails.patientForm.controls['exDate'].setValue(null);
          this.patientDetails.patientForm.controls['dob'].setValue(null);

        } else {
          Swal.fire('', 'Error occurred while fetching civilId id details </br>' + res["message"], 'error');
          civilIdEntryType = AppUtils.CIVILID_ENTERY_MANUALLY;
          this.patientDetails.patientForm.controls['exDate'].setValue(null);
          this.patientDetails.patientForm.controls['dob'].setValue(null);
        }
        resultSubject.next(civilIdEntryType);
      }
      resultSubject.complete();
    },
      error => {
        console.error("Error while fetching MPI details:", error);
        resultSubject.error(error);
      }
    );

    return resultSubject.asObservable();
  }

  getDateFormat = (data) => {
    if (data) {
      return new Date(data).toISOString();
    } else {
      return '';
    }
  };



}
