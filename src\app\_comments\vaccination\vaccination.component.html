<h6 style="font-weight: bold; color: #973633; font-family: open_sanssemibold; background: #fff;">Vaccination</h6>
<div class="content-wrapper mb-2 lab-results">
    <!-- <app-patient-details [submitted]="false" #patientDetails></app-patient-details>
     <input type="text" class="form-control form-control-sm" formControlName="runId">
                <input type="text" class="form-control form-control-sm" formControlName="enteredBy">
                <input type="text" class="form-control form-control-sm" formControlName="source">
    -->
    <div [formGroup]="vaccinationForm">
        <div class="text-right pb-2">
            <button *ngIf="showAddNewButton" (click)="onAddNewVaccine()" class="btn btn-sm btn-primary">Add New</button>
            <button *ngIf="showVaccineButton" (click)="callFetchDataFromAlShifa()" class="btn btn-sm btn-primary">Download</button>
        </div>

        <p-dataTable [immutable]="false" [value]="vaccineFg" [editable]="true" dataKey="runId" [responsive]="true">


            <p-column field="vaccinationDate" header="Vaccination Date">
               
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.vaccinationDate | date:'dd-MM-yyyy'}}</div>
                            <div *ngIf="row.isEditable">
                                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="vaccinationDate"
                              [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                              yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>

                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>
            <p-column field="vaccineCode" header="Vaccination Name">

                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">
                                {{getVaccineName(row.vaccineCode)}}
                            </div>
                            <div *ngIf="row.isEditable">
                                <ng-select #entryPoint [items]="vaccinMastList" [virtualScroll]="true"
                                    placeholder="Select Vaccination" bindLabel="vaccineName" bindValue="vaccineId"
                                    formControlName="vaccineCode">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.vaccineName }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>
            <!-- <p-column field="vaccinatedInst" header="institute">
                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">
                                {{getVaccineInstName(row.vaccinatedInst)}}
                            </div>
                            <div *ngIf="row.isEditable">
                                <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                    formControlName="vaccinatedInst" placeholder="Select institutes" bindLabel="estName"
                                    bindValue="estCode">
                                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column> -->
            <p-column field="remarks" header="Remarks">

                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.remarks}}</div>
                            <div *ngIf="row.isEditable">
                                <input type="text" class="form-control form-control-sm" formControlName="remarks">
                                <input type="hidden" class="form-control form-control-sm" formControlName="civilId">
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>




            <!--    <p-column field="runId" header="RunId" >

                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.runId}}</div>
                            <div *ngIf="row.isEditable">
                                <input type="text" class="form-control form-control-sm" formControlName="runId">
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>



            <p-column field="enteredBy" header="Entered By" >

                <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

                    <ng-container formArrayName="rgTbVaccinationInfo">
                        <div [formGroupName]="rowIndex">
                            <div *ngIf="!row.isEditable">{{row.enteredBy}}</div>
                            <div *ngIf="row.isEditable">
                                <input type="text" class="form-control form-control-sm" formControlName="enteredBy">
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </p-column>-->




            <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                <ng-template let-row="rowData" pTemplate="body">
                    <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                    class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>
        
                  <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                    class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
                </ng-template>
            </p-column>
  
            <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                <ng-template let-row="rowData" pTemplate="body">
                    <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
                </ng-template>


            </p-column>

        </p-dataTable>
    </div>

</div>