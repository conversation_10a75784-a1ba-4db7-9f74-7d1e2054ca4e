import { NutLactation } from "./nutLactation.model";
import { NutVisitsModel } from "./nutVisits.model";

export class NutRegister {
    nutritionId: number;
    createdBy: number;
    createdDate: Date;
    epiNo: String;
    headCrBirth: number;
    healthAssesment: string;
    keyMessages: string;
    locationRemarks: string;
    lengthBirth: number;
    modifiedBy: number;
    modifiedDate: Date;
    nutritionNo: String;
    outcomeCause: number;
    regDate: Date;
    seqNo: number;
    status: number;
    weightBirth: number;

    // rgTbNutLactation: Array<NutLactation> ;
    // rgTbNutVisits: Array<NutVisitsModel> ;


    
  
  }