import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { MasterService } from '../../_services/master.service';
import { HttpClient, HttpParams } from '@angular/common/http';
// import { Observable } from 'rxjs';
import { Component, OnInit, Input, ViewChild, TemplateRef } from '@angular/core';
import * as AppParams from '../../_helpers/app-param.constants';
import * as CommonConstants from '../../_helpers/common.constants';
import * as moment from "moment";
import Swal from 'sweetalert2';
import { SharedService } from '../../_services/shared.service';
import * as AppUtils from '../../common/app.utils';
import { AppComponent } from '../../app.component';
import { RenalRegistryFrom } from '../../common/objectModels/rena-registry-model';
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import { clinicalDetailsComponent } from '../../_comments/clinical-details/clinical-details.component';
import { MedicationComponent } from '../../_comments/medication/medication.component';
import { surgeryComponent } from '../../_comments/surgery/surgery.component';
import { VaccinationComponent } from '../../_comments/vaccination/vaccination.component';
import { LabResultsComponent } from '../../_comments/lab-result-listing/lab-result-listing.component';
import { LoginService } from '../../login/login.service';
import { AlShifaLoginService } from '../../alshifa/alShifaLogin.service';
import { ComplicationComponent } from '../../complication/complication.component';
import { TbVitalSigns } from '../../common/objectModels/vital-signs-model';
import { formatDate } from '@angular/common';
import { GeneticService } from '../genetic.service';
import { GeneticDisorderRegistry } from '../../_models/genetic-disorder-registry.model';
import { Diagnosis } from '../../common/objectModels/diagnosis-model';
import { FormArray } from '@angular/forms';
import { NgbModal, ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { analyzeAndValidateNgModules } from '@angular/compiler';
import * as AppCompUtils from '../../common/app.component-utils';
import { NgxSpinnerService } from 'ngx-spinner';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { async } from '@angular/core/testing';
import { RegistryService } from 'src/app/_services/registry.service';
import { DeathDetailsComponent } from 'src/app/_comments/death-details/death-details.component';


@Component({
  selector: 'app-blood-disorder',
  templateUrl: './blood-disorder.component.html',
  styleUrls: ['./blood-disorder.component.scss']
})
export class BloodDisorderComponent implements OnInit {

  isOpened = true;
  isOpened2 = true;
  isOpened3 = true;
  isOpened4 = true;
  isOpened5 = true;
  isOpened6 = true;
  value: Date;
  fieldArray: Array<any> = [];
  labList: any[];
  labTest: any;
  //centralRegNo: number = 33;
  labnewList: any[];
  labTestName: any[];
  compFg: any = [];
  testDone: any[];
  disLabFollowUpInfoalshifa: any;
  selectedTestCode: any;
  testcomponet: any;
  public rgTbGenDisorderDtls: any = [];
  newAttribute: any = {};
  disRegForm: any;
  hplcForm: any;
  diagnosticForm: any = [];
  testNameArray: any;
  compId: any;
  complicationMastList: any;
  ////itemsTableArray= [];
  renalDonor: any;
  ///  genDisorderReg: GeneticDisorderRegistry;
  formData: GeneticDisorderRegistry;
  diagnosisData: Array<Diagnosis>;
  civilId: any;
  civilIdInvalid: boolean;
  nationListFilter: any;
  nationList: any;
  wallayatList: any[];
  villageList: any[];
  rgTbFamilyDtl: any[];
  wallayatListFilter: any[];
  bloodDisorder: any;
  patientId: any;
  icd: any;
  //institutes: any[];
  institutes: any;
  medicineMaster: any;
  surgeryMaster: any;
  createdBy: any;
  componentTestName: any;
  genobase: any;
  regId: any;
  /// formData: RenalRegistryFrom;
  famHistory: any = [];
  diagnosticData: any = [];
  today = new Date();
  delRow: any;
  relation: any;
  hplc: any;
  hbd: any;
  vaccinMastList: any;
  rgTbVaccinationInfoDB: any
  dispatientInfoalshifa: any;
  disclinicalInfoalshifa: any;
  dismedicineInfoalshifa: any;
  disSurgeryInfoalshifa: any;
  disVaccineInfoalshifa: any;
  /// disLabFollowUpInfoalshifa: any;
  disLabInfoalshifa: any;
  disHplHistorylshifa: any;
  refreshHPLC: any;
  rgTbGenDisorderDtlsDB: any[];
  public rgTbGenComplication: any = [];
  complicationList: any[];
  public discoderType: any;
  /// public genotype: string;
  public remarks: any;
  public medicineType: any;
  public frequency: any;

  rgTbVaccinationInfo: any = [];
  vaccineFg: any = [];
  public dose: any;
  public medicineId: any;
  public medName: any;
  public startDate: any;
  public genotypeBase: any;
  public transfusedDate: any;
  public transfusedInst: any;
  //public paramValue: any;
  public enteredBy: any;
  public orderByName: any;
  public releasedDate: any;
  public instCode: any;
  public genotype: any;
  public surgeryName: any;
  public surgeryDt: any;
  public vaccinatedInst: any;
  public rgTbFamilyHistory: any = [];
  public REG_TYPE_GENETIC_BLOOD: any = AppUtils.REG_TYPE_GENETIC_BLOOD;
  loginId: any;
  loginName: any;
  hbsresult: any;
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  downloadFromShifa: boolean = true;
  currentCivilId = '';
  estCode;
  setHplc;
  hplcMohTestCode = 10070;
  bloodGroupMohTestCode = 10208;
  antigenPhenoTypingMohTestCode = 10221;
  antibodyInvestigationMohTestCode = 10223;

  hplcList = AppCompUtils.HLPC_TEST;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  mapBloodGroupList = AppCompUtils.MAP_BLOOD_GROUP;
  DisabledButton = false;
  isAntigenPhenoTypingFormEditable = false;
  isAntibodyInvestigationEditable = false
  nehrVisitClassCount: any;
  opdClassCount: any = 0;
  ipdClassCount: any = 0;
  emrClassCount: any = 0;
  alive = true;
  setDefultGeneticTypeId: any;
  genobaseDropdownSettings: IDropdownSettings;
  rgTbGenGenotypeDtlsDB: any = [];
  selectedItem: any;
  updateHplcInvstid: any;
  hplcHistoryHeader: Array<any>;
  hplcHistoryDetails: Array<any>;
  hplcHistorycForm: any;
  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  @ViewChild('clinicalDetails', { static: false }) clinicalDetails: clinicalDetailsComponent;
  @ViewChild('Medication', { static: false }) Medication: MedicationComponent;
  @ViewChild('surgery', { static: false }) surgery: surgeryComponent;
  @ViewChild('Vaccination', { static: false }) Vaccination: VaccinationComponent;
  @ViewChild('LabResults', { static: false }) LabResults: LabResultsComponent;
  @ViewChild('complication', { static: false }) complication: ComplicationComponent;
  @ViewChild('deathDetails', { static: false }) deathDetails: DeathDetailsComponent;
  

  @ViewChild('hplcHistory', { static: false }) public hplcHistory: TemplateRef<any>;

  @Input() patientForm: FormGroup;

  @Input() submitted = false;

  genDisorderForm: FormGroup;
  genHplcHisForm: FormGroup;

  constructor(private _masterService: MasterService, private fb: FormBuilder, private _sharedService: SharedService, private _registryService: RegistryService, private _geneticService: GeneticService, private modalService: NgbModal,

    private _http: HttpClient, private loginService: LoginService, private _alShifaLoginService: AlShifaLoginService, private spinner: NgxSpinnerService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode;
    this.loginName = curUser['person'].personName;

    this.initializeFormGroup();



    this.getNationalityList();
    this.getWilayatList();
    this.getVillageList();

  }


  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(response => {
      this.nationList = response.result;
      this.nationListFilter = this.nationList;

    }, error => {

    });
  }

  getWilayatList(regCode: any = 0) {
    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {
    });
  }

  getVillageList(walCode: any = 0) {
    this._masterService.getVillageList(walCode).subscribe(response => {
      this.villageList = response.result;
    }, error => {
    });
  }


  ngOnInit() {
    // this.genDisorderReg = new GeneticDisorderRegistry;
    this._masterService.getDisorderType().subscribe(async response => {
      this.bloodDisorder = await response['result'];
      this.setDefultGeneticTypeId = 1;

    })

    this._masterService.getGenComplicationMast().subscribe(async response => {
      this.complicationMastList = await response.result;
    });

    this._masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];

    })

    this._masterService.getMedicineMaster().subscribe(async res => {
      this.medicineMaster = await res["result"];
    })

    this._masterService.getSurgeryMaster().subscribe(async res => {
      this.surgeryMaster = await res["result"];
    })

    this._masterService.getGenotypeDesc().subscribe(async res => {
      this.genotype = await res["result"];
    })

    this._masterService.getGenotypeBase().subscribe(async res => {
      this.genobase = await res["result"];
    })

    this._masterService.getRelationMast().subscribe(async res => {
      this.relation = await res["result"];
    })

    this._masterService.getLabMaster().subscribe(async res => {
      this.LabResults.labTest = await res.result;
    })


    // this.initializeFormGroup();

    this.genobaseDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'description',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };




  }

  ngAfterViewInit() {
    setTimeout(() => {

      if (this._alShifaLoginService.getAlShifanData()) {
        let alShifaPathentId: any;
        let alShifaEstCode: any;
        let alShifaRegNo: any;
        let alShifaValCode: any;

        this._alShifaLoginService.getAlShifanData().forEach(element => {
          if (element["regNo"]) {
            alShifaRegNo = element["regNo"]
          } else if (element["patientId"]) {
            alShifaPathentId = element["patientId"];
          } else if (element["estCode"]) {
            alShifaEstCode = element["estCode"];
          } else if (element["valCode"]) {
            alShifaValCode = element["valCode"];
          }
        });

        if (alShifaRegNo) {
          this.regId = alShifaRegNo;
          // this.search();
          this.getList(this.regId);
        } else if (alShifaEstCode && alShifaPathentId) {
          if (!alShifaValCode) {
            alShifaValCode = 0;
          }
          this.callFetchDataFromAlShifa(alShifaEstCode, alShifaPathentId, alShifaValCode);
        }

      }

      if (this._sharedService.getNavigationData()) {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        // this.search();
        this.getList(this.regId);
      }


    }, 1000);


  }
  //calls following method to change medicine list in medication component
  callGenMedList() {
    this._geneticService.getGeneticMedicineList().subscribe(res => {
      this.Medication.medicine = res["result"];
    });
  }



  private initializeFormGroup() {
    this.genDisorderForm = this.fb.group({

      rgTbPatientInfo: this.fb.group({}),
      centralRegNo: ['', Validators.compose([Validators.required])],
      registerType: ['', Validators.compose([Validators.required])],
      patientID: [null,],
      regInst: [null,],
      rgTbGenDisorderDtls: this.fb.group({
        discoderId: [''],
        discoderType: ['', Validators.compose([Validators.required])],
        genotype: [''],
        remarks: [''],
        genotypeBase: [''],
        dateDiagnosed: [''],
        lastOnsetDate: [''],
      }),

      rgTbGenGenotypeDtls: this.fb.group({
        genotype: ['', Validators.compose([Validators.required])],
      }),

      hplcForm: this.fb.group({
        runId: ['', Validators.compose([Validators.required])],
        mohTestCode: ['', Validators.compose([Validators.required])],
        orderBy: ['', Validators.compose([Validators.required])],
        orderByName: ['', Validators.compose([Validators.required])],
        releasedDate: ['', Validators.compose([Validators.required])],
        instCode: ['', Validators.compose([Validators.required])],
        labComments: ['', Validators.compose([Validators.required])],
      }),
      antigenPhenoTypingForm: this.fb.group({
        runId: ['', Validators.compose([Validators.required])],
        mohTestCode: ['', Validators.compose([Validators.required])],
        orderBy: ['', Validators.compose([Validators.required])],
        orderByName: ['', Validators.compose([Validators.required])],
        releasedDate: ['', Validators.compose([Validators.required])],
        instCode: ['', Validators.compose([Validators.required])],
        resultSummary: ['', Validators.compose([Validators.required])],
        source: ['', Validators.compose([Validators.required])],

      }),
      antibodyInvestigationForm: this.fb.group({
        runId: ['', Validators.compose([Validators.required])],
        mohTestCode: ['', Validators.compose([Validators.required])],
        orderBy: ['', Validators.compose([Validators.required])],
        orderByName: ['', Validators.compose([Validators.required])],
        releasedDate: ['', Validators.compose([Validators.required])],
        instCode: ['', Validators.compose([Validators.required])],
        resultSummary: ['', Validators.compose([Validators.required])],
        source: ['', Validators.compose([Validators.required])],

      }),

      rgTbBldTransDtls: this.fb.group({
        runId: ['', Validators.compose([Validators.required])],
        transfusedDate: ['', Validators.compose([Validators.required])],
        transfusedInst: ['', Validators.compose([Validators.required])],
      }),
      bloodgroupData: this.fb.group({
        runId: ['', Validators.compose([Validators.required])],
        paramDesc: ['', Validators.compose([Validators.required])],
        paramValue: ['', Validators.compose([Validators.required])],
        paramid: ['', Validators.compose([Validators.required])],
        entryDate: [''],
        entryBy: [''],
      }),

      rgTbLabTests: this.fb.group({

        instCode: ['', Validators.compose([Validators.required])],
        testDate: ['', Validators.compose([Validators.required])],
        resultSummary: ['', Validators.compose([Validators.required])],
        mohTestCode: ['', Validators.compose([Validators.required])],
        runId: ['', Validators.compose([Validators.required])],

      }),

      rgTbFamilyHistory: this.fb.array([]),
      diagnosticForm: this.fb.array([]),


    })

    this.genHplcHisForm = this.fb.group({
      hplcHistorycForm: this.fb.array([]),
    })
    let a = AppCompUtils.HLPC_TEST;
    for (let i = 0; i < a.length; i++) {
      this.AddDiagnostic('', a[i].id, a[i].value, '', '', '', '', '', '', '', '');
    }
    //
  }

  //////////////////////////////////////////////
  AddDiagnostic(runId: any, mohTestCode: any, testname: any, testRunRunId: any, valueNumeric: any, valueQualitative: any,
    resultRange: any, unitDesc: any, lowerVal: any, upperVal: any, parentMohTestCode: any) {
    this.diagnosticForm = this.genDisorderForm.get('diagnosticForm') as FormArray;

    this.diagnosticData = Object.assign([], this.diagnosticForm.value);
    const diagnosticDataItem: any = this.createDiagnosticItem(runId, mohTestCode, testname, testRunRunId, valueNumeric, valueQualitative
      , resultRange, unitDesc, lowerVal, upperVal, parentMohTestCode);
    this.diagnosticForm.push(this.createDiagnosticGrpItem(diagnosticDataItem));
    this.diagnosticData.push(diagnosticDataItem);
  }


  createDiagnosticGrpItem(diagnosticDataItem: any): FormGroup {
    return this.fb.group(diagnosticDataItem);
  }

  createDiagnosticItem(runId: any = null, mohTestCode: any = null, testname: any = null, testRunRunId: any = null, valueNumeric: any = null
    , valueQualitative: any = null, resultRange: any = null, unitDesc: any = null, lowerVal: any = null, upperVal: any = null, parentMohTestCod: any = null) {
    return {
      runId: runId,
      mohTestCode: mohTestCode,
      testname: testname,
      testRunRunId: testRunRunId,
      valueNumeric: valueNumeric,
      valueQualitative: valueQualitative,
      resultRange: resultRange,
      unitDesc: unitDesc,
      lowerVal: lowerVal,
      upperVal: upperVal,

    };
  }




  //////////////////////////////////////////////////////
  //rgTbGenDisorderDtls
  get v() { return this.diagnosticForm.controls; }
  get f() { return this.genDisorderForm.controls; }
  get f_disorderDtls() {
    const frmGrp = (<FormGroup>this.genDisorderForm.controls.rgTbGenDisorderDtls);

    return frmGrp.controls;
  }

  get f_GenGenotypeDtls() {
    const frmGrp = (<FormGroup>this.genDisorderForm.controls.rgTbGenGenotypeDtls);
    return frmGrp.controls;
  }


  ///////////////////P DATA TABLE
  onRowEditInitFH(row: any) {
    row.isEditable = true;
    //this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss"); 
  }

  onRowEditSaveFH(row: any) {

    let rowIndex = this.famHistory.indexOf(row);
    this.famHistory[rowIndex] = this.genDisorderForm.value.rgTbFamilyHistory[rowIndex];
    let data = this.famHistory[rowIndex];
    data.instName = this.institutes.filter(s => s.estCode == data.instID).map(s => s.estName);
    data.relationName = this.relation.filter(s => s.relationCode == data.relation).map(s => s.relationName)[0];
    //data.surgeryDt = moment(data.surgeryDt, "DD-MM-YYYY").format(); 
    data.isEditable = false;


  }

  onAddNewFH() {
    this.addFamilyIstory('', '', '', '', '', 'W', false);
    this.famHistory[this.famHistory.length - 1].isEditable = true;          //editable last entering row

  }

  addFamilyIstory(runId: any, name: any, relation: any, patientId: any, instId: any, source: any, isEditable: any = false): void {
    this.rgTbFamilyHistory = this.genDisorderForm.get('rgTbFamilyHistory') as FormArray;

    this.famHistory = Object.assign([], this.rgTbFamilyHistory.value);
    const familyHistItem: any = this.createFHItem(runId, name, relation, patientId, instId, source, isEditable);
    this.rgTbFamilyHistory.push(this.createFHGrpItem(familyHistItem));

    this.famHistory.push(familyHistItem);
    //this.famHistory[this.famHistory.length - 1].isEditable = true;
  }

  createFHGrpItem(familyHistItem: any): FormGroup {
    return this.fb.group(familyHistItem);
  }

  createFHItem(runId: any = null, name: any = null, relation: any = null, patientId: any = null, instId: any = null, source: any = null, isEditable: any = false) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      patientID: patientId,
      instID: instId,
      source: source,
      isEditable: isEditable
    };
  }

  getRelationName(relation) {
    if (relation) {
      return this.relation.filter(s => s.relationCode == relation).map(s => s.relationName)[0];
    }
  }

  getInstName(instID) {
    if (instID) {
      return this.institutes.filter(s => s.estCode == instID).map(s => s.estName)[0];
    }
  }

  addFieldValue() {
    this.fieldArray.push(this.newAttribute)
    this.newAttribute = {};
  }

  deleteFieldValue(index) {
    this.fieldArray.splice(index, 1);
  }

  // getPatientDetails(civilId, patientId, estcode) {
  //   if (civilId != null) {
  //     let mpiUser = {};
  //     this.loginService.getMpiDetails(civilId).subscribe(response => {

  //       mpiUser = response["result"];
  //       this.patientDetails.patientForm.patchValue({
  //         civilId: civilId,
  //         patientId: patientId,
  //         regInst: estcode,
  //         /// centralRegNo: 235,
  //         firstName: mpiUser["firstNameEn"],
  //         secondName: mpiUser["secondNameEn"],
  //         thirdName: mpiUser["thirdNameEn"],
  //         tribe: mpiUser["sixthNameEn"],
  //         dob: moment(mpiUser["birthDate"]).format("DD-MM-YYYY"),
  //         sex: mpiUser["sex"] === "Male" ? "M" : "F",
  //         age: mpiUser["age"],
  //         nationality: mpiUser["countryID"],
  //         maritalStatus: mpiUser["maritalStatus"] === "Married" ? "M" : (mpiUser["maritalStatus"] === "Single") ? "S" : (mpiUser["maritalStatus"] === "Divorced") ? "D" : (mpiUser["maritalStatus"] === "Widow/Widower") ? "W" : "O",
  //         mobileNo: mpiUser["mobileNo"]

  //       })
  //     });
  //   }

  // }

  /* public onClickKey($event: any) {
     if ($event.keyCode == 13) {
       this.civilId = $event.target.value;
 
       this.getPatientDetails(this.civilId);
     }
     else {
       this.civilIdInvalid = false;
     }
   }*/

  search() {

    if (this.regId) {
      this.clear();
      this.getList(this.regId);
      this.regId = "";


      //this.LabResults.getlabTest(this.patientDetails.patientForm.value['regId']);
    } else {
      this.regId = "";
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
      /*
      swal({
        text: 'Please enter Registration ID',
        type: 'warning',
        confirmButtonText: 'Ok',
        confirmButtonColor: '#3085d6'
      });*/
    }
  }

  //   click(){
  // this.Vaccination.vaccinationForm.patchValue({civilId: '105866112'})
  // this.diagnosticForm.rgTbPatientInfo=this.Vaccination.rgTbVaccinationInfo.civilId

  //   }
  get diagnosticFormArray() {
    return this.genDisorderForm.controls["diagnosticForm"] as FormArray;
  }

  get hplcHistoryFormArray() {
    return this.genHplcHisForm.controls["hplcHistorycForm"] as FormArray;
  }

  get rgTbFamilyHistoryArray() {
    return this.genDisorderForm.controls["rgTbFamilyHistory"] as FormArray;
  }



  getList(regNo: any) {

    this.downloadFromShifa = false;
    this._geneticService.getGeneticRegister(regNo).subscribe(response => {
      if (response.code == "S0000") {
        //this.DisabledButton = true;

        // from registery         response['result']
        this.formData = response['result'];
        this.patientDetails.setPatientDetails(response.result);
        this.currentCivilId = response.result.rgTbPatientInfo['civilId'];
        this.patientId = response.result['patientID'];
        this.estCode = response.result['regInst'];

        ///
        if (response['result'].rgTbDeathDetails != null) {
          this.alive = false;
        } else {
          this.alive = true;
        }


        let rgTbGenDisorderDtls: any = null;
        let rgTbBldTransDtls: any = null;
        let rgTbLabTests: any = null;
        let hplcForm: any = null;
        let antigenPhenoTypingForm: any = null;
        let antibodyInvestigationForm: any = null;
        let diagnosticForm: any = null;
        /////////////// Form group rgTbGenDisorderDtls
        let data: any[] = response.result.rgTbGenDisorderDtls;
        this.rgTbGenDisorderDtlsDB = response.result.rgTbGenDisorderDtls;
        if (data != null && data != undefined && data.length > 0) {
          rgTbGenDisorderDtls = data[0];
        }
        if (rgTbGenDisorderDtls.finalYn == 'Y') {
          this.DisabledButton = true;
        }

        // hplcForm 
        data = response.result.rgTbLabTests.filter(s => s.mohTestCode == this.hplcMohTestCode);
        if (data != null && data != undefined && data.length > 0) {
          hplcForm = data[0];

          let fbHplcForm: FormGroup = <FormGroup>this.genDisorderForm.controls["hplcForm"];
          fbHplcForm.patchValue({
            runId: hplcForm.runId,
            mohTestCode: hplcForm.mohTestCode,
            orderBy: hplcForm.orderBy,
            orderByName: hplcForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(hplcForm.releasedDate),
            instCode: hplcForm.instCode,
            labComments: hplcForm.labComments,
          })
        }


        // antigenPhenoTypingForm 
        data = response.result.rgTbLabTests.filter(s => s.mohTestCode == this.antigenPhenoTypingMohTestCode);
        if (data != null && data != undefined && data.length > 0) {
          antigenPhenoTypingForm = data[0];

          let fbAntigenPhenoTypingForm: FormGroup = <FormGroup>this.genDisorderForm.controls["antigenPhenoTypingForm"];
          fbAntigenPhenoTypingForm.patchValue({
            runId: antigenPhenoTypingForm.runId,
            mohTestCode: antigenPhenoTypingForm.mohTestCode,
            orderBy: antigenPhenoTypingForm.orderBy,
            orderByName: antigenPhenoTypingForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(antigenPhenoTypingForm.releasedDate),
            instCode: antigenPhenoTypingForm.instCode,
            resultSummary: antigenPhenoTypingForm.resultSummary,
            source: antigenPhenoTypingForm.source
          })
          if (antigenPhenoTypingForm.source == 'S') {
            this.isAntigenPhenoTypingFormEditable = true;
          } else {
            this.isAntigenPhenoTypingFormEditable = false;
          }
        }

        //antibodyInvestigationForm
        data = response.result.rgTbLabTests.filter(s => s.mohTestCode == this.antibodyInvestigationMohTestCode);
        if (data != null && data != undefined && data.length > 0) {
          antibodyInvestigationForm = data[0];

          let fbantibodyInvestigationForm: FormGroup = <FormGroup>this.genDisorderForm.controls["antibodyInvestigationForm"];
          fbantibodyInvestigationForm.patchValue({
            runId: antibodyInvestigationForm.runId,
            mohTestCode: antibodyInvestigationForm.mohTestCode,
            orderBy: antibodyInvestigationForm.orderBy,
            orderByName: antibodyInvestigationForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(antibodyInvestigationForm.releasedDate),
            instCode: antibodyInvestigationForm.instCode,
            resultSummary: antibodyInvestigationForm.resultSummary,
            source: antibodyInvestigationForm.source
          })
          if (antibodyInvestigationForm.source == 'S') {
            this.isAntibodyInvestigationEditable = true;
          } else {
            this.isAntibodyInvestigationEditable = false;
          }
        }




        /////////////////////////////////////////////
        let a = AppCompUtils.HLPC_TEST;
        //data =response.result.rgTbTestResultsComp;
        data = [];
        for (let row of response.result.rgTbTestResultsComp) {
          for (let n of a) {
            if (n.id == row.mohTestCode) {
              data.push(row);

            }
          }
        }

        this.diagnosticData.forEach(element => {
          this.diagnosticFormArray.removeAt(0);
        });

        for (let row of data) {
          let testName = AppCompUtils.HLPC_TEST.filter(s => s.id == row.mohTestCode).map(s => s.value);
          this.AddDiagnostic(row.runId, row.mohTestCode, testName, row.testRunRunId, row.valueNumeric, row.valueQualitative
            , row.resultRange, row.unitDesc, row.lowerVal, row.upperVal, row.parentMohTestCode)
        }
        /////////////////////////////////////////////

        /////////////From Group rgTbGenGenotypeDtls
        this.rgTbGenGenotypeDtlsDB = response.result.rgTbGenGenotypeDtls;
        let fgGenGenotypeDtls: FormGroup = <FormGroup>this.genDisorderForm.controls["rgTbGenGenotypeDtls"];
        let tmp = [];
        this.rgTbGenGenotypeDtlsDB.forEach(element => {
          let des = this.genobase.filter(s => s.id == element.genotypeId).map(s => s.description)[0];
          tmp.push({ id: element.genotypeId, description: des });
        });
        fgGenGenotypeDtls.patchValue({
          genotype: tmp
        })
        this.genobaseDropdownSettings = {
          singleSelection: false,
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 5,
          allowSearchFilter: true
        };




        let fgDisorderDtls: FormGroup = <FormGroup>this.genDisorderForm.controls["rgTbGenDisorderDtls"];
        ///   fgDisorderDtls.patchValue(rgTbGenDisorderDtls);

        fgDisorderDtls.patchValue({
          discoderId: rgTbGenDisorderDtls.discoderId,
          genotype: rgTbGenDisorderDtls.genotype,
          dateDiagnosed: this._sharedService.setDateFormat(rgTbGenDisorderDtls.dateDiagnosed),
          lastOnsetDate: this._sharedService.setDateFormat(rgTbGenDisorderDtls.lastOnsetDate),
          discoderType: rgTbGenDisorderDtls.discoderType,
          genotypeBase: rgTbGenDisorderDtls.genotypeBase,
          remarks: rgTbGenDisorderDtls.remarks,
        });

        /////////////// Form group rgTbBldTransDtls

        data = response.result.rgTbBldTransDtls;
        if (data != null && data != undefined && data.length > 0) {
          rgTbBldTransDtls = data[0];

          let fgBldTransDtls: FormGroup = <FormGroup>this.genDisorderForm.controls["rgTbBldTransDtls"];
          fgBldTransDtls.patchValue({
            runId: rgTbBldTransDtls.runId,
            transfusedInst: rgTbBldTransDtls.transfusedInst,
            //  releasedDate: rgTbBldTransDtls.transfusedDate,
            transfusedDate: this._sharedService.setDateFormat(rgTbBldTransDtls.transfusedDate),
          });
        }

        ////////////// Form group bloodgroupData
        let bloodGroup: any = null;
        data = response.result.rgTbVitalSigns.filter(s => s.paramid == AppParams.VITAL_BLOOD_GROUP);
        if (data != null && data != undefined && data.length > 0) {
          bloodGroup = data[0];

          let fgbloodgroupData: FormGroup = <FormGroup>this.genDisorderForm.controls["bloodgroupData"];
          fgbloodgroupData.patchValue({
            runId: bloodGroup.runId,
            paramid: bloodGroup.paramid,
            // paramValue: bloodGroup.paramValue,
            paramValue: bloodGroup.paramValue.toString(),
            paramDesc: bloodGroup.paramDesc,
            entryDate: bloodGroup.entryDate,
            entryBy: bloodGroup.entryBy,
          });
        }




        /////////////// Form group rgTbLabTests
        data = response.result.rgTbLabTests;
        if (data != null && data != undefined && data.length > 0) {
          rgTbLabTests = data[0];

          let fgLabTests: FormGroup = <FormGroup>this.genDisorderForm.controls["rgTbLabTests"];
          fgLabTests.patchValue({
            runId: rgTbLabTests.runId,
            orderByName: rgTbLabTests.orderByName,
            releasedDate: this._sharedService.setDateFormat(rgTbLabTests.releasedDate),
            instCode: rgTbLabTests.instCode,
            resultSummary: rgTbLabTests.resultSummary,
          });

        }



        /////////////// Form group rgTbFamilyHistory
        let rgTbFamilyHistory: any = response.result.rgTbFamilyHistory;
        let faFamilyHistory: FormArray = <FormArray>this.genDisorderForm.controls["rgTbFamilyHistory"];


        /* this.famHistory.forEach(element => {
           this.rgTbFamilyHistoryArray.removeAt(0)
         });
 */
        for (let famHist of rgTbFamilyHistory) {
          this.addFamilyIstory(famHist.runId, famHist.name, famHist.relation, famHist.patientID, famHist.instID, famHist.source, false);
        }

        /////////////// Form group complication
        const rgTbGenComplication: any = response.result.rgTbGenComplication;
        for (let compList of rgTbGenComplication) {
          //runid, compId, remarks,enteredBy,enteredDt, isEditable, frequency , onsetDt
          this.complication.addNewCompl(compList.runid, compList.compId, compList.remarks, compList.enteredBy, compList.enteredDt, false, compList.frequency, this._sharedService.setDateFormat(compList.onsetDt));

        }


        /////////////// Form group clinicalDetails
        const rgTbDiagnosis: any = response.result.rgTbDiagnosis;
        for (let diagList of rgTbDiagnosis) {
          this.clinicalDetails.addNewIcd(diagList.runId, diagList.icd, diagList.icdFlag, diagList.enteredBy, this._sharedService.setDateFormat(diagList.entryDate), diagList.remarks, diagList.source, false);
        }


        /////////////// Form group Medication
        const rgTbMedicineDtls: any = response.result.rgTbMedicineDtls;
        for (let medList of rgTbMedicineDtls) {
          this.Medication.addNewMed(medList.runId, medList.enteredBy, medList.enteredDt, medList.medicineID, this._sharedService.setDateFormat(medList.startDate), medList.dose, medList.frequency, medList.medicineType, medList.source, false);
        }

        /////////////// Form group surgery
        const rgTbSurgeryDtlsDB: any = response.result.rgTbSurgeryDtls;
        for (let surList of rgTbSurgeryDtlsDB) {
          this.surgery.addNewSurgery(surList.runId, surList.surgeryID, this._sharedService.setDateFormat(surList.surgeryDt), surList.remarks, surList.enteredBy, surList.enteredDt, surList.source, false);
        }


        /////////////// Form group LabResults
        //data = response.result.rgTbLabTests.filter(s => s.mohTestCode != 10070); 
        const rgTbLabTestsDB: any = response.result.rgTbLabTests.filter(s => s.mohTestCode != this.hplcMohTestCode && s.mohTestCode != this.antigenPhenoTypingMohTestCode && s.mohTestCode != this.antibodyInvestigationMohTestCode);
        for (let labRList of rgTbLabTestsDB) {
          this.LabResults.addNewLabResult(labRList.runId, this._sharedService.setDateFormat(labRList.testDate), labRList.mohTestCode, labRList.resultSummary, labRList.instCode, labRList.enteredBy, labRList.enteredDate, labRList.source, false);
        }


        /////////////// Form group VaccinationInfo
        const rgTbVaccinationInfoDb: any = response.result.rgTbVaccinationInfo;
        for (let vaccinaList of rgTbVaccinationInfoDb) {
          this.Vaccination.addNewVaccine(vaccinaList.runId, vaccinaList.enteredBy, this._sharedService.setDateFormat(vaccinaList.vaccinationDate), vaccinaList.vaccineCode, vaccinaList.vaccinatedInst, vaccinaList.remarks, vaccinaList.civilId, vaccinaList.source, false)
        }


        /////////// death details result

        let rgTbDeathDetails: any = response.result.rgTbDeathDetails;

        if (rgTbDeathDetails != null && rgTbDeathDetails != undefined) {
          this.alive = false;
          setTimeout(function () {
            this.deathDetails.setDeathDetailsResult(rgTbDeathDetails);
          }.bind(this), 3000);

        }



        //get visit class count from nehr
        this._masterService.getNehrVisitClassCount(this.currentCivilId).subscribe(res => {
          this.nehrVisitClassCount = res["result"];
          this.showVisitClassCount(res["result"]);
        })

      //get new hplc
      
      this.refreshHPLC =true;
        //this.FetchAllHplcFromAlShifa( this.estCode, this.patientId );
  
  

      } else if (response.code == "F0000") {
        Swal.fire('Warning', 'No Record Found with Entered ', 'warning')

      }
      else {
        Swal.fire('', response["message"], 'error')
      }

    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while retrieving user details </br>', 'error')
    })
  }

  showVisitClassCount(list: any) {
    // outpatient     emergency     inpatient

    if (list.filter(s => s.patientClass == "outpatient").length > 0) {
      this.opdClassCount = list.filter(s => s.patientClass == "outpatient").map(s => s.count)[0];
    }
    if (list.filter(s => s.patientClass == "inpatient").length > 0) {
      this.ipdClassCount = list.filter(s => s.patientClass == "inpatient").map(s => s.count)[0];
    }

    if (list.filter(s => s.patientClass == "emergency").length > 0) {
      this.emrClassCount = list.filter(s => s.patientClass == "emergency").map(s => s.count)[0];
    }



  }

  callFetchDataFromAlShifa(estcode: any, patientId: any, valCode: any) {
    //call check se  estCode :any,patientId : any,regType:any    this.patientDetails.patientForm.controls["Institute"].value

    if (estcode == 0 && patientId == 0) {
      estcode = this.patientDetails.patientForm.controls["regInst"].value; //20068
      patientId = this.patientDetails.patientForm.controls["patientId"].value;

      if (estcode == "" || patientId == "" || estcode == null || patientId == null) {
        this.clear();
        Swal.fire({
          icon: 'warning',
          title: 'PatientID and Institute should be selected'
        });
        return false;
      }


    }


    this._geneticService.checkGeneticRegister(estcode, patientId, AppUtils.REG_TYPE_GENETIC_BLOOD).subscribe(res => {
      this.clear();
      if (res['code'] == "0") {
        this.getList(res['result']);
      } else if (res['code'] == "3" || res['code'] == "5") {
        this.FetchDataFromAlShifa(estcode, patientId, valCode);
      } else {
        Swal.fire('Error!', res['message'], 'error')
      }

    }, error => {
      if (error.status == 401)

        Swal.fire('Error!', 'Error occured while retrieving the  details', 'error')
    });




  }



  // Start Fetch from Al Shifa
  FetchDataFromAlShifa(estcode: any, patientId: any, valCode: any) {
    this.spinner.show();
    this._geneticService.fetchGeneticRegisterFromShifa(estcode, patientId).subscribe(res => {

      this.spinner.hide();
      if (res['code'] == "S0000") {
        this.downloadFromShifa = false;
        let mpiUser = {};
        mpiUser = res["result"].personInfoDto;
        valCode = res["result"].vwGenDisorderClinicalDtls[0].valCode;



        if (mpiUser != null) {

          this.patientDetails.patientForm.patchValue({
            civilId: mpiUser["civilId"],
            patientId: patientId,
            regInst: Number(estcode), 
            firstName: mpiUser["firstNameEn"],
            secondName: mpiUser["secondNameEn"],
            thirdName: mpiUser["thirdNameEn"],
            tribe: mpiUser["sixthNameEn"],
            // dob: moment(mpiUser["birthDate"]).format("DD-MM-YYYY"),
            dob: new Date(mpiUser["birthDate"]),
            sex: mpiUser["sex"] === "Male" ? "M" : "F",
            age: mpiUser["age"],
            nationality: mpiUser["countryID"],
            maritalStatus: mpiUser["maritalStatus"] === "Married" ? "M" : (mpiUser["maritalStatus"] === "Single") ? "S" : (mpiUser["maritalStatus"] === "Divorced") ? "D" : (mpiUser["maritalStatus"] === "Widow/Widower") ? "W" : "O",
            mobileNo: mpiUser["mobileNo"],
             village: valCode,
          })
        }

        //set Governorate, Wilayat, Town/Village if valCode is fetching from Al Shifa
        let valObj
        if (valCode != 0) {
          let wal;
          let reg;
          let val: number;
          val = Number(valCode);
         // val = this.villageList.filter(s => s.vilCode == valCode).map(s => s.valCode)[0];
          wal = this.villageList.filter(s => s.vilCode == valCode).map(s => s.walCode)[0];
          reg = this.wallayatList.filter(s => s.walCode == wal).map(s => s.regCode)[0];
          valObj = { "valCode": val, "walCode": wal, "regCode": reg };
          this.patientDetails.changeRegion(valObj);
          this.patientDetails.changeWalayat(valObj);
          this.patientDetails.patientForm.patchValue({ 'village': val });

        }


        this.setDefultGeneticTypeId = 1;

        this.disclinicalInfoalshifa = res["result"].vwGenDisorderClinicalDtls;
        this.clinicalDetails.getDataFromAlshifa(this.disclinicalInfoalshifa);

        this.dismedicineInfoalshifa = res["result"].vwGenDisorderMedicineDtls;
        this.Medication.getDataFromAlshifa(this.dismedicineInfoalshifa);

        this.disSurgeryInfoalshifa = res["result"].vwGenDisorderSurgeryDtls;
        this.surgery.getDataFromAlshifa(this.disSurgeryInfoalshifa);

        this.disVaccineInfoalshifa = res["result"].vwGenDisorderVaccineDtls;
        this.Vaccination.getDataFromAlshifa(this.disVaccineInfoalshifa);

        this.disLabFollowUpInfoalshifa = res["result"].vwGenDisorderLabFollowUp;

        this.LabResults.getDataFromAlshifa(this.disLabFollowUpInfoalshifa.filter(s => s.mohTestCode != this.bloodGroupMohTestCode && s.mohTestCode != this.hplcMohTestCode && s.mohTestCode != this.antigenPhenoTypingMohTestCode && s.mohTestCode != this.antibodyInvestigationMohTestCode));

        /// --------------- start map blood group -----------------------
        let bloodgourp = res["result"].vwGenDisorderLabFollowUp.filter(s => s.mohTestCode == this.bloodGroupMohTestCode).map(s => s.resultSummary)[0];
        let releasedDt = res["result"].vwGenDisorderLabFollowUp.filter(s => s.mohTestCode == this.bloodGroupMohTestCode).map(s => s.releasedDt)[0];
        let releasedBy = res["result"].vwGenDisorderLabFollowUp.filter(s => s.mohTestCode == this.bloodGroupMohTestCode).map(s => s.releasedBy)[0];
        if (releasedDt == null) {
          releasedDt = this.currentDate;
        }
        if (releasedBy == null) {
          releasedBy = this.loginId;

        }
        let getbloodMapId = this.mapBloodGroupList.filter(s => s.value == bloodgourp).map(s => s.id)[0];
        let bloodGroup: any = null;
        bloodGroup = this.bloodGroupList.filter(s => s.id == getbloodMapId)[0];
        let fgbloodgroupData: FormGroup = <FormGroup>this.genDisorderForm.controls["bloodgroupData"];
        if (bloodGroup != null) {
          fgbloodgroupData.patchValue({
            runId: null,
            paramid: AppParams.VITAL_BLOOD_GROUP,
            paramValue: bloodGroup.id,
            paramDesc: bloodGroup.value,
            entryDate: releasedDt,
            entryBy: releasedBy,
          });
        }

        /// --------------- end map blood group -----------------------




        /// --------------- start antigenPhenoTypingForm -----------------------
        let antigenPhenoTypingForm: any = null;

        let antigenshifadata = this.disLabFollowUpInfoalshifa.filter(s => s.mohTestCode == this.antigenPhenoTypingMohTestCode);
        if (antigenshifadata != null && antigenshifadata != undefined && antigenshifadata.length > 0) {
          antigenPhenoTypingForm = antigenshifadata[0];
        }
        let fbantigenPhenoTypingForm: FormGroup = <FormGroup>this.genDisorderForm.controls["antigenPhenoTypingForm"];
        if (antigenPhenoTypingForm != null) {
          // if there is record in al shifa
          fbantigenPhenoTypingForm.patchValue({
            runId: null,
            mohTestCode: antigenPhenoTypingForm.mohTestCode,
            orderBy: antigenPhenoTypingForm.orderedBy,
            orderByName: antigenPhenoTypingForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(antigenPhenoTypingForm.releasedDate),
            instCode: estcode, //antigenPhenoTypingForm.instCode,
            resultSummary: antigenPhenoTypingForm.resultSummary,
            source: 'S'
          })
          this.isAntigenPhenoTypingFormEditable = true;
        } else {
          // if there is no record in al shifa
          fbantigenPhenoTypingForm.patchValue({
            runId: null,
            mohTestCode: this.antigenPhenoTypingMohTestCode,
            orderBy: this.loginId,
            orderByName: this.loginName,
            releasedDate: this._sharedService.setDateFormat(this.currentDate),
            instCode: estcode,
            resultSummary: null,
            source: 'W'
          })
          this.isAntigenPhenoTypingFormEditable = false;
        }
        /// --------------- End antigenPhenoTypingForm -----------------------

        /// --------------- start antibodyInvestigationForm -----------------------
        let antibodyInvestigationForm: any = null;
        let antibodyInvestigationshifadata = this.disLabFollowUpInfoalshifa.filter(s => s.mohTestCode == this.antibodyInvestigationMohTestCode);
        if (antibodyInvestigationshifadata != null && antibodyInvestigationshifadata != undefined && antibodyInvestigationshifadata.length > 0) {
          antibodyInvestigationForm = antibodyInvestigationshifadata[0];
        }
        let fbantibodyInvestigationForm: FormGroup = <FormGroup>this.genDisorderForm.controls["antibodyInvestigationForm"];
        if (antibodyInvestigationForm != null) {
          // if there is record in al shifa
          fbantibodyInvestigationForm.patchValue({
            runId: null, //antibodyInvestigationForm.runId,
            mohTestCode: antibodyInvestigationForm.mohTestCode,
            orderBy: antibodyInvestigationForm.orderedBy,
            orderByName: antibodyInvestigationForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(antibodyInvestigationForm.releasedDate),
            instCode: estcode, //antibodyInvestigationForm.instCode,
            resultSummary: antibodyInvestigationForm.resultSummary,
            source: 'S'
          })
          this.isAntibodyInvestigationEditable = true;
        } else {
          // if there is no record in al shifa
          fbantibodyInvestigationForm.patchValue({
            runId: null,
            mohTestCode: this.antigenPhenoTypingMohTestCode,
            orderBy: this.loginId,
            orderByName: this.loginName,
            releasedDate: this._sharedService.setDateFormat(this.currentDate),
            instCode: estcode,
            resultSummary: null,
            source: 'W'
          })
          this.isAntibodyInvestigationEditable = false;
        }
        /// --------------- start antibodyInvestigationForm -----------------------



        //hplcHistoryHeader
        //---------------- start HPLC history ----------------------


        if (res["result"].vwGenDisorderHplcDtls != null) {
          this.disHplHistorylshifa = res["result"].vwGenDisorderHplcDtls;
          this.hplcHistoryHeader = [];

          this.setHplc = [{ id: 18, description: 'HPLC' }]; // use to set HPLC on "diagnosis based"          

          // this.disHplHistorylshifa.forEach(elm => {
          //   this.hplcHistoryHeader.push({ invstid: elm.invstid, releasedDt: elm.releasedDt });
          // })

          this.disHplHistorylshifa.forEach(elm => {
            if (this.hplcHistoryHeader.filter(s => s.invstid == elm.invstid).length == 0) {
              this.hplcHistoryHeader.push({ invstid: elm.invstid, releasedDt: this._sharedService.setDateFormat2(elm.releasedDt) });
            }
          })

        }



        //---------------- end HPLC history ----------------------

        //hplcForm



        //vwGenDisorderLabDtls  
        this.disLabInfoalshifa = res["result"].vwGenDisorderLabDtls;
        let hplcMastList = AppCompUtils.HLPC_TEST;
        let data = [];
        for (let row of this.disLabInfoalshifa) {
          for (let n of hplcMastList) {
            if (n.id == row.resMohTestCode) {
              data.push(row);
            }
          }
        }

        for (let row of data) {
          let testName = AppCompUtils.HLPC_TEST.filter(s => s.id == row.resMohTestCode).map(s => s.value);
          this.AddDiagnostic(null, row.resMohTestCode, testName, null, row.valueNumeric, row.valueQualitative
            , row.resultRange, row.unitDwsc, row.lowerVal, row.upperVal, this.hplcMohTestCode)
        }

        hplcMastList.forEach(elm => {
          if (this.diagnosticData.filter(s => s.mohTestCode == elm.id).length == 0) {
            this.AddDiagnostic('', elm.id, elm.value, '', '', '', '', '', '', '', '');
          }

        })

        /// --------------- start hplcForm -----------------------
        let hplcForm: any = null;
        let shifadata = this.disLabInfoalshifa.filter(s => s.mohTestCode == this.hplcMohTestCode);
        if (shifadata != null && shifadata != undefined && shifadata.length > 0) {
          hplcForm = shifadata[0];
        }


        if (hplcForm != null) {
          let fbHplcForm: FormGroup = <FormGroup>this.genDisorderForm.controls["hplcForm"];
          fbHplcForm.patchValue({
            runId: null,
            mohTestCode: hplcForm.mohTestCode,
            orderBy: hplcForm.orderedBy,
            orderByName: hplcForm.orderByName,
            releasedDate: this._sharedService.setDateFormat(hplcForm.releasedDt), //hplcForm.releasedDt,
            instCode: estcode,
            labComments: hplcForm.labComments,
          })
        }
        /// --------------- End hplcForm -----------------------




        //get visit class count from nehr
        this._masterService.getNehrVisitClassCount(mpiUser["civilId"]).subscribe(res => {
          this.nehrVisitClassCount = res["result"];
          this.showVisitClassCount(res["result"]);
        })

      } else {

        Swal.fire('Error!', res['message'], 'error');
      }


    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while fetching from alshifa', 'error');
    })
  }
  // End Fetch from Al Shifa

  //Start Fetch All Hplc From Al Shifa
  FetchAllHplcFromAlShifa() {
 this.refreshHPLC = false;

    this._geneticService.fetchAllHplcFromShifa(this.estCode, this.patientId).subscribe(res => {

      if (res['code'] == 'S0000') {
        if (res["result"] != null) {
          this.disHplHistorylshifa = res["result"];
          this.hplcHistoryHeader = [];
  
          this.setHplc = [{ id: 18, description: 'HPLC' }]; // use to set HPLC on "diagnosis based"          
  
          this.disHplHistorylshifa.forEach(elm => {
            if (this.hplcHistoryHeader.filter(s => s.invstid == elm.invstid).length == 0) {
              this.hplcHistoryHeader.push({ invstid: elm.invstid, releasedDt: this._sharedService.setDateFormat2(elm.releasedDt) });
            }
          })
  
        }
        this.openModal(this.hplcHistory);
        //this.hplcHistory = this.modalService.open()
      }

    });



  }
  //End Fetch All Hplc From Al Shifa
  submit() {
    if (this.genDisorderForm.status === 'VALID') {

    }
  }




  /////////////////Family Form Array////////////
  initRgTbFamilyHistory(name: any, relation: any, patientID: any, instID: any): FormGroup {
    return this.fb.group({
      name: [name],
      relation: [relation],
      patientID: [patientID],
      instID: [instID]

    });
  }

  addNewFamilyList1(name: any, relation: any, patientID: any, instID: any, formArr: any): void {
    this.rgTbFamilyHistory = formArr.get('rgTbFamilyHistory') as FormArray;
    this.rgTbFamilyHistory.push(this.initRgTbFamilyHistory(name, relation, patientID, instID));

  }


  removeRgTbFamilyHistory(i: any) {
    const control = <FormArray>(this.genDisorderForm.get('rgTbFamilyHistory'));
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbFamilyHistoryRow(control);
    }
  }
  removeAllRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.genDisorderForm.get('rgTbFamilyHistory');
    control.removeAt(i);
    control.controls = [];
  }

  addRgTbFamilyHistoryRow(formArr: any) {
    this.addNewFamilyList1(null, null, null, null, formArr);
  }


  getRgTbFamilyHistory(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbFamilyHistory.value;
  }

  ///////////////////////////////////////////////////
  addNewFamilyList() {
    if (!this.rgTbFamilyHistory) {
      this.rgTbFamilyHistory = [];
    }
    this.rgTbFamilyHistory.push({ name: '', relation: '', patientID: '', instID: '' });
    this.rgTbFamilyHistory[this.rgTbFamilyHistory.length - 1].isEditable = true;
  }

  delete(row: any) {

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbFamilyHistory = this.genDisorderForm.get('rgTbFamilyHistory') as FormArray;
        this.delRow = this.famHistory.indexOf(row);
        this.famHistory.splice(this.delRow, 1);
        this.rgTbFamilyHistory.removeAt(this.delRow);
      }
    })





  }





  edit() {

    this.patientDetails.patientForm.patchValue({
      createdBy: this.loginId,
      createdOn: this.currentDate,
      modifiedBy: this.loginId,
      modifiedOn: this.currentDate,
    });


  }

  addCivilId() {

    this.Vaccination.vaccinationForm.patchValue({
      civilId: this.civilId,
      enteredBy: this.loginId,

    });


  }

  async save(formData: any, buttonType: any) {
    this.submitted = true;


    let finalYn
    if (buttonType == 'Save') {
      finalYn = 'N';
    } else if (buttonType == 'Finalize') {
      await
        Swal.fire({
          title: 'Are you sure?',
          text: "You won't be able to revert this!",
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes, Finalize it!'
        }).then((result) => {
          if (!result.dismiss) {
            finalYn = 'Y';
          } else {
            finalYn = '0';  //set finalYn = 0 to stop the function 
            // return false;
            //  exit;

          }
        })

    }
    // use following if statment to stop the function if finalYn = 0
    if (finalYn == '0') {
      return false;
    }

    if (this.patientDetails.patientForm.invalid) {
      return false;
    }
    if (this.genDisorderForm.controls.rgTbGenDisorderDtls.invalid) {
      return false;
    }
    if (this.genDisorderForm.controls.rgTbGenGenotypeDtls.invalid) {
      return false;
    }


    // instRegDate:    this.f.instRegDate.value ? moment(this.f.instRegDate.value).format("DD-MM-yyyy") : null, // formatDate(new Date(), 'yyyy-MM-dd', 'en'); 

    //dob: this.patientDetails.f.dob.value ? moment(this.patientDetails.f.dob.value).format("DD-MM-yyyy") : null, //this.patientDetails.f.dob.value,
    formData.rgTbPatientInfo = this.patientDetails.patientForm.value;

    formData.patientID = this.patientDetails.patientForm.controls.patientId.value;
    formData.regInst = this.patientDetails.patientForm.controls.regInst.value;
    formData.rgTbPatientInfo.dob = this.patientDetails.patientForm.controls.dob.value ? moment(this.patientDetails.patientForm.controls.dob.value).format("DD-MM-yyyy") : null;

    formData.registerType = this.REG_TYPE_GENETIC_BLOOD;
    formData.centralRegNo = this.patientDetails.f.centralRegNo.value;    //ch
    if (formData.centralRegNo == null) {
      formData.createdBy = this.loginId;
      formData.createdOn = this.currentDate;
      formData.rgTbPatientInfo.createdBy = this.loginId;
      formData.rgTbPatientInfo.createdOn = this.currentDate;
      formData.instRegDate = this.currentDate;
    } else {
      formData.modifiedBy = this.loginId;
      formData.modifiedOn = this.currentDate;
      formData.createdBy = this.formData.createdBy;
      formData.createdOn = this.formData.createdOn;
      formData.rgTbPatientInfo.createdBy = this.formData.rgTbPatientInfo.createdBy;
      formData.rgTbPatientInfo.createdOn = this.formData.rgTbPatientInfo.createdOn;
      formData.instRegDate = this.formData.instRegDate;
    }




    //this.rgTbGenDisorderDtlsDB.filter(s=> s.discoderType == 00).map(s => s.discoderId);


    let rgTbGenDisorderDtls = [];
    //rgTbGenDisorderDtls = formData.rgTbGenDisorderDtls
    rgTbGenDisorderDtls.push(formData.rgTbGenDisorderDtls);

    rgTbGenDisorderDtls.forEach(ele => {
      let id;
      ele.finalYn = finalYn;
      if (this.rgTbGenDisorderDtlsDB != null && this.rgTbGenDisorderDtlsDB.filter(s => s.discoderType == ele.discoderType).length > 0) {
        //  id = this.rgTbGenDisorderDtlsDB.filter(s => s.discoderType == ele.discoderType).map(s => s.discoderId)[0];
        ele.createdOn = this.currentDate;
        ele.createdBy = this.loginId;

      } else {
        id = null;
        ele.createdOn = this.currentDate;
        ele.createdBy = this.loginId;
      }


      //  ele.discoderId = id;
    });

    //formData.rgTbGenDisorderDtls = rgTbGenDisorderDtls;




    let rgTbBldTransDtls = [];
    rgTbBldTransDtls.push(formData.rgTbBldTransDtls);
    //formData.rgTbBldTransDtls = rgTbBldTransDtls;

    /*let rgTbLabTests = [];
    rgTbLabTests.push(formData.rgTbLabTests);
    formData.rgTbLabTests = rgTbLabTests;
*/
    formData.regInst = formData.regInst;



    this.genDisorderForm.value.diagnosticForm.forEach(element => {
      element.parentMohTestCod = this.hplcMohTestCode;
    });



    formData.rgTbGenComplication = this.complication.complicationForm.value.rgTbGenComplication;
    formData.rgTbDiagnosis = this.clinicalDetails.clinicalForm.value.rgTbDiagnosis;
    formData.rgTbMedicineDtls = this.Medication.medicationForm.value.rgTbMedicineDtls;
    formData.rgTbSurgeryDtls = this.surgery.surgeryForm.value.rgTbSurgeryDtls;
    //s => s.mohTestCode != this.bloodGroupMohTestCode && 
    formData.rgTbLabTests = this.LabResults.labResultForm.value.rgTbLabTests.filter(s => s.mohTestCode != this.bloodGroupMohTestCode && s.mohTestCode != this.hplcMohTestCode && s.mohTestCode != this.antibodyInvestigationMohTestCode && s.mohTestCode != this.antigenPhenoTypingMohTestCode);

    let hplcData = this.genDisorderForm.value.hplcForm

    if (this.genDisorderForm.value.hplcForm.orderBy == null) {
      this.genDisorderForm.value.hplcForm.orderBy = this.loginId;
      this.genDisorderForm.value.hplcForm.orderByName = this.loginName;
    }
    //{runId: -65, testDate: "2021-07-04T20:00:00.000+0000", mohTestCode: 10070, resultSummary: "HPLC",  enteredBy: -1001 ,enteredDate: "2021-07-09T00:00:00.000+0000", instCode: 30184 , source: "S"}
    formData.rgTbLabTests.push({
      runId: hplcData.runId, mohTestCode: this.hplcMohTestCode,
      orderBy: hplcData.orderBy, orderByName: hplcData.orderByName, releasedDate: hplcData.releasedDate
      , instCode: hplcData.instCode, labComments: hplcData.labComments
    });

    // start push to rgTbLabTests 
    //antigenPhenoTypingForm
    let antigenPhenoTypingForm = this.genDisorderForm.value.antigenPhenoTypingForm
    formData.rgTbLabTests.push({
      runId: antigenPhenoTypingForm.runId, mohTestCode: this.antigenPhenoTypingMohTestCode,
      orderBy: antigenPhenoTypingForm.orderBy, orderByName: antigenPhenoTypingForm.orderByName, releasedDate: antigenPhenoTypingForm.releasedDate
      , instCode: antigenPhenoTypingForm.instCode, resultSummary: antigenPhenoTypingForm.resultSummary
    });


    //antibodyInvestigationForm
    let antibodyInvestigationForm = this.genDisorderForm.value.antibodyInvestigationForm
    formData.rgTbLabTests.push({
      runId: antibodyInvestigationForm.runId, mohTestCode: this.antibodyInvestigationMohTestCode,
      orderBy: antibodyInvestigationForm.orderBy, orderByName: antibodyInvestigationForm.orderByName, releasedDate: antibodyInvestigationForm.releasedDate
      , instCode: antibodyInvestigationForm.instCode, resultSummary: antibodyInvestigationForm.resultSummary
    });
    // end push to rgTbLabTests


    let genGenotypeDtlsForm = this.genDisorderForm.value.rgTbGenGenotypeDtls.genotype  //{id: 18, description: "HPLC"}
    let genGenotypeDtlsSave = [];
    genGenotypeDtlsForm.forEach(el => {
      if (this.rgTbGenGenotypeDtlsDB.filter(s => s.genotypeId == el.id).length > 0) {
        let db = this.rgTbGenGenotypeDtlsDB.filter(s => s.genotypeId == el.id)[0];
        genGenotypeDtlsSave.push({
          runId: db.runId, enteredDate: db.enteredDate, enteredBy: db.enteredBy, remarks: db.remarks, genotypeId: db.genotypeId
        })

      } else {
        genGenotypeDtlsSave.push({
          runId: null, enteredDate: this.currentDate, enteredBy: this.loginId, remarks: null, genotypeId: el.id
        })
      }

    });




    ///VaccinationInfo

    formData.rgTbVaccinationInfo = this.Vaccination.vaccinationForm.value.rgTbVaccinationInfo;

    // set bloodgroupData 
    formData.bloodgroupData.paramid = AppParams.VITAL_BLOOD_GROUP;
    formData.bloodgroupData.paramDesc = this.bloodGroupList.filter(s => s.id == formData.bloodgroupData.paramValue).map(s => s.value)[0];
    if (formData.bloodgroupData.entryDate == null || formData.bloodgroupData.entryDate == "") {
      formData.bloodgroupData.entryDate = this.currentDate;
    }
    if (formData.bloodgroupData.entryBy == null || formData.bloodgroupData.entryBy == "") {
      formData.bloodgroupData.entryBy = this.loginId;

    }

    //push blood group Data to RgTbVitalSign 
    let rgTbVitalSigns = [];
    rgTbVitalSigns.push(formData.bloodgroupData);

    //death details data
    let deathDetails = null;
    if (this.alive == false) {
      deathDetails = this.deathDetails.deatDetailsForm.value;
      if (deathDetails.civillID != null) {
        if (deathDetails.createdBy == null) {
          deathDetails.createdBy = this.loginId;
          deathDetails.createdDate = this.currentDate;
        } else {
          deathDetails.lastModifiedBy = this.loginId;
          deathDetails.lastModifiedDate = this.currentDate;
        }
      } else {
        deathDetails = null;
      }
    }



    //deathDetails.civillID = this.patientDetails.f.civilId.value;



    let saveData = {
      centralRegNo: formData.centralRegNo,
      activeYn: "Y",
      patientID: this.patientDetails.f.patientId.value,
      createdBy: formData.createdBy,
      createdOn: formData.createdOn,
      instRegDate: formData.instRegDate,
      modifiedBy: formData.modifiedBy,
      modifiedOn: formData.modifiedOn,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_GENETIC_BLOOD,
      rgTbPatientInfo: formData.rgTbPatientInfo,
      rgTbBldTransDtls: rgTbBldTransDtls, //formData.rgTbBldTransDtls,
      rgTbDiagnosis: formData.rgTbDiagnosis,
      rgTbFamilyHistory: formData.rgTbFamilyHistory,
      rgTbGenComplication: formData.rgTbGenComplication,
      rgTbGenDisorderDtls: rgTbGenDisorderDtls,
      rgTbLabTests: formData.rgTbLabTests,
      rgTbMedicineDtls: formData.rgTbMedicineDtls,
      rgTbSurgeryDtls: formData.rgTbSurgeryDtls,
      rgTbVaccinationInfo: formData.rgTbVaccinationInfo,
      rgTbTestResultsComp: this.genDisorderForm.value.diagnosticForm,
      rgTbVitalSigns: rgTbVitalSigns,
      rgTbGenGenotypeDtls: genGenotypeDtlsSave,
      rgTbDeathDetails: deathDetails
    }



    this._geneticService.saveGenetic(saveData).subscribe(res => {
      if (res['code'] == 0) {
        Swal.fire('Saved!', 'Blood Disorder successfully.', 'success');
        this.regId = res["result"];
        this.search();
      } else if (res['code'] == "3") {
        Swal.fire('Saved!', res['message'], 'error');
      } else {
        Swal.fire('Error!', res['message'], 'error');
      }

    }, err => {
      Swal.fire('Error!', 'Error occured while saving Blood Disorder ' + err.message, 'error')
    })



  }

  disableForm() {
    this.patientDetails.patientForm.disable();

    //this.complication.complicationForm.disable();
    this.genDisorderForm.disable();
    //   this.clinicalDetails.clinicalForm.disable();
    this.DisabledButton = true;
  }



  clear() {


    this.formData = null;
    this.downloadFromShifa = true;
    this.patientDetails.clear();
    this.clinicalDetails.clear();
    this.Medication.clear();
    this.surgery.clear();
    this.Vaccination.clear();
    this.LabResults.clear();
    this.complication.clear();
    this.genDisorderForm.reset();

    if (this.alive == false) {
      this.deathDetails.clear();
    }


    this.genDisorderForm.value.rgTbBldTransDtls = [];

    this.diagnosticData.forEach(element => {
      this.diagnosticFormArray.removeAt(0);
    });

    this.famHistory.forEach(element => {
      this.rgTbFamilyHistoryArray.removeAt(0)
    });
    this.famHistory = [];
    this.DisabledButton = false;

    this.opdClassCount = 0;
    this.ipdClassCount = 0;
    this.emrClassCount = 0;
    //  this.ngOnInit();


  }



  openModal(model) {
    this.modalService.open(model);
  }

  listClick(event, newValue) {
    this.genHplcHisForm.reset();
    this.hplcHistoryFormArray.value.forEach(element => {
      this.hplcHistoryFormArray.removeAt(0);
    });


    this.selectedItem = newValue;
    this.updateHplcInvstid = newValue.invstid;





    this.hplcHistoryDetails = this.disHplHistorylshifa.filter(s => s.invstid == this.updateHplcInvstid);


    let hplcMastList = AppCompUtils.HLPC_TEST;
    let data = [];
    for (let row of this.hplcHistoryDetails) {
      for (let n of hplcMastList) {
        if (n.id == row.resMohTestCode) {
          data.push(row);
        }
      }
    }


    for (let row of data) {
      let testName = AppCompUtils.HLPC_TEST.filter(s => s.id == row.resMohTestCode).map(s => s.value);
      this.DisplayhplcHist(null, row.resMohTestCode, testName, null, row.valueNumeric, row.valueQualitative
        , row.resultRange, row.unitDwsc, row.lowerVal, row.upperVal, this.hplcMohTestCode)
    }

    hplcMastList.forEach(elm => {
      if (this.hplcHistoryDetails.filter(s => s.mohTestCode == elm.id).length == 0) {
        this.DisplayhplcHist('', elm.id, elm.value, '', '', '', '', '', '', '', '');
      }
    })

  }

  DisplayhplcHist(runId: any, mohTestCode: any, testname: any, testRunRunId: any, valueNumeric: any, valueQualitative: any,
    resultRange: any, unitDesc: any, lowerVal: any, upperVal: any, parentMohTestCode: any) {
    this.hplcHistorycForm = this.genHplcHisForm.get('hplcHistorycForm') as FormArray;

    this.hplcHistoryDetails = Object.assign([], this.hplcHistorycForm.value);
    const diagnosticDataItem: any = this.createDiagnosticItem(runId, mohTestCode, testname, testRunRunId, valueNumeric, valueQualitative
      , resultRange, unitDesc, lowerVal, upperVal, parentMohTestCode);
    this.hplcHistorycForm.push(this.createDiagnosticGrpItem(diagnosticDataItem));
    this.hplcHistoryDetails.push(diagnosticDataItem);
  }


  updateDeathDetails() {
    this.alive = false;
  }

  updateHplc() {


    if (this.updateHplcInvstid != null) {
      /// --------------- start update hplcForm -----------------------
      let hplcForm: any = null;
      let estcode = this.patientDetails.patientForm.controls["regInst"].value;
      let selectedShifadata = this.disHplHistorylshifa.filter(s => s.invstid == this.updateHplcInvstid);
      if (selectedShifadata != null && selectedShifadata != undefined && selectedShifadata.length > 0) {
        hplcForm = selectedShifadata[0];
      }


      if (hplcForm != null) {
        let fbHplcForm: FormGroup = <FormGroup>this.genDisorderForm.controls["hplcForm"];
        fbHplcForm.patchValue({
          runId: null,
          mohTestCode: hplcForm.mohTestCode,
          orderBy: hplcForm.orderedBy,
          orderByName: hplcForm.orderByName,
          releasedDate: this._sharedService.setDateFormat(hplcForm.releasedDt), //hplcForm.releasedDt,
          instCode: estcode,
          labComments: hplcForm.labComments,
        })
      }
      /// --------------- End update AddDiagnostic hplcMastList -----------------------

      ////start  clear hplc data 
      // if (this.diagnosticData > 0){
      this.diagnosticData.forEach(element => {
        this.diagnosticFormArray.removeAt(0);
      });
      // }

      ////end  clear hplc data 

      let hplcMastList = AppCompUtils.HLPC_TEST;
      let data = [];
      for (let row of selectedShifadata) {
        for (let n of hplcMastList) {
          if (n.id == row.resMohTestCode) {
            data.push(row);
          }
        }
      }

      for (let row of data) {
        let testName = AppCompUtils.HLPC_TEST.filter(s => s.id == row.resMohTestCode).map(s => s.value);
        this.AddDiagnostic(null, row.resMohTestCode, testName, null, row.valueNumeric, row.valueQualitative
          , row.resultRange, row.unitDwsc, row.lowerVal, row.upperVal, this.hplcMohTestCode)
      }

      //this.diagnosticData = this.hplcHistoryDetails;
      hplcMastList.forEach(elm => {
        if (this.diagnosticData.filter(s => s.mohTestCode == elm.id).length == 0) {
          this.AddDiagnostic('', elm.id, elm.value, '', '', '', '', '', '', '', '');
        }
      })



      /// --------------- start update AddDiagnostic hplcMastList -----------------------


    }


  }



}