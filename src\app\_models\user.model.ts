import { Menues } from './menues.model';
import { WorkStatus } from './work-status.model';
import { UserSystem } from './user-system';
import { Registries } from './registries.model';
import { Institutes } from './institutes.model';
import { Person } from './person.model';
export class User {
    id: number;
    loginId: string;
    person: Person;
    passwordLastModified: Date;
    active: boolean;
    firstLogin: string;
    roles: Array<Person>;
    institutes: Array<Institutes>;
    registries: Array<Registries>;
    userSystem: Array<UserSystem>;
    workStatus: Array<WorkStatus>;
    menues: Array<Menues>;
}
