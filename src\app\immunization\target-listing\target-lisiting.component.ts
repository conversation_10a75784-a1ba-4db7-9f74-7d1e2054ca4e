import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { GridOptions, _ } from "ag-grid-community";
import { DatePipe } from '@angular/common'
import Swal from 'sweetalert2';
import { ButtonOrganDonorRendererComponent } from 'src/app/common/agGridComponents/ButtonOrganDonorRendererComponent';
import { OrganDonorExcel } from 'src/app/_models/organ-donor-excel.model';
import { SharedService } from 'src/app/_services/shared.service';
import { Paginator } from 'primeng/paginator';
import * as moment from 'moment';
import { vaccineTagetModel } from 'src/app/_models/vaccine-target-listing-model';
import * as AppUtils from '../../common/app.utils';
import { MasterService } from 'src/app/_services/master.service';
import { TargetListingService } from './target-listing.service';
import { RegionDataModel } from 'src/app/common/objectModels/region-model';
import { vaccineTbMaster } from 'src/app/_models/vaccine-tb-master-model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { VaccineService } from '../vaccine.service';
import { vaccineStockMast } from 'src/app/_models/vaccine-target-model';
import { ButtonRendererComponent } from 'src/app/common/agGridComponents/ButtonRendererComponent';
import { WallayatDataModel } from 'src/app/common/objectModels/wallayat-model';
import { data } from 'jquery';

@Component({
  selector: 'app-target-listing',
  templateUrl: './target-listing.component.html',
  styleUrls: ['./target-listing.component.scss'],

})


export class TargetListingComponent implements OnInit {

  months = [
    { code: 1, name: 'January' },
    { code: 2, name: 'February' },
    { code: 3, name: 'March' },
    { code: 4, name: 'April' },
    { code: 5, name: 'May' },
    { code: 6, name: 'June' },
    { code: 7, name: 'July' },
    { code: 8, name: 'August' },
    { code: 9, name: 'September' },
    { code: 10, name: 'October' },
    { code: 11, name: 'November' },
    { code: 12, name: 'December' }
  ];

  vaccineTbMaster: Array<vaccineTbMaster> = new Array<vaccineTbMaster>();
  regionData: RegionDataModel[];
  institeList: any[];
  institeListFilter: any[];

  @ViewChild('viewVaccineStock', { static: false }) public viewVaccineStock: TemplateRef<any>;
  @ViewChild('targetPaginator', { static: false }) paginator: Paginator;
  targetSearch: FormGroup;

  thisYear = (new Date()).getFullYear();
  startDate = new Date("1/1/" + this.thisYear);
  defaultFormattedDate = this.datepipe.transform(this.startDate, 'dd-MM-yyyy');
  totalRecords: any;
  paginationSize: any;

  targetList: vaccineTagetModel[] = [];
  vaccineStock: Array<vaccineStockMast> = new Array<vaccineStockMast>();
  public rowData: any[] = [];
  private gridApi: any;
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },


    frameworkComponents: {
      buttonRenderer: ButtonRendererComponent

    }
  };

  gridOptions1: GridOptions = <GridOptions>{
    enableColResize: true,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions1.api.sizeColumnsToFit();
    }
  }


  cellRenderer = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };

  cellRendererVaccineName = (data) => {
    if (data.value || data >= 0) {
      let vaccineName = ''
      this.vaccineList.forEach((item) => {
        if (data.value) {
          if (item.vaccineGroupId == data.value) {
            vaccineName = item.vaccineGroupName
          }
        }
        if (data >= 0) {
          if (item.vaccineGroupId == data) {
            vaccineName = item.vaccineGroupName
          }
        }

      }
      );

      return vaccineName;
    } else {
      return '';
    }
  };

  cellRendererInstName = (data) => {
    if (data.value) {
      let InstName = ''
      this.institeList.forEach((item) => {

        if (item.estCode == data.value) {
          InstName = item.estName

        }

      });
      return InstName;
    } else {
      return '';
    }
  };

  cellRendererRegionName = (data) => {
    if (data.value || data >= 0) {
      let regName = ''
      this.regionData.forEach((item) => {
        if (data.value) {
          if (item.regCode == data.value) {
            regName = item.regName
          }
        }
        if (data >= 0) {
          if (item.regCode == data) {
            regName = item.regName
          }
        }


      });
      return regName;
    } else {
      return '';
    }
  };

  cellRendererWallayatName = (data) => {
    if (data.value || data >= 0) {
      let walName = ''
      this.wallayatList.forEach((item) => {
        if (data.value) {
          if (item.walCode == data.value) {
            walName = item.walName
          }
        }
        if (data >= 0) {
          if (item.walCode == data) {
            walName = item.walName
          }
        }

      });
      return walName;
    } else {
      return '';
    }
  };

  cellRendererMonthName = (data) => {
    if (data.value || data >= 0) {
      let monthName = ''
      this.months.forEach((item) => {
        if (data.value) {
          if (item.code == data.value) {
            monthName = item.name
          }
        }
        if (data >= 0) {
          if (item.code == data) {
            monthName = item.name
          }
        }
      });
      return monthName;
    } else {
      return '';
    }
  };

  excelCriteria: any = {};
  columnDefs = [
    { headerName: 'Vaccine Name', field: 'vaccineGroupId', cellRenderer: this.cellRendererVaccineName },
    { headerName: 'Institute', field: 'estName' },
    { headerName: 'Region', field: 'regCode', cellRenderer: this.cellRendererRegionName },
    { headerName: 'Wilayat', field: 'walCode', cellRenderer: this.cellRendererWallayatName },
    { headerName: 'Month', field: 'month', cellRenderer: this.cellRendererMonthName },
    { headerName: 'Year', field: 'year' },
    {
      headerName: 'Stock Info',
      cellRenderer: 'buttonRenderer',
      sortable: true, minWidth: 150,
      cellRendererParams: {
        onClick: this.openVaccineModal.bind(this),
        label: 'Show'
      }
    },

  ];

  columnVaccineStock = [
    { headerName: 'Institute', field: 'estCode', cellRenderer: this.cellRendererInstName },
    { headerName: 'vaccineName', field: 'vaccineGroupId', cellRenderer: this.cellRendererVaccineName },
    { headerName: 'qty', field: 'qty' },
    { headerName: 'lostQty', field: 'lostQty' },
    { headerName: 'expiryDate', field: 'expiryDate', cellRenderer: this.cellRenderer },
    { headerName: 'receivedDate', field: 'receivedDate', cellRenderer: this.cellRenderer },
  ];




  data: any;
  currentPageData: any;
  runOnlyOnceInEvent: boolean = false;
  vaccineList: any;
  wallayatList: WallayatDataModel[];
  wallayatListFilter: any;
  walCodeSelected: any;
  estCodeSelected: any;
  regCodeSelected: any;

  constructor(fb: FormBuilder, private _targetListingService: TargetListingService, private _sharedService: SharedService, private _masterService: MasterService, public datepipe: DatePipe, private modalService: NgbModal, private _vaccineService: VaccineService) {
    this.targetSearch = fb.group({
      runId: new FormControl(null, Validators.required),
      estCode: new FormControl(null, Validators.required),
      estName: new FormControl(null),
      month: new FormControl(null),
      year: new FormControl(null),
      regCode: new FormControl(null),
      scheduleQty: new FormControl(null),
      targetQty: new FormControl(null),
      vaccineId: new FormControl(null, Validators.required),
      walCode: new FormControl(null, Validators.required),
      vaccineGroupId: new FormControl(null,Validators.required),
      vaccineGroupName: new FormControl(null),
    })

  };

  






  ngOnInit(): void {
    this.getMasterData();

  }







  public onGridReady(params: any) {
    this.gridApi = params.api;
  }

  getTargetList(event?: any) {

    let source = this.targetSearch.value;
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    }


    source = { pageable, ...source }




    this._targetListingService.getTargetListing(source).subscribe(data => {
      this.data = data
      this.rowData = data.result['content'];
      this.totalRecords = data.result['totalElements'];


    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occured while retrieving Organ Donor details', 'error')
    })
      ;
  }

  onSubmit() {
    this.getTargetList();

  }


  clear() {
    this.rowData = [];
    this.exportToExcel = null;
    this.excelCriteria = null;
    this.targetList = null;
    this.targetSearch.reset();

  }

  formatData(rowData) {
    rowData.forEach(el => {
      this.targetList.push({
        Vaccine: this.cellRendererVaccineName(el.vaccineGroupId),
        Institute: el.estName, Region: this.cellRendererRegionName(el.regCode),
        Wilayat: this.cellRendererWallayatName(el.walCode), Month: this.cellRendererMonthName(el.month), Year: el.year
      })
    })

    this._sharedService.exportAsExcelFile(this.targetList, "Vaccine_Target_Listing");
  }


  exportToExcel(event?: any) {
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
        this.formatData(this.rowData);
      } else {

        let source = this.targetSearch.value;

        let pageable = {
          page: event ? event.page : 0,
          size: event ? event.rows : this.totalRecords,
        }

        source = { pageable, ...source }

        this._targetListingService.getTargetListing(source).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['content'];
            if (excelData && excelData.length > 0) {
              this.formatData(excelData);
            }
          }
        });
      }
    } else {
      Swal.fire('Warning!', 'Please search first', 'warning')

    }


  }

  getMasterData() {

    this._masterService.getRegionsMasterFull();
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regionData = value;
    });

    this._masterService.getInstitutesMasterFull();
    this._masterService.institutesMasterFull.subscribe(res => {
      this.institeList = res;
      this.institeListFilter = this.institeList;
    })

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(res => {
      this.wallayatList = res;
    })

    this._vaccineService.getTbVaccineMaster().subscribe(res => {
      let data = res['result'];
      let vaccineMast = data.map(el=>{
        return { vaccineGroupId : el.vaccineGroupId , vaccineGroupName : el.vaccineGroupName }
     });

     this.vaccineList = vaccineMast.filter((value, index, array) => index == array.findIndex(item => item.vaccineGroupId == value.vaccineGroupId));
   

     

    })


  }

  getDistinctList<T>(inputList: T[]): T[] {
    return [...new Set(inputList)];
  }

  openVaccineModal(e) {
    this.modalService.open(this.viewVaccineStock, { size: <any>'lg' });
    this.VaccineStockMast(e.rowData.vaccineGroupId, e.rowData.estCode);
  }

  VaccineStockMast(vaccineGroupId, estCode) {
    this._vaccineService.getVaccineStockMast(estCode, vaccineGroupId).subscribe(res => {
      this.vaccineStock = res['result'];
    })





  }

  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  locSelect(event: any, field?: any) {
    let body = this.targetSearch.value;
    if (field == "region") {
      if (event == undefined) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.walCodeSelected = null;
        this.estCodeSelected = null;
        body['walCode'] = null;
        body['estCode'] = null;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }

    } else if (field == "wilayat") {
      if (event == undefined) {
        if (body['regCode'] != null) {
          this.institeListFilter = this.institeList.filter(s => s.regCode == body['regCode']);
        } else { this.institeListFilter = this.institeList; }
        body['estCode'] = null;

        this.estCodeSelected = null;

      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode == event.walCode);
      }

    }
    else if (field == "institute") {
      if (this.isEmptyNullZero(event) === true) {
        let reg = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.regCode)[0];
        let wal = this.institeListFilter.filter(s => s.estCode == event.estCode).map(s => s.walCode)[0];
        this.regCodeSelected = reg;
        this.walCodeSelected = wal;
      }

    }
  }
}

