<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Receipient Follow Up</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Regstration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()" >
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>
<div class="accordion register">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6> Patient Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>
<div class="content-wrapper mb-2">
    <ag-grid-angular style="width: 100%; height: 200px;" class="ag-theme-balham" [gridOptions]="gridOptions"
        [rowData]="rowData" [columnDefs]="columnDefs" rowSelection="single">
    </ag-grid-angular>
</div>
<div class="content-wrapper">
    <div class="card-header"><strong>Add FollowUp Details</strong></div>
    <div class="card-body">
    <div class="row">
        <div class="col-lg-2 col-md-3 col-sm-3">
            <div class="form-group" [formGroup]="transplantFollowUpForm">
                <label>FollowUp Date</label>
                <p-calendar formControlName="followUpDate" monthNavigator="true" yearNavigator="true"
                    placeholder="dd-mm-yyyy" yearNavigator="true" showButtonBar="true">
                </p-calendar>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group" [formGroup]="transplantFollowUpForm">
                <label>Complications</label>
                <ng-multiselect-dropdown formControlName="complicationType" [placeholder]="'Add complication'"
                    [data]="compList" [settings]="dropdownSettings" [(ngModel)]="compMAstList">
                </ng-multiselect-dropdown>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group" [formGroup]="transplantFollowUpForm">
                <label>Remarks</label>
                <input type="text" class="form-control form-control-sm" formControlName="remarks">
            </div>
        </div>
        <div class="col-md-3" [formGroup]="transplantFollowUpForm">
            <div class="form-group" transplantFollowUpForm>
                <label>Transferred To</label>
                <select class="form-control form-control-sm" formControlName="transferredTo">
                    <option value=''> --Select--</option>
                    <option *ngFor="let inst of institutes" [value]="inst.estCode">{{inst.estName}}</option>
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Death</label>
                <input type="text" class="form-control form-control-sm" value="">
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Cause of death</label>
                <input type="text" class="form-control form-control-sm" value="">
            </div>
        </div>
        <label >Return to Dialysis</label>
        <div class="card-body">
            <div  style="margin-left: -120px; margin-top: 12px;">
                <div class="form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" class="form-check-input" name="dialysis" [(ngModel)]="dialysis"
                            [value]="true">Yes
                    </label>
                </div>
                <div class="form-check-inline">
                    <label class="form-check-label">
                        <input type="radio" class="form-check-input" name="dialysis" [(ngModel)]="dialysis"
                            [value]="false">No
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div class="content-wrapper mt-2">
<div *ngIf="dialysis">
    <div class="cardtheme-card">
        <div class="card-header"><strong>Dialysis Details</strong></div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group" [formGroup]="transplantFollowUpForm">
                        <label>Return to Dialysis Date</label>
                        <p-calendar formControlName="returnDialysisDate" monthNavigator="true" yearNavigator="true"
                            placeholder="dd-mm-yyyy" yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                        </p-calendar>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-4">
                    <div class="form-group" [formGroup]="transplantFollowUpForm">
                        <label>Return to Dialysis Reason</label>
                        <input type="text" class="form-control form-control-sm" formControlName="returnDialysisReason">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="btn-container">
    <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
    <button class="btn btn-sm btn-primary" (click)="saveFollowUp()">Save</button>
</div>
