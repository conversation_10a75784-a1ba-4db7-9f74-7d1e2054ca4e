<div class="row">
  <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="page-title">Cornea Request Details</h6>
  </div>
  <div class="col-lg-3 col-md-3 col-sm-3"></div>
  <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px;">
    <input type="text" placeholder="Search Request No" [(ngModel)]="requestNo" class="form-control input-sm"
      (keyup.enter)="search()" />
    <span class="input-group-btn">
      <button class="btn btn-default btn-sm" id="search" (click)="search()">
        <i class="fa fa-search"></i>
      </button>
    </span>
  </div>
</div>
<div class="cornea-register">
  <form [formGroup]="transplantForm">
    <ngb-accordion #acc="ngbAccordion" activeIds="corneaRequest,communicationDetails">
      <ngb-panel id="corneaRequest">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <h6>Cornea Request</h6>
            <button type="button" ngbPanelToggle class="btn btn-link p-0">
              <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
            </button>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <div class="col-sm-12 ">
            <div class="row">
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Request No</label>
                  <input formControlName="requestNo" type="text" class="form-control form-control-sm" readonly />
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Requesting Institute <span class="mdtr">*</span></label>
                  <ng-select [items]="instituteListFilter" [virtualScroll]="true" placeholder="Select"
                    bindLabel="estName" bindValue="estCode" formControlName="reqInst">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}</ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Date <span class="mdtr">*</span></label>
                  <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="reqDate"
                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                    yearNavigator="true" showButtonBar="true"></p-calendar>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Other Specification</label>
                  <textarea formControlName="otherSpec" rows="1" class="form-control form-control-sm"></textarea>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Indication </label>
                  <ng-select [items]="corIndicationTypeFilter" [virtualScroll]="true" placeholder="Select"
                    bindLabel="paramName" bindValue="paramId" formControlName="indication">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName }}</ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Primary Diagnosis </label>
                  <ng-select [items]="corPrimaryDiagFilter" [virtualScroll]="true" placeholder="Select"
                    bindLabel="paramName" bindValue="paramId" formControlName="primaryDiag">
                    <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName }}</ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Intended Arrival Date</label>
                  <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="intendedArrDate"
                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]=today yearRange="1930:2030"
                    yearNavigator="true" showButtonBar="true"></p-calendar>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label>Intended Surgery Date </label>
                  <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="intendedSurDate"
                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]=today yearRange="1930:2030"
                    yearNavigator="true" showButtonBar="true"></p-calendar>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group ">
                  <label>Remarks</label>
                  <input formControlName="reqRemarks" type="text" class="form-control form-control-sm" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-sm-12 mt-2">
            <div class="row">
              <div class="col-sm-6">
                <div class="mcard">
                  <div class="mcard-header d-flex justify-content-between align-items-center">
                    <span>Requesting Team</span>
                    <button [disabled]="!hasPrivilege('REG_COR_REQUEST')" class="btn btn-sm btn-primary" type="button"
                      (click)="addRequestingTeam()">
                      + Add New
                    </button>
                  </div>
                  <div class="mcard-body" formArrayName="requestingTeam">
                    <div *ngIf="paginatedRequestingTeam && paginatedRequestingTeam.length">
                      <div class="form-row font-weight-bold px-2 mb-1">
                        <div class="form-group col-md-4 m-0">
                          Doctor <span class="mdtr">*</span>
                        </div>
                        <div class="form-group col-md-4 m-0">
                          Role <span class="mdtr">*</span>
                        </div>
                        <div class="form-group col-md-3 m-0">
                          Contact No <span class="mdtr">*</span>
                        </div>
                        <div class="form-group col-md-1 m-0"></div>
                      </div>
                      <div *ngFor="let row of paginatedRequestingTeam; let i = index" [formGroup]="row"
                        class="p-2 mcard-row border mb-2">
                        <div class="form-row">
                          <div class="form-group col-md-4 mb-0">
                            <ng-select [items]="surgeonNamesListFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="staffName" bindValue="persCode" formControlName="reqStaff">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.staffName
                                }}</ng-template>
                            </ng-select>
                          </div>
                          <div class="form-group col-md-4 mb-0">
                            <ng-select [items]="roleOptions" bindLabel="label" bindValue="id" placeholder="Select Role"
                              formControlName="staffRole">
                              <ng-template ng-option-tmp let-item="item">
                                {{ item.label }}
                              </ng-template>
                            </ng-select>
                          </div>
                          <div class="form-group col-md-3 mb-0">
                            <input type="text" maxlength="16" class="form-control form-control-sm" pattern="\d{8,16}"
                              formControlName="contactNo" inputmode="numeric"
                              oninput="this.value = this.value.replace(/[^0-9]/g, '')" />
                          </div>
                          <div class="form-group col-md-1 d-flex mb-0 pt-3">
                            <div class="text-danger" (click)="removeRequestingTeam((pageTeam - 1) * pageSizeTeam + i)"
                              [style.pointerEvents]="!hasPrivilege('REG_COR_REQUEST') ? 'none' : 'auto'"
                              [ngClass]="{'disabled-trash': !hasPrivilege('REG_COR_REQUEST')}" style="cursor: pointer;">
                              <i class="fas fa-trash"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <ngb-pagination class="d-flex justify-content-center mt-2" [collectionSize]="requestingTeam.length"
                      [(page)]="pageTeam" [pageSize]="pageSizeTeam" [boundaryLinks]="true">
                    </ngb-pagination>
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="mcard">
                  <div class="mcard-header d-flex justify-content-between align-items-center">
                    <span>Requesting Tissue <span class="mdtr">*</span></span>
                    <button [disabled]="!hasPrivilege('REG_COR_REQUEST')" type="button" class="btn btn-sm btn-primary"
                      (click)="addRequestingTissue()">
                      + {{ "Add New" }}
                    </button>
                  </div>
                  <div class="mcard-body">
                    <div *ngIf="paginatedTissues && paginatedTissues.length">
                      <div class="form-row font-weight-bold px-2 mb-1">
                        <div class="form-group col-md-4 m-0">
                          Tissue <span class="mdtr">*</span>
                        </div>
                        <div class="form-group col-md-4 m-0" *ngIf="showSizeZoneHeader">Size Of Clear Zone</div>
                        <div class="form-group col-md-3 m-0">
                          Remarks
                        </div>
                        <div class="form-group col-md-1 m-0"></div>
                      </div>
                      <div *ngFor="let row of paginatedTissues; let i = index" [formGroup]="row"
                        class="p-2 mcard-row border mb-2">
                        <div class="form-row ">
                          <div class="form-group col-md-4 mb-0">
                            <ng-select [items]="corTissueTypeFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" bindValue="paramId" formControlName="tissueId">
                              <ng-template ng-option-tmp let-item="item" let-index="index">
                                {{ item.paramName }}
                              </ng-template>
                            </ng-select>
                          </div>
                          <div class="form-group col-md-4 mb-0" *ngIf="row.get('tissueId').value === 8">
                            <ng-select [items]="corClearZoneFilter" [virtualScroll]="true" placeholder="Select"
                              bindLabel="paramName" bindValue="paramId" formControlName="sizeZone">
                              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                                }}</ng-template>
                            </ng-select>
                          </div>
                          <div
                            [ngClass]="row.get('tissueId').value === 8 ? 'form-group col-md-3 mb-0' : 'form-group col-md-7 mb-0'">
                            <input min="0" type="text" class="form-control form-control-sm" formControlName="remarks" />
                          </div>
                          <div class="form-group col-md-1 d-flex align-items-end mb-0">
                            <div class="text-danger"
                              [style.pointerEvents]="!hasPrivilege('REG_COR_REQUEST') ? 'none' : 'auto'"
                              [ngClass]="{'disabled-trash': !hasPrivilege('REG_COR_REQUEST')}"
                              (click)="removeRequestingTissue((page3 - 1) * pageSize3 + i)" style="cursor: pointer;">
                              <i class="fas fa-trash"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <ngb-pagination class="d-flex justify-content-center mt-2" [(page)]="page3" [pageSize]="pageSize3"
                      [collectionSize]="requestingTissue.length" [maxSize]="5" [rotate]="true" [ellipses]="false"
                      [boundaryLinks]="true">
                    </ngb-pagination>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </ngb-panel>
      <ngb-panel id="communicationDetails">
        <ng-template ngbPanelHeader let-opened="opened">
          <div class="d-flex align-items-center justify-content-between card-head"
            [ngClass]="opened ? 'opened' : 'collapsed'">
            <div>
              <p class="mb-2" *ngIf="isEnabled" [innerHTML]="transplantForm.get('requestSendMessage')?.value"></p>
            </div>
            <div>
              <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                  [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
            </div>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <div class="col-sm-12">
            <div class="mcard">
              <div class="mcard-header d-flex justify-content-between align-items-center">
                <span>Communication Details</span>
                <button type="button" class="btn btn-sm btn-primary" (click)="addCommunicationDetail()"
                  [disabled]="!isEnabled || !hasPrivilege('REG_COR_REQ_CDNTR')">
                  + Add New
                </button>
              </div>
              <div class="mcard-body">
                <div *ngIf="communicationDetails && communicationDetails.length">
                  <div class="form-row font-weight-bold px-2 mb-1">
                    <div class="form-group col-md-3 m-0 d-flex align-items-center justify-content-between">Institute
                      Name
                    </div>
                    <div class="form-group col-md-1 m-0">Replied Date</div>
                    <div class="form-group col-md-1 m-0">Send Date</div>
                    <div class="form-group col-md-1 m-0">Remarks</div>
                    <div class="form-group w-120 m-0">Document</div>
                    <div class="form-group col-md-1 m-0">Decision</div>
                    <div class="form-group col-md-2 m-0">Reason</div>
                    <div class="form-group col-md-1 m-0">Tissue No.</div>
                    <div class="form-group col-md-1 m-0">Tissue Received Date</div>
                    <div class="m-0"></div>
                  </div>
                  <div *ngFor="let row of communicationDetails.controls, let i = index" [formGroup]="row"
                    class="p-2 mcard-row border mb-2">
                    <div class="form-row">
                      <div class="form-group col-md-3 mb-0 d-flex p-0 w-100">
                        <div class="w-100">
                          <ng-select [items]="reqInstitutesFilter" [virtualScroll]="true" placeholder="Select"
                            bindLabel="instName" bindValue="instId" formControlName="instId"
                            (change)="onInstituteSelect($event)">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.instName
                              }}</ng-template>
                          </ng-select>
                          <div class="form-group">
                            <div *ngIf="row.get('instId').value">
                              <textarea fr id="message" rows="1" class="form-control mt-1" disabled="true"
                                [value]="getInstituteAddress(row.get('instId').value)"></textarea>
                            </div>
                          </div>
                        </div>
                        <div class="d-flex align-items-start flex-column custom-div">
                          <button *ngIf="row.get('instId').value" type="button"
                            class="mb-1 bg-transparent border-0 text-decoration-none pe-3 fs-5 text-success"
                            (click)="openModalForEdit(addInstituteModal, row.get('instId').value)"
                            [disabled]="!hasPrivilege('REG_COR_REQ_CDNTR')"
                            [ngClass]="{'disabled-trash': !hasPrivilege('REG_COR_REQ_CDNTR')}">
                            <i class="fas fa-edit fs-2"></i>
                          </button>
                          <button type="button" [disabled]="!hasPrivilege('REG_COR_REQ_CDNTR')"
                            [ngClass]="{'disabled-trash': !hasPrivilege('REG_COR_REQ_CDNTR')}"
                            class="mb-1 bg-transparent border-0 text-decoration-none pe-3 fs-5 text-danger"
                            (click)="openModal(addInstituteModal)">
                            <i class="fas fa-plus"></i>
                          </button>
                        </div>
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="replyDate"
                          [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]=today
                          yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true formControlName="sendDate"
                          [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]=today
                          yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <input type="text" class="form-control form-control-sm" formControlName="remarks"
                          placeholder="Remarks" />
                      </div>
                      <div class="mb-0 d-flex align-items-center flex-wrap align-content-start">
                        <button type="button" class="btn btn-sm btn-danger ml-1"
                          (click)="attachDocument(i, fileAttachModal)" [disabled]="!hasPrivilege('REG_COR_REQ_CDNTR')">
                          Attach
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary ml-1"
                          (click)="viewDocument(i, fileViewModal)" [disabled]="!hasPrivilege('REG_COR_REQ_APPROVE')">
                          View
                        </button>
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <div class="form-check p-0">
                          <label class="d-flex" [class.disabled]="!hasPrivilege('REG_COR_REQ_APPROVE')"
                            [style.pointerEvents]="!hasPrivilege('REG_COR_REQ_APPROVE') ? 'none' : 'auto'">
                            <input type="radio" formControlName="decision" value="A"
                              (change)="onDecisionRadioChange(i, 'A', $event.target.checked)" /> Accepted
                          </label>
                          <label class="d-flex" [class.disabled]="!hasPrivilege('REG_COR_REQ_APPROVE')"
                            [style.pointerEvents]="!hasPrivilege('REG_COR_REQ_APPROVE') ? 'none' : 'auto'">
                            <input type="radio" formControlName="decision" value="R"
                              (change)="onDecisionRadioChange(i, 'R', $event.target.checked)" /> Rejected
                          </label>
                        </div>
                      </div>
                      <div class="form-group col-md-2 mb-0">
                        <input type="text" class="form-control form-control-sm" formControlName="reason" />
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <input type="text" class="form-control form-control-sm" formControlName="tissueNo"
                          placeholder="Tissue No." />
                      </div>
                      <div class="form-group col-md-1 mb-0">
                        <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon="true" formControlName="receivedDate"
                          [ngModelOptions]="{standalone: true}" monthNavigator="true" [minDate]="today"
                          yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                      </div>
                      <div class="d-flex align-items-end align-items-center flex-wrap align-content-start pt-3 mb-0">
                        <div class="text-danger" [ngClass]="{'disabled-trash': !hasPrivilege('REG_COR_REQ_CDNTR')}"
                          (click)="hasPrivilege('REG_COR_REQ_CDNTR') && communicationDetails.removeAt(i)"
                          [style.pointerEvents]="!hasPrivilege('REG_COR_REQ_CDNTR') ? 'none' : 'auto'"
                          [style.color]="!hasPrivilege('REG_COR_REQ_CDNTR') ? '#ccc' : ''" style="cursor: pointer;">
                          <i class="fas fa-trash"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
    <div class="btn-container d-flex px-1 justify-content-between">
      <div>
        <button class="btn btn-sm btn-secondary" type="button" (click)="sendSMSMsgDr()"
          [disabled]="!isEnabled || !hasPrivilege('REG_COR_REQUEST')">
          Sending Request
        </button>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-sm btn-secondary" (click)="clearForm()" type="button">Clear</button>
        <button class="btn btn-sm btn-primary" type="submit" (click)="save()">Save</button>
      </div>
    </div>
  </form>
</div>
<ng-template #addInstituteModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">{{isEdit ? 'Edit Institute' : 'Add Institute'}}</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <form [formGroup]="instForm" (ngSubmit)="saveInstitute(modal)">
    <div class="modal-body">
      <div class="form-group">
        <label>Institute Name</label>
        <input formControlName="instName" type="text" class="form-control" />
      </div>
      <div class="form-group">
        <label>Institute Address</label>
        <textarea rows="4" class="form-control mt-1" formControlName="instAddress"></textarea>
      </div>
    </div>
    <div class="modal-footer  mr-2">
      <button type="submit" class="btn btn-success" [disabled]="instForm.invalid">Save</button>
      <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    </div>
  </form>
</ng-template>
<ng-template #fileViewModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View Attachment</h5>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div *ngIf="uploadedFiles && uploadedFiles[selectedFileIndex]?.length" class="d-flex justify-content-center">
      <table class="table table-bordered table-hover table-sm">
        <thead class="thead-dark">
          <tr>
            <th>File Name</th>
            <th>Type</th>
            <th>Preview</th>
            <th>Download</th>
            <th>Delete</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let file of uploadedFiles[selectedFileIndex]; let fileIdx = index">
            <td>{{ file.name }}</td>
            <td>{{ file.type || 'Unknown' }}</td>
            <td class="text-center">
              <ng-container [ngSwitch]="true">
                <img *ngSwitchCase="file.type && file.type.startsWith('image/')" [src]="getFileUrl(file)" alt="preview"
                  class="zoom-img" />
                <span *ngSwitchCase="file.type === 'application/pdf'">
                  <i class="fa fa-file-pdf-o fa-2x text-danger"></i>
                  <a [href]="getFileUrl(file)" target="_blank" class="btn text-secondary ml-1">
                    <i class="fa fa-eye"></i>
                  </a>
                </span>
                <span *ngSwitchDefault>
                  <a [href]="getFileUrl(file)" target="_blank" class="btn btn-outline-secondary btn-sm">
                    <i class="fa fa-eye fa-1x text-dangerfa-2x text-danger"></i> Preview
                  </a>
                </span>
              </ng-container>
            </td>
            <td class="text-center">
              <a [href]="getFileUrl(file)" [download]="file.name" class="btn btn-outline-success btn-sm">
                <i class="fa fa-download"></i> Download
              </a>
            </td>
            <td class="text-center">
              <button type="button" class="btn btn-outline-danger btn-sm"
                (click)="deleteUploadedFile(selectedFileIndex, fileIdx)">
                <i class="fa fa-trash"></i> Delete
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div *ngIf="!uploadedFiles || !uploadedFiles[selectedFileIndex]?.length">
      <p>No file to display.</p>
    </div>
  </div>
</ng-template>

<ng-template #fileAttachModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Attachment</h5>

    <div class="d-flex align-items-center flex-wrap align-content-start">
      <label class="btn btn-sm btn-danger mr-1 mb-0">
        Upload files
        <input ng2FileSelect #fileInput type="file" (change)="onFileSelected($event, selectedFileIndex, fileInput)"
          style="display: none;" multiple accept="image/*,application/pdf" [attr.maxsize]="20971520" />
      </label>

      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

  </div>
  <div class="modal-body">
    <div *ngIf="uploadedFiles && uploadedFiles[selectedFileIndex]?.length" class="d-flex justify-content-center">
      <table class="table table-bordered table-hover table-sm">
        <thead class="thead-dark">
          <tr>
            <th>File Name</th>
            <th>Type</th>
            <th>Preview</th>
            <th>Delete</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let file of uploadedFiles[selectedFileIndex]; let fileIdx = index">
            <td>{{ file.name }}</td>
            <td>{{ file.type || '-' }}</td>
            <td class="text-center">
              <ng-container [ngSwitch]="true">
                <img *ngSwitchCase="file.type && file.type.startsWith('image/')" [src]="getFileUrl(file)" alt="preview"
                  class="zoom-img" />
                <span *ngSwitchCase="file.type === 'application/pdf'">
                  <i class="fa fa-file-pdf-o fa-2x text-danger"></i>
                  <a [href]="getFileUrl(file)" target="_blank" class="btn text-secondary ml-1">
                    <i class="fa fa-eye"></i>
                  </a>
                </span>
                <span *ngSwitchDefault>
                  <a [href]="getFileUrl(file)" target="_blank" class="btn btn-outline-secondary btn-sm">
                    <i class="fa fa-eye fa-1x text-dangerfa-2x text-danger"></i> Preview
                  </a>
                </span>
              </ng-container>
            </td>
            <td class="text-center">
              <button type="button" class="btn btn-outline-danger btn-sm"
                (click)="deleteUploadedFile(selectedFileIndex, fileIdx)">
                <i class="fa fa-trash"></i> Delete
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div *ngIf="!uploadedFiles || !uploadedFiles[selectedFileIndex]?.length">
      <p>No file to display.</p>
    </div>
  </div>
</ng-template>