<div class="row">
  <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="page-title">Tissue Screening</h6>
  </div>
  <div class="col-lg-3 col-md-3 col-sm-3"></div>
  <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
    <input type="text" placeholder="Search Regstration No" [(ngModel)]="regId" class="form-control input-sm"
      (keyup.enter)="search()">
    <span class="input-group-btn">
      <button class="btn btn-default btn-sm" id="search" (click)="search()" type="submit">
        <i class="fa fa-search"></i>
      </button>
    </span>
  </div>
</div>


<div class="accordion register">
  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">

    <ngb-panel id="patientDetails" id="ngb-panel-0">
      <ng-template ngbPanelHeader let-opened="opened">
        <div class="d-flex align-items-center justify-content-between card-head"
          [ngClass]="opened ? 'opened' : 'collapsed'">
          <h6> Patient Details</h6>
          <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
              [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
      </ng-template>
    </ngb-panel>



  </ngb-accordion>
</div>



<div class="content-wrapper mb-2">
  <div>
    <form [formGroup]="tissueForm">
      <div class="row mb-2">

        <div class="col-sm-4">
          <div class="card mb-2">
            <div class="card-header text-center">Class 1</div>
            <div class="card-body entry-center">
              <div class="form-group row gx-1">
                <label class="col-sm-2 col-form-label text-right">A:</label>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" id="a_Test" formControlName="a_Test" required>
                  <span *ngIf="submitted && fTissue.a_Test.errors" class="tooltiptext">{{'data is required'}}</span>

                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="a_1_Test" required>
                  <span *ngIf="submitted && fTissue.a_1_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="a_2_Test" required>
                  <span *ngIf="submitted && fTissue.a_2_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="a_3_Test" required>
                  <span *ngIf="submitted && fTissue.a_3_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
              </div>
              <div class="form-group row gx-1">
                <label class="col-sm-2 col-form-label text-right">B:</label>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="b_Test" required>
                  <span *ngIf="submitted && fTissue.b_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="b_1_Test" required>
                  <span *ngIf="submitted && fTissue.b_1_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="b_2_Test" required>
                  <span *ngIf="submitted && fTissue.b_2_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="b_3_Test" required>
                  <span *ngIf="submitted && fTissue.b_3_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
              </div>
              <div class="form-group row gx-1">
                <label class="col-sm-2 col-md-2 col-form-label text-right">CW:</label>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="cw_Test" required>
                  <span *ngIf="submitted && fTissue.cw_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="cw_1_Test" required>
                  <span *ngIf="submitted && fTissue.cw_1_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="cw_2_Test" required>
                  <span *ngIf="submitted && fTissue.cw_2_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="cw_3_Test" required>
                  <span *ngIf="submitted && fTissue.cw_3_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
              </div>
              <div class="form-group row gx-1">
                <label class="col-sm-2 col-md-2 col-form-label text-right">BW:</label>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="bw_Test" required>
                  <span *ngIf="submitted && fTissue.bw_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="bw_1_Test" required>
                  <span *ngIf="submitted && fTissue.cw_1_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="bw_2_Test" required>
                  <span *ngIf="submitted && fTissue.bw_2_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
                <div class="col-sm-2">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'patient')" formControlName="bw_3_Test" required>
                  <span *ngIf="submitted && fTissue.bw_3_Test.errors" class="tooltiptext">{{'data is required'}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-sm-8">
          <div class="card mb-2">
            <div class="card-header text-center">Class 2</div>
            <div class="card-body entry-center">
              <div class="row">
                <div class="col-sm-6 border-right">
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DRB1:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb1_Test" required>
                      <span *ngIf="submitted && fTissue.drb1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb1_1_Test" required>
                      <span *ngIf="submitted && fTissue.drb1_1_Test.errors" class="tooltiptext">data is required </span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb1_2_Test" required>
                      <span *ngIf="submitted && fTissue.drb1_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb1_3_Test" required>
                      <span *ngIf="submitted && fTissue.drb1_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DRB3:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb3_Test" required>
                      <span *ngIf="submitted && fTissue.drb3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb3_1_Test" required>
                      <span *ngIf="submitted && fTissue.drb3_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb3_2_Test" required>
                      <span *ngIf="submitted && fTissue.drb3_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb3_3_Test" required>
                      <span *ngIf="submitted && fTissue.drb3_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DRB4:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb4_Test" required>
                      <span *ngIf="submitted && fTissue.drb4_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb4_1_Test" required>
                      <span *ngIf="submitted && fTissue.drb4_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb4_2_Test" required>
                      <span *ngIf="submitted && fTissue.drb4_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb4_3_Test" required>
                      <span *ngIf="submitted && fTissue.drb4_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DRB5:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb5_Test" required>
                      <span *ngIf="submitted && fTissue.drb5_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb5_1_Test" required>
                      <span *ngIf="submitted && fTissue.drb5_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb5_2_Test" required>
                      <span *ngIf="submitted && fTissue.drb5_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="drb5_3_Test" required>
                      <span *ngIf="submitted && fTissue.drb5_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>
                </div>
                <div class="col-sm-6">
                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DQB1:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqb1_Test" required>
                      <span *ngIf="submitted && fTissue.dqb1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqb1_1_Test" required>
                      <span *ngIf="submitted && fTissue.dqb1_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqb1_2_Test" required>
                      <span *ngIf="submitted && fTissue.dqb1_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqb1_3_Test" required>
                      <span *ngIf="submitted && fTissue.dqb1_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>

                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DQA1:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqa1_Test" required>
                      <span *ngIf="submitted && fTissue.dqa1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqa1_1_Test" required>
                      <span *ngIf="submitted && fTissue.dqa1_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqa1_2_Test" required>
                      <span *ngIf="submitted && fTissue.dqa1_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dqa1_3_Test" required>
                      <span *ngIf="submitted && fTissue.dqa1_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>

                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DPB1:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpb1_Test" required>
                      <span *ngIf="submitted && fTissue.dpb1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpb1_1_Test" required>
                      <span *ngIf="submitted && fTissue.dpb1_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpb1_2_Test" required>
                      <span *ngIf="submitted && fTissue.dpb1_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpb1_3_Test" required>
                      <span *ngIf="submitted && fTissue.dpb1_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>

                  <div class="form-group row gx-1">
                    <label class="col-sm-2 col-form-label text-right">DPA1:</label>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpa1_Test" required>
                      <span *ngIf="submitted && fTissue.dpa1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpa1_1_Test" required>
                      <span *ngIf="submitted && fTissue.dpa1_1_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpa1_2_Test" required>
                      <span *ngIf="submitted && fTissue.dpa1_2_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                    <div class="col-sm-2">
                      <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                        (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                        (focusout)="changeFormat($event.target, 'patient')" formControlName="dpa1_3_Test" required>
                      <span *ngIf="submitted && fTissue.dpa1_3_Test.errors" class="tooltiptext">data is required</span>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="col-md-4">
          <h6>Remarks & Recommendations</h6>
          <textarea class="form-control" cols="5" formControlName="remarks" style="height: 190px;"></textarea>
        </div> -->
      </div>
    </form>

    <div class="row mt-4">
      <div class="col-sm-12 ">
        <h5 class="mb-3">Unacceptable Antigens</h5>
        <div>
          <div class="row">
            <div class="col-sm-6">
              <h6>Selected Antigens</h6>
              <div class="border p-3 rounded min-ht-100">
                <ul class="chips">
                  <li *ngFor="let item of selectedOptions">{{ item.group}} - {{ item.label }} <i class="fas fa-times"
                      (click)="removeSelectedOption(item)"></i>
                  </li>
                </ul>
              </div>
            </div>
            <div class="col-sm-6">
              <h6>Summary</h6>
              <textarea class="form-control min-ht-100" cols="5" formControlName="summary"></textarea>
            </div>
          </div>

          <div class="mt-4">
            <ul ngbNav #nav="ngbNav" class="nav-tabs" [(activeId)]="activeTabIndex">
              <li *ngFor="let tab of tabs; let i = index" [ngbNavItem]="i">
                <a ngbNavLink>{{ tab.title }}</a>
                <ng-template ngbNavContent>

                  <div class="row">
                    <div class="col-sm-8 border-right">
                      <!-- Group Buttons -->
                      <div class="group-box">
                        <button class="btn btn-outline-secondary antigen-btn" *ngFor="let group of tab.groups"
                          [class.active]="isGroupActive(i, group.name)" (click)="selectGroup(i, group.name)">
                          {{ group.name }}
                        </button>
                      </div>

                      <!-- Selected Group Options -->
                      <div class="row-cont">
                        <div *ngFor="let group of tab.groups">
                          <ng-container *ngIf="isGroupActive(i, group.name)">
                            <h6>Group {{ group.name }}</h6>
                            <div class="check-row">
                              <div *ngFor="let option of group.options" class="form-check-inline check">
                                <input type="checkbox" class="form-check-input custom-check"
                                  (change)="onCheckboxChange($event, group.name, option)"
                                  [checked]="isOptionSelected(group.name, option)"
                                  [id]="tab.title + '-' + group.name + '-' + option" />
                                <label class="form-check-label" [for]="tab.title + '-' + group.name + '-' + option">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <label>Remarks & Recommendations</label>
                      <textarea class="form-control min-ht-100" cols="5"></textarea>
                    </div>
                  </div>

                </ng-template>
              </li>
            </ul>
            <div [ngbNavOutlet]="nav" class="antigens"></div>
          </div>

        </div>
      </div>
    </div>

    <div class="mt-3 text-right">
      <button class="btn btn-sm btn-primary mr-2" id="HLAButton" [disabled]="this.hasSave"
        (click)="donorSelectModal(donor)">Add HLA For
        Donor</button>
      <button class="btn btn-sm btn-primary" *ngIf="centralRegNoExit" (click)="callCase()">Case Details</button>
      <button class="btn btn-sm btn-primary" [disabled]="this.hasEdit" (click)="editValue()">Edit</button>
      <button class="btn btn-sm btn-primary" [disabled]="this.hasvalue" (click)="saveTissueTypeInfo('S')">Save</button>
    </div>

  </div>
</div>





<!-- <div style="margin-top:20px">
  <ag-grid-angular style="width: 100%; height: 410px;" class="ag-theme-balham" [rowData]="donorTissue"
    (rowDoubleClicked)="onCellDoubleClicked($event)" [columnDefs]="columnDefs" [gridOptions]="gridOptions">
  </ag-grid-angular>

</div> -->


<ng-template #donor id="donor" let-c="close" let-d="dismiss" role="dialog">
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Add Donor</h4>
    <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>


  <div class="modal-body">

    <div class="row" [formGroup]="regRenalDonorForm">

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Civil ID</label>
          <input type="text" class="form-control form-control-sm" formControlName="civilId" disabled>
        </div>
      </div>

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Name</label>
          <input type="text" class="form-control form-control-sm" formControlName="fullName" disabled>
        </div>
      </div>

      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Gender</label>
          <select class="form-control form-control-sm" formControlName="sex" disabled>
            <option value='M'>Male</option>
            <option value='F'>Female</option>
          </select>

        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Nationality</label>
          <select class="form-control form-control-sm" formControlName="nationality" disabled>
            <option selected [value]="null">All Nationality</option>
            <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>
            <!--<option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>-->
          </select>
        </div>
      </div>



      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Date of Birth</label>
          <p-calendar dateFormat="dd-mm-yy" monthNavigator="true" yearRange="1930:2030" yearNavigator="true"
            showButtonBar="true" formControlName="dob">
          </p-calendar>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Blood Group</label>
          <select class="form-control form-control-sm" formControlName="bloodGroup">
            <option value='A+'>A +</option>
            <option value='A-'>A -</option>
            <option value='B-'>B -</option>
            <option value='B+'>B +</option>
            <option value='O+'>O +</option>
            <option value='O-'>O -</option>
            <option value='AB+'>AB +</option>
            <option value='AB-'>AB -</option>
          </select>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Telephone Number</label>
          <input type="text" class="form-control form-control-sm" formControlName="telNo" disabled>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-4">
        <div class="form-group">
          <label>Address</label>
          <textarea class="form-control form-control-sm" rows="1" formControlName="address" disabled></textarea>
        </div>
      </div>
    </div>
    <br>

    <form [formGroup]="donorTissueForm">
      <div class="row mb-2">


        <div class="col-lg-6 col-md-6 col-sm-6">
          <div class="card mb-2">
            <div class="card-header border-0 text-center">Class 1</div>
            <div class="card-body entry-center">
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">A:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="a_Test" id="a_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="a_1_Test" id="a_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">B:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="b_Test" id="b_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="b_1_Test" id="b_1_Test">
                </div>
              </div>
              <!-- <div class="form-group row">
              <label class="col-sm-2 col-md-2 col-form-label text-right">BW:</label>
              <div class="col-sm-5 col-md-5">
                <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500" (keypress)="numberOnly($event)" 
                pattern="^[0-9]{2}$" (focusout)="format16()"  formControlName="bw_Test">
              </div>
              <div class="col-sm-5 col-md-5">
                <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500" (keypress)="numberOnly($event)" 
                pattern="^[0-9]{2}$" (focusout)="format17()"   formControlName="bw_1_Test">
              </div>
            </div> -->
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">CW:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="cw_Test" id="cw_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="cw_1_Test" id="cw_1_Test">
                </div>
              </div>
            </div>
          </div>
        </div>




        <div class="col-lg-6 col-md-6 col-sm-6">
          <div class="card mb-2">
            <div class="card-header text-center border-0 ">Class 2</div>
            <div class="card-body entry-center custome-height">
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DR:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="dr_Test" id="dr_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="dr_1_Test" id="dr_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DRW:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="drw_Test" id="drw_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="drw_1_Test" id="drw_1_Test">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-2 col-md-2 col-form-label text-right">DQ:</label>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target, 'donor')" formControlName="dq_Test" id="dq_Test">
                </div>
                <div class="col-sm-5 col-md-5">
                  <input type="text" class="form-control form-control-sm" value="" maxlength="2" min="0" max="500"
                    (keypress)="numberOnly($event)" pattern="^[0-9]{2}$"
                    (focusout)="changeFormat($event.target , 'donor')" formControlName="dq_1_Test" id="dq_1_Test">
                </div>
              </div>


            </div>
          </div>



        </div>
      </div>
    </form>



  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-primary" (click)="saveTissueTypeInfo('D')">Save</button>
  </div>
</ng-template>