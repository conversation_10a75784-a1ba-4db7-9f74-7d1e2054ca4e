import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { MasterService } from 'src/app/_services/master.service';
import * as AppCompUtils from '../../common/app.component-utils';
import Swal from 'sweetalert2';
import { LungService } from '../lung.service';
import * as AppUtils from '../../common/app.utils';
import { SharedService } from 'src/app/_services/shared.service';
import { Router } from '@angular/router';
import { formatDate } from '@angular/common';
import { GridOptions } from 'ag-grid-community';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-lung-transplant-listing',
  templateUrl: './lung-transplant-listing.component.html',
  styleUrls: ['./lung-transplant-listing.component.scss'],
  providers: [LungService]
})
export class LungTransplantListingComponent implements OnInit {
  lungSearchForm: FormGroup;
  rowData: any[] = [];
  columnDefs: any[] = [];
  dataList: any[];
  totalRecords: any;
  lastSearchBody: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  gridOptions: GridOptions = <GridOptions>{
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };

  gender = AppCompUtils.GENDER;
  eyeOperated = AppCompUtils.EYE_OPERATED;
  corTissueTypeFilter: any[] = [];
  regionDataListFilter: any[] = [];
  wallayatListFilter: any[] = [];
  instituteListFilter: any[] = [];
  visualAcuityListFilter: any;
  transplantOutcomeListFilter: any;
  genderTypeOptions: any;
  regionData: any;
  wallayatList: any;
  institeList: any;
  yesNo: any;

  constructor(
    private _masterService: MasterService,
    private fb: FormBuilder,
    private _http: HttpClient,
    private _lungService: LungService,
    private _sharedService: SharedService,
    private _router: Router
  ) {}

  ngOnInit() {
    this.initializeForm();
    this.getMasterData();
    this.columnDefs = this.initColumnDefs();
  }

  initializeForm() {
    this.lungSearchForm = this.fb.group({
      civilId: new FormControl(null),
      regNo: new FormControl(null),
      ageFrom: new FormControl(null),
      ageTo: new FormControl(null),
      sex: new FormControl(null),
      regCode: new FormControl(null),
      walCode: new FormControl(null),
      tranInst: new FormControl(null),
      tranDateFrom: new FormControl(null),
      tranDateTo: new FormControl(null),
      transplantOutcome: new FormControl(null)
    });
  }

  getMasterData(regCode: any = 0, walCode: any = 0) {
    this._masterService.getRegionsList().subscribe(
      (response) => (this.regionDataListFilter = response.result),
      () => {}
    );
    this._masterService.getWilayatList(regCode).subscribe(
      (response) => (this.wallayatListFilter = response.result),
      () => {}
    );
    this._masterService.getInstiteList(regCode, walCode).subscribe(
      (response) => (this.instituteListFilter = response.result),
      () => {}
    );
  }

  getList(event?: any) {
      let pageable = {
        page: event ? event.page : 0,
        size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
      };
      if (this.gridOptions.api) { this.gridOptions.api.showLoadingOverlay(); }
  
      let body = this.lungSearchForm.value;

      console.log("body--",body);
  
      body = { pageable, ...body }
  
  
      this._http.post(AppUtils.GET_LUNG_TRANSPLANT_LIST, body).subscribe(res => {
        if (res && res['code'] == "S0000" && res['result']) {
          console.log("res--", res);
          this.rowData = res['result']['content'] || [];
          this.totalRecords = res['result']['totalElements'] || 0;
  
          for (let i = 0; i < this.rowData.length; i++) {
          
  
            this.rowData[i].regCode = this.regionDataListFilter
              .filter(s => s.regCode == Number(this.rowData[i].regCode))
              .map(s => s.regName)
              .toString();
  
            this.rowData[i].walCode = this.wallayatListFilter
              .filter(s => s.walCode == Number(this.rowData[i].walCode))
              .map(s => s.walName)
              .toString();
  
            this.rowData[i].estCode = this.instituteListFilter
              .filter(s => s.estCode == Number(this.rowData[i].estCode))
              .map(s => s.estName)
              .toString();
  
            // this.rowData[i].recommendTransplantYn = this.yesNo
            //   .filter(s => s.id == this.rowData[i].recommendTransplantYn)
            //   .map(s => s.value)
            //   .toString();
  
            // this.rowData[i].transplantUrgentYn = this.yesNo
            //   .filter(s => s.id == this.rowData[i].transplantUrgentYn)
            //   .map(s => s.value)
            //   .toString();
  
          }
        } else {
          this.rowData = null;
          Swal.fire('Error!', res ? res['message'] : 'Invalid response', 'error');
        }
      }, error => {
        if (error.status == 401) {
          Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
        }
      });
  
    }

    getListLung(event?: any): void {
        let pageable = {
          page: event ? event.page : 0,
          size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
        };
    
        const searchBody = { ...this.lungSearchForm.value, pageable };
        this.lastSearchBody = { ...searchBody };
    
        if (this.gridOptions.api) {
          this.gridOptions.api.showLoadingOverlay();
        }
    
        this._lungService.fetchLungTransplantList(searchBody).subscribe(
          (res) => {
            if (res['code'] === 'S0000') {
              const content = res['result']['content'] || [];
              this.rowData = content
                .map((item) => {
                  const mappedItem: any = {};
                  this.columnDefs.forEach((colDef) => {
                    const field = colDef.field;
                    if (field) {
                      mappedItem[field] = item[field];
                    }
                  });
                  return mappedItem;
                })
                .sort(
                  (a, b) =>
                    new Date(b['reqDate']).getTime() - new Date(a['reqDate']).getTime()
                );
              this.totalRecords = res['result']['totalElements'] || 0;
              this.dataList = [...this.rowData];
            } else {
              this.handleError(res['message'] || 'Invalid response');
            }
          },
          (error) => {
            if (error['status'] === 401) {
              this.handleError('Error occurred while retrieving user details');
            } else {
              this.handleError('An unexpected error occurred');
            }
          }
        );
      }

  initColumnDefs(): any[] {
    const format = (params: any) =>
      params.data[params.colDef.field]
        ? formatDate(params.data[params.colDef.field], AppCompUtils.DATE_FORMATS.STANDARD, 'en')
        : null;

    const mapValue = (list: any[], id: any) => {
      const item = list.find((entry) => entry.id === id);
      return item ? item.value : null;
    };

    return [
      { headerName: 'Central Reg No', field: 'regNo', minWidth: 150, sortable: true, resizable: true },
      { headerName: 'Civil ID', field: 'civilId', minWidth: 150, sortable: true, resizable: true },
      { headerName: 'Patient ID', field: 'patientId', minWidth: 150,sortable: true, resizable: true },
      { headerName: 'Patient Name', field: 'fullName', minWidth: 280, sortable: true, resizable: true },
      { headerName: 'Gender', field: 'sex', minWidth: 100, valueGetter: (params) => mapValue(this.gender, params.data.sex) },
      { headerName: 'Age', field: 'age', minWidth: 80, sortable: true, resizable: true },
      { headerName: 'Wilayath Name', field: 'walCode', minWidth: 150, sortable: true, resizable: true },
      { headerName: 'Region Name', field: 'regCode', minWidth: 150, sortable: true, resizable: true },
       { headerName: 'Transplant Date', field: 'transplantDate', minWidth: 180, sortable: true, valueFormatter: format },
       { headerName: 'Transplant Remarks', field: 'transplantRemarks', minWidth: 150, resizable: true }
    ];
  }

  exportExcel() {
    if (!this.rowData || this.rowData.length === 0) {
      return Swal.fire('Warning!', 'No Records to export', 'warning');
    }
    const searchBody = { ...this.lungSearchForm.value, getAll: true, pageable: { page: 0, size: this.totalRecords } };

    this._lungService.getCorneaTransplantList(searchBody).subscribe({
      next: (res) => {
        const data = res['result']['content'] || [];
        if (!data.length) {
          return Swal.fire('Warning!', 'No Records to export', 'warning');
        }
        const formattedData = data.map((el) => {
          const mappedItem: any = {};
          this.columnDefs.forEach((colDef) => {
            const headerName = colDef.headerName;
            const field = colDef.field;
            if (headerName && field) {
              mappedItem[headerName] = el[field];
            }
          });
          return mappedItem;
        });
        this._sharedService.exportAsExcelFile(formattedData, 'Cornea_Listing');
      },
      error: () => this.handleError('Error occurred during export')
    });
  }

  clear(): void {
    this.lungSearchForm.reset();
    this.rowData = [];
    if (this.gridOptions.api) {
      this.gridOptions.api.setRowData([]);
    }
  }

  onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['lung/transplant'], { state: { regNo: event.data.regNo } });
  }

  private handleError(message: string): void {
    this.rowData = [];
    Swal.fire('Error!', message, 'error');
  }
}
