import { RegistryPatient } from './registry-patient.model';

export class RenalWaitingList {
    public runId: number;
    public enteredBy: number;
    public entryDate: any;
    public modifiedDate: any;
    public modifiedBy: number;
    public noPrevTransplant: number;
    public readyTransplantYn: string;
    public transplantDelayReason: string;
    public transplantReasonReamarks: string;
    public transplantUrgentYn: string;
    public previousOrganDonor: string;
    public rgTbRegistryPatient:any;
    public readyTransplantYnValue: string;
    public transplantDelayReasonValue: string; 
    public transplantUrgentYnValue: string;
    public previousOrganDonorValue: string;   
  //  public rgTbRegistryPatient : RegistryPatient;
        
}



