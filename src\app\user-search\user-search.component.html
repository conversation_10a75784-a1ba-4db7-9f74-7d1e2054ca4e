<div >
    <h4 class="head">User Management0    
        <!-- <button class="btn add-btn pull-right" data-toggle="modal"  (click)="showAddUserPopup()"><i class="fa fa-plus"></i>Add User</button> -->
    </h4> 
    <div class="inner-wrapper">
    <div class="row">
        <div class="col-md-12">
            <form [formGroup]="myForm">
                <div class="row search-area">

                    <div class="col-lg-2 col-md-2 col-sm-2">
                        <div class="ui-input-group mar-0">
                          <input formControlName="personName" type="text" class="form-control" placeholder="Name Like">
                       </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-2">
                        <div class="ui-input-group mar-0">
                          <input formControlName="perscode" type="text" class="form-control" placeholder=" Perscode">
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-2">
                        <div class="ui-input-group mar-0">
                          <input formControlName="loginId" type="text" class="form-control" placeholder=" Login ID">
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-2">
                    
                        <p-multiSelect   defaultLabel="Select Roles" class="widthprime" [options]="staffRoles" formControlName="roles" name="roles" ></p-multiSelect>
                    </div>

                    <div class="col-lg-2 col-md-2 col-sm-2">
                        <!-- <p-multiSelect  defaultLabel="Select Institutes"  [options]="staffInst" name="institutes" formControlName="institute"  [itemSize]="25"></p-multiSelect> -->
                        <ng-multiselect-dropdown
                        [placeholder]="'Select Institutes'"
                        [data]="staffInst"
                        formControlName="institute"
                        [settings]="dropdownSettings"
                        [(ngModel)]="institutes" class="custom-dropdown"
                      >
                      </ng-multiselect-dropdown>
                    </div>
                </div>
                <div class="btn-container ">
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <button type="submit" (click)="getUsers()" class="btn btn-primary">Search</button>
                        <button  class="btn btn-primary ripple" (click)="clear($event)">Clear</button>
                       <!--  <div class="text-right top-space"> -->
                                <button class="btn btn-primary" [disabled]="!hasPrivilege('REG_ADD_USER')" (click)="addNewUser()"> Add New User</button>
                          <!-- </div> -->
                    </div>
                </div>
            </form> 
        </div>
    </div>  
      
    <div class="row">
        <div class="col-md-12">
            <div class="mar-top">
                <div class="ui-widget-header" style="padding:4px 10px;border-bottom: 0 none">
                    <i class="fa fa-search" style="margin:4px 4px 0 0"></i>
                    <input #gb type="text" pInputText size="50" placeholder="Global Filter">
                </div>
                <p-dataTable [value]="usersList" selectionMode="single" [immutable]="false" [rows]="10" [paginator]="false" 
                (onRowSelect)="onRowSelect($event)" (onRowUnselect)="onRowUnselect($event)" dataKey="id" [globalFilter]="gb">
                        <p-column field="loginId" header="Login Id"></p-column>
                        <p-column field="name" header="Name"></p-column>
                        <p-column field="categoryName" header="User Category"></p-column>
                        <p-column field="gsm" header="GSM"></p-column>
                        <p-column field="email" header="Email"></p-column>
                </p-dataTable>
                <div *ngIf="totalRecords > 0">
                    <p-paginator #pp rows="10" totalRecords="{{totalRecords}}" (onPageChange)="getUsers($event)"
                      showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)"></p-paginator>
                  </div>
            </div>
        </div>
        
    </div>
  
    </div>

    <!-- Add User -->
    <div class="inner-content" id="userDetailsview" *ngIf="userDetails !=null || showuserview == true ">
             <h4>User Details </h4> 
             <div class="inner-wrapper">
             <div class="message-location">
                 <label *ngIf="applayMessage == true" class="invalid-message-user">User Already Exist !!! </label>
            </div>          
            
            <div class="mainbody ">
                <div class="row ui-input-group"> 
                    <div class="col-md-2">
                        <div class="row">
                           <div class="col-md-9">
                                <input class="form-control ng-untouched ng-pristine ng-valid disabled" [disabled]="!isNewUser" id="perscode" #fieldPersCode [(ngModel)]="userDetails.person.perscode"
                                placeholder=" Perscode" type="text" (keydown)="keyDownFunction($event)"/>
                           </div>  
                           <div >
                                <button type ="submit" class="btn btn-default btn-sm glyphicon glyphicon-arrow-right submitperscode"
                                (click)=getuserbyperscode($event)></button>
                           </div>    
                       </div>  
                    </div>      
                    <div class="col-md-2"><input class="form-control ng-untouched ng-pristine disabled" [(ngModel)]="userDetails.person.personName" disabled=""
                            placeholder=" Name" type="text">
                    </div>
                    <div class="col-md-2"> <input class="form-control ng-untouched ng-pristine disabled" [(ngModel)]="userDetails.loginId" disabled="" placeholder=" LoginId"
                            type="text">
                    </div>
                    <div class="col-md-5" *ngIf="showuserview == true">
                        
                        <div class="form-inputs">    
                            <input checked="" id="checkboxFourInput" name="" [(ngModel)]="userDetails.active" type="checkbox" >
                            <label for="checkboxFourInput">Active</label>                            
                        </div>                       
                    </div>
                </div>

                <div class="row top-space">
                    <div class="col-md-3">
                        <div class="modal-container">
                            <div class="modal-header">
                                Institutes
                    
                            </div>
                            <div class="modal-mainbody">

                                <ul class="list-group" id="instituteData">
                                    <li *ngFor="let inst of userDetails.institutes"
                                         class="list-group-item" (click)="listClick($event, inst)"
                                        [ngClass]="{'active':selectedItem == inst }">
                                        <span><p-radioButton name="defaultInst" value="Y" title="Default Institute" [(ngModel)]="inst.defaultYN" (ngModelChange)="selectDefaultInst(inst,$event)" >
                                        </p-radioButton></span>{{inst.estFullName}}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="modal-container">
                            <div class="modal-header">
                                Roles
                                <div class="header-controls">
                                    <span class="glyphicon glyphicon-plus" [disabled]="!hasPrivilege('REG_ADD_USER')" data-target="#rolesViewModal" data-toggle="modal"></span>
                                    <span class="glyphicon glyphicon-remove" [disabled]="!hasPrivilege('REG_ADD_USER')" (click)=" removeRole($event) "></span>
                                </div>
                            </div>
                            <div class="modal-mainbody">
                                <ul class="list-group">
                                    <li class="list-group-item" *ngFor="let role of userDetails.roles" (click)="listClick($event, role)" 
                                    [ngClass]="{'active':selectedItem==role}">{{role.name}}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                   
                    <div class="col-md-12 top-space">
                        <button class="btn btn-primary" type="button" [disabled]="!hasPrivilege('REG_ADD_USER')" (click)=saveUser()>Save</button>
                        <button class="btn btn-primary" type="button" [disabled]="!hasPrivilege('REG_ADD_USER')" (click)=cleardata()>Clear</button>
                        <button class="btn btn-primary" type="button" [disabled]="!hasPrivilege('REG_ADD_USER')" (click)=resetPassword()>Reset Password</button>
                    </div>

                </div>
            </div>
            </div>
        </div>
        
        <!-- institutesViewModal -->
        <div class="modal fade custom-modal" id="institutesViewModal" role="dialog">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">institutes View</h4>
                        </div>
                        <div class="modal-body">
                            <input type="text" id="myFilterInst" class="form-conrol panel-search hidden-xs hidden-sm" placeholder="Filter Institute">
                            <br>
                            <br>
            
                            <div class="row">
            
                                <div class="col-md-5">
            
                                    <select id="availableInst" [(ngModel)]="availableInstitutes" multiple="" name="availableInst" class="form-control AllBoxHeight">
                                    <!--template bindings={}--><option [value]="inst.estCode" *ngFor="let inst of availableInst" [class.hidden]="!shouldDisplay(inst.estName)">{{inst.estName}}</option>
                                </select>
                                </div>
                                <div class="col-md-1 add-remove-controls">
                                    <div id="left"><button class="glyphicon glyphicon-menu-right" (click)="moveLtoRInst()" type="button"></button></div>
                                    <div id="right"> <button class="glyphicon glyphicon-menu-left" (click)="moveRtoLInst()" type="button"></button></div>
                                    <div id="leftall"><button class="glyphicon glyphicon-forward" (click)="moveAllInstToL()" type="button"></button></div>
                                    <div id="rightall"><button class="glyphicon glyphicon-backward" (click)="moveAllInstToR()" type="button"></button></div>
                                </div>
                                <div class="col-md-6">
                                    <select id="estAvailable" multiple="" name="estAvailable" class="form-control AllBoxHeight" [(ngModel)]="assignedInstitutes">
                                        <!--template bindings={}--><option [value]="inst.estCode" *ngFor="let inst of assignedInst">{{inst.estName}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" id="okinst " class="btn btn-default" data-dismiss="modal">Ok</button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
        </div>

        <!-- rolesViewModal -->
        <div class="modal fade custom-modal" id="rolesViewModal" data-backdrop="false" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Roles View</h4>
                        </div>
               
                        <div class="modal-body role-modal">
              
                            <div class="row">
                                <div class="col-md-5">
                                    <input type="text" [(ngModel)]="searchText" placeholder="Search Role" />
                                   <!-- <select id="availableRole" [(ngModel)]="availableRoles" name="availableRole" multiple="" class="form-control AllBoxHeight">
                                    <option [value]="role.id" *ngFor="let role of availableRole | filter : searchText : 'name'" [class.hidden]="!shouldDisplay(role.name)">{{role.name}}</option>
                                </select>
                                -->
                                </div>  
                                <div class="col-md-1 add-remove-controls">
                                    <div id="leftRole"><button class="glyphicon glyphicon-menu-right" (click)="moveLtoRRole()" type="button"></button></div>
                                    <div id="rightRole"> <button class="glyphicon glyphicon-menu-left" (click)="moveRtoLRole()" type="button"></button></div>
                                    <div id="leftallRole"><button class="glyphicon glyphicon-forward" (click)="moveAllRoleToL()" type="button"></button></div>
                                    <div id="rightallRole"><button class="glyphicon glyphicon-backward" (click)="moveAllRoleToR()" type="button"></button></div>
                                </div>
                                <div class="col-md-6">
                                    <select id="assignedRole" name="assignedRole" multiple="" class="form-control AllBoxHeight" [(ngModel)]="assignedRoles">
                                     <option [value]="role.id" *ngFor="let role of assignedRole">{{role.name}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Ok</button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

</div> <!-- ./container -->