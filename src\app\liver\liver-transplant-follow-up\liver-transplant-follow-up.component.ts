import { ComlicationMastModel } from '../../common/objectModels/complication-mast-model';
import { InstituteDataModel } from '../../common/objectModels/institute-model';
import { Validators, FormControl, FormGroup, FormBuilder } from '@angular/forms';
import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { MasterService } from '../../_services/master.service';
import * as AppUtils from '../../common/app.utils';
import { GridOptions } from "ag-grid-community";
import { PatientDetailsComponent } from '../../_comments/patient-details/patient-details.component';
import Swal from 'sweetalert2';
import { formatDate } from '@angular/common';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { LiverService } from '../liver.service';
import { liverTransplantFollowupModel } from 'src/app/common/objectModels/liver-transplant-follow-up-model';
import { AuthenticationService } from 'src/app/_services/authentication.service';
import { LiverTransplantFollowupResultModel } from 'src/app/common/objectModels/liver-transplant-follow-up-result-model';
import { DATE_FORMATS } from 'src/app/common/app.component-utils';
import { ICDList } from 'src/app/_models/icdList-models';

@Component({
  selector: 'app-liver-transplant-follow-up',
  templateUrl: './liver-transplant-follow-up.component.html',
  styleUrls: ['./liver-transplant-follow-up.component.scss'],
  providers: [LiverService],
})
export class LiverTransplantFollowUpComponent implements OnInit {
  wallayatList: any[];
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  compList: any[];
  compMAstList: ComlicationMastModel[] = [];
  regId: any;
  institutes: InstituteDataModel[] = [];
  icdDeathList: Array<ICDList>;
  instituteListFilter: any[];
  icdDeathListFilter: any[];
  followupDto: liverTransplantFollowupModel = new liverTransplantFollowupModel();
  followupData: liverTransplantFollowupModel = new liverTransplantFollowupModel();
  transplantFollowUpForm: FormGroup;
  ComplicationsForm: FormGroup;
  followUpList: LiverTransplantFollowupResultModel[] = [];
  userId = this._authenticationService.getUserId();

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };

  private gridApi: any;
  submitted = false;
  rowSelection: liverTransplantFollowupModel;
  selectedrow: any;
  dialysis;
  medicines: any[] = [];
  disease: any[] = [];

  @ViewChild('patientDetails', { static: false }) patientDetails: PatientDetailsComponent;
  columnDefs = [
    {
      headerName: 'Follow Up Date',
      field: 'fwUpDate',
      minWidth: 250,
      sortable: true,
      valueGetter: (params: any) => {
        return params.data.fwUpDate
          ? formatDate(params.data.fwUpDate, DATE_FORMATS.STANDARD, 'en')
          : '';
      }
    },
    {
      headerName: 'Complications',
      field: 'compDesc',
      resizable: true,
      minWidth: 200
    },
    {
      headerName: 'Medicines For',
      field: 'medDesc',
      resizable: true,
      minWidth: 200
    },
    {
      headerName: 'Disease Recurrence',
      field: 'disDesc',
      resizable: true,
      minWidth: 200
    },
    {
      headerName: 'Cause of Death',
      field: 'causeOfDeath',
      resizable: true,
      minWidth: 200
    }
  ];

  rowData: Array<LiverTransplantFollowupResultModel> = new Array<LiverTransplantFollowupResultModel>();

  dropdownSettings: IDropdownSettings;
  patientForm: FormGroup;
  compMap: any;
  medMap: any;
  disMap: any;

  constructor(
    private fb: FormBuilder,
    private _liverService: LiverService,
    private _masterService: MasterService,
    private _authenticationService: AuthenticationService,
    private cdr: ChangeDetectorRef
  ) {}

  regIdControl = new FormControl('');

  ngOnInit() {
    this.rowData = [];
    this.getAllLiverTransFwupComp();
    this.getAllLiverTransFwupDisease();
    this.getAllLiverTransFwupMed();
    this.getInstituteList();
    this.getIcdDeathList();

    this.patientForm = this.fb.group({
      patientId: new FormControl(),
      civilId: new FormControl(),
      dob: new FormControl(),
      age: new FormControl(),
      tribe: new FormControl(),
      firstName: new FormControl(),
      secondName: new FormControl(),
      sex: new FormControl(),
      thirdName: new FormControl(),
      maritalStatus: new FormControl(),
      village: new FormControl(),
      walayat: new FormControl(),
      region: new FormControl(),
      mobileNo: new FormControl(),
      tel: new FormControl(),
    });

    this.transplantFollowUpForm = this.fb.group({
      fwUpId: new FormControl(),
      fwUpDate: new FormControl(),
      deathYn: new FormControl(),
      causeOfDeath: new FormControl(),
      rgTbOrganTransDiseaseList: new FormControl(),
      rgTbOrganTransMedList: new FormControl(),
      rgTbOrganTransCompList: new FormControl(),
      createdBy: new FormControl(),
      createdDate: new FormControl(),
      transferredTo: new FormControl(),
      transferredYn: new FormControl(),
      remarks: new FormControl(),
    });

    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'value',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };
  }

  ngAfterViewInit() {
    this.patientDetails.patientForm.disable();
  }

  get controlValues() { return this.transplantFollowUpForm.controls; }
  get complicationsControlValues() { return this.ComplicationsForm.controls; }

  search() {
    const regId = this.regIdControl.value;

    if (regId) {
      this.getList(regId);
      this.followupDto.centralRegNo = regId;
      this.clear();
      this.getLiverTransplantFollowupList(regId);
      this.regIdControl.setValue('');
    } else {
      Swal.fire({
        icon: 'warning',
        title: 'Please enter Registration ID'
      });
    }
  }

  getList(regNo: any) {
    this._liverService.getLiverWaitingList(regNo).subscribe(res => {
      if (res['code'] == "S0000") {
        this.patientDetails.setPatientDetails(res['result']);
      } else {
        Swal.fire(
          'Warning',
          'No Record Found with Entered Regstration No. ',
          'warning'
        )
      }      
    }, error => {
      if (error.status == 401)
        Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
    });
  }

  getLiverTransplantFollowupList(centralRegNo?: string, event?: any) {
    if (!centralRegNo) {
      centralRegNo = this.patientDetails.patientForm.value.centralRegNo;
    }
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };
    if (this.gridOptions.api) { this.gridOptions.api.showLoadingOverlay(); }

    let body = { centralRegNo, pageable };
    this._liverService.getLiverTransplantFollowUp(body).subscribe(res => {
      if (res) {
        if (res['code'] === "S0000") {
          this.followUpList = res['result']['content'] || [];
          this.rowData = res['result']['content'];
          this.totalRecords = res['result']['totalElements'] || 0;
        }
      }
    }, err => {});
  }

  saveFollowUp() {
    if (this.patientDetails.patientForm.invalid) {
      Swal.fire('Warning!', 'Please enter Registration ID', 'warning');
      return false;
    }

    if (!this.followupDto.centralRegNo) {
      Swal.fire('Warning!', 'Please enter Registration ID', 'warning');
      return;
    }

    if (!this.controlValues.fwUpDate.value) {
      Swal.fire('Warning!', 'Follow-up Date cannot be empty', 'warning');
      return;
    }

    if (this.controlValues.fwUpId.value === null) {
      const duplicateEntry = this.followUpList.some(followUp =>
        new Date(followUp.fwUpDate).getTime() === new Date(this.controlValues.fwUpDate.value).getTime()
      );

      if (duplicateEntry) {
        Swal.fire('Error!', 'This Follow-up Date is already recorded.', 'error');
        return;
      }
    } else {
      const duplicateEntry = this.followUpList.some(followUp =>
        followUp.fwUpId !== this.controlValues.fwUpId.value &&
        new Date(followUp.fwUpDate).getTime() === new Date(this.controlValues.fwUpDate.value).getTime()
      );

      if (duplicateEntry) {
        Swal.fire('Error!', 'This Follow-up Date is already recorded.', 'error');
        return;
      }
    }

    this.submitted = true;

    this.followupDto.fwUpId = this.controlValues.fwUpId.value;

    this.followupDto.fwUpDate = this.controlValues.fwUpDate.value;
    this.followupDto.centralRegNo = this.patientDetails.patientForm.value.centralRegNo;
    this.followupDto.registerType = 14;

    if (this.followupDto.fwUpId) {
      this.followupDto.createdBy = this.controlValues.createdBy.value;
      this.followupDto.createdDate = this.controlValues.createdDate.value;
    }

    this.followupDto.rgTbOrganTransCompList = this.controlValues.rgTbOrganTransCompList.value
      ? this.controlValues.rgTbOrganTransCompList.value.map(comp => ({
        runId: null,
        complicationType: comp.id,
        complicationRemarks: comp.value,
      }))
      : [];

    this.followupDto.rgTbOrganTransMedList = this.controlValues.rgTbOrganTransMedList.value
      ? this.controlValues.rgTbOrganTransMedList.value.map(comp => ({
        runId: null,
        medicineFor: comp.id,
        remarks: comp.value,
      }))
      : [];

    this.followupDto.rgTbOrganTransDiseaseList = this.controlValues.rgTbOrganTransDiseaseList.value
      ? this.controlValues.rgTbOrganTransDiseaseList.value.map(comp => ({
        runId: null,
        diseaseType: comp.id,
        remarks: comp.value,
      }))
      : [];



    if (this.controlValues.transferredYn.value == 'true') {
      this.followupDto.transferredYn = "Y";
      this.followupDto.transferredTo = this.controlValues.transferredTo.value;
    }else{
      this.followupDto.transferredYn = "N";
      this.followupDto.transferredTo = null;
    }

    if (this.controlValues.deathYn.value == 'true') {
      this.followupDto.deathYn = "Y";
      this.followupDto.causeOfDeath = this.controlValues.causeOfDeath.value;
    } else {
      this.followupDto.deathYn = "N";
      this.followupDto.causeOfDeath = null;

    }

    this.followupDto.remarks = this.controlValues.remarks
      .value;

    this._liverService.addLiverTransplantFollowUp(this.followupDto).subscribe(res => {
      if (res) {
        if (res['code'] === "0") {
          Swal.fire('Saved!', 'Liver Recipient Follow Up Saved successfully.', 'success');
          this.clear();
          this.getList(res['result']);
          this.followupDto.centralRegNo = res['result'];
          this.getLiverTransplantFollowupList(res['result']);
        } else if (res['code'] === "3") {
          Swal.fire('Saved!', res['message'], 'error');
        } else {
          Swal.fire('Error!', res['message'], 'error');
        }
      }
    }, err => {
      Swal.fire('Error!', 'Error occurred while saving TransplantFollowUp ' + err.message, 'error');
    });
  }

  getAllLiverTransFwupComp() {
    this._masterService.getAllLiverTransFwupComp().subscribe(response => {
      this.compList = response.result;
    }, error => {});
  }

  getAllLiverTransFwupMed() {
    this._masterService.getAllLiverTransFwupMed().subscribe(response => {
      this.medicines = response.result;
    }, error => {});
  }

  getAllLiverTransFwupDisease() {
    this._masterService.getAllLiverTransFwupDisease().subscribe(response => {
      this.disease = response.result || [];
    }, error => {});
  }

  getInstituteList() {
    this._masterService.institiutes.subscribe(res => {
      this.institutes = res["result"];
      this.instituteListFilter = this.institutes;
    }, error => {});
  }

  getIcdDeathList() {
    this._masterService.getIcdDeathList();
    this._masterService.icdDeathShortList.subscribe((value) => {
      this.icdDeathList = value;
      //this.icdDeathListFilter = this.icdDeathList;
    });
  }

  onCellDoubleClicked(event: any) {
    if (!event.data) return;
    this.getLiverTransplantFollowupListById(event.data.fwUpId);
  }

  clear() {
    this.patientDetails.clear();
    this.rowData = null;
    this.resetForm()
  }

  resetForm() {
    this.transplantFollowUpForm.reset();
    this.controlValues.fwUpDate.setValue(null);
    this.controlValues.causeOfDeath.setValue(null);
    this.controlValues.deathYn.setValue(null);
    this.controlValues.rgTbOrganTransCompList.setValue([]);
    this.controlValues.rgTbOrganTransMedList.setValue([]);
    this.controlValues.rgTbOrganTransDiseaseList.setValue([]);
    this.controlValues.transferredTo.setValue(null);
    this.controlValues.transferredYn.setValue(null);
    this.controlValues.remarks.setValue(null);
    this.submitted = false;
  }

  getLiverTransplantFollowupListById(fwUpId?: string) {
    this._liverService.getLiverTransplantFollowUpById(fwUpId).subscribe(res => {
      if (res && res['code'] === "S0000") {
        const result = res['result'];

        const parsedCompIds = result['compId'] ? result['compId'].split(',').map(id => Number(id.trim())) : [];
        const parsedMedIds = result['medId'] ? result['medId'].split(',').map(id => Number(id.trim())) : [];
        const parsedDisIds = result['disId'] ? result['disId'].split(',').map(id => Number(id.trim())) : [];

        if (!this.compList.length || !this.medicines.length || !this.disease.length) {
          console.warn("Dropdown data is empty! Waiting for it to load.");
          return;
        }

        const selectedComp = parsedCompIds.map(id => this.compList.find(comp => comp.id === id) || null);
        const selectedMed = parsedMedIds.map(id => this.medicines.find(med => med.id === id) || null);
        const selectedDis = parsedDisIds.map(id => this.disease.find(dis => dis.id === id) || null);

        this.transplantFollowUpForm.controls['rgTbOrganTransCompList'].setValue([]);
        this.transplantFollowUpForm.controls['rgTbOrganTransMedList'].setValue([]);
        this.transplantFollowUpForm.controls['rgTbOrganTransDiseaseList'].setValue([]);
        this.cdr.detectChanges();

        setTimeout(() => {
          this.transplantFollowUpForm.patchValue({
            fwUpId: result['fwUpId'],
            fwUpDate: result['fwUpDate'] ? new Date(result['fwUpDate']) : null,
            causeOfDeath: result['causeOfDeath'],
            createdDate: result['createdDate'],
            createdBy: result['createdBy'],
            rgTbOrganTransCompList: selectedComp,
            rgTbOrganTransMedList: selectedMed,
            rgTbOrganTransDiseaseList: selectedDis,
            deathYn: result['deathYn'] === "Y" ? "true" : "false",
            transferredTo: result['transferredTo'],
            transferredYn: result['transferredYn'] === "Y" ? "true" : "false",
            remarks: result['remarks'],

          });

          this.transplantFollowUpForm.controls['rgTbOrganTransCompList'].updateValueAndValidity();
          this.transplantFollowUpForm.controls['rgTbOrganTransMedList'].updateValueAndValidity();
          this.transplantFollowUpForm.controls['rgTbOrganTransDiseaseList'].updateValueAndValidity();

          this.cdr.detectChanges();
        }, 100);

        setTimeout(() => {
          this.dropdownSettings = { ...this.dropdownSettings };
          this.cdr.detectChanges();
        }, 200);
      }
    }, err => {});
  }
}



