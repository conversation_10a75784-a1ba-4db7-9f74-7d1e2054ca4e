import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import * as AppUtils from '../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class VaccineService {

  constructor(private http: HttpClient) { }

   // vaccine listing
   getVaccineListing(data:any): Observable<any> {
    return this.http.post(AppUtils.SEARCH_VACCINE_REGISTRY, data);
    
  }

  getVaccineStockMast(estCode:any,vaccineId:any): Observable<any>{
    return this.http.get(AppUtils.VACCINE_STOCK_MAST,{
      params: new HttpParams().set("estCode",estCode).set("vaccineId", vaccineId)
    })
    }

  getTbVaccineMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_TB_VACCINE_MASTER);
}


getImmunization(civilId,regNo,estCode){
  return this.http.get(AppUtils.GET_Imunization_REGISTRY, {
    params: new HttpParams()
    .set("civilId", civilId)
    .set("regNo", regNo)
    .set("estCode", estCode)
    //.set("regType",AppUtils.REG_TYPE_IMMUNIZATION.toString())
    
  })
}

getImmunizationByCivilId(civilId,dob): Observable<any> {
  return this.http.get(AppUtils.GET_Imunization+"/"+civilId + "/"+dob); 
}

saveImmunization(data) :Observable<any>{
  return this.http.post(AppUtils.SAVE_Imunization_REGISTRY, data);
 }

 //Immunization Dashboard

 getImmunizationDashboard(): Observable<any> {
  return this.http.get(AppUtils.IMMUNIZATION_DASHBOARD);
}


}





    
