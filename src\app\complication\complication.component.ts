import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MasterService } from '../_services/master.service';
import { RegistryService } from '../_services/registry.service';
import { labTestModel } from '../common/objectModels/labTestModel';
import { HttpClient } from '@angular/common/http';
import * as AppUtils from '../common/app.utils';
import Swal from 'sweetalert2';
import * as moment from 'moment';
import { SharedService } from '../_services/shared.service';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { FormArray } from '@angular/forms';
import * as CommonConstants from './../_helpers/common.constants';
import { formatDate } from '@angular/common';
import { GeneticService } from '../genetic-blood-disorder/genetic.service';

@Component({
  selector: 'app-complication',
  templateUrl: './complication.component.html',
  styleUrls: ['./complication.component.scss'],




})
export class ComplicationComponent implements OnInit {
  @Input() complicationForm: FormGroup;
  @Output() uploaded = new EventEmitter<string>();
  complicationList: any[];
  selectedDate: any;
  delRow: any;
  complicationMastList: any;
  complicationsFrequencyList: any;
  public rgTbGenComplication: any = [];
  compFg: any = [];
  loginId: any;
  today = new Date();
  currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
  constructor(private _masterService: MasterService, private _http: HttpClient, private formBuilder: FormBuilder, private _geneticService: GeneticService) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser['person'].perscode
    //// this.initializeFormGroup();
  }

  ngOnInit() {

    this.complicationForm = this.formBuilder.group({
      // 'icd': [null],
      // 'remarks': [""],
      rgTbGenComplication: this.formBuilder.array([]),

    }),

      this._masterService.getGenComplicationMast().subscribe(response => {
        this.complicationMastList = response.result;
      });

    this._geneticService.getGeneticComplicationsFrequency().subscribe(response => {
      this.complicationsFrequencyList = response.result;
    });

  }




  private initializeFormGroup() {

  }

  ///////////////////P DATA TABLE



  onAddNewComp() {
    this.addNewCompl('', '', '', this.loginId, this.currentDate, false, '', ''); //add new empty row with uneditable 
    this.compFg[this.compFg.length - 1].isEditable = true;          //editable last entering row
  }


  addNewCompl(runid: any = null, compId: any = null, remarks: any = null, enteredBy: any = null, enteredDt: any = null, isEditable: any = false,  frequency: any = null,  onsetDt: any = null): void {
    this.rgTbGenComplication = this.complicationForm.get('rgTbGenComplication') as FormArray;

    this.compFg = Object.assign([], this.rgTbGenComplication.value);
    const familyHistItem: any = this.createCOPMtem(runid, compId, remarks, enteredBy, enteredDt, isEditable,  frequency, onsetDt);
    this.rgTbGenComplication.push(this.createCompGrpItem(familyHistItem));

    this.compFg.push(familyHistItem);
    //this.compFg[this.compFg.length - 1].isEditable = true;
  }

  createCompGrpItem(familyHistItem: any): FormGroup {
    return this.formBuilder.group(familyHistItem);
  }

  createCOPMtem(runid: any = null, compId: any = null, remarks: any = null, enteredBy: any = null, enteredDt: any = null, isEditable: any = false,  frequency: any = null,  onsetDt: any = null) {
    return {
      runid: runid,
      compId: compId,
      remarks: remarks,
      enteredBy: enteredBy,
      enteredDt: enteredDt,
      isEditable: isEditable,
      frequency: frequency,
      onsetDt: onsetDt,
    };
  }

  getComplicationName(compId) {
    if (compId) {
      return this.complicationMastList.filter(s => s.id == compId).map(s => s.description)[0];
    }
  }

  getComplicationsFrequencyName(frequency) {
    if (frequency){
     return this.complicationsFrequencyList.filter(s => s.id == frequency).map(s => s.description)[0];
    }

  }
  ///////////////////P DATA TABLE


  addNewComplication() {
    if (!this.complicationList) {
      this.complicationList = [];
    }
    this.complicationList.push({ complication: '', remarks: '' });
    this.complicationList[this.complicationList.length - 1].isEditable = true;
  }

  onRowEditInit(row: any) {
    //this.complicationList.filter(row => row.isEditable).map(r => { r.isEditable = false; return r });
    row.isEditable = true;
    this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }
  onRowEditSave(row: any) {
    let rowIndex = this.compFg.indexOf(row);
    this.compFg[rowIndex] = this.complicationForm.value.rgTbGenComplication[rowIndex];
    let data = this.compFg[rowIndex];
    data.complicationName = this.complicationMastList.filter(s => s.id == data.compId).map(s => s.description)[0];
    data.frequencyName = this.complicationsFrequencyList.filter(s => s.id == data.frequency).map(s => s.description)[0];
    data.entryDate = moment(this.selectedDate, "DD-MM-YYYY").format();
    data.isEditable = false;

  }

  delete(row: any) {

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (!result.dismiss) {

        this.rgTbGenComplication = this.complicationForm.get('rgTbGenComplication') as FormArray;
        this.delRow = this.compFg.indexOf(row);
        this.compFg.splice(this.delRow, 1);
        this.rgTbGenComplication.removeAt(this.delRow);

      }
    })




  }

  getDataFromAlshifa(sendingData: any = 0) {
    this.compFg = sendingData;
  }

  clear() {
    this.compFg = [];
    this.complicationForm.reset();

    this.complicationForm = this.formBuilder.group({
      rgTbGenComplication: this.formBuilder.array([]),
    })
  }


}