import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as AppUtils from '../../common/app.utils';




@Injectable({
  providedIn: 'root'
})
export class AncRegisterService {

  constructor(private http: HttpClient) { }


    getAncRegisterByAncNo(ancNo: any): Observable<any> {
      return this.http.get(AppUtils.GET_ANC_REGISTER_BY_ANC_NO, {
        params: new HttpParams().set("ancNo", ancNo)
      })
    }

    getAncRegisterByCivilId(civilId: any): Observable<any> {
      return this.http.get(AppUtils.GET_ANC_REGISTER_BY_CIVILID, {
        params: new HttpParams().set("civilId", civilId)
      })
    }

    getVwAncDtlsByAncNo(ancNo: any): Observable<any> {
      return this.http.get(AppUtils.GET_VW_ANC_DTLS_BY_ANC_NO, {
        params: new HttpParams().set("ancNo", ancNo)
      })
    }


    getRequestAncDtlsByAncNo(ancNo: any): Observable<any> {
      return this.http.get(AppUtils.GET_VW_ANC_REQUEST_DTLS_BY_ANC_NO, {
        params: new HttpParams().set("ancNo", ancNo)
      })
    }

    

    saveAncRegister(data: any): Observable<any> {
      return this.http.post(AppUtils.SAVE_ANC_REGISTER, data);
    }

    getRegister(regNo, regType, civilId) {
      return this.http.get(AppUtils.GET_ANC_REGISTER, {
        params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_ANC.toString()).set("civilId", civilId)
      })
    }

  
  }

