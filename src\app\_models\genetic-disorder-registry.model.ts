
import { TbPatientInfo } from './patient-info.model';
import {Diagnosis} from '../common/objectModels/diagnosis-model';
import {medictionModel} from '../common/objectModels/mediction-model';
import {labTestModel} from '../common/objectModels/labTestModel';
import {TestResults} from '../_models/test-result.model';
import {SurgeryDtls} from '../_models/surgery-dtls.model';
// import {genDisorderDtls} from '../_models/gen-disorder-dtl.model';
import {BldTransDtls} from '../_models/bld-trans-dtls.model';
import {FamilyHistory} from '../_models/family-history.model';
import {LabTests} from '../_models/lab-tests.model';
import {GenComlication} from '../_models/gen-comlication.model';
import {VaccinationInfo} from '../_models/vaccination-info.model';
export class GeneticDisorderRegistry {
    public centralRegNo: number;
    public activeYn: string;
    public patientID: number;
    public completedOn: Date;
    public createdBy: number;
    public createdOn: Date;
    public   instRegDate: Date;
    public  modifiedBy: number;
    public  modifiedOn: Date;
    public  regInst: number;
    public registerType: number;
    public localRegReferance: string;
    public rgTbPatientInfo: TbPatientInfo;
    public diagnosis: Array<Diagnosis>;
    public rgTbTestResults: Array<TestResults>;
    public rgTbSurgeryDtls: Array<SurgeryDtls>;
    public rgTbMedicineDtls: Array<medictionModel>;
    // public rgTbGenDisorderDtls: Array<genDisorderDtls>;
    public rgTbBldTransDtls: Array<BldTransDtls>;
    public rgTbFamilyHistory: Array<FamilyHistory>;
    public rgTbLabTests: Array<labTestModel>;
    public rgTbGenComplication: Array<GenComlication>;
    public rgTbVaccinationInfo: Array<VaccinationInfo>;

    
  public discoderType: any;
  /// public genotype: string;
  public remarks: any;
  public genotypeBase: any;
  public transfusedDate: any;
  public transfusedInst: any;
  public enteredBy: any;
  public orderBy: any;
  public releasedDate: any;
  public instCode: any;
  public genotype: any;


} 

