import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import * as AppUtils from '../common/app.utils';
import { RenalDonor } from './../_models/renal-donor.model';
import { SharedService } from '../_services/shared.service';
import { CaseDetails, LiverIndication, LiverRegister, ResultDecorator, LiverCaseDetailsDto, LiverComplicationMasterDto, SubItem, LiverCirrhosisStage, LiverComplication, LiverComplicationSave, LiverComplicationSubItem } from 'src/app/_models/liverTransplant.model';
import { RenalDonorPatient } from '../_models/renal_donor_patient.model';
import { liverTransplantFollowupModel } from '../common/objectModels/liver-transplant-follow-up-model';
@Injectable()

export class LiverService {
  [x: string]: any;
  liverComplicationMastList = new BehaviorSubject<LiverComplicationMasterDto[]>(null);
  constructor(private _http: HttpClient, private _sharedService: SharedService) { }

  //LIVER Dashboard

  getCompareHlaScore(regNo, donorId) {
    return this._http.get(AppUtils.GET_COMPARE_HLA_SCORE, { params: new HttpParams().set("regNo", regNo).set("donorId", donorId) })

  }

  updateLiverDonorPatient(donorID, relationType, regNo) {
    return this._http.get(AppUtils.UPDATE_LIVER_DONOR_PATIENT, { params: new HttpParams().set("donorID", donorID).set("relationType", relationType).set("regNo", regNo) })
  }

  getDashboard(): Observable<any> {
    return this._http.get(AppUtils.LIVER_DASHBOARD);
  }

  saveTissueTypeList(data: any): Observable<any> {
    return this._http.post(AppUtils.SAVE_HLA_LIST_RETURN_RUN_ID, data);
  }

  getComplications(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_COMPLICATION_MAST);
  }

  getLiverProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_PROCEDURE_MAST);
  }

  getLiverDonorProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_DONOR_PROCEDURE_MAST);
  }

  getCurrentManagement(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LIVER_CURR_MGMT_MAST);
  }
  //liver
  saveLiver(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_REGISTRY, data);
  }

  //liver
  saveMainCaseDetails(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_CASE_DETAILS, data);
  }

  saveLiverTransplant(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_TRANSPLANT, data);
  }

  getLiverCase(regNo, regType, civilId) {
    return this._http.get(AppUtils.GET_LIVER_CASE_DETAILS, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_LIVER.toString()).set("civilId", civilId)
    })
  }

  getLiverTransplant(regNo, regType, civilId) {
    return this._http.get(AppUtils.GET_LIVER_TRANSPLANT, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_LIVER.toString()).set("civilId", civilId)
    })
  }


  getLiverCaseOrg(liverId: number): Observable<LiverCaseDetailsDto> {
    return this.http.get(AppUtils.GET_LIVER_CASE_DETAILS);
  }


  getLiver(regNo, regType, civilId) {
    return this._http.get(AppUtils.GET_LIVER_REGISTRY, {
      params: new HttpParams().set("centralRegNo", regNo).set("regType", AppUtils.REG_TYPE_LIVER.toString()).set("civilId", civilId)
    })
  }
  // liver listing
  getLiverListing(date): Observable<any> {
    return this._http.post(AppUtils.SEARCH_LIVER, date);
  }

  //LIVER Waiting List

  public getLiverWaitingList(regNo) {
    return this._http.get(AppUtils.FIND_LIVER_WAITING_LIST_RESULT, { params: new HttpParams().set("centralRegNo", regNo) })
  }

  public saveLiverWaitingList(saveList) {
    return this._http.post(AppUtils.SAVE_LIVER_WAITING_LIST, saveList)
  }


  public deleteLiverWaitingList(runid) {
    return this._http.get(AppUtils.DELETE_LIVER_WAITING_LIST, { params: new HttpParams().set("runId", runid) })
  }
  //Transplant list
  getLiverTransplantFollowUp(body): Observable<any> {
    return this._http.post(AppUtils.GET_LIVER_TRANSPLANT_FOLLOWUP, body);
  }

  getLiverTransplantFollowUpById(fwUpId): Observable<any> {
    return this._http.post(AppUtils.GET_LIVER_TRANSPLANT_FOLLOWUP_BY_ID, fwUpId);
  }

  addLiverTransplantFollowUp(dto: any): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_TRANSPLANT_FOLLOWUP, dto);
  }

  addLiverFollowUpComplication(dto: any): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_TRANSPLANT_COMPLICATION, dto);
  }

  //DIAGNOSIS

  public deleteDiagnosis(runId) {
    return this._http.get(AppUtils.LIVER_DELETE_DIAGNOSIS, { params: new HttpParams().set("runId", runId) })
  }


  //   //Scores
  //   saveScores(data): Observable<any> {
  //     return this._http.post(AppUtils.SAVE_LIVER_SCORES, data);
  //   }

  //   getScores(regNo) {
  //     return this._http.get(AppUtils.GET_LIVER_SCORES, { params: new HttpParams().set("centralRegNo", regNo) })
  //   }


  //Case Details
  SaveCaseDetails(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_CASE_DETAILS, data);
  }



  getCaseDetails(regNo) {
    return this._http.get(AppUtils.GET_LIVER_CASE_DETAILS, { params: new HttpParams().set("regNo", regNo).set("regType", AppUtils.REG_TYPE_LIVER.toString()) })
  }


  // Brain Death Determination
  //   getBrainDeathExam() {
  //     return this._http.get(AppUtils.LIVER_BRAIN_DEATH_EXAM_PARA);
  //   }
  //   saveBrainDeathDetermination(data): Observable<any> {
  //     return this._http.post(AppUtils.SAVE_LIVER_BRAIN_DEATH_REGISTER, data);
  //   }
  //   getBrainDeathDetermination(civilId ,callType) {
  //     return this._http.get(AppUtils.GET_LIVER_BRAIN_DEATH_REGISTER, { params: new HttpParams().set("civilId", civilId).set("callType", callType) });
  //   }

  getPatientsDeceasedDonor(civilId) {
    return this._http.get(AppUtils.LIVER_GET_PATIENTS_DECEASED_DONOR, { params: new HttpParams().set("civilId", civilId) })
  }

  getPatientsLiverDeceasedDonor(kidneyDonorId) {
    return this._http.get(AppUtils.LIVER_GET_PATIENTS_LIVER_DECEASED_DONOR, { params: new HttpParams().set("renalDonorId", kidneyDonorId) });
  }

  // updategetgetgetRenalWaitingListWaitingListWaitingListDonorPatient(donorID,regNo) {
  //   return this._http.get(AppUtils.UPDATE_LIVER_DONOR_PATIENT, { params: new HttpParams().set("donorID", donorID).set("regNo", regNo) })
  // }


  getAllLiverDonorPatient(): Observable<any> {
    return this._http.get(AppUtils.GET_ALL_LIVER_DONOR_PATIENT);
  }

  getDonorDetails(Data, Type) {
    if (Type == 'civilId') {
      return this._http.get(AppUtils.REG_LIVER_DTL + Data);
    } else {
      return this._http.get(AppUtils.REG_LIVER_DTL_BY_ID + Data);
    }
  }
  getDonorByCivilId(Data) {
    return this._http.get(AppUtils.REG_LIVER_DTL + Data);
  }

  getDonorByDonorID(Data) {
    return this._http.get(AppUtils.REG_LIVER_DTL_BY_ID + Data);
  }

  saveLiverDonorPatient(data: RenalDonorPatient): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_DONOR_PATIENT, data);
  }

  saveDonor(data: RenalDonor): Observable<any> {
    return this._http.post(AppUtils.SAVE_LIVER_DONOR, data);
  }

  saveLiverDonor(data): Observable<any> {

    return this._http.post(AppUtils.SAVE_LIVER_DONOR, data);
  }

  saveLiverDonorRegistry(data): Observable<any> {

    return this._http.post(AppUtils.SAVE_LIVER_DONOR_REGISTRY, data);
  }

  fetchAllDonorLabFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_LAB_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);

  }

  fetchAllDonorSurgeryFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_SURGERY_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);

  }

  fetchAllDonorVaccineFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_VACCINE_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);

  }

  fetchAllDonorProcedureFromShifa(civilId: any, estCode: any): Observable<any> {
    return this._http.get(AppUtils.FETCH_ALL_LIVER_DONOR_PROCEDURE_FROM_ALSHIFA + "?estCode=" + estCode + "&civilId=" + civilId);
  }

  getLungDonorProcedure(): Observable<ResultDecorator> {
    return this._http.get<ResultDecorator>(AppUtils.GET_LUNG_DONOR_PROCEDURE_MAST);
  }

  getPatientsDonorbyRelationType(relationType: number, kidneyDonorId: number): Observable<any> {
    return this._http.get(AppUtils.GET_LIVER_PATIENTS_DONOR_BY_RELATION_TYPE, {
      params: new HttpParams().set("relationType", relationType.toString()).set("kidneyDonorId", kidneyDonorId.toString())
    });
  }

}

