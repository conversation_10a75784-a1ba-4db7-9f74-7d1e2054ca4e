.card-minht {
    min-height: 125px;
}

.radio-list {
    padding: 6px;
}

.color-div {
    width: 160px;
}

.mal-ico {
    width: 15px;
    height: 15px;
    border-radius: 10px;
    display: inline-block;
    margin: 8px 15px 0 0;
}

.red-color {
    border: 1px solid rgb(243, 36, 36);

    &.active {
        background: rgb(243, 36, 36);
    }
}

.pink-color {
    border: 1px solid rgb(175, 141, 164);

    &.active {
        background: rgb(175, 141, 164);
    }
}

.orng-color {
    border: 1px solid rgb(228, 107, 51);

    &.active {
        background: rgb(228, 107, 51);
    }
}

.ylw-color {
    border: 1px solid rgb(255, 196, 70);

    &.active {
        background: rgb(255, 196, 70);
    }
}

.grn-color {
    border: 1px solid rgb(50, 155, 41);

    &.active {
        background: rgb(50, 155, 41);
    }
}

.table {
    margin-top: 1rem;
}

.follow-up-list {
    display: flex;
    flex-direction: column-reverse !important;
    border: 1px solid #e3e3e3;
    border-bottom: 0;

    a {
        cursor: pointer;
        padding: 8px;
        border-bottom: 1px solid #e3e3e3;
        border-left: 3px solid transparent;
        text-decoration: none;
        color: #444;

        &:hover {
            background: #f7f7f7;
            color: #d9534f;
            font-family: var(--font-bold);
        }

        &.active {
            background: #d9534f1c !important;
            color: #d9534f !important;
            font-family: var(--font-bold);
            border-left-color: #d9534f;
            font-family: var(--font-bold);
        }
    }
}

.add-new {
    width: 100%;
    margin-bottom: 10px;
}

h6 {
    font-family: var(--font-bold);
}

.divider {
    border-top: 1px dashed #ccc;
}

.btn-box {
    margin: 0 10px;
}

.action-btn {
    border: 0;
    background: #d9534f;
    color: white;
    font-size: 14px;
    margin-top: 5px;
    padding: 7px;
    margin-right: 8px;
    border-radius: 3px;
}

.h-30 {
    height: 30px;
}

::ng-deep .ng-custom {
    .ng-select-container {
        height: auto !important;

        .ng-value-container {
            height: auto !important;

            .ng-value {
                white-space: normal !important;
            }
        }
    }
}

.table-scroll {
    max-height: 300px;
    overflow: auto;
    margin-bottom: 20px;
}

.radio-label {
    display: inline-block;
    margin-right: 10px;
}

.radio-color {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #ccc;
    margin-right: 5px;
    cursor: pointer;
}

//   input[type="radio"] {
//     display: none;
//   }

//   input[type="radio"]:checked + .radio-color {
//     border-color: #000;
//   }

.followup-nav {
    .list-group-item {
        padding: 5px;
        cursor: pointer;
        text-align: center;

        &.active {
            background: #fffbfb;
            border-color: #d9534f;
            color: #d9534f;
        }

        &:hover {
            background: #f7f7f7;
        }
    }
}

.added-list {
    position: relative;

    button {
        // position: absolute;
        // right: 5px;
        float: right;
        border: 0;
        background: none;
        color: #d9534f;
    }
}

.spicfy {
    background-color: rgba(253, 246, 245, 0.994);
}

.disableCheckBox {
    pointer-events: none;
}

.custom-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    width: 300px;
}

.card-header {
    margin-bottom: 10px;
}

.card-tabs {
    display: flex;
    justify-content: space-between;
}

.card-tabs button {
    padding: 10px;
    border: none;
    background-color: #f1f1f1;
    cursor: pointer;
    width: 100%;
}

.card-tabs button.active {
    background-color: #d9534f;
    color: white;
}

.card-tabs button:hover {
    background-color: #ddd;
}

.card-content {
    margin-top: 5px;
}

::ng-deep .accordion .card .active .pagination {
    display: flex !important;
}




///////////////////////////
/// 
/// 
/// 
#chartjs-tooltip {
    opacity: 0;
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 3px;
    -webkit-transition: all .1s ease;
    transition: all .1s ease;
    pointer-events: none;
    transform: translate(-50%, 0);
    padding: 10px;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.3);
}

#chartjs-tooltip.above {
    transform: translate(-50%, -100%);
}

#chartjs-tooltip.below {
    transform: translate(-50%, 0);
}

#chartjs-tooltip.no-transform {
    transform: none;
}

@media print {

    html,
    body {
        height: 100vh;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden;
    }
}

.icon-btn {
    color: #d9534f;
    font-size: 18px;
}

.custom-mcard {
    .mcard-header {
        margin: 10px;
    }

    .mcard-body {
        background: #f3f3f3;
        border-radius: 5px;
        padding: 10px;
    }
}

::ng-deep .tab-view {
    margin: 15px 0;

    tabset {
        .nav {
            .nav-item {
                margin: 0 1px;

                a {
                    color: #495057;
                    background: #f5f5f5 !important;

                    &.active {
                        font-family: var(--font-bold);
                        background: #fff !important;
                    }
                }
            }
        }

        tab {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: 0;
            margin: 0 1px;
            border-radius: 5px;
            border-top-left-radius: 0;

            table {
                margin-top: 0 !important;
            }
        }
    }
}