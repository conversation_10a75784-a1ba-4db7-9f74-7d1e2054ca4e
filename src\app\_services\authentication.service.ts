import { Router } from '@angular/router';
import { User } from '../_models/user.model';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import { environment } from './../../environments/environment';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { catchError, map } from 'rxjs/operators';
import * as CommonConstants from './../_helpers/common.constants';

@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {


  private messageSource = new BehaviorSubject<User>(new User());
  private aisKey = new BehaviorSubject<String>(new String());
  currentKey = this.aisKey.asObservable();
  navigationData: any;
  authenticated: boolean = false;

  private currentUserSubject: BehaviorSubject<User>;
  public currentUser: Observable<User>;

  constructor(private http: HttpClient, private router: Router) {
    this.currentUserSubject = new BehaviorSubject<User>(JSON.parse(localStorage.getItem('currentUser')));
    this.currentUser = this.currentUserSubject.asObservable();
  }

  setUser(user: User) {
    this.messageSource.next(user);
    this.currentUser = this.messageSource.asObservable();
  }

  public get currentUserValue(): User {
    return this.currentUserSubject.value;
  }
  userAuthentication(userName, password) {
    const data = '?grant_type=password' + '&client_id=' + CommonConstants.CLIENT_ID + '&username=' + userName + '&password=' + password;
    return this.http.get(environment.centralAuthenticationHost + CommonConstants.AUTH_TOKEN_API + data);
  }

  getUserDetails(): Observable<any> {
    return this.http.get(environment.centralAuthenticationHost + CommonConstants.FIND_USER_BY_LOGIN_AND_SYSTEM + '?systemId=' +
      CommonConstants.SYSTEM_ID).pipe(map(res => {
        localStorage.setItem('currentUser', JSON.stringify(res));

        this.currentUserSubject.next(<any>res);
        return res;
      }),
        catchError(this.handleError)
      );
  }

  setAlshifaCurrentUserSubject(currentUser: any) {
    this.currentUserSubject.next(<any>currentUser );
    this.messageSource.next(currentUser);
    this.currentUser=  this.messageSource.asObservable();
  }


  logout() {
    localStorage.clear();
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }


  private handleError(error: HttpErrorResponse | any) {
    console.error('ApiService::handleError', error);
    return throwError(error);
  }
  getUserId(): number | null {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    if (currentUser && currentUser.id) {
      return currentUser.id;
    }
    return null;
  }
}
