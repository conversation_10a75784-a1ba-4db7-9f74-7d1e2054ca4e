import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from 'rxjs/Observable';
import * as AppUtils from '../common/app.utils';


@Injectable({
    providedIn: 'root'
  })
  
  export class AncService {
    constructor(private http: HttpClient) { }
    
    getAncDashboard(): Observable<any> {
        return this.http.get(AppUtils.ANC_DASHBOARD);
      }

      getAncLabInvest(): Observable<any> {
   
        return this.http.get(AppUtils.GET_ANC_LAB_INVEST);
      }
  
      getAncMidicalHistory(): Observable<any> {
     
        return this.http.get(AppUtils.GET_ANC_MIDICAL_HISTORY);
      }
  
      getAncBirtSpacing (): Observable<any> {
     
        return this.http.get(AppUtils.GET_ANC_BIRTH_SPACING);
      }
      getAbortionType (): Observable<any> {
     
        return this.http.get(AppUtils.GET_ABORTION_TYPE);
      }
  
      getDeliveryMode (): Observable<any> {
     
        return this.http.get(AppUtils.GET_DELIVERY_MODE);
      }
  
      getPerinealTears (): Observable<any> {
     
        return this.http.get(AppUtils.GET_PERINEAL_TEARS);
      }
  
      getDeliveryType (): Observable<any> {
     
        return this.http.get(AppUtils.GET_DELIVERY_TYPE);
      }
  
      getPregnancyOutcome (): Observable<any> {
     
        return this.http.get(AppUtils.GET_PREGNANCY_OUTCOME);
      }
  
  
      getMensturalCycle (): Observable<any> {
     
        return this.http.get(AppUtils.GET_MENSTURAL_CYCLE);
      }


  }