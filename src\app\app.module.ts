import { DatePipe } from '@angular/common';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
/* import { AlertComponent } from './_components/alerts/alert.component'; */
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from "@ng-select/ng-select";
import { AgGridModule } from 'ag-grid-angular';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ModalModule } from 'ngx-bootstrap/modal';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ToastrModule } from 'ngx-toastr';
import { ChartModule } from 'primeng/chart';

import { SelectButtonModule } from 'primeng/selectbutton';

import {
  AutoCompleteModule, ButtonModule, CalendarModule, CheckboxModule, DataTableModule,
  InputSwitchModule, InputTextModule, PaginatorModule, ProgressSpinnerModule, RadioButtonModule, SliderModule, TooltipModule
} from 'primeng/primeng';
import { TableModule } from 'primeng/table';
import { AlShifaLoginComponent } from './alshifa/alShifaLogin.component';
import { AlShifaLoginService } from './alshifa/alShifaLogin.service';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AuthTokenService } from './auth/auth-token.service';
import { TokenInterceptor } from './auth/token.interceptor';
import { clinicalDetailsComponent } from './_comments/clinical-details/clinical-details.component';
import { YNCellRenderer } from './common/agGridComponents/ag-YN-select-component';
import { ButtonRendererComponent } from './common/agGridComponents/ButtonRendererComponent';
import { ButtonOrganDonorRendererComponent } from './common/agGridComponents/ButtonOrganDonorRendererComponent';
import { GridNgSelectDataComponent } from './common/agGridComponents/grid-ngSelect-data.component';
import { SliderService } from './common/slider.service';
import { ComplicationComponent } from './complication/complication.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { DraggableModalComponent } from './draggableModel/draggable-modal.Component';
import { ElderlyDashboardComponent } from './elderly/elderly-dashboard/elderly-dashboard.component';
import { ElderlyListingComponent } from './elderly/elderly-listing/elderly-listing.component';
import { ElderlyRegisterComponent } from './elderly/elderly-register/elderly-register.component';
import { ElderlyService } from './elderly/elderly.service';
import { BloodDisorderComponent } from './genetic-blood-disorder/blood-disorder-registration/blood-disorder.component';
import { GeneticBloodDisorderDashboardComponent } from './genetic-blood-disorder/genetic-blood-disorder-dashboard/genetic-blood-disorder-dashboard.component';
import { GeneticBloodDisorderListingComponent } from './genetic-blood-disorder/genetic-blood-disorder-listing/genetic-blood-disorder-listing.component';
import { HomeComponent } from './home/<USER>';
import { LabResultsComponent } from './_comments/lab-result-listing/lab-result-listing.component';
import { LoginComponent } from './login/login.component';
import { MedicationComponent } from './_comments/medication/medication.component';
import { PatientDetailsComponent } from './_comments/patient-details/patient-details.component';
import { SortListPipe } from './pipes/sort.list.pipe';
import { DonorInformationListingComponent } from './renal/donor-information-listing/donor-information-listing.component';
import { DonorInformationComponent } from './renal/donor-information/donor-information.component';
import { DonorService } from './renal/donor.service';
import { KidneyDiseaseStagesComponent } from './renal/kidney-disease-stages/kidney-disease-stages.component';
import { OrganDonorDashboardComponent } from './renal/organ-donor/organ-donor-dashboard/organ-donor-dashboard.component';
import { OrganDonorListingComponent } from './renal/organ-donor/organ-donor-listing/organ-donor-listing.component';
import { OrganDonorService } from './renal/organ-donor/organ-donors.service';
import { RenalDashboardComponent } from './renal/renal-dashboard/renal-dashboard.component';
import { RenalListingComponent } from './renal/renal-listing/renal-listing.component';
import { RenalRegisterComponent } from './renal/renal-register/renal-register.component';
import { RenalWaitingListListingComponent } from './renal/renal-waiting-list-listing/renal-waiting-list-listing.component';
import { RenalWaitingListComponent } from './renal/renal-waiting-list/renal-waiting-list.component';
import { TissueTypingComponent } from './renal/tissue-typing/tissue-typing.component';
import { TransplantFollowUpComponent } from './renal/transplant-follow-up/transplant-follow-up.component';
/* import { BootstrapModalModule } from 'ng2-bootstrap-modal'; */
import { SearchComponent } from './search/search.component';
import { surgeryComponent } from './_comments/surgery/surgery.component';
import { TestComponent } from './test/test.component';
//import { UserSearchComponent } from './user-search/user-search.component';
import { UsersManagmentComponent } from './users/users.component';
import { VaccinationComponent } from './_comments/vaccination/vaccination.component';
import { FilterPipe } from './_pipes/filter.pipe';
import { SortByPipe } from './_pipes/sort.pipe';
import { MasterService } from './_services/master.service';
import { RegistryService } from './_services/registry.service';
import { SharedService } from './_services/shared.service';
import { DeathDetailsComponent } from './_comments/death-details/death-details.component';
import { DiabeticDashboardComponent } from './diabetic/diabetic-dashboard/diabetic-dashboard.component';
//import { DiabeticDashboardComponent } from './diabetic/diabetic-dashboard/diabetic-dashboard.component';
import { DiabeticListingComponent } from './diabetic/diabetic-listing/diabetic-listing.component';
import { DiabeticRegistryComponent } from './diabetic/diabetic-registry/diabetic-registry.component';
import { BrainDeathRegistrationComponent } from './renal/brain-death/brain-death-registration/brain-death-registration.component';

import { BrainDeathListingComponent } from './renal/brain-death/brain-death-listing/brain-death-listing.component';
import { VaccineListingComponent } from './immunization/vaccine-listing/vaccine-lisiting.component';
import { VaccineRegisterComponent } from './immunization/vaccine-register/vaccine-register.component';

import { AncRequestRegistrationComponent } from './ancRequest/anc-request-registration.component';
// import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { GridDateComponent } from './common/agGridComponents/grid-date.component';
import { AncRegisterComponent } from './anc/anc-register/anc-register.component';
import { AncListingComponent } from './anc/anc-listing/anc-listing.component';
import { CaseDetailsComponent } from './liver/case-details/case-details.component';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { LiverRegisterComponent } from './liver/liver-register/liver-register.component';
import { LiverListingComponent } from './liver/liver-listing/liver-listing.component';
import { LiverWaitingListComponent } from './liver/liver-waiting-list/liver-waiting-list.component';
import { LiverWaitingListListingComponent } from './liver/liver-waiting-list-listing/liver-waiting-list-listing.component';
import { LiverDonorInformationListingComponent } from './liver/donor-information-listing/donor-information-listing.component';
import { LiverDonorInformationComponent } from './liver/donor-information/donor-information.component';
import { LiverTranplantComponent } from './liver/liver-transplant/liver-transplant.component';
import { LiverDashboardComponent } from './liver/liver-dashboard/liver-dashboard.component';
import { LiverDonorService } from './liver/donor.service';
import { PreparingLiverWaitingListComponent } from './liver/preparing-liver-waiting-list/preparing-liver-waiting-list.component';

import { TabsModule } from 'ngx-bootstrap/tabs';
import { AncRegisterListingComponent } from './anc/anc-register-listing/anc-register-listing.component';
import { AsthmaListingComponent } from './asthma/asthma-listing/asthma-listing.component';
import { AsthmaDashboardComponent } from './asthma/asthma-dashboard/asthma-dashboard.component';
import { TargetListingComponent } from './immunization/target-listing/target-lisiting.component';
import { ImmunizationDashboardComponent } from './immunization/Dashboard/immunization-dashboard.component';
import { AncDashboardComponent } from './anc/Dashboard/anc-dashboard.component';
import { ChildListingComponent } from './child/child-listing.component';
import { ChildNutriitonRegisterComponent } from './child-nutrition/child-nutriiton-register/child-nutriiton-register.component';
import { ChangePasswordService } from './changePassword/change-password.service';
import { ChangePassowrdComponent } from './changePassword/change-passowrd.component';
import { ChildNutritionListingComponent } from './child-nutrition/child-nutrition-listing/child-nutrition-listing.component';
import { NutDashboardComponent } from './child-nutrition/Dashboard/nut-dashboard.component';
import { TransplantRegistrationComponent } from './transplant-registration/transplant-registration.component';


import { CommonModule } from '@angular/common';
import { CorneaTransplantComponent } from './cornea/cornea-transplant/cornea-transplant.component';

import { CorneaRegistryComponent } from './cornea/cornea-registry/cornea-registry.component';
import { CorneaDashboardComponent } from './cornea/cornea-dashboard/cornea-dashboard.component';
import { LiverTransplantFollowUpComponent } from './liver/liver-transplant-follow-up/liver-transplant-follow-up.component';


import { AgGridAngular } from 'ag-grid-angular';

import { MentalRegisterComponent } from './Mental/mental-register/mental-register.component';
import { DeceasedDonorListingComponent } from './deceased-donor/deceased-donor-listing/deceased-donor-listing.component'; import { CorneaRequestListingComponent } from './cornea/cornea-request-listing/cornea-request-listing.component';
import { DeceasedDonorDashboardComponent } from './deceased-donor/deceased-donor-dashboard/deceased-donor-dashboard.component'; import { CorneaTransplantListingComponent } from './cornea/cornea-transplant-listing/cornea-transplant-listing.component';
import { DeceasedDonorRegisterComponent } from './deceased-donor/deceased-donor-register/deceased-donor-register.component';
import { LungRegistryComponent } from './lung/lung-registry/lung-registry.component';
import { LungCaseDetailsComponent } from './lung/lung-case-details/lung-case-details.component';
import { LungTranplantComponent } from './lung/lung-transplant/lung-transplant.component';
import { LungListingComponent } from './lung/lung-listing/lung-listing.component';
import { LungTransplantListingComponent } from './lung/lung-transplant-listing/lung-transplant-listing.component';
import { LungDashboardComponent } from './lung/lung-dashboard/lung-dashboard.component';
import { LungDonorListComponent } from './lung/lung-donor-list/lung-donor-list.component';
import { LungDonorRegistryComponent } from './lung/lung-donor-registry/lung-donor-registry.component';
import { HeartCaseDetailsComponent } from './heart/heart-case-details/heart-case-details.component';

import { HeartRegistyComponent } from './heart/heart-registy/heart-registy.component';
import { HeartRegistyListComponent } from './heart/heart-registy-list/heart-registy-list.component';
import { HeartDonorRegistryComponent } from './heart/heart-donor-registry/heart-donor-registry.component';
import { HeartDonorListingComponent } from './heart/heart-donor-listing/heart-donor-listing.component';
import { HeartDashboardComponent } from './heart/heart-dashboard/heart-dashboard.component';
import { LungTissueTypingComponent } from './lung/tissue-typing/lung-tissue-typing.component';

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    HomeComponent,
    ElderlyRegisterComponent,
    PatientDetailsComponent,
    SortByPipe,
    /*  AlertComponent, */
    DashboardComponent,
    FilterPipe,
    UsersManagmentComponent,
    SearchComponent,
    ElderlyDashboardComponent,
    DonorInformationListingComponent,
    ElderlyListingComponent,
    SearchComponent,
    OrganDonorListingComponent,
    DonorInformationComponent,
    VaccinationComponent,
    SortListPipe,
    RenalWaitingListComponent,
    RenalWaitingListListingComponent,
    RenalRegisterComponent,
    YNCellRenderer,
    GridNgSelectDataComponent,
    ButtonRendererComponent,
    ButtonOrganDonorRendererComponent,

    SearchComponent,
    OrganDonorListingComponent,
    DonorInformationComponent,
    VaccinationComponent,
    SortListPipe,

    RenalWaitingListComponent,
    RenalWaitingListListingComponent,
    RenalRegisterComponent,
    YNCellRenderer,
    GridNgSelectDataComponent,

    ButtonRendererComponent,
    ButtonOrganDonorRendererComponent,

    SearchComponent,
    OrganDonorListingComponent,
    DonorInformationComponent,
    LabResultsComponent,
    VaccinationComponent,
    TestComponent,
    SortListPipe,
    RenalWaitingListComponent,
    RenalWaitingListListingComponent,
    KidneyDiseaseStagesComponent,
    TransplantFollowUpComponent,
    RenalListingComponent,
    TissueTypingComponent,
    RenalDashboardComponent,
    GeneticBloodDisorderListingComponent,
    BloodDisorderComponent,
    clinicalDetailsComponent,
    MedicationComponent,
    surgeryComponent,
    ComplicationComponent,
    SortByPipe,
    AlShifaLoginComponent,
    GeneticBloodDisorderDashboardComponent,
    DraggableModalComponent,
    OrganDonorDashboardComponent,
    DeathDetailsComponent,
    DiabeticDashboardComponent,
    //DiabeticDashboardComponent,
    DiabeticListingComponent,
    DiabeticRegistryComponent,
    BrainDeathRegistrationComponent,
    BrainDeathListingComponent,
    VaccineListingComponent,
    TargetListingComponent,
    ImmunizationDashboardComponent,
    VaccineRegisterComponent,
    AncRequestRegistrationComponent,
    GridDateComponent,
    AncRegisterComponent,
    AncListingComponent,
    CaseDetailsComponent,
    LiverRegisterComponent,
    LiverListingComponent,
    LiverWaitingListComponent,
    LiverWaitingListListingComponent,
    LiverDonorInformationListingComponent,
    LiverDonorInformationComponent,
    LiverDashboardComponent,
    LiverTranplantComponent,
    AncRegisterListingComponent,
    AsthmaListingComponent,
    AsthmaDashboardComponent,
    AncDashboardComponent,
    ChildListingComponent,
    ChildNutriitonRegisterComponent,
    ChangePassowrdComponent,
    ChildNutritionListingComponent,
    NutDashboardComponent,
    TransplantRegistrationComponent,
    DeceasedDonorRegisterComponent,
    DeceasedDonorListingComponent,
    DeceasedDonorDashboardComponent,
    CorneaRegistryComponent,
    CorneaTransplantComponent,
    CorneaDashboardComponent,
    PreparingLiverWaitingListComponent,
    LiverTransplantFollowUpComponent,
    MentalRegisterComponent,
    CorneaRequestListingComponent,
    CorneaTransplantListingComponent,
    LungRegistryComponent,
    LungCaseDetailsComponent,
    LungTranplantComponent,
    LungListingComponent,
    LungTransplantListingComponent,
    LungDashboardComponent,
    LungDonorListComponent,
    LungDonorRegistryComponent,
    HeartCaseDetailsComponent,
    HeartRegistyComponent,
    HeartRegistyListComponent,
    HeartDonorRegistryComponent,
    HeartDonorListingComponent,
    LungTissueTypingComponent,
    HeartDashboardComponent,
    LungTissueTypingComponent
  ],

  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxSpinnerModule,
    HttpClientModule,
    ToastrModule.forRoot(),
    NgbModule,
    CalendarModule,
    NgSelectModule,
    ChartModule,
    SliderModule,
    InputTextModule,
    ButtonModule,
    CommonModule,
    CalendarModule,

    //BootstrapModalModule,

    /*     BootstrapModalModule.forRoot({ container: document.body }), */
    AgGridModule.withComponents([YNCellRenderer, GridNgSelectDataComponent, ButtonRendererComponent, ButtonOrganDonorRendererComponent]),

    /*     BootstrapModalModule.forRoot({ container: document.body }), */
    AgGridModule.withComponents([GridDateComponent]),

    ModalModule.forRoot(),
    NgMultiSelectDropDownModule,
    TableModule,
    BrowserAnimationsModule,
    NgSelectModule,
    CheckboxModule,
    DataTableModule,
    CalendarModule,
    AutoCompleteModule,
    RadioButtonModule,
    PaginatorModule,
    TooltipModule,
    ProgressSpinnerModule,
    InputSwitchModule,
    AccordionModule.forRoot(),
    SelectButtonModule,
    TabsModule.forRoot()


  ],
  providers: [AuthTokenService, MasterService, SharedService, SliderService, ElderlyService, RegistryService, DonorService, AlShifaLoginService, ChangePasswordService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptor,
      multi: true
    }, OrganDonorService, DatePipe],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]

})
export class AppModule { }
