import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HeartCaseDetailsComponent } from './heart-case-details.component';

describe('CaseDetailsComponent', () => {
  let component: HeartCaseDetailsComponent;
  let fixture: ComponentFixture<HeartCaseDetailsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HeartCaseDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HeartCaseDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
