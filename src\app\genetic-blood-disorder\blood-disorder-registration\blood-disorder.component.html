<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Genetic Registry</h6>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>

        </span>
    </div>
</div>
<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0" [destroyOnHide]="false">
    <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head"
                [ngClass]="opened ? 'opened' : 'collapsed'">
                <h6> Patient Details
                    <i class="col-lg-2 col-md-2 col-sm-2  fas fa-male " *ngIf="alive" (click)="updateDeathDetails()"
                    style="color: green; font-size:22px;" title="patient is alive "><span
                        style="padding-left: 5px;"></span></i>
                        <i class="col-lg-2 col-md-2 col-sm-2  fas fa-male " *ngIf="!alive" style="color: black; font-size:22px;"
                        title="patient is dead "><span style="padding-left: 5px;"></span></i>
                </h6>

                <div class="input-group input-group col-md-2 pull-right col-lg-4 col-md-4 col-sm-4">
                    <h6>Hospital Visit Info:</h6>
                    <i class="col-lg-2 col-md-2 col-sm-2 fas fa-procedures " style="color: blue; font-size:16px;"
                        title="# Admissions "><span style="padding-left: 5px;">{{ipdClassCount}}</span></i>
        
                    <i class="col-lg-2 col-md-2 col-sm-2 fas fa-stethoscope " style="color: green; font-size:16px;"
                        title="# OPD Visits "><span style="padding-left: 5px;"> {{opdClassCount}}</span></i>
        
                    <i class="col-lg-2 col-md-2 col-sm-2 fas fa-ambulance" style="color: red; font-size:16px;"
                        title="# ED Visits "><span style="padding-left: 5px;">{{emrClassCount}}</span></i>
                </div>
                <div>
                    <span style="padding-left: 5px; color: green; font-size:18px" class="fas" *ngIf="alive" (click)="updateDeathDetails()">De-Activate</span> 
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                        [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <app-patient-details [submitted]="submitted" [downloadFromShifa]="downloadFromShifa"
            (fetchShifa)="callFetchDataFromAlShifa(0,0,0)" #patientDetails></app-patient-details>
            <!--
                <button (click)="callFetchDataFromAlShifa(0,0)" class="btn btn-sm btn-primary btn5 " title="Download Patient Data from Selected Institute!">Download </button>
            -->
        </ng-template>
    </ngb-panel>
</ngb-accordion>
<div class="text-right pb-2">
</div>

<form [formGroup]="genDisorderForm" (ngSubmit)="submit()">
    <div class="row">
        <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6">
            <h6>Genetic Blood Disorder</h6>
        </div>
    </div>

    <div class="content-wrapper mb-2" *ngIf="! alive">
        <h6 style="font-weight: bold;">Death Details </h6>
        <app-death-details [currentCivilId]="currentCivilId" [estCode]="estCode" [patientId]="patientId" #deathDetails>
        </app-death-details>
    </div>

    <div class=" genetic">
        <div class="content-wrapper mb-2 genetic">
            <!--  Genetic Blood Disorder header details Start  -->
            <div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="row" formGroupName="rgTbGenDisorderDtls">
                                    <div class="col-lg-3 col-md-3 col-sm-3">
                                        <input type="hidden" class="form-control form-control-sm"
                                            formControlName="discoderId">
                                        <div class="form-group">
                                            <label> Disorder Type : </label> <span class="mdtr">*</span>
                                            <select #entryPoint class="form-control form-control-sm"
                                                formControlName="discoderType" [(ngModel)]="setDefultGeneticTypeId">
                                                bloodDisorder
                                                <option selected [value]="null">All</option>
                                                <option [value]="res.geneticTypeId" *ngFor="let res of bloodDisorder">
                                                    {{res.geneticTypeDesc}}</option>
                                            </select>
                                        </div>
                                        <span *ngIf="submitted && f_disorderDtls.discoderType.errors"
                                            class="tooltiptext">{{'Disorder Type
                                            is required'}}</span>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-3">
                                        <div class="form-group">
                                            <label>Date of Diagnosis: </label>
                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                formControlName="dateDiagnosed" [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                                                yearNavigator="true" showButtonBar="true"></p-calendar>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-md-3 col-sm-3">
                                        <div class="form-group">
                                            <label>Date of Onset (Last Attack): </label>
                                            <p-calendar  dateFormat="dd-mm-yy" showIcon=true
                                                formControlName="lastOnsetDate" [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                                                yearNavigator="true" showButtonBar="true"></p-calendar>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="row">
                                    <div class="col-lg-3 col-md-3 col-sm-3">
                                        <div class="form-group">
                                            <label>Diagnosis based on </label>
                                            <div formGroupName="rgTbGenGenotypeDtls">
                                                <ng-multiselect-dropdown formControlName="genotype" [(ngModel)]="setHplc"
                                                    class=" multiappend custom-dropdown custom_color custom_box_color check_box_custom-color"
                                                    [placeholder]="'Add '" [data]="genobase"
                                                    [settings]="genobaseDropdownSettings">
                                                </ng-multiselect-dropdown>
                                            </div>
                                            <span *ngIf="submitted && f_GenGenotypeDtls.genotype.errors"
                                                class="tooltiptext">{{'Diagnosis based on
                                                is required'}}</span>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="rgTbGenDisorderDtls">
                                        <label>Geno Type </label>
                                        <select class="form-control form-control-sm" formControlName="genotype">
                                            <option selected [value]="null">All</option>
                                            <option [value]="res.id" *ngFor="let res of genotype">{{res.description}}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6" formGroupName="rgTbGenDisorderDtls">
                        <div class="form-group">
                            <label>Remarks </label>
                            <textarea class="form-control form-control-sm" formControlName="remarks"
                                style="height: 100px; width: 700px;">
                            </textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!--  Genetic Blood Disorder header details End  -->

            <!-- HPLC Details Start -->
            <div class=" " formGroupName="hplcForm">
                <h6 style="font-weight: bold;">HPLC:</h6>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Doctor : </label>
                                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="orderBy">
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="orderByName" disabled>
                                    <input type="hidden" class="form-control form-control-sm"
                                        formControlName="mohTestCode">
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Release Dt : </label>
                                    <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="releasedDate"
                                        [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Institute : </label>
                                    <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                        placeholder="Select" bindLabel="estName" bindValue="estCode"
                                        formControlName="instCode">
                                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-3" *ngIf="refreshHPLC">
                                <div class="form-group">
                                    <label> </label>
                                    <div class="linkpadding"> <i class="fas fa-info-circle"></i> <a class="link"
                                            (click)="FetchAllHplcFromAlShifa()">
                                            Refrech HPLC</a></div>
                                </div>
                            </div>
                            <div class="col-sm-3" *ngIf="disHplHistorylshifa">
                                <div class="form-group">
                                    <label> </label>
                                    <div class="linkpadding"> <i class="fas fa-info-circle"></i> <a class="link"
                                            (click)="openModal(hplcHistory)">
                                            Other HPLC test results</a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- HPLC Details End -->

            <!-- Diagnostic Details Start rgTbTestResultsComp -->
            <div>
                <p-dataTable [immutable]="true" [value]="diagnosticData" [editable]="true" dataKey="runId"
                    [responsive]="true">
                    <p-column field="testDate" header="HPLC">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="diagnosticForm">
                                <div [formGroupName]="rowIndex">
                                    <input type="hidden" style="width: 100px;" formControlName="runId">
                                    <input type="hidden" style="width: 100px;" formControlName="mohTestCode" disabled>
                                    {{row.testname}}
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>
                    <p-column field="testDate" header="Quantitative">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="diagnosticForm">
                                <div [formGroupName]="rowIndex">
                                    <input type="text" class="form-control form-control-sm" style="border: 0;"
                                        formControlName="valueNumeric">
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>
                    <p-column field="testDate" header="Qualitative">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="diagnosticForm">
                                <div [formGroupName]="rowIndex">
                                    <input type="text" class="form-control form-control-sm" style="border: 0;"
                                        formControlName="valueQualitative">
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>
                </p-dataTable>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12" formGroupName="hplcForm">
                <div class="form-group">
                    <label>Lab Comments </label>
                    <textarea class="form-control form-control-sm" formControlName="labComments"
                        style="height: 100px; ">
                    </textarea>
                </div>
            </div>
            <!-- Diagnostic Details End -->

            <br>
            <h6 style="font-weight: bold;">Transfusion Details :</h6>
            <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="rgTbBldTransDtls">
                    <div class="form-group">
                        <label> Previous Blood Transfusion on </label>
                        <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                        <p-calendar showIcon=true formControlName="transfusedDate"></p-calendar>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="rgTbBldTransDtls">
                    <div class="form-group">
                        <label>Institutes : </label>
                        <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                            bindLabel="estName" bindValue="estCode" formControlName="transfusedInst">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                            </ng-template>
                        </ng-select>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3" formGroupName="bloodgroupData">
                    <div class="form-group">
                        <label>Blood Group : </label>
                        <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                        <input type="hidden" class="form-control form-control-sm" formControlName="entryDate">
                        <input type="hidden" class="form-control form-control-sm" formControlName="entryBy">

                        <ng-select #entryPoint [items]="bloodGroupList" [virtualScroll]="true" placeholder="Select"
                            bindLabel="value" bindValue="id" formControlName="paramValue">
                            <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                            </ng-template>
                        </ng-select>
                    </div>
                </div>

                <!-- Antigen PhenoTyping Start -->
                <div class="col-lg-6 col-md-6 col-sm-6" formGroupName="antigenPhenoTypingForm">
                    <h6>Antigen PhenoTyping</h6>
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                    <input type="hidden" class="form-control form-control-sm" formControlName="mohTestCode">
                    <textarea class="form-control form-control-sm" formControlName="resultSummary" style="height: 100px; width: 700px;"
                        [readonly]="isAntigenPhenoTypingFormEditable">
                    </textarea>
                </div>
                <!-- Antigen PhenoTyping  End -->

                <!-- Antibody Investigation Start -->
                <div class="col-lg-6 col-md-6 col-sm-6" formGroupName="antibodyInvestigationForm">
                    <h6>Antibody Investigation</h6>
                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                    <input type="hidden" class="form-control form-control-sm" formControlName="mohTestCode">
                    <textarea class="form-control form-control-sm" formControlName="resultSummary" style="height: 100px; width: 700px;"
                        [readonly]="isAntibodyInvestigationEditable">
                    </textarea>
                </div>
                <!-- Antibody Investigation  End -->
            </div>
        </div>

        <div class="content-wrapper mb-2">
            <h6 style="font-weight: bold;">Complication </h6>
            <app-complication [submitted]="submitted" #complication></app-complication>
        </div>
        <div class="content-wrapper mb-2">
            <h6 style="font-weight: bold;">Clinical Details </h6>
            <app-clinical-details [submitted]="submitted" #clinicalDetails></app-clinical-details>
        </div>

        <div class="content-wrapper mb-2 ">
            <h6 style="font-weight: bold;">Medication </h6>
            <app-medication [submitted]="submitted" #Medication (callGenMedList)="callGenMedList()"></app-medication>
        </div>

        <div class="content-wrapper mb-2 ">
            <h6 style="font-weight: bold;">Surgeries in Past </h6>
            <app-surgery [submitted]="submitted" #surgery></app-surgery>
        </div>

        <!---------------------------------------------Family Array Start--------------------------------------------------------->
        <div class="content-wrapper mb-2 ">
            <h6 style="font-weight: bold;">Other Family Members Suffering from Genetic <button
                    class="btn btn-sm float-right"></button></h6>

            <div class="content-wrapper mb-2 lab-results">
                <div class="text-right pb-2">
                    <button (click)="onAddNewFH()" class="btn btn-sm btn-primary">Add New</button>
                    <button class="btn btn-sm btn-primary">Download</button>
                </div>

                <p-dataTable [immutable]="false" [value]="famHistory" [editable]="true" dataKey="runId"
                    [responsive]="true">
                    <p-column field="name" header="Name">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                                    <input type="hidden" class="form-control form-control-sm" formControlName="source">
                                    <div *ngIf="!row.isEditable">{{row.name}} </div>
                                    <div *ngIf="row.isEditable">
                                        <input type="text" class="form-control form-control-sm" formControlName="name">
                                    </div>
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>

                    <p-column field="relation" header="Relations">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">
                                        {{getRelationName(row.relation)}}
                                    </div>
                                    <div *ngIf="row.isEditable">
                                        <ng-select #entryPoint [items]="relation" [virtualScroll]="true"
                                            placeholder="Select Medicine Type" bindLabel="relationName"
                                            bindValue="relationCode" formControlName="relation">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">
                                                {{item.relationName}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>

                    <p-column field="patientID" header="patientID">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">{{row.patientID}}</div>
                                    <div *ngIf="row.isEditable">
                                        <input type="number" class="form-control form-control-sm"
                                            formControlName="patientID">
                                    </div>
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>

                    <p-column field="instID" header="Institute">
                        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                            <ng-container formArrayName="rgTbFamilyHistory">
                                <div [formGroupName]="rowIndex">
                                    <div *ngIf="!row.isEditable">
                                        {{getInstName(row.instID)}}
                                    </div>
                                    <div *ngIf="row.isEditable">
                                        <ng-select #entryPoint [items]="institutes" [virtualScroll]="true"
                                            placeholder="Select" bindLabel="estName" bindValue="estCode"
                                            formControlName="instID">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">
                                                {{ item.estName}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>
                            </ng-container>
                        </ng-template>
                    </p-column>

                    <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                        <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="onRowEditInitFH(row)" *ngIf="row.source == 'W' && row.isEditable == false"
                                class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>
                            <button (click)="onRowEditSaveFH(row)" *ngIf="row.source == 'W' && row.isEditable == true"
                                class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
                        </ng-template>
                    </p-column>

                    <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
                        <ng-template let-row="rowData" pTemplate="body">
                            <button (click)="delete(row)" class="btn btn-sm btn-primary"><i
                                    class="fa fa-trash"></i></button>
                        </ng-template>
                    </p-column>
                </p-dataTable>
            </div>
        </div>
        
        <!---------------------------------------------Family ArrayEnd--------------------------------------------------------->

        <h6 style="font-weight: bold;">Follow Up </h6>
        <div class="content-wrapper mb-2">
            <app-lab-results [submitted]="submitted" #LabResults></app-lab-results>
            <app-vaccination [submitted]="submitted" [currentCivilId]="currentCivilId" #Vaccination></app-vaccination>
        </div>
    </div>
</form>

<div class="modal-footer">
    <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
    <button type="submit" class="btn btn-sm btn-primary" (click)="save(genDisorderForm.value, 'Save')"
        >Save</button>
    <button type="submit" class="btn btn-sm btn-primary" (click)="save(genDisorderForm.value,'Finalize')"
        [disabled]="DisabledButton" hidden>Finalize</button>
</div>

<div style="position: relative;">
    <ngx-spinner bdOpacity=0.9 bdColor="rgba(51,51,51,0.8)" size="medium" color="#fff" type="ball-scale-multiple">
        <p style="font-size: 20px; color: white">Fetching from Al Shifa...</p>
    </ngx-spinner>
</div>

<ng-template #hplcHistory let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">HPLC History</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation" *ngFor="let data of hplcHistoryHeader"
                (click)="listClick($event, data)">
                <button class="nav-link" [ngClass]="{'active':selectedItem == data }" id="data.invstid"
                    data-bs-toggle="tab" data-bs-target="#data.invstid" type="button" role="tab"
                    aria-controls="data.invstid" aria-selected="true">{{data.releasedDt}}</button>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent" *ngFor="let data of hplcHistoryHeader">
            <div class="tab-pane fade " [ngClass]="{'show active':selectedItem == data }" id="data.invstid"
                role="tabpanel" aria-labelledby="data.invstid">
                {{data.releasedDt}}
            </div>
        </div>

        <div [formGroup]="genHplcHisForm">
            <p-dataTable [immutable]="true" [value]="hplcHistoryDetails" [editable]="true" dataKey="runId"
                [responsive]="true">
                <p-column field="testDate" header="HPLC">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                        <ng-container formArrayName="hplcHistorycForm">
                            <div [formGroupName]="rowIndex">
                                <input type="hidden" style="width: 100px;" formControlName="runId">
                                <input type="hidden" style="width: 100px;" formControlName="mohTestCode" disabled>
                                {{row.testname}}
                            </div>
                        </ng-container>
                    </ng-template>
                </p-column>
                <p-column field="testDate" header="Quantitative">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                        <ng-container formArrayName="hplcHistorycForm">
                            <div [formGroupName]="rowIndex">
                                <input type="text" class="form-control form-control-sm" style="border: 0;"
                                    formControlName="valueNumeric">
                            </div>
                        </ng-container>
                    </ng-template>
                </p-column>
                <p-column field="testDate" header="Qualitative">
                    <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
                        <ng-container formArrayName="hplcHistorycForm">
                            <div [formGroupName]="rowIndex">
                                <input type="text" class="form-control form-control-sm" style="border: 0;"
                                    formControlName="valueQualitative">
                            </div>
                        </ng-container>
                    </ng-template>
                </p-column>
            </p-dataTable>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-primary" (click)="modal.dismiss('Cross click')"
            (click)="updateHplc()">update HPLC</button>
    </div>

</ng-template>