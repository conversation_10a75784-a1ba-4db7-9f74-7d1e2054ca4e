<h6 class="page-title">Heart Listing</h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="heartSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Civil Id</label>
                    <input type="number" class="form-control form-control-sm" formControlName="civilId" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Registration No</label>
                    <input type="number" class="form-control form-control-sm" formControlName="regNo" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Age (from)</label>
                    <input type="number" class="form-control form-control-sm" formControlName="ageFrom" min="0" />
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>To</label>
                    <input type="number" min="0" class="form-control form-control-sm" formControlName="ageTo" />
                    <div *ngIf="heartSearchForm.get('ageFrom')?.value !== null && heartSearchForm.get('ageTo')?.value !== null && heartSearchForm.get('ageFrom')?.value > heartSearchForm.get('ageTo')?.value">
                        <small class="text-danger">'To' age cannot be less than 'From' age.</small>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="genderOptions" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select [items]="regionData" [virtualScroll]="true" placeholder="Select" bindLabel="regName"
                        bindValue="regCode" formControlName="regCode" (change)="onRegionSelect($event)">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.regName }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode"
                        (change)="onWilayatSelect($event)">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.walName }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select [items]="instituteListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="regInst">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause of Heart Disease</label>
                    <ng-select [items]="icdHeartList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="codeDisease" bindValue="code" formControlName="causeHeart">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.codeDisease
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Primary Diagnosis</label>
                    <ng-select [items]="primaryDiagList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramName" bindValue="paramId" formControlName="primaryDiag">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Physiotherapy Baseline</label>
                    <ng-select [items]="PhyBaselineList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramName" bindValue="paramId" formControlName="physiotherapyBase">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>NYHA Class</label>
                    <ng-select [items]="nyhaClassList" [virtualScroll]="true" placeholder="Select" bindLabel="paramName"
                        bindValue="paramId" formControlName="nyhaClass">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.paramName
                            }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Transplant Recommended</label>
                    <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="transplantRecommended">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Transplant Urgent</label>
                    <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="transplantUrgent">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Inotrope Used</label>
                    <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="inotropeUsed">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3" *ngIf="heartSearchForm.get('inotropeUsed')?.value === 'Y'">
                <div class="form-group">
                    <label>Inotrope Type</label>
                    <ng-multiselect-dropdown [data]="inotropeList" formControlName="inotrope" placeholder="Select"
                        [settings]="inotropeDropdownSettings"></ng-multiselect-dropdown>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3" >
                <div class="form-group">
                    <label>MCS Device Used</label>
                    <ng-select [items]="yesNoOptions" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                        bindValue="id" formControlName="mcsDeviceUsed">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}</ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3" *ngIf="heartSearchForm.get('mcsDeviceUsed')?.value === 'Y'">
                <div class="form-group">
                    <label>MCS Device Type</label>
                    <ng-multiselect-dropdown [data]="mcsDeviceList" formControlName="mcsDevice" placeholder="Select"
                        [settings]="mcsDeviceDropdownSettings">
                    </ng-multiselect-dropdown>
                </div>
            </div>

            <div class="col-lg-4 col-md-3 col-sm-3">
                <div class="form-group">
                    <label><strong>Blood Group</strong></label><br />
                    <div class="chexkBoxList">
                        <ng-container *ngFor="let bloodGroup of bloodGroupOptions">
                            <input type="checkbox" [value]="bloodGroup.value" (change)="onBloodGroupSelect($event)" />
                            {{ bloodGroup.value }}
                        </ng-container>
                    </div>
                </div>
            </div>

            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="button" (click)="exportToExcel()" class="btn btn-primary ripple">EXCEL</button>
                    <button type="reset" (click)="clearForm()" class="btn btn-sm btn-secondary">Clear</button>
                    <button type="submit" (click)="onSearch()" class="btn btn-sm btn-primary">Search</button>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="content-wrapper mt-2 grid-container">
    <ag-grid-angular class="ag-theme-balham heart-grid" [rowData]="rowData" [columnDefs]="columnDefs"
        (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
    </ag-grid-angular>

    <div *ngIf="rowData && rowData.length > 0">
        <p-paginator #paginator [rows]="paginationSize" [totalRecords]="totalRecords" (onPageChange)="onSearch($event)"
            showCurrentPageReport="true" currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
            [rowsPerPageOptions]="[10, 20, 30]">
        </p-paginator>
    </div>
</div>