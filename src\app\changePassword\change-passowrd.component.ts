import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ChangePasswordService } from './change-password.service';
import swal from 'sweetalert2';
import { ChangePassword } from './change-password';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MustMatch } from '../common/custom-validators/must-match.validator';

@Component({
  selector: 'app-change-passowrd',
  templateUrl: './change-passowrd.component.html',
  styleUrls: ['./change-passowrd.component.css']
})
export class ChangePassowrdComponent implements OnInit {
  password: ChangePassword = new ChangePassword();
  form : FormGroup;
  submitted: boolean;

  @Input() submittedPws:boolean;
  @Output() closeModal = new EventEmitter<void>();

  constructor(private changePasswordService : ChangePasswordService, private formBuilder: FormBuilder) { }

  
  ngOnInit() {
    this.form = this.formBuilder.group({
      oldPassword: ['', Validators.required],
      newPassword: ['', [Validators.required]],
      confirmPassword: ['', Validators.required]
  }, {
      validator: MustMatch('newPassword', 'confirmPassword')
  });
      
  }

  // convenience getter for easy access to form fields
  get f() { return this.form.controls; }

  onSubmit() {
    this.submitted = true;
    // console.log(this.form.invalid);
    // stop here if form is invalid
    if (this.form.invalid) {
        return false;
    }
    this.changePassword();
}

  changePassword() {
    this.changePasswordService.changePassword(this.password).subscribe(res => {
        if (res['code'] === 0) {
            swal.fire('Saved!', res['message'], 'success')
            this.clear();
            this.submittedPws = true
            this.emitCloseEvent();
        }
        else {
            swal.fire('Saving Error : ', res['message'], 'error');
            this.clear();
        }
    });


}

clear() {
    this.submitted = false;
    this.password.oldPassword = null;
    this.password.newPassword = null;
    this.password.confirmPassword = null;
    this.emitCloseEvent();
}

emitCloseEvent(): void {
  // Emit the close event
  this.closeModal.emit();
}

}
