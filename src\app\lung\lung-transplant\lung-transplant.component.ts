import { ChangeDete<PERSON><PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";

import {
  FormArray,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";

import { Input } from "@angular/core";


import {
  LiverListResult,
  LiverSurgicalInfo,
  LiverSurgicalSubTypeInfo,
  LiverTransPlantSubType,
  LiverTransPlantType,
  LiverTransplantMed,
} from "../../_models/liverTransplant.model";
import { GridNgSelectDataComponent } from "../../common/agGridComponents/grid-ngSelect-data.component";
import { MasterService } from "../../_services/master.service";
import Swal from "sweetalert2";
import { HttpClient, HttpParams } from "@angular/common/http";
import * as AppUtils from "../../common/app.utils";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";

import { DecimalPipe } from "@angular/common";
import { Diagnosis } from "../../common/objectModels/diagnosis-model";
import { ICDList } from "../../common/objectModels/icdList-models";
import { ICDLiverShortList } from "../../common/objectModels/icdLiverShortList-models";
import * as CommonConstants from "../../_helpers/common.constants";
import * as AppParams from "../../_helpers/app-param.constants";
import { TbVitalSigns } from "../../common/objectModels/vital-signs-model";
import { formatDate } from "@angular/common";
import * as GridUtils from "../../common/agGridComponents/app.grid-spec-utils";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { ButtonRendererComponent } from "../../common/agGridComponents/ButtonRendererComponent";
import { SharedService } from "../../_services/shared.service";
import { Router } from "@angular/router";
import * as AppCompUtils from "../../common/app.component-utils";
import { LoginService } from "src/app/login/login.service";
import { DeathDetailsComponent } from "src/app/_comments/death-details/death-details.component";

import { LiverRegistryFrom } from "../../common/objectModels/liver-registry-model";
import { LungService } from "../lung.service";
import { stagesTransplantMode } from "src/app/common/objectModels/stages-transplant-model";
import { convertToObject } from "typescript";
@Component({
  selector: "app-lung-transplant",
  templateUrl: "./lung-transplant.component.html",
  styleUrls: ["./lung-transplant.component.scss"],
  providers: [LungService]
})
export class LungTranplantComponent implements OnInit {
  showButton=false;
  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  patientInfoDetails = [];
  submitted = false;
  patientForm: FormGroup;
  today = new Date();
  public page3 = 1;
  InductionForm: FormGroup;
  LateForm: FormGroup;
  EarlyForm: FormGroup;
  ImmediateForm: FormGroup;
  public pageSize = 2;
  recordEnabled: boolean = false;
  recordEnabled1: boolean = false;
  recordEnabled2: boolean = false;
  transplantRegistryForm: FormGroup;

  rowData: any[] = [];
  regId: any;
  alive = true;
  icdData: Array<Diagnosis> = new Array<Diagnosis>();
  dbIcdDataGrid: any;
  dbIcdData: Array<Diagnosis>;
  diagnosis: Array<Diagnosis>;
  icdList: Array<ICDList>;
  icdLiverShortList: Array<ICDLiverShortList>;
  formData: LiverRegistryFrom;
  rgTbVitalSign: Array<TbVitalSigns>;

  columnDefs;
  frameworkComponents;
  _decimalPipe: DecimalPipe = new DecimalPipe("en-US");
  currentDate = formatDate(new Date(), "yyyy-MM-dd", "en");
  grids: any = [];
  loginId: any;
  centralRegNoExit: boolean = false;
  bloodGroupList = AppCompUtils.BLOOD_GROUP;
  // alive: boolean = true;
  currentCivilId = "";
  estCode;
  patientId;
  @ViewChild("patientDetails", { static: false })
  @ViewChild("deathDetails", { static: false })
  deathDetails: DeathDetailsComponent;
  institutes: any[];
  nationListFilter: any;
  nationList: any;
  institeList: any[];
  institeListFilter: any[];
  public rgTbMedicines: any = [];
  public inductionMedFormArray: any = [];
  public immediateMedFormArray: any = [];
  public earlyMedFormArray: any = [];
  public lateMedFormArray: any = [];
  hospitalsList: any;
  liverSurgicalInfoMastList: Array<LiverSurgicalInfo>;
  liverTransplanTypeMastList: Array<LiverTransPlantType>;
  liverTransplanSubTypeMastList: Array<LiverTransPlantSubType>;
  liverSurgicalInfoSubTypeMastList: Array<LiverSurgicalSubTypeInfo>;
  liverTransplantMedicineList: Array<LiverTransplantMed>;
  livMedList: any = [];
  delRow;
  medicineForm: FormGroup;
  medicines: any;
  //medicines: any;
  //   { id: 1, name: 'Medicine 1' },
  //   { id: 2, name: 'Medicine 2' },
  //   { id: 3, name: 'Medicine 3' }
  // ];

  inductionMedList: any = [];
  immediateMedList: any = [];
  earlyMedList: any = [];
  lateMedList: any = [];

  IandPmedicineList: any[]=[];

  openAccordion: boolean[] = [];

  selectedTab: number = 0;
  selectedSubList: any[] = [];
  selectedSubListItem: any = null;
  constructor(
    private _sharedService: SharedService,
    private _loginService: LoginService,
    private _router: Router,
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _http: HttpClient,
    private _lungService: LungService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
   
    this.initialsPatientForm();
    this.getMasterData();
   //this.initializeMedicineFormControls();
   //this.selectTab(0);

   this.initializeComponent();
    
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }
  

  toggleAccordion(index: number): void {
    this.openAccordion[index] = !this.openAccordion[index];
  }

  

  getInstName(instID) {
    if (instID) {
      return this.institutes
        .filter((s) => s.estCode == instID)
        .map((s) => s.estName)[0];
    }
  }

  selectSubListItem(item: any): void {
    this.selectedSubListItem = item;
  }

  initialsPatientForm() {
    // patient form initialization
    this.patientForm = this.fb.group({
      centralRegNo: [null],
      patientId: [null, Validators.required],
      civilId: [null, Validators.required],
      dob: [null, Validators.required],
      age: [null],
      tribe: [null],
      firstName: [null, Validators.required],
      secondName: [null, Validators.required],
      sex: [null],
      maritalStatus: [null],
      thirdName: [null],
      village: [null],
      walayat: [null],
      region: [null],
      mobileNo: [null],
      kinTelNo: [null],
      careGiverTel: [null],
      regInst: [null, Validators.required],
      exDate: [null],
    });

    // transplant form initialization
    this.transplantRegistryForm = this.fb.group({
      transId: [null],
      centralRegNo: [null, Validators.required],
      preemptiveTransplantYn: [null, Validators.maxLength(1)],
      transplantDate: [null, Validators.required],
      transplantCountry: [null, Validators.required],
      transplantRemarks: [
        null,
        [Validators.maxLength(1000), Validators.required],
      ],
      transplantType: [null, Validators.required],
      transplantSubType: [null, Validators.required],
      surgeryType: [null, Validators.required],
      explantedLiverPath: [
        null,
        [Validators.maxLength(1000), Validators.required],
      ],
      noDaysWard: [null, Validators.required],
      noDaysIcu: [null, Validators.required],
      reAdmissionYn: [null, Validators.maxLength(1)],
      reAdmissionReason: [null, Validators.maxLength(1000)],
      reAdmissionDays: [null],
      surgicalInterYn: [null, Validators.maxLength(1)],
      surgicalInterReason: [null, Validators.maxLength(1000)],
      surgicalInterType: [null, Validators.maxLength(1000)],
      followUpHosp: [null, Validators.required],
      followUpHospOthers: [{ value: "", disabled: true }, Validators.required],
      postTransRejectionYn: [null, Validators.maxLength(1)],
      rejectionDate: [null],
      biopsyYn: [null, Validators.maxLength(1)],
      rejectionGradeYn: [null, Validators.maxLength(1)],
      rxRejectionYn: [null, Validators.maxLength(1)],
      surgerySubType: [null, Validators.required],
      registerType: [null],
    });

    this.transplantRegistryForm
      .get("reAdmissionYn")
      .valueChanges.subscribe((value) => {
        this.setReAdmissionValidators(value);
        if (value === "N") {
          this.clearReAdmissionFields();
        }
      });

    this.transplantRegistryForm
      .get("surgicalInterYn")
      .valueChanges.subscribe((value) => {
        this.setSurgicalInterventionValidators(value);
        if (value === "N") {
          this.clearSurgicalInterventionFields();
        }
      });

    this.transplantRegistryForm
      .get("postTransRejectionYn")
      .valueChanges.subscribe((value) => {
        this.setPostTransplantRejectionValidators(value);
        if (value === "N") {
          this.clearPostTransplantRejectionFields();
        }
      });

    this.transplantRegistryForm
      .get("reAdmissionYn")
      .valueChanges.subscribe((value) => {
        this.setReAdmissionValidators(value);
      });

    this.transplantRegistryForm
      .get("surgicalInterYn")
      .valueChanges.subscribe((value) => {
        this.setSurgicalInterventionValidators(value);
      });

    this.transplantRegistryForm
      .get("postTransRejectionYn")
      .valueChanges.subscribe((value) => {
        this.setPostTransplantRejectionValidators(value);
      });

    this.transplantRegistryForm
      .get("followUpHosp")
      .valueChanges.subscribe((value) => {
        if (value === -1) {
          this.transplantRegistryForm.get("followUpHospOthers").enable();
        } else {
          this.transplantRegistryForm.get("followUpHospOthers").disable();
        }
      });

    // medicine form initialization
    this.medicineForm = this.fb.group({
      rgTbMedicines: this.fb.array([]),
      inductionMedFormArray: this.fb.array([]),
      immediateMedFormArray: this.fb.array([]),
      earlyMedFormArray: this.fb.array([]),
      lateMedFormArray: this.fb.array([]),
    });
  }

  selectTab(index: number): void {
    if (this.IandPmedicineList && this.IandPmedicineList.length > 0) {
      this.selectedTab = index;
      this.selectedSubList = this.IandPmedicineList[index].SubList;
      this.selectedSubListItem = null; // Reset selected sublist item
    }
  }

  initializeMedicineFormControls() {
    if (this.IandPmedicineList.length > 0) {
      this.IandPmedicineList.forEach(item => {
        item.SubList.forEach(subItem => {
          this.medicineForm.addControl('selected_' + subItem.id, new FormControl(subItem.isSelected));
          subItem.SubSubList.forEach(subSubItem => {
            this.medicineForm.addControl('selectedSubSub_' + subSubItem.id, new FormControl(subSubItem.isSelected));
            this.medicineForm.addControl('dose_' + subSubItem.id, new FormControl(subSubItem.dose));
            this.medicineForm.addControl('level_' + subSubItem.id, new FormControl(subSubItem.level));
          });
        });
      });
    }
  }


  clearReAdmissionFields() {
    this.transplantRegistryForm.get("reAdmissionReason").reset();
    this.transplantRegistryForm.get("reAdmissionDays").reset();
  }

  clearSurgicalInterventionFields() {
    this.transplantRegistryForm.get("surgicalInterType").reset();
    this.transplantRegistryForm.get("surgicalInterReason").reset();
  }

  clearPostTransplantRejectionFields() {
    this.transplantRegistryForm.get("rejectionDate").reset();
    this.transplantRegistryForm.get("rejectionGradeYn").reset();
    this.transplantRegistryForm.get("rxRejectionYn").reset();
  }

  private getMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getInstitutesMasterByUserRoles().subscribe((res) => {
      this.institeList = res["result"];
      this.institeListFilter = this.institeList;
    });

    this._masterService.getHospitals().subscribe((response) => {
      this.hospitalsList = response["result"];
      this.hospitalsList.push({ estCode: -1, estName: "Others" });
    });

    this._masterService.getLiverTransplantMedList().subscribe((response) => {
      this._masterService.getMedicineMaster().subscribe((res) => {
        this.medicines = response["result"];
      });
    });

    this.getNationalityList();
    this._masterService.getLiverSurgicalMast();

    this._masterService.liverSurgicalInfoMastList.subscribe((value) => {
      this.liverSurgicalInfoMastList = value;
    });
    this._masterService.getLiverTransplantTypeMast();
    this._masterService.liverTransplanTypeMastList.subscribe((value) => {
      this.liverTransplanTypeMastList = value;
    });

    this._masterService.getLiverTransplantSubTypeMast();
    this._masterService.liverTransplanSubTypeMastList.subscribe((value) => {
      this.liverTransplanSubTypeMastList = value;
    });
    this._masterService.getLiverSurgicalSubInfoMast();
    this._masterService.liverSurgicalInfoSubTypeMastList.subscribe((value) => {
      this.liverSurgicalInfoSubTypeMastList = value;
    });


    this._masterService.getLiverTransplantMedicineList().subscribe(response => {
     // console.log(response, "response");
      if (response && response.result) {
       // console.log(response.result, "response.result");
        this.IandPmedicineList = response.result.map(item => ({
          id: item.id,
          value: item.value,
          active: item.active,
          remarksYn: item.remarksYn,
          isSelected: item.isSelected,
          dose: item.dose,
          level: item.level,
          SubList: item.subList.map(subItem => ({
            id: subItem.id,
            prevId: subItem.prevId,
            value: subItem.value,
            active: subItem.active,
            remarksYn: subItem.remarksYn,
            isSelected: subItem.isSelected,
            dose: subItem.dose,
            level: subItem.level,
            SubSubList: subItem.subSubList.map(subSubItem => ({
              id: subSubItem.id,
              prevId: subSubItem.prevId,
              value: subSubItem.value,
              active: subSubItem.active,
              remarksYn: subSubItem.remarksYn,
              isSelected: subSubItem.isSelected,
              dose: subSubItem.dose,
              level: subSubItem.level
            }))
          }))
        }));
        this.initializeMedicineFormControls();
        this.selectTab(0);
      }
   //   console.log(this.IandPmedicineList, "MedicineList");
    }, error => {
      console.error("Error fetching liver transplant medicine list", error);
    });
  }
    

  onRowEditSaveMedicine(row: any, period: string): void {
    let rowIndex;
    const formArray = this.getFormArrayByPeriod(period);
    //const rowIndex = formArray.controls.indexOf(row);
    let list;
    switch (period) {
      case "induction":
        list = this.inductionMedList;
        rowIndex = list.indexOf(row);
        list[rowIndex] =
          this.medicineForm.value.inductionMedFormArray[rowIndex];
        break;
      case "immediate":
        list = this.immediateMedList;
        rowIndex = list.indexOf(row);
        list[rowIndex] =
          this.medicineForm.value.immediateMedFormArray[rowIndex];
        break;
      case "early":
        list = this.earlyMedList;
        rowIndex = list.indexOf(row);
        list[rowIndex] = this.medicineForm.value.earlyMedFormArray[rowIndex];
        break;
      case "late":
        list = this.lateMedList;
        rowIndex = list.indexOf(row);
        list[rowIndex] = this.medicineForm.value.lateMedFormArray[rowIndex];
        break;
      default:
        return;
    }

    let data = list[rowIndex];
   // console.log(data, "data");
    data.value = this.medicines
      .filter((s) => s.id == data.medId)
      .map((s) => s.name)[0];
    data.isEditable = false;
  }

  onRowEditInitMedicine(row: any) {
    row.get("isEditable").setValue(true); // Set the row to editable
  }

  onAddNewMedicine(period: string): void {
   // console.log("Add New Medicine:", period);
    let list;
    let periodNumb;
    switch (period) {
      case "induction":
        list = this.inductionMedList;
        periodNumb = 123;
        break;
      case "immediate":
        list = this.immediateMedList;
        periodNumb = 124;
        break;
      case "early":
        list = this.earlyMedList;
        periodNumb = 125;
        break;
      case "late":
        list = this.lateMedList;
        periodNumb = 126;
        break;
      default:
        return;
    }
    this.addMedicineDetails("", "", "", "", period, false);
    //console.log(list, "list");
    list[list.length - 1].isEditable = true;
  }

  getFormArrayByPeriod(period: string): FormArray {
    switch (period) {
      case "induction":
        return (this.inductionMedFormArray = this.medicineForm.get(
          "inductionMedFormArray"
        ) as FormArray);
      case "immediate":
        return (this.immediateMedFormArray = this.medicineForm.get(
          "immediateMedFormArray"
        ) as FormArray);
      case "early":
        return (this.earlyMedFormArray = this.medicineForm.get(
          "earlyMedFormArray"
        ) as FormArray);
      case "late":
        return (this.lateMedFormArray = this.medicineForm.get(
          "lateMedFormArray"
        ) as FormArray);
      default:
        return this.fb.array([]);
    }
  }

  addMedicineDetails(
    runId: any,
    medId: any,
    dose: any,
    medLevel: any,
    medPeriod: any,
    isEditable: any = false
  ): void {
    //console.log("Add Medicine:", medPeriod);
    // this.rgTbMedicines = this.medicineForm.get(
    //   "rgTbMedicines"
    // ) as FormArray;

    const formArray = this.getFormArrayByPeriod(medPeriod);

    // this.livMedList = Object.assign([], this.rgTbMedicines.value);

    const medicineItem: any = this.createMedicineItem(
      runId,
      medId,
      dose,
      medLevel,
      medPeriod,
      isEditable
    );

    //console.log(medicineItem, "medicineItem");

    formArray.push(this.createMedicineGroup(medicineItem));

    //console.log(formArray, "formArray");
    //this.livMedList.push(medicineItem);
    let list;
    switch (medPeriod) {
      case "induction":
        // this.inductionMedList= Object.assign([], this.rgTbMedicines.value);
        list = this.inductionMedList;
        this.inductionMedList.push(medicineItem);
        break;
      case "immediate":
        list = this.immediateMedList;
        //  this.immediateMedList= Object.assign([], this.rgTbMedicines.value);
        this.immediateMedList.push(medicineItem);
        break;
      case "early":
        list = this.earlyMedList;
        // this.earlyMedList= Object.assign([], this.rgTbMedicines.value);
        this.earlyMedList.push(medicineItem);
        break;
      case "late":
        list = this.lateMedList;
        //this.lateMedList= Object.assign([], this.rgTbMedicines.value);
        this.lateMedList.push(medicineItem);
        break;
    }
    list = Object.assign([], formArray.value);
  }

  createMedicineGroup(medicineItem: any): FormGroup {
    return this.fb.group({
      runId: [medicineItem.runId || ""],
      medId: [medicineItem.medicine || "", Validators.required],
      dose: [medicineItem.dose || "", Validators.required],
      medLevel: [medicineItem.level || "", Validators.required],
      medPeriod: [medicineItem.period || "", Validators.required],
      isEditable: [medicineItem.isEditable],
    });
  }

  createMedicineItem(
    runId: any = null,
    medicine: any = null,
    dose: any = null,
    level: any = null,
    period: any,
    isEditable: any = false
  ) {
    let periodNumb;
    switch (period) {
      case "induction":
        periodNumb = 123;
        break;
      case "immediate":
        periodNumb = 124;
        break;
      case "early":
        periodNumb = 125;
        break;
      case "late":
        periodNumb = 126;
        break;
      default:
        return;
    }

    return {
      runId: runId,
      medId: medicine,
      dose: dose,
      medLevel: level,
      medPeriod: periodNumb,
      isEditable: isEditable,
    };
  }

  getMedicineName(medicineId) {
    if (medicineId) {
      return this.medicines
        .filter((s) => s.id == medicineId)
        .map((s) => s.name)[0];
    }
  }

  removeMedicine(row: any, period: any): void {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        let list;
        switch (period) {
          case "induction":
            list = this.inductionMedList;
            break;
          case "immediate":
            list = this.immediateMedList;
            break;
          case "early":
            list = this.earlyMedList;
            break;
          case "late":
            list = this.lateMedList;
            break;
          default:
            return;
        }
        const formArray = this.getFormArrayByPeriod(period);
        //list = formArray;
        this.delRow = formArray.controls.indexOf(row);
        list.splice(this.delRow, 1);
        formArray.removeAt(this.delRow);
      }
    });
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        // console.log(this.nationList, "nationlist");
        this.nationListFilter = this.nationList;
      },
      (error) => {
        if (error.status == 401)
          Swal.fire("", "Error occured while retrieving country list", "error");
      }
    );
  }

  updateRecordStatus() {
    // console.log("Re-Admitted:", this.recordEnabled); // True or False based on selection
    // console.log("Surgical Intervention:", this.recordEnabled1); // True or False based on selection
    // console.log("Past Transplant Rejection:", this.recordEnabled2); // True or False based on selection
  }

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;

    this.frameworkComponents = {
      ngSelectEditor: GridNgSelectDataComponent,
      buttonRenderer: ButtonRendererComponent,
    };

    //this.populateMasterData();

    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().regNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  get rgTbMedArray() {
    return this.medicineForm.controls["rgTbMedicines"] as FormArray;
  }

  callMpiMethod() {
    this.getdata("civilId", " ", this.patientDetails.patientForm.value.civilId);
  }

  clearAllMedList() {
    this.livMedList = [];
    this.inductionMedList = [];
    this.immediateMedList = [];
    this.earlyMedList = [];
    this.lateMedList = [];
   // this.IandPmedicineList= [];
    this.rgTbMedArray.clear();
  }

  clear() {
    this.showButton = false;
    this.transplantRegistryForm.reset();
    this.formData = null;
    this.patientDetails.clear();

    // console.log(this.IandPmedicineList, "IandPmedicineList");
    // // Reset the selected state of all items in IandPmedicineList 
    // // and clear the dose and level values
    if (this.IandPmedicineList) {
      this.IandPmedicineList.forEach((item) => {
        item.SubList.forEach((subItem) => {
          subItem.isSelected = false; // Reset the selected state
          subItem.dose = null; // Clear dose value
          subItem.level = null; // Clear level value
          if (subItem.SubSubList) {
            subItem.SubSubList.forEach((subSubItem) => {
              subSubItem.isSelected = false; // Reset the selected state
              subSubItem.dose = null; // Clear dose value
              subSubItem.level = null; // Clear level value
            });
          }
        });
      });
    }

    if (this.medicineForm) {
      Object.keys(this.medicineForm.controls).forEach((key) => {
        this.medicineForm.get(key).reset();
      });
    }

    
    this.clearAllMedList();
    this.centralRegNoExit = false;
    this.submitted = false;
    this.regId = '';
    //this.formData.instRegDate= this.today;
  }

  search() {
    if (this.regId) {
      setTimeout(() => {
        this.getdata("regNo", this.regId, "");
        this.regId = "";
      }, 1000);
    } else {
      this.regId = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Registration ID",
      });
    }
  }

  getdata(searchby: any, regNo: any, civilId: any) {
    let msg;
    let callMPI = true;
    if (searchby == AppUtils.CALLTYPE_BY_REGISTRATION_NO) {
      msg = "No Record Found with Entered Regstration No.";
    } else if (searchby == AppUtils.CALLTYPE_BY_CIVILID) {
      if (
        !(
          this.patientDetails.f.civilId.value &&
          (this.patientDetails.f.exDate || this.patientDetails.f.dob)
        )
      ) {
        callMPI = false;
        msg =
          "No Record Found in the registry..</br></br> Please enter civil id and expiry date to fetch Demographic information from ROP.";
      } else {
        msg =
          "No Record Found in the registry..</br></br>  Fetching Demographic information from ROP.";
      }
    }

    this._lungService.getLiverTransplant(regNo, "", civilId).subscribe(
      async (res) => {
        if (res["code"] == "S0000") {
          // get data
          this.showButton = true;
          this.centralRegNoExit = true;
          this.formData = res["result"];
          this.patientDetails.setPatientDetails(res["result"]);

          this.currentCivilId = res["result"].rgTbPatientInfo["civilId"];
          this.patientId = this.formData["patientID"];
          this.estCode = this.formData["regInst"];
          //this.rgTbVitalSign = this.formData.rgTbVitalSigns;

          if (res["result"].rgTbDeathDetails != null) {
            this.alive = false;
          } else {
            this.alive = true;
          }

          let rgTbDeathDetails: any = res["result"].rgTbDeathDetails;

          if (rgTbDeathDetails != null && rgTbDeathDetails != undefined) {
            this.alive = false;
            setTimeout(
              function () {
                this.deathDetails.setDeathDetailsResult(rgTbDeathDetails);
              }.bind(this),
              30
            );
          }

          if (res["result"].rgLiverTransplantDto) {
            const transplantData = res["result"].rgLiverTransplantDto;
            if (transplantData.transplantDate) {
              transplantData.transplantDate = new Date(
                transplantData.transplantDate
              );
            }
            if (transplantData.rejectionDate) {
              transplantData.rejectionDate = new Date(
                transplantData.rejectionDate
              );
            }
            transplantData.registerType = AppUtils.REG_TYPE_LUNG;
            this.transplantRegistryForm.patchValue(transplantData);
          }

          const liverTransplantMedDtos =
            res["result"].rgTbLiverTransplantMedDtos;
         // console.log("liverTransplantMedDtos--", liverTransplantMedDtos);

          if(liverTransplantMedDtos && liverTransplantMedDtos.length > 0) {
          liverTransplantMedDtos.forEach((dto) => {
          this.IandPmedicineList.forEach(item => {
            if (item.id === dto.medPeriod) {
              item.SubList.forEach(subItem => {
                if (subItem.id === dto.medId) {
                  this.medicineForm.get('selected_' + subItem.id).setValue(true);
                }
                subItem.SubSubList.forEach(subSubItem => {
                  if (subSubItem.id === dto.medId) {
                    this.medicineForm.get('selectedSubSub_' + subSubItem.id).setValue(true);
                    this.medicineForm.get('dose_' + subSubItem.id).setValue(dto.dose);
                    this.medicineForm.get('level_' + subSubItem.id).setValue(dto.medLevel);
                  }
                });
              });
            }
          });
        });
      }
        } else if (res["code"] == "F0000" || res["code"] == "3") {
          //if record not exist in eReg DB, and qeury by Civil ID , then call fetchMpi method.
          //
          // title: 'Warning',
          // text: msg +'</br></br> dsdd',
          // icon: 'warning',
          await Swal.fire("", msg, "warning").then((result) => {
            if (callMPI == true) {
              this._sharedService.setPatientData(this.patientDetails);
              this._sharedService.fetchMpi();
            }
          });
        } else {
          Swal.fire("", res["message"], "error");
        }
      },
      (error) => {
        if (error.status == 401)
          Swal.fire("", "Error occured while retrieving user details", "error");
      }
    );
  }

  submitForm() {
    this.submitted = true;
  }

  get f() {
    return this.transplantRegistryForm.controls;
  }

  setSurgicalInterventionValidators(value: string) {
    const surgicalInterType =
      this.transplantRegistryForm.get("surgicalInterType");
    const surgicalInterReason = this.transplantRegistryForm.get(
      "surgicalInterReason"
    );

    if (value === "Y") {
      surgicalInterType.setValidators([Validators.required]);
      surgicalInterReason.setValidators([Validators.required]);
    } else {
      surgicalInterType.clearValidators();
      surgicalInterReason.clearValidators();
    }

    surgicalInterType.updateValueAndValidity();
    surgicalInterReason.updateValueAndValidity();
  }

  setPostTransplantRejectionValidators(value: string) {
    const rejectionDate = this.transplantRegistryForm.get("rejectionDate");
    const rejectionGradeYn =
      this.transplantRegistryForm.get("rejectionGradeYn");
    const rxRejectionYn = this.transplantRegistryForm.get("rxRejectionYn");

    if (value === "Y") {
      rejectionDate.setValidators([Validators.required]);
      rejectionGradeYn.setValidators([Validators.required]);
      rxRejectionYn.setValidators([Validators.required]);
    } else {
      rejectionDate.clearValidators();
      rejectionGradeYn.clearValidators();
      rxRejectionYn.clearValidators();
    }

    rejectionDate.updateValueAndValidity();
    rejectionGradeYn.updateValueAndValidity();
    rxRejectionYn.updateValueAndValidity();
  }

  setReAdmissionValidators(value: string) {
    const reAdmissionReason =
      this.transplantRegistryForm.get("reAdmissionReason");
    const reAdmissionDays = this.transplantRegistryForm.get("reAdmissionDays");

    if (value === "Y") {
      reAdmissionReason.setValidators([Validators.required]);
      reAdmissionDays.setValidators([Validators.required]);
    } else {
      reAdmissionReason.clearValidators();
      reAdmissionDays.clearValidators();
    }

    reAdmissionReason.updateValueAndValidity();
    reAdmissionDays.updateValueAndValidity();
  }

  saveDetails() {
    this.submitted = true;
    let patientInfoData = this.patientDetails.patientForm.value;

    // let transplantRegistryForm= [];
    // if (this.transplantRegistryForm.valid) {
    //   transplantRegistryForm = this.transplantRegistryForm.value;
    // }
    this.transplantRegistryForm.controls["centralRegNo"].setValue(
      this.patientDetails.f.centralRegNo.value
    );

    if (this.transplantRegistryForm.invalid) {
      return;
    }

    const transplantRegistryData = this.transplantRegistryForm.value;
    transplantRegistryData.registerType = AppUtils.REG_TYPE_LUNG;

    //console.log("transplantRegistryForm--", transplantRegistryData);

    const formData = this.medicineForm.value;

    // Combine all medicine form arrays into a single array
    // const combinedMedicineArray = [
    //   ...formData.inductionMedFormArray,
    //   ...formData.immediateMedFormArray,
    //   ...formData.earlyMedFormArray,
    //   ...formData.lateMedFormArray
    // ].filter(item => item); // Filt
    const combinedMedicineArray = this.saveMedicineData();
    //console.log("combinedMedicineArray--", combinedMedicineArray);

    let saveData = {
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      patientID: this.patientDetails.f.patientId.value,
      //regInst: this.patientDetails.f.regInst.value,
      //registerType: AppUtils.REG_TYPE_LIVER,
      rgTbPatientInfo: patientInfoData,
      rgLiverTransplantDto: transplantRegistryData,
      rgTbLiverTransplantMedDtos: combinedMedicineArray,
    };

    //console.log("saveData--", saveData);

    this._lungService.saveLiverTransplant(saveData).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Lung Transplant Saved successfully.", "success");
          this.regId = res["result"];
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire(
          "Error!",
          "Error occured while saving Lung Transplant " + err.message,
          "error"
        );
      }
    );
  }




  saveMedicineData() {
    const medicineDataArray = [];

    if (this.IandPmedicineList.length > 0) {
      this.IandPmedicineList.forEach((item) => {
        item.SubList.forEach((subItem) => {
          if (this.medicineForm.get("selected_" + subItem.id).value) {
            const subItemData = {
              centralRegNo: this.patientDetails.f.centralRegNo.value,
              medPeriod: item.id,
              medId: subItem.id,
              dose: null,
              medLevel: null,
            };
            medicineDataArray.push(subItemData);

            subItem.SubSubList.forEach((subSubItem) => {
              if (this.medicineForm.get("selectedSubSub_" + subSubItem.id).value) {
                const subSubItemData = {
                  centralRegNo: this.patientDetails.f.centralRegNo.value,
                  medPeriod: item.id,
                  medId: subSubItem.id,
                  dose: this.medicineForm.get("dose_" + subSubItem.id).value,
                  medLevel: this.medicineForm.get("level_" + subSubItem.id).value,
                };
                medicineDataArray.push(subSubItemData);
              }
            });
          }
        });
      });
    }

    return medicineDataArray;
  }

  numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

}
