import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppUtils from '../../common/app.utils';
import { GridOptions } from 'ag-grid-community';
import { Router } from '@angular/router';
import { SharedService } from '../../_services/shared.service';
import Swal from 'sweetalert2';
import { LiverWaitingListResult } from 'src/app/_models/liver-waiting-list-result.model';
import * as AppCompUtils from '../../common/app.component-utils';

@Component({
  selector: 'app-liver-waiting-list-listing',
  templateUrl: './liver-waiting-list-listing.component.html',
  styleUrls: ['./liver-waiting-list-listing.component.scss']
})
export class LiverWaitingListListingComponent implements OnInit {
  yesNo = AppCompUtils.YES_NO;
  gender = AppCompUtils.GENDER;
  totalRecords: any;
  paginationSize: any = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  liverWaitingListSearchForm: FormGroup;
  regionData: RegionDataModel[];
  wallayatList: any[];
  wallayatListFilter: any[];
  institeList: any[];
  institeListFilter: any[];
  genderTypeOptions = [];
  columnDefs: any[];
  rowData: Array<LiverWaitingListResult> = new Array<LiverWaitingListResult>();
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    }
  };

  constructor(private _router: Router, private _http: HttpClient, private _masterService: MasterService, private _sharedService: SharedService) {
    this.liverWaitingListSearchForm = new FormGroup({
      'civilId': new FormControl(),
      'centralRegNo': new FormControl(),
      'ageFrom': new FormControl(),
      'ageTo': new FormControl(),
      'sex': new FormControl(),
      'regCode': new FormControl(),
      'walCode': new FormControl(),
      'estCode': new FormControl(),
      'recommendTransplantYn': new FormControl(),
      'transplantUrgentYn': new FormControl(),
      'donorAvailableYn': new FormControl(),
    });

    this.getRegionList();
    this.getWilayatList();
    this.getInstiteList();

    this.columnDefs = [
      { headerName: 'Civil ID', field: 'civilId', minWidth: 125 },
      { headerName: 'Registration No.', field: 'centralRegNo', minWidth: 125 },
      { headerName: 'Gender', field: 'sex', minWidth: 125 },
      { headerName: 'Age', field: 'age', minWidth: 150 },
      { headerName: 'Region', field: 'regCode', minWidth: 125 },
      { headerName: 'Wilayat', field: 'walCode', minWidth: 125 },
      { headerName: 'Institute', field: 'estCode', minWidth: 125 },
      { headerName: 'Transplant Recommend Yn', field: 'recommendTransplantYn', minWidth: 125 },
      { headerName: 'Transplant Urgent Yn', field: 'transplantUrgentYn', minWidth: 125 },
      { headerName: 'Donor Available Yn', field: 'donorAvailableYn', minWidth: 125 },
    ];
  }

  ngOnInit() {
    this.genderTypeOptions = AppCompUtils.GENDER;
  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['liver/preparing-liver-waiting-list'], { state: { centralRegNo: event.data.centralRegNo } });
  }

  getList(event?: any) {
    let pageable = {
      page: event ? event.page : 0,
      size: event ? event.rows : AppUtils.E_REGISTRY_PAGINATION_SIZE
    };
    if (this.gridOptions.api) { this.gridOptions.api.showLoadingOverlay(); }

    let body = this.liverWaitingListSearchForm.value;

    body = { pageable, ...body }


    this._http.post(AppUtils.FIND_LIVER_WAITING_LIST, body).subscribe(res => {
      if (res && res['code'] == "S0000" && res['result']) {
        this.rowData = res['result']['content'] || [];
        this.totalRecords = res['result']['totalElements'] || 0;

        for (let i = 0; i < this.rowData.length; i++) {
          this.rowData[i].sex = this.genderTypeOptions
            .filter(s => s.id == this.rowData[i].sex)
            .map(s => s.value)
            .toString();

          this.rowData[i].regCode = this.regionData
            .filter(s => s.regCode == Number(this.rowData[i].regCode))
            .map(s => s.regName)
            .toString();

          this.rowData[i].walCode = this.wallayatList
            .filter(s => s.walCode == Number(this.rowData[i].walCode))
            .map(s => s.walName)
            .toString();

          this.rowData[i].estCode = this.institeList
            .filter(s => s.estCode == Number(this.rowData[i].estCode))
            .map(s => s.estName)
            .toString();

          this.rowData[i].recommendTransplantYn = this.yesNo
            .filter(s => s.id == this.rowData[i].recommendTransplantYn)
            .map(s => s.value)
            .toString();

          this.rowData[i].transplantUrgentYn = this.yesNo
            .filter(s => s.id == this.rowData[i].transplantUrgentYn)
            .map(s => s.value)
            .toString();

          this.rowData[i].donorAvailableYn = this.yesNo
            .filter(s => s.id == this.rowData[i].donorAvailableYn)
            .map(s => s.value)
            .toString();
        }
      } else {
        this.rowData = null;
        Swal.fire('Error!', res ? res['message'] : 'Invalid response', 'error');
      }
    }, error => {
      if (error.status == 401) {
        Swal.fire('Error!', 'Error occurred while retrieving user details', 'error');
      }
    });

  }

  clear(e) {
    this.liverWaitingListSearchForm.reset();
    this.wallayatListFilter = this.wallayatList;
    this.institeListFilter = this.institeList;
    this.rowData = null;
  }

  getRegionList() {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    }, error => {});
  }

  getWilayatList(regCode: any = 0) {
    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    }, error => {});
  }

  getInstiteList(regCode: any = 0, walCode: any = 0) {
    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    }, error => {});
  }


  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }

  regSelect(event: any, field?: any) {
    if (event != undefined) {
      if (this.isEmptyNullZero(event.regCode) === false) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode == event.regCode);
      }
    } else {
      this.wallayatListFilter = this.wallayatList;
      this.institeListFilter = this.institeList;
    }
  }


  walSelect(event: any, field?: any) {
    if (this.isEmptyNullZero(event.target.value) === false) {
      if (this.liverWaitingListSearchForm.value['regCode']) {
        this.institeListFilter = this.institeList.filter(s => s.regCode == this.liverWaitingListSearchForm.value['regCode']);
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode == this.liverWaitingListSearchForm.value['regCode']);
      } else {
        this.institeListFilter = this.institeList;
      }
    } else {
      this.institeListFilter = this.institeList.filter(s => s.walCode == event.target.value);
    }
  }
}
