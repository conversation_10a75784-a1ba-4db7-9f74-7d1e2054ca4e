
export interface CaseDetails  {
  liverTransplantIndications: LiverIndication[];
  familyHistory: FamilyHistory;
  liverCirrhosisStages: LiverCirrhosisStage[];
  liverCirrhosisComplications: LiverComplicationMasterDto[];
  currentManagement: any;

}

export interface LiverIndication {
  id: number;
  value: string;
  checked: boolean;
  remarksYn: string;
  remarks: string;
}

export interface LiverSurgicalInfo{
  id: number;
  paramDesc: string;
}

export interface LiverTransPlantType{
  id: number;
  paramDesc: string;
}

export interface LiverSurgicalSubTypeInfo{
  id: number;
  prevId: number;
  paramDesc: string;
}

export interface LiverTransplantMed{
  id: number;
    prevId: number;
    value: string;
    active: string;
    remarksYn?: string | null;
    isSelected: boolean;
    dose?: string | null;
    level?: string | null;
    subList: LiverTransplantMed[];
    subSubList: LiverTransplantMed[];
}

export interface LiverTransPlantSubType{
  id: number;
  prevId: number;
  paramDesc: string;
}

// export interface LiverCirrhosisStage  {
//   id: number;
//   value: string;
//   score: string;
// }

export interface FamilyHistory {
  hasHistory: boolean;
  remarks: string;
}

export interface LiverCirrhosisStage {
  id: number;
  value: string;
  remarks: string;
  meldException: MeldException;
  peldException: PeldException;
}

export interface MeldException {
  hasException: boolean;
  remarks: string;
}

export interface PeldException {
  hasException: boolean;
  remarks: string;
}

export interface LiverManagement {
  mgmtId: number;
  liverId: number;  // This will map to rgTbLiverRegister
  paramId: number;
  subId: number;
  dose: number;
  frequency: number;
  doneDate: Date;
  remarks: string;
}

export interface LiverManagementRequest {
  liverId: number;
  managementList: LiverManagement[];
}


export interface LiverComplication {
  id: number;
  value: string;
  remarksYn: string;
  remarks: string;
  subList: string[];
}

export interface LiverComplicationSave {
  liverId: number;
  complicationId: number;
  remarks: string;
  subItems: LiverComplicationSubItem[];
}

export interface SubItem {
  remarks: string;
  checked: boolean;
}

export interface LiverComplicationMasterDto {
  id: number;
  value: string;
  remarksYn: string;
  remarks: string;
  subList: string[];
  selected: string;// Added for UI state
  subItems: SubItem[];  // Added for UI state
}

export interface LiverComplicationSubItem {
  itemValue: string;
  checked: boolean;
  remarks: string;
}

export interface LiverRegister {
  liverId: number;
  centralRegNo: number;
  familyHistoryYn: string;
  childPughScore: number;
  childPughClass: string;
  meldScore: number;
  meldNaScore: number;
  meldExceptionYn: string;
  meldExceptionDetails: string;
  peldScore: number;
  peldExceptionYn: string;
  peldExceptionDetails: string;
  dialysisYn: string;
}

export interface StageTransplantData {
  runId: number;
  ckdFailureStage: number;
  dialysisYn: string;
  transplantYn: string;
  preEmptiveYn: string;
  transplantDatr: Date;
  transplantInst: number;
  transplantPlaceDesc: string;
  followupInst: number;
  followUpCountry: number;
  transplantCountry: number;
  transplantReadiness: string;
  transplantWillingness: string;
}

export interface LiverCirrhosisStage {
  id: number;
  value: string;
}
export interface LiverCaseDetailsDto {
  centralRegNo: number;
  activeYn: string;
  patientID: number;
  completedOn: Date;
  createdBy: number;
  createdOn: Date;
  instRegDate: Date;
  modifiedBy: number;
  modifiedOn: Date;
  regInst: number;
  registerType: number;
  localRegReferance: string;
  //rgTbPatientInfo: RgTbPatientInfoDto;
  //liverTransplantIndicationDtos: LiverTransplantIndicationDto[];
  liRegisterDtos: LiverRegister[];
  liverComplicationDtos: LiverComplicationMasterDto[];
 // livManagementDtos: RgLiverManagementDto[];
  //rgTbLabTests: RgTbLabTestsDto[];


}

export interface ResultDecorator {
  code: string;
  message: string;
  result: any;
  // add other properties if present in ResultDecorator
}

export interface LiverCurrMgmtMasterDto {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperDto[];
}

export interface LiverCurrMgmtSubTyperDto {
  id: number;
  prevId: number;
  value: string;
  active: string;
  remarksYn: string;
  name: string;
}

export interface LiverCurrMgmtUIState {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperUIState[];
}
export interface LiverCurrMgmtSubTyperUIState extends LiverCurrMgmtSubTyperDto {
  isSelected: boolean;
  dose1: number;
  dose2: number;
  date: Date | null;
  comments: string;
}

export interface LiverCurrMgmtSaveDto {
  id: number;
  value: string;
  active: string;
  remarksYn: string;
  subList: LiverCurrMgmtSubTyperSaveDto[];
}

export interface LiverCurrMgmtSubTyperSaveDto {
  id: number;
  prevId: number;
  value: string;
  active: string;
  remarksYn: string;
  name: string;
  dose1: number;
  dose2: number;
  date: string;
  comments: string;
}

export interface LiverComplicationDto {
  compId: number;
  liverId: number;
  paramId: number;
  paramYn: string;
  remarks: string;
  noOfTumors: number;
  tumorLength: number;
  milanCriteria: string;
  prevLocoTherapyYn: string;
  prevLocoTherapyDtls: string;
}

export interface LiverManagementDto {
  mgmtId: number;
  liverId: number;
  paramId: number;
  subId: number;
  dose: number;
  frequency: number;
  doneDate: Date;
  remarks: string;
}

export interface RgVwLiverProceduresDto {
  paramId: number;
  paramName: string;
}

export interface RgVwLiverDonorProceduresDto {
  procId: number;
  procedureName: string;
  shortName: string;
  active:string
}

export interface RgProcedureDetailsDto {
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface RgProcedureDetailsSaveDto {
  runId: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}

export interface ContactDetails {
  relationType: number;
  phone: number;
  email: string;
}

export interface ProcedureDetails {
  //runId?: number;
  //centralRegNo: number;
  procId: number;
  doneDate: Date;
  remarks: string;
}


// export interface LiverCurrMgmtMasterDto {
//   id: number;
//   value: string;
//   active: string;
//   remarksYn: string;
//   subList: LiverCurrMgmtSubTyperDto[];
// }

// export interface LiverCurrMgmtSubTyperDto {
//   id: number;
//   prevId: number;
//   value: string;
//   active: string;
//   remarksYn: string;
//   name: string;
//   // Additional fields for UI state
//   isSelected: boolean;
//   dose1: string;
//   dose2: string;
//   date: Date;
//   comments: string;
// }

// export interface LiverCurrMgmtSaveDto {
//   id: number;
//   value: string;
//   active: string;
//   remarksYn: string;
//   subList: LiverCurrMgmtSubTyperDto[];
// }

export interface LiverTransplantIndicationDto {
  transId: number;
  liverId: number;
  paramId: number;
  paramYn: string;
  remarks: string;
}

export class LiverListResult {

  public centralRegNo: number;
  public civilId: number;
  public fullname: string;

  public dob: Date;
  public age: number;
  public sex: string;
  public estCode: number;
  public walCode: number;
  public causeKidney: string;
  public stages: number;
  public dialysis: string;
  public transplant: string;
  public height: number;
  public width: number;
  public bmi: string;
  public bloodGroup: number;
}

export class LiverRegistryResultDto {
  public regNo: number;
  public civilId: number;
  public dateOfBirth: Date;
  public age: number;
  public fullName: string;
  public sex: string;
  public regInst: string;
  public walCode: string;
  public regCode: string;
  public weight: number;
  public height: number;
  public bloodGroup: number;
  public transplantYn: string;
  public transplantReadiness: string;
  public icd: string;
  public deathCase: number;
  public familyHistYn: string;
  public childPughScore: number;
  public childPughClass: string;
  public meldScore: number;
  public meldNaScore: number;
  public meldExceptionYn: string;
  public meldExceptionDtls: string;
  public peldScore: number;
  public peldExceptionYn: string;
  public peldExceptionDtls: string;
}