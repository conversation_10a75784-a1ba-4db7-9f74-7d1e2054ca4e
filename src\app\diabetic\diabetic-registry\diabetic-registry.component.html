<div class="row">
    <div class="input-group input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
        <h6 class="page-title">Diabetic Register</h6>

    </div>
    <div class="col-lg-3 col-md-3 col-sm-3"></div>
    <div class="input-group input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px; ">
        <input type="text" placeholder="Search Registration No" [(ngModel)]="regId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>

        <input type="text" placeholder="Search Civil ID" [(ngModel)]="civilId" class="form-control input-sm"
            (keyup.enter)="search()">
        <span class="input-group-btn">
            <button class="btn btn-default btn-sm" id="search" (click)="search()">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>




<!-- <div class="accordion register"> -->
<ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
    <ngb-panel id="patientDetails" id="ngb-panel-0">
        <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between card-head">
                <h6> Patient Details</h6>
            </div>
        </ng-template>

        <!-- [ngClass]="opened ? 'opened' : 'collapsed'"> -->
        <!-- <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button> -->
        <!-- </div> -->



        <ng-template ngbPanelContent>
            <app-patient-details #patientDetails [submitted]="submitted" [isDiabtic]="true"
                (callMethod)="callMpiMethod()" [nationalityForDiabetic]="nationalityForDiabetic">
            </app-patient-details>
        </ng-template>



    </ngb-panel>
</ngb-accordion>


<form [formGroup]="diabeticRegistry">
    <div>
        <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
            <ngb-panel id="RegistrationINFO" id="ngb-panel-1">
                <ng-template ngbPanelHeader let-opened="opened">
                    <div class="d-flex align-items-center justify-content-between card-head"
                        [ngClass]="opened ? 'opened' : 'collapsed'">
                        <h6> Registration Information</h6>
                        <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                                [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                    </div>
                </ng-template>

                <ng-template ngbPanelContent>
                    <div class="row">
                        <div class="col-lg-2">
                            <div class="form-group">
                                <label>Registration Date <span class="mdtr">*</span></label>
                                <p-calendar showIcon=true formControlName="regDate"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                                <span *ngIf="submittedZero" class="tooltiptext">{{ 'Registration Date is required'
                                    }}</span>

                            </div>

                        </div>


                        <div class="col-lg-2">
                            <div class="form-group">
                                <label>Type <span class="mdtr">*</span></label>
                                <ng-select #entryPoint [items]="diabetesTypesList" [disabled]="true"
                                    [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                    formControlName="diabetesType">
                                    <ng-template ng-option-tmp let-item="item"
                                        let-index="index">{{item.value}}</ng-template>
                                </ng-select>
                                <span *ngIf="submittedOne" class="tooltiptext">{{ 'Diabetes Type is required'
                                    }}</span>
                            </div>

                        </div>
                        <div class="col-lg-2">
                            <div class="form-group">
                                <label>Date of Diagnosis <span class="mdtr">*</span></label>
                                <p-calendar showIcon=true formControlName="diagnosedDate"
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
                                <span *ngIf="submittedThree" class="tooltiptext">{{ 'Date of Diagnosis is required'
                                    }}</span>

                            </div>

                        </div>



                        <div class="col-lg-2">
                            <div class="form-group">
                                <label>File Status</label>
                                <select class="form-control form-control-sm" formControlName="fileStatus">
                                    <option [value]=null></option>
                                    <option [value]="1">Active</option>
                                    <option [value]="2">transfered out</option>
                                    <option [value]="3">Expired</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-1">
                            <div class="form-group">
                                <label>Duration </label>
                                <input type="text" class="form-control form-control-sm" formControlName="duration"
                                    [(ngModel)]="rd.duration">
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group sp top-space">
                                <label id="moh" class="resize">Diagnosed AS :<span class="mdtr">*</span></label>
                                <label class="chkbox-outer"><input type="radio" formControlName="regType"
                                        value="N">Newly Diagnosed</label>
                                <label class="chkbox-outer"><input type="radio" formControlName="regType" value="O">Old
                                    Diagnosed</label>
                                <span *ngIf="submittedFive" class="tooltiptext">{{ 'Diagnosed AS is required'
                                    }}</span>
                            </div>

                        </div>
                    </div>

                    <div class="mcard">
                        <div class="mcard-header">History and Physical Examination</div>
                        <div class="mcard-body">
                            <div class="row">

                                <div class="col-lg-2">
                                    <div class="form-group">
                                        <label>Weight <span class="mdtr">*( kg )</span></label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b" formControlName="weight"
                                            (blur)="getBmi()">
                                        <span *ngIf="submittedfour" class="tooltiptext">{{ 'weight is required'
                                            }}</span>

                                    </div>


                                </div>

                                <div class="col-lg-2">
                                    <div class="form-group">
                                        <label>Height <span class="mdtr">*( cm )</span></label>
                                        <input type="number" class="form-control form-control-sm"
                                            pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b" formControlName="hight"
                                            (blur)="getBmi()">
                                        <span *ngIf="submittedfour" class="tooltiptext">{{ 'hight is required'
                                            }}</span>
                                    </div>
                                </div>

                                <div class="col-lg-1">
                                    <div class="form-group">
                                        <label>BMI </label>
                                        <input type="number" class="form-control form-control-sm" formControlName="bmi"
                                            readonly>
                                    </div>
                                </div>


                                <div class="col-lg-1">
                                    <div class="form-group">
                                        <label>BP - diastolic </label>
                                        <input type="number" class="form-control form-control-sm"
                                            formControlName="bp_Dia">
                                    </div>
                                </div>

                                <div class="col-lg-1">
                                    <div class="form-group">
                                        <label>BP - systolic</label>
                                        <input type="number" class="form-control form-control-sm"
                                            formControlName="bp_Sys">
                                    </div>
                                </div>
                                <div class="col-lg-3 col-xl-2">
                                    <div class="form-group">
                                        <label>Mode of presentation</label>
                                        <ng-select [isOptionDisabled]="true" [items]="modeOfPrestList"
                                            [virtualScroll]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                            formControlName="mode_Presentation">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>


                                <div class="col-lg-3 col-xl-2">
                                    <div class="form-group">
                                        <label>Family History of Diabetes</label>
                                        <ng-select [items]="familyHistoryOptions" [virtualScroll]="true"
                                            [disabled]="true" placeholder="Select" bindLabel="value" bindValue="id"
                                            formControlName="family_Id">
                                            <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>


                    <!-- *****************************************Lab Investigation (Tick if test sent at diagnosis)***************************************** -->
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="mcard">
                                <div class="mcard-header">Lab Investigation (Tick if test sent at diagnosis)</div>
                                <div class="mcard-body">

                                    <div *ngIf="diabLabInvestList && diabLabInvestList.length" class="mcard-body">
                                        <table #bLabtable class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th width="5%"></th>
                                                    <th>Test Name</th>
                                                    <th>Test Result</th>
                                                    <th>Remark</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    *ngFor="let item of diabLabInvestList | slice: (page1-1) * pageSize : (page1-1) * pageSize + pageSize">
                                                    <td>
                                                        <input type="checkbox" [value]="item.id" id="check"
                                                            (change)="onChicked(item, $event)"
                                                            [checked]="item.checked || tasnim" />
                                                    </td>
                                                    <td>{{item.value}}</td>

                                                    <td>
                                                        <select *ngIf="item.id === 1544"
                                                            [value]="item.lab_Result != undefined ? item.lab_Result : null"
                                                            (input)="onInput(item, $event)"
                                                            class="form-control form-control-sm" id="lab_Result"
                                                            [attr.disabled]="!item.checked ? true : null">
                                                            <option selected>Select</option>
                                                            <option value="Present">Present</option>
                                                            <option value="Absent">Absent</option>

                                                        </select>
                                                        <input *ngIf="item.id !== 1544"
                                                            [value]="item.lab_Result != undefined ? item.lab_Result : null"
                                                            (input)="onInput(item, $event)"
                                                            class="form-control form-control-sm" type="text"
                                                            id="lab_Result"
                                                            [attr.disabled]="!item.checked ? true : null" />
                                                    </td>

                                                    <td>
                                                        <input class="form-control form-control-sm" type="text"
                                                            id="remarks"
                                                            [value]="item.remarks != undefined ? item.remarks : null"
                                                            (input)="onInput(item, $event)"
                                                            [attr.disabled]="!item.checked ? true : null" />
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                        <ngb-pagination class="d-flex justify-content-center" [(page)]="page1"
                                            [pageSize]="pageSize" [collectionSize]="diabLabInvestList.length">
                                        </ngb-pagination>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- *****************************************Lab Investigation (Tick if test sent at diagnosis) Ends Here***************************************** -->


                        <div class="col-sm-6">
                            <div class="mcard">
                                <div class="mcard-header">Management Plan</div>

                                <div class="mcard-body">

                                    <div class="form-group sp">
                                        <label id="mof" class="resize">Managed By:</label>

                                        <label class="chkbox-outer">
                                            <input type="checkbox" class="form-check-input"
                                                formControlName="lifestyleModify"
                                                (change)="updateCheckboxValue('lifestyleModify', $event)">
                                            Life Style Modification
                                        </label>

                                        <label class="chkbox-outer">
                                            <input type="checkbox" class="form-check-input"
                                                formControlName="oralHypoDrugs"
                                                (change)="updateCheckboxValue('oralHypoDrugs', $event)">
                                            Oral Hypoglycemic Drugs
                                        </label>

                                        <label class="chkbox-outer">
                                            <input type="checkbox" class="form-check-input"
                                                formControlName="ingGlptAgonist"
                                                (change)="updateCheckboxValue('ingGlptAgonist', $event)">
                                            GLP-1 Agonist
                                        </label>

                                        <label class="chkbox-outer">
                                            <input type="checkbox" class="form-check-input" formControlName="insulin"
                                                (change)="updateCheckboxValue('insulin', $event)">
                                            Insulin
                                        </label>
                                    </div>


                                    <div class="col-12">
                                        <div class="form-group sp" *ngIf="showInsulinDetails">
                                            <label id="mop" class="resize"> Insulin Regimen :</label>
                                            {{dmg.insulin}}

                                            <label class="chkbox-outer">
                                                <input type="radio" name="insulinType" formControlName="insulinType"
                                                    value="I" required>Single
                                            </label>
                                            <label class="chkbox-outer">
                                                <input type="radio" name="insulinType" formControlName="insulinType"
                                                    value="S" required>Split (Mixed)
                                            </label>
                                            <label class="chkbox-outer">
                                                <input type="radio" name="insulinType" formControlName="insulinType"
                                                    value="M" required>MDI (Multiple Daily Injections)
                                            </label>
                                            <label class="chkbox-outer">
                                                <input type="radio" name="insulinType" formControlName="insulinType"
                                                    value="P" required>Pump
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-sm-12 mt-2">

                                        <div class="form-group sp">


                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <label>Is the patient on Sensor?</label>


                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio"
                                                            formControlName="sensorYn" value="Y"> Yes
                                                        <span class="mx-2"></span>
                                                        <input class="form-check-input" type="radio"
                                                            formControlName="sensorYn" value="N"> No
                                                    </div>
                                                </div>

                                            
                                            <div class="text col-sm-6">
                                                <div *ngIf="diabeticRegistry.get('sensorYn')?.value === 'Y'">

                                                    <div class="col-sm-6">

                                                        <label for="sensorType">What type of Sensor?</label>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <ng-select #sensorDropdown appendTo="body"
                                                                [items]="sensorTypeList" [virtualScroll]="true"
                                                                formControlName="sensorType" placeholder="Select"
                                                                bindLabel="value" bindValue="id">
                                                            </ng-select>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div
                                                                *ngIf="diabeticRegistry.get('sensorType')?.value === 1560 ">
                                                                <input type="text" class="form-control form-control-sm"
                                                                    formControlName="sensorOther"
                                                                    placeholder="Please specify other sensor">
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div></div>
                                            <!-- Show input only if "Others" is selected -->

                                        
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <label>Is the patient on Pump?</label>
                                                <div class="form-check form-check-inline label3">
                                                    <input class="form-check-input" type="radio"
                                                        formControlName="pumpYn" value="Y"> Yes
                                                    <span class="mx-2"></span>
                                                    <input class="form-check-input" type="radio"
                                                        formControlName="pumpYn" value="N"> No
                                                </div>
                                            </div>
                                            <div class="text col-sm-6">
                                                <div *ngIf="diabeticRegistry.get('pumpYn')?.value === 'Y'">
                                                    <div class="col-sm-6">
                                                        <label for="pumpType">What type of Pump?</label>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <ng-select [items]="pumpType" [isOptionDisabled]="true"
                                                                [virtualScroll]="true" placeholder="Select"
                                                                bindLabel="value" bindValue="id"
                                                                formControlName="pumpType" id="pumpType"></ng-select>



                                                            <ng-template ng-option-tmp let-item="item"
                                                                let-index="index">
                                                                {{ item.value }} <!-- Display the value here -->
                                                            </ng-template>

                                                        </div>


                                                        <!-- Show input only if "Others" is selected -->
                                                        <div class="col-sm-6 px-3">
                                                            <div
                                                                *ngIf="diabeticRegistry.get('pumpType')?.value === 1561 ">
                                                                <input type="text" class="form-control form-control-sm"
                                                                    formControlName="pumpOther"
                                                                    placeholder="Please specify other pump">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>

                                </div>

                            </div>



                        </div>

                        </div>

                 
   


    </ng-template>
    </ngb-panel>
    </ngb-accordion>
    </div>
</form>
<div class="btn-container">

</div>

<div [formGroup]="followUpForm">
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
        <ngb-panel id="patientDetails" id="ngb-panel-0">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head">
                    <h6> Follow Up</h6>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>

                <div class="row">

                    <div class="col-sm-2">
                        <p-calendar class="addNewCale" appendTo="body" dateFormat="dd-mm-yy" showIcon="true"
                            [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]="today"
                            yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
                            (onSelect)="addNewFollowUp($event, 'get');">
                        </p-calendar>

                        <ul class="list-group followup-nav">
                            <li class="list-group-item added-list" *ngFor="let item of diabVisitDetails; let i = index"
                                (click)="addEditFollowUp(this.followUpForm.value, 'add', item, i);"
                                [ngClass]="{'active': selectedFUDate == item.visitDate}">
                                {{ item.visitDate | date: 'dd-MM-yyyy' }}
                                <button *ngIf="item.visitId == null" class="fas fa-trash"
                                    (click)="removeFollowUp(i)"></button>
                                <button *ngIf="selectedFUDate === item.visitDate" class="fa fa-print"></button>
                            </li>
                        </ul>
                    </div>

                    <div *ngIf="selectedFUDate" class="col-sm-10 border-left">
                        <div class="mcard">
                            <div class="mcard-body">
                                <div class="row">
                                    <div class="col-lg-2">
                                        <div class="form-group">
                                            <label>Weight <span class="mdtr">*( kg )</span></label>
                                            <input type="number" class="form-control form-control-sm"
                                                pattern="\b([1-9]|[1-9][0-9]|1[0-4][0-9]|14)\b"
                                                formControlName="weight">
                                        </div>
                                    </div>

                                    <div class="col-lg-2">
                                        <div class="form-group">
                                            <label>Height <span class="mdtr">*( cm )</span></label>
                                            <input type="number" class="form-control form-control-sm"
                                                pattern="\b([1-9]|[1-9][0-9]|1[0-9][0-9]|19)\b" formControlName="hight"
                                                (blur)="getvisitBmi()">

                                        </div>
                                    </div>

                                    <div class="col-lg-1">
                                        <div class="form-group">
                                            <label>BMI </label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="bmi" readonly>
                                        </div>
                                    </div>


                                    <div class="col-lg-1">
                                        <div class="form-group">
                                            <label>BP - diastolic </label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="bp_Dia">
                                        </div>
                                    </div>

                                    <div class="col-lg-1">
                                        <div class="form-group">
                                            <label>BP - systolic</label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="bp_Sys">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-xl-2">
                                        <div class="form-group">
                                            <label>Mode of presentation</label>
                                            <ng-select [items]="modeOfPrestList" [virtualScroll]="true"
                                                placeholder="Select" bindLabel="value" bindValue="id"
                                                formControlName="mode_Presentation" [disabled]="true">
                                                <ng-template ng-option-tmp let-item="item" let-index="index">
                                                    {{item.value}}
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                    <!-- <div class="col-lg-2">
                                            <div class="form-group">
                                                <label>Type <span class="mdtr">*</span></label>
                                                <ng-select #entryPoint [items]="diabetesTypesList"
                                                    [virtualScroll]="true" placeholder="Select" bindLabel="value"
                                                    bindValue="id" formControlName="diabetesType" [disabled]="true">
                                                    <ng-template ng-option-tmp let-item="item" let-index="index"
                                                        disabled>{{item.value}}</ng-template>
                                                </ng-select>
                                                <span *ngIf="submittedOne" class="tooltiptext">{{ 'Diabetes Type is
                                                    required'
                                                    }}</span>
                                            </div>

                                        </div> -->
                                </div>
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-sm-6">
                                <div class="mcard">

                                    <div class="mcard-header">Lab Investigation (Tick if test sent at diagnosis)
                                    </div>
                                    <div class="mcard-body">

                                        <div *ngIf="diabLabInvestVisitList && diabLabInvestVisitList.length"
                                            class="mcard-body">
                                            <table #bLabtable class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th width="5%"></th>
                                                        <th>Test Name</th>
                                                        <th>Test Result</th>
                                                        <th>Remark</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        *ngFor="let item of diabLabInvestVisitList | slice: (page2-1) * pageSize : (page2-1) * pageSize + pageSize">
                                                        <!-- <td>
                                                                <input type="checkbox" [value]="item.id" id="check_v"
                                                                    (change)="onChicked(item, $event)" [(ngModel)]="item.checked"
                                                                    [checked]="item.checked || tasnim" />
                                                            </td> -->
                                                        <td>
                                                            <input type="checkbox" [value]="item.id" id="check_v"
                                                                (change)="onChicked(item, $event)"
                                                                [(ngModel)]="item.checked"
                                                                [ngModelOptions]="{standalone: true}"
                                                                [checked]="item.checked || tasnim" />
                                                        </td>
                                                        <td>{{item.value}}</td>

                                                        <td>
                                                            <select *ngIf="item.id === 1544"
                                                                [value]="item.lab_Result != undefined ? item.lab_Result : null"
                                                                (input)="onInput(item, $event)"
                                                                class="form-control form-control-sm" id="lab_Result"
                                                                [attr.disabled]="!item.checked ? true : null">
                                                                <option selected>Select</option>
                                                                <option value="Present">Present</option>
                                                                <option value="Absent">Absent</option>

                                                            </select>
                                                            <input *ngIf="item.id !== 1544"
                                                                class="form-control form-control-sm" type="text"
                                                                id="lab_Result" formControlName="lab_Result"
                                                                [value]="item.lab_Result != undefined ? item.lab_Result : null"
                                                                (input)="onInput(item, $event)"
                                                                [attr.disabled]="!item.checked ? true : null" />
                                                        </td>

                                                        <td>
                                                            <input class="form-control form-control-sm" type="text"
                                                                id="remarks" formControlName="remarks"
                                                                [value]="item.remarks != undefined ? item.remarks : null"
                                                                (input)="onInput(item, $event)"
                                                                [attr.disabled]="!item.checked ? true : null" />
                                                        </td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                            <ngb-pagination class="d-flex justify-content-center" [(page)]="page2"
                                                [pageSize]="pageSize" [collectionSize]="diabLabInvestVisitList.length">
                                            </ngb-pagination>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="mcard">
                                    <div class="mcard-header">Management Plan</div>

                                    <div class="mcard-body">

                                        <div class="form-group sp">
                                            <label id="mof" class="resize">Managed By : </label>

                                            <label class="chkbox-outer">
                                                <input type="checkbox" class="form-check-input" name="lifestyleModify"
                                                    formControlName="lifestyleModify"
                                                    (change)="updateFollowUpCheckboxValue('lifestyleModify', $event)">
                                                Life Style Modification
                                            </label>

                                            <label class="chkbox-outer">
                                                <input type="checkbox" class="form-check-input"
                                                    formControlName="oralHypoDrugs"
                                                    (change)="updateFollowUpCheckboxValue('oralHypoDrugs', $event)">
                                                Oral Hypoglycemic Drugs
                                            </label>

                                            <label class="chkbox-outer">
                                                <input type="checkbox" class="form-check-input"
                                                    formControlName="ingGlptAgonist"
                                                    (change)="updateFollowUpCheckboxValue('ingGlptAgonist', $event)">
                                                GLP-1 Agonist
                                            </label>
                                            <label class="chkbox-outer">
                                                <input type="checkbox" class="form-check-input"
                                                    formControlName="insulin"
                                                    (change)="updateFollowUpCheckboxValue('insulin', $event)">
                                                Insulin
                                            </label>
                                        </div>

                                        <div class="col-lg-12">
                                            <div class="form-group sp" *ngIf="showFolloUpInsulinDetails">
                                                <label id="mop" class="resize"> Insulin Regimen :</label>
                                                {{dmg.insulin}}
                                                <label class="chkbox-outer">
                                                    <input type="radio" name="insulinType" formControlName="insulinType"
                                                        value="I" required>Single
                                                </label>
                                                <label class="chkbox-outer">
                                                    <input type="radio" name="insulinType" formControlName="insulinType"
                                                        value="S" required>Split (Mixed)
                                                </label>
                                                <label class="chkbox-outer">
                                                    <input type="radio" name="insulinType" formControlName="insulinType"
                                                        value="M" required>MDI (Multiple Daily Injections)
                                                </label>
                                                <label class="chkbox-outer">
                                                    <input type="radio" name="insulinType" formControlName="insulinType"
                                                        value="P" required>Pump
                                                </label>
                                            </div>
                                            <div>

                                                <div class="form-group sp">

                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <label>Is the patient on Sensor?</label>
                                                            <div class="form-check form-check-inline">
                                                                <input class="form-check-input" type="radio"
                                                                    formControlName="sensorYn" value="Y"> Yes
                                                                <span class="mx-2"></span>
                                                                <input class="form-check-input" type="radio"
                                                                    formControlName="sensorYn" value="N"> No
                                                            </div>
                                                        </div>
                                                        <div class="label1 col-sm-6">
                                                            <div *ngIf="followUpForm.get('sensorYn')?.value === 'Y'">
                                                               
                                                                  

                                                                        <label for="sensorType">What type of
                                                                            Sensor?</label> 
                                                                             <div class="row">
                                                        <div class="col-sm-6">

                                                                        <ng-select [items]="sensorTypeList"
                                                                            [isOptionDisabled]="true"
                                                                            [virtualScroll]="true" placeholder="Select"
                                                                            bindLabel="value" bindValue="id"
                                                                            formControlName="sensorType"
                                                                            id="sensorType">

                                                                            <!-- <ng-template ng-option-tmp let-item="item"
                                                                        let-index="index">
                                                                        {{ item.value }}
                                                                    </ng-template> -->
                                                                        </ng-select>
                                                                    </div>
                                                               
                                                            <!-- Show input only if "Others" is selected -->
                                                            <div class="col-sm-6">
                                                                <div
                                                                    *ngIf="followUpForm.get('sensorType')?.value === 1560 ">
                                                                    <input type="text"
                                                                        class="form-control form-control-sm"
                                                                        formControlName="sensorOther"
                                                                        placeholder="Please specify other sensor">
                                                                </div>
                                                            </div>
                                                        </div></div></div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <label>Is the patient on Pump?</label>
                                                            <div class="form-check form-check-inline label3">
                                                                <input class="form-check-input" type="radio"
                                                                    formControlName="pumpYn" value="Y"> Yes
                                                                <span class="mx-2"></span>
                                                                <input class="form-check-input" type="radio"
                                                                    formControlName="pumpYn" value="N"> No
                                                            </div>
                                                        </div>
                                                        <div class="label2 col-sm-6">
                                                            <div *ngIf="followUpForm.get('pumpYn')?.value === 'Y'">
                                                                
                                                                    <label for="pumpType">What type of Pump?</label>
                                                                <div class="row">
                                                        <div class="col-sm-6">

                                                                <ng-select [items]="pumpType" [isOptionDisabled]="true"
                                                                    [virtualScroll]="true" placeholder="Select"
                                                                    bindLabel="value" bindValue="id"
                                                                    formControlName="pumpType" id="pumpType">

                                                                    <ng-template ng-option-tmp let-item="item"
                                                                        let-index="index">
                                                                        {{ item.value }}
                                                                        <!-- Display the value here -->
                                                                    </ng-template>
                                                                </ng-select></div>
                                                                <div class="col-sm-6">
                                                            <div *ngIf="followUpForm.get('pumpType')?.value === 1561 ">
                                                                <input type="text" class="form-control form-control-sm"
                                                                    formControlName="pumpOther"
                                                                    placeholder="Please specify other pump">
                                                            </div>
                                                        </div>
                                                            </div>

                                                        </div></div>
                                                        <!-- Show input only if "Others" is selected -->
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col-6">
                                <div class="mcard">
                                    <div class="mcard-header">Final Dignosis(ICD 10)</div>

                                    <div class="mcard-body">
                                        <div class="row">

                                            <div class="col-lg-12 col-md-12 col-sm-12">
                                                <div class="form-group">
                                                    <label>ICD</label>
                                                    <ng-select #entryPoint [items]="icdList" [virtualScroll]="true"
                                                        placeholder="Select" bindLabel="disease" bindValue="icd"
                                                        formControlName="finalDiagnosis">
                                                        <ng-template ng-option-tmp let-item="item"
                                                            let-index="index">{{item.disease}}
                                                        </ng-template>
                                                    </ng-select>

                                                </div>
                                            </div>


                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-sm-12">
                                                <div class="form-group">

                                                    <textarea class="form-control form-control-sm" placeholder="remarks"
                                                        style="
                                            height: 82px; " formControlName="visitRemarks">
                                                    </textarea>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mcard">
                                    <div class="mcard-header">Advice</div>
                                    <div class="mcard-body">
                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-sm-12" style="
                                    height: 133px;
                                ">
                                                <div class="form-group">

                                                    <textarea class="form-control form-control-sm"
                                                        formControlName="advice" style="
                                            height: 123px;
                                        ">
                                                    </textarea>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-template>
        </ngb-panel>

    </ngb-accordion>



</div>
<div class="btn-container">
    <button class="btn btn-sm btn-primary" (click)="save();">Save</button>
    <button id="spaceq" class="btn btn-sm btn-secondary" (click)="clear()"> Clear</button>
</div>