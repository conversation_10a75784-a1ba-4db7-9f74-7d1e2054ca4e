<p>Child Nutrition Registry Listing</p>
<div class="content-wrapper mb-2">
    <form [formGroup]="childNutritionSearchForm">
        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>EPI NO</label>
                    <input type="text" class="form-control form-control-sm" formControlName="epiNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>NUTRITION NO</label>
                    <input type="text" class="form-control form-control-sm" formControlName="nutritionNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Region</label>
                    <ng-select #entryPoint [items]="regionData" [virtualScroll]="true" placeholder="Select"
                        bindLabel="regName" bindValue="regCode" formControlName="regCode"
                        (change)="regSelect($event,'region')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.regName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Wilayat</label>
                    <ng-select #entryPoint [items]="wallayatListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="walName" bindValue="walCode" formControlName="walCode"
                        (change)="walSelect($event,'wilayat')">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.walName}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Institute</label>
                    <ng-select #entryPoint [items]="institeListFilter" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="regInst">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.estName}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Gender</label>
                    <ng-select #entryPoint [items]="gender" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="sex">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Cause of Outcome</label>
                    <ng-select #entryPoint [items]="assOutcome" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="paramId" formControlName="outComeCause">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-3 col-xl-2">
                <label>Registration Date</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="regDateFrom" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="From"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="regDateTo" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="To"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Specified Action</label>
                    <ng-select #entryPoint [items]="malStatus" [virtualScroll]="true" placeholder="Select"
                        bindLabel="paramDesc" bindValue="paramId" formControlName="status">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.paramDesc}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>CENTRAL REG NO</label>
                    <input type="text" class="form-control form-control-sm" formControlName="centralRegNo">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>VISIT TYPE</label>
                    <ng-select #entryPoint [items]="visitTypeList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="visitType">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>

                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>HB_LEVEL</label>
                    <div class="d-flex">
                        <input type="number" class="form-control me-2" placeholder="From" formControlName="hbLevelFrom"
                            min="1" />
                        <input type="number" class="form-control" placeholder="To" formControlName="hbLevelTo"
                            min="1" />
                    </div>
                </div>
            </div>




            <div class="col-lg-3 col-xl-2">
                <label>VISIT DATE</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="visitDateFrom" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="From"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="col-sm-6 pl-0">
                        <div class="form-group">
                            <p-calendar dateFormat="dd-mm-yy" formControlName="visitDateTo" monthNavigator="true"
                                yearNavigator="true" placeholder="dd-mm-yyyy" yearRange="1930:2030" placeholder="To"
                                showButtonBar="true">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Length/Height for Age Z-Score</label>
                    <input type="text" class="form-control form-control-sm" formControlName="haZscore">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Length/Height for Age</label>
                    <ng-select #entryPoint [items]="haRatingList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="haRating">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Weight for Age Z-Score</label>
                    <input type="text" class="form-control form-control-sm" formControlName="waZscore">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Weight for Age</label>
                    <ng-select #entryPoint [items]="waRatingList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="waRating">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>HB_Diagnosis</label>
                    <ng-select #entryPoint [items]="hbLevelListDiagnosis" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="hbDiagnosis">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}</ng-template>
                    </ng-select>
                </div>
            </div>





            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Weight for Length/Height Z-Score</label>
                    <input type="text" class="form-control form-control-sm" formControlName="whZscore">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>Weight for Length/Height</label>
                    <ng-select #entryPoint [items]="whRatingList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="whRating">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>File Activation</label>
                    <ng-select #entryPoint [items]="fileActivation" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="activeYn">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>WH_PROGRESS</label>
                    <ng-select #entryPoint [items]="whProgressList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="whProgress">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>WA_PROGRESS</label>
                    <ng-select #entryPoint [items]="waProgressList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="waProgress">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>HA_PROGRESS</label>
                    <ng-select #entryPoint [items]="haProgressList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="haProgress">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-2 col-md-3 col-sm-3">
                <div class="form-group">
                    <label>HCA_PROGRESS</label>
                    <ng-select #entryPoint [items]="hcaProgressList" [virtualScroll]="true" placeholder="Select"
                        bindLabel="value" bindValue="id" formControlName="hcaProgress">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{item.value}}
                        </ng-template>
                    </ng-select>
                </div>
            </div>



            <!-- <p-selectButton class=".ui-widget-header .ui-button, .ui-widget-content .ui-button, .ui-widget.ui-buttonui-button" [options]="paymentOptions" [(ngModel)]="value" [multiple]="true" optionLabel="name" optionValue="value"></p-selectButton> -->

            <div class="col-lg-4 col-md-3 col-sm-3">

                <div class="form-group mt-4">
                    <div class="form-check-inline">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm diarrhea-btn dia-yes-sel"
                                [class.active]="diFaliedYnValue==true" (click)="setDiaFaliedYn('Y')"><i
                                    class="fa fa-check"></i></button>
                            <button type="button" class="btn btn-sm diarrhea-btn dia-null-sel"
                                [class.active]="diFaliedYnValue==null" (click)="setDiaFaliedYn(null)"><i
                                    class="fa fa-minus"></i></button>
                            <button type="button" class="btn btn-sm diarrhea-btn dia-no-sel"
                                [class.active]="diFaliedYnValue==false" (click)="setDiaFaliedYn('N')"><i
                                    class="fa fa-times"></i></button>

                        </div>
                        <label class="form-check-label space-left">DIARRHEA</label>
                    </div>
                    <div class="form-check-inline">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm oedema-btn oed-yes-sel"
                                [class.active]="oedFaliedYnValue==true" (click)="setOedFaliedYn('Y')"><i
                                    class="fa fa-check"></i></button>
                            <button type="button" class="btn btn-sm oedema-btn oed-null-sel"
                                [class.active]="oedFaliedYnValue==null" (click)="setOedFaliedYn(null)"><i
                                    class="fa fa-minus"></i></button>
                            <button type="button" class="btn btn-sm oedema-btn oed-no-sel"
                                [class.active]="oedFaliedYnValue==false" (click)="setOedFaliedYn('N')"><i
                                    class="fa fa-times"></i></button>


                        </div>
                        <label class="form-check-label space-left">OEDEMA</label>
                    </div>
                </div>

            </div>
        </div>
        <div class="text-right col-lg-12 col-md-12 col-sm-12">
            <div class="btn-box">
                <button type="submit" [disabled]="!rowData || (rowData && rowData.length == 0)"
                    (click)=" showDashboard()" class="btn btn-primary ripple"> Dashboard</button>
                <button type="submit" (click)=" exportToExcel()" class="btn btn-primary ripple"> Export Excel</button>
                <button type="reset" (click)="clear($event)" class="btn btn-sm btn-secondary">Clear</button>
                <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
            </div>
        </div>
    </form>
</div>

<div style="margin-top:20px">
    <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData"
        [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
    </ag-grid-angular>

    <div *ngIf="rowData && rowData.length > 0">
        <p-paginator #childNutritionPaginator rows={{paginationSize}} totalRecords="{{totalRecords}}"
            (onPageChange)="getList($event)" showCurrentPageReport="true"
            currentPageReportTemplate="(Total: {{totalRecords}} records)" pageLinkSize="10"
            [rowsPerPageOptions]="[10, 20, 30]">
        </p-paginator>
    </div>

</div>