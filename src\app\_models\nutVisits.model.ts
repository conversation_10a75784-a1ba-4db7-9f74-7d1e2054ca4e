import { AncLabInvestModel } from "./ancLabInvest.model";
import { NutCfAssessment } from "./nutCfAssessment.model";

export class NutVisitsModel {
    visitId: number;
    ageAtVisit: number;
    createdBy: string;
    createdDate: Date;
    diarrheaResult: string;
    diarrheaYn: string;
    haRating: string;
    haZscore: number;
    hbLevel: number;
    headCircum: number;
    healthStatus: string;
    height: number;
    modifiedBy: number;
    modifiedDate: Date;
    nutCounselling: string;
    oedemaYn: string;
    otherDiseases: string;
    visitDate: Date;
    visitType: string;
    waRating: string;
    waZscore: number;
    weight: number;
    whRating: string;
    whZscore: number;
    
    // rgTbNutLabInvsts: Array <AncLabInvestModel>;
    // rgTbNutCfAssessment: Array <NutCfAssessment>;
    index: number;
  }