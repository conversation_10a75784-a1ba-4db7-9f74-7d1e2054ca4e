<div class="enc-register">
    <div class="row">
        <div class="col-sm-9">
            <h6 class="page-title">ANC Register</h6>
        </div>
        <div class="col-sm-3">
            <form [formGroup]="searchForm">
                <div class="row top-search">
                    <label class="col col-form-label text-right">ANC No.</label>
                    <div class="col">
                        <input type="text" class="form-control form-control-sm" formControlName="ancNo"
                            (keyup.enter)="search()">
                    </div>
                    <label class="col col-form-label text-right">Civil Id</label>
                    <div class="col">
                        <input type="text" class="form-control form-control-sm" formControlName="civilId"
                            (keyup.enter)="openSmallWindow()">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Patient Details -->
    <ngb-accordion #acc="ngbAccordion" activeIds="ngb-active">
        <ngb-panel id="patientDetails" id="ngb-active">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6>Patient Details</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-patient-details [submitted]="submitted" [patientForm]="patientForm" #patientDetails
                    (callMethod)="callMpiMethod()"></app-patient-details>
            </ng-template>
        </ngb-panel>
        <!-- Booking -->
        <ngb-panel id="booking" id="ngb-active">
            <ng-template ngbPanelHeader let-opened="opened">
                <div class="d-flex align-items-center justify-content-between card-head"
                    [ngClass]="opened ? 'opened' : 'collapsed'">
                    <h6>Booking</h6>
                    <button ngbPanelToggle class="btn btn-link p-0"><i class="fas"
                            [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <form [formGroup]="bookingForm">
                    <div class="row">
                        <div class="col-sm-6 divider-right">
                            <div class="col-sm-12 ">
                                <div class="row" style="justify-content:space-around;">
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>ANC No. <span class="mdtr">*</span></label>
                                            <input type="text" class="form-control form-control-sm"
                                                formControlName="ancNo">
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="bookingForm.get('ancNo').hasError('required') && bookingForm.get('ancNo').touched">
                                            ANC No. is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>ANC Institutes <span class="mdtr">*</span></label>
                                            <ng-select #entryPoint appendTo="body" [items]="ancInstitutesFilter"
                                                [virtualScroll]="true" formControlName="ancInstitute"
                                                placeholder="Select" bindLabel="estName" bindValue="estCode"
                                                (change)="changeAncInstitute($event)">

                                                <ng-option *ngFor="let c of institutes" [value]="c.estCode">{{ c.estName
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="bookingForm.get('ancInstitute').hasError('required') && bookingForm.get('ancInstitute').touched">
                                            ANC Institutes is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>LMP <span class="mdtr">*</span></label>
                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                formControlName="lmp" [ngModelOptions]="{standalone: true}"
                                                monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                                                yearNavigator="true" showButtonBar="true"></p-calendar>
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="bookingForm.get('lmp').hasError('required') && bookingForm.get('lmp').touched">
                                            LMP is required.
                                        </span>
                                    </div>

                                    <div class="col-sm-5 border rounded m-1">
                                        <div class="row px-2 pt-2">
                                            <label>Expected Dates of Delivery:</label>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <div class="form-group">
                                                        <label>By LMP <span class="mdtr">*</span></label>
                                                        <p-calendar appendTo="body" dateFormat="dd-mm-yy"
                                                            showIcon="true" formControlName="eddCalc"
                                                            [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                            yearRange="1930:2030" yearNavigator="true"
                                                            showButtonBar="true"></p-calendar>
                                                    </div>
                                                    <span class="tooltiptext"
                                                        *ngIf="bookingForm.get('eddCalc').hasError('required') && bookingForm.get('eddCalc').touched">
                                                        By LMP is required.
                                                    </span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <div class="form-group">
                                                        <label>By Scan</label>
                                                        <p-calendar appendTo="body" dateFormat="dd-mm-yy"
                                                            showIcon="true" formControlName="eddScan"
                                                            [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                            yearRange="1930:2030" yearNavigator="true"
                                                            showButtonBar="true"></p-calendar>
                                                    </div>
                                                </div>
                                                <div class="col-sm-4">
                                                    <div class="form-group">
                                                        <label>Corrected</label>
                                                        <p-calendar appendTo="body" dateFormat="dd-mm-yy"
                                                            showIcon="true" formControlName="eddCorrected"
                                                            [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                            yearRange="1930:2030" yearNavigator="true"
                                                            showButtonBar="true"></p-calendar>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>Gravida <span class="mdtr">*</span></label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="gravida">
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="bookingForm.get('gravida').hasError('required') && bookingForm.get('gravida').touched">
                                            Gravida is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>Parity <span class="mdtr">*</span></label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="parity">
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="bookingForm.get('parity').hasError('required') && bookingForm.get('parity').touched">
                                            Parity is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label>Abortion</label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="abortion">
                                        </div>
                                    </div>
                                    <div class="col-sm-5">
                                        <div class="form-group">
                                            <label>Ectopic</label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="ectopicPreg">
                                        </div>
                                    </div>
                                    <div class="col-sm-5 border rounded m-1" style="
                                    padding-top: 9px;
                                ">
                                        <div class="row pb-2">
                                            <div class="col-sm-5">
                                                <div class="form-group">
                                                    <label title="Early pregnancy U/S findings">EP U/S findings</label>
                                                    <ng-select appendTo="body" [items]="findings" [virtualScroll]="true"
                                                        (change)="clearInputFelid($event, bookingForm.controls.earlyUsFindings, bookingForm.controls.earlyUsRemarks)"
                                                        formControlName="earlyUsFindings" placeholder="Select"
                                                        bindLabel="name" bindValue="code">
                                                        <ng-option *ngFor="let c of findings" [value]="c.code">{{ c.name
                                                            }}</ng-option>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="col-sm-7">
                                                <span>EP U/S Remarks </span>
                                                <div>
                                                    <textarea class="form-control " rows="1"
                                                        (keyup)="autoGrowTextZone($event)"
                                                        (keydown)="autoGrowTextZone($event)"
                                                        [attr.disabled]="bookingForm.controls.earlyUsFindings.value === null || bookingForm.controls.earlyUsFindings.value === 'N' ? true : null"
                                                        formControlName="earlyUsRemarks" style="
    margin-top: 7px;
"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 border rounded m-1" style="
                                    padding-top: 9px;
                                ">
                                        <div class="row pb-2">
                                            <div class="col-sm-4">
                                                <div class="checkbox pt-3">
                                                    <label>
                                                        <input type="checkbox"
                                                            title="Gynecology and Obstetrics Risk Factors at Booking"
                                                            (change)="clearInputFelid($event, bookingForm.controls.riskFactorsYn, bookingForm.controls.riskFactorsRemarks)"
                                                            formControlName="riskFactorsYn"> Risk Factors
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-sm-8">
                                                <span>Risk Factors Remarks </span>
                                                <div>
                                                    <textarea class="form-control " rows="1"
                                                        (keyup)="autoGrowTextZone($event)"
                                                        (keydown)="autoGrowTextZone($event)"
                                                        [attr.disabled]="bookingForm.controls.riskFactorsYn.value === false || bookingForm.controls.earlyUsFindings.value === 'Y' ? true : null"
                                                        formControlName="riskFactorsRemarks" style="
                                                        margin-top: 7px;
                                                    "></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label title="Type of Pregnancy">TP Pregnancy</label>
                                            <ng-select appendTo="body" [items]="typeOfPregnancy" [virtualScroll]="true"
                                                formControlName="pregnancyType" placeholder="Select" bindLabel="name"
                                                bindValue="code">
                                                <ng-option *ngFor="let c of typeOfPregnancy" [value]="c.code">{{
                                                    c.name
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label title="Birth Interval">Birth Interval</label>
                                            <input type="number" class="form-control form-control-sm"
                                                formControlName="birthInterval">
                                        </div>
                                    </div>

                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label title="Planned Pregnancy">Planned Pregnancy</label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input type="radio" id="plannedPregnancyYes"
                                                        class="form-check-input" formControlName="plannedPregnancy"
                                                        value="Y" />
                                                    <label for="plannedPregnancyYes"
                                                        class="form-check-label">Yes</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input type="radio" id="plannedPregnancyNo" class="form-check-input"
                                                        formControlName="plannedPregnancy" value="N" />
                                                    <label for="plannedPregnancyNo" class="form-check-label">No</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>




                                    <div class="col-sm-6">
                                        <div class="checkbox pt-3">
                                            <label>
                                                <input type="checkbox"
                                                    title="Conceived While on Modern Contrceptive Method"
                                                    formControlName="contraMethodUsedYn"> Conceived While on
                                                Modern Contrceptive Method
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="checkbox pt-3">
                                            <label>
                                                <input type="checkbox" formControlName="congAnamoliesYn">
                                                Congenital
                                                Anomalies
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-11 border rounded" style="
                                            padding-top: 9px;
                                        ">
                                        <div class="form-checkbox pt-3">
                                            <div class="head-txt pb-4 " title="Birth Spacing Method">Birth
                                                Spacing Method: </div>
                                            <span class="form-checkbox "
                                                *ngFor="let bsp of ancBirthSpacing; let i = index">
                                                <input type="checkbox" id="checkbox{{bsp.id}}" [checked]="bsp.checked"
                                                    (click)="addRemoveBspItem(bsp,$event,'ancBirthSpacing')" />
                                                <label class="pl-3 pr-3" for="{{i}}"> {{bsp.parmDesc}}</label>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="col-sm-12">
                                        <div class="row pt-3">
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="HEIGHT">HEIGHT</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="height">
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="WEIGHT">WEIGHT</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="weight">
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="Body Mass Index">BMI</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="bmi">
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="VTE Risk Assessment Score at Booking">VTE Risk
                                                        Score</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="vteRiskScore">
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="Iron Deficiency Anemia">Anaemic</label>
                                                    <div>
                                                        <div class="form-check form-check-inline">
                                                            <input type="radio" id="anaemicYes" class="form-check-input"
                                                                formControlName="anaemic" value="Y" />
                                                            <label for="anaemicYes" class="form-check-label">Yes</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input type="radio" id="anaemicNo" class="form-check-input"
                                                                formControlName="anaemic" value="N" />
                                                            <label for="anaemicNo" class="form-check-label">No</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label title="Risk Grading at Booking">Risk Grading <span
                                                            class="mdtr">*</span></label>
                                                    <ng-select appendTo="body" [items]="riskGrading"
                                                        [virtualScroll]="true" formControlName="riskGrading"
                                                        placeholder="Select" bindLabel="name" bindValue="code">
                                                        <ng-option *ngFor="let c of riskGrading" [value]="c.code">{{
                                                            c.name
                                                            }}</ng-option>
                                                    </ng-select>
                                                </div>
                                                <span class="tooltiptext"
                                                    *ngIf="bookingForm.get('riskGrading').hasError('required') && bookingForm.get('riskGrading').touched">
                                                    Risk Grading is required.
                                                </span>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group">
                                                    <label title="Last Pregnancy Status">Last Pregnancy Status</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        formControlName="statusInLastPregnancy">
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group">
                                                    <label>Last Delivery Date</label>
                                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                        formControlName="lastDeliveryDate"
                                                        [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                        [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                                                        showButtonBar="true"></p-calendar>
                                                </div>
                                            </div>
                                            <div class="col-sm-5 border rounded m-1" style="
                                            padding-top: 9px;
                                        ">
                                                <div class="row pb-2">
                                                    <div class="col-sm-5">
                                                        <div class="checkbox label-space">
                                                            <label>
                                                                <input type="checkbox" title="Abortion"
                                                                    (change)="clearInputFelid($event, bookingForm.controls.abortionYn, bookingForm.controls.lastAbortionDate)"
                                                                    formControlName="abortionYn"> Abortion
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-7">
                                                        <div class="form-group">
                                                            <label>Last Abortion Date</label>
                                                            <p-calendar appendTo="body" dateFormat="dd-mm-yy"
                                                                showIcon=true formControlName="lastAbortionDate"
                                                                [ngModelOptions]="{standalone: true}"
                                                                monthNavigator="true" [maxDate]=today
                                                                yearRange="1930:2030" yearNavigator="true"
                                                                [disabled]="bookingForm.controls.abortionYn.value === false || bookingForm.controls.abortionYn.value === 'N' ? true : null"
                                                                showButtonBar="true"></p-calendar>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="col">
                                <!-- Lab -->
                                <div class="mcard">
                                    <div class="mcard-header">Lab</div>
                                    <div *ngIf="ancLabInvestB && ancLabInvestB.length" class="mcard-body">
                                        <table #bLabtable class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th width="5%"></th>
                                                    <th>Test Name</th>
                                                    <th>Test Result</th>
                                                    <th>Remark</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    *ngFor="let item of ancLabInvestB | slice: (page1-1) * pageSize : (page1-1) * pageSize + pageSize">
                                                    <td>
                                                        <input type="checkbox" [value]="item.id" id="check"
                                                            (change)="onChicked(item,$event)"
                                                            [checked]="item.checked" />
                                                    </td>
                                                    <td>{{item.parmDesc}}</td>
                                                    <td>
                                                        <input class="form-control form-control-sm" type="text"
                                                            id='testResult'
                                                            [value]="item.testResult != undefined ? item.testResult : null"
                                                            (input)="onInput(item,$event)"
                                                            [attr.disabled]="!item.checked ? true : null" />
                                                    </td>
                                                    <td>
                                                        <input class="form-control form-control-sm" type="text"
                                                            id="remarks"
                                                            [value]="item.remarks != undefined ? item.remarks : null"
                                                            (input)="onInput(item,$event)"
                                                            [attr.disabled]="!item.checked ? true : null" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <ngb-pagination class="d-flex justify-content-center" [(page)]="page1"
                                            [pageSize]="pageSize" [collectionSize]="ancLabInvestB.length">
                                        </ngb-pagination>
                                    </div>
                                </div>
                                <!-- Medical History -->
                                <div class="mcard">
                                    <div class="mcard-header">Medical History</div>
                                    <div class="mcard-body">
                                        <table #bMidicaltable class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th width="5%"></th>
                                                    <th>Disease Name</th>
                                                    <th>Remark</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    *ngFor="let item of ancMidicalHistoB | slice: (page3-1) * pageSize : (page3-1) * pageSize + pageSize">
                                                    <td>
                                                        <input type="checkbox" [value]="item.id" id="bcheck"
                                                            [checked]="item.checked"
                                                            (change)="onChicked(item,$event)" />
                                                    </td>
                                                    <td>{{item.parmDesc}}</td>
                                                    <td>
                                                        <input class="form-control form-control-sm" type="text"
                                                            id="remarks"
                                                            [value]="item.remarks != undefined ? item.remarks : null"
                                                            (input)="onInput(item,$event)"
                                                            [attr.disabled]="!item.checked ? true : null" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <ngb-pagination class="d-flex justify-content-center" [(page)]="page3"
                                            [pageSize]="pageSize" [collectionSize]="ancMidicalHistoB.length">
                                        </ngb-pagination>
                                    </div>
                                </div>
                            </div>

                            <!-- Immunization -->
                            <div class="mcard m-3">
                                <div class="mcard-header clearfix">
                                    <h6 class="float-left pt-1"> Immunization</h6>
                                    <div class="float-right">
                                        <button class="btn btn-sm btn-primary" (click)="AddNewImmun()">Add New</button>
                                    </div>
                                </div>
                                <div class="mcard-body">
                                    <table #bMidicaltable class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th width="20%">Vaccination Date</th>
                                                <th width="20%">Vaccination Name</th>
                                                <th width="20%">Institute</th>
                                                <th width="20%">Remarks</th>
                                                <th></th>
                                                <th></th>
                                           
                                            </tr>
                                        </thead>
                                        <tbody >
                                            <tr *ngFor="let item of ancImmList; let i = index">
                                                <td>
                                                    <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                        [(ngModel)]="item.vaccinationDate"
                                                        [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                        [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                                                        showButtonBar="true"></p-calendar>
                                                </td>
                                                <td>
                                                    <ng-select appendTo="body" [items]="vaccinMastList"
                                                        [(ngModel)]="item.vaccineode"
                                                        [ngModelOptions]="{standalone: true}" [virtualScroll]="true"
                                                        placeholder="Select" bindLabel="vaccineName"
                                                        bindValue="vaccineId" class="section">

                                                        <ng-option *ngFor="let c of vaccinMastList"
                                                            [value]="c.vaccineId">{{
                                                            c.vaccineName
                                                            }}</ng-option>
                                                    </ng-select>
                                                </td>
                                                <td>
                                                    <ng-select appendTo="body" [items]="ancInstitutesFilter"
                                                        [(ngModel)]="item.vaccinatedInst"
                                                        [ngModelOptions]="{standalone: true}" [virtualScroll]="true"
                                                        placeholder="Select" bindLabel="estName" bindValue="estCode"class="section">

                                                        <ng-option *ngFor="let c of vaccinMastList"
                                                            [value]="c.estCode">{{
                                                            c.estName
                                                            }}</ng-option>
                                                    </ng-select>
                                                </td>
                                                <td>
                                                    <textarea class="form1 " rows="1"  [(ngModel)]="item.remarks" [ngModelOptions]="{standalone: true}" (keyup)="autoGrowTextZone($event)"
                                                        (keydown)="autoGrowTextZone($event)"></textarea>

                                                </td>
                                                <td >
                                                    <button class="fas fa-save action-btn"
                                                        (click)="addEditImmun(item, i)" ></button>
                                                </td>
                                                <td style="
                                                padding-right: 158px;
                                            ">
                                                    <button class="fas fa-trash action-btn" *ngIf="!item.runId"
                                                        (click)="removeImmun(i)"  ></button>
                                                </td>

                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </ng-template>
        </ngb-panel>
    </ngb-accordion>


    <div class="tabs">
        <tabset>
            <tab id="followup" heading="Follow Up">
                <!-- Followup -->
                <div class="content-wrapper">
                    <form [formGroup]="followUpForm">
                        <div class="row">
                            <div class="col-sm-2 pr-0">
                                <p-calendar class="addNewCale" appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
                                    (onSelect)="addNewFollowUp($event); getFollowUpVisit(ancVisitsList[ancVisitsList.length-1], ancVisitsList.length-1)"></p-calendar>

                                <ul class="list-group followup-nav">
                                    <li class="list-group-item added-list"
                                        *ngFor="let item of ancVisitsList; let i = index"
                                        (click)="addEditFollowUp(this.followUpForm.value); getFollowUpVisit(item, i)"
                                        [ngClass]="{'active': selectedFUDate == item.visitDate}">{{
                                        item.visitDate | date: 'dd-MM-yyyy'}} <button *ngIf="item.visitId == null"
                                            class="fas fa-trash" (click)="removeFollowUp(i)"></button></li>
                                </ul>
                            </div>
                            <div *ngIf="ancVisitsList && ancVisitsList.length && selectedFUDate" class="col-sm-10">
                                <div class="border-box">
                                    <h6 class="sub-title">
                                        {{ followUpForm.get('visitDate').value | date: 'dd-MM-yyyy'}}
                                    </h6>
                                    <div class="row">
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <div class="col-sm-5 border rounded ml-3 " style="
                                                padding-top: 9px;
                                            ">
                                                    <div class="row pb-2">
                                                        <div class="col-sm-5">
                                                            <div class="form-group">
                                                                <label title="Scan Findings">Scan Findings <span
                                                                        class="mdtr">*</span></label>
                                                                <ng-select appendTo="body" [items]="findings"
                                                                    (change)="clearInputFelid($event, followUpForm.controls.scanFindings, followUpForm.controls.scanRemarks)"
                                                                    [virtualScroll]="true"
                                                                    formControlName="scanFindings" placeholder="Select"
                                                                    bindLabel="name" bindValue="code">

                                                                    <ng-option *ngFor="let c of findings"
                                                                        [value]="c.code">{{ c.name
                                                                        }}</ng-option>
                                                                </ng-select>
                                                            </div>
                                                            <span class="tooltiptext"
                                                                *ngIf="followUpForm.get('scanFindings').hasError('required') && followUpForm.get('scanFindings').touched">
                                                                Scan Findings is required.
                                                            </span>
                                                        </div>
                                                        <div class="col-sm-7">
                                                            <div class="col-sm-9">
                                                                <span>Scan Remarks </span>
                                                            </div>
                                                            <div class="col-sm-3">
                                                                <textarea class="form-control " rows="1"
                                                                    (keyup)="autoGrowTextZone($event)"
                                                                    (keydown)="autoGrowTextZone($event)"
                                                                    [attr.disabled]="followUpForm.controls.scanFindings.value === null || followUpForm.controls.scanFindings.value === 'N' ? true : null"
                                                                    formControlName="scanRemarks"
                                                                    style="
                                                                       margin-top: 7px; margin-bottom: -31px; padding-right: 136px;"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <label title="Risk Grading at FollowUp">Risk Grading <span
                                                                class="mdtr">*</span></label>
                                                        <ng-select appendTo="body" [items]="riskGrading"
                                                            [virtualScroll]="true" formControlName="riskGrade"
                                                            placeholder="Select" bindLabel="name" bindValue="code">
                                                            <ng-option *ngFor="let c of riskGrading" [value]="c.code">{{
                                                                c.name
                                                                }}</ng-option>
                                                        </ng-select>
                                                    </div>
                                                    <span class="tooltiptext"
                                                        *ngIf="followUpForm.get('riskGrade').hasError('required') && followUpForm.get('riskGrade').touched">
                                                        Risk Grading is required.
                                                    </span>
                                                </div>
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <label title="Head Circumference (mm)">Head Circum.</label>
                                                        <div class="input-group">
                                                            <input type="number" class="form-control form-control-sm"
                                                                formControlName="headCircum">
                                                            <span class="input-group-text p-0 px-1">mm</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3" style="
                                                margin-top: 10px;">
                                                    <div class="form-group">
                                                        <label title="Abdominal Circumference (mm)">Abdominal Circum.
                                                        </label>
                                                        <div class="input-group">
                                                            <input type="number" class="form-control form-control-sm"
                                                                formControlName="abdomenCircum">
                                                            <span class="input-group-text p-0 px-1">mm</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3" style="
                                                margin-top: 10px;">
                                                    <div class="form-group">
                                                        <label title="Estimated Fetal Weight (g)">Est. Fetal Weight
                                                        </label>
                                                        <div class="input-group">
                                                            <input type="number" class="form-control form-control-sm"
                                                                formControlName="estFetalWeight">
                                                            <span class="input-group-text p-0 px-1">g</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-6" style="
                                                margin-top: 10px;">
                                                    <span>Doctor Comments </span>
                                                    <div class="col-sm-11">
                                                        <textarea class="form-control " rows="1"
                                                            (keyup)="autoGrowTextZone($event)"
                                                            (keydown)="autoGrowTextZone($event)"
                                                            formControlName="docComments" style=" margin-top: 7px;
                                                            margin-left: -14px;"></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <label title="Plan for Management">Plan for Management</label>
                                                        <input type="string" class="form-control form-control-sm"
                                                            formControlName="planManagement">
                                                    </div>
                                                </div>
                                                <br>
                                                <div class="col-sm-8 border rounded m-1 ml-3" style="
                                                padding-top: 9px;
                                            ">
                                                    <div class="row pb-2">

                                                        <div class="col-sm-12">
                                                            <span>Internal Referral</span>
                                                        </div>
                                                        <div class="col-sm-3">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox"
                                                                        title="Internal/External Referral to Nutrition"
                                                                        formControlName="refToNutrition">
                                                                    Nutrition
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-3">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox"
                                                                        title="Internal/External Referral to Health Educator"
                                                                        formControlName="refToHealthEdu">
                                                                    Health
                                                                    Educator
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-3">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox"
                                                                        title="Internal/External Referral to Obstetrician"
                                                                        formControlName="refToObstetrician">
                                                                    Obstetrician
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-3">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox"
                                                                        title="Internal/External Referral to Psychiatrist"
                                                                        formControlName="refToPsychatrist">
                                                                    Psychiatrist
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-6 border rounded m-1 ml-3" style="
                                                padding-top: 9px;
                                            ">
                                                    <div class="row pb-2">
                                                        <div class="col-sm-5">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox"
                                                                        (change)="clearInputFelid($event, followUpForm.controls.refToOtherDept, followUpForm.controls.otherDept)"
                                                                        title="Internal/External Referral to Other Department"
                                                                        formControlName="refToOtherDept"> Int./Ext. Ref.
                                                                    to
                                                                    Other Dept.
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-7">
                                                            <span>Other Department </span>
                                                            <div>
                                                                <textarea class="form-control " rows="1"
                                                                    (keyup)="autoGrowTextZone($event)"
                                                                    (keydown)="autoGrowTextZone($event)"
                                                                    [attr.disabled]="followUpForm.controls.refToOtherDept.value === false || followUpForm.controls.refToOtherDept.value === 'Y' ? true : null"
                                                                    formControlName="otherDept" style="
                                                                    margin-top: 5px;"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <span>Reason for Referral </span>
                                                    <div>
                                                        <textarea class="form-control " rows="1"
                                                            (keyup)="autoGrowTextZone($event)"
                                                            (keydown)="autoGrowTextZone($event)"
                                                            formControlName="refReason" style="
                                                            margin-top: 10px;
                                                            "></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <label>Appointment Date</label>
                                                        <p-calendar appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                                            formControlName="appDate"
                                                            [ngModelOptions]="{standalone: true}" monthNavigator="true"
                                                            [maxDate]=today yearRange="1930:2030" yearNavigator="true"
                                                            showButtonBar="true"></p-calendar>

                                                    </div>
                                                </div>
                                                <div class="col-sm-6 border rounded m-1" style="
                                                padding-top: 9px;
                                            ">
                                                    <div class="row pb-2">
                                                        <div class="col-sm">
                                                            <div class="checkbox pt-3">
                                                                <label>
                                                                    <input type="checkbox" title="Abortion Yes/No?"
                                                                        (change)="clearInputFelid1($event, followUpForm.controls.abortionYn, followUpForm.controls.typeTest)"
                                                                        formControlName="abortionYn"> Abortion Yes/No?
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="form-group">
                                                                <label>Abortion Type

                                                                </label>
                                                                <ng-select appendTo="body" [items]="abortionType"
                                                                    [virtualScroll]="true"
                                                                    formControlName="abortionType" placeholder="Select"
                                                                    bindLabel="description" bindValue="id"
                                                                    [disabled]="followUpForm.controls.abortionYn.value == 'true' ">

                                                                    <ng-option *ngFor="let c of abortionType"
                                                                        [value]="c.id">{{ c.description}}
                                                                    </ng-option>
                                                                </ng-select>

                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <!-- lab -->
                                            <div class="mcard">
                                                <div class="mcard-header">Lab</div>
                                                <div class="mcard-body">
                                                    <table #fLabtable class="table table-sm table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th width="5%"></th>
                                                                <th>Test Name</th>
                                                                <th>Test Result</th>
                                                                <th>Remark</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr
                                                                *ngFor="let element of ancLabInvestF | slice: (page2-1) * pageSize : (page2-1) * pageSize + pageSize">
                                                                <td>
                                                                    <input type="checkbox" [value]="element.id"
                                                                        id="check1" (change)="onChicked(element,$event)"
                                                                        [checked]="element.checked" />
                                                                </td>
                                                                <td>{{element.parmDesc}}</td>
                                                                <td>
                                                                    <input class="form-control form-control-sm"
                                                                        type="text" id="testResult"
                                                                        [value]="element.testResult != undefined ? element.testResult : null"
                                                                        (input)="onInput(element,$event)"
                                                                        [attr.disabled]="!element.checked ? true : null" />
                                                                </td>
                                                                <td>
                                                                    <input class="form-control form-control-sm"
                                                                        type="text" id="remarks"
                                                                        [value]="element.remarks != undefined ? element.remarks : null"
                                                                        (input)="onInput(element,$event)"
                                                                        [attr.disabled]="!element.checked ? true : null" />
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <ngb-pagination class="d-flex justify-content-center"
                                                        [(page)]="page2" [pageSize]="pageSize"
                                                        [collectionSize]="ancLabInvestF.length">
                                                    </ngb-pagination>
                                                </div>
                                            </div>
                                            <!-- Medical History -->
                                            <div class="mcard">
                                                <div class="mcard-header">Medical History</div>
                                                <div class="mcard-body">
                                                    <table #fMidicaltable class="table table-sm table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th width="5%"></th>
                                                                <th>Disease Name</th>
                                                                <th>Remark</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr
                                                                *ngFor="let item of ancMidicalHistoF | slice: (page3-1) * pageSize : (page3-1) * pageSize + pageSize">
                                                                <td>
                                                                    <input type="checkbox" [value]="item.id" id="bcheck"
                                                                        [checked]="item.checked"
                                                                        (change)="onChicked(item,$event)" />
                                                                </td>
                                                                <td>{{item.parmDesc}}</td>
                                                                <td>
                                                                    <input class="form-control form-control-sm"
                                                                        type="text" id="remarks"
                                                                        [value]="item.remarks != undefined ? item.remarks : null"
                                                                        (input)="onInput(item,$event)"
                                                                        [attr.disabled]="!item.checked ? true : null" />
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <ngb-pagination class="d-flex justify-content-center"
                                                        [(page)]="page3" [pageSize]="pageSize"
                                                        [collectionSize]="ancMidicalHistoF.length">
                                                    </ngb-pagination>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </tab>
            <tab heading="Delivery Details" id="deliveryDetails">
                <!-- Delivery Details -->

                <div class="content-wrapper">
                    <form [formGroup]="deliveryDetailForm">
                        <div class="row">
                            <div class="col-sm-2 border rounded m-1" style="
                            padding-top: 9px; padding-right: 7px; padding-left: -43px;
                        ">
                                <div class="row pb-2">
                                    <div class="col-sm-6" style="
                                     padding-left: -22px;
                                ">
                                        <div class="checkbox pt-3">
                                            <label>
                                                <input type="checkbox" title="Abortion Yes/No?"
                                                    (change)="clearInputFelid2($event, deliveryDetailForm.controls.abortionYn, deliveryDetailForm.controls.typeOfBortion)"
                                                    formControlName="abortionYn"> Abortion? <span class="mdtr">*</span>
                                            </label>
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="deliveryDetailForm.get('abortionYn').hasError('required') && deliveryDetailForm.get('abortionYn').touched">
                                            Abortion is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-6" style="padding-left: 0px;">
                                        <div class="form-group">
                                            <label>Abortion Type</label>
                                            <ng-select appendTo="body" [items]="abortionType" [virtualScroll]="true"
                                                formControlName="typeOfBortion" placeholder="Select"
                                                bindLabel="description" bindValue="typeOfBortion"
                                                [disabled]="deliveryDetailForm.controls.abortionYn.value == 'true' ">
                                                <ng-option *ngFor="let c of abortionType" [value]="c.typeOfBortion">{{
                                                    c.description
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label title="Gestational age at time of Abortion (weeks)">Gest. Age </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-sm"
                                            formControlName="gestAge">
                                        <span class="input-group-text p-0 px-1">weeks</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Delivery Date <span class="mdtr">*</span></label>
                                    <p-calendar dateFormat="dd-mm-yy" appendTo="body" showIcon=true
                                        formControlName="deliveryDate"></p-calendar>
                                </div>
                                <span class="tooltiptext"
                                    *ngIf="deliveryDetailForm.get('deliveryDate').hasError('required') && deliveryDetailForm.get('deliveryDate').touched">
                                    Delivery Date is required.
                                </span>
                            </div>
                            <div class="col-sm-3 border rounded m-1" style="
                            padding-top: 9px;
                        ">
                                <div class="row pb-2">
                                    <div class="col-sm-5">
                                        <div class="form-group">
                                            <label title="Place of Delivery">Place of Delivery <span
                                                    class="mdtr">*</span></label>
                                            <ng-select appendTo="body" [items]="places" [virtualScroll]="true"
                                                (change)="clearInputFelid($event, deliveryDetailForm.controls.deliveryPlaceType, deliveryDetailForm.controls.placeOfdelivery)"
                                                formControlName="deliveryPlaceType" placeholder="Select"
                                                bindLabel="name" bindValue="code" [disabled]="true">

                                                <ng-option *ngFor="let c of places" [value]="c.code">{{ c.name
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="deliveryDetailForm.get('deliveryPlaceType').hasError('required') && deliveryDetailForm.get('deliveryPlaceType').touched">
                                            Place of Delivery is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-7">
                                        <span>Name Of Delivery Place </span>
                                        <div>
                                            <textarea class="form-control " rows="1" (keyup)="autoGrowTextZone($event)"
                                                (keydown)="autoGrowTextZone($event)"
                                                [attr.disabled]="deliveryDetailForm.controls.deliveryPlaceType.value === null || deliveryDetailForm.controls.deliveryPlaceType.value === 'B' || deliveryDetailForm.controls.deliveryPlaceType.value === 'H'? true : null"
                                                formControlName="placeOfdelivery" style="
                                                margin-top: 7px;
                                            "></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label title="Booking Status">Booking Status</label>
                                    <ng-select appendTo="body" [items]="bStatus" [virtualScroll]="true"
                                        formControlName="bookingStatus" [disabled]="true" placeholder="Select"
                                        bindLabel="name" bindValue="code" [disabled]="true">

                                        <ng-option *ngFor="let c of bStatus" [value]="c.code">{{ c.name
                                            }}</ng-option>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>No. Of ANC Visits <span class="mdtr">*</span></label>
                                    <input type="number" class="form-control form-control-sm"
                                        formControlName="totalAncVisits">
                                </div>
                                <span class="tooltiptext"
                                    *ngIf="deliveryDetailForm.get('totalAncVisits').hasError('required') && deliveryDetailForm.get('totalAncVisits').touched">
                                    No. Of ANC Visits is required.
                                </span>
                            </div>
                            <div class="col-sm-3 border rounded m-1" style="
                            padding-top: 9px;
                        ">
                                <div class="row pb-2">
                                    <div class="col-sm-5">
                                        <div class="form-group">
                                            <label title="Risk Grading at Delivery">Risk Grading <span
                                                    class="mdtr">*</span></label>
                                            <ng-select appendTo="body" [items]="riskGrading" [virtualScroll]="true"
                                                formControlName="riskGrade" placeholder="Select" bindLabel="name"
                                                bindValue="code">

                                                <ng-option *ngFor="let c of riskGrading" [value]="c.code">{{
                                                    c.name
                                                    }}</ng-option>
                                            </ng-select>
                                        </div>
                                        <span class="tooltiptext"
                                            *ngIf="deliveryDetailForm.get('riskGrade').hasError('required') && deliveryDetailForm.get('riskGrade').touched">
                                            Risk Grading is required.
                                        </span>
                                    </div>
                                    <div class="col-sm-7">
                                        <span>Remarks </span>
                                        <div>
                                            <textarea class="form-control " rows="1" (keyup)="autoGrowTextZone($event)"
                                                (keydown)="autoGrowTextZone($event)" formControlName="riskGradeRemarks"
                                                style="
                                                margin-top: 7px;
                                            "></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Mode of Delivery <span class="mdtr">*</span></label>
                                    <ng-select appendTo="body" [items]="deliveryMode" [virtualScroll]="true"
                                        formControlName="deliveryMode" [disabled]="true" placeholder="Select"
                                        bindLabel="paramDesc" bindValue="id" [disabled]="true">
                                        <ng-option *ngFor="let c of deliveryMode" [value]="c.id">{{ c.paramDesc
                                            }}</ng-option>
                                    </ng-select>
                                </div>
                                <span class="tooltiptext"
                                    *ngIf="deliveryDetailForm.get('deliveryMode').hasError('required') && deliveryDetailForm.get('deliveryMode').touched">
                                    Mode of Delivery is required.
                                </span>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Perineal Tears</label>
                                    <ng-select appendTo="body" [items]="perinealTears" [virtualScroll]="true"
                                        formControlName="perineum" [disabled]="true" placeholder="Select"
                                        bindLabel="paramDesc" bindValue="id" [disabled]="true">
                                        <ng-option *ngFor="let c of perinealTears" [value]="c.id">{{ c.paramDesc
                                            }}</ng-option>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Delivery Type</label>
                                    <ng-select appendTo="body" [items]="deliveryType" [virtualScroll]="true"
                                        formControlName="deliveryType" [disabled]="true" placeholder="Select"
                                        bindLabel="paramDesc" bindValue="id" [disabled]="true">
                                        <ng-option *ngFor="let c of deliveryType" [value]="c.id">{{ c.paramDesc
                                            }}</ng-option>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Pregnancy Outcome</label>
                                    <ng-select appendTo="body" [items]="pregnancyOutcome" [virtualScroll]="true"
                                        formControlName="pregOutcome" [disabled]="true" placeholder="Select"
                                        bindLabel="paramDesc" bindValue="id" [disabled]="true">

                                        <ng-option *ngFor="let c of pregnancyOutcome" [value]="c.id">{{ c.paramDesc
                                            }}</ng-option>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label title="Medical and Obstetrics Condition at time of Delivery">Condition at
                                        Delivery </label>
                                    <div class="input-group">
                                        <input type="string" class="form-control form-control-sm"
                                            formControlName="condAtDelivery">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label title="VTE Assessment Score after Delivery">VTE Score </label>
                                    <div class="input-group">
                                        <input type="string" class="form-control form-control-sm"
                                            formControlName="vteScore">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </tab>
            <tab heading="Baby Details" id="babyDetails">
                <!-- Baby Details -->
                <div class="content-wrapper">
                    <form [formGroup]="babyDetailForm">
                        <div class="row">
                            <div class="col-sm-3 pr-0">
                                <button class="btn add-btn" (click)="showInputFeild()">+ Add New</button>
                                <ul class="list-group followup-nav">
                                    <li class="list-group-item added-list"
                                        *ngFor="let item of babydetalisList; let i = index"
                                        (click)="addEditBaby(this.babyDetailForm.value); getBabyDetailForm(item, i)"
                                        [ngClass]="{'active': selectedBaby.babyFileNo == item.babyFileNo}">
                                        {{item.babyFileNo !=
                                        null
                                        ?item.babyFileNo : 'Enter File No.'}} <button *ngIf="item.babyId == null"
                                            class="fas fa-trash" (click)="removeBaby(i)"></button></li>
                                </ul>
                            </div>
                            <div *ngIf="selectedBaby && selectedBaby.babyFileNo" class="col-sm-9">
                                <div class="border-box">
                                    <div class="row" style="
                                    background: lightgray;
                                    margin-top: -11px;
                                    border-radius: 3px;
                                    margin-left: -10px;
                                    margin-right: -10px; ">
                                        <div class="col-sm-2" style="
                                        padding-top: 4px;
                                    ">
                                            <label>Baby File No.</label>
                                        </div>
                                        <div class="col-sm-4">
                                            <input type="text" class="form-control form-control-sm"
                                                formControlName="babyFileNo">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4" style="
                                        padding-top: 30px;
                                    ">
                                            <div class="form-group">
                                                <label>Body Weight</label>
                                                <input type="text" class="form-control form-control-sm"
                                                    formControlName="babyWeight">
                                            </div>
                                        </div>
                                        <div class="col-sm-4 border rounded m-3 ml-3" style="
                                        padding-top: 7px;
                                    
                                    ">
                                            <div class="row pb-2">
                                                <div class="col-sm-6" style="
                                                padding-left: 12px;
                                            ">
                                                    <div class="checkbox pt-3">
                                                        <label>
                                                            <input type="checkbox" formControlName="conAnamolyYn"
                                                                (change)="clearInputFelid($event, babyDetailForm.controls.conAnamolyYn, babyDetailForm.controls.conAnamolyRemarks)">
                                                            Congenital Anomaly
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <span>Congenital Remarks </span>
                                                    <div>
                                                        <textarea class="form-control " rows="1"
                                                            (keyup)="autoGrowTextZone($event)"
                                                            (keydown)="autoGrowTextZone($event)"
                                                            [attr.disabled]="babyDetailForm.controls.conAnamolyYn.value === false || babyDetailForm.controls.conAnamolyYn.value === 'N' ? true : null"
                                                            formControlName="conAnamolyRemarks" style="
                                                            margin-top: 6px;
                                                        
                                                        "></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label title="Baby Screening for Hypothyroidism">Hypothyroidism</label>

                                                <ng-select appendTo="body" [items]="babyScHypoth" [virtualScroll]="true"
                                                    formControlName="hypothyroidismYn" [disabled]="true"
                                                    placeholder="Select" bindLabel="name" bindValue="code"
                                                    [disabled]="true">

                                                    <ng-option *ngFor="let c of babyScHypoth" [value]="c.code">{{ c.name
                                                        }}</ng-option>
                                                </ng-select>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label title="ECHO Hearing Test for Baby">Echo Test</label>

                                                <ng-select appendTo="body" [items]="echoTestLst" [virtualScroll]="true"
                                                    formControlName="echoTest" [disabled]="true" placeholder="Select"
                                                    bindLabel="name" bindValue="code" [disabled]="true">

                                                    <ng-option *ngFor="let c of echoTestLst" [value]="c.code">{{ c.name
                                                        }}</ng-option>
                                                </ng-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </tab>
            <tab heading="PNC Visit" id="pncVisit">
                <!-- PNC Visit Details -->
                <div class="content-wrapper">
                    <form [formGroup]="pncVisitForm">
                        <div class="row">
                            <div class="col-sm-2 pr-0">
                                <p-calendar class="addNewCale" appendTo="body" dateFormat="dd-mm-yy" showIcon=true
                                    [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                                    yearRange="1930:2030" yearNavigator="true" showButtonBar="true"
                                    (onSelect)="addNewPncVisit($event); getPncVisit(pncVisitsList[pncVisitsList.length-1], pncVisitsList.length-1)"></p-calendar>

                                <ul class="list-group followup-nav">
                                    <li class="list-group-item added-list"
                                        *ngFor="let item of pncVisitsList; let i = index"
                                        (click)="addEditPncVisit(this.pncVisitForm.value); getPncVisit(item, i)"
                                        [ngClass]="{'active': selectedPNCDate == item.visitDate}">{{
                                        item.visitDate | date: 'dd-MM-yyyy'}} <button *ngIf="item.pncId == null"
                                            class="fas fa-trash" (click)="removePnc(i)"></button></li>
                                </ul>
                            </div>
                            <div *ngIf="pncVisitsList && pncVisitsList.length && selectedPNCDate" class="col-sm-10">
                                <div class="border-box">
                                    <h6 class="sub-title">
                                        {{ pncVisitForm.get('visitDate').value | date: 'dd-MM-yyyy'}}
                                    </h6>
                                    <div class="row px-3">
                                        <div class="col-sm-8 divider-right">
                                            <div class="mb-2">


                                                <div class="row">
                                                    <div class="row">
                                                        <span title="Management & Advice" class="col-sm-3">Advice:
                                                        </span>
                                                        <div class="col-sm-7">
                                                            <input type="string" class="form-control form-control-sm"
                                                                formControlName="advice">
                                                        </div>
                                                    </div>



                                                    <div class="row">

                                                        <span class="col-sm-4">BP SYS: </span>
                                                        <div class="col-sm-7">
                                                            <input type="number" class="form-control form-control-sm"
                                                                formControlName="bpSys">
                                                        </div>
                                                    </div>



                                                    <div class="row">
                                                        <span class="col-sm-4">BP DIA: </span>
                                                        <div class="col-sm-7">
                                                            <input type="number" class="form-control form-control-sm"
                                                                formControlName="bpDia">
                                                        </div>

                                                    </div>

                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="row" style="
                                            padding-top: 25px;
                                        ">

                                                    <span class="col-sm-9" style="
                                                padding-left: 12px;
                                            ">Breast Feeding: </span>
                                                    <div class="col-sm-1">
                                                        <input type="checkbox" name="breastFeeding"
                                                            formControlName="breastFeeding">

                                                    </div>
                                                </div>

                                                <div class="row" style="
                                        padding-top: 9px;
                                        padding-left: 62px;
                                    ">
                                                    <div class="col-sm-10 border rounded m-1" style="
                                            padding-top: 9px;
                                        ">
                                                        <div class="row pb-2">
                                                            <div class="col-sm-5">
                                                                <div class="form-group">
                                                                    <label title="Breast Examination">Breast
                                                                        Exam:</label>

                                                                    <ng-select appendTo="body" [items]="findings"
                                                                        (change)="clearInputFelid($event, pncVisitForm.controls.breastExam, pncVisitForm.controls.breastExamRemarks)"
                                                                        [virtualScroll]="true"
                                                                        formControlName="breastExam"
                                                                        placeholder="Select" bindLabel="name"
                                                                        bindValue="code">

                                                                        <ng-option *ngFor="let c of findings"
                                                                            [value]="c.code">{{
                                                                            c.name
                                                                            }}</ng-option>
                                                                    </ng-select>
                                                                </div>
                                                            </div>
                                                            <div class="col-sm-7">
                                                                <span>Breast Exam Remarks: </span>
                                                                <div>
                                                                    <textarea class="form-control " rows="1"
                                                                        (keyup)="autoGrowTextZone($event)"
                                                                        (keydown)="autoGrowTextZone($event)"
                                                                        [attr.disabled]="pncVisitForm.controls.breastExam.value === null || pncVisitForm.controls.breastExam.value === 'N' ? true : null"
                                                                        formControlName="breastExamRemarks"></textarea>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <!-- lab -->
                                            <div class="mcard">
                                                <div class="mcard-header">Lab</div>
                                                <div class="mcard-body">
                                                    <table #pLabtable class="table table-sm table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th width="5%"></th>
                                                                <th>Test Name</th>
                                                                <th>Test Result</th>
                                                                <th>Remark</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr
                                                                *ngFor="let element of ancLabInvestP | slice: (page2-1) * pageSize : (page2-1) * pageSize + pageSize">
                                                                <td>
                                                                    <input type="checkbox" [value]="element.id"
                                                                        id="check3" (change)="onChicked(element,$event)"
                                                                        [checked]="element.checked" />
                                                                </td>
                                                                <td>{{element.parmDesc}}</td>
                                                                <td>
                                                                    <input class="form-control form-control-sm"
                                                                        type="text" id="testResult"
                                                                        [value]="element.testResult != undefined ? element.testResult : null"
                                                                        (input)="onInput(element,$event)"
                                                                        [attr.disabled]="!element.checked ? true : null" />
                                                                </td>
                                                                <td>
                                                                    <input class="form-control form-control-sm"
                                                                        type="text" id="remarks"
                                                                        [value]="element.remarks != undefined ? element.remarks : null"
                                                                        (input)="onInput(element,$event)"
                                                                        [attr.disabled]="!element.checked ? true : null" />
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <ngb-pagination class="d-flex justify-content-center"
                                                        [(page)]="page2" [pageSize]="pageSize"
                                                        [collectionSize]="ancLabInvestP.length">
                                                    </ngb-pagination>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </tab>
        </tabset>
    </div>




    <div class="btn-ftr-container">
        <button class="btn btn-sm btn-secondary" (click)="clearAll()">Clear</button>
        <button class="btn btn-sm btn-primary" (click)="submitForm()">Submit</button>
    </div>
</div>


<!-- //////////////////////////////////////////(((((Small Window ))))\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ -->
<ng-template #viewSmallANCWindow let-modal>
    <div class="modal-header">
        <h5 class="modal-title" id="modal-basic-title">ANC Register List</h5>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="modal-body">
            <ag-grid-angular style="width: 100%; height: 400px;" class="ag-theme-balham" [rowData]="ancRegList"
                singleClickEdit="true" [columnDefs]="columnAncRegList" [suppressRowClickSelection]="true"
                rowSelection="single" (gridReady)="onGridReady($event)" (rowClicked)="onRowClicked($event)">
            </ag-grid-angular>
        </div>

        <div>
            <button type="submit" (click)="newReg()" *ngIf="newRegId" class="btn btn-primary ripple"> New </button>
        </div>
    </div>
</ng-template>

<ng-template #popupModal let-modal>
    <div class="modal-header">
        <h6 class="modal-title">Baby File No.</h6>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">&times;</button>

    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-6 py-4">
                <div class="panel py-0 px-5">
                    <div>
                        <input type="text" placeholder="Enter Baby File No."
                            (keyup.enter)="addNewBaby($event); getBabyDetailForm(babydetalisList[babydetalisList.length-1], babydetalisList.length-1);clear">
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>