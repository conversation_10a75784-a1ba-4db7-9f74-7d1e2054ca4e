
import { MasterDataService } from '../_services/app.master.service';
import { AlShifaLoginService } from './alShifaLogin.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder } from '@angular/forms';
import * as AppUtils from '../common/app.utils';
import * as CommonConstants from '../_helpers/common.constants';
import { LoginService } from '../login/login.service';
//import { SharedService } from '../common/services/shared.service';
import { AuthenticationService } from '../_services/authentication.service';
import { SharedService } from '../_services/shared.service';
import Swal from 'sweetalert2';

@Component({
    selector: 'alShifaLogin',
    templateUrl: 'alShifaLogin.component.html',
    providers: [SharedService]
})

export class AlShifaLoginComponent {
    loginService: LoginService;
    error: string;
    estCode: number;
    persCode: number;
    isTrusted: string;
    typeCode: number;
    patientId: any;
    civilId: any;
    valCode: any;
    loading: boolean;
    regNo: any
    geneticType: any
    alShifanData: any;
    ngOnInit() {

    }

    constructor(public router: Router, loginService: LoginService, private route: ActivatedRoute, private alShifaLoginService: AlShifaLoginService,
        private sharedService: SharedService, private masterService: MasterDataService, private authenticationService: AuthenticationService,) {
        /**
         * fetch url param 
         * http://localhost:4200/eregistry/#/alShifaLogin?estCode=20068&persCode=-1001&type=-999&civilId=99&isTrusted=Y
         * http://eservices.healthnet.gov.om/eregistry/#/alShifaLogin?estCode=162&persCode=27183&type=24&patientId=457018&isTrusted=Y
         */

        //this.route.params.subscribe(params => {
        this.route.queryParams.subscribe(params => {
            this.loading = true;
            this.loginService = loginService;
            this.estCode = params['estCode'];                   //required
            this.persCode = params['persCode'];                 //required
            this.isTrusted = params['isTrusted'];               //required
            this.typeCode = params['type'];                     //required

            this.patientId = params['patientId'];               //optional
            this.civilId = params['civilId'];                   //optional
            this.regNo = params['regNo'];                       //optional
            this.geneticType = params['geneticType'];           //optional
            this.valCode = params['valCode']; 

            this.alShifanData = Array();

            if (this.patientId) {
            //    localStorage.setItem(AppUtils.ALSHIFA_TRUSTED_PATIENT_ID, this.patientId);
                this.alShifanData.push({ patientId: this.patientId });
            }
            if (this.typeCode) {
              //  localStorage.setItem(AppUtils.EREG_TYPE_CODE, JSON.stringify(+this.typeCode));
              this.alShifanData.push({ typeCode: this.typeCode });
            }
            if (this.civilId) {
              //  localStorage.setItem(AppUtils.ALSHIFA_TRUSTED_CIVIL_ID, this.civilId);
              this.alShifanData.push({ civilId: this.civilId });
            }
            if (this.regNo) {
                this.alShifanData.push({ regNo: this.regNo });
            }
            if (this.estCode) {
                this.alShifanData.push({ estCode: this.estCode });
            }
            if (this.valCode) {
                this.alShifanData.push({ valCode: this.valCode });
            }
            /* if (this.persCode < 0) {
                  localStorage.clear();
                  swal('This user is not authorized to access the application', '', 'error');
                  this.router.navigate(['/login']);
              } else {*/
            if (this.isTrusted === 'Y' || !this.isTrusted) {
                this.isTrusted = 'Y';
                this.trustedAuthenticate();
            }
            /*  }*/

            alShifaLoginService.setAlShifanData(this.alShifanData);
        });

    }

    /**
     * To validate the alshifa login, whether its from the trusted source.
     */
    trustedAuthenticate() {
        this.alShifaLoginService.trustedAuthenticate(this.estCode, this.persCode).subscribe((res) => {
            this.loading = false;
            if (res && res.access_token != null) {


                //localStorage.setItem(AppUtils.ALSHIFA_TRUSTED_INFO, "");


                
                
              //   localStorage.setItem(AppUtils.STORAGE_ACCOUNT_ACCESS_TOKEN, res.access_token);
                 localStorage.setItem(CommonConstants.TOKEN, res.access_token);
                 localStorage.setItem(AppUtils.STORAGE_ACCOUNT_REFRESH_TOKEN, res.refresh_token);   // need


                // localStorage.setItem(AppUtils.STORAGE_ACCOUNT_EXPIRES_IN, res.expires_in);
                // localStorage.setItem(AppUtils.LOGIN_ID, '' + this.persCode);
                // localStorage.setItem(AppUtils.ALSHIFA_TRUSTED_LOGIN, this.isTrusted);
                // localStorage.setItem(AppUtils.ALSHIFA_TRUSTED_LOGIN_INST, '' + this.estCode);
                this.sharedService.isTrusted = this.isTrusted;
                this.setUserDetials(res.userInfo);
            } else {
                this.loading = false;
                this.loginService.removeAccount();
                this.error = res.message;
                Swal.fire('Bad Credentials', res.message, 'error');
                this.router.navigate([AppUtils.REDIRECT_LOGOUT_URI]);

            }
        },
            err => {
                this.loading = false;
                Swal.fire('Bad Credentials', err.error_description, 'error');
                this.router.navigate([AppUtils.REDIRECT_LOGOUT_URI]);
            }

        );
    }

    /**
     * To set user information & route to the appropriate landing page.
     * @param res  response which contains the user information.
     */
    setUserDetials(res: any) {
        const userToken = AppUtils.STORAGE_ACCOUNT_ACCESS_TOKEN;
        const refreshToken = AppUtils.STORAGE_ACCOUNT_REFRESH_TOKEN;
        const expiresIn = AppUtils.STORAGE_ACCOUNT_EXPIRES_IN;
        const institutes = AppUtils.INSTITUTES;
        const loginId = AppUtils.STORAGE_ACCOUNT_REFRESH_TOKEN;
        const defaultInstitutes = AppUtils.DEFAULT_INSTITUTE;
        const personName = AppUtils.PERSON_NAME;
        // set user info to shared service
        this.sharedService.setUserData({
            userToken: res.access_token,
            refreshToken: res.refresh_token,
            expiresIn: res.expires_in,
            institutes: res.institutes,
            loginId: this.persCode,
            defaultInstitutes: res.institutes.filter(est => est.defaultYN && est.defaultYN.indexOf('Y') !== -1)[0], roles: res.roles
        });

        // localStorage.setItem(AppUtils.MENU, JSON.stringify(res.menues));
      //  localStorage.setItem(AppUtils.INSTITUTES, JSON.stringify(res.institutes.filter(s => s.estCode == this.estCode)[0]));
      //  localStorage.setItem(AppUtils.DEFAULT_INSTITUTE, '' + JSON.stringify(res.institutes));
       // localStorage.setItem(AppUtils.LOGIN_ID, res.loginId);
       // localStorage.setItem(AppUtils.LOGIN_ID, res.person.perscode);
    // localStorage.setItem(AppUtils.PERSON_NAME, res.person.personName);
    //    localStorage.setItem(AppUtils.USER_ROLES, JSON.stringify(res.roles));

        res.workStatus = res.institutes.filter(s => s.estCode == this.estCode);
        res.institutes = res.institutes.filter(s => s.estCode == this.estCode);
        localStorage.setItem(AppUtils.CURRENT_USER, JSON.stringify(res));    //// need 
        // res.roles.forEach(element => {
        //     localStorage.setItem(AppComponentUtils.LOGGED_IN_USER_TYPE, element['name']);
        // });

        // this.loginService.setMenus(res.menues);
        this.sharedService.setPrivileges();
        //  this.sharedService.getApplicationRequiredData();
      //  this.loginService.setLoggedInUsername(res.loginId.replace(/['"]+/g, ''));

          
        this.authenticationService.setAlshifaCurrentUserSubject(JSON.stringify(res));
        //this.authenticationService.getUserDetails().subscribe((res) => {
       // });

        setTimeout(() => {
            //route to pre configured landing page for the given type.
            if (this.typeCode) {
                //  this.router.navigate(['home']);
                this.masterService.getNotificationTypes().subscribe(re => {
                    if (re) {
                        const notificationData = re.filter(item => item.id == this.typeCode)[0];
                        if (notificationData && notificationData.pageUrl && this.patientId) {
                            this.router.navigate([notificationData.pageUrl]);

                        }else  if (notificationData && notificationData.pageUrl && this.civilId) {
                            this.router.navigate([notificationData.pageUrl]);

                        }  else {
                            if (notificationData && notificationData.pageUrl && (+this.typeCode == AppUtils.BLOOD_TYPE_CODE)) {
                                //if(notificationData && notificationData.pageUrl && (+this.typeCode==AppUtils.VACCINATION_TYPE_CODE || +this.typeCode ==AppUtils.BLOOD_TYPE_CODE)){
                                this.router.navigate([notificationData.pageUrl]);
                                // this.router.navigate([AppUtils.RENAL_DASHBOARD]);

                            }
                            else {
                                this.router.navigate([AppUtils.BACKEND_API_URL]);
                            }

                           // this.router.navigate([AppUtils.BACKEND_API_URL]);
                        }
                    }
                });

            } else {
                // if no route configured ? route to Home page
                this.router.navigate([AppUtils.BACKEND_API_URL]);
            }
        }, 1000);

        /*let menues = res.menues.sort(function (a: any, b: any) {
            return a.last_nom > b.last_nom;
        });
        res.menues = this.sort(res.menues);*/
    }


    /**
     * To sort the menu
     */
    sort(array: Array<any>): Array<string> {
        array.sort((a: any, b: any) => {
            if (a.menuName < b.menuName) {
                return -1;
            } else if (a.menuName > b.menuName) {
                return 1;
            } else {
                return 0;
            }
        });
        return array;
    }

}