import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import * as AppUtils from '../../common/app.utils';

@Injectable({
  providedIn: 'root'
})
export class TargetListingService {

  constructor(private http: HttpClient) { }

   //target listing
  getTargetListing(date): Observable<any> {
  
    return this.http.post(AppUtils.GET_VACCINE_TARGET_LIST, date);
   
  }

}





    
