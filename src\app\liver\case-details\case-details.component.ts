// Angular imports
import { Component, OnInit, ViewChild, Input } from "@angular/core";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { AbstractControl } from "@angular/forms";
import { LoaderService } from "src/app/_services/loader.service";
import {
  FormArray,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";

// Third-party imports
import { GridOptions } from "ag-grid-community";
import Swal from "sweetalert2";
import * as _ from "lodash";
import { NgbModal, ModalDismissReasons } from "@ng-bootstrap/ng-bootstrap";
import { IDropdownSettings } from "ng-multiselect-dropdown";

// Application imports
import { MasterService } from "src/app/_services/master.service";
import { SharedService } from "src/app/_services/shared.service";
import { LiverService } from "../liver.service";
import { CaseDetailService } from "./case-details.service";
import * as AppUtils from "../../common/app.utils";
import * as CommonConstants from "../../_helpers/common.constants";
import { VaccinationComponent } from "../../_comments/vaccination/vaccination.component";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";
import {
  CaseDetails,
  LiverTransplantIndicationDto,
  LiverComplicationDto,
  LiverManagementDto,
  LiverCurrMgmtSubTyperSaveDto,
  LiverCurrMgmtUIState,
  LiverIndication,
  LiverRegister,
  ResultDecorator,
  LiverCaseDetailsDto,
  LiverCurrMgmtMasterDto,
  LiverComplicationMasterDto,
  SubItem,
  LiverCirrhosisStage,
  LiverComplicationSave,
  LiverComplicationSubItem,
  RgVwLiverProceduresDto,
  RgProcedureDetailsDto,
  RgProcedureDetailsSaveDto,
  StageTransplantData,
} from "src/app/_models/liverTransplant.model";
import { Paginator } from "primeng/paginator";

@Component({
  selector: "app-case-details",
  templateUrl: "./case-details.component.html",
  styleUrls: ["./case-details.component.scss"],
  providers: [LiverService],
})
export class CaseDetailsComponent implements OnInit {
  @ViewChild("labTestPaginator", { static: false }) labTestPaginator: Paginator;
  totalLabRecords: number = 0;
  paginationSize: number = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  filteredLabnewList: any[] = [];

  showDownloadButton: boolean = false;
  showVaccineButton: boolean = false;
  @ViewChild("Vaccination", { static: false })
  Vaccination: VaccinationComponent;
  showCommentBox: boolean = false;
  showCommentBox_peld: boolean = false;
  contactDetailsForm: FormGroup;
  contactDetails: any[] = []; // Replace with your actual data type

  @Input() liverId: number;
  caseDetails: LiverCaseDetailsDto | null = null;

  hepatocellular: boolean = false;
  locoTherapy: boolean = false;
  selectedComplications: Map<number, boolean> = new Map();
  complicationDetails: Map<number, any> = new Map();

  liverCirrhosisComplications: LiverComplicationMasterDto[] = [];
  vaccNewList: any[];
  vaccName: any[];
  vaccinMastList: any;
  liverTransIndications: LiverIndication[] = [];
  familyHistoryYn: number = 0;
  familyHistoryRemarks: string = "";
  liverCirrhosisStages: LiverCirrhosisStage[] = [];
  meldExceptionYn: number = 0;
  peldExceptionYn: number = 0;
  meldExceptionRemarks: string = "";
  peldExceptionRemarks: string = "";
  page: number = 1;
  pageSize: number = 3;
  disVaccineInfoalshifa: any;
  currentManagement: LiverCurrMgmtUIState[] = [];
  openAccordion: boolean[] = [];
  hospitals: any;
  public rgTbFamilyHistory: any = [];
  public contactDetailsArray: any = [];
  relation: any;

  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;

  dialysis = null;
  stage: any;
  transplant = null;
  readiness = null;
  willingness = null;
  preEmptiveTransplant;
  nationListFilter: any;
  nationList: any;
  dialysisType;
  selectedTransplantPlace: any;
  selectedfollowUpHospital: any;
  procedures: RgVwLiverProceduresDto[] = [];
  procedureDetails: RgProcedureDetailsSaveDto[] = [];
  uniqueProcedureTypes: string[] = [];
  dropdownSettings: IDropdownSettings;
  selectedProcedures: number[] = [];
  liverRegisterForm: FormGroup;
  familyHistoryForm: FormGroup;
  contactAddressForm: FormGroup;
  @Input() caseDetailsForm: FormGroup;
  patientForm: FormGroup;
  @Input() filterModelForm: FormGroup;
  public labTestForm: FormGroup;
  centralRegNoExit: boolean = false;
  currentCivilId: any;
  patntId = "";
  loginId: any;
  today: any;
  testcomponet: any;
  profileList: any;
  ComponetList: any;
  testComponetList: any;
  rgTbLabTestInfo: any = [];
  labTestFg: any = [];
  labTest: any;
  testDone: any[];
  testListToDownload: any[];
  labnewList: any[];
  labListToFixRowSpan: any[];
  labTestName: any[];
  regId: any;
  institutes: any[];
  @Input() submitted = false;
  famHistory: any = [];
  contacts: any = [];
  isChecked: any;
  alive = true;
  isPrint = false;
  delRow;
  selectedCompYes: any;
  selectedCompNo: any;
  rowData: any[] = [];
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
  };

  // Properties for lab results accordion
  labResults: any[] = [];
  openAccordionLab: boolean[] = [];

  liverRegister: LiverRegister = {
    liverId: null,
    centralRegNo: null,
    familyHistoryYn: null,
    childPughScore: null,
    childPughClass: null,
    meldScore: null,
    meldNaScore: null,
    meldExceptionYn: null,
    meldExceptionDetails: null,
    peldScore: null,
    peldExceptionYn: null,
    peldExceptionDetails: null,
    dialysisYn: null,
  };

  rgTbCkdStageTransplant: StageTransplantData = {
    runId: null,
    ckdFailureStage: null,
    dialysisYn: null,
    transplantYn: null,
    preEmptiveYn: null,
    transplantDatr: null,
    transplantInst: null,
    transplantPlaceDesc: null,
    followupInst: null,
    followUpCountry: null,
    transplantCountry: null,
    transplantReadiness: null,
    transplantWillingness: null,
  };

  cateories = [
    {
      title: "Accordion Item 1",
      children: [{ title: "Child 1.1" }, { title: "Child 1.2" }],
    },
    {
      title: "Accordion Item 2",
      children: [{ title: "Child 2.1" }, { title: "Child 2.2" }],
    },
    {
      title: "Accordion Item 3",
      children: [
        { title: "Child 3.1" },
        { title: "Child 3.2" },
        { title: "Child 3.3" },
      ],
    },
  ];

  liverCirrhosisStagesData = [
    {
      id: 1,
      value: "Child Pugh Score",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 2,
      value: "MELD Score",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 3,
      value: "MELD Na Score",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 4,
      value: "MELD Exception",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 5,
      value: "PELD (Patient < 12 years)",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 6,
      value: "PELD Exception",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
    {
      id: 7,
      value: "Child Pugh Class",
      remarksYn: "N",
      checked: false,
      remarks: "",
    },
  ];

  columnDefs = [
    { headerName: "Date", field: "date" },
    { headerName: "Profile Name", field: "profilename" },
    { headerName: "Test Component", field: "testComponent" },
    { headerName: "Result", field: "result" },
    { headerName: "Unit", field: "unit" },
    { headerName: "Institute", field: "institute" },
  ];
  estCode: any;
  private _fullLabnewList: any;
  vaccineFg: any;

  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _router: Router,
    private _http: HttpClient,
    private formBuilder: FormBuilder,
    private _sharedService: SharedService,
    private _caseDetaisService: CaseDetailService,
    private _LiverService: LiverService,
    private loaderService: LoaderService
  ) {}

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;
    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }

  ngOnInit() {
    this.initializeForm();
    this.getMasterData();
    this.loadTransplantIndications();
    this.loadProcedures();
    this.loadComplications();
    this.loadCurrentManagement();
    this.initializeComponent();
    this.getNationalityList();
    this.doDropDown();
    //this.loadLabData();
  }

  doDropDown() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: "mohTestCode",
      textField: "testName",
      selectAllText: "Select All",
      unSelectAllText: "UnSelect All",
      itemsShowLimit: 6,
      allowSearchFilter: true,
    };
  }

  loadContactDetails() {
    const contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;
    this.contactDetails.forEach((contact) => {
      contactDetailsArray.push(
        this.initRgTbContactDetails(
          contact.name,
          contact.relation,
          contact.phone,
          contact.email
        )
      );
    });
  }

  createContactFormGroup(contact: any): FormGroup {
    return this.fb.group({
      runId: [contact.runId],
      name: [contact.name],
      relation: [contact.relation],
      phone: [contact.phone],
      email: [contact.email],
      isEditable: [false], // Default to false
    });
  }

  onAddNewContact() {
    this.addContactDetails("", "", "", "", "", false);
    this.contacts[this.contacts.length - 1].isEditable = true; //
  }

  isEmpty(input) {
    return input.replace(/\s/g, "") === "";
  }

  emailValidator(data) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(data);
  }

  phoneValidator(data) {
    const phoneRegex = /^\d{8,16}$/;// Example: 10-digit number
    return phoneRegex.test(data);
  }

  custom_emailValidator(control: AbstractControl) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(control.value) ? null : { invalidEmail: true }; // Return null if valid
  }

  custom_phoneValidator(control: AbstractControl) {
    const phoneRegex = /^\d{8,16}$/; // Example: 10-digit number
    return phoneRegex.test(control.value) ? null : { invalidPhone: true }; // Return null if valid
  }

  disableSaveButton(row) {
    if (row.name === "") {
      return true;
    }
    return false;
  }

  createContactDetail(): FormGroup {
    return this.fb.group({
      email: ["", [Validators.email]],
      phone: ["", [this.phoneValidator]],
    });
  }

  private initializeForm() {
    this.initializeLiverRegister();

    this.caseDetailsForm = this.fb.group({
      rgTbCkdStageTransplant: this.fb.group({
        runId: [null],
        ckdFailureStage: [null],
        dialysisYn: [null],
        transplantYn: null,
        preEmptiveYn: [null],
        transplantDatr: [null],
        transplantInst: [null],
        transplantPlaceDesc: [null],
        followupInst: [null],
        followUpCountry: [null],
        transplantCountry: [null],
        transplantReadiness: [null],
        transplantWillingness: [null],
      }),
    });

    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
    this.familyHistoryForm = this.fb.group({
      rgTbFamilyHistory: this.fb.array([]),
    });

    this.contactDetailsForm = this.fb.group({
      rgTbContactDetails: this.fb.array([]), // Initialize as a FormArray
    });
    this.filterModelForm = this.fb.group({
      fromDate: [null],
      toDate: [null],
      profileT: [null],
    });
  }

  // Get Master Data for dropdown
  private getMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getLabTestList().subscribe((res) => {
      this.labTest = res.result;
    });

    this._masterService.getLabMaster().subscribe((res) => {
      this.testDone = res.result;
    });
    this._masterService.getLabTestToDownload().subscribe((res) => {
      this.testListToDownload = res["result"];
    });

    this._masterService.getLiverComplicationMast();

    this._masterService.getRelationMast().subscribe(async (res) => {
      this.relation = res["result"];
    });

    this._masterService.getDialysisHospital().subscribe((res) => {
      this.hospitals = res["result"];
    });

    this.liverCirrhosisStages = _.cloneDeep(this.liverCirrhosisStagesData);

    this._masterService.getAllMohLabMaster().subscribe((res) => {
      this.testComponetList = res.result;
      this.profileList = [];
      this.ComponetList = [];
      this.testComponetList.forEach((element) => {
        if (
          this.profileList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.profileList.push({
            testId: element.mohTestCode,
            testName: element.testName,
          });
        }

        if (
          this.ComponetList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.ComponetList.push({
            componentTestId: element.mohTestCode,
            componentTestName: element.testName,
          });
        }
      });
    });
  }

  // seach function for getting case details based on registration id
  search() {
    this.clear();
    if (this.regId) {
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Registration ID",
      });
    }
  }

  // GetList function for loading case details
  getList(regNo: any) {
    this._LiverService.getCaseDetails(regNo).subscribe({
      next: (res) => {
        if (res["code"] == "S0000") {
          this.isPrint = true;
          this.centralRegNoExit = true;
          this.patientDetails.setPatientDetails(res["result"]);
          this.currentCivilId = res["result"].rgTbPatientInfo["civilId"];
          this.estCode = res["result"].rgTbPatientInfo["estCode"];
          this.patntId = res["result"].patientID;
          this.showDownloadButton = true;
          this.showVaccineButton = true;

          // Load medical procedures if available
          if (res["result"].liverProcedureDtos) {
            // Clear existing procedures
            this.procedureDetails = [];

            // Load the procedures
            res["result"].liverProcedureDtos.forEach((proc) => {
              this.procedureDetails.push({
                runId: proc.runId,
                procId: proc.procId,
                doneDate: proc.doneDate ? new Date(proc.doneDate) : null,
                remarks: proc.remarks || "",
              });
            });

            // Update selected procedures
            this.selectedProcedures = this.procedureDetails.map(
              (proc) => proc.procId
            );

            // Make sure procedures are loaded
            if (!this.procedures.length) {
              this.loadProcedures();
            }
          }

          if (res["result"].liRegisterDtos) {
            this.loadLiverRegisterData(res["result"].liRegisterDtos);
          }

          if (res["result"].liverComplicationDtos) {
            this.loadLiverComplicationsData(
              res["result"].liverComplicationDtos
            );
          }

          if (res["result"].livManagementDtos) {
            this.loadCurrentManagementData(res["result"].livManagementDtos);
          }

          if (res["result"].liverTransplantIndicationDtos) {
            this.loadTransplantIndicationsData(
              res["result"].liverTransplantIndicationDtos
            );
          }

          if (
            res["result"].rgTbCkdStageTransplant &&
            res["result"].rgTbCkdStageTransplant.length > 0
          ) {
            let stag = res["result"].rgTbCkdStageTransplant[0];
            let fgStageTransplant: FormGroup = <FormGroup>(
              this.caseDetailsForm.controls["rgTbCkdStageTransplant"]
            );
            fgStageTransplant.patchValue({
              runId: stag.runId,
              ckdFailureStage: stag.ckdFailureStage,
              dialysisYn: stag.dialysisYn,
              transplantYn: stag.transplantYn,
              preEmptiveYn: stag.preEmptiveYn,
              transplantDatr: new Date(stag.transplantDatr),
              transplantInst: stag.transplantInst,
              transplantCountry: stag.transplantCountry,
              followupInst: stag.followupInst,
              followUpCountry: stag.followUpCountry,
              transplantReadiness: stag.transplantReadiness,
              transplantWillingness: stag.transplantWillingness,
            });

            this.dialysis = stag.dialysisYn;
            this.transplant = stag.transplantYn;

            this.readiness = stag.transplantReadiness;
            this.willingness = stag.transplantWillingness;

            this.preEmptiveTransplant = stag.preEmptiveYn;
            //this.stage = stag.ckdFailureStage.toString();
          }

          if (res["result"].rgTbFamilyHistory) {
            let rgTbFamilyHistory: any = res["result"].rgTbFamilyHistory;
            let faFamilyHistory: FormArray = <FormArray>(
              this.familyHistoryForm.controls["rgTbFamilyHistory"]
            );

            for (let famHist of rgTbFamilyHistory) {
              this.addFamilyIstory(
                famHist.runId,
                famHist.name,
                famHist.relation,
                famHist.patientID,
                famHist.instID,
                famHist.source,
                false
              );
            }
          }

          // console.log(res["result"].rgAddContactDetails);

          if (res["result"].rgAddContactDetails) {
            let rgTbContactDetails: any = res["result"].rgAddContactDetails;

            //console.log("rgTbContactDetails--1",rgTbContactDetails);

            let contactDetailsFM: FormArray = <FormArray>(
              this.contactDetailsForm.controls["rgTbContactDetails"]
            );

            for (let conts of rgTbContactDetails) {
              this.addContactDetails(
                conts.runId,
                conts.name,
                conts.relation,
                conts.phone,
                conts.email,
                false
              );
            }

            // console.log("contactDetailsArra--3",this.contactDetailsArray.value);
          }


          if (res["result"].rgTbLabTests) { 
          for (let labRList of res["result"].rgTbLabTests) {
            let componentTestName = this.testComponetList
              .filter((s) => s.mohTestCode == labRList.mohTestCode)
              .map((s) => s.testName)[0]
              .toString();
            let instName = this.institutes
              .filter((s) => s.estCode == labRList.instCode)
              .map((s) => s.estName)[0];
            let profileTestName = this.testComponetList
              .filter((s) => s.mohTestCode == labRList.profileTestCode)
              .map((s) => s.testName)[0]
              .toString();

              //this._fullLabnewList = [...res["result"].rgTbLabTest];
         // this.totalLabRecords = this._fullLabnewList.length;

            this.addNewLabList(
              labRList.runId,
              this._sharedService.setDateFormat(labRList.testDate),
              this._sharedService.setDateFormat(labRList.releasedDate),
              labRList.profileTestCode,
              profileTestName,
              labRList.mohTestCode,
              componentTestName,
              labRList.value,
              labRList.unit,
              labRList.instCode,
              instName,
              labRList.enteredBy,
              labRList.enteredDate,
              labRList.source,
              false
            );
          }
        }

          this.labListToFixRowSpan = res["result"].rgTbLabTest;

          if(res["result"].rgTbVaccinationInfo){
            this.vaccineFg = res["result"].rgTbVaccinationInfo;
              const rgTbVaccinationInfoDb: any = res["result"].rgTbVaccinationInfo;
          for (let vaccinaList of rgTbVaccinationInfoDb) {
            this.Vaccination.addNewVaccine(
              vaccinaList.runId,
              vaccinaList.enteredBy,
              new Date(vaccinaList.vaccinationDate),
              vaccinaList.vaccineCode,
              vaccinaList.vaccinatedInst,
              vaccinaList.remarks,
              vaccinaList.civilId,
              vaccinaList.source,
              false
            );
          }
          }
          
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      error: (error) => {
        if (error.status == 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      },
    });
  }

  // Transpant Indication Start

  loadTransplantIndications() {
    this._masterService.getLiverTransplantMast();

    this._masterService.liverTransplanMastList.subscribe((value) => {
      this.liverTransIndications = _.cloneDeep(value);
    });
  }

  prepareTransplantIndicationsData(): any {
    return this.liverTransIndications
      .filter((indication) => indication.checked)
      .map((indication) => ({
        id: indication.id,
        value: indication.value,
        checked: indication.checked,
        remarksYn: indication.remarksYn,
        remarks: indication.remarks || "",
      }));
  }

  submit() {
    if (this.caseDetailsForm.status === "VALID") {
    }
  }

  getTransplantIndicationControls() {
    return (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).controls;
  }

  addTransplantIndication() {
    const indicationForm = this.fb.group({
      checked: [false],
      value: [""],
      remarks: [""],
    });
    (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).push(indicationForm);
  }

  // Getter methods for FormArrays
  getLiverCirrhosisStagesControls() {
    return (this.liverRegisterForm.get("liverCirrhosisStages") as FormArray)
      .controls;
  }

  getLiverComplicationControls() {
    return (this.liverRegisterForm.get("rgLiverComplication") as FormArray)
      .controls;
  }

  getCurrentManagementControls() {
    return (this.liverRegisterForm.get("rgTbLiverManagement") as FormArray)
      .controls;
  }

  // Methods to add form groups to arrays
  addLiverCirrhosisStage(stage: any) {
    const stageForm = this.fb.group({
      id: [stage.id],
      stageName: [stage.stageName],
      remarks: [""],
      meldExceptionYn: ["N"],
      meldExceptionDetails: [""],
    });
    (this.liverRegisterForm.get("liverCirrhosisStages") as FormArray).push(
      stageForm
    );
  }

  addLiverComplication(complication: any) {
    const complicationForm = this.fb.group({
      id: [complication.id],
      complicationName: [complication.name],
      checked: [false],
      remarks: [""],
      remarksYn: [complication.remarksYn],
    });
    (this.liverRegisterForm.get("rgLiverComplication") as FormArray).push(
      complicationForm
    );
  }

  // Current Management Start

  addCurrentManagement(management: any) {
    const managementForm = this.fb.group({
      id: [management.id],
      managementType: [management.type],
      status: ["N"],
      remarks: [""],
    });
    (this.liverRegisterForm.get("rgTbLiverManagement") as FormArray).push(
      managementForm
    );
  }

  loadCurrentManagementMaster() {
    this._LiverService.getCurrentManagementMaster().subscribe({
      next: (response: ResultDecorator) => {
        if (response && response.result) {
          this.currentManagement = response.result.map(
            (master: LiverCurrMgmtMasterDto) => ({
              id: master.id,
              value: master.value,
              active: master.active,
              remarksYn: master.remarksYn,
              subList: master.subList.map((sub) => ({
                id: sub.id,
                prevId: sub.prevId,
                value: sub.value,
                active: sub.active,
                remarksYn: sub.remarksYn,
                name: sub.name,
                isSelected: false,
                dose1: "",
                dose2: "",
                date: null,
                comments: "",
              })),
            })
          );

          // Initialize accordion state
          this.openAccordion = new Array(this.currentManagement.length).fill(
            false
          );
        }
      },
      error: (error) => {
        console.error("Error loading current management master:", error);
      },
    });
  }

  // Current Management End

  // Procedure start
  loadProcedures() {
    this._LiverService.getLiverProcedure().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.procedures = response.result;
          // Get unique procedure types
          this.uniqueProcedureTypes = [
            ...new Set(this.procedures.map((p) => p.paramName)),
          ];
        }
      },
      error: (error) => {
        console.error("Error loading procedures:", error);
      },
    });
  }

  onProcedureTypeSelect(name: string) {
    const procedure = this.procedures.find((p) => p.paramName === name);
    if (procedure) {
      const existingIndex = this.procedureDetails.findIndex(
        (pd) => pd.procId === procedure.paramId
      );

      if (existingIndex === -1) {
        // Add new procedure
        this.procedureDetails.push({
          runId: null,
          procId: procedure.paramId,
          doneDate: null,
          remarks: "",
        });
      } else {
        // Remove procedure if it exists
        this.procedureDetails.splice(existingIndex, 1);
      }
    }
  }

  updateProcedureDetails(procId: number, field: string, value: any) {
    const detail = this.procedureDetails.find((pd) => pd.procId === procId);
    if (detail) {
      detail[field] = value;
    }
  }

  isProcedureSelected(type: string): boolean {
    if (!this.procedureDetails || !this.procedures) {
      return false;
    }
    return this.procedureDetails.some(
      (pd) =>
        this.procedures.find((p) => p.paramId === pd.procId).paramName ===
        type
    );
  }

  getProcedureType(procId: number): string {
    if (!this.procedures) {
      return "";
    }
    const procedure = this.procedures.find((p) => p.paramId === procId);
    return procedure ? procedure.paramName : "";
  }

  getProcedureDetail(procedureType: string): RgProcedureDetailsDto | undefined {
    if (!this.procedureDetails || !this.procedures) {
      return undefined;
    }

    return this.procedureDetails.find(
      (detail) => this.getProcedureType(detail.procId) === procedureType
    );
  }

  // procedure end

  // Complication start

  getLiverComplications() {
    this._LiverService.getLiverComplications().subscribe(
      (data: LiverComplicationMasterDto[]) => {
        this.liverCirrhosisComplications = data;
        // Load existing data if editing
        if (this.liverId) {
          this.loadExistingComplications();
        }
      },
      (error) => {
        console.error("Error fetching complications", error);
      }
    );
  }

  loadExistingComplications() {
    this._LiverService.getLiverComplicationsByLiverId(this.liverId).subscribe(
      (data) => {
        data.forEach((comp) => {
          this.selectedComplications.set(comp.complicationId, true);
          this.complicationDetails.set(comp.complicationId, comp);

          if (comp.complicationId === 24) {
            this.hepatocellular = true;
          }
          if (comp.complicationId === 102) {
            this.locoTherapy = true;
          }
        });
      },
      (error) => {
        console.error("Error loading existing complications", error);
      }
    );
  }

  saveComplications() {
    const complications: LiverComplicationSave[] = [];

    this.selectedComplications.forEach((isSelected, id) => {
      if (isSelected) {
        const complication = this.liverCirrhosisComplications.find(
          (c) => c.id === id
        );
        if (complication) {
          const saveItem: LiverComplicationSave = {
            liverId: this.liverId,
            complicationId: id,
            remarks: "",
            subItems: [],
          };

          // Handle hepatocellular carcinoma (id 24)
          if (id === 24 && this.hepatocellular) {
            const subItems: LiverComplicationSubItem[] = [];
            complication.subList.forEach((item, index) => {
              const subItem: LiverComplicationSubItem = {
                itemValue: item,
                remarks: "",
                checked: false,
              };

              if (index < 2) {
                // Get text input values
                const input = document.getElementById(
                  `text-${id}-${index}`
                ) as HTMLInputElement;
                subItem.remarks = input.value || "";
              } else {
                // Get checkbox values
                const checkbox = document.getElementById(
                  `checkbox-${id}-${index}`
                ) as HTMLInputElement;
                subItem.checked = checkbox.checked || false;
              }

              subItems.push(subItem);
            });
            saveItem.subItems = subItems;
          }

          // Handle loco regional therapy (id 102)
          if (id === 102 && this.locoTherapy) {
            const input = document.getElementById(
              `remarks-${id}`
            ) as HTMLInputElement;
            saveItem.remarks = input.value || "";
          }

          complications.push(saveItem);
        }
      }
    });

    // console.log(complications);
  }

  onChangeN(event: any, id: number) {
    const isChecked = event.target.value === "1";
    this.selectedComplications.set(id, isChecked);

    if (id === 24) {
      this.hepatocellular = isChecked;
    }
    if (id === 102) {
      this.locoTherapy = isChecked;
    }

    if (!isChecked) {
      this.complicationDetails.delete(id);
    }
  }

  loadComplications() {
    //console.log("callingapi");
    this._LiverService.getComplications().subscribe({
      next: (response: any) => {
        if (response && response.result) {
          // console.log("test--" + JSON.stringify(response));
          // The result property contains the actual array of complications
          const complications = response.result as LiverComplicationMasterDto[];

          this.liverCirrhosisComplications = complications.map((comp) => ({
            ...comp,
            selected: null,
            subItems: comp.id === 24 ? this.initializeSubItems(comp) : [],
          }));

          const hepatocellularCarcinoma = this.liverCirrhosisComplications.find(
            (comp) => comp.id === 24
          );
          if (hepatocellularCarcinoma) {
            this.liverCirrhosisComplications =
              this.liverCirrhosisComplications.filter((comp) => comp.id != 24);
            this.liverCirrhosisComplications.push(hepatocellularCarcinoma);
          }
        }
      },
      error: (error) => {
        console.error("Error loading complications:", error);
        this.liverCirrhosisComplications = [];
      },
    });
  }

  private initializeSubItems(
    complication: LiverComplicationMasterDto
  ): SubItem[] {
    // If it's the hepatocellular carcinoma (id: 24)
    if (complication.id === 24 && complication.subList) {
      return complication.subList.map((item, index) => ({
        remarks: "", // For first two items (No. of tumors, Size of largest tumor)
        checked: false, // For last two items (Within Milan, Beyond Milan)
      }));
    }

    // For other complications, return empty array or existing subItems
    return complication.subItems || [];
  }

  loadCurrentManagement() {
    this._LiverService.getCurrentManagement().subscribe({
      next: (response: ResultDecorator) => {
        if (response && response.result) {
          this.currentManagement = this.transformToUIState(
            response.result as LiverCurrMgmtMasterDto[]
          );
          // this.currentManagement = response.result as LiverCurrMgmtMasterDto[];
          // Initialize accordion state array
          this.openAccordion = new Array(this.currentManagement.length).fill(
            false
          );

          // Initialize UI state for subList items
          this.currentManagement.forEach((management) => {
            if (management.subList) {
              management.subList = management.subList.map((item) => ({
                ...item,
                isSelected: false,
                dose1: null,
                dose2: null,
                date: null,
                comments: "",
              }));
            }
          });
        }
      },
      error: (error) => {
        console.error("Error loading current management:", error);
        this.currentManagement = [];
      },
    });
  }

  saveCurrentManagement() {
    const savedData = this.currentManagement.map((management) => ({
      ...management,
      subList: management.subList
        .filter((item) => item.isSelected)
        .map((item) => ({
          id: item.id,
          prevId: management.id,
          value: item.value,
          active: item.active,
          remarksYn: item.remarksYn,
          name: item.name,
          dose1: management.id !== 2 ? item.dose1 || "" : "",
          dose2: management.id !== 2 ? item.dose2 || "" : "",
          date:
            management.id === 2
              ? item.date
                ? this.formatDate(item.date)
                : ""
              : "",
          comments: item.comments || "",
        })),
    }));

    this._LiverService.saveCurrentManagement(savedData).subscribe({
      next: (response: ResultDecorator) => {
        if (response.code === "SUCCESS") {
          console.log("Current management saved successfully");
          // Handle success (show toast message, etc.)
        } else {
          console.error("Error saving current management:", response.message);
          // Handle error
        }
      },
      error: (error) => {
        console.error("Error saving current management:", error);
        // Handle error
      },
    });
  }

  loadTransplantMaster() {
    this._LiverService.getTransplantMaster().subscribe({
      next: (response: ResultDecorator) => {
        if (response && response.result) {
          this.liverTransIndications = response.result.map(
            (item: LiverIndication) => ({
              ...item,
              checked: false, // Initialize as unchecked
              remarks: "", // Add remarks field for UI
            })
          );
        }
      },
      error: (error) => {
        console.error("Error loading transplant master:", error);
      },
    });
  }

  loadSavedTransplantIndications(centralRegNo: number) {
    this._LiverService.getCaseDetails(centralRegNo).subscribe({
      next: (response: ResultDecorator) => {
        if (response.result.liverTransplantIndicationDtos) {
          const savedIndications =
            response.result.liverTransplantIndicationDtos;

          // Update master list with saved values
          this.liverTransIndications = this.liverTransIndications.map(
            (master) => {
              const saved = savedIndications.find(
                (s) => s.paramId === master.id
              );
              if (saved) {
                return {
                  ...master,
                  checked: saved.paramYn === "Y",
                  remarks: saved.remarks || "",
                };
              }
              return master;
            }
          );
        }
      },
      error: (error) => {
        console.error("Error loading saved indications:", error);
      },
    });
  }

  specifyIndication(option: LiverIndication, event: any): void {
    option.checked = event.target.checked;
    if (!option.checked) {
      option.remarks = "";
    }
  }

  transformToUIState(
    apiData: LiverCurrMgmtMasterDto[]
  ): LiverCurrMgmtUIState[] {
    return apiData.map((management) => ({
      id: management.id,
      value: management.value,
      active: management.active,
      remarksYn: management.remarksYn,
      subList: management.subList.map((item) => ({
        ...item,
        isSelected: false,
        dose1: null,
        dose2: null,
        date: null,
        comments: "",
      })),
    }));
  }

  debugSelectedItems() {
    // console.log('=== Debugging Selected Items ===');
    this.currentManagement.forEach((management) => {
      const selectedItems = management.subList.filter(
        (item) => item.isSelected
      );
      if (selectedItems.length > 0) {
        console.log(
          `Management ${management.id} (${management.value}) has selected items:`,
          selectedItems.map((item) => ({
            id: item.id,
            value: item.value,
            isSelected: item.isSelected,
          }))
        );
      }
    });

    // Test the transformation
    const preparedData = this.transformToSaveDto(this.currentManagement);
    //console.log("Transformed data:", preparedData);
  }

  private transformToSaveDtoDebug(
    uiState: LiverCurrMgmtUIState[]
  ): LiverCurrMgmtMasterDto[] {
    // console.log('Starting transformToSaveDto with UI state:', JSON.stringify(uiState, null, 2));

    // Filter managements with selected items
    const filteredManagements = uiState.filter((management) =>
      management.subList.some((item) => item.isSelected)
    );
    // console.log(
    //   "Filtered managements with selected items:",
    //   JSON.stringify(filteredManagements, null, 2)
    // );

    const transformedData = filteredManagements.map((management) => {
      // console.log(
      //   `Processing management ID ${management.id}:`,
      //   JSON.stringify(management, null, 2)
      // );

      const transformedSubList = management.subList
        .filter((item) => {
          const isSelected = item.isSelected;
          //console.log(`SubItem ${item.id} selected:`, isSelected);
          return isSelected;
        })
        .map((item) => {
          // console.log(
          //   `Transforming subItem ${item.id} for management ${management.id}:`,
          //   JSON.stringify(item, null, 2)
          // );

          const saveDto: LiverCurrMgmtSubTyperSaveDto = {
            id: item.id,
            prevId: management.id,
            value: item.value,
            active: item.active,
            remarksYn: item.remarksYn,
            name: item.name,
            dose1: management.id !== 2 ? item.dose1 : null,
            dose2: management.id !== 2 ? item.dose2 : null,
            date:
              management.id === 2
                ? item.date
                  ? this.formatDate(item.date)
                  : ""
                : "",
            comments: item.comments || "",
          };

          // console.log(
          //   `Created saveDto for subItem ${item.id}:`,
          //   JSON.stringify(saveDto, null, 2)
          // );
          return saveDto;
        });

      const result = {
        id: management.id,
        value: management.value,
        active: management.active,
        remarksYn: management.remarksYn,
        subList: transformedSubList,
      };

      // console.log(
      //   `Final transformed management ${management.id}:`,
      //   JSON.stringify(result, null, 2)
      // );
      return result;
    });

    // console.log(
    //   "Final transformed data:",
    //   JSON.stringify(transformedData, null, 2)
    // );
    return transformedData;
  }

  transformToSaveDto(
    uiState: LiverCurrMgmtUIState[]
  ): LiverCurrMgmtMasterDto[] {
    return uiState
      .filter((management) =>
        management.subList.some((item) => item.isSelected)
      )
      .map((management) => ({
        id: management.id,
        value: management.value,
        active: management.active,
        remarksYn: management.remarksYn,
        subList: management.subList
          .filter((item) => item.isSelected)
          .map((item) => {
            const saveDto: LiverCurrMgmtSubTyperSaveDto = {
              id: item.id,
              prevId: management.id,
              value: item.value,
              active: item.active,
              remarksYn: item.remarksYn,
              name: item.name,
              dose1: management.id !== 2 ? item.dose1 : null,
              dose2: management.id !== 2 ? item.dose2 : null,
              date:
                management.id === 2
                  ? item.date
                    ? this.formatDate(item.date)
                    : ""
                  : "",
              comments: item.comments || "",
            };
            return saveDto;
          }),
      }));
  }

  formatDate(date: Date): string {
    if (!date) return "";
    if (typeof date === "string") return date;
    return date.toISOString().split("T")[0];
  }

  saveComplicationsLiver(): LiverComplicationDto[] {
    const complications: LiverComplicationDto[] = [];

    this.liverCirrhosisComplications.forEach((complication) => {
      if (complication.selected === "Y") {
        const dto: any = {
          liverId: this.patientDetails.f.centralRegNo.value, // Assuming you have liverId from parent
          paramId: complication.id,
          paramYn: complication.selected,
          remarks: complication.remarks || "",
        };

        // Handle Hepatocellular Carcinoma
        if (complication.id === 24 && complication.subItems) {
          // First two items are for number of tumors and tumor length
          dto.noOfTumors = parseInt(complication.subItems[0].remarks) || null;
          dto.tumorLength =
            parseFloat(complication.subItems[1].remarks) || null;

          // Remaining items are checkboxes for Milan criteria
          const milanCriteriaChecked = complication.subItems
            .slice(2)
            .some((item) => item.checked);
          dto.milanCriteria = milanCriteriaChecked ? "Y" : "N";
        }

        // Handle Loco Therapy
        if (complication.id === 102) {
          dto.prevLocoTherapyYn = complication.selected;
          dto.prevLocoTherapyDtls = complication.remarks || "";
        }

        complications.push(dto);
      }
    });

    return complications;
  }

  prepareComplicationsData(): any[] {
    return this.liverCirrhosisComplications
      .filter((comp) => comp.selected === "Y")
      .map((comp) => ({
        id: comp.id,
        value: comp.value,
        remarksYn: comp.remarksYn,
        remarks: comp.remarks || "",
        subList: comp.subList,
        subItems: comp.id === 24 ? comp.subItems : undefined,
      }));
  }

  loadCaseDetails(liverId: number) {
    this._LiverService.getLiverCaseOrg(liverId).subscribe({
      next: (response: LiverCaseDetailsDto) => {
        this.caseDetails = response;

        // Update complications with saved data
        if (
          response.liverComplicationDtos &&
          response.liverComplicationDtos.length > 0
        ) {
          this.liverCirrhosisComplications =
            this.liverCirrhosisComplications.map((comp) => {
              const savedComp = response.liverComplicationDtos!.find(
                (saved) => saved.id === comp.id
              );

              if (savedComp) {
                return {
                  ...comp,
                  selected: "Y",
                  remarks: savedComp.remarks,
                  subItems: this.mapSubItems(comp.subList, savedComp.subItems),
                };
              }
              return { ...comp, selected: "N" };
            });
        }
      },
      error: (error) => {
        console.error("Error loading case details:", error);
      },
    });
  }

  private mapSubItems(
    subList: string[] | undefined,
    savedSubItems: any[] | undefined
  ): SubItem[] {
    if (!subList) return [];

    return subList.map((_, index) => {
      const savedItem =
        index >= 0 && index < savedSubItems.length
          ? savedSubItems[index]
          : undefined;
      return {
        remarks: savedItem.remarks || "",
        checked: savedItem.checked || false,
      };
    });
  }

  loadLiverCase(liverId: number) {
    this._LiverService.getLiverCase(liverId, "", "").subscribe({
      next: (data: LiverRegister) => {
        this.liverRegister = {
          ...this.liverRegister,
          ...data,
          // Ensure Y/N values are properly set
          familyHistoryYn: data.familyHistoryYn || "N",
          meldExceptionYn: data.meldExceptionYn || "N",
          peldExceptionYn: data.peldExceptionYn || "N",
          dialysisYn: data.dialysisYn || "N",
        };
      },
      error: (error) => {
        console.error("Error loading liver case:", error);
        // Handle error appropriately
      },
    });
  }

  prepareLiverRegisterData(): LiverRegister {
    return {
      ...this.liverRegister,
      // Convert empty strings to null for number fields
      childPughScore: this.liverRegister.childPughScore || null,
      meldScore: this.liverRegister.meldScore || null,
      meldNaScore: this.liverRegister.meldNaScore || null,
      peldScore: this.liverRegister.peldScore || null,
      // Clear details if exception is No
      meldExceptionDetails:
        this.liverRegister.meldExceptionYn === "Y"
          ? this.liverRegister.meldExceptionDetails
          : null,
      peldExceptionDetails:
        this.liverRegister.peldExceptionYn === "Y"
          ? this.liverRegister.peldExceptionDetails
          : null,
    };
  }

  addRemarks(option: LiverIndication, event: any): void {
    option.remarks = event.target.value;
  }

  updateCirrhosisStageRemarks(stage: LiverCirrhosisStage, event: any): void {
    stage.remarks = event.target.value;
  }

  saveCurrentManagementLiver(): LiverManagementDto[] {
    const managementList: LiverManagementDto[] = [];

    this.currentManagement.forEach((management) => {
      // Filter only selected items
      const selectedItems = management.subList.filter(
        (item) => item.isSelected
      );

      selectedItems.forEach((item) => {
        const dto: any = {
          liverId: this.patientDetails.f.centralRegNo.value, // Assuming you have liverId from parent
          paramId: management.id,
          subId: item.id,
        };

        // Handle different management types
        if (management.id === 2) {
          // Procedures
          dto.doneDate = item.date ? new Date(item.date) : null;
          dto.remarks = item.comments || "";
        } else {
          // Medications
          // Convert dose1 and dose2 to dose and frequency
          dto.dose = item.dose1 ? item.dose1 : null;
          dto.frequency = item.dose2 ? item.dose2 : null;
          dto.remarks = item.comments || "";
        }

        managementList.push(dto);
      });
    });

    return managementList;
  }

  validateComplications(): boolean {
    const validationErrors: string[] = [];

    this.liverCirrhosisComplications.forEach((complication) => {
      if (complication.selected === "Y") {
        // Validate Hepatocellular Carcinoma
        if (complication.id === 24) {
          const noOfTumors = parseInt(complication.subItems[0].remarks);
          const tumorLength = parseFloat(complication.subItems[1].remarks);

          if (!noOfTumors || noOfTumors < 0) {
            validationErrors.push("Number of tumors must be a positive number");
          }
          if (!tumorLength || tumorLength < 0) {
            validationErrors.push("Tumor length must be a positive number");
          }
        }

        // Validate Loco Therapy
        if (complication.id === 102 && !complication.remarks) {
          validationErrors.push("Please specify details for Loco Therapy");
        }
      }
    });

    if (validationErrors.length > 0) {
      console.error("Validation errors:", validationErrors);
      // Show validation errors to user
      return false;
    }

    return true;
  }

  validateCurrentManagement(): boolean {
    const validationErrors: string[] = [];

    this.currentManagement.forEach((management) => {
      const selectedItems = management.subList.filter(
        (item) => item.isSelected
      );

      selectedItems.forEach((item) => {
        if (management.id === 2) {
          // Procedures
          if (!item.date) {
            validationErrors.push(`Date is required for ${item.value}`);
          }
        } else {
          // Medications
          if (!item.dose1) {
            validationErrors.push(`Dose is required for ${item.value}`);
          }
          if (!item.dose2) {
            validationErrors.push(`Frequency is required for ${item.value}`);
          }
        }
      });
    });

    if (validationErrors.length > 0) {
      console.error("Validation errors:", validationErrors);
      // Show validation errors to user
      return false;
    }

    return true;
  }

  prepareSaveData(): CaseDetails {
    return {
      liverTransplantIndications: this.liverTransIndications.filter(
        (item) => item.checked || item.remarks
      ),
      familyHistory: {
        hasHistory: this.familyHistoryYn === 1,
        remarks: this.familyHistoryRemarks,
      },
      liverCirrhosisStages: this.liverCirrhosisStages.map((stage) => ({
        ...stage,
        meldException:
          stage.id === 5
            ? {
                hasException: this.meldExceptionYn === 1,
                remarks: this.meldExceptionRemarks,
              }
            : undefined,
        peldException:
          stage.id === 7
            ? {
                hasException: this.peldExceptionYn === 1,
                remarks: this.peldExceptionRemarks,
              }
            : undefined,
      })),
      liverCirrhosisComplications: this.prepareComplicationsData(),
      currentManagement: this.transformToSaveDto(this.currentManagement),
    };
  }

  onIndicationChange(option: any) {
    // Convert boolean to Y/N
    option.paramYn = option.checked ? "Y" : "N";

    // Clear remarks if unchecked
    if (!option.checked) {
      option.remarks = "";
    }
  }

  onRemarksChange(option: any) {
    // Validate remarks if required
    if (option.remarksYn === "Y" && option.checked && !option.remarks) {
      // Show validation message
      console.error(`Remarks are required for ${option.value}`);
    }
  }

  saveTransplantIndications(): LiverTransplantIndicationDto[] {
    const indications: LiverTransplantIndicationDto[] = [];

    this.liverTransIndications.forEach((indication) => {
      if (indication.checked) {
        const dto: any = {
          liverId: this.patientDetails.f.centralRegNo.value, // Assuming you have liverId from parent
          paramId: indication.id,
          paramYn: "Y",
          remarks: indication.remarksYn === "Y" ? indication.remarks : null,
        };

        indications.push(dto);
      }
    });

    return indications;
  }

  validateTransplantIndications(): boolean {
    const validationErrors: string[] = [];

    this.liverTransIndications.forEach((indication) => {
      if (
        indication.checked &&
        indication.remarksYn === "Y" &&
        !indication.remarks
      ) {
        validationErrors.push(`Remarks are required for ${indication.value}`);
      }
    });

    if (validationErrors.length > 0) {
      console.error("Validation errors:", validationErrors);
      // Show validation errors to user
      return false;
    }

    return true;
  }

  saveProcedureDetails(): RgProcedureDetailsDto[] {
    return this.uniqueProcedureTypes
      .filter((name) => this.isProcedureSelected(name))
      .map((name) => {
        const detail = this.getProcedureDetail(name);
        if (!detail) return null;

        let doneDate = null;
        if (detail.doneDate) {
          try {
            if (detail.doneDate instanceof Date) {
              doneDate = detail.doneDate;
            } else {
              doneDate = new Date(detail.doneDate);
            }
          } catch (e) {
            console.error("Error parsing date:", e);
          }
        }

        return {
          procId: detail.procId,
          doneDate: doneDate,
          remarks: detail.remarks || "",
        };
      })
      .filter((detail) => detail !== null);
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        // console.log(this.nationList, "nationlist");
        this.nationListFilter = this.nationList;
      },
      (error) => {}
    );
  }

  validateLabTests(labTestsData: any[]): boolean {
       const invalidLabTests = (labTestsData || []).filter(
  l => !l.mohTestCode || !l.profileTestCode || !l.instCode || !l.testDate || l.value === null || l.value === ''
);

if (invalidLabTests.length > 0) {
  Swal.fire("Warning", "Lab entries are mandatory.", "warning");
  return false;
}

return true;
}

validateVaccination(vaccinationInfoData: any[]): boolean {
  const invalidVaccines = (vaccinationInfoData || []).filter(
    v => !v.vaccineCode || !v.vaccinationDate
  );

  if (invalidVaccines.length > 0) {
    Swal.fire("Warning", "Vaccine Name and Date are mandatory for all entries.", "warning");
    return false;
  }

  return true;
  }

  saveDetails(): void {
    let patientInfoData = this.patientDetails.patientForm.value;
    let labTestsData = this.labTestForm.value.rgTbLabTestInfo;

    if (!this.validateLabTests(labTestsData)) {
      return;
    }

    let vaccinationInfoData =
      this.Vaccination.vaccinationForm.value.rgTbVaccinationInfo;

    if (!this.validateVaccination(vaccinationInfoData)) {
      return;
    }

    let tranplantIndication = [];

    tranplantIndication.push({
      tranplantIndication: this.saveTransplantIndications(),
    });

    let StageTransplantData = this.caseDetailsForm.value.rgTbCkdStageTransplant;
    let rgTbCkdStageTransplant = [];
    rgTbCkdStageTransplant.push({
      runId: StageTransplantData.runId,
      ckdFailureStage: StageTransplantData.ckdFailureStage,
      dialysisYn: StageTransplantData.dialysisYn,
      transplantYn: StageTransplantData.transplantYn,
      preEmptiveYn: StageTransplantData.preEmptiveYn,
      transplantDatr: StageTransplantData.transplantDatr,
      transplantInst: StageTransplantData.transplantInst,
      transplantPlaceDesc: StageTransplantData.transplantPlaceDesc,
      followupInst: StageTransplantData.followupInst,
      followupPlaceDesc: StageTransplantData.followupPlaceDesc,
      followUpCountry: StageTransplantData.followUpCountry,
      transplantCountry: StageTransplantData.transplantCountry,
      transplantReadiness: StageTransplantData.transplantReadiness,
      transplantWillingness: StageTransplantData.transplantWillingness,
    });

    const liverRegister: LiverRegister = {
      liverId: this.patientDetails.f.centralRegNo.value,
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      childPughScore: this.liverRegister.childPughScore || null,
      childPughClass: this.liverRegister.childPughClass || null,
      meldScore: this.liverRegister.meldScore || null,
      meldNaScore: this.liverRegister.meldNaScore || null,
      peldScore: this.liverRegister.peldScore || null,
      meldExceptionYn: this.liverRegister.meldExceptionYn || "N",
      peldExceptionYn: this.liverRegister.peldExceptionYn || "N",
      // Clear details if exception is No
      meldExceptionDetails:
        this.liverRegister.meldExceptionYn === "Y"
          ? this.liverRegister.meldExceptionDetails
          : null,
      peldExceptionDetails:
        this.liverRegister.peldExceptionYn === "Y"
          ? this.liverRegister.peldExceptionDetails
          : null,
      familyHistoryYn: this.liverRegister.familyHistoryYn || "N",
      dialysisYn: this.liverRegister.dialysisYn || "N",
    };

    let complicationsData = [];
    complicationsData.push({
      complicationsData: this.saveComplicationsLiver(),
    });

    let currentMgmtData = [];
    currentMgmtData.push({
      currentMgmtData: this.saveCurrentManagementLiver(),
    });

    const contactDetailsArray =
      this.contactDetailsForm.value.rgTbContactDetails;
    const validContactDetails = [];

    contactDetailsArray.forEach((contact) => {
      if (contact.name && contact.phone && contact.email) {
        validContactDetails.push(contact);
      }
    });

    let saveData = {
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      patientID: this.patientDetails.f.patientId.value,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_LIVER,
      rgTbPatientInfo: patientInfoData,
      rgTbLabTests: labTestsData,
      liverTransplantIndicationDtos: this.saveTransplantIndications(),
      liRegisterDtos: liverRegister,
      liverComplicationDtos: this.saveComplicationsLiver(),
      livManagementDtos: this.saveCurrentManagementLiver(),
      liverProcedureDtos: this.saveProcedureDetails(),
      rgTbVaccinationInfo: vaccinationInfoData,
      rgTbFamilyHistory: this.familyHistoryForm.value.rgTbFamilyHistory,
      rgAddContactDetails: validContactDetails,
      rgTbCkdStageTransplant: rgTbCkdStageTransplant,
    };

    console.log(saveData);

    this._LiverService.saveMainCaseDetails(saveData).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire("Saved!", "Liver Registry Saved successfully.", "success");
          this.regId = res["result"];
          //console.log("regId--", this.regId);
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire(
          "Error!",
          "Error occured while saving Liver Registry " + err.message,
          "error"
        );
      }
    );
  }

  prepareCurrMgmtData() {
    this.debugSelectedItems(); // Log current state

    const preparedData = this.transformToSaveDto(this.currentManagement);

    if (preparedData.length === 0) {
      console.warn("No items selected for saving");
      return;
    }
  }

  loadComplicationsMaster() {
    this._LiverService.getComplicationsMaster().subscribe({
      next: (response: ResultDecorator) => {
        if (response && response.result) {
          this.liverCirrhosisComplications = response.result.map(
            (master: LiverComplicationMasterDto) => ({
              id: master.id,
              value: master.value,
              remarksYn: master.remarksYn,
              selected: "N",
              remarks: "",
              subList: master.subList || [],
              // Initialize subItems for special cases
              subItems:
                master.id === 24
                  ? [
                      { remarks: "" }, // Number of tumors
                      { remarks: "" }, // Tumor length
                      { checked: false }, // Milan criteria
                    ]
                  : [],
            })
          );
        }
      },
      error: (error) => {
        console.error("Error loading complications master:", error);
      },
    });
  }

  private loadLiverRegisterData(registerData: LiverRegister) {
    this.liverRegister = {
      liverId: registerData.liverId || null,
      centralRegNo: registerData.centralRegNo || null,
      familyHistoryYn: registerData.familyHistoryYn || "N",
      childPughScore: registerData.childPughScore || null,
      childPughClass: registerData.childPughClass
        ? registerData.childPughClass.toUpperCase()
        : null,
      meldScore: registerData.meldScore || null,
      meldNaScore: registerData.meldNaScore || null,
      meldExceptionYn: registerData.meldExceptionYn || "N",
      meldExceptionDetails: registerData.meldExceptionDetails || null,
      peldScore: registerData.peldScore || null,
      peldExceptionYn: registerData.peldExceptionYn || "N",
      peldExceptionDetails: registerData.peldExceptionDetails || null,
      dialysisYn: registerData.dialysisYn || "N",
    };
  }

  private loadLiverComplicationsData(complications: LiverComplicationDto[]) {
    const masterComplications =
      this._masterService.liverComplicationMastList.value;

    if (!masterComplications) {
      console.error("Master complications not loaded");
      return;
    }

    this.liverCirrhosisComplications = masterComplications.map((masterComp) => {
      const savedComp = complications.find((c) => c.paramId === masterComp.id);

      // Special handling for Hepatocellular Carcinoma (id: 24)
      if (masterComp.id === 24 && savedComp) {
        return {
          ...masterComp,
          selected: savedComp.paramYn || "N",
          remarks: savedComp.remarks || "",
          subItems: masterComp.subList
            ? masterComp.subList.map((item, index) => {
                if (index === 0) {
                  // Number of tumors
                  return {
                    remarks: savedComp.noOfTumors.toString() || "",
                    checked: false,
                  };
                } else if (index === 1) {
                  // Tumor size
                  return {
                    remarks: savedComp.tumorLength.toString() || "",
                    checked: false,
                  };
                } else {
                  // Milan criteria checkboxes
                  return {
                    remarks: "",
                    checked: savedComp.milanCriteria === "Y",
                  };
                }
              })
            : [],
        };
      }

      // Special handling for Loco Therapy (id: 102)
      if (masterComp.id === 102 && savedComp) {
        return {
          ...masterComp,
          selected: savedComp.prevLocoTherapyYn || "N",
          remarks: savedComp.prevLocoTherapyDtls || "",
          subItems: this.initializeSubItemsTest(masterComp),
        };
      }

      if (savedComp) {
        return {
          ...masterComp,
          selected: savedComp.paramYn || "N",
          remarks: savedComp.remarks || "",
          subItems: this.initializeSubItemsTest(savedComp),
        };
      }

      return {
        ...masterComp,
        selected: "N",
        remarks: "",
        subItems: this.initializeSubItemsTest(masterComp),
      };
    });
  }

  private loadCurrentManagementData(management: LiverManagementDto[]) {
    this.currentManagement = this.currentManagement.map((mgmt, index) => {
      const updatedSubList = mgmt.subList.map((sub) => {
        const savedItem = management.find(
          (s) => s.paramId === mgmt.id && s.subId === sub.id
        );
        if (savedItem) {
          return {
            ...sub,
            isSelected: true,
            dose1: savedItem.dose ? savedItem.dose : null, // Check if savedItem.dose is not null
            dose2: savedItem.frequency ? savedItem.frequency : null, // Check if savedItem.frequency is not null
            date: savedItem.doneDate ? new Date(savedItem.doneDate) : null,
            comments: savedItem.remarks ? savedItem.remarks : null,
          };
        }
        return sub;
      });

      // Update the accordion state based on whether any sub-items are selected
      const isAnySubItemSelected = updatedSubList.some((sub) => sub.isSelected);
      this.openAccordion[index] = isAnySubItemSelected;

      return {
        ...mgmt,
        subList: updatedSubList,
      };
    });
  }

  // private loadCurrentManagementData(management: LiverManagementDto[]) {
  //   this.currentManagement = this.currentManagement.map((mgmt) => ({
  //     ...mgmt,
  //     subList: mgmt.subList.map((sub) => {
  //       const savedItem = management.find(
  //         (s) => s.paramId === mgmt.id && s.subId === sub.id
  //       );
  //       if (savedItem) {
  //         return {
  //           ...sub,
  //           isSelected: true,
  //           dose1: savedItem.dose ? savedItem.dose : null, // Check if savedItem.dose is not null
  //           dose2: savedItem.frequency ? savedItem.frequency : null, // Check if savedItem.frequency is not null
  //           date: savedItem.doneDate ? new Date(savedItem.doneDate) : null,
  //           comments: savedItem.remarks ? savedItem.remarks : null,
  //         };
  //       }
  //       return sub;
  //     }),
  //   }));
  // }

  private loadTransplantIndicationsData(
    indications: LiverTransplantIndicationDto[]
  ) {
    this.liverTransIndications = this.liverTransIndications.map(
      (indication) => {
        const savedIndication = indications.find(
          (s) => s.paramId === indication.id
        );
        if (savedIndication) {
          return {
            ...indication,
            checked: savedIndication.paramYn === "Y",
            remarks: savedIndication.remarks || "",
          };
        }
        return indication;
      }
    );
  }

  private initializeSubItemsTest(masterComp: any): SubItem[] {
    const subItems: SubItem[] = [];

    if (masterComp.subList) {
      for (let i = 0; i < masterComp.subList.length; i++) {
        subItems.push({
          remarks: "",
          checked: false,
        });
      }
    }

    while (subItems.length < 10) {
      subItems.push({
        remarks: "",
        checked: false,
      });
    }

    return subItems;
  }

  onChange(e, id) {
    // hepatocellular
    if (id == 24 && e.target.value == 1) {
      this.hepatocellular = true;
    } else if (id == 24 && e.target.value == 0) {
      this.hepatocellular = false;
    }

    // locoTherapy
    if (id == 102 && e.target.value == 1) {
      this.locoTherapy = true;
    } else if (id == 102 && e.target.value == 0) {
      this.locoTherapy = false;
    }
  }

  updateDeathDetails() {
    Swal.fire({
      //title: 'Death details',
      text: "Do you want to add death details?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes",
    }).then((result) => {
      if (!result.dismiss) {
        this.alive = false;
      }
    });
  }

  private initializeLiverRegister(): void {
    this.liverRegister = {
      liverId: null,
      centralRegNo: null,
      familyHistoryYn: null,
      childPughScore: null,
      childPughClass: null,
      meldScore: null,
      meldNaScore: null,
      meldExceptionYn: null,
      meldExceptionDetails: null,
      peldScore: null,
      peldExceptionYn: null,
      peldExceptionDetails: null,
      dialysisYn: null,
    };

    // this.liverCirrhosisComplications=[];
    // this.currentManagement = [];
    // this.liverTransIndications = [];
    this.procedureDetails = [];
  }

  clear() {

    this.showDownloadButton = false;
    this.showVaccineButton  = false;

    this.patientDetails.clear();

    this.labTestFg = [];

    this.labnewList = [];

    this.contacts = [];
    this.famHistory = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });

    this.familyHistoryForm = this.formBuilder.group({
      rgTbFamilyHistory: this.formBuilder.array([]),
    });

    this.contactDetailsForm = this.formBuilder.group({
      rgTbContactDetails: this.formBuilder.array([]),
    });

    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();
    this.Vaccination.clear();
    this._sharedService.setNavigationData(null);
    this.ngOnInit();
  }

  createLabGrpItem(createLabItem: any): FormGroup {
    return this.formBuilder.group(createLabItem);
  }

  createLabItem(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      testDate: testDate,
      releasedDate: releasedDate,
      profileTestCode: profileTestCode,
      profileTestName: profileTestName,
      mohTestCode: mohTestCode,
      componentTestName: componentTestName,
      unit: unit,
      value: value,
      instCode: instCode,
      instName: instName,
      enteredBy: enteredBy,
      enteredDate: enteredDate,
      source: source,
      isEditable: isEditable,
    };
  }
  onRowEditSave(row: any) {
    let rowIndex = this.labnewList.indexOf(row);
    this.labnewList[rowIndex] =
      this.labTestForm.value.rgTbLabTestInfo[rowIndex];
    let data = this.labnewList[rowIndex];

    if (!data.mohTestCode || !data.profileTestCode || !data.instCode || !data.testDate) {
      Swal.fire("Please fill all required fields.");
      data.isEditable = true;
      return;
    }

    data.componentTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.mohTestCode)
      .map((s) => s.testName)[0];
    data.instName = this.institutes
      .filter((s) => s.estCode == data.instCode)
      .map((s) => s.estName)[0];
    data.profileTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.profileTestCode)
      .map((s) => s.testName)[0];
    data.isEditable = false;
  }

  toggleAccordion(index: number) {
    // Close all accordions
    this.openAccordion = this.openAccordion.map((_, i) =>
      i === index ? !this.openAccordion[i] : false
    );
  }

  //   toggleAccordion(index: number) {
  //     this.openAccordion[index] = !this.openAccordion[index];
  // }

  getLabFromNehr(data: any = 0) {
    data.forEach((el) => {
      // console.log(el, "el")
      let date = "";

      let componentTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[7])
        .map((s) => s.testName)[0];
      let instName = this.institutes
        .filter((s) => s.estCode == el[19])
        .map((s) => s.estName)[0];
      let profileTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[3])
        .map((s) => s.testName)[0];

      this.addNewLabList(
        null,
        el[1],
        el[5],
        el[3],
        profileTestName,
        el[7],
        componentTestName,
        el[9],
        el[10],
        el[19],
        instName,
        this.loginId,
        date,
        "S",
        false
      );
    });
  }

  // deletelab(row: any) {
  //   Swal.fire({
  //     title: "Are you sure?",
  //     text: "You won't be able to revert this!",
  //     icon: "warning",
  //     showCancelButton: true,
  //     confirmButtonColor: "#3085d6",
  //     cancelButtonColor: "#d33",
  //     confirmButtonText: "Yes, delete it!",
  //   }).then((result) => {
  //     if (!result.dismiss) {
  //       this.rgTbLabTestInfo = this.labTestForm.get(
  //         "rgTbLabTestInfo"
  //       ) as FormArray;
  //       this.delRow = this.labnewList.indexOf(row);
  //       this.labnewList.splice(this.delRow, 1);
  //       this.rgTbLabTestInfo.removeAt(this.delRow);
  //     }
  //   });
  // }

  deletelab(row: any) {
  Swal.fire({
    title: "Are you sure?",
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, delete it!"
  }).then((result) => {
    if (!result.dismiss) {
      // Remove from form array
      this.rgTbLabTestInfo = this.labTestForm.get("rgTbLabTestInfo") as FormArray;
      this.delRow = this.labnewList.indexOf(row);
      
      // Remove from both full list and current page list
      const fullListIndex = this._fullLabnewList.findIndex(item => 
        item.runId === row.runId && 
        item.testDate === row.testDate && 
        item.mohTestCode === row.mohTestCode
      );
      
      if (fullListIndex > -1) {
        this._fullLabnewList.splice(fullListIndex, 1);
      }
      this.labnewList.splice(this.delRow, 1);
      this.rgTbLabTestInfo.removeAt(this.delRow);

      // Update total records
      this.totalLabRecords = this._fullLabnewList.length;

      // If current page is empty and there are more records, load previous page
      if (this.labnewList.length === 0 && this.totalLabRecords > 0) {
        const currentPage = Math.floor(this.labTestPaginator.first / this.paginationSize);
        const newPage = Math.max(currentPage - 1, 0);
        const newFirst = newPage * this.paginationSize;
        
        // Update paginator
        this.labTestPaginator.first = newFirst;
        this.onLabPageChange({
          first: newFirst,
          rows: this.paginationSize
        });
      }
    }
  });
}

  onRowEditInit(row: any) {
    row.isEditable = true;
  }

  callFetchLabDataFromAlShifa() {
    let profileList = [];
    if (this.filterModelForm.controls["profileT"].value != null) {
      this.filterModelForm.controls["profileT"].value.forEach((element) => {
        profileList.push(element.mohTestCode);
      });
    }

    let body = {
      civiLId: this.patientDetails.patientForm.controls["civilId"].value,
      fromDate: this.filterModelForm.controls["fromDate"].value,
      toDate: this.filterModelForm.controls["toDate"].value,
      profile_list: profileList,
    };
    //this.patientDetails.patientForm.controls["civilId"].value;
    this._masterService.getNehrLabTest(body).subscribe((response) => {
      this.getLabFromNehr(response.result);
    });
  }

  addNewlab() {
    this.addNewLabList(
      null,
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      this.loginId,
      "",
      "W",
      true
    );
  }

  addNewLabList(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ): void {
    this.rgTbLabTestInfo = this.labTestForm.get("rgTbLabTestInfo") as FormArray;

    this.labnewList = Object.assign([], this.rgTbLabTestInfo.value);
    const labItem: any = this.createLabItem(
      runId,
      testDate,
      releasedDate,
      profileTestCode,
      profileTestName,
      mohTestCode,
      componentTestName,
      value,
      unit,
      instCode,
      instName,
      enteredBy,
      enteredDate,
      source,
      isEditable
    );
    this.rgTbLabTestInfo.push(this.createLabGrpItem(labItem));

    this.labnewList.push(labItem);
    this.labnewList.slice(0, this.paginationSize);

    this.initializeLabPagination();
  }

  initRgTbFamilyHistory(
    name: any,
    relation: any,
    patientID: any,
    instID: any
  ): FormGroup {
    return this.fb.group({
      name: [name],
      relation: [relation],
      patientID: [patientID],
      instID: [instID],
    });
  }

  initRgTbContactDetails(
    name: any,
    relation: any,
    phone: any,
    email: any
  ): FormGroup {
    return this.fb.group({
      name: [name, [Validators.required]],
      relation: [relation],
      phone: [phone, [this.custom_phoneValidator]], // Default to false, can be changed when editing
      email: [email, [this.custom_emailValidator]],
    });
  }

  addNewContact(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewContactList(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewFamilyList1(
    name: any,
    relation: any,
    patientID: any,
    instID: any,
    formArr: any
  ): void {
    this.rgTbFamilyHistory = formArr.get("rgTbFamilyHistory") as FormArray;
    this.rgTbFamilyHistory.push(
      this.initRgTbFamilyHistory(name, relation, patientID, instID)
    );
  }

  removeRgTbContact(i: number) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbContactRow(control);
    }
  }

  removeAllRgTbContact(i: any) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    control.controls = [];
  }

  // Method to add a new contact row
  addRgTbContactRow(formArr: any) {
    this.addNewContactList("", "", "", "", formArr);
  }

  removeRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbFamilyHistoryRow(control);
    }
  }
  removeAllRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    control.controls = [];
  }

  addRgTbFamilyHistoryRow(formArr: any) {
    this.addNewFamilyList1(null, null, null, null, formArr);
  }

  getRelationName(relation) {
    if (this.relation != null && relation) {
      return this.relation
        .filter((s) => s.relationCode == relation)
        .map((s) => s.relationName)[0];
    }
  }

  getRgTbFamilyHistory(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbFamilyHistory.value;
  }

  getRgTbContactDetails(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbContactDetails.value;
  }

  getInstName(instID) {
    if (instID) {
      return this.institutes
        .filter((s) => s.estCode == instID)
        .map((s) => s.estName)[0];
    }
  }

  onRowEditInitFH(row: any) {
    row.isEditable = true;
    //this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }

  onRowEditSaveFH(row: any) {
    let rowIndex = this.famHistory.indexOf(row);
    this.famHistory[rowIndex] =
      this.familyHistoryForm.value.rgTbFamilyHistory[rowIndex];
    let data = this.famHistory[rowIndex];

    if (!data.name || !data.relation) {
      Swal.fire("Please fill name and relation.");
      data.isEditable = true;
      return;
    }

    data.instName = this.institutes
      .filter((s) => s.estCode == data.instID)
      .map((s) => s.estName);
    data.relationName = this.relation
      .filter((s) => s.relationCode == data.relation)
      .map((s) => s.relationName)[0];
    //data.surgeryDt = moment(data.surgeryDt, "DD-MM-YYYY").format();
    data.isEditable = false;
  }

  onRowEditInitContact(row: any) {
    row.isEditable = true; // Set the row to editable
  }

  onRowEditSaveContact(row: any) {
    let rowIndex = this.contacts.indexOf(row);
    const contactFormGroup = (
      this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray
    ).at(rowIndex);
    this.contacts[rowIndex] =
      this.contactDetailsForm.value.rgTbContactDetails[rowIndex];

    let data = this.contacts[rowIndex];

    let isValid = true;

    // Clear previous errors
    contactFormGroup.get("email").setErrors(null);
    contactFormGroup.get("phone").setErrors(null);

    ///console.log(data.email);
    if (data.email && !this.emailValidator(data.email)) {
      contactFormGroup.get("email").setErrors({ invalidEmail: true }); // Set custom error
      contactFormGroup.get("email").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (data.phone && !this.phoneValidator(data.phone)) {
      contactFormGroup.get("phone").setErrors({ invalidPhone: true });
      contactFormGroup.get("phone").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (isValid) {
      data.relationName = this.relation
        .filter((s) => s.relationCode == data.relation)
        .map((s) => s.relationName)[0];
      data.isEditable = false;
    } else {
      contactFormGroup.markAllAsTouched();
      console.log("Invalid email or phone number");
      return false;
    }
  }

  getNameControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("name");
  }

  isContactValid(row: any): boolean {
    let rowIndex = this.contacts.indexOf(row);
    const emailControl = this.getEmailControl(rowIndex);
    const phoneControl = this.getPhoneControl(rowIndex);
    const nameControl = (
      this.contactDetailsForm.get("rgTbContactDetails") as FormArray
    )
      .at(rowIndex)
      .get("name");

    // Check if all controls are valid
    return emailControl.valid && phoneControl.valid && nameControl.valid;
  }

  getPhoneControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("phone");
  }

  getEmailControl(rowIndex: number) {
    //console.log("called--"+rowIndex);
    return (this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray)
      .at(rowIndex)
      .get("email");
  }

  onAddNewFH() {
    this.addFamilyIstory("", "", "", "", "", "W", false);
    this.famHistory[this.famHistory.length - 1].isEditable = true; //editable last entering row
  }

  addFamilyIstory(
    runId: any,
    name: any,
    relation: any,
    patientId: any,
    instId: any,
    source: any,
    isEditable: any = false
  ): void {
    this.rgTbFamilyHistory = this.familyHistoryForm.get(
      "rgTbFamilyHistory"
    ) as FormArray;

    this.famHistory = Object.assign([], this.rgTbFamilyHistory.value);
    const familyHistItem: any = this.createFHItem(
      runId,
      name,
      relation,
      patientId,
      instId,
      source,
      isEditable
    );
    this.rgTbFamilyHistory.push(this.createFHGrpItem(familyHistItem));

    this.famHistory.push(familyHistItem);
    //this.famHistory[this.famHistory.length - 1].isEditable = true;
  }

  // Method to remove empty or invalid contacts
  removeEmptyContacts() {
    this.contactDetailsArray.controls =
      this.contactDetailsArray.controls.filter((control) => {
        const contact = control.value;
        return (
          contact.name &&
          contact.relation &&
          contact.phone &&
          contact.email &&
          contact.name.trim() !== "" &&
          contact.relation.trim() !== "" &&
          contact.phone.trim() !== "" &&
          contact.email.trim() !== ""
        );
      });
  }

  addContactDetails(
    runId: any,
    name: any,
    relation: any,
    phone: any,
    email: any,
    isEditable: any = false
  ): void {
    this.contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;

    const contactItem: any = this.createContactItem(
      runId,
      name,
      relation,
      phone,
      email,
      isEditable
    );

    this.contactDetailsArray.push(this.createContactGroup(contactItem));

    this.contacts.push(contactItem);
  }

  createContactGroup(contactItem: any): FormGroup {
    return this.fb.group({
      runId: [contactItem.runId || ""], // Use the value from contactItem or default to empty
      name: [contactItem.name || "", [Validators.required]], // Required validator
      relation: [contactItem.relation || ""], // Default value or can be left empty
      phone: [contactItem.phone || "", [this.custom_phoneValidator]], // Custom validator
      email: [contactItem.email || "", [this.custom_emailValidator]], // Custom validator
    });
  }

  // createContactGroup(contactItem: any): FormGroup {
  //   return this.fb.group(contactItem);
  // }

  //   createContactGroup(): FormGroup {
  //     return this.fb.group({
  //         runId: [''],  // Default value or can be left empty
  //         name: ['', [Validators.required]],  // Required validator
  //         relation: [''],  // Default value or can be left empty
  //         phone: ['', [this.custom_phoneValidator]],  // Custom validator
  //         email: ['', [this.custom_emailValidator]]  // Custom validator
  //     });
  // }

  // Helper method to create a contact item
  createContactItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    phone: any = null,
    email: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      phone: phone,
      email: email,
      isEditable: isEditable,
    };
  }

  createFHGrpItem(familyHistItem: any): FormGroup {
    return this.fb.group(familyHistItem);
  }

  createFHItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    patientId: any = null,
    instId: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      patientID: patientId,
      instID: instId,
      source: source,
      isEditable: isEditable,
    };
  }

  addNewFamilyList() {
    if (!this.rgTbFamilyHistory) {
      this.rgTbFamilyHistory = [];
    }
    this.rgTbFamilyHistory.push({
      name: "",
      relation: "",
      patientID: "",
      instID: "",
    });
    this.rgTbFamilyHistory[this.rgTbFamilyHistory.length - 1].isEditable = true;
  }

  get rgTbFamilyHistoryArray() {
    return this.familyHistoryForm.controls["rgTbFamilyHistory"] as FormArray;
  }

  get rgTbContactDetails() {
    return this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray;
  }

  deleteContact(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        this.contactDetailsArray = this.contactDetailsForm.get(
          "rgTbContactDetails"
        ) as FormArray;

        this.delRow = this.contacts.indexOf(row);
        this.contacts.splice(this.delRow, 1);

        this.contactDetailsArray.removeAt(this.delRow);

        //this.contactDetailsForm.value.rgTbContactDetails;

        this.contactDetailsForm
          .get("rgTbContactDetails")
          .setValue(this.contactDetailsArray.value);
        //this.contactDetailsForm.get('rgTbContactDetails') as FormArray).setValue(this.contactDetailsArray.value);
      }
    });
  }

  delete(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.rgTbFamilyHistory = this.familyHistoryForm.get(
          "rgTbFamilyHistory"
        ) as FormArray;
        this.delRow = this.famHistory.indexOf(row);
        this.famHistory.splice(this.delRow, 1);
        this.rgTbFamilyHistory.removeAt(this.delRow);
      }
    });
  }

  clearForm() {
    //this.caseDetailsForm.reset();

    this.showDownloadButton = false;
    this.showVaccineButton  = false;

    this.patientDetails.clear();
    this.Vaccination.clear();

    this.famHistory.forEach((element) => {
      this.rgTbFamilyHistoryArray.removeAt(0);
    });
    this.famHistory = [];

    this.contacts.forEach((element) => {
      this.contactDetailsArray.removeAt(0);
    });
    this.contacts = [];
    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();

    this.labTestFg = [];
    this.labTestForm.reset();
    this.labnewList = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
  }

  navigateToRegister() {
    this._sharedService.setNavigationData({
      centralRegNo: this.patientDetails.f.centralRegNo.value,
    });
    this._router.navigate(["liver/liverRegister"], {
      state: { centralRegNo: this.patientDetails.f.centralRegNo.value },
    });
  }

  navigateToDashboard() {
    this._router.navigateByUrl("liver/liverRegister");
  }

  resetForm() {
    this.liverRegisterForm.reset();
    this.initializeForm();
  }

  validateNumberInput(event: any): boolean {
    const value = event.target.value;

    // Allow backspace and delete
    if (event.key === "Backspace" || event.key === "Delete") {
      return true;
    }

    // Allow only numbers, decimal point, and negative sign
    if (!/^-?\d*\.?\d*$/.test(value + event.key)) {
      event.preventDefault();
      return false;
    }

    // Check for maximum 10 digits before decimal and 2 after
    const parts = (value + event.key).split(".");
    if (parts[0] && parts[0].replace("-", "").length > 10) {
      event.preventDefault();
      return false;
    }
    if (parts[1] && parts[1].length > 2) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  openModal(downloadLabTest) {
    this.modalService.open(downloadLabTest);
  }

  print() {
    window.print();
  }

  generatePDF_old() {
    this.loaderService.show();
    const data = document.getElementById("case-details-form");
    const registrationId = this.patientDetails.f.centralRegNo.value;
    html2canvas(data).then((canvas) => {
      const imgWidth = 208;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      const heightLeft = imgHeight;

      const contentDataURL = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      const position = 0;
      pdf.text('Liver Case Details', imgWidth / 2, 10, { align: 'center' });
      pdf.addImage(contentDataURL, "PNG", 0, position, imgWidth, imgHeight);
      pdf.save(`${registrationId}_case_details.pdf`);
      this.loaderService.hide();
    });
  }

  generatePDF() {
  
      // add loader
      this.loaderService.show();
  
       const oldPageSize = this.pageSize;
  
    // Step 2: Show all records in the DOM
      this.pageSize = this.totalLabRecords;
    
      const registrationId = this.patientDetails.f.centralRegNo.value;
      const element = document.getElementById('case-details-form');
  
      html2canvas(element, { scale: 2 }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        
        const imgProps = (pdf as any).getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
  
        let heightLeft = pdfHeight;
        let position = 20;
  
        pdf.text('Liver Case Details', pdfWidth / 2, 10, { align: 'center' });
        pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
        heightLeft -= pdf.internal.pageSize.getHeight();
  
        while (heightLeft > 0) {
          position = heightLeft - pdfHeight;
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
          heightLeft -= pdf.internal.pageSize.getHeight();
        }
  
        pdf.save(`${registrationId}_case_details.pdf`);
        this.pageSize = oldPageSize;
        this.loaderService.hide();
      });
  
      
    }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  // loadMedicalsProc(result: any){
  //  console.log(result);
  //     // Clear existing procedures
  //     this.procedureDetails = [];

  //     // Load the procedures
  //     result.forEach((proc) => {
  //       this.procedureDetails.push({
  //         runId: proc.patientId,
  //         procId: proc.procedureId,
  //         doneDate: proc.reportDate ? new Date(proc.reportDate) : null,
  //         remarks: proc.report || "",
  //       });
  //     });

  //     console.log('Procedure Details--',this.procedureDetails);

  //     // Update selected procedures
  //     this.selectedProcedures = this.procedureDetails.map(
  //       (proc) => proc.procId
  //     );

  //     // Make sure procedures are loaded
  //     if (!this.procedures.length) {
  //       this.loadProcedures();
  //     }

  // }

  downloadMedicalProcedures() {
    //this.currentCivilId = 6732772;
    //this.estCode = 20068;
    if (this.estCode && this.currentCivilId) {
      this._LiverService
        .fetchAllDonorProcedureFromShifa(this.estCode, this.currentCivilId)
        .subscribe({
          next: (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbLabTestsDB: any = res["result"];

                //const existingCount = this.procedureDetails ? this.procedureDetails.length : 0;

                // Load medical procedures (will append to existing list)
                this.loadMedicalsProc(rgTbLabTestsDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          error: (error) => {
            console.error("Error loading procedures from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  loadMedicalsProc(result: any) {
    if (!result || result.length === 0) {
      console.log("No medical procedures data to load");
      return;
    }

    console.log("Loading medical procedures:", result);

    console.log("Procedure Details--", this.procedureDetails);
    console.log("Procedures--", this.procedures);

    // If procedures aren't loaded yet, load them first
    if (!this.procedures || this.procedures.length === 0) {
      this._LiverService.getLiverProcedure().subscribe({
        next: (response) => {
          if (response && response.result) {
            this.procedures = response.result;
            // Now process the procedures with the master data loaded
            this.processMedicalProcedures(result);
          }
        },
        error: (error) => {
          console.error("Error loading procedures:", error);
          Swal.fire({
            title: "Error",
            text: "Failed to load procedure master data",
            icon: "error",
          });
        },
      });
    } else {
      // Procedures already loaded, process directly
      this.processMedicalProcedures(result);
    }
  }

  private processMedicalProcedures(result: any) {
    // Make sure we have the existing procedures array
    if (!this.procedureDetails) {
      this.procedureDetails = [];
    }

    // Get valid procedure IDs from master data
    const validProcedureIds = this.procedures.map((p) => p.paramId);

    // Filter procedures that exist in our master data
    const validProcedures = result.filter((proc) =>
      validProcedureIds.includes(proc.procedureId)
    );

    if (validProcedures.length === 0) {
      Swal.fire({
        title: "Warning",
        text: "No matching procedures found in the system",
        icon: "warning",
      });
      return;
    }

    // Count of newly added procedures
    let newlyAddedCount = 0;

    console.log("validProcedures--", validProcedures);
    console.log("procedures--", this.procedures);

    // Add all procedures from API to the existing list
    validProcedures.forEach((proc) => {
      // Map the procedure data from AlShifa to our format
      this.procedureDetails.push({
        runId: proc.patientId || null,
        procId: proc.procedureId,
        doneDate: proc.reportDate ? new Date(proc.reportDate) : null,
        remarks: proc.report || "",
      });

      newlyAddedCount++;
    });

    console.log(
      "Procedure Details after adding new ones:",
      this.procedureDetails
    );

    // Update selected procedures to include all procedures
    this.selectedProcedures = this.procedureDetails.map((proc) => proc.procId);

    // Update unique procedure types
    //this.updateUniqueProcedureTypes();

    if (!this.procedures.length) {
      this.loadProcedures();
    }

    // Show success message
    if (newlyAddedCount > 0) {
      Swal.fire({
        title: "Success",
        text: `${newlyAddedCount} Medical procedures added successfully`,
        icon: "success",
        timer: 2000,
        showConfirmButton: false,
      });
    } else {
      Swal.fire({
        title: "Information",
        text: "No new procedures found to add",
        icon: "info",
        timer: 2000,
        showConfirmButton: false,
      });
    }
  }
  // Helper method to update unique procedure types
  private updateUniqueProcedureTypes() {
    // Get unique procedure types from the loaded procedures
    const procedureIds = this.procedureDetails.map((p) => p.procId);

    // Filter procedures to only include those that are in the procedureDetails
    const matchedProcedures = this.procedures.filter((p) =>
      procedureIds.includes(p.paramId)
    );

    // Extract unique procedure names
    this.uniqueProcedureTypes = [
      ...new Set(matchedProcedures.map((p) => p.paramName)),
    ];

    console.log("Updated unique procedure types:", this.uniqueProcedureTypes);
  }
onLabPageChange(event: any) {
  const startIndex = event.first;
  const endIndex = startIndex + event.rows;
  
  // Make sure we have the full list stored
  if (!this._fullLabnewList || this._fullLabnewList.length === 0) {
    this._fullLabnewList = [...this.labnewList];
  }
  
  // Update the displayed list based on pagination
  this.labnewList = this._fullLabnewList.slice(startIndex, endIndex);
}

  loadLabData() {
    // Your existing code to fetch lab data

    // After setting this.labnewList, add:
    this._fullLabnewList = [...this.labnewList]; // Store full list
    this.totalLabRecords = this._fullLabnewList.length;

    // Initialize with first page
    if (this.labTestPaginator) {
      this.labTestPaginator.first = 0;
    }
    this.onLabPageChange({ first: 0, rows: this.paginationSize });
  }

  initializeLabPagination() {
  // Store the full list
  this._fullLabnewList = [...this.labnewList];
  this.totalLabRecords = this._fullLabnewList.length;
  
  // Only show first page (10 records)
  this.labnewList = this._fullLabnewList.slice(0, this.paginationSize);
  
  // Reset paginator to first page if it exists
  if (this.labTestPaginator) {
    this.labTestPaginator.first = 0;
  }
}

onDownloadVaccination() {
     this.estCode = "20068";
    // this.civilId = this.donorForm.value.civilId;
    // console.log("estCode--" + this.estCode + "civilId--" + this.civilId);
    //this.Vaccination.callFetchDataFromAlShifa(true);

    if (this.estCode && this.currentCivilId) {
      this.fetchAllDonorVaccineFromAlShifa();
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  fetchAllDonorVaccineFromAlShifa() {
      // Check if estCode and civilId are not null
      if (this.estCode && this.currentCivilId) {
        this._LiverService
          .fetchAllDonorVaccineFromShifa(this.estCode, this.currentCivilId)
          .subscribe(
            (res) => {
              if (res["code"] == "S0000") {
                if (res["result"] != null) {
                  const rgTbVaccinationInfoDb: any =
                    res["result"];
                  for (let vaccinaList of rgTbVaccinationInfoDb) {
                    this.Vaccination.addNewVaccine(
                      vaccinaList.runId,
                      vaccinaList.enteredBy,
                      this._sharedService.setDateFormat(
                        vaccinaList.vaccinationDate
                      ),
                      vaccinaList.vaccineCode,
                      vaccinaList.vaccinatedInst,
                      vaccinaList.remarks,
                      vaccinaList.civilId,
                      vaccinaList.source,
                      false
                    );
                  }
                } else {
                  Swal.fire("No vaccination records found.", "info");
                }
              } else {
                Swal.fire(res["message"]);
              }
            },
            (error) => {
              Swal.fire(
                "Error",
                "Error occurred while fetching vaccination records: " +
                  error.message,
                "error"
              );
            }
          );
      } else {
        Swal.fire(
          "Error",
          "Please enter civilId before fetching data.",
          "warning"
        );
      }
    }

}
