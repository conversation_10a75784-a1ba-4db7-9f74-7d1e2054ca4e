<div class="row">
  <div class="input-group col-md-2 pull-left col-lg-6 col-md-6 col-sm-6" style="padding: 10px;">
    <h6 class="page-title">Liver Recipient Follow Up</h6>
  </div>
  <div class="col-lg-3 col-md-3 col-sm-3"></div>
  <div class="input-group col-md-2 pull-right col-lg-3 col-md-3 col-sm-3" style="padding: 10px;">
    <input type="text" placeholder="Search Registration No." [formControl]="regIdControl" class="form-control input-sm" (keyup.enter)="search()" />
    <span class="input-group-btn">
      <button class="btn btn-default btn-sm" id="search" (click)="search()">
        <i class="fa fa-search"></i>
      </button>
    </span>
  </div>
</div>

<div class="accordion register">
  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
    <ngb-panel id="patientDetails" id="ngb-panel-0">
      <ng-template ngbPanelHeader let-opened="opened">
        <div class="d-flex align-items-center justify-content-between card-head" [ngClass]="opened ? 'opened' : 'collapsed'">
          <h6> Patient Details</h6>
          <button ngbPanelToggle class="btn btn-link p-0">
            <i class="fas" [ngClass]="opened ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
          </button>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <app-patient-details [submitted]="submitted" #patientDetails></app-patient-details>
      </ng-template>
    </ngb-panel>
  </ngb-accordion>
</div>

<div class="content-wrapper mb-2">
  <ag-grid-angular style="width: 100%; height: 300px;" class="ag-theme-balham" [rowData]="rowData" [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)" [gridOptions]="gridOptions">
  </ag-grid-angular>
  <div *ngIf="rowData && rowData.length > 0">
    <p-paginator [rows]="paginationSize" [totalRecords]="totalRecords" (onPageChange)="getLiverTransplantFollowupList(this.regId, $event)" showCurrentPageReport="true" currentPageReportTemplate="Total: {{totalRecords}} records" [rowsPerPageOptions]="[10, 20, 30]">
    </p-paginator>
  </div>
</div>

<form [formGroup]="transplantFollowUpForm">
  <div class="content-wrapper">
    <div class="card-header"><strong>Add FollowUp Details</strong></div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label>FollowUp Date</label>
            <p-calendar formControlName="fwUpDate" monthNavigator="true" yearNavigator="true" placeholder="dd-mm-yyyy" showButtonBar="true">
            </p-calendar>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Complications</label>
            <ng-multiselect-dropdown formControlName="rgTbOrganTransCompList" [placeholder]="'Add Complication'" [data]="compList" [settings]="dropdownSettings">
            </ng-multiselect-dropdown>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Medicines For</label>
            <ng-multiselect-dropdown formControlName="rgTbOrganTransMedList" [placeholder]="'Add Medicines For'" [data]="medicines" [settings]="dropdownSettings">
            </ng-multiselect-dropdown>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Disease Recurrence</label>
            <ng-multiselect-dropdown formControlName="rgTbOrganTransDiseaseList" [placeholder]="'Add Disease Recurrence'" [data]="disease" [settings]="dropdownSettings">
            </ng-multiselect-dropdown>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Is Death?</label>
            <div>
              <div class="form-check form-check-inline">
                <input type="radio" class="form-check-input" id="deathYes" formControlName="deathYn"  value="true" />
                <label class="form-check-label" for="deathYes">Yes</label>
              </div>
              <div class="form-check form-check-inline">
                <input type="radio" class="form-check-input" id="deathNo" formControlName="deathYn" value="false" />
                <label class="form-check-label" for="deathNo">No</label>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3" *ngIf="transplantFollowUpForm.get('deathYn').value === 'true'">
          <div class="form-group">
            <label>Cause of death</label>
            <!-- <input type="text" class="form-control form-control-sm" formControlName="causeOfDeath" /> -->

            <ng-select [items]="icdDeathList" [virtualScroll]="true" placeholder="Select" bindLabel="disease"
              bindValue="icd" formControlName="causeOfDeath">
              <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.disease }}</ng-template>
            </ng-select>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Is Transferred?</label>
            <div>
              <div class="form-check form-check-inline">
                <input type="radio" class="form-check-input" id="transferredYes" formControlName="transferredYn" value="true"/>
                <label class="form-check-label" for="transferredYes">Yes</label>
              </div>
              <div class="form-check form-check-inline">
                <input type="radio" class="form-check-input" id="transferredNo" formControlName="transferredYn" value="false" />
                <label class="form-check-label" for="transferredNo">No</label>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3" *ngIf="transplantFollowUpForm.get('transferredYn').value === 'true'">
          <div class="form-group">
            <label>Transferred To</label>
              <ng-select [items]="instituteListFilter" [virtualScroll]="true" placeholder="Select" bindLabel="estName"
                bindValue="estCode" formControlName="transferredTo">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}</ng-template>
              </ng-select>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Remarks</label>
            <input type="text" class="form-control form-control-sm" formControlName="remarks" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="btn-container">
    <button class="btn btn-sm btn-primary" (click)="resetForm()">Add</button>
    <button class="btn btn-sm btn-secondary" (click)="clear()">Clear</button>
    <button class="btn btn-sm btn-primary" (click)="saveFollowUp()">Save</button>
  </div>
</form>

