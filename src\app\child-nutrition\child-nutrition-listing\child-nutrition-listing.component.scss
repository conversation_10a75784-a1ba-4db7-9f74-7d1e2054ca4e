.label-block label {

  display: block;
}




.sp label {

  padding-right: 61px;
  display: inline-block;
  position: relative;
}


.chkbox-outer {

  input[type=radio],
  input[type=checkbox] {
    margin-left: 0 !important;
    position: relative;
    margin-right: 10px;


  }

}

.resize {
  padding-left: 0 !important;

}


#moh {
  padding-right: 27px;
}


#mof {
  padding-right: 35px;
}

#mop {
  padding-right: 37px;
}

// min-width:1145


@media (max-width:1300px) {
  .resize {
    display: block !important;
  }

  .chkbox-outer {
    margin-left: 20px;
    display: block !important;
  }
}

// .ui-widget-header .ui-button, .ui-widget-content .ui-button, .ui-widget.ui-button, .ui-button {
//   // border-color: #ced4da !important;
//   background: #ac1c1c !important;
//   // padding: 3px 0px 4px;
// }

//   .ui-widget-header .ui-button, .ui-widget-content .ui-button, .ui-widget.ui-button, .ui-button {
//     border-color: #ced4da !important;
//     background: #973633 !important;
//     padding: 3px 0px 4px;
// }
.dia-yes-sel {
  color: green;
}

.dia-no-sel {
  color: red;
}

.dia-null-sel {
  color: gray;
}

.diarrhea-btn {
  border: 1px solid #ccc;
  margin: 0;
}

.diarrhea-btn.active {
  color: white;

  &.dia-yes-sel {
    background: green;
  }

  &.dia-no-sel {
    background: red;
  }

  &.dia-null-sel {
    background: gray;
  }
}

.oed-yes-sel {
  color: green;
}

.oed-no-sel {
  color: red;
}

.oed-null-sel {
  color: gray;
}

.oedema-btn {
  border: 1px solid #ccc;
  margin: 0;
}

.oedema-btn.active {
  color: white;

  &.oed-yes-sel {
    background: green;
  }

  &.oed-no-sel {
    background: red;
  }

  &.oed-null-sel {
    background: gray;
  }
}

.space-left {
  margin-left: 10px;
}