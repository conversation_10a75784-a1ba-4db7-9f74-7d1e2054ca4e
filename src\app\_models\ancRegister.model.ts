export class AncRegisterModel {

    public pregnancyId: number= null;
    public instPregnancyId: number= null;
    public civilId: number= null;
    public centralRegNo: number= null;
    public gravida: number= null;
    public parity: number= null;
    public aportion: number= null;
    public ectopicPreg: number= null;
    public mensturalCycle: number= null;
    public mensturalLength: String= null;
    public mensturalDuration: String= null;
    public ancNo: String= null;
    public ancInstitute: number= null;
    public lmp: Date= null;
    public eddCalc: Date= null;
    public eddScan: Date= null;
    public eddCorrected: Date= null;
    public congAnamoliesYn: string= null;
    public statusInLastPregnancy: number= null;
    public lastDeliveryDate: Date= null;
    public lastAbortionDate: Date= null;
    public requestedInstRemarks: string= null;
    public requestedInst: number= null;
    public educationLevel: number= null;
    public consanguinity: string= null;
    public earlyUsFindings: string= null;
    public earlyUsRemarks: string= null;
    public pregnancyType: string= null;
    public birthInterval: number= null;
    public contraMethodUsedYn: string= null;
    public plannedPregnancy: string= null;
    public riskFactorsYn: string= null;
    public riskFactorsRemarks: string= null;
    public height: number= null;
    public weight: number= null;
    public bmi: number= null;
    public vteRiskScore: number= null;
    public anaemic: string= null;
    public riskGrading: string= null;
    public abortionYn: string= null;
    public createdDate: Date= null;
    public createdBy: string= null;
    public modifiedDate: Date= null;
    public modifiedBy: number= null;

}