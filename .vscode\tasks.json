{"version": "2.0.0", "tasks": [{"type": "npm", "script": "start", "isBackground": true, "presentation": {"focus": true, "panel": "dedicated"}, "group": {"kind": "build", "isDefault": true}, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^([^\\s].*)\\((\\d+,\\d+)\\):\\s+(error|warning|info)\\s+(TS\\d+)\\s*:\\s*(.*)$", "file": 1, "location": 2, "severity": 3, "code": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*$", "endsPattern": "Compiled |Failed to compile."}}}]}