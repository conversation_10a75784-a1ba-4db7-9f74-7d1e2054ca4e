import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs-compat';
import * as AppUtils from '../common/app.utils';


@Injectable({
  providedIn: 'root'
})
export class DiabeticService {

  constructor(private _http: HttpClient) { }




  // diabetic listing
  getDiabeticListing(data): Observable<any> {
    return this._http.post(AppUtils.SEARCH_DIABETIC_REGISTRY, data);
  }


  //DIABETIC SAVING
  saveDiabetic(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_DIABETIC_REGISTRY, data);
  }
//DIABETIC VISIT SAVING
  saveVisitDiabetic(data): Observable<any> {
    return this._http.post(AppUtils.SAVE_VISIT_DIABETIC_REGISTRY, data);
  }

  getDiabeticRegistryByCivilId(civilId): Observable<any> {

    return this._http.get(AppUtils.GET_DIAB_Registry_BY_CIVIL_ID,{
      params: new HttpParams().set("civilId", civilId)
      });
    
    }
  

  getDiabetic(regNo) {
    
    return this._http.get(AppUtils.GET_DIABETIC_REGISTRY, {
      params: new HttpParams().set("centralRegNo", regNo)
    })
  }

  getDashboardByYear(regYear): Observable<any> {
    return this._http.get(AppUtils.DIABETIC_DASHBOARD_BY_REG_YEAR, { params: new HttpParams().set("regYear", regYear) })
  }

  getMinMaxYear(): Observable<any> {
    return this._http.get(AppUtils.DIABETIC_GET_MIN_MAX_YEAR);
  }
  
  
}

