import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NationalityDataModel } from 'src/app/common/objectModels/nationality-model';
import { OrganDonorDashboard } from 'src/app/_models/organ-donr-dashboard';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { OrganDonorService } from '../organ-donors.service';
import * as AppCompUtils from '../../../common/app.component-utils';
import { Parameter } from 'src/app/common/objectModels/parameter-model';

@Component({
  selector: 'app-organ-donor-dashboard',
  templateUrl: './organ-donor-dashboard.component.html',
  styleUrls: ['./organ-donor-dashboard.component.scss']
})
export class OrganDonorDashboardComponent implements OnInit {
  boardForm: FormGroup;
  organDonorForm: FormGroup;
  natData: NationalityDataModel[];
  sexList: [];
  DashboardDataDB: OrganDonorDashboard[];
  DashboardDataFilter: OrganDonorDashboard[];
  gender = AppCompUtils.GENDER;
  organStatus = AppCompUtils.ORGAN_STATUS;
  filterType: any;
  afterDeathType: any;
  ageValidate: boolean;
  natCodeSelected: any;
  sexSelected: any;
  afterDeathSelected: any;

  constructor(private formBuilder: FormBuilder, private _masterService: MasterService, private _organDonorService: OrganDonorService, private _sharedService: SharedService) {
    this.callFrom();
    this.getMasterData();


  }

  ngOnInit() {
  }

  callFrom() {
    this.boardForm = this.formBuilder.group({
      'natCode': [null],
      'sex': [null],
      'afterDeath': [null],
      'ageF': [null],
      'ageT': [null],
    });

    
    this.organDonorForm = this.formBuilder.group({

      totalFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      kidneyFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      lungsFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      heartFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      liverFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      corneatFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),

      pancreasFormGroup: this.formBuilder.group({
        'total': [null],
        'omani': [null],
        'nonOmani': [null],
        'male': [null],
        'female': [null],
        'afterDeath': [null],
      }),
    })

  }

  getMasterData(regCode: any = 0, walCode: any = 0) {

    this._masterService.getNationalityList(0).subscribe(res => {
      this.natData = res.result;
    })


  }
  isEmptyNullZero(val) {
    return (val === undefined || val == null || val === "null" || val <= 0) ? false : true;
  }
  /* ------------  filter action   ---------------- */
  locSelect(event: any, field?: any) {
    let body = this.boardForm.value;

    this.callFilter();

  }

  /* ------------  filter action   ---------------- */
  getDashboardData() {

    this._organDonorService.getDashboard().subscribe(res => {
      // this.DashboardDataDB = res.result;


      if (res['code'] == "S0000" || res['code'] == "F0000") {

        let tmpList = [];
        // if (curUser['roles'].filter(s => s.name == 'EREGISTRY_ADMIN').length > 0 || curUser['roles'].filter(s => s.name == 'REG_ADMIN_GENETIC').length > 0) {
        //   tmpList = res['result'];
        // } else if (curUser['roles'].filter(s => s.name == 'REG_REGION_USER_GENETIC').length > 0) {
        //   res['result'].forEach(element => {
        //     if (this.institeList.filter(s => s.regCode == element.regCode).length > 0) {
        //       tmpList.push(element);
        //     }
        //   });
        // } else if (curUser['roles'].filter(s => s.name == 'REG_EST_USER_GENETIC').length > 0) {
        //   res['result'].forEach(element => {
        //     if (this.institeList.filter(s => s.estCode == element.estCode).length > 0) {
        //       tmpList.push(element);
        //     }
        //   });
        // }

        tmpList = res['result'];
        this.DashboardDataDB = tmpList;
        for (var i = 0; i < this.DashboardDataDB.length; i++) {
          this.DashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.DashboardDataDB[i].dob);
        }
      }

      this.callFilter();
    })
  }
  callFilter() {

    if (this.DashboardDataDB == undefined) {
      this.getDashboardData();
    } else {
      let body = this.boardForm.value;

      if (this.isEmptyNullZero(body['ageF']) === true && this.isEmptyNullZero(body['ageT']) === true && body['ageF'] < body['ageT']) {
        let tmpAgelist = [];
        for (var n = body['ageF']; n <= body['ageT']; n++) {
          let a: any[];
          a = this.DashboardDataDB.filter(s => s.age == n);
          for (var i in a) {
            tmpAgelist.push(a[i]);
          }
        }
        this.DashboardDataFilter = tmpAgelist;
      } else {
        this.DashboardDataFilter = this.DashboardDataDB;
      }

      if (body['natCode'] != undefined || body['natCode'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.natCode == body['natCode']);
        this.filterType = 'Nationality';
      } else {
        this.filterType = 'all';
      }

      if (body['sex'] != undefined || body['sex'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.sex == body['sex']);
        // this.filterType = 'Nationality';
      } else {
        // this.filterType = 'all';
      }

      if (body['afterDeath'] != undefined || body['afterDeath'] != null) {
        this.DashboardDataFilter = this.DashboardDataFilter.filter(s => s.afterDeathYn == body['afterDeath']);
        if (body['afterDeath'] == 'Y') {
          this.afterDeathType = 'Yes';
        } else if (body['afterDeath'] == 'N') {
          this.afterDeathType = 'No';
        }
        else if (body['afterDeath'] == 'D') {
          this.afterDeathType = 'Decide Later';
        }

      } else {
        this.afterDeathType = 'all';
      }


      if (body['ageF'] > body['ageT']) {
        this.ageValidate = true;
      } else {
        this.ageValidate = false;
        //   this.setChartData();
        this.setDisplayBoard();

      }

    }
  }

  setDisplayBoard() {

    
    let totalFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["totalFormGroup"];
    this.setOrganDonorStatistic(totalFormGroup, this.DashboardDataFilter);

    let kidneyFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["kidneyFormGroup"];
    this.setOrganDonorStatistic(kidneyFormGroup, this.DashboardDataFilter.filter(s => s.kidneysYn == 'Y'));

    let pancreasFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["pancreasFormGroup"];
    this.setOrganDonorStatistic(pancreasFormGroup, this.DashboardDataFilter.filter(s => s.pancreasYn == 'Y'));

    let corneatFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["corneatFormGroup"];
    this.setOrganDonorStatistic(corneatFormGroup, this.DashboardDataFilter.filter(s => s.corneasYn == 'Y'));

    let liverFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["liverFormGroup"];
    this.setOrganDonorStatistic(liverFormGroup, this.DashboardDataFilter.filter(s => s.liverYn == 'Y'));

    let heartFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["heartFormGroup"];
    this.setOrganDonorStatistic(heartFormGroup, this.DashboardDataFilter.filter(s => s.heartYn == 'Y'));

    let lungsFormGroup: FormGroup = <FormGroup>this.organDonorForm.controls["lungsFormGroup"];
    this.setOrganDonorStatistic(lungsFormGroup, this.DashboardDataFilter.filter(s => s.lungsYn == 'Y'));


  }

  setOrganDonorStatistic(displayForm: any, displayData: any) {
    let display: OrganDonorDashboard[] = displayData;
    let total = display.length;;
    let male = display.filter(s => s.sex == 'M').length;
    let Female = display.filter(s => s.sex == 'F').length;
    let omani = display.filter(s => s.natCode == 268).length;
    let nonOmani = display.filter(s => s.natCode != 268).length;
    let afterDeath = display.filter(s => s.afterDeathYn == 'Y').length;

    displayForm.setValue({ total: total, omani: omani, nonOmani: nonOmani, male: male, female: Female, afterDeath: afterDeath });

  }

}
