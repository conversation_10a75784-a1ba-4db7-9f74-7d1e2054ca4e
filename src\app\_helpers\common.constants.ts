import { AbstractControl, ValidationErrors, ValidatorFn } from "@angular/forms";

// Headers HTTP
export const HEADER_AUTHENTICATION = 'Authorization';
export const TOKEN_BEARER:string = 'Bearer'
export const STORAGE_ACCOUNT_ACCESS_TOKEN = 'access_token';

// token
export const CLIENT_ID = 'eregauth';
export const TOKEN = 'reg-token';
export const REFRESH_TOKEN = 'reg-refrresh-token'; 
export const STORAGE_ACCOUNT_EXPIRES_IN = "expires-in";


export const CUR_USER = 'currentUser';
export const REFRESH_TOKEN_FAILED = 100;
export const SYSTEM_ID = 11;


// OAuth API
export const AUTH_TOKEN_API = 'OauthServer/oauth/token';
export const REFRESH_TOKEN_API = 'OauthServer/oauth/token?grant_type=refresh_token&refresh_token=';
export const FIND_USER_BY_LOGIN_AND_SYSTEM = 'OauthServer/user/findUserByLoginAndSystem';
export const GET_ROLE: string = 'OauthServer/master/getRoles?systemId=';
export const DATE_FORMAT_CUSTOM_UPDATE = "dd-MM-yyyy";
export const OAUTH_SUCCESS_CODE = 1;
export const STORAGE_PREAUTH_ACCESS_TOKEN = "pre_auth_access_token";
export const GRACE_PERIOD_MONTHS =3;
export const AUTHENTICATE_2FA="authenticateWith2FA"
export const ISD_CODE = 968;
export const MPI_CIVIL_ID_SUCCESS_CODE = 0;
export const MAX_CLIENT_INVALID_LOGIN_ATTEMPTS = 20;
export const MAX_SERVER_INVALID_LOGIN_ATTEMPTS = 5;
export const FAILED_ATTEMPTS = "failed-login-attempts";
export const CAPTCHA_EMPTY: string = 'Captcha cannot be empty';
export const CAPTCH_INVALID: string = 'Invalid Captcha';
export const AUTH_PREFIX: string = 'Captcha is valid';





// For User Managment
export const SEARCH_USERS: string = 'OauthServer/user/searchUsers';
export const GET_USER_BY_PERSCODE: string = 'OauthServer/user/perscodeInfo?perscode=';
export const SAVE_USERS: string = 'OauthServer/user/saveUser';
export const RESET_PASSWORD:string = 'OauthServer/user/resetPwd?loginId=';


// MASTER
export const GET_REGIONS: string = 'regions';
export const GET_INSTITUTE: string = 'OauthServer/master/getInstitutes';


// Common
export const PAGINATION_SIZE = 10;
export const INDEX = 0;

//Router
export const ROUTE_SEARCH = "search";
export const ROUTE_DASHBOARD = "dashboard";

export const YEAR_RANGE_START = 1930;
export const YEAR_RANGE_END = new Date().getFullYear() + 20;
export const YEAR_RANGE = `${YEAR_RANGE_START}:${YEAR_RANGE_END}`;




// MD for Child Nut
export const malnutritionDataR = [
    {
      name: "Weight for Height/Length",
      formCName: 'whZscore',
      formCLevel: 'whLevel',
      level: [{ item: 'Obese', value: 'O', formCLevel: 'whLevel' }, { item: 'Overweight', value: 'V', formCLevel: 'whLevel' }, { item: 'Moderate Wasting', value: 'M', formCLevel: 'whLevel' }, { item: 'Severe Wasting', value: 'S', formCLevel: 'whLevel' }],
      color: [{ name: 'pink-color', value: 'O', formCLevel: 'whLevel' }, { name: 'ylw-color', value: 'V', formCLevel: 'whLevel' }, { name: 'orng-color', value: 'M', formCLevel: 'whLevel' }, { name: 'red-color', value: 'S', formCLevel: 'whLevel' }],
      zId: [{gender:'F', id:29, dayFactor: 'M', ageF:0, ageT:2}, {gender:'M', id:30, dayFactor: 'M', ageF:0, ageT:2}, {gender:'F', id:31, dayFactor: 'M', ageF:2, ageT:5}, {gender:'M', id:32, dayFactor: 'M', ageF:2, ageT:5}]
    },
    {
      name: "Height/Length for Age",
      formCName: 'haZscore',
      formCLevel: 'haLevel',
      level: [{ item: 'Severely Giant', value: 'I', formCLevel: 'haLevel' }, { item: 'Moderate Giant', value: 'G', formCLevel: 'haLevel' }, { item: 'Moderate Stunting', value: 'M', formCLevel: 'haLevel' }, { item: 'Severe Stunting', value: 'S', formCLevel: 'haLevel' }],
      color: [{ name: 'pink-color', value: 'I', formCLevel: 'haLevel' }, { name: 'ylw-color', value: 'G', formCLevel: 'haLevel' }, { name: 'orng-color', value: 'M', formCLevel: 'haLevel' }, { name: 'red-color', value: 'S', formCLevel: 'haLevel' }],
      zId: [{gender:'F', id:25, dayFactor: 'D', ageF:0, ageT:5}, {gender:'M', id:26, dayFactor: 'D', ageF:0, ageT:5}]
    },
    {
      name: "Weight for Age",
      formCName: 'waZscore',
      formCLevel: 'waLevel',
      level: [{ item: 'Moderate Underweight', value: 'M', formCLevel: 'waLevel' }, { item: 'Severe Underweight', value: 'S', formCLevel: 'waLevel' }],
      color: [{ name: 'orng-color', value: 'M', formCLevel: 'waLevel' }, { name: 'red-color', value: 'S', formCLevel: 'waLevel' }],
      zId: [{gender:'F', id:27, dayFactor: 'D', ageF:0, ageT:5}, {gender:'M', id:28, dayFactor: 'D', ageF:0, ageT:5}]
    },
    {
      name: "Head Circumference for Age",
      formCName: 'hcaZscore',
      formCLevel: 'hcaLevel',
      level: [{ item: 'Microcephaly', value: 'I', formCLevel: 'hcaLevel' }, { item: 'Macrocephaly', value: 'A', formCLevel: 'hcaLevel' }],
      color: [{ name: 'ylw-color', value: 'I', formCLevel: 'hcaLevel' }, { name: 'orng-color', value: 'A', formCLevel: 'hcaLevel' }],
      zId: [{gender:'F', id:35, dayFactor: 'D', ageF:0, ageT:5}, {gender:'M', id:36, dayFactor: 'D', ageF:0, ageT:5}]
    }
  ];

  export const malnutritionDataF  = [
    {
      name: "Weight for Height/Length",
      formCName: 'whZscore',
      formCLevel: 'whLevel',
      progress: 'whProgress',
      progressDes:'whProgressDes',
      level: [{ item: 'Obese', value: 'O', formCLevel: 'whLevel' }, { item: 'Overweight', value: 'V', formCLevel: 'whLevel' }, { item: 'Normal', value: 'N', formCLevel: 'whLevel' }, { item: 'Moderate Wasting', value: 'M', formCLevel: 'whLevel' }, { item: 'Severe Wasting', value: 'S', formCLevel: 'whLevel' }],
      color: [{ name: 'pink-color', value: 'O', formCLevel: 'whLevel' }, { name: 'ylw-color', value: 'V', formCLevel: 'whLevel' }, { name: 'grn-color', value: 'N', formCLevel: 'whLevel' }, { name: 'orng-color', value: 'M', formCLevel: 'whLevel' }, { name: 'red-color', value: 'S', formCLevel: 'whLevel' }],
    },
    {
      name: "Height/Length for Age",
      formCName: 'haZscore',
      formCLevel: 'haLevel',
      progress: 'haProgress',
      progressDes:'haProgressDes',
      level: [{ item: 'Severely Giant', value: 'I', formCLevel: 'haLevel' }, { item: 'Moderate Giant', value: 'G', formCLevel: 'haLevel' },{ item: 'Normal', value: 'N', formCLevel: 'haLevel' }, { item: 'Moderate Stunting', value: 'M', formCLevel: 'haLevel' }, { item: 'Severe Stunting', value: 'S', formCLevel: 'haLevel' }],
      color: [{ name: 'pink-color', value: 'I', formCLevel: 'haLevel' }, { name: 'ylw-color', value: 'G', formCLevel: 'haLevel' }, { name: 'grn-color', value: 'N', formCLevel: 'haLevel' }, { name: 'orng-color', value: 'M', formCLevel: 'haLevel' }, { name: 'red-color', value: 'S', formCLevel: 'haLevel' }],

    },
    {
      name: "Weight for Age",
      formCName: 'waZscore',
      formCLevel: 'waLevel',
      progress: 'waProgress',
      progressDes:'waProgressDes',
      level: [{ item: 'Normal', value: 'N', formCLevel: 'waLevel' }, { item: 'Moderate Underweight', value: 'M', formCLevel: 'waLevel' }, { item: 'Severe Underweight', value: 'S', formCLevel: 'waLevel' }],
      color: [{ name: 'grn-color', value: 'N', formCLevel: 'waLevel' }, { name: 'orng-color', value: 'M', formCLevel: 'waLevel' }, { name: 'red-color', value: 'S', formCLevel: 'waLevel' }],
    },
    {
      name: "Head Circumference for Age",
      formCName: 'hcaZscore',
      formCLevel: 'hcaLevel',
      progress: 'hcaProgress',
      progressDes:'hcaProgressDes',
      level: [{ item: 'Normal', value: 'N', formCLevel: 'hcaLevel' }, { item: 'Microcephaly', value: 'I', formCLevel: 'hcaLevel' }, { item: 'Macrocephaly', value: 'A', formCLevel: 'hcaLevel' }],
      color: [{ name: 'grn-color', value: 'N', formCLevel: 'hcaLevel' }, { name: 'ylw-color', value: 'I', formCLevel: 'hcaLevel' }, { name: 'orng-color', value: 'A', formCLevel: 'hcaLevel' }],

    }
  ];



  export const CFA_PERIOD  = [
    {
      name: "(0-5 Months)",
      value: 1
    },
    {
      name: "(6-24 Months)",
      value: 2
    },
    {
      name: "(25-60 Months)",
      value: 3
    },
  ];
export const ageRangeValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const ageF = control.get('ageF');
  const ageT = control.get('ageT');

  if (ageF && ageT && ageF.value !== null && ageT.value !== null && ageF.value > ageT.value) {
    return { ageRange: true };
  }

  return null;
};