<h6>Liver Donor Listing</h6>

<div class="content-wrapper mb-2">
    <form [formGroup]="searchForm">
        <div class="row">


            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Civil ID</label>
                    <input type="number" class="form-control form-control-sm" formControlName="civilId">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control form-control-sm" formControlName="fullname">
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Gender</label>
                    <select class="form-control form-control-sm" formControlName="sex">
                        <option value =''> Select</option>
                        <option value='M'>Male</option>
                        <option value='F'>Female</option>
                    </select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Nationality</label>
                    <select class="form-control form-control-sm" formControlName="nationality">
                        <option selected [value]="null">All Nationality</option>
                        <option [value]="res.natCode" *ngFor="let res of nationListFilter">{{res.nationality}}</option>
                    </select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Date of Birth</label>
                    <p-calendar dateFormat="dd-mm-yy" formControlName="dob" monthNavigator="true" [maxDate]=today
                        yearRange="1930:2030" yearNavigator="true" showButtonBar="true">
                    </p-calendar>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Blood Group</label>
                    <select class="form-control form-control-sm" formControlName="bloodGroup">
                        <option value =''> Select</option>
                        <option value='A +'>A +</option>
                        <option value='A -'>A -</option>
                        <option value='B -'>B -</option>
                        <option value='B +'>B +</option>
                        <option value='O +'>O +</option>
                        <option value='O -'>O -</option>
                        <option value='AB +'>AB +</option>
                        <option value='AB -'>AB -</option>
                    </select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Telephone Number</label>
                    <input type="text" class="form-control form-control-sm" formControlName="telNo">
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Address</label>
                    <textarea class="form-control form-control-sm" rows="1" formControlName="address"></textarea>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Donating Hospital Name (In deceased donor)</label>
                    <ng-select #entryPoint [items]="institutes" [virtualScroll]="true" placeholder="Select"
                        bindLabel="estName" bindValue="estCode" formControlName="instCode">
                        <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.estName }}
                        </ng-template>
                    </ng-select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4">
                <div class="form-group">
                    <label>Donor Hospital File No.</label>
                    <input type="text" class="form-control form-control-sm" formControlName="instPatientId">
                </div>
            </div>

         
            <div class="text-right col-lg-12 col-md-12 col-sm-12">
                <div class="btn-box">
                    <button type="reset" (click)="clear($event)"class="btn btn-sm btn-primary">Clear</button>
                    <button type="submit" (click)="getList()" class="btn btn-sm btn-primary">Search</button>
                </div>
            </div>


        </div>
    </form>
</div>



<div style="margin-top:20px">
    <ag-grid-angular
    style="width: 100%; height: 300px;"
    class="ag-theme-balham"
    [rowData]="rowData"
    [columnDefs]="columnDefs" (rowDoubleClicked)="onCellDoubleClicked($event)"
    [gridOptions]="gridOptions"
    >
</ag-grid-angular>

</div>