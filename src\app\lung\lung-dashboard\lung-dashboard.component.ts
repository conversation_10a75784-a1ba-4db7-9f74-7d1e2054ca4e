import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';
import { RegionDataModel } from '../../common/objectModels/region-model';
import { WallayatDataModel } from '../../common/objectModels/wallayat-model';
import { MasterService } from '../../_services/master.service';
import * as AppComponent from '../../common/app.component-utils';
import { SharedService } from '../../_services/shared.service';
import * as _ from "lodash";
import { LungService } from '../lung.service';
import { LungDashboard, LungDashboardDisplay } from 'src/app/_models/lung-dashboard.model';
import { ageRangeValidator } from 'src/app/_helpers/common.constants';



@Component({
  selector: 'app-lung-dashboard',
  templateUrl: './lung-dashboard.component.html',
  styleUrls: ['./lung-dashboard.component.scss'],
  providers: [LungService]
})
export class LungDashboardComponent implements OnInit {
  boardForm: FormGroup;
  regionData: RegionDataModel[] = [];
  wallayatList: WallayatDataModel[] = [];
  wallayatListFilter: WallayatDataModel[] = [];
  institeList: any[] = [];
  institeListFilter: any[] = [];

  dashboardDataDB: LungDashboard[] = [];
  dashboardDataFilter: LungDashboard[] = [];

  filterType: 'institute' | 'wilayat' | 'region' | 'all' = 'all';
  filterTitle: string = 'All Regions';
  pieOption: any;
  options: any;
  charBGColor: string[];
  createTime: string | null = null;

  icdPieChart: any;
  transplantPieChart: any;
  urgentTransplantPieChart: any;
  icdLungShortList: any;
  lungTransIndications: any;
  indicationChart: any;

  constructor(
    private _masterService: MasterService, 
    private _sharedService: SharedService, 
    private _lungService: LungService
  ) {
    this.initForm();
    this.getMasterData();

    this.options = AppComponent.CHAR_GENERAL_OPTION;
    this.pieOption = AppComponent.CHAR_PIE_OPTION;
    this.charBGColor = AppComponent.CHAR_BG_COLOR;

    this.charBGColor.sort(() => Math.random() - 0.5);
  }

  ngOnInit(): void {}

  private initForm(): void {
    this.boardForm = new FormGroup({
      regCode: new FormControl(null),
      walCode: new FormControl(null),
      estCode: new FormControl(null),
      ageF: new FormControl(null),
      ageT: new FormControl(null),
    }, { validators: ageRangeValidator });
  }

  private getMasterData(regCode: any = 0, walCode: any = 0): void {
    this._masterService.getRegionsList().subscribe(response => {
      this.regionData = response.result;
    });

    this._masterService.getWilayatList(regCode).subscribe(response => {
      this.wallayatList = response.result;
      this.wallayatListFilter = this.wallayatList;
    });

    this._masterService.getInstiteList(regCode, walCode).subscribe(response => {
      this.institeList = response.result;
      this.institeListFilter = this.institeList;
    });

    this._masterService.getIcdLungShortList();
    this._masterService.icdLungShortList.subscribe(value => {
      this.icdLungShortList = value;
    });

    this._masterService.getLungTransplantMast();
    this._masterService.lungTransplantMastList.subscribe((value) => {
      this.lungTransIndications = _.cloneDeep(value);
    });
  }

  private isEmptyNullZero(val: any): boolean {
    return !(val === undefined || val === null || val === 'null' || val <= 0);
  }

  private getCodeValue(val: any, key: string): any {
    if (val && typeof val === 'object') {
      return val[key];
    }
    return val;
  }

  locSelect(event: any, field?: 'region' | 'wilayat'): void {
    const body = this.boardForm.value;
    
    if (field === 'region') {
      if (!event) {
        this.wallayatListFilter = this.wallayatList;
        this.institeListFilter = this.institeList;
        this.boardForm.patchValue({ walCode: null, estCode: null });
      } else {
        this.wallayatListFilter = this.wallayatList.filter(s => s.regCode === event.regCode);
        this.institeListFilter = this.institeList.filter(s => s.regCode === event.regCode);
        this.boardForm.patchValue({ walCode: null, estCode: null });
      }
    } else if (field === 'wilayat') {
      if (!event) {
        if (body['regCode'] != null) {
          const regCodeVal = this.getCodeValue(body['regCode'], 'regCode');
          this.institeListFilter = this.institeList.filter(s => s.regCode === regCodeVal);
        } else { 
          this.institeListFilter = this.institeList; 
        }
        this.boardForm.patchValue({ estCode: null });
      } else {
        this.institeListFilter = this.institeList.filter(s => s.walCode === event.walCode);
        this.boardForm.patchValue({ estCode: null });
      }
    }
  }

  getDashboardData(): void {
    this.createTime = this._sharedService.setDateFormatDataTime(new Date());
    this._lungService.getDashboard().subscribe(res => {
      if (res['code'] == "S0000" || res['code'] == "F0000") {
        this.dashboardDataDB = this._sharedService.filterDataByUserRole(res['result'], 'LUNG', this.institeList);

        for (let i = 0; i < this.dashboardDataDB.length; i++) {
          this.dashboardDataDB[i].age = this._sharedService.ageFromDateOfBirthday(this.dashboardDataDB[i].dob);
        }
      }
      this.applyFilters();
    });
  }

  callReset(): void {
    window.location.reload();
  }

  callFilter(): void {
    if (!this.dashboardDataDB || this.dashboardDataDB.length === 0) {
      this.getDashboardData();  
    } else {
      this.applyFilters();
    }
  }

  private applyFilters(): void {
    const body = this.boardForm.value;
    this.dashboardDataFilter = [...this.dashboardDataDB];

    this.applyLocationFilters(body);

    this.displayFilterType();
    this.callChart();
  }

  private applyLocationFilters(body: any): void {
    const {
      ageF = null,
      ageT = null,
      estCode = null,
      walCode = null,
      regCode = null
    } = body || {};
    if (this.isEmptyNullZero(ageF) && this.isEmptyNullZero(ageT) && ageF <= ageT) {
      this.dashboardDataFilter = this.dashboardDataDB.filter(s => s.age >= ageF && s.age <= ageT);
    } else {
      this.dashboardDataFilter = this.dashboardDataDB;
    }

    const estCodeVal = this.getCodeValue(estCode, 'estCode');
    const walCodeVal = this.getCodeValue(walCode, 'walCode');
    const regCodeVal = this.getCodeValue(regCode, 'regCode');

    if (estCodeVal != null && estCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.estCode) === String(estCodeVal));
      this.filterType = 'institute';
    } else if (walCodeVal != null && walCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.walCode) === String(walCodeVal));
      this.filterType = 'wilayat';
    } else if (regCodeVal != null && regCodeVal !== '') {
      this.dashboardDataFilter = this.dashboardDataFilter.filter(s => String(s.regCode) === String(regCodeVal));
      this.filterType = 'region';
    } else {
      this.filterType = 'all';
    }
  }

  displayFilterType(): void {
    const formValue = this.boardForm.value;

    switch (this.filterType) {
      case 'institute': {
        const est =
          formValue.estCode && typeof formValue.estCode === 'object'
            ? formValue.estCode
            : (this.institeList ? this.institeList.find(s => String(s.estCode) === String(formValue.estCode)) : null);
        this.filterTitle = est ? est.estName : 'Selected Institute';
        break;
      }
      case 'wilayat': {
        const wal =
          formValue.walCode && typeof formValue.walCode === 'object'
            ? formValue.walCode
            : (this.wallayatList ? this.wallayatList.find(s => String(s.walCode) === String(formValue.walCode)) : null);
        this.filterTitle = wal ? `All Institutes under ${wal.walName}` : 'Selected Wilayat';
        break;
      }
      case 'region': {
        const reg =
          formValue.regCode && typeof formValue.regCode === 'object'
            ? formValue.regCode
            : (this.regionData ? this.regionData.find(s => String(s.regCode) === String(formValue.regCode)) : null);
        this.filterTitle = reg ? `All Wilayat under ${reg.regName}` : 'Selected Region';
        break;
      }
      default:
        this.filterTitle = 'All Regions';
    }
  }
  
  callChart(): void {
    this.icdPieChart = null;
    this.transplantPieChart = null;
    this.urgentTransplantPieChart = null;
    this.indicationChart = null;

    const transplantData = this.dashboardDataFilter
      .filter(item => item != null && item.transplant != null)
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.transplant
      }));

    const urgentTransplantData = this.dashboardDataFilter
      .filter(item => item != null && item.urgentTransplantYn != null)
      .map(item => ({
        centralRegNo: item.centralRegNo,
        value: item.urgentTransplantYn
      }));

    const icdData = this.dashboardDataFilter
      .filter(item => item != null && item.icd != null && item.icd !== '')
      .map(item => {
        let icdName = item.icd;
        if (this.icdLungShortList && item.icd) {
          const icdItem = this.icdLungShortList.find(icdData => icdData.code == item.icd);
          icdName = icdItem ? icdItem.disease : item.icd;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(icdName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    const indicationData = this.dashboardDataFilter
      .filter(item => item != null && item.indication != null && item.indication !== undefined && item.indication !== 0)
      .map(item => {
        let indicationName = item.indication;
        if (this.lungTransIndications && item.indication) {
          const indicationItem = this.lungTransIndications.find(indicationData => indicationData.paramId == item.indication);
          indicationName = indicationItem ? indicationItem.paramName : item.indication;
        }
        return {
          centralRegNo: item.centralRegNo,
          value: String(indicationName)
        };
      }).filter(item => item.value != null && item.value !== '' && item.value !== 'null' && item.value !== 'undefined');

    if (transplantData && transplantData.length > 0) {
      this.callPieChart(transplantData, 'transplantPieChart');
    }
    if (urgentTransplantData && urgentTransplantData.length > 0) {
      this.callPieChart(urgentTransplantData, 'urgentTransplantPieChart');
    }
    if (icdData && icdData.length > 0) {
      this.callBarChart(icdData, 'icdPieChart', 'Y');
    }
    if (indicationData && indicationData.length > 0) {
      this.callBarChart(indicationData, 'indicationChart', 'Y');
    }
  }

  callBarChart(listData: LungDashboardDisplay[], chartData: any, withNull: 'Y' | 'N'): void {
    if (!listData || listData.length === 0) {
      return;
    }
    const includeNull = withNull === 'Y';
    const { labels: charlabels, data: charData } = this.buildCounts<LungDashboardDisplay>(
      listData,
      (x: any) => (x ? (x as any).value : null),
      includeNull,
      (val: any) => String(val)
    );

    if (charlabels.length === 0 || charData.length === 0) {
      return;
    }

  if (chartData === 'icdPieChart') {
      this.icdPieChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      }
  } else if (chartData === 'indicationChart') {
      this.indicationChart = {
        labels: charlabels,
        datasets: [
          {
            backgroundColor: this.charBGColor,
            borderColor: '#1E88E5',
            data: charData,
          }
        ]
      }
    }
  } 

  callPieChart(listData: LungDashboardDisplay[], chartData: any): void {
    if (!listData || listData.length === 0) {
      return;
    }

    const { labels: charlabels, data: charData } = this.buildCounts<LungDashboardDisplay>(
      listData,
  (x: any) => (x ? (x as any).value : null),
      false,
      (val: any) => (val === 'Y' ? 'Yes' : 'No')
    );
    if (charlabels.length === 0 || charData.length === 0) return;


    const chartConfig = {
      labels: charlabels,
      datasets: [
        {
          backgroundColor: this.charBGColor,
          hoverBackgroundColor: this.charBGColor,
          data: charData,
        }
      ]
    };

    if (chartData === 'transplantPieChart') {
      this.transplantPieChart = chartConfig;
    } else if (chartData === 'urgentTransplantPieChart') {
      this.urgentTransplantPieChart = chartConfig;
    }
  }

  private buildCounts<T>(
    items: T[],
    getValue: (item: T) => any,
    includeNull: boolean,
    labelFormatter?: (value: any) => string
  ): { labels: string[]; data: number[] } {
    const counts = new Map<any, number>();
    for (const it of items) {
      const raw = getValue(it);
      if (!includeNull && (raw === null || raw === undefined)) continue;
      const key = raw;
      counts.set(key, (counts.get(key) || 0) + 1);
    }
    const labels: string[] = [];
    const data: number[] = [];
    for (const [key, count] of counts.entries()) {
      labels.push(labelFormatter ? labelFormatter(key) : String(key));
      data.push(count);
    }
    return { labels, data };
  }
}