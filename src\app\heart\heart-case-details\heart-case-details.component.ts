// Angular imports
import { Component, OnInit, ViewChild, Input } from "@angular/core";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { AbstractControl } from "@angular/forms";
import { LoaderService } from "src/app/_services/loader.service";
import {
  FormArray,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";

// Third-party imports
import { GridOptions } from "ag-grid-community";
import Swal from "sweetalert2";
import * as _ from "lodash";
import { NgbModal, ModalDismissReasons } from "@ng-bootstrap/ng-bootstrap";
import { IDropdownSettings } from "ng-multiselect-dropdown";

// Application imports
import { MasterService } from "src/app/_services/master.service";
import { SharedService } from "src/app/_services/shared.service";
import { HeartService } from "../heart.service";
import { HeartCaseDetailService } from "./heart-case-details.service";
import * as AppUtils from "../../common/app.utils";
import * as CommonConstants from "../../_helpers/common.constants";
import { VaccinationComponent } from "../../_comments/vaccination/vaccination.component";
import { PatientDetailsComponent } from "../../_comments/patient-details/patient-details.component";
import {
  Medication,
  HeartRegister,
  HeartInotropeDtls,
  HeartMcsDtls,
  RgVwLiverProceduresDto,
  RgProcedureDetailsSaveDto,
  RgProcedureDetailsDto,
} from "src/app/_models/heart-casedetails-model";
import { Paginator } from "primeng/paginator";
import { Observable } from "rxjs";
import { MedicationComponent } from "src/app/_comments/medication/medication.component";
import { surgeryComponent } from "src/app/_comments/surgery/surgery.component";

@Component({
  selector: "app-heart-case-details",
  templateUrl: "./heart-case-details.component.html",
  styleUrls: ["./heart-case-details.component.scss"],
  providers: [HeartCaseDetailService, HeartService],
})
export class HeartCaseDetailsComponent implements OnInit {
  @ViewChild("labTestPaginator", { static: false }) labTestPaginator: Paginator;
  totalLabRecords: number = 0;
  paginationSize: number = AppUtils.E_REGISTRY_PAGINATION_SIZE;
  filteredLabnewList: any[] = [];
  showButton: boolean = false;
  showDownloadButton: boolean = false;
  showVaccineButton: boolean = false;
  @ViewChild("Vaccination", { static: false })
  Vaccination: VaccinationComponent;
  showCommentBox: boolean = false;
  showCommentBox_peld: boolean = false;
  contactDetailsForm: FormGroup;
  contactDetails: any[] = []; // Replace with your actual data type
  procedures: RgVwLiverProceduresDto[] = [];
  procedureDetails: RgProcedureDetailsSaveDto[] = [];
  uniqueProcedureTypes: string[] = [];

  physiotherapyBaseline: any[] = [];
  nyhaClassList: any[] = [];

  primaryMfEtiology: any[] = [];

  psychologicalTeamClearance: any[] = [];

  inotropeAgent: any[] = [];

  mcsDeviceUsed: any[] = [];

  financialInsuranceApproval = [
    { id: "Y", name: "Yes" },
    { id: "N", name: "No" },
  ];

  @Input() liverId: number;

  vaccNewList: any[];
  vaccName: any[];
  vaccinMastList: any;

  familyHistoryYn: number = 0;
  familyHistoryRemarks: string = "";

  page: number = 1;
  pageSize: number = 3;
  disVaccineInfoalshifa: any;
  openAccordion: boolean[] = [];
  hospitals: any;
  public rgTbFamilyHistory: any = [];
  public contactDetailsArray: any = [];
  relation: any;
  pharmacologicalNotes: string = "";

  @ViewChild("patientDetails", { static: false })
  patientDetails: PatientDetailsComponent;
  @ViewChild("surgery", { static: false }) surgery: surgeryComponent;
  dialysis = null;
  stage: any;
  transplant = null;
  readiness = null;
  willingness = null;
  preEmptiveTransplant;
  nationListFilter: any;
  nationList: any;
  dialysisType;
  selectedTransplantPlace: any;
  selectedfollowUpHospital: any;
  dropdownSettings: IDropdownSettings;
  selectedProcedures: number[] = [];
  liverRegisterForm: FormGroup;
  familyHistoryForm: FormGroup;
  contactAddressForm: FormGroup;
  @Input() caseDetailsForm: FormGroup;
  patientForm: FormGroup;
  @Input() filterModelForm: FormGroup;
  public labTestForm: FormGroup;
  centralRegNoExit: boolean = false;
  currentCivilId: any;
  patntId = "";
  loginId: any;
  today: any;
  testcomponet: any;
  profileList: any;
  ComponetList: any;
  testComponetList: any;
  rgTbLabTestInfo: any = [];
  labTestFg: any = [];
  labTest: any;
  testDone: any[];
  testListToDownload: any[];
  labnewList: any[];
  labListToFixRowSpan: any[];
  labTestName: any[];
  regId: any;
  institutes: any[];
  @Input() submitted = false;
  famHistory: any = [];
  contacts: any = [];
  isChecked: any;
  alive = true;
  isPrint = false;
  delRow;
  selectedCompYes: any;
  selectedCompNo: any;
  rowData: any[] = [];
  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
    pagination: false,
    resizable: true,
    onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    },
  };

  // Properties for lab results accordion
  labResults: any[] = [];
  openAccordionLab: boolean[] = [];

  medications: Medication[] = [];
  medicationForm: FormGroup;

  heartRegister: HeartRegister = {
    heartId: null,
    centralRegNo: null,
    familyHistYn: null,
    smokingYn: null,
    cigaretteQuitDate: null,
    substanceUseYn: null,
    substanceQuitDate: null,
    psychosocialAssessment: null,
    caregiverDtls: null,
    primaryDiag: null,
    diagOthers: null,
    duration: null,
    nyhaClass: null,
    inotropeYn: null,
    inotropeOther: null,
    mcsDeviceUsed: null,
    mcsDeviceOther: null,
    mcsInitiatedDate: null,
    recTransYn: null,
    recTransReason: null,
    urgentTransYn: null,
    urgentTransReason: null,
    psychosocialClearance: null,
    dieticianReview: null,
    physiotherapyBase: null,
    financialInsuranceYn: null,
    rgTbHeartMcsDtls: null,
    rgTbHeartInotropeDtls: null,
  };

  columnDefs = [
    { headerName: "Date", field: "date" },
    { headerName: "Profile Name", field: "profilename" },
    { headerName: "Test Component", field: "testComponent" },
    { headerName: "Result", field: "result" },
    { headerName: "Unit", field: "unit" },
    { headerName: "Institute", field: "institute" },
  ];
  estCode: any;
  private _fullLabnewList: any;
  vaccineFg: any;
  @ViewChild("Medication", { static: false }) Medication: MedicationComponent;

  civilId: any;

  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    private _masterService: MasterService,
    private _router: Router,
    private _http: HttpClient,
    private formBuilder: FormBuilder,
    private _sharedService: SharedService,
    private _caseDetaisService: HeartCaseDetailService,
    private _heartService: HeartService,
    private loaderService: LoaderService
  ) {}

  private initializeComponent(): void {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    this.loginId = curUser["person"].perscode;
    if (this._sharedService.getNavigationData()) {
      setTimeout(() => {
        this.regId = this._sharedService.getNavigationData().centralRegNo;
        //console.log("data--", this._sharedService.getNavigationData());
        this.search();
        this._sharedService.setNavigationData(null);
      }, 1000);
    }
  }

  ngAfterViewChecked() {
    this.patientDetails.patientForm.disable();
  }

  ngOnInit() {
    this.initializeForm();
    this.getMasterData();
    this.initializeComponent();
    this.getNationalityList();
    this.doDropDown();
    this.initMedicationForm();
    this.loadProcedures();
    this.loadPhysiotherapyBaseline();
    this.loadPrimaryMfEtiology();
    this.loadInotropeAgent();
    this.loadMcsDeviceUsed();
    this.loadPsychologicalTeamClearance();
    this.loadNyhaClass();
  }

  loadPrimaryMfEtiology() {
    this._heartService.getAllHeartPrimaryEtiology().subscribe((res) => {
      this.primaryMfEtiology = res["result"];
    });
  }

  loadInotropeAgent() {
    this._heartService.getAllHeartInotropes().subscribe((res) => {
      this.inotropeAgent = res["result"];
    });
  }

  loadMcsDeviceUsed() {
    this._heartService.getAllHeartMcsDevice().subscribe((res) => {
      this.mcsDeviceUsed = res["result"];
    });
  }

  loadPhysiotherapyBaseline() {
    this._heartService.getHeartPhysioBaseline().subscribe((res) => {
      this.physiotherapyBaseline = res["result"];
    });
  }

  loadPsychologicalTeamClearance() {
    this._heartService.getAllHeartPsClearance().subscribe((res) => {
      this.psychologicalTeamClearance = res["result"];
    });
  }

  loadNyhaClass() {
    this._heartService.getAllHeartNyhaClass().subscribe((res) => {
      this.nyhaClassList = res["result"];
    });
  }

  initMedicationForm() {
    this.medicationForm = this.fb.group({
      rgTbMedications: this.fb.array([]),
    });
  }

  callGenMedList() {
    this._heartService.getGeneticMedicineList().subscribe((res) => {
      this.Medication.medicine = res["result"];
    });
  }

  onAddNewMedication() {
    const medicationsArray = this.medicationForm.get(
      "rgTbMedications"
    ) as FormArray;
    medicationsArray.push(this.createMedicationFormGroup());
    this.medications.push({
      id: null,
      medicine: "",
      startDate: new Date(),
      dose: "",
      frequency: "",
      medicineType: "",
      isEditable: true,
    });
  }

  clearFamilyHistory() {
    this.famHistory = [];
    this.familyHistoryForm.reset();
  }

  createMedicationFormGroup(): FormGroup {
    return this.fb.group({
      medicine: ["", Validators.required],
      startDate: [new Date(), Validators.required],
      dose: [""],
      frequency: [""],
      medicineType: [""],
    });
  }

  onRowEditInitMedication(row: Medication) {
    row.isEditable = true;
  }

  onRowEditSaveMedication(row: Medication) {
    row.isEditable = false;
    // Add your save logic here
  }

  deleteMedication(row: Medication) {
    const index = this.medications.indexOf(row);
    if (index > -1) {
      this.medications.splice(index, 1);
      const medicationsArray = this.medicationForm.get(
        "rgTbMedications"
      ) as FormArray;
      medicationsArray.removeAt(index);
    }
  }

  doDropDown() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: "mohTestCode",
      textField: "testName",
      selectAllText: "Select All",
      unSelectAllText: "UnSelect All",
      itemsShowLimit: 6,
      allowSearchFilter: true,
    };
  }

  loadContactDetails() {
    const contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;
    this.contactDetails.forEach((contact) => {
      contactDetailsArray.push(
        this.initRgTbContactDetails(
          contact.name,
          contact.relation,
          contact.phone,
          contact.email
        )
      );
    });
  }

  createContactFormGroup(contact: any): FormGroup {
    return this.fb.group({
      runId: [contact.runId],
      name: [contact.name],
      relation: [contact.relation],
      phone: [contact.phone],
      email: [contact.email],
      isEditable: [false], // Default to false
    });
  }

  onAddNewContact() {
    this.addContactDetails("", "", "", "", "", false);
    this.contacts[this.contacts.length - 1].isEditable = true; //
  }

  isEmpty(input) {
    return input.replace(/\s/g, "") === "";
  }

  emailValidator(data) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(data);
  }

  phoneValidator(data) {
    const phoneRegex = /^\d{8,16}$/; // Example: 10-digit number
    return phoneRegex.test(data);
  }

  custom_emailValidator(control: AbstractControl) {
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    return emailRegex.test(control.value) ? null : { invalidEmail: true }; // Return null if valid
  }

  custom_phoneValidator(control: AbstractControl) {
    const phoneRegex = /^\d{8,16}$/; // Example: 10-digit number
    return phoneRegex.test(control.value) ? null : { invalidPhone: true }; // Return null if valid
  }

  disableSaveButton(row) {
    if (row.name === "") {
      return true;
    }
    return false;
  }

  createContactDetail(): FormGroup {
    return this.fb.group({
      email: ["", [Validators.email]],
      phone: ["", [this.phoneValidator]],
    });
  }

  private initializeForm() {
    this.caseDetailsForm = this.fb.group({
      heartRegister: this.fb.group({
        heartId: [null],
        centralRegNo: [null],
        familyHistYn: [null],
        smokingYn: [null],
        cigaretteQuitDate: [null],
        substanceUseYn: [null],
        substanceQuitDate: [null],
        psychosocialAssessment: [null],
        caregiverDtls: [null],
        primaryDiag: [null],
        diagOthers: [null],
        duration: [null],
        nyhaClass: [null],
        inotropeYn: [null],
        inotropeOther: [null],
        mcsDeviceUsed: [null],
        mcsDeviceOther: [null],
        mcsInitiatedDate: [null],
        recTransYn: [null],
        recTransReason: [null],
        urgentTransYn: [null],
        urgentTransReason: [null],
        psychosocialClearance: [null],
        dieticianReview: [null],
        physiotherapyBase: [null],
        financialInsuranceYn: [null],
        heartInotropeDtls: this.fb.group({
          inotropeAgentId: [[], Validators.required], // for multi-select
          remarks: [""],
        }),
        heartMcsDtls: this.fb.group({
          mcsId: [[], Validators.required], // for multi-select
          remarks: [""],
        }),
      }),
    });
    this.initializeHeartRegister();

    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
    this.familyHistoryForm = this.fb.group({
      rgTbFamilyHistory: this.fb.array([]),
    });

    this.contactDetailsForm = this.fb.group({
      rgTbContactDetails: this.fb.array([]), // Initialize as a FormArray
    });
    this.filterModelForm = this.fb.group({
      fromDate: [null],
      toDate: [null],
      profileT: [null],
    });
  }

  // Get Master Data for dropdown
  private getMasterData() {
    this._masterService.institiutes.subscribe((res) => {
      this.institutes = res["result"];
    });

    this._masterService.getLabTestList().subscribe((res) => {
      this.labTest = res.result;
    });

    this._masterService.getLabMaster().subscribe((res) => {
      this.testDone = res.result;
    });
    this._masterService.getLabTestToDownload().subscribe((res) => {
      this.testListToDownload = res["result"];
    });

    this._masterService.getLiverComplicationMast();

    this._masterService.getRelationMast().subscribe(async (res) => {
      this.relation = res["result"];
    });

    this._masterService.getDialysisHospital().subscribe((res) => {
      this.hospitals = res["result"];
    });

    this._masterService.getAllMohLabMaster().subscribe((res) => {
      this.testComponetList = res.result;
      this.profileList = [];
      this.ComponetList = [];
      this.testComponetList.forEach((element) => {
        if (
          this.profileList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.profileList.push({
            testId: element.mohTestCode,
            testName: element.testName,
          });
        }

        if (
          this.ComponetList.filter((s) => s.mohTestCode == element.mohTestCode)
            .length == 0
        ) {
          this.ComponetList.push({
            componentTestId: element.mohTestCode,
            componentTestName: element.testName,
          });
        }
      });
    });
  }

  // seach function for getting case details based on registration id
  search() {
    this.clear();
    if (this.regId) {
      this.getList(this.regId);
      this.regId = "";
    } else {
      this.regId = "";
      Swal.fire({
        icon: "warning",
        title: "Please enter Registration ID",
      });
    }
  }

  // GetList function for loading case details
  getList(regNo: any) {
    this._caseDetaisService.getCaseDetails(regNo).subscribe({
      next: (res) => {
        if (res["code"] == "S0000") {
          this.isPrint = true;
          this.centralRegNoExit = true;
          this.patientDetails.setPatientDetails(res["result"]);
          this.currentCivilId = res["result"].rgTbPatientInfo["civilId"];
          this.estCode = res["result"].regInst;
          this.patntId = res["result"].rgTbPatientInfo["patientId"];
          this.showDownloadButton = true;
          this.showVaccineButton = true;
          this.showButton = true;
          //console.log("res: " + JSON.stringify(res));

          if (res["result"].rgTbHeartRegister) {
            this.loadHeartRegisterData(res["result"].rgTbHeartRegister);
          }

          if (res["result"].rgTbFamilyHistory) {
            let rgTbFamilyHistory: any = res["result"].rgTbFamilyHistory;
            let faFamilyHistory: FormArray = <FormArray>(
              this.familyHistoryForm.controls["rgTbFamilyHistory"]
            );

            for (let famHist of rgTbFamilyHistory) {
              this.addFamilyIstory(
                famHist.runId,
                famHist.name,
                famHist.relation,
                famHist.patientID,
                famHist.instID,
                famHist.source,
                false
              );
            }
          }

          // load Medication
          if (res["result"].rgTbMedicineDtls) {
            const rgTbMedicineDtls: any = res["result"].rgTbMedicineDtls;
            for (let medList of rgTbMedicineDtls) {
              this.Medication.addNewMed(
                medList.runId,
                medList.enteredBy,
                medList.enteredDt,
                medList.medicineID,
                this._sharedService.setDateFormat(medList.startDate),
                medList.dose,
                medList.frequency,
                medList.medicineType,
                medList.source,
                false
              );
            }
          }
          /////////////// Form group surgery

          if (res["result"].rgTbSurgeryDtls) {
            const rgTbSurgeryDtlsDB: any = res["result"].rgTbSurgeryDtls;
            for (let surList of rgTbSurgeryDtlsDB) {
              this.surgery.addNewSurgery(
                surList.runId,
                surList.surgeryID,
                this._sharedService.setDateFormat(surList.surgeryDt),
                surList.remarks,
                surList.enteredBy,
                surList.enteredDt,
                surList.source,
                false
              );
            }
          }

          if (res["result"].rgAddContactDetails) {
            let rgTbContactDetails: any = res["result"].rgAddContactDetails;

            //console.log("rgTbContactDetails--1",rgTbContactDetails);

            let contactDetailsFM: FormArray = <FormArray>(
              this.contactDetailsForm.controls["rgTbContactDetails"]
            );

            for (let conts of rgTbContactDetails) {
              this.addContactDetails(
                conts.runId,
                conts.name,
                conts.relation,
                conts.phone,
                conts.email,
                false
              );
            }
          }

          if (res["result"].rgTbLabTests) {
            for (let labRList of res["result"].rgTbLabTests) {
              let componentTestName = this.testComponetList
                .filter((s) => s.mohTestCode == labRList.mohTestCode)
                .map((s) => s.testName)[0];

              let instName = this.institutes
                .filter((s) => s.estCode == labRList.instCode)
                .map((s) => s.estName)[0];
              let profileTestName = this.testComponetList
                .filter((s) => s.mohTestCode == labRList.profileTestCode)
                .map((s) => s.testName)[0];

              this.addNewLabList(
                labRList.runId,
                this._sharedService.setDateFormat(labRList.testDate),
                this._sharedService.setDateFormat(labRList.releasedDate),
                labRList.profileTestCode,
                profileTestName,
                labRList.mohTestCode,
                componentTestName,
                labRList.value,
                labRList.unit,
                labRList.instCode,
                instName,
                labRList.enteredBy,
                labRList.enteredDate,
                labRList.source,
                false
              );
            }

            this.labListToFixRowSpan = res["result"].rgTbLabTest;
          }

          if (res["result"].rgTbVaccinationInfo) {
            this.vaccineFg = res["result"].rgTbVaccinationInfo;
            const rgTbVaccinationInfoDb: any =
              res["result"].rgTbVaccinationInfo;
            for (let vaccinaList of rgTbVaccinationInfoDb) {
              this.Vaccination.addNewVaccine(
                vaccinaList.runId,
                vaccinaList.enteredBy,
                new Date(vaccinaList.vaccinationDate),
                vaccinaList.vaccineCode,
                vaccinaList.vaccinatedInst,
                vaccinaList.remarks,
                vaccinaList.civilId,
                vaccinaList.source,
                false
              );
            }
          }

          if (res["result"].liverProcedureDtos) {
            // Clear existing procedures
            this.procedureDetails = [];

            // Load the procedures
            res["result"].liverProcedureDtos.forEach((proc) => {
              this.procedureDetails.push({
                runId: proc.runId,
                procId: proc.procId,
                doneDate: proc.doneDate ? new Date(proc.doneDate) : null,
                remarks: proc.remarks || "",
              });
            });

            // Update selected procedures
            this.selectedProcedures = this.procedureDetails.map(
              (proc) => proc.procId
            );

            // Make sure procedures are loaded
            if (!this.procedures.length) {
              this.loadProcedures();
            }
          }
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      error: (error) => {
        if (error.status == 401) {
          Swal.fire(
            "Error!",
            "Error occurred while retrieving user details",
            "error"
          );
        }
      },
    });
  }

  submit() {
    if (this.caseDetailsForm.status === "VALID") {
    }
  }

  getTransplantIndicationControls() {
    return (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).controls;
  }

  addTransplantIndication() {
    const indicationForm = this.fb.group({
      checked: [false],
      value: [""],
      remarks: [""],
    });
    (
      this.liverRegisterForm.get("rgTbLiverTransplantIndication") as FormArray
    ).push(indicationForm);
  }

  formatDate(date: Date): string {
    if (!date) return "";
    if (typeof date === "string") return date;
    return date.toISOString().split("T")[0];
  }

  onRemarksChange(option: any) {
    // Validate remarks if required
    if (option.remarksYn === "Y" && option.checked && !option.remarks) {
      // Show validation message
      console.error(`Remarks are required for ${option.value}`);
    }
  }

  getNationalityList(natCode: any = 0) {
    this._masterService.getNationalityList(natCode).subscribe(
      (response) => {
        this.nationList = response.result;
        // console.log(this.nationList, "nationlist");
        this.nationListFilter = this.nationList;
      },
      (error) => {}
    );
  }

  validateLabTests(labTestsData: any[]): boolean {
    const invalidLabTests = (labTestsData || []).filter(
      (l) =>
        !l.mohTestCode ||
        !l.profileTestCode ||
        !l.instCode ||
        !l.testDate ||
        l.value === null ||
        l.value === ""
    );

    if (invalidLabTests.length > 0) {
      Swal.fire("Warning", "Lab entries are mandatory.", "warning");
      return false;
    }

    return true;
  }

  validateVaccination(vaccinationInfoData: any[]): boolean {
    const invalidVaccines = (vaccinationInfoData || []).filter(
      (v) => !v.vaccineCode || !v.vaccinationDate
    );

    if (invalidVaccines.length > 0) {
      Swal.fire(
        "Warning",
        "Vaccine Name and Date are mandatory for all entries.",
        "warning"
      );
      return false;
    }

    return true;
  }

  validateContactDetails(contactDetailsArray: any[]): boolean {
    const invalidContacts = (contactDetailsArray || []).filter(
      (c) => !c.name || !c.relation || !c.phone || !c.email
    );

    if (invalidContacts.length > 0) {
      Swal.fire(
        "Warning",
        "Contact details are mandatory for all entries.",
        "warning"
      );
      return false;
    }

    return true;
  }

  validateFamilyHistory(familyHistoryData: any[]): boolean {
    const invalidFamilyHistory = (familyHistoryData || []).filter(
      (fh) => !fh.name || !fh.relation || !fh.instID
    );

    if (invalidFamilyHistory.length > 0) {
      Swal.fire("Warning", "Family history entries are mandatory.", "warning");
      return false;
    }

    return true;
  }

  validateMedications(medicationsData: any[]): boolean {
    const invalidMedications = (medicationsData || []).filter(
      (m) =>
        !m.medicineID ||
        !m.startDate ||
        !m.dose ||
        !m.frequency ||
        !m.medicineType
    );

    if (invalidMedications.length > 0) {
      Swal.fire("Warning", "Medication entries are mandatory.", "warning");
      return false;
    }

    return true;
  }

  validateSurgery(surgeryData: any[]): boolean {
    const invalidSurgery = (surgeryData || []).filter(
      (s) => !s.surgeryID || !s.surgeryDt
    );

    if (invalidSurgery.length > 0) {
      Swal.fire("Warning", "Surgery entries are mandatory.", "warning");
      return false;
    }

    return true;
  }

  saveDetails(): void {
    let patientInfoData = this.patientDetails.patientForm.value;
    let labTestsData = this.labTestForm.value.rgTbLabTestInfo;

    if (!this.validateLabTests(labTestsData)) {
      return;
    }

    let vaccinationInfoData =
      this.Vaccination.vaccinationForm.value.rgTbVaccinationInfo;

    if (!this.validateVaccination(vaccinationInfoData)) {
      return;
    }

    const heartRegisterValue = this.caseDetailsForm.get("heartRegister").value;

    // Prepare Inotrope Agent array
    const inotropeDtls = [];
    const inotropeForm = this.caseDetailsForm.get(
      "heartRegister.heartInotropeDtls"
    ).value;
    if (
      heartRegisterValue.inotropeYn === "Y" &&
      inotropeForm.inotropeAgentId &&
      inotropeForm.inotropeAgentId.length > 0
    ) {
      inotropeForm.inotropeAgentId.forEach((id: number) => {
        inotropeDtls.push({
          transId: heartRegisterValue.transId, // or set as needed
          heartId: heartRegisterValue.heartId,
          inotropeAgentId: id,
          remarks: inotropeForm.remarks,
        });
      });
    }

    // Prepare MCS Device array
    const mcsDtls = [];
    const mcsForm = this.caseDetailsForm.get(
      "heartRegister.heartMcsDtls"
    ).value;
    if (
      heartRegisterValue.mcsDeviceUsed === "Y" &&
      mcsForm.mcsId &&
      mcsForm.mcsId.length > 0
    ) {
      mcsForm.mcsId.forEach((id: number) => {
        mcsDtls.push({
          transId: heartRegisterValue.transId, // or set as needed
          heartId: heartRegisterValue.heartId,
          mcsId: id,
          remarks: mcsForm.remarks,
        });
      });
    }

    let heartRegisterData = this.caseDetailsForm.value.heartRegister;
    const heartRegister: HeartRegister = {
      heartId: this.heartRegister.heartId || null,
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      familyHistYn: this.heartRegister.familyHistYn || null,
      smokingYn: heartRegisterData.smokingYn || null,
      cigaretteQuitDate: heartRegisterData.cigaretteQuitDate || null,
      substanceUseYn: heartRegisterData.substanceUseYn || null,
      substanceQuitDate: heartRegisterData.substanceQuitDate || null,
      psychosocialAssessment: heartRegisterData.psychosocialAssessment || null,
      caregiverDtls: heartRegisterData.caregiverDtls || null,
      primaryDiag: heartRegisterData.primaryDiag || null,
      diagOthers: heartRegisterData.diagOthers || null,
      duration: heartRegisterData.duration || null,
      nyhaClass: heartRegisterData.nyhaClass || null,
      inotropeYn: heartRegisterData.inotropeYn || null,

      inotropeOther:
        heartRegisterData.inotropeYn === "Y"
          ? heartRegisterData.inotropeOther || null
          : null,
      mcsDeviceUsed: heartRegisterData.mcsDeviceUsed || null,
      mcsDeviceOther:
        heartRegisterData.mcsDeviceUsed === "Y"
          ? heartRegisterData.mcsDeviceOther || null
          : null,
      mcsInitiatedDate:
        heartRegisterData.mcsDeviceUsed === "Y"
          ? heartRegisterData.mcsInitiatedDate || null
          : null,
      recTransYn: heartRegisterData.recTransYn || null,
      recTransReason: heartRegisterData.recTransReason || null,
      urgentTransYn: heartRegisterData.urgentTransYn || null,
      urgentTransReason: heartRegisterData.urgentTransReason || null,
      dieticianReview: heartRegisterData.dieticianReview || null,
      physiotherapyBase: heartRegisterData.physiotherapyBase || null,
      financialInsuranceYn: heartRegisterData.financialInsuranceYn || null,
      psychosocialClearance: heartRegisterData.psychosocialClearance || null,
      rgTbHeartMcsDtls: mcsDtls || null,
      rgTbHeartInotropeDtls: inotropeDtls || null,
    };

    if (heartRegister.familyHistYn == "N") {
      // clear the family history form.

      this.familyHistoryForm.value.rgTbFamilyHistory = [];

      this.familyHistoryForm.setControl("rgTbFamilyHistory", this.fb.array([]));
    }

    //console.log("lungRegister data", lungRegister);

    const contactDetailsArray =
      this.contactDetailsForm.value.rgTbContactDetails;

    if (!this.validateContactDetails(contactDetailsArray)) {
      return;
    }

    const validContactDetails = [];

    contactDetailsArray.forEach((contact) => {
      if (contact.name && contact.phone && contact.email) {
        validContactDetails.push(contact);
      }
    });

    let medicineInfoDate =
      this.Medication.medicationForm.value.rgTbMedicineDtls;

    if (!this.validateMedications(medicineInfoDate)) {
      return;
    }

    let familyHistoryData = this.familyHistoryForm.value.rgTbFamilyHistory;

    if (!this.validateFamilyHistory(familyHistoryData)) {
      return;
    }

    let surgeryData = this.surgery.surgeryForm.value.rgTbSurgeryDtls;
    if (!this.validateSurgery(surgeryData)) {
      return;
    }

    let saveData = {
      centralRegNo: this.patientDetails.f.centralRegNo.value,
      patientID: this.patientDetails.f.patientId.value,
      regInst: this.patientDetails.f.regInst.value,
      registerType: AppUtils.REG_TYPE_LUNG,
      rgTbPatientInfo: patientInfoData,
      rgTbLabTests: labTestsData,
      rgTbMedicineDtls: this.Medication.medicationForm.value.rgTbMedicineDtls,
      rgTbFamilyHistory: this.familyHistoryForm.value.rgTbFamilyHistory,
      rgTbSurgeryDtls: this.surgery.surgeryForm.value.rgTbSurgeryDtls,
      rgTbVaccinationInfo: vaccinationInfoData,
      rgAddContactDetails: validContactDetails,
      liverProcedureDtos: this.saveProcedureDetails(),
      rgTbHeartRegister: heartRegister,
    };

    //console.log(saveData);

    this._heartService.saveMainCaseDetails(saveData).subscribe(
      (res) => {
        if (res["code"] == 0) {
          Swal.fire(
            "Saved!",
            "Heart Case Details Saved Successfully.",
            "success"
          );
          this.regId = res["result"];
          //console.log("regId--", this.regId);
          this.search();
        } else if (res["code"] == "3") {
          Swal.fire("Saved!", res["message"], "error");
        } else {
          Swal.fire("Error!", res["message"], "error");
        }
      },
      (err) => {
        Swal.fire(
          "Error!",
          "Error occured while saving Liver Registry " + err.message,
          "error"
        );
      }
    );
  }

  private loadHeartRegisterData(registerData: HeartRegister) {
    if (registerData) {
      this.caseDetailsForm.patchValue({
        heartRegister: {
          ...registerData,
          heartId: registerData.heartId || null,
          familyHistYn: registerData.familyHistYn || null,
          smokingYn: registerData.smokingYn || null,
          cigaretteQuitDate: registerData.cigaretteQuitDate
            ? new Date(registerData.cigaretteQuitDate)
            : null,
          substanceUseYn: registerData.substanceUseYn || null,
          substanceQuitDate: registerData.substanceQuitDate
            ? new Date(registerData.substanceQuitDate) || null
            : null,
          psychosocialAssessment: registerData.psychosocialAssessment || null,
          caregiverDtls: registerData.caregiverDtls || null,
          primaryDiag: registerData.primaryDiag || null,
          diagOthers: registerData.diagOthers || null,
          duration: registerData.duration || null,
          nyhaClass: registerData.nyhaClass || null,
          inotropeYn: registerData.inotropeYn || null,
          inotropeOther: registerData.inotropeOther || null,
          mcsDeviceUsed: registerData.mcsDeviceUsed || null,
          mcsDeviceOther: registerData.mcsDeviceOther || null,
          mcsInitiatedDate: registerData.mcsInitiatedDate
            ? new Date(registerData.mcsInitiatedDate) || null
            : null,
          recTransYn: registerData.recTransYn || null,
          recTransReason: registerData.recTransReason || null,
          urgentTransYn: registerData.urgentTransYn || null,
          urgentTransReason: registerData.urgentTransReason || null,
          psychosocialClearance: registerData.psychosocialClearance || null,
          dieticianReview: registerData.dieticianReview || null,
          physiotherapyBase: registerData.physiotherapyBase || null,
        },
      });

      this.heartRegister = {
        ...this.heartRegister,
        ...registerData,
        heartId: registerData.heartId || null,
        familyHistYn: registerData.familyHistYn || null,
      };

      this.loadHeartMcsDtls(registerData.rgTbHeartMcsDtls);
      this.loadHeartInotropeDtls(registerData.rgTbHeartInotropeDtls);
    }
  }

  loadHeartMcsDtls(heartMcsDtls: HeartMcsDtls[]) {
    if (heartMcsDtls) {
      this.caseDetailsForm.get("heartRegister.heartMcsDtls").patchValue({
        mcsId: heartMcsDtls.map((x: any) => x.mcsId),
        remarks: heartMcsDtls.length ? heartMcsDtls[0].remarks : "",
      });
    }
  }

  loadHeartInotropeDtls(heartInotropeDtls: HeartInotropeDtls[]) {
    if (heartInotropeDtls) {
      this.caseDetailsForm.get("heartRegister.heartInotropeDtls").patchValue({
        inotropeAgentId: heartInotropeDtls.map((x: any) => x.inotropeAgentId),
        remarks: heartInotropeDtls.length ? heartInotropeDtls[0].remarks : "",
      });
    }
  }

  private initializeHeartRegister(): void {
    // Initialize lungRegister with default values

    this.heartRegister = {
      heartId: null,
      centralRegNo: null,
      familyHistYn: null,
      smokingYn: null,
      cigaretteQuitDate: null,
      substanceUseYn: null,
      substanceQuitDate: null,
      psychosocialAssessment: null,
      caregiverDtls: null,
      primaryDiag: null,
      diagOthers: null,
      duration: null,
      nyhaClass: null,
      inotropeYn: null,
      inotropeOther: null,
      mcsDeviceUsed: null,
      mcsDeviceOther: null,
      mcsInitiatedDate: null,
      recTransYn: null,
      recTransReason: null,
      urgentTransYn: null,
      urgentTransReason: null,
      psychosocialClearance: null,
      dieticianReview: null,
      physiotherapyBase: null,
      financialInsuranceYn: null,
      rgTbHeartMcsDtls: null,
      rgTbHeartInotropeDtls: null,
    };
  }

  clear() {
    this.showDownloadButton = false;
    this.showVaccineButton = false;

    this.patientDetails.clear();

    this.labTestFg = [];

    this.labnewList = [];

    this.contacts = [];
    this.famHistory = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });

    this.familyHistoryForm = this.formBuilder.group({
      rgTbFamilyHistory: this.formBuilder.array([]),
    });

    this.contactDetailsForm = this.formBuilder.group({
      rgTbContactDetails: this.formBuilder.array([]),
    });

    this.surgery.surgeryForm.reset();
    this.Medication.medicationForm.reset();
    this.procedureDetails = [];
    this.selectedProcedures = [];

    this.surgery.clear();
    this.Medication.clear();

    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();
    this.Vaccination.clear();
    this._sharedService.setNavigationData(null);
    this.ngOnInit();
  }

  createLabGrpItem(createLabItem: any): FormGroup {
    return this.formBuilder.group(createLabItem);
  }

  createLabItem(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      testDate: testDate,
      releasedDate: releasedDate,
      profileTestCode: profileTestCode,
      profileTestName: profileTestName,
      mohTestCode: mohTestCode,
      componentTestName: componentTestName,
      unit: unit,
      value: value,
      instCode: instCode,
      instName: instName,
      enteredBy: enteredBy,
      enteredDate: enteredDate,
      source: source,
      isEditable: isEditable,
    };
  }
  onRowEditSave(row: any) {
    let rowIndex = this.labnewList.indexOf(row);
    this.labnewList[rowIndex] =
      this.labTestForm.value.rgTbLabTestInfo[rowIndex];
    let data = this.labnewList[rowIndex];

    if (
      !data.mohTestCode ||
      !data.profileTestCode ||
      !data.instCode ||
      !data.testDate
    ) {
      Swal.fire("Please fill all required fields.");
      this.labnewList[rowIndex].isEditable = true;
      return;
    }

    data.componentTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.mohTestCode)
      .map((s) => s.testName)[0];
    data.instName = this.institutes
      .filter((s) => s.estCode == data.instCode)
      .map((s) => s.estName)[0];
    data.profileTestName = this.testComponetList
      .filter((s) => s.mohTestCode == data.profileTestCode)
      .map((s) => s.testName)[0];
    data.isEditable = false;
  }

  toggleAccordion(index: number) {
    // Close all accordions
    this.openAccordion = this.openAccordion.map((_, i) =>
      i === index ? !this.openAccordion[i] : false
    );
  }

  //   toggleAccordion(index: number) {
  //     this.openAccordion[index] = !this.openAccordion[index];
  // }

  getLabFromNehr(data: any = 0) {
    data.forEach((el) => {
      // console.log(el, "el")
      let date = "";

      let componentTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[7])
        .map((s) => s.testName)[0];
      let instName = this.institutes
        .filter((s) => s.estCode == el[19])
        .map((s) => s.estName)[0];
      let profileTestName = this.testComponetList
        .filter((s) => s.mohTestCode == el[3])
        .map((s) => s.testName)[0];

      this.addNewLabList(
        null,
        el[1],
        el[5],
        el[3],
        profileTestName,
        el[7],
        componentTestName,
        el[9],
        el[10],
        el[19],
        instName,
        this.loginId,
        date,
        "S",
        false
      );
    });
  }

  deletelab(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        // Remove from form array
        this.rgTbLabTestInfo = this.labTestForm.get(
          "rgTbLabTestInfo"
        ) as FormArray;
        this.delRow = this.labnewList.indexOf(row);

        // Remove from both full list and current page list
        const fullListIndex = this._fullLabnewList.findIndex(
          (item) =>
            item.runId === row.runId &&
            item.testDate === row.testDate &&
            item.mohTestCode === row.mohTestCode
        );

        if (fullListIndex > -1) {
          this._fullLabnewList.splice(fullListIndex, 1);
        }
        this.labnewList.splice(this.delRow, 1);
        this.rgTbLabTestInfo.removeAt(this.delRow);

        // Update total records
        this.totalLabRecords = this._fullLabnewList.length;

        // If current page is empty and there are more records, load previous page
        if (this.labnewList.length === 0 && this.totalLabRecords > 0) {
          const currentPage = Math.floor(
            this.labTestPaginator.first / this.paginationSize
          );
          const newPage = Math.max(currentPage - 1, 0);
          const newFirst = newPage * this.paginationSize;

          // Update paginator
          this.labTestPaginator.first = newFirst;
          this.onLabPageChange({
            first: newFirst,
            rows: this.paginationSize,
          });
        }
      }
    });
  }

  onRowEditInit(row: any) {
    row.isEditable = true;
  }

  callFetchLabDataFromAlShifa() {
    let profileList = [];
    if (this.filterModelForm.controls["profileT"].value != null) {
      this.filterModelForm.controls["profileT"].value.forEach((element) => {
        profileList.push(element.mohTestCode);
      });
    }

    let body = {
      civiLId: this.patientDetails.patientForm.controls["civilId"].value,
      fromDate: this.filterModelForm.controls["fromDate"].value,
      toDate: this.filterModelForm.controls["toDate"].value,
      profile_list: profileList,
    };
    //this.patientDetails.patientForm.controls["civilId"].value;
    this._masterService.getNehrLabTest(body).subscribe((response) => {
      if (response.result.length > 0) {
        this.getLabFromNehr(response.result);
      } else {
        Swal.fire("No Records Found");
      }
      //this.getLabFromNehr(response.result);
    });
  }

  addNewlab() {
    this.addNewLabList(
      null,
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      this.loginId,
      "",
      "W",
      true
    );
  }

  addNewLabList(
    runId: any = null,
    testDate: any = null,
    releasedDate: any = null,
    profileTestCode: any = null,
    profileTestName: any = null,
    mohTestCode: any = null,
    componentTestName: any = null,
    value: any = null,
    unit: any = null,
    instCode: any = null,
    instName: any = null,
    enteredBy: any = null,
    enteredDate: any = null,
    source: any = null,
    isEditable: any = false
  ): void {
    this.rgTbLabTestInfo = this.labTestForm.get("rgTbLabTestInfo") as FormArray;

    this.labnewList = Object.assign([], this.rgTbLabTestInfo.value);
    const labItem: any = this.createLabItem(
      runId,
      testDate,
      releasedDate,
      profileTestCode,
      profileTestName,
      mohTestCode,
      componentTestName,
      value,
      unit,
      instCode,
      instName,
      enteredBy,
      enteredDate,
      source,
      isEditable
    );
    this.rgTbLabTestInfo.push(this.createLabGrpItem(labItem));

    this.labnewList.push(labItem);
    this.labnewList.slice(0, this.paginationSize);

    this.initializeLabPagination();
  }

  initRgTbFamilyHistory(
    name: any,
    relation: any,
    patientID: any,
    instID: any
  ): FormGroup {
    return this.fb.group({
      name: [name],
      relation: [relation],
      patientID: [patientID],
      instID: [instID],
    });
  }

  initRgTbContactDetails(
    name: any,
    relation: any,
    phone: any,
    email: any
  ): FormGroup {
    return this.fb.group({
      name: [name, [Validators.required]],
      relation: [relation],
      phone: [phone, [this.custom_phoneValidator]], // Default to false, can be changed when editing
      email: [email, [this.custom_emailValidator]],
    });
  }

  addNewContact(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewContactList(
    name: any,
    relation: any,
    phone: any,
    email: any,
    formArr: any
  ): void {
    this.contactDetailsArray = formArr.get("rgTbContactDetails") as FormArray;
    this.contactDetailsArray.push(
      this.initRgTbContactDetails(name, relation, phone, email)
    );
  }

  addNewFamilyList1(
    name: any,
    relation: any,
    patientID: any,
    instID: any,
    formArr: any
  ): void {
    this.rgTbFamilyHistory = formArr.get("rgTbFamilyHistory") as FormArray;
    this.rgTbFamilyHistory.push(
      this.initRgTbFamilyHistory(name, relation, patientID, instID)
    );
  }

  removeRgTbContact(i: number) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbContactRow(control);
    }
  }

  removeAllRgTbContact(i: any) {
    const control = <FormArray>(
      this.contactDetailsForm.get("rgTbContactDetails")
    );
    control.removeAt(i);
    control.controls = [];
  }

  // Method to add a new contact row
  addRgTbContactRow(formArr: any) {
    this.addNewContactList("", "", "", "", formArr);
  }

  removeRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    if (control.length == 0) {
      this.addRgTbFamilyHistoryRow(control);
    }
  }
  removeAllRgTbFamilyHistory(i: any) {
    const control = <FormArray>this.familyHistoryForm.get("rgTbFamilyHistory");
    control.removeAt(i);
    control.controls = [];
  }

  addRgTbFamilyHistoryRow(formArr: any) {
    this.addNewFamilyList1(null, null, null, null, formArr);
  }

  getRelationName(relation) {
    if (this.relation != null && relation) {
      return this.relation
        .filter((s) => s.relationCode == relation)
        .map((s) => s.relationName)[0];
    }
  }

  getRgTbFamilyHistory(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbFamilyHistory.value;
  }

  getRgTbContactDetails(form: any) {
    // return form.controls.rgTbFamilyHistory.controls;
    return form.controls.rgTbContactDetails.value;
  }

  getInstName(instID) {
    if (instID) {
      return this.institutes
        .filter((s) => s.estCode == instID)
        .map((s) => s.estName)[0];
    }
  }

  onRowEditInitFH(row: any) {
    row.isEditable = true;
    //this.selectedDate = moment(row.entryDate).format("DD-MM-YYYY h:mm:ss");
  }

  onRowEditSaveFH(row: any) {
    let rowIndex = this.famHistory.indexOf(row);
    this.famHistory[rowIndex] =
      this.familyHistoryForm.value.rgTbFamilyHistory[rowIndex];
    let data = this.famHistory[rowIndex];

    if (!data.name || !data.relation) {
      Swal.fire("Please fill name and relation.");
      data.isEditable = true;
      return;
    }

    data.instName = this.institutes
      .filter((s) => s.estCode == data.instID)
      .map((s) => s.estName);
    data.relationName = this.relation
      .filter((s) => s.relationCode == data.relation)
      .map((s) => s.relationName)[0];
    //data.surgeryDt = moment(data.surgeryDt, "DD-MM-YYYY").format();
    data.isEditable = false;
  }

  onRowEditInitContact(row: any) {
    row.isEditable = true; // Set the row to editable
  }

  onRowEditSaveContact(row: any) {
    let rowIndex = this.contacts.indexOf(row);
    const contactFormGroup = (
      this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray
    ).at(rowIndex);
    this.contacts[rowIndex] =
      this.contactDetailsForm.value.rgTbContactDetails[rowIndex];

    let data = this.contacts[rowIndex];

    let isValid = true;

    // Clear previous errors
    contactFormGroup.get("email").setErrors(null);
    contactFormGroup.get("phone").setErrors(null);

    ///console.log(data.email);
    if (data.email && !this.emailValidator(data.email)) {
      contactFormGroup.get("email").setErrors({ invalidEmail: true }); // Set custom error
      contactFormGroup.get("email").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (data.phone && !this.phoneValidator(data.phone)) {
      contactFormGroup.get("phone").setErrors({ invalidPhone: true });
      contactFormGroup.get("phone").markAsTouched(); // Mark as touched to show error
      isValid = false;
      return false;
    }

    if (isValid) {
      data.relationName = this.relation
        .filter((s) => s.relationCode == data.relation)
        .map((s) => s.relationName)[0];
      data.isEditable = false;
    } else {
      contactFormGroup.markAllAsTouched();
      console.log("Invalid email or phone number");
      return false;
    }
  }

  getNameControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("name");
  }

  isContactValid(row: any): boolean {
    let rowIndex = this.contacts.indexOf(row);
    const emailControl = this.getEmailControl(rowIndex);
    const phoneControl = this.getPhoneControl(rowIndex);
    const nameControl = (
      this.contactDetailsForm.get("rgTbContactDetails") as FormArray
    )
      .at(rowIndex)
      .get("name");

    // Check if all controls are valid
    return emailControl.valid && phoneControl.valid && nameControl.valid;
  }

  getPhoneControl(rowIndex: number): AbstractControl {
    return (this.contactDetailsForm.get("rgTbContactDetails") as FormArray)
      .at(rowIndex)
      .get("phone");
  }

  getEmailControl(rowIndex: number) {
    //console.log("called--"+rowIndex);
    return (this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray)
      .at(rowIndex)
      .get("email");
  }

  onAddNewFH() {
    this.addFamilyIstory("", "", "", "", "", "W", false);
    this.famHistory[this.famHistory.length - 1].isEditable = true; //editable last entering row
  }

  addFamilyIstory(
    runId: any,
    name: any,
    relation: any,
    patientId: any,
    instId: any,
    source: any,
    isEditable: any = false
  ): void {
    this.rgTbFamilyHistory = this.familyHistoryForm.get(
      "rgTbFamilyHistory"
    ) as FormArray;

    this.famHistory = Object.assign([], this.rgTbFamilyHistory.value);
    const familyHistItem: any = this.createFHItem(
      runId,
      name,
      relation,
      patientId,
      instId,
      source,
      isEditable
    );
    this.rgTbFamilyHistory.push(this.createFHGrpItem(familyHistItem));

    this.famHistory.push(familyHistItem);
    //this.famHistory[this.famHistory.length - 1].isEditable = true;
  }

  // Method to remove empty or invalid contacts
  removeEmptyContacts() {
    this.contactDetailsArray.controls =
      this.contactDetailsArray.controls.filter((control) => {
        const contact = control.value;
        return (
          contact.name &&
          contact.relation &&
          contact.phone &&
          contact.email &&
          contact.name.trim() !== "" &&
          contact.relation.trim() !== "" &&
          contact.phone.trim() !== "" &&
          contact.email.trim() !== ""
        );
      });
  }

  addContactDetails(
    runId: any,
    name: any,
    relation: any,
    phone: any,
    email: any,
    isEditable: any = false
  ): void {
    this.contactDetailsArray = this.contactDetailsForm.get(
      "rgTbContactDetails"
    ) as FormArray;

    const contactItem: any = this.createContactItem(
      runId,
      name,
      relation,
      phone,
      email,
      isEditable
    );

    this.contactDetailsArray.push(this.createContactGroup(contactItem));

    this.contacts.push(contactItem);
  }

  createContactGroup(contactItem: any): FormGroup {
    return this.fb.group({
      runId: [contactItem.runId || ""], // Use the value from contactItem or default to empty
      name: [contactItem.name || "", [Validators.required]], // Required validator
      relation: [contactItem.relation || ""], // Default value or can be left empty
      phone: [contactItem.phone || "", [this.custom_phoneValidator]], // Custom validator
      email: [contactItem.email || "", [this.custom_emailValidator]], // Custom validator
    });
  }

  // createContactGroup(contactItem: any): FormGroup {
  //   return this.fb.group(contactItem);
  // }

  //   createContactGroup(): FormGroup {
  //     return this.fb.group({
  //         runId: [''],  // Default value or can be left empty
  //         name: ['', [Validators.required]],  // Required validator
  //         relation: [''],  // Default value or can be left empty
  //         phone: ['', [this.custom_phoneValidator]],  // Custom validator
  //         email: ['', [this.custom_emailValidator]]  // Custom validator
  //     });
  // }

  // Helper method to create a contact item
  createContactItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    phone: any = null,
    email: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      phone: phone,
      email: email,
      isEditable: isEditable,
    };
  }

  createFHGrpItem(familyHistItem: any): FormGroup {
    return this.fb.group(familyHistItem);
  }

  createFHItem(
    runId: any = null,
    name: any = null,
    relation: any = null,
    patientId: any = null,
    instId: any = null,
    source: any = null,
    isEditable: any = false
  ) {
    return {
      runId: runId,
      name: name,
      relation: relation,
      patientID: patientId,
      instID: instId,
      source: source,
      isEditable: isEditable,
    };
  }

  addNewFamilyList() {
    if (!this.rgTbFamilyHistory) {
      this.rgTbFamilyHistory = [];
    }
    this.rgTbFamilyHistory.push({
      name: "",
      relation: "",
      patientID: "",
      instID: "",
    });
    this.rgTbFamilyHistory[this.rgTbFamilyHistory.length - 1].isEditable = true;
  }

  get rgTbFamilyHistoryArray() {
    return this.familyHistoryForm.controls["rgTbFamilyHistory"] as FormArray;
  }

  get rgTbContactDetails() {
    return this.contactDetailsForm.controls["rgTbContactDetails"] as FormArray;
  }

  deleteContact(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        this.contactDetailsArray = this.contactDetailsForm.get(
          "rgTbContactDetails"
        ) as FormArray;

        this.delRow = this.contacts.indexOf(row);
        this.contacts.splice(this.delRow, 1);

        this.contactDetailsArray.removeAt(this.delRow);

        //this.contactDetailsForm.value.rgTbContactDetails;

        this.contactDetailsForm
          .get("rgTbContactDetails")
          .setValue(this.contactDetailsArray.value);
        //this.contactDetailsForm.get('rgTbContactDetails') as FormArray).setValue(this.contactDetailsArray.value);
      }
    });
  }

  delete(row: any) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (!result.dismiss) {
        this.rgTbFamilyHistory = this.familyHistoryForm.get(
          "rgTbFamilyHistory"
        ) as FormArray;
        this.delRow = this.famHistory.indexOf(row);
        this.famHistory.splice(this.delRow, 1);
        this.rgTbFamilyHistory.removeAt(this.delRow);
      }
    });
  }

  clearForm() {
    //this.caseDetailsForm.reset();

    this.showDownloadButton = false;
    this.showVaccineButton = false;

    this.patientDetails.clear();
    this.Vaccination.clear();

    this.famHistory.forEach((element) => {
      this.rgTbFamilyHistoryArray.removeAt(0);
    });
    this.famHistory = [];

    this.contacts.forEach((element) => {
      this.contactDetailsArray.removeAt(0);
    });
    this.contacts = [];
    this.contactDetailsForm.reset();
    this.familyHistoryForm.reset();

    this.labTestFg = [];
    this.labTestForm.reset();
    this.labnewList = [];

    this.centralRegNoExit = false;
    this.labTestForm = this.formBuilder.group({
      rgTbLabTestInfo: this.formBuilder.array([]),
    });
  }

  navigateToRegister() {
    this._sharedService.setNavigationData({
      regNo: this.patientDetails.f.centralRegNo.value,
    });
    this._router.navigate(["heart/heartRegister"], {
      state: { regNo: this.patientDetails.f.centralRegNo.value },
    });
  }

  navigateToDashboard() {
    this._router.navigateByUrl("heart/dashboard");
  }

  resetForm() {
    this.liverRegisterForm.reset();
    this.initializeForm();
  }

  validateNumberInput(event: any): boolean {
    const value = event.target.value;

    // Allow backspace and delete
    if (event.key === "Backspace" || event.key === "Delete") {
      return true;
    }

    // Allow only numbers, decimal point, and negative sign
    if (!/^-?\d*\.?\d*$/.test(value + event.key)) {
      event.preventDefault();
      return false;
    }

    // Check for maximum 10 digits before decimal and 2 after
    const parts = (value + event.key).split(".");
    if (parts[0] && parts[0].replace("-", "").length > 10) {
      event.preventDefault();
      return false;
    }
    if (parts[1] && parts[1].length > 2) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  openModal(downloadLabTest) {
    this.modalService.open(downloadLabTest);
  }

  print() {
    window.print();
  }

  generatePDF_old() {
    this.loaderService.show();
    const data = document.getElementById("case-details-form");
    const registrationId = this.patientDetails.f.centralRegNo.value;
    html2canvas(data).then((canvas) => {
      const imgWidth = 208;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      const heightLeft = imgHeight;

      const contentDataURL = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      const position = 0;
      pdf.text('Heart Case Details', imgWidth / 2, 10, { align: 'center' });
      pdf.addImage(contentDataURL, "PNG", 0, position, imgWidth, imgHeight);
      pdf.save(`${registrationId}_case_details.pdf`);
      this.loaderService.hide();
    });
  }

  generatePDF() {
  
      // add loader
      this.loaderService.show();
  
       const oldPageSize = this.pageSize;
  
    // Step 2: Show all records in the DOM
      this.pageSize = this.totalLabRecords;
    
      const registrationId = this.patientDetails.f.centralRegNo.value;
      const element = document.getElementById('case-details-form');
  
      html2canvas(element, { scale: 2 }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        
        const imgProps = (pdf as any).getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
  
        let heightLeft = pdfHeight;
        let position = 20;
  
        pdf.text('Heart Case Details', pdfWidth / 2, 10, { align: 'center' });
        pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
        heightLeft -= pdf.internal.pageSize.getHeight();
  
        while (heightLeft > 0) {
          position = heightLeft - pdfHeight;
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
          heightLeft -= pdf.internal.pageSize.getHeight();
        }
  
        pdf.save(`${registrationId}_case_details.pdf`);
        this.pageSize = oldPageSize;
        this.loaderService.hide();
      });
  
      
    }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  // Helper method to update unique procedure types
  onLabPageChange(event: any) {
    const startIndex = event.first;
    const endIndex = startIndex + event.rows;

    // Make sure we have the full list stored
    if (!this._fullLabnewList || this._fullLabnewList.length === 0) {
      this._fullLabnewList = [...this.labnewList];
    }

    // Update the displayed list based on pagination
    this.labnewList = this._fullLabnewList.slice(startIndex, endIndex);
  }

  loadLabData() {
    // Your existing code to fetch lab data

    // After setting this.labnewList, add:
    this._fullLabnewList = [...this.labnewList]; // Store full list
    this.totalLabRecords = this._fullLabnewList.length;

    // Initialize with first page
    if (this.labTestPaginator) {
      this.labTestPaginator.first = 0;
    }
    this.onLabPageChange({ first: 0, rows: this.paginationSize });
  }

  initializeLabPagination() {
    // Store the full list
    this._fullLabnewList = [...this.labnewList];
    this.totalLabRecords = this._fullLabnewList.length;

    // Only show first page (10 records)
    this.labnewList = this._fullLabnewList.slice(0, this.paginationSize);

    // Reset paginator to first page if it exists
    if (this.labTestPaginator) {
      this.labTestPaginator.first = 0;
    }
  }

  fetchVaccineFromAlShifa() {
    // Check if estCode and civilId are not null
    this.estCode = "20068"; // Example estCode, replace with actual value

    if (this.estCode && this.currentCivilId) {
      this._caseDetaisService
        .fetchVaccineFromShifa(this.estCode, this.currentCivilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbVaccinationInfoDb: any = res["result"];
                this.Vaccination.getDataFromAlshifa(rgTbVaccinationInfoDb);
              } else {
                Swal.fire("No vaccination records found.", "info");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching vaccination records: " +
                error.message,
              "error"
            );
          }
        );
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  FetchLabFromAlShifa() {
    // Check if estCode and civilId are not null
    // console.log('estCode: ' + this.estCode + ', civilId: ' + this.civilId);
    if (this.estCode && this.civilId) {
      this._caseDetaisService
        .fetchLabFromShifa(this.estCode, this.civilId)
        .subscribe(
          (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null && res["result"].length > 0) {
                const rgTbLabTestsDB: any = res["result"];
                for (let labRList of rgTbLabTestsDB) {
                  this.addNewLabList(
                    labRList.runId,
                    this._sharedService.setDateFormat(labRList.testDate),
                    labRList.mohTestCode,
                    labRList.resultSummary,
                    labRList.instCode,
                    labRList.enteredBy,
                    labRList.enteredDate,
                    labRList.source,
                    false
                  );
                }
              } else {
                Swal.fire(
                  "No Record Found",
                  "No lab results found for the given estCode and civilId.",
                  "info"
                );
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          (error) => {
            Swal.fire(
              "Error",
              "Error occurred while fetching lab results: " + error.message,
              "error"
            );
          }
        );
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  fetchSurgeryFromAlShifa() {
    //this.estCode="20068";
    //console.log("estCode--" + this.estCode + "civilId--" + this.civilId);
    //this.civilId=this.donorForm.value.civilId;
    //this.refreshHPLC = false;
    if (this.estCode && this.currentCivilId) {
      this._caseDetaisService
        .fetchSurgeryFromShifa(this.estCode, this.currentCivilId)
        .subscribe((res) => {
          if (res["code"] == "S0000") {
            if (res["result"] != null) {
              const rgTbSurgeryDtlsDB: any = res["result"];
              this.surgery.getDataFromAlshifa(rgTbSurgeryDtlsDB);
            } else {
              Swal.fire("No records found.", "info");
            }
          } else {
            Swal.fire(res["message"]);
          }
        });
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  fetchMedicineDtlsFromAlShifa() {
    //this.estCode="20068";
    // this.patntId="10365";
    //this.civilId=this.donorForm.value.civilId;
    //this.refreshHPLC = false;
    if (this.estCode && this.patntId) {
      this._caseDetaisService
        .getLungRegistryMedicineInfo(this.estCode, this.patntId)
        .subscribe((res) => {
          if (res["code"] == "S0000") {
            if (res["result"] != null) {
              const rgTbMedicineDtlsDB: any = res["result"];

              // console.log("rgTbMedicineDtlsDB: " + JSON.stringify(rgTbMedicineDtlsDB));

              this.Medication.getDataFromAlshifa(rgTbMedicineDtlsDB);
            } else {
              Swal.fire("No records found.", "info");
            }
          } else {
            Swal.fire(res["message"]);
          }
        });
    } else {
      Swal.fire(
        "Error",
        "Please enter civilId before fetching data.",
        "warning"
      );
    }
  }

  resetFormModal() {
    this.filterModelForm.reset();
  }

  loadProcedures() {
    this._heartService.getLiverProcedure().subscribe({
      next: (response) => {
        if (response && response.result) {
          this.procedures = response.result;
          // Get unique procedure types
          this.uniqueProcedureTypes = [
            ...new Set(this.procedures.map((p) => p.paramName)),
          ];
        }
      },
      error: (error) => {
        console.error("Error loading procedures:", error);
      },
    });
  }

  // loadHeartPhysioBaseline() {
  //   this._heartService.getHeartPhysioBaseline().subscribe({
  //     next: (response) => {
  //       if (response && response.result) {
  //         this.physioBaseline = response.result;
  //       }
  //     },
  //     error: (error) => {
  //       console.error("Error loading physio baseline:", error);
  //     },
  //   });
  // }

  onProcedureTypeSelect(name: string) {
    const procedure = this.procedures.find((p) => p.paramName === name);
    if (procedure) {
      const existingIndex = this.procedureDetails.findIndex(
        (pd) => pd.procId === procedure.paramId
      );

      if (existingIndex === -1) {
        // Add new procedure
        this.procedureDetails.push({
          runId: null,
          procId: procedure.paramId,
          doneDate: null,
          remarks: "",
        });
      } else {
        // Remove procedure if it exists
        this.procedureDetails.splice(existingIndex, 1);
      }
    }
  }

  updateProcedureDetails(procId: number, field: string, value: any) {
    const detail = this.procedureDetails.find((pd) => pd.procId === procId);
    if (detail) {
      detail[field] = value;
    }
  }

  isProcedureSelected(type: string): boolean {
    if (!this.procedureDetails || !this.procedures) {
      return false;
    }
    return this.procedureDetails.some(
      (pd) =>
        this.procedures.find((p) => p.paramId === pd.procId).paramName === type
    );
  }

  getProcedureType(procId: number): string {
    if (!this.procedures) {
      return "";
    }
    const procedure = this.procedures.find((p) => p.paramId === procId);
    return procedure ? procedure.paramName : "";
  }

  getProcedureDetail(procedureType: string): RgProcedureDetailsDto | undefined {
    if (!this.procedureDetails || !this.procedures) {
      return undefined;
    }

    return this.procedureDetails.find(
      (detail) => this.getProcedureType(detail.procId) === procedureType
    );
  }

  downloadMedicalProcedures() {
    //this.currentCivilId = 6732772;
    //this.estCode = 20068;
    if (this.estCode && this.currentCivilId) {
      this._heartService
        .fetchAllDonorProcedureFromShifa(this.estCode, this.currentCivilId)
        .subscribe({
          next: (res) => {
            if (res["code"] == "S0000") {
              if (res["result"] != null) {
                const rgTbLabTestsDB: any = res["result"];

                //const existingCount = this.procedureDetails ? this.procedureDetails.length : 0;

                // Load medical procedures (will append to existing list)
                this.loadMedicalsProc(rgTbLabTestsDB);
              } else {
                Swal.fire("No records found.");
              }
            } else {
              Swal.fire(res["message"]);
            }
          },
          error: (error) => {
            console.error("Error loading procedures from AlShifa:", error);
          },
        });
    } else {
      console.warn(
        "estCode or civilId is null. FetchAllDonorProcedureFromAlShifa will not be executed."
      );
    }
  }

  loadMedicalsProc(result: any) {
    if (!result || result.length === 0) {
      console.log("No medical procedures data to load");
      return;
    }

    console.log("Loading medical procedures:", result);

    console.log("Procedure Details--", this.procedureDetails);
    console.log("Procedures--", this.procedures);

    // If procedures aren't loaded yet, load them first
    if (!this.procedures || this.procedures.length === 0) {
      this._heartService.getLiverProcedure().subscribe({
        next: (response) => {
          if (response && response.result) {
            this.procedures = response.result;
            // Now process the procedures with the master data loaded
            this.processMedicalProcedures(result);
          }
        },
        error: (error) => {
          console.error("Error loading procedures:", error);
          Swal.fire({
            title: "Error",
            text: "Failed to load procedure master data",
            icon: "error",
          });
        },
      });
    } else {
      // Procedures already loaded, process directly
      this.processMedicalProcedures(result);
    }
  }

  private processMedicalProcedures(result: any) {
    // Make sure we have the existing procedures array
    if (!this.procedureDetails) {
      this.procedureDetails = [];
    }

    // Get valid procedure IDs from master data
    const validProcedureIds = this.procedures.map((p) => p.paramId);

    // Filter procedures that exist in our master data
    const validProcedures = result.filter((proc) =>
      validProcedureIds.includes(proc.procedureId)
    );

    if (validProcedures.length === 0) {
      Swal.fire({
        title: "Warning",
        text: "No matching procedures found in the system",
        icon: "warning",
      });
      return;
    }

    // Count of newly added procedures
    let newlyAddedCount = 0;

    console.log("validProcedures--", validProcedures);
    console.log("procedures--", this.procedures);

    // Add all procedures from API to the existing list
    validProcedures.forEach((proc) => {
      // Map the procedure data from AlShifa to our format
      this.procedureDetails.push({
        runId: proc.patientId || null,
        procId: proc.procedureId,
        doneDate: proc.reportDate ? new Date(proc.reportDate) : null,
        remarks: proc.report || "",
      });

      newlyAddedCount++;
    });

    console.log(
      "Procedure Details after adding new ones:",
      this.procedureDetails
    );

    // Update selected procedures to include all procedures
    this.selectedProcedures = this.procedureDetails.map((proc) => proc.procId);

    // Update unique procedure types
    //this.updateUniqueProcedureTypes();

    if (!this.procedures.length) {
      this.loadProcedures();
    }

    // Show success message
    if (newlyAddedCount > 0) {
      Swal.fire({
        title: "Success",
        text: `${newlyAddedCount} Medical procedures added successfully`,
        icon: "success",
        timer: 2000,
        showConfirmButton: false,
      });
    } else {
      Swal.fire({
        title: "Information",
        text: "No new procedures found to add",
        icon: "info",
        timer: 2000,
        showConfirmButton: false,
      });
    }
  }
  // Helper method to update unique procedure types
  private updateUniqueProcedureTypes() {
    // Get unique procedure types from the loaded procedures
    const procedureIds = this.procedureDetails.map((p) => p.procId);

    // Filter procedures to only include those that are in the procedureDetails
    const matchedProcedures = this.procedures.filter((p) =>
      procedureIds.includes(p.paramId)
    );

    // Extract unique procedure names
    this.uniqueProcedureTypes = [
      ...new Set(matchedProcedures.map((p) => p.paramName)),
    ];

    console.log("Updated unique procedure types:", this.uniqueProcedureTypes);
  }

  saveProcedureDetails(): RgProcedureDetailsDto[] {
    return this.uniqueProcedureTypes
      .filter((name) => this.isProcedureSelected(name))
      .map((name) => {
        const detail = this.getProcedureDetail(name);
        if (!detail) return null;

        let doneDate = null;
        if (detail.doneDate) {
          try {
            if (detail.doneDate instanceof Date) {
              doneDate = detail.doneDate;
            } else {
              doneDate = new Date(detail.doneDate);
            }
          } catch (e) {
            console.error("Error parsing date:", e);
          }
        }

        return {
          procId: detail.procId,
          doneDate: doneDate,
          remarks: detail.remarks || "",
        };
      })
      .filter((detail) => detail !== null);
  }

  // if intrope used in no then clear the intrope agent id and others
  clearInotropeAgent() {
    this.caseDetailsForm.get("heartRegister.heartInotropeDtls").reset();
  }

  clearMcsDeviceUsed() {
    this.caseDetailsForm.get("heartRegister.heartMcsDtls").reset();
    this.caseDetailsForm.get("heartRegister.mcsInitiatedDate").reset();
  }
}
