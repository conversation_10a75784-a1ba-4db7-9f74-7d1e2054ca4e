﻿﻿import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
// import ServiceUrlConstants from '../common/serviceUrl.constants';

import { environment } from './../../environments/environment';
import { map, shareReplay } from 'rxjs/operators';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import * as CommonConstants from './../_helpers/common.constants';
import * as AppUtils from '../common/app.utils';
import { observableToBeFn } from 'rxjs/internal/testing/TestScheduler';
import { async } from 'rxjs/internal/scheduler/async';
import { timeout } from 'rxjs-compat/operator/timeout';
import { RgTbCancerSite, RgTbLab, RgTbOrganPARAM, RgTbPARAM, RgTbProcedure, RgVwDecDonEthicalApprvls, RgVwOccupationMast } from '../_models/deceased-donor.model';


@Injectable({ providedIn: 'root' })
export class MasterService {

  constructor(private http: HttpClient) { }

  villagesMast = new BehaviorSubject<any>(null);
  regions = new BehaviorSubject<any>(null);
  regionsMaster = new BehaviorSubject<any>(null);
  regionsMasterFull = new BehaviorSubject<any>(null);
  institutesMaster = new BehaviorSubject<any>(null);
  institutesMasterFull = new BehaviorSubject<any>(null);
  wallayatMaster = new BehaviorSubject<any>(null);
  wallayatMasterFull = new BehaviorSubject<any>(null);
  menuShortNameList = new BehaviorSubject<any>(null);
  icdList = new BehaviorSubject<any>(null);
  visitPurposeList = new BehaviorSubject<any>(null);
  durSymptomsList = new BehaviorSubject<any>(null);
  reAdmissionList = new BehaviorSubject<any>(null);
  diagnosisIcdList = new BehaviorSubject<any>(null);
  symptomsList = new BehaviorSubject<any>(null);
  medicationsList = new BehaviorSubject<any>(null);
  comorbiditiesList = new BehaviorSubject<any>(null);
  psychotherapyList= new BehaviorSubject<any>(null);
  scaleList= new BehaviorSubject<any>(null);
  MedIdList = new BehaviorSubject<any>(null);
  conditionsList = new BehaviorSubject<any>(null);
  InhalerList = new BehaviorSubject<any>(null);
  diabLabList = new BehaviorSubject<any>(null);
  liverTransplanMastList = new BehaviorSubject<any>(null);
  liverTransplanSubTypeMastList = new BehaviorSubject<any>(null);
  liverTransplanTypeMastList = new BehaviorSubject<any>(null);
  liverSurgicalInfoMastList = new BehaviorSubject<any>(null);
  liverSurgicalInfoSubTypeMastList = new BehaviorSubject<any>(null);
  liverTransplantMedicineList = new BehaviorSubject<any>(null);
  liverComplicationMastList = new BehaviorSubject<any>(null);
  liverProcedureMastList = new BehaviorSubject<any>(null);
  liverCurrMgmtMastList = new BehaviorSubject<any>(null);
  modeOfPrestList = new BehaviorSubject<any>(null);
  bmiList = new BehaviorSubject<any>(null);
  icdRenalShortList = new BehaviorSubject<any>(null);
  icdLiverShortList = new BehaviorSubject<any>(null);
  icdDeathShortList = new BehaviorSubject<any>(null);
  icdLungShortList = new BehaviorSubject<any>(null);
  icdLungList = new BehaviorSubject<any>(null);
  icdHeartShortList = new BehaviorSubject<any>(null);
  icdHeartList = new BehaviorSubject<any>(null);
  lungTransplanDiseaseSeverityList = new BehaviorSubject<any>(null);

  private paramCache$: Observable<any>;
  private exParamCache$: Observable<any>;
  private institutesCache$: Observable<any>;
  public menuShortName;

  lungTransplantMastList = new BehaviorSubject<any>(null);
  public getVillagesMasterFull() {
    if (!this.villagesMast.value) {
      this.http.get(environment.eregistryApi + "api/master/getVillageMast?walCode=0").subscribe(response => {
        if (response && response['result']) {
          this.villagesMast.next(response['result']);
        }
      })
    }
  }
  public getMenuShortNameList() {
    if (!this.menuShortNameList.value) {
      let n;
      this.http.get(AppUtils.GET_MENU_SHOURT_NAME_LIST).subscribe(response => {
        if (response && response['result']) {
          n = response['result'];
          this.menuShortNameList.next(n);
        }
      })
    }
  }

  getExamFreqList(examParam: any): Observable<any> {
    return this.http.get(environment.eregistryApi + "api/master/getExaminations", {
      params: new HttpParams().set("examParam", examParam)
    })
  }

  public getRegions() {
    if (!this.regions.value) {
      this.http.get(environment.eregistryApi + "api/master/region-mast").subscribe(response => {
        if (response && response['result']) {
          this.regions.next(response['result']);
        }
      })
    }
  }

  checkuserLevel() {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    let estDefult = curUser['institutes'][0];
    let estList = curUser['workStatus'].filter(s => s.regCode == estDefult.regCode);
    let level: string = null;

    if (!this.menuShortNameList.value) {

      setTimeout(() => {
        this.getMenuShortNameList();
      }, 1000);

    }

    this.menuShortName = this.menuShortNameList.value.filter(s => s.menuId == JSON.parse(localStorage.getItem(AppUtils.MENU_ID))).map(s => s.regShName)[0];

    if (curUser['roles'].filter(s => s.name == 'EREGISTRY_ADMIN').length > 0 || curUser['roles'].filter(s => s.name == 'REG_ADMIN_' + this.menuShortName).length > 0) {

      level = "admin";
    } else if (curUser['roles'].filter(s => s.name == 'REG_REGION_USER_' + this.menuShortName).length > 0) {
      level = "reg";
    } else if (curUser['roles'].filter(s => s.name == 'REG_EST_USER_' + this.menuShortName).length > 0) {
      level = "est";
    } else {
      level = "est";
    }
    return level;
  }

  public getRegionsMaster() {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    let estDefult = curUser['institutes'][0];
    let estList = curUser['workStatus'].filter(s => s.regCode == estDefult.regCode);
    if (!this.menuShortNameList.value) {
      this.getMenuShortNameList();
    }
    this.menuShortName = this.menuShortNameList.filter(s => s.menuId == JSON.parse(localStorage.getItem(AppUtils.MENU_ID))).map(s => s.regShName)[0];
    if (!this.regions.value) {
      this.http.get(AppUtils.GET_REGION_MASTER).subscribe(response => {
        if (response && response['result']) {
          /************ ckeck user roles and filter region depend to the user*************/
          /*  regCode: 50009 */

          if (this.checkuserLevel() == 'admin') {
            this.regionsMaster.next(response['result']);
          } else {
            let n = [];
            response['result'].forEach(element => {
              estList.forEach(userReg => {
                //&& response['result'].filter(s => s.estCode == userReg['estCode']).length > 0
                if (element['regCode'] == userReg['regCode'] && n.filter(s => s.regCode == userReg['regCode']).length == 0) {
                  n.push(element);
                }
              });

            });
            this.regionsMaster.next(n);
          }
          /************ ckeck user roles and filter region depend to the user*************/
        }
      })

    }
  }
  public getRegionsMasterFull() {
    if (!this.regionsMasterFull.value) {
      this.http.get(AppUtils.GET_REGION_MASTER).subscribe(response => {
        if (response && response['result']) {
          this.regionsMasterFull.next(response['result']);
        }
      })

    }
  }


  public getWallayatMaster() {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    let estDefult = curUser['institutes'][0];
    let estList = curUser['workStatus'].filter(s => s.regCode == estDefult.regCode);
    if (!this.wallayatMaster.value) {
      this.http.get(AppUtils.GET_WALLAYAT_MASTER + "?regCode=0").subscribe(response => {
        if (response && response['result']) {
          /************ ckeck user roles and filter wallayat depend to the user*************/
          /* "walCode": 50103,
  "regCode": 50001,*/
          if (this.checkuserLevel() == "admin") {
            this.wallayatMaster.next(response['result']);
          } else if (this.checkuserLevel() == "reg") {
            let n = [];
            estList.forEach(userReg => {
              if (n.filter(s => s.regCode == userReg['regCode']).length == 0) {
                response['result'].forEach(element => {
                  if (n.filter(s => s.walCode == element['walCode']).length == 0) {
                    n.push(element);
                  }
                });
              }
            });
            this.wallayatMaster.next(n);
          } else if (this.checkuserLevel() == "est") {
            let n = [];
            response['result'].forEach(element => {
              estList.forEach(userReg => {
                if (element['walCode'] == userReg['walCode'] && n.filter(s => s.walCode == userReg['walCode']).length == 0) {
                  n.push(element);
                }
              });

            });
            this.wallayatMaster.next(n);

          } else {
            let n = [];
            response['result'].forEach(element => {
              estList.forEach(userReg => {
                if (element['walCode'] == userReg['walCode'] && n.filter(s => s.walCode == userReg['walCode']).length == 0) {
                  n.push(element);
                }
              });

            });
            this.wallayatMaster.next(n);
          }
          /************ ckeck user roles and filter wallayat depend to the user*************/
        }
      })

    }
  }

  public getWallayatMasterFull() {
    if (!this.wallayatMasterFull.value) {
      this.http.get(AppUtils.GET_WALLAYAT_MASTER + "?regCode=0").subscribe(response => {
        if (response && response['result']) {
          this.wallayatMasterFull.next(response['result']);
        }
      })
    }
  }

  public getInstitutesMasterFull() {
    if (!this.institutesMasterFull.value) {
      this.http.get(AppUtils.GET_INSTITE_MASTER).subscribe(response => {
        if (response && response['result']) {
          this.institutesMasterFull.next(response['result']);
        }
      })
    }
  }



  checkInstList(response) {
    let curUser = JSON.parse(localStorage.getItem(CommonConstants.CUR_USER));
    let estDefult = curUser['institutes'][0];
    let estList = curUser['workStatus'].filter(s => s.regCode == estDefult.regCode);

    /************ new ckeck user roles and filter institutes depend to the user*************/
    /*"estCode": 240,
      "walCode": 50136,
      "regCode": 50006
      REG_REGION_USER_
      */

    if (this.checkuserLevel() == "admin") {
      return response
      // this.institutesMaster.next(response['result']);
    } else if (this.checkuserLevel() == "reg") {
      let n = [];
      estList.forEach(userReg => {
        if (n.filter(s => s.regCode == userReg['regCode']).length == 0) {
          response.forEach(element => {
            if (element['regCode'] == userReg['regCode'] && n.filter(s => s.estCode == userReg['estCode']).length == 0) {
              n.push(element);
            }
          });
        }
      });
      return n;
      //    this.institutesMaster.next(n);
    }
    else if (this.checkuserLevel() == "est") {
      let n = [];
      estList.forEach(element => {
        curUser['institutes'].forEach(userEst => {
          if (element['estCode'] == userEst['estCode'] && n.filter(s => s.estCode == userEst['estCode']).length == 0) {
            element['estName'] = element['estFullName'];
            n.push(element);
          }
        });

      });
      return n;
      // this.institutesMaster.next(n);
    }
    else {
      let n = [];
      response.forEach(element => {
        estList.forEach(userReg => {
          if (element['estCode'] == userReg['estCode'] && n.filter(s => s.estCode == userReg['estCode']).length == 0) {
            n.push(element);
          }
        });

      });
      return n;
      // this.institutesMaster.next(n);
    }
    /************ new ckeck user roles and filter institutes depend to the user*************/

  }

  public getInstitutesMaster() {

    // if (!this.menuShortNameList.value) {
    //     this.getMenuShortNameList();
    // }
    // this.menuShortName = this.menuShortNameList.filter(s=> s.menuId == JSON.parse(localStorage.getItem(AppUtils.MENU_ID)) ).map(s => s.regShName)[0];

    if (!this.institutesMaster.value) {
      this.http.get(AppUtils.GET_INSTITE_MASTER).subscribe(response => {
        if (response && response['result']) {
          this.institutesMaster.next(response['result']);
          //    return   this.checkInstList(this.institutesMaster.value);
          //     return this.institutesMaster.value;

        }

      })

    } else {
      //   return this.institutesMaster.value;
      // return  this.checkInstList(this.institutesMaster.value);
    }

    return this.checkInstList(this.institutesMaster.value);
  }

  getInstitutesMasterByUserRoles(): Observable<any> {
    let menuID = JSON.parse(localStorage.getItem(AppUtils.MENU_ID))

    if (menuID == null) {
      menuID = 0;
    }
    return this.http.get(AppUtils.INSTITUTES_USER_ROLES, {
      params: new HttpParams().set("menuID", menuID)
    })
  }

  public getInstitutesMasterByEstCode(estCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_INSTITE_MASTER_BY_EST_CODE, {
      params: new HttpParams().set("estCode", estCode)
    })
  }

  getRegionsList(): Observable<any> {
    return this.http.get(AppUtils.GET_REGION_MASTER)
  }

  getWilayatList(regCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_WALLAYAT_MASTER, {
      params: new HttpParams().set("regCode", regCode)
    })
  }

  getVillageList(walCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_VILLAGE_MASTER, {
      params: new HttpParams().set("walCode", walCode)
    })
  }
  getInstiteList(regCode: any, walCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_INSTITE_MASTER, {
      params: new HttpParams().set("regCode", regCode)
        .set("walCode", walCode)
    })
  }

  // to get the list of hospitals only


  getHospitals(): Observable<any> {
    return this.http.get(AppUtils.GET_HOSPITALS)
  }

  getLiverTransplantMedList(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_TRANSPLANT_MEDICINE)
  }


  // getIcdList(): Observable<any> {
  //     return this.http.get(AppUtils.GET_ICD_LIST)
  // }

  getIcdList() {
    if (!this.icdList.value) {
      this.http.get(AppUtils.GET_ICD_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdList.next(response['result']);
        }
      })
    }
  }
  getMedicationIdList() {
    if (!this.MedIdList.value) {
      this.http.get(AppUtils.GET_MED_ID_LIST).subscribe(response => {
        if (response && response['result']) {
          this.MedIdList.next(response['result']);
        }
      })
    }
  }


  getInhalerIdList() {
    if (!this.InhalerList.value) {
      this.http.get(AppUtils.GET_INHALER_LIST).subscribe(response => {
        if (response && response['result']) {
          this.InhalerList.next(response['result']);
        }
      })
    }
  }


  getDiabLabInvst() {
    if (!this.diabLabList.value) {
      this.http.get(AppUtils.GET_DIAB_LAB_INVST).subscribe(response => {
        if (response && response['result']) {
          this.diabLabList.next(response['result']);
        }
      })
    }
  }

  getLiverTransplantMast() {
    if (!this.liverTransplanMastList.value) {
      this.http.get(AppUtils.GET_LIVER_TRANS_IND_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanMastList.next(response['result']);
        }
      })
    }
  }


  getLungTransplantMast() {

    if (!this.lungTransplantMastList.value) {

      this.http.get(AppUtils.GET_LUNG_TRANS_IND_MAST).subscribe(response => {
        if (response && response['result']) {
          console.log("response", response['result']);
          this.lungTransplantMastList.next(response['result']);
        }
      })
    }
  }


  getLungTransplantDiseaseMast(paramId: any): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_TRANS_IND_DIS_MAST, {
      params: new HttpParams().set("prevId", String(paramId))
    })
  }


  getLungTransplantDiseaseSeverity(): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_DIS_MAST);
  }


  getLungCurrentMgmt(): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_CURR_MGMT_MAST);
  }


  getLungDiseaseSeverityMast() {
    if (!this.lungTransplanDiseaseSeverityList.value) {
      this.http.get(AppUtils.GET_LUNG_DIS_MAST).subscribe(response => {
        if (response && response['result']) {
          this.lungTransplanDiseaseSeverityList.next(response['result']);
        }
      })
    }
  }

  getLungCurrMgmtMast() {
    if (!this.liverTransplanMastList.value) {
      this.http.get(AppUtils.GET_LUNG_CURR_MGMT_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanMastList.next(response['result']);
        }
      })
    }
  }

  getLungICDMST() {
    if (!this.liverTransplanMastList.value) {
      this.http.get(AppUtils.GET_LUNG_ICD_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanMastList.next(response['result']);
        }
      })
    }
  }

  getLungPleurodesisIndicationMST() {
    if (!this.liverTransplanMastList.value) {
      this.http.get(AppUtils.GET_LUNG_PLEURODESIS_IND_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanMastList.next(response['result']);
        }
      })
    }
  }

  getLungMedicineDetailMST() {
    if (!this.liverTransplanMastList.value) {
      this.http.get(AppUtils.GET_LUNG_MEDICINE_DTL_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanMastList.next(response['result']);
        }
      })
    }
  }


  getLiverTransplantSubTypeMast() {
    if (!this.liverTransplanSubTypeMastList.value) {
      this.http.get(AppUtils.GET_LIVER_TRANS_PLANT_SUB_TYPE_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanSubTypeMastList.next(response['result']);
        }
      })
    }
  }

  // getLiverTransplantMedicineList() {
  //     if (!this.liverTransplantMedicineList.value) {
  //         this.http.get(AppUtils.GET_LIVER_TRANS_PLANT_MED_LIST).subscribe(response => {
  //             if (response && response['result']) {
  //                // console.log(response);
  //                 this.liverTransplantMedicineList.next(response);
  //             }
  //         })
  //     }
  // }

  getLiverTransplantMedicineList(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_TRANS_PLANT_MED_LIST)
  }


  getLiverTransplantTypeMast() {
    if (!this.liverTransplanTypeMastList.value) {
      this.http.get(AppUtils.GET_LIVER_TRANS_PLANT_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverTransplanTypeMastList.next(response['result']);
        }
      })
    }
  }

  getLiverSurgicalMast() {
    if (!this.liverSurgicalInfoMastList.value) {
      this.http.get(AppUtils.GET_LIVER_SURGICAL_INFO_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverSurgicalInfoMastList.next(response['result']);
        }
      })
    }
  }


  getLiverSurgicalSubInfoMast() {
    if (!this.liverSurgicalInfoSubTypeMastList.value) {
      this.http.get(AppUtils.GET_LIVER_SURGICAL_SUB_INFO_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverSurgicalInfoSubTypeMastList.next(response['result']);
        }
      })
    }
  }

  getLiverProceduresMast() {
    if (!this.liverProcedureMastList.value) {
      this.http.get(AppUtils.GET_LIVER_PROCEDURE_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverProcedureMastList.next(response['result']);
        }
      })
    }
  }

  getLiverComplicationMast() {
    if (!this.liverComplicationMastList.value) {
      this.http.get(AppUtils.GET_LIVER_COMPLICATION_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverComplicationMastList.next(response['result']);
        }
      })
    }
  }

  getLiverCurrenMgmtMast() {
    if (!this.liverCurrMgmtMastList.value) {
      this.http.get(AppUtils.GET_LIVER_CURR_MGMT_MAST).subscribe(response => {
        if (response && response['result']) {
          this.liverCurrMgmtMastList.next(response['result']);
        }
      })
    }
  }

  getbmiValueList() {
    if (!this.bmiList.value) {
      this.http.get(AppUtils.GET_BMI_VALUE_LIST).subscribe(response => {
        if (response && response['result']) {
          this.bmiList.next(response['result']);
        }
      })
    }
  }

  // getIcdRenalShortList(): Observable<any> {
  //     return this.http.get(AppUtils.GET_ICD_RENAL_SHORT_LIST)
  // }

  getIcdRenalShortList() {
    if (!this.icdRenalShortList.value) {
      this.http.get(AppUtils.GET_ICD_RENAL_SHORT_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdRenalShortList.next(response['result']);
        }
      })
    }
  }

  getIcdLiverShortList() {
    if (!this.icdLiverShortList.value) {
      this.http.get(AppUtils.GET_ICD_LIVER_SHORT_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdLiverShortList.next(response['result']);
        }
      })
    }
  }


  getIcdDeathList() {
    if (!this.icdDeathShortList.value) {
      this.http.get(AppUtils.GET_ICD_DEATH_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdDeathShortList.next(response['result']);
        }
      })
    }
  }

  get eldParams() {
    if (!this.paramCache$) {
      this.paramCache$ = this.requestElderlyParam().pipe(
        shareReplay(1)
      );
    }
    return this.paramCache$;
  }

  get exParams() {
    if (!this.exParamCache$) {
      this.exParamCache$ = this.getExamParams().pipe(
        shareReplay(1)
      );
    }
    return this.exParamCache$;
  }

  getExamParams(): Observable<any> {
    return this.http.get(environment.eregistryApi + "api/master/getExaminations").pipe(
      map(response => response));
  }

  private requestElderlyParam() {
    return this.http.get(environment.eregistryApi + "api/master/elderly/exam/params").pipe(
      map(response => response)
    );
  }

  private getInstitutes() {
    return this.http.get(environment.eregistryApi + "api/master/institutes").pipe(
      map(response => response)
    );
  }

  get institiutes() {
    if (!this.institutesCache$) {
      this.institutesCache$ = this.getInstitutes().pipe(
        shareReplay(1)
      );
    }
    return this.institutesCache$;
  }




  // Vaccination
  getVaccinationCivilId(civilID: any): Observable<any> {
    return this.http.get(AppUtils.GET_VACCINATION_CIVILID, {
      params: new HttpParams().set("civilID", civilID)
    })
  }

  getVaccinationMastList(): Observable<any> {
    return this.http.get(AppUtils.GET_VACCINATION_MAST_LIST)
  }




  getNationalityList(natCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_NATION_MASTER, {
      params: new HttpParams().set("natCode", natCode)

    })
  }

  getRelationTypeMast(relationCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_RELATION_TYPE_MASTER, {
      params: new HttpParams().set("relationCode", relationCode)

    })
  }

  getPatientbyCivilId(civilid: any): Observable<any> {
    return this.http.get(AppUtils.GET_PATIENT_BY_CIVILID + civilid);
  }

  getLabTestList(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_TEST_NAME);
  }

  getMpiV2Details(req: any): Observable<any> {
    return this.http.post(AppUtils.GET_MPI_V2_DETAILS, req);
  }

  getRegDonorDetails(civilId: any): Observable<any> {
    return this.http.get(AppUtils.REG_DONOR_DTL + civilId);
  }

  getRegDonorDetailsById(kidneyDonorId: any): Observable<any> {
    return this.http.get(AppUtils.REG_DONOR_DTL_BY_ID + kidneyDonorId);
  }

  public findElderlyPatientVisitInfo(estCode: number, patientId: number) {
    return this.http.get(AppUtils.FIND_ELDERLY_PATIENT_VISIT_INFO + estCode + "/" + patientId);
  }

  getLabByRegNo(centralRegNo: any): Observable<any> {
    return this.http.get(AppUtils.GET_LAB_REGNO, {
      params: new HttpParams().set("centralRegNo", centralRegNo)
    })
  }


  //renal Transplant Complication

  getComplicationList(compId: any): Observable<any> {
    return this.http.get(AppUtils.GET_COMPLICATION_MAST);
  }

  getGenComplicationMast(): Observable<any> {
    return this.http.get(AppUtils.GET_COMPLICATION_LIST);
  }


  getLiverDonorComplicationMast(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_DONOR_COMPLICATION_LIST);
  }

  getBloodRelation(): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_RELATION);
  }
  getNonBloodRelation(): Observable<any> {
    return this.http.get(AppUtils.GET_NON_BLOOD_RELATION);
  }

  // genetic blood disorder

  getGeneticBlood(geneticTypeId: any): Observable<any> {
    return this.http.get(AppUtils.GET_GENETIC_BLOOD + geneticTypeId);
  }

  getComponentsTest(geneticTypeId: any, testID: any): Observable<any> {
    return this.http.get(AppUtils.GET_COMPONENT_TEST + "geneticTypeId=" + geneticTypeId + "&" + "testID=" + testID);
  }

  getGenotypeDesc(): Observable<any> {
    return this.http.get(AppUtils.GET_GENO_TYPE_DESC);
  }

  getGenotypeBase(): Observable<any> {
    return this.http.get(AppUtils.GET_GENO_TYPE_BASE);
  }

  getMedicineMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_MEDICINE_MASTER);
  }

  getLabMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_LAB_MASTER);
  }

  getLabTestToDownload(): Observable<any> {
    return this.http.get(AppUtils.LAB_TEST_TO_DOWNLOAD);
  }

  //download lab test from NEHR
  getNehrLabTest(body): Observable<any> {
    return this.http.post(AppUtils.NEHR_LAB_TEST_DOWNLOAD, body);
  }

  // Get visit class count fron NEHR by Civil ID
  getNehrVisitClassCount(civil_id: any): Observable<any> {
    return this.http.get(AppUtils.NEHR_VISIT_CLASS_COUNT + '/' + civil_id);
  }

  getAllTestComponent(): Observable<any> {
    return this.http.get(AppUtils.GET_LAB_TEST_COMPONENT);
  }

  getAllMohLabMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_MOH_LAB_MASTER);
  }

  getPlaceOfDeath(): Observable<any> {
    return this.http.get(AppUtils.GET_PLACE_OF_DEATH);
  }
  getSurgeryMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_SURGERY_MASTER);
  }

  getVaccineMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_VACCINE_MASTER);
  }

  getDisorderType(): Observable<any> {
    return this.http.get(AppUtils.GET_DISORDER_TYPE);
  }
  getMedFrequency(): Observable<any> {
    return this.http.get(AppUtils.GET_MED_FREQUENCY);
  }
  getMedicineType(): Observable<any> {
    return this.http.get(AppUtils.GET_MEDICINE_TYPE);
  }

  // diagnosis
  getDiagnosisMastList(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_DIAGNOSIS);
  }

  getDiagnosisRegNo(centralRegNo: any): Observable<any> {
    return this.http.get(AppUtils.GET_DIAGNOSIS_BY_CENTRAL_NO, {
      params: new HttpParams().set("centralRegNo", centralRegNo)
    })
  }


  getSurgeryRegNo(centralRegNo: any): Observable<any> {
    return this.http.get(AppUtils.GET_SURGERY_BY_CENTRAL_REG_NO, {
      params: new HttpParams().set("centralRegNo", centralRegNo)
    })
  }

  getRelationMast(): Observable<any> {
    return this.http.get(AppUtils.GET_RELATION_TYPE_MASTER);
  }
  // disorder services
  getdisoredPatinetInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_PATIENT_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredClinicalInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_CLINICAL_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredMedicineInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_MEDICINE_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredSurgeryInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_SURGERY_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredVaccineInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_VACCINE_ALSHIFA + estCode + "/" + patientId);

  }

  getVaccineHistroy(estCode: any, patientId: any) {
    return this.http.get(AppUtils.VACCINATION_HISTORY + estCode + "/" + patientId);
  }

  getdisoredLabInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_LAB_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredLabFollowUpInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_LAB_FOLLOW_UP_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getdisoredBloodTransInfo(estCode: any, patientId: any): Observable<any> {
    return this.http.get(AppUtils.GET_BLOOD_DISORDERD_BLOOD_TRANS_INFO_ALSHIFA + estCode + "/" + patientId);

  }

  getDialysisHospital(): Observable<any> {
    return this.http.get(AppUtils.GET_DIALYSIS_HOSPITAL);
  }

  getDialysisCenters(): Observable<any> {
    return this.http.get(AppUtils.GET_DIALYSIS_CENTERS);
  }

  //Diabetic

  getDiabetesTypes(): Observable<any> {
    return this.http.get(AppUtils.GET_DIABETES_TYPES);
  }

  getAllDiabeticSubtypes(): Observable<any> {
    return this.http.get(AppUtils.GET_ALLDIABETIC_SUBTYPES);
  }


  getAllDiabeticDashboard(): Observable<any> {
    return this.http.get(AppUtils.GET_ALLDIABETIC_DASHBOARD);
  }

  getpersCode(persCode: any): Observable<any> {
    return this.http.get(AppUtils.GET_PERSCODE_INFO + persCode);
  }

  getDiabetesExamParams(prevId: any): Observable<any> {
    return this.http.get(AppUtils.GET_DIABETES_EXAM_PARAMS + prevId);
  }
  getAllDiabetesExamParams(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_DIABETES_EXAM_PARAMS);
  }

  getAllDiabetesPresMod(): Observable<any> {

    return this.http.get(AppUtils.GET_DIABETES_PRES_MOD);

  }
  getDiabSensorsMasterData(): Observable<any> {

    return this.http.get(AppUtils.GET_DIAB_SENSOR_MASTER_DATA);

  }
  getDiabPumpsMasterData(): Observable<any> {

    return this.http.get(AppUtils.GET_DIAB_PUMPS_MASTER_DATA);

  }


  getAllDiabetesPresModReg() {
    if (!this.modeOfPrestList.value) {
      this.http.get(AppUtils.GET_DIABETES_PRES_MOD).subscribe(response => {
        if (response && response['result']) {
          this.modeOfPrestList.next(response['result']);
        }
      })
    }
  }


  getMedicinesList(): Observable<any> {
    return this.http.get(AppUtils.GET_MEDICINES_LIST);
  }

  getDiseaseList(): Observable<any> {
    return this.http.get(AppUtils.GET_DISEASE_LIST);
  }
  getAllLiverTransFwupComp(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_TRANS_FWUP_COMP_LIST)
  }
  getAllLiverTransFwupMed(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_TRANS_FWUP_MED_LIST)
  }
  getAllLiverTransFwupDisease(): Observable<any> {
    return this.http.get(AppUtils.GET_LIVER_TRANS_FWUP_DISEASE_LIST)
  }


  // Deceased Donor Ethical Approvals - ERG-580
  getEthicalApprovals(): Observable<RgTbPARAM[]> {
    return this.http.get<{ result: RgTbPARAM[] }>(AppUtils.DECEASED_DONOR_ETHICAL_APPROVAL_PARAM)
      .pipe(map(response => response.result));
  }

  getMedicalHistory(): Observable<RgTbPARAM[]> {
    return this.http.get<{ result: RgTbPARAM[] }>(AppUtils.DECEASED_DONOR_MEDICAL_HISTORY_PARAM)
      .pipe(map(response => response.result));
  }

  getOrganRelatedParam(): Observable<RgTbOrganPARAM[]> {
    return this.http.get<{ result: RgTbOrganPARAM[] }>(AppUtils.DECEASED_DONOR_ORGAN_RELATED_PARAM)
      .pipe(map(response => response.result));
  }

  getHemoDynamicParam(): Observable<RgTbPARAM[]> {
    return this.http.get<{ result: RgTbPARAM[] }>(AppUtils.DECEASED_DONOR_HEMODYNAMIC_PARAM)
      .pipe(map(response => response.result));
  }

  getCancerCategory(): Observable<RgTbCancerSite[]> {
    return this.http.get<{ result: RgTbCancerSite[] }>(AppUtils.DECEASED_DONOR_CANCER_SITE_LIST)
      .pipe(map(response => response.result));
  }

  getOccupationMaster(): Observable<RgVwOccupationMast[]> {
    return this.http.get<{ result: RgVwOccupationMast[] }>(AppUtils.DECEASED_DONOR_OCCUPATION_MASTER)
      .pipe(map(response => response.result));
  }


  // getDonorLabListing(): Observable<RgTbLab[]> {
  //   return this.http.get<{ result: RgTbLab[] }>(AppUtils.DECEASED_DONOR_LAB_LIST)
  //     .pipe(map(response => response.result));
  // }

  getDonorOtherLabListing(): Observable<RgTbLab[]> {
    return this.http.get<{ result: RgTbLab[] }>(AppUtils.DECEASED_DONOR_OTHER_LAB_LIST)
      .pipe(map(response => response.result));
  }



  getVitalParam(): Observable<RgTbPARAM[]> {
    return this.http.get<{ result: RgTbPARAM[] }>(AppUtils.DECEASED_DONOR_VITAL_LIST)
      .pipe(map(response => response.result));
  }

  getProcedureListing(): Observable<RgTbProcedure[]> {
    return this.http.get<{ result: RgTbProcedure[] }>(AppUtils.DECEASED_DONOR_PROCEDURE_LIST)
      .pipe(map(response => response.result));
  }

  getDonorLabListing(): Observable<any> {
    return this.http.get(AppUtils.DECEASED_DONOR_LAB_LIST);
  }

  getAllCorSurgeryType(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_SURGERY_TYPE_LIST)
  }
  getAllCorSurgeonNamesList(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_SURGEON_NAMES_LIST)
  }
  getAllCorVisualAcuity(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_VISUAL_ACUITY_LIST)
  }
  getAllCorTransplantOutcome(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_TRANSPLANT_OUTCOME_LIST)
  }
  getAllCorTransDisease(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_TRANS_DISEASE_LIST)
  }
  getAllCorIndicationType(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_INDICATION_TYPE_LIST)
  }
  getAllCorPreOpCond(): Observable<any> {
    return this.http.get(AppUtils.GET_COR_PRE_OP_COND_LIST)
  }

  getAllCorCheckList(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_CHECK_LIST)
  }

  getAllCorClearZone(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_CLEAR_ZONE_LIST)
  }

  getAllCorPrimaryDiag(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_PRIMARY_DIAG_LIST)
  }

  getAllCorTissueType(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_TISSUE_TYPE_LIST)
  }

  getAllCorReqInstitutes(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_REQ_INSTITUTES_LIST)
  }
  saveCorReqInstitutes(dto) {
    return this.http.post(AppUtils.SAVE_RG_VW_COR_REQ_INSTITUTES, dto);
  }

  getAllCorTransDiseaseProcByPrevId(prevId: any): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_TRANS_DISEASE_PROC_BY_PREV_ID_LIST, { params: new HttpParams().set("prevId", prevId) });
  }

  getAllCorOperativeDtls(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_OPERATIVE_DTLS_LIST);
  }

  getAllCorOperDtlsList(): Observable<any> {
    return this.http.get(AppUtils.GET_RG_VW_COR_OPER_DTLS_LIST);
  }

  getLungIcdList() {
    if (!this.icdLungList.value) {
      this.http.get(AppUtils.GET_LUNG_ICD_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdLungList.next(response['result']);
        }
      })
    }
  }

  getIcdLungShortList() {
    if (!this.icdLungShortList.value) {
      this.http.get(AppUtils.GET_ICD_LUNG_SHORT_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdLungShortList.next(response['result']);
        }
      })
    }
  }

  getLungPleurodesisIndications(): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_PLEURODESIS_TRANS_IND);
  }

  getLungPleurodesisMethod(): Observable<any> {
    return this.http.get(AppUtils.GET_PLEURODESIS_METHOD);
  }

  getSMSWebService(smsWebService: any): Observable<any> {
    return this.http.post(AppUtils.SMS_WEB_SERVICE, smsWebService);
  }

  getLungDonorComplicationMast(): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_DONOR_COMPLICATION_LIST);
  }

  getHeartDonorComplicationMast(): Observable<any> {
    return this.http.get(AppUtils.GET_HEART_DONOR_COMPLICATION_LIST);
  }

  getLungProceduresMast() {
    return this.http.get(AppUtils.GET_LUNG_DONOR_PROCEDURE_MAST);
  }

  getAllLungTransIndication(): Observable<any> {
    return this.http.get(AppUtils.GET_LUNG_TRANS_IND_MAST)
  }

  getVisitPurpose() {
    if (!this.visitPurposeList.value) {
      this.http.get(AppUtils.GET_VISIT_PURPOSE_LIST).subscribe(response => {
        if (response && response['result']) {
          this.visitPurposeList.next(response['result']);
        }
      })
    }
  }

  getdurSymptoms() {
    if (!this.durSymptomsList.value) {
      this.http.get(AppUtils.GET_DURATION_SYMPTOMS_LIST).subscribe(response => {
        if (response && response['result']) {
          this.durSymptomsList.next(response['result']);
        }
      })
    }
  }

  getreAdmission() {
    if (!this.reAdmissionList.value) {
      this.http.get(AppUtils.GET_READMISSION_LIST).subscribe(response => {
        if (response && response['result']) {
          this.reAdmissionList.next(response['result']);
        }
      })
    }
  }

  getDiagnosisIcd() {
    if (!this.diagnosisIcdList.value) {
      this.http.get(AppUtils.GET_DIAGNOSIS_ICD_LIST).subscribe(response => {
        if (response && response['result']) {
          this.diagnosisIcdList.next(response['result']);
        }
      })
    }
  }
  getSymptoms() {
    if (!this.symptomsList.value) {
      this.http.get(AppUtils.GET_SYMPTOM_LIST).subscribe(response => {
        if (response && response['result']) {
          this.symptomsList.next(response['result']);
        }
      })
    }
  }

  getMedications() {
    if (!this.medicationsList.value) {
      this.http.get(AppUtils.GET_MEDICATION_LIST).subscribe(response => {
        if (response && response['result']) {
          this.medicationsList.next(response['result']);
        }
      })
    }
  }

  getComorbidities(){
    if (!this.comorbiditiesList.value) {
      this.http.get(AppUtils.GET_COMORBIDITIES_LIST).subscribe(response => {
        if (response && response['result']) {
          this.comorbiditiesList.next(response['result']);
        }
      })
    }
  }
  getPsychotherapy() {
    if (!this.psychotherapyList.value) {
      this.http.get(AppUtils.GET_PSYCHOTHER_LIST).subscribe(response => {
        if (response && response['result']) {
          this.psychotherapyList.next(response['result']);
        }
      })
    }
  }
  getScale() {
    if (!this.scaleList.value) {
      this.http.get(AppUtils.GET_SCALE_LIST).subscribe(response => {
        if (response && response['result']) {
          this.scaleList.next(response['result']);
        }
      })
    }
  }

  getConditions() {
    if (!this.conditionsList.value) {
      this.http.get(AppUtils.GET_CONDITIONS_LIST).subscribe(response => {
        if (response && response['result']) {
          this.conditionsList.next(response['result']);
        }
      })
    }
  }

  getHeartIcdList() {
    if (!this.icdHeartList.value) {
      this.http.get(AppUtils.GET_HEART_ICD_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdHeartList.next(response['result']);
        }
      })
    }
  }

  getIcdHeartShortList() {
    if (!this.icdHeartShortList.value) {
      this.http.get(AppUtils.GET_ICD_HEART_SHORT_LIST).subscribe(response => {
        if (response && response['result']) {
          this.icdHeartShortList.next(response['result']);
        }
      })
    }
  }
  getAllPrimaryEtiology(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_PRIMARY_ETOLOGY);
  }

  getAllHeartNyhaClass(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_HEART_NYHA_CLASS);
  }

  getHeartPhysioBaseline(): Observable<any> {
    return this.http.get(AppUtils.GET_HEART_PHYSIO_BASELINE);
  }

  getAllHeartInotropes(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_HEART_INOTROPES);
  }

  getAllMCSHeartDevice(): Observable<any> {
    return this.http.get(AppUtils.GET_ALL_HEART_MCS_DEVICE); 
  }

  getHeartProceduresMast() {
    return this.http.get(AppUtils.GET_HEART_DONOR_PROCEDURE_MAST);
  }

  getAntiGenMaster(): Observable<any> {
    return this.http.get(AppUtils.GET_ANIT_GEN_MASTER);
  }
}
