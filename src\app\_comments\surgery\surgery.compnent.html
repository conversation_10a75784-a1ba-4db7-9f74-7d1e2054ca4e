<div class="content-wrapper mb-2 lab-results">
  <div [formGroup]="surgeryForm">
    <div class="text-right pb-2">

      <button *ngIf="showAddNewButton" (click)="onAddNewSurgery()" class="btn btn-sm btn-primary">Add New</button>
      <button *ngIf="showSurgeryButton" (click)="callFetchDataFromAlShifa()" class="btn btn-sm btn-primary">Download</button>
    </div>

    <p-dataTable [immutable]="false" [value]="surgList" [editable]="true" dataKey="runId" [responsive]="true">

      <p-column field="surgeryName" header="Surgery">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbSurgeryDtls">
            <div [formGroupName]="rowIndex">
              <input type="hidden" class="form-control form-control-sm" formControlName="runId">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy">
              <input type="hidden" class="form-control form-control-sm" formControlName="enteredDt">
              <input type="hidden" class="form-control form-control-sm" formControlName="source">
              <!-- <div *ngIf="!row.isEditable">{{surgeryName}}</div> -->
              <div *ngIf="!row.isEditable">
                {{getSurgery(row.surgeryID)}}
              </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="surgeryMaster" [virtualScroll]="true" placeholder="Select Surgery"
                  bindLabel="procName" bindValue="procID" formControlName="surgeryID">
                  <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.procName }}
                  </ng-template>
                </ng-select>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>



      <p-column field="surgeryDt" header="Date">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbSurgeryDtls">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.surgeryDt | date:'dd-MM-yyyy'}}</div>
              <div *ngIf="row.isEditable">
              
                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="surgeryDt"
                [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today
                yearRange="1930:2030" yearNavigator="true" showButtonBar="true"></p-calendar>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="remarks" header="Remarks">
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbSurgeryDtls">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.remarks}}</div>
              <div *ngIf="row.isEditable">
                <input type="text" class="form-control form-control-sm" formControlName="remarks">
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>


      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">

          <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
          class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>

        <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
          class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
     

        </ng-template>
      </p-column>

      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
        </ng-template>
      </p-column>

    </p-dataTable>
  </div>
</div>