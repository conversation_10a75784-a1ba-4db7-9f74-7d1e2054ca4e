import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import * as moment from 'moment';
import { MasterService } from 'src/app/_services/master.service';
import { SharedService } from 'src/app/_services/shared.service';
import { AncRegisterListingService } from './anc-register-listing.service';
import Swal from 'sweetalert2';
import * as AppUtils from '../../common/app.utils';
import { GridOptions } from 'ag-grid-community';
import { AncRegisterListingExcel } from 'src/app/_models/ancRegisterListingExcel';

@Component({
  selector: 'app-anc-register-listing',
  templateUrl: './anc-register-listing.component.html',
  styleUrls: ['./anc-register-listing.component.scss']
})
export class AncRegisterListingComponent implements OnInit {

  ancRegisterSearch: FormGroup;
  institutes: any;
  regions: any[];
  wallayats: any[];
  villages: any[];
  wallayatsFilter: any[];
  villagesFilter: any[];
  ancStatusOptions = [];
  statusName: any;
  ancRegisterList: Array<AncRegisterListingExcel> = new Array<AncRegisterListingExcel>();
  data: any;
  rowData: any[]=[];
  totalRecords:any;

  gridOptions: GridOptions = <GridOptions>{
    enableColResize: true,
      pagination: false,
      resizable: true,
     paginationPageSize: AppUtils.GRID_PAGINATION_SIZE,
     onGridSizeChanged: () => {
      this.gridOptions.api.sizeColumnsToFit();
    } 
    };

  getPatientFullName(params: any) {
    let eDiv: any = document.createElement('div');
    let fname: any = "";
    let sname: any = "";
    let tname: any = "";
    let tribe: any = "";
    if (params.node && params.node.data && params.node.data) {
      fname = params.node.data.firstName? params.data.firstName: "";
      sname = params.node.data.secondName? params.data.secondName: "";
      tname = params.node.data.thirdName? params.data.thirdName: "";
      tribe = params.node.data.tribe? params.data.tribe : "";
    }
    eDiv.innerHTML = '<span>' + fname + " " + sname + " " + tname + " " + tribe + '</span>';
    return eDiv;
  }

  getDateFormat = (data) => {
    if (data.value) {
      const formattedDate = moment(data.value).format('DD-MM-yyyy');
      return formattedDate;
    } else {
      return '';
    }
  };

  getAncInstitute = (ancInstitute) => {
    if (ancInstitute.value) {
     let estName
     this.institutes.forEach(ele => {
       if (ancInstitute.value == ele.estCode){
         estName = ele.estName;
       }})
       return estName
    }else{
     return ' '
    }
   };


   getNameOfRequestInst = (requestInst) => {
     if (requestInst.value) {
      let estName
      this.institutes.forEach(ele => {
        if (requestInst.value == ele.estCode){
          estName = ele.estName;
        }})
        return estName
     }else{
      return ' '
     }
    };

    columnDefs = [
      { headerName: 'ANC NO', field: 'ancNo'},
      { headerName: 'Civil ID',field: 'civilId' },
      { headerName: 'Name',field: '', cellRenderer: this.getPatientFullName},
      { headerName: 'Age',field: 'age'},
      { headerName: 'ANC Institute',field: 'ancInstitute',cellRenderer:this.getAncInstitute},
      { headerName: 'Requested Institute',field: 'requestedInst',cellRenderer:this.getNameOfRequestInst},
    ]
  
  constructor(fb: FormBuilder, private _masterService: MasterService, private _sharedService: SharedService, private _ancRegisterListingService:AncRegisterListingService,
    private _router: Router) { this._masterService.institiutes.subscribe(async res => {
      this.institutes = await res["result"];
    });
    this._masterService.getRegionsMasterFull();
    this._masterService.regionsMasterFull.subscribe(value => {
      this.regions = value;
    });

    this._masterService.getWallayatMasterFull();
    this._masterService.wallayatMasterFull.subscribe(value => {
      this.wallayats = value;
    });

    this._masterService.getVillagesMasterFull();
    this._masterService.villagesMast.subscribe(value => {
      this.villages = value;
    });


    this.ancRegisterSearch = fb.group({
      civilId: new FormControl(null, Validators.required),
      fullName: new FormControl(null, Validators.required),
      age: new FormControl(null),
      ageFrom: new FormControl(null),
      ageTo: new FormControl(null),
      firstName: new FormControl(null),
      secondName: new FormControl(null),
      thirdName: new FormControl(null),
      tribe: new FormControl(null),
      mobileNo: new FormControl(null),
      ancNo: new FormControl(null),
      ancInstitute: new FormControl(null), 
      requestInst: new FormControl(null), 
      requestDate: new FormControl(null),
      village: new FormControl(null), 
      walCode: new FormControl(null), 
      regCode: new FormControl(null), 
    })
   
   }

  ngOnInit() {
  }

  changeRegion(obj) {
    this.wallayatsFilter = [];
    if (obj && obj.regCode) {
      this.wallayatsFilter = this.wallayats.filter(s => s.regCode == obj.regCode);
    }
  }

  changeWalayat(obj) {
    this.villagesFilter = [];
    if (obj && obj.walCode) {
      this.villagesFilter = this.villages.filter(s => s.walCode == obj.walCode);
    }
  }

  getSearchDetailesList(event?:any){
    let source = this.ancRegisterSearch.value;
    let pageable = {
        page: event?event.page:0,
        size: event?event.rows:AppUtils.E_REGISTRY_PAGINATION_SIZE
      }
      source = {pageable, ...source}
      
      this._ancRegisterListingService.getAncRegisterListing(source).subscribe(res => {
        this.data = res

        this.rowData = this.data.result['content'];
        this.totalRecords = this.data.result['totalElements'];
       
      },error => {
        if (error.status == 401)
          Swal.fire('Error!', 'Error occured while retrieving details', 'error')
      }); 
  }

  exportToExcel(event?:any) {
    if (this.rowData && this.rowData.length > 0) {
      if (this.totalRecords == this.rowData.length) {
          this.formatData(this.rowData);
      } else {
        let source = this.ancRegisterSearch.value;
    
      let pageable = {
          page: event?event.page:0,
          size: event?event.rows: this.totalRecords,
        }
 
      source = {pageable, ...source}
        
        this._ancRegisterListingService.getAncRegisterListing(source).subscribe(res => {
          if (res['code'] == "S0000") {
            let excelData = res['result']['content'];
            if (excelData && excelData.length > 0) {
              this.formatData(excelData);
            }
          }
        });
      }
    } else {
        Swal.fire('Warning!', 'Please search first', 'warning')

    }

    
  }

  formatData(rowData){
    this.ancRegisterList=[];
    rowData.forEach(el => {
      let name =  el.firstName +' ' +el.secondName+' ' +el.thirdName +' '+ el.tribe;
      let ancInstitute = this.getInstitute(el.ancInstitute)
      let requestedInstitute = this.getInstitute(el.requestedInst)
      this.ancRegisterList.push({ancNo:el.ancNo,
                              civilId: el.civilId,
                              name:name,
                              age: el.age , ancInstitute:ancInstitute,
                              requestedInst: requestedInstitute,
                              
                             })
    })
     this._sharedService.exportAsExcelFile(this.ancRegisterList, "AncRegistered_Listing");
  }

  getInstitute (estcode){
    if (estcode) {
      let estName
      this.institutes.forEach(ele => {
        if (estcode == ele.estCode){
          estName = ele.estName;
        }})
        return estName
     }else{
      return ' '
     }
    };

  clear() {
    this.rowData = [];
    this.ancRegisterList = null;
    this.ancRegisterSearch.reset();
    this.wallayatsFilter= [];
    this.villagesFilter= [];
  }

  public onCellDoubleClicked(event: any) {
    this._sharedService.setNavigationData(event.data);
    this._router.navigate(['anc/ancRegister'],{ state: { ancNo: event.data.ancNo } });
    
  }

}
