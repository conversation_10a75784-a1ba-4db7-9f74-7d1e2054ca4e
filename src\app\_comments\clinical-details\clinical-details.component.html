<div class="content-wrapper mb-2 lab-results">
  <form [formGroup]="clinicalForm">
    <div class="text-right pb-2">
      <button (click)="onAddNewIcd()" class="btn btn-sm btn-primary">Add New</button>
      <button (click)="getdiagnosis()" class="btn btn-sm btn-primary">Download</button>
    </div>


    <p-dataTable [immutable]="false" [value]="diagFg" [editable]="false" dataKey="runId" [responsive]="true">
      <p-column field="entryDate" header="Date">
        <!-- <ng-template let-row="rowData" pTemplate="body"> -->
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbDiagnosis">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.entryDate |date:'dd-MM-yyyy'}} </div>
              <div *ngIf="row.isEditable">

                <p-calendar dateFormat="dd-mm-yy" showIcon=true formControlName="entryDate"
                  [ngModelOptions]="{standalone: true}" monthNavigator="true" [maxDate]=today yearRange="1930:2030"
                  yearNavigator="true" showButtonBar="true"></p-calendar>

              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>
      <p-column field="icd" header="ICD">
        <!-- <ng-template let-row="rowData" pTemplate="body"> -->
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">

          <ng-container formArrayName="rgTbDiagnosis">

            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.icd}}
                {{getICD(row.icd)}}
              </div>
              <div *ngIf="row.isEditable">
                <input type="hidden" class="form-control form-control-sm" formControlName="icd">
                <input type="hidden" class="form-control form-control-sm" formControlName="runId">
                <input type="hidden" class="form-control form-control-sm" formControlName="enteredBy">
                <input type="hidden" class="form-control form-control-sm" formControlName="source">

                <ng-select #entryPoint [items]="icdList" [virtualScroll]="true" placeholder="Select" bindLabel="disease"
                bindValue="icd" formControlName="icd">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.disease }}
                </ng-template>
              </ng-select>

              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>

      <p-column field="icdFlag" header="ICD Flag">
        <!-- <ng-template let-row="rowData" pTemplate="body"> -->
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbDiagnosis">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">
                {{getICDFlag(row.icdFlag)}}
              </div>
              <div *ngIf="row.isEditable">
                <ng-select #entryPoint [items]="icdFlagList" [virtualScroll]="true" placeholder="Select" bindLabel="value"
                bindValue="id" formControlName="icdFlag">
                <ng-template ng-option-tmp let-item="item" let-index="index">{{ item.value }}
                </ng-template>
              </ng-select>

              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>



      <p-column field="remarks" header="Remarks">
        <!-- <ng-template let-row="rowData" pTemplate="body"> -->
        <ng-template let-row="rowData" pTemplate="body" let-rowIndex="rowIndex">
          <ng-container formArrayName="rgTbDiagnosis">
            <div [formGroupName]="rowIndex">
              <div *ngIf="!row.isEditable">{{row.remarks}}</div>
              <div *ngIf="row.isEditable">
                <input type="text" class="form-control form-control-sm" formControlName="remarks">
              </div>
            </div>
          </ng-container>
        </ng-template>
      </p-column>


      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="onRowEditInit(row)" *ngIf="row.source == 'W' && row.isEditable == false"
            class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></button>

          <button (click)="onRowEditSave(row)" *ngIf="row.source == 'W' && row.isEditable == true"
            class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button>
        </ng-template>
      </p-column>

      <p-column field="" header="" [style]="{'text-align':'center','width':'54px'}" styleClass="foo">
        <ng-template let-row="rowData" pTemplate="body">
          <button (click)="delete(row)" class="btn btn-sm btn-primary"><i class="fa fa-trash"></i></button>
        </ng-template>
      </p-column>

    </p-dataTable>
  </form>
</div>