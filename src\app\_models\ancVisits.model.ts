import { AncLabInvestModel } from "./ancLabInvest.model";
import { AncMidicalHistoryModel } from "./ancMidicalHistory.model";

export class AncVisitsModel {
    visitId: number;
    visitDate: Date;
    pregnancyId: number;
    scanFindings: string;
    scanRemarks: string;
    riskGrade: string;
    headCircum: number;
    abdomenCircum: number;
    estFetalWeight: number;
    abortionType: number;
    abortionYn: string;
    appDate: Date;
    docComments: string;
    planManagement: string;
    refToNutrition: string;
    refToHealthEdu: string;
    refToObstetrician: string;
    refToPsychatrist: string;
    refToOtherDept: string;
    otherDept: string;
    refReason: string;
    rgTbAncLab: Array <AncLabInvestModel>;
    rgTbMidical: Array <AncMidicalHistoryModel>;
    index: number;
  }